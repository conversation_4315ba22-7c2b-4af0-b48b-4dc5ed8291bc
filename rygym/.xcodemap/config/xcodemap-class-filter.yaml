autoDetectedPackages:
- cn.uone.ams.tpi
- cn.uone.application.constant
- cn.uone.application.enumerate
- cn.uone.application.vo
- cn.uone.bean.aspect
- cn.uone.bean.constant
- cn.uone.bean.entity
- cn.uone.bean.parameter
- cn.uone.business
- cn.uone.config
- cn.uone.crm
- cn.uone.fegin.bus
- cn.uone.fegin.crm
- cn.uone.fegin.sys
- cn.uone.fegin.tpi
- cn.uone.inerceptor
- cn.uone.job
- cn.uone.serializer.business
- cn.uone.util
enableAutoDetect: true
entryDisplayConfig:
  excludedPathPatterns: []
  skipJsCss: true
funcDisplayConfig:
  skipConstructors: false
  skipFieldAccess: true
  skipFieldChange: true
  skipGetters: false
  skipNonProjectPackages: false
  skipPrivateMethods: false
  skipSetters: false
ignoreSameClassCall: null
ignoreSamePackageCall: null
includedPackagePrefixes: null
includedParentClasses: null
name: xcodemap-filter
recordMode: all
sourceDisplayConfig:
  color: blue
startOnDebug: false
