2025-06-23 10:30:31.852 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-06-23 10:30:31.858 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-06-23 10:30:31.863 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-06-23 10:30:33.268 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$82587ea4 [80] -| ===配置shiro路由规则====
2025-06-23 10:30:34.109 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-06-23 10:30:34.110 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-06-23 10:30:34.110 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-06-23 10:30:34.186 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-06-23 10:30:35.801 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-06-23 10:30:35.801 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-06-23 10:30:38.120 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-06-23 10:30:38.148 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-06-23 10:30:38.153 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 7.012 seconds (JVM running for 7.887)
2025-06-23 10:30:38.219 |-INFO  [RMI TCP Connection(1)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 10:30:38.324 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-06-23 10:30:38.327 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-06-23 10:51:44.305 |-WARN  [Thread-4] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-23 10:51:44.305 |-WARN  [Thread-21] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-06-23 10:51:44.307 |-WARN  [Thread-21] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-06-23 10:51:44.308 |-WARN  [Thread-4] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-06-23 10:51:44.318 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [90] -| De-registering from Nacos Server now...
2025-06-23 10:51:44.321 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [110] -| De-registration finished.
2025-06-23 14:08:48.689 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-06-23 14:08:48.702 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-06-23 14:08:48.708 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-06-23 14:08:50.055 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$718e39b7 [80] -| ===配置shiro路由规则====
2025-06-23 14:08:51.342 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-06-23 14:08:51.342 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-06-23 14:08:51.343 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-06-23 14:08:51.431 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-06-23 14:08:52.973 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-06-23 14:08:52.973 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-06-23 14:08:55.160 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-06-23 14:08:55.225 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-06-23 14:08:55.233 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 7.254 seconds (JVM running for 8.277)
2025-06-23 14:08:55.238 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:08:55.587 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-06-23 14:08:55.593 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-06-23 14:08:55.636 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[***************:8031],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:8031;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@45f4c27c
2025-06-23 14:08:56.463 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-23 14:08:56.523 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:56.580 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:56.640 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:56.641 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:08:56.641 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:08:56.699 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-23 14:08:56.838 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:56.880 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:56.921 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:56.922 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:08:56.922 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:08:57.026 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-23 14:08:57.067 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.145 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.208 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.209 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:08:57.209 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:08:57.252 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-23 14:08:57.292 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.331 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.375 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.375 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:08:57.375 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:08:57.422 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-23 14:08:57.456 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.498 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.542 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.543 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:08:57.543 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:08:57.580 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-23 14:08:57.619 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.668 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.709 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.709 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:08:57.709 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:08:57.754 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-23 14:08:57.788 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.838 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.880 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-23 14:08:57.880 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:08:58.081 |-INFO  [RMI TCP Connection(1)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 14:08:58.200 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-06-23 14:08:58.202 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-06-23 14:29:10.934 |-INFO  [Thread-39] com.netflix.loadbalancer.PollingServerListUpdater [53] -| Shutting down the Executor Pool for PollingServerListUpdater
2025-06-23 14:29:10.934 |-WARN  [Thread-21] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-06-23 14:29:10.937 |-WARN  [Thread-21] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-06-23 14:29:10.934 |-WARN  [Thread-4] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-23 14:29:10.945 |-WARN  [Thread-4] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-06-23 14:29:10.959 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [90] -| De-registering from Nacos Server now...
2025-06-23 14:29:10.976 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [110] -| De-registration finished.
2025-06-23 14:29:17.169 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-06-23 14:29:17.179 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-06-23 14:29:17.186 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-06-23 14:29:21.071 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$d0e55ac3 [80] -| ===配置shiro路由规则====
2025-06-23 14:29:22.180 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-06-23 14:29:22.181 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-06-23 14:29:22.181 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-06-23 14:29:22.310 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-06-23 14:29:24.399 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-06-23 14:29:24.400 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-06-23 14:29:28.656 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-06-23 14:29:28.774 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-06-23 14:29:28.789 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 13.62 seconds (JVM running for 15.056)
2025-06-23 14:29:28.793 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:29:29.105 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-06-23 14:29:29.113 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-06-23 14:29:29.127 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@1b87f7cd
2025-06-23 14:29:29.229 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.232 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.237 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.242 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.242 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-23 14:29:29.242 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:29:29.242 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:29:29.245 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.249 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.253 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.255 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.255 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-23 14:29:29.255 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:29:29.255 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:29:29.258 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.261 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.269 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.273 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.273 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-23 14:29:29.273 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:29:29.273 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:29:29.276 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.279 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.285 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.315 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.316 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-23 14:29:29.316 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:29:29.316 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:29:29.337 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.340 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.341 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.345 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.345 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-23 14:29:29.346 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:29:29.346 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:29:29.359 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.364 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.369 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.372 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.372 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-23 14:29:29.372 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:29:29.372 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-23 14:29:29.379 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.385 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.388 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.395 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-23 14:29:29.395 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-23 14:29:29.395 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-23 14:29:29.976 |-INFO  [RMI TCP Connection(5)-***************] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 14:29:30.228 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-06-23 14:29:30.236 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
