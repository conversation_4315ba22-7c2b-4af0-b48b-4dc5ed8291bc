2025-07-01 09:24:43.622 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-07-01 09:24:43.650 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-07-01 09:24:43.656 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-07-01 09:24:45.330 |-WARN  [Thread-5] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-01 09:24:45.331 |-WARN  [Thread-5] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-07-01 09:24:52.873 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-07-01 09:24:52.886 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-07-01 09:24:52.896 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-07-01 09:24:58.876 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$9dace3b5 [80] -| ===配置shiro路由规则====
2025-07-01 09:25:01.560 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-07-01 09:25:01.561 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-07-01 09:25:01.565 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-07-01 09:25:01.886 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-07-01 09:25:04.746 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-07-01 09:25:04.747 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-07-01 09:25:07.509 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-07-01 09:25:07.611 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-07-01 09:25:07.617 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 17.039 seconds (JVM running for 18.651)
2025-07-01 09:25:07.622 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 09:25:07.829 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-01 09:25:07.834 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-07-01 09:25:07.841 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@1314ccc1
2025-07-01 09:25:07.892 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.894 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.896 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.898 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.898 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 09:25:07.898 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 09:25:07.898 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 09:25:07.899 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.901 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.902 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.904 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.904 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 09:25:07.904 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 09:25:07.905 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 09:25:07.906 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.907 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.909 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.910 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.910 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 09:25:07.910 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 09:25:07.910 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 09:25:07.911 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.913 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.914 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.917 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.917 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 09:25:07.917 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 09:25:07.917 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 09:25:07.919 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.921 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.922 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.924 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.924 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 09:25:07.924 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 09:25:07.924 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 09:25:07.925 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.927 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.929 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.931 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.931 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 09:25:07.931 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 09:25:07.932 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 09:25:07.941 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.943 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.945 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.947 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 09:25:07.947 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 09:25:07.947 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 09:25:08.541 |-INFO  [RMI TCP Connection(9)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 09:25:08.900 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-07-01 09:25:08.903 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-07-01 16:05:32.728 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-07-01 16:05:32.740 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-07-01 16:05:32.761 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-07-01 16:05:36.713 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$353678ee [80] -| ===配置shiro路由规则====
2025-07-01 16:05:39.653 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-07-01 16:05:39.656 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-07-01 16:05:39.656 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-07-01 16:05:39.752 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-07-01 16:05:41.974 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-07-01 16:05:41.975 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-07-01 16:05:44.658 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-07-01 16:05:44.788 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-07-01 16:05:44.796 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 13.152 seconds (JVM running for 14.412)
2025-07-01 16:05:44.805 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:05:45.059 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-01 16:05:45.069 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-07-01 16:05:45.077 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@388ad8aa
2025-07-01 16:05:45.134 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.137 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.140 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.144 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.145 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:05:45.145 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:05:45.145 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:05:45.146 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.148 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.149 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.151 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.151 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:05:45.151 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:05:45.151 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:05:45.153 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.155 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.156 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.157 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.158 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:05:45.158 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:05:45.158 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:05:45.160 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.162 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.163 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.166 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.166 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:05:45.166 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:05:45.166 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:05:45.168 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.170 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.173 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.174 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.174 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:05:45.174 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:05:45.174 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:05:45.176 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.178 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.179 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.180 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.180 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:05:45.181 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:05:45.181 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:05:45.182 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.183 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.184 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.186 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:05:45.186 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:05:45.186 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:05:45.517 |-INFO  [RMI TCP Connection(5)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 16:05:45.690 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-07-01 16:05:45.695 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-07-01 16:09:18.727 |-WARN  [Thread-3] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-01 16:09:18.727 |-INFO  [Thread-48] com.netflix.loadbalancer.PollingServerListUpdater [53] -| Shutting down the Executor Pool for PollingServerListUpdater
2025-07-01 16:09:18.727 |-WARN  [Thread-29] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-07-01 16:09:18.732 |-WARN  [Thread-29] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-07-01 16:09:18.744 |-WARN  [Thread-3] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-07-01 16:09:18.799 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [90] -| De-registering from Nacos Server now...
2025-07-01 16:09:18.838 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [110] -| De-registration finished.
2025-07-01 16:10:46.345 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-07-01 16:10:46.381 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-07-01 16:10:46.403 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-07-01 16:10:51.847 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$d0e55ac3 [80] -| ===配置shiro路由规则====
2025-07-01 16:10:55.143 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-07-01 16:10:55.145 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-07-01 16:10:55.149 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-07-01 16:10:56.162 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-07-01 16:11:02.179 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-07-01 16:11:02.220 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-07-01 16:11:08.292 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-07-01 16:11:08.497 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-07-01 16:11:08.506 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 26.526 seconds (JVM running for 30.003)
2025-07-01 16:11:08.513 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:11:09.024 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-01 16:11:09.032 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-07-01 16:11:09.185 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[***************:8031],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:8031;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@7469c73d
2025-07-01 16:11:10.376 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-07-01 16:11:10.478 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:10.549 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:10.782 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:10.782 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:11:10.782 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:11:10.899 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-07-01 16:11:10.951 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.019 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.095 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.095 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:11:11.095 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:11:11.151 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-07-01 16:11:11.198 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.262 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.313 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.313 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:11:11.313 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:11:11.357 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-07-01 16:11:11.397 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.441 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.493 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.493 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:11:11.493 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:11:11.539 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-07-01 16:11:11.591 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.659 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.796 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:11.796 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:11:11.796 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:11:11.895 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-07-01 16:11:11.995 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:12.064 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:12.144 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:12.144 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:11:12.144 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:11:12.230 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-07-01 16:11:12.304 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:12.370 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:12.483 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-07-01 16:11:12.483 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:11:12.877 |-INFO  [RMI TCP Connection(18)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 16:11:13.029 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-07-01 16:11:13.032 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-07-01 16:50:29.379 |-WARN  [Thread-40] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-07-01 16:50:29.379 |-INFO  [Thread-71] com.netflix.loadbalancer.PollingServerListUpdater [53] -| Shutting down the Executor Pool for PollingServerListUpdater
2025-07-01 16:50:29.379 |-WARN  [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-01 16:50:29.403 |-WARN  [Thread-40] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-07-01 16:50:29.442 |-WARN  [Thread-9] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-07-01 16:50:29.720 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [90] -| De-registering from Nacos Server now...
2025-07-01 16:50:29.770 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [110] -| De-registration finished.
2025-07-01 16:51:16.887 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-07-01 16:51:16.895 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-07-01 16:51:16.906 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-07-01 16:51:20.291 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$4547034f [80] -| ===配置shiro路由规则====
2025-07-01 16:51:22.298 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-07-01 16:51:22.300 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-07-01 16:51:22.302 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-07-01 16:51:22.622 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-07-01 16:51:28.833 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-07-01 16:51:28.834 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-07-01 16:51:34.055 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-07-01 16:51:34.112 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-07-01 16:51:34.119 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 20.617 seconds (JVM running for 21.882)
2025-07-01 16:51:34.123 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:51:34.345 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-01 16:51:34.354 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-07-01 16:51:34.361 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@768d8a25
2025-07-01 16:51:34.419 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.422 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.423 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.425 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.425 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:51:34.426 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:51:34.426 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:51:34.427 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.429 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.435 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.437 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.437 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:51:34.437 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:51:34.437 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:51:34.439 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.442 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.445 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.448 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.450 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:51:34.450 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:51:34.450 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:51:34.455 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.456 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.458 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.461 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.461 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:51:34.461 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:51:34.461 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:51:34.462 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.464 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.465 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.466 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.467 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:51:34.467 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:51:34.467 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:51:34.468 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.469 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.470 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.472 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.472 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:51:34.472 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:51:34.472 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 16:51:34.473 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.474 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.476 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.477 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 16:51:34.477 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 16:51:34.477 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 16:51:34.746 |-INFO  [RMI TCP Connection(9)-***************] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 16:51:35.092 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-07-01 16:51:35.098 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-07-01 17:03:11.616 |-INFO  [Thread-54] com.netflix.loadbalancer.PollingServerListUpdater [53] -| Shutting down the Executor Pool for PollingServerListUpdater
2025-07-01 17:03:11.617 |-WARN  [Thread-28] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-07-01 17:03:11.617 |-WARN  [Thread-2] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-01 17:03:11.641 |-WARN  [Thread-28] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-07-01 17:03:11.646 |-WARN  [Thread-2] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-07-01 17:20:52.693 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-07-01 17:20:52.704 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-07-01 17:20:52.712 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-07-01 17:20:58.442 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$16cff4d3 [80] -| ===配置shiro路由规则====
2025-07-01 17:21:00.698 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-07-01 17:21:00.699 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-07-01 17:21:00.699 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-07-01 17:21:00.835 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-07-01 17:21:03.878 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-07-01 17:21:03.879 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-07-01 17:21:07.533 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-07-01 17:21:07.617 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-07-01 17:21:07.626 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 17.463 seconds (JVM running for 19.409)
2025-07-01 17:21:07.633 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 17:21:08.341 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-01 17:21:08.355 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-07-01 17:21:08.367 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@1da53092
2025-07-01 17:21:08.567 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:08.683 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:08.742 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:08.836 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:08.836 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 17:21:08.836 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 17:21:08.836 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 17:21:08.929 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:08.959 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:08.989 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.069 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.070 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 17:21:09.070 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 17:21:09.070 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 17:21:09.137 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.318 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.372 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.386 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.387 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 17:21:09.387 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 17:21:09.387 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 17:21:09.396 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.403 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.416 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.480 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.481 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 17:21:09.481 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 17:21:09.481 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 17:21:09.513 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.565 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.579 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.635 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.636 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 17:21:09.636 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 17:21:09.636 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 17:21:09.699 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.761 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.798 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.804 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.804 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 17:21:09.804 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 17:21:09.804 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-07-01 17:21:09.845 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.863 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.879 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.887 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-07-01 17:21:09.887 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-07-01 17:21:09.888 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-07-01 17:21:10.655 |-INFO  [RMI TCP Connection(12)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 17:21:11.079 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-07-01 17:21:11.088 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
