2025-06-25 09:34:27.520 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-06-25 09:34:27.548 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-06-25 09:34:27.557 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-06-25 09:34:29.627 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$90b9526a [80] -| ===配置shiro路由规则====
2025-06-25 09:34:30.665 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-06-25 09:34:30.666 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-06-25 09:34:30.667 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-06-25 09:34:30.772 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-06-25 09:34:33.103 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-06-25 09:34:33.104 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-06-25 09:34:36.491 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-06-25 09:34:36.577 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-06-25 09:34:36.587 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 12.675 seconds (JVM running for 14.81)
2025-06-25 09:34:36.595 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 09:34:36.894 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-06-25 09:34:36.901 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-06-25 09:34:36.907 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@2cc9a5a2
2025-06-25 09:34:36.978 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.980 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.982 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.984 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.984 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 09:34:36.984 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 09:34:36.984 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 09:34:36.987 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.989 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.990 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.991 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.991 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 09:34:36.991 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 09:34:36.991 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 09:34:36.993 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.994 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.996 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.998 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:36.998 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 09:34:36.998 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 09:34:36.998 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 09:34:36.999 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.001 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.002 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.008 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.008 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 09:34:37.008 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 09:34:37.008 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 09:34:37.010 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.012 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.014 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.016 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.016 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 09:34:37.016 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 09:34:37.016 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 09:34:37.017 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.019 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.020 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.021 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.022 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 09:34:37.022 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 09:34:37.022 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 09:34:37.023 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.024 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.026 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.027 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 09:34:37.027 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 09:34:37.027 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 09:34:37.275 |-INFO  [RMI TCP Connection(1)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 09:34:37.571 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-06-25 09:34:37.578 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-06-25 09:39:29.665 |-WARN  [Thread-27] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-06-25 09:39:29.665 |-WARN  [Thread-6] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-25 09:39:29.665 |-INFO  [Thread-48] com.netflix.loadbalancer.PollingServerListUpdater [53] -| Shutting down the Executor Pool for PollingServerListUpdater
2025-06-25 09:39:29.673 |-WARN  [Thread-27] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-06-25 09:39:29.699 |-WARN  [Thread-6] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-06-25 09:39:29.894 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [90] -| De-registering from Nacos Server now...
2025-06-25 09:39:29.914 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [110] -| De-registration finished.
2025-06-25 10:21:35.316 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-06-25 10:21:35.921 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-06-25 10:21:35.931 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-06-25 10:21:37.687 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$2bb09072 [80] -| ===配置shiro路由规则====
2025-06-25 10:21:38.737 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-06-25 10:21:38.738 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-06-25 10:21:38.739 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-06-25 10:21:38.833 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-06-25 10:21:40.632 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-06-25 10:21:40.633 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-06-25 10:21:43.221 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-06-25 10:21:43.257 |-ERROR [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [79] -| nacos registry, ams-tpi register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='http://127.0.0.1:8848', endpoint='', namespace='f9cabb73-130b-4a70-b059-34c263ceb01e', watchDelay=30000, logName='', service='ams-tpi', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='***************', networkInterface='', port=8033, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null}},
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([http://127.0.0.1:8848]) tried: java.net.ConnectException: Connection refused (Connection refused)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:556) ~[nacos-client-1.4.1.jar:?]
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498) ~[nacos-client-1.4.1.jar:?]
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493) ~[nacos-client-1.4.1.jar:?]
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:246) ~[nacos-client-1.4.1.jar:?]
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:212) ~[nacos-client-1.4.1.jar:?]
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:74) [spring-cloud-starter-alibaba-nacos-discovery-2.2.5.RELEASE.jar:2.2.5.RELEASE]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:239) [spring-cloud-commons-2.2.2.RELEASE.jar:2.2.2.RELEASE]
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:78) [spring-cloud-starter-alibaba-nacos-discovery-2.2.5.RELEASE.jar:2.2.5.RELEASE]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:138) [spring-cloud-commons-2.2.2.RELEASE.jar:2.2.2.RELEASE]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:101) [spring-cloud-commons-2.2.2.RELEASE.jar:2.2.2.RELEASE]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:88) [spring-cloud-commons-2.2.2.RELEASE.jar:2.2.2.RELEASE]
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:47) [spring-cloud-commons-2.2.2.RELEASE.jar:2.2.2.RELEASE]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172) [spring-context-5.2.4.RELEASE.jar:5.2.4.RELEASE]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165) [spring-context-5.2.4.RELEASE.jar:5.2.4.RELEASE]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139) [spring-context-5.2.4.RELEASE.jar:5.2.4.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:403) [spring-context-5.2.4.RELEASE.jar:5.2.4.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:360) [spring-context-5.2.4.RELEASE.jar:5.2.4.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:165) [spring-boot-2.2.5.RELEASE.jar:2.2.5.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:553) [spring-context-5.2.4.RELEASE.jar:5.2.4.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141) [spring-boot-2.2.5.RELEASE.jar:2.2.5.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747) [spring-boot-2.2.5.RELEASE.jar:2.2.5.RELEASE]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397) [spring-boot-2.2.5.RELEASE.jar:2.2.5.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315) [spring-boot-2.2.5.RELEASE.jar:2.2.5.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226) [spring-boot-2.2.5.RELEASE.jar:2.2.5.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215) [spring-boot-2.2.5.RELEASE.jar:2.2.5.RELEASE]
	at cn.uone.ams.tpi.TpiApplication.main(TpiApplication.java:15) [classes/:?]
2025-06-25 10:21:43.261 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Pausing ProtocolHandler ["http-nio-8033"]
2025-06-25 10:21:44.464 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Stopping service [Tomcat]
2025-06-25 10:21:44.465 |-ERROR [http-nio-8033-Acceptor] org.apache.tomcat.util.net.Acceptor [175] -| Socket accept failed
java.nio.channels.AsynchronousCloseException: null
	at java.nio.channels.spi.AbstractInterruptibleChannel.end(AbstractInterruptibleChannel.java:205) ~[?:1.8.0_452]
	at sun.nio.ch.ServerSocketChannelImpl.accept(ServerSocketChannelImpl.java:256) ~[?:1.8.0_452]
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:466) ~[tomcat-embed-core-9.0.31.jar:9.0.31]
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:72) ~[tomcat-embed-core-9.0.31.jar:9.0.31]
	at org.apache.tomcat.util.net.Acceptor.run(Acceptor.java:95) [tomcat-embed-core-9.0.31.jar:9.0.31]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_452]
2025-06-25 10:21:44.472 |-WARN  [main] org.apache.catalina.loader.WebappClassLoaderBase [173] -| The web application [ROOT] appears to have started a thread named [spring.cloud.inetutils] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2044)
 java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-06-25 10:21:44.473 |-WARN  [main] org.apache.catalina.loader.WebappClassLoaderBase [173] -| The web application [ROOT] appears to have started a thread named [lettuce-eventExecutorLoop-1-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2083)
 java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
 io.netty.util.concurrent.SingleThreadEventExecutor.takeTask(SingleThreadEventExecutor.java:265)
 io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:64)
 io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:877)
 io.netty.util.concurrent.DefaultThreadFactory$DefaultRunnableDecorator.run(DefaultThreadFactory.java:144)
 java.lang.Thread.run(Thread.java:750)
2025-06-25 10:21:44.474 |-WARN  [main] org.apache.catalina.loader.WebappClassLoaderBase [173] -| The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.client.naming.updater] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2044)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1088)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-06-25 10:21:44.474 |-WARN  [main] org.apache.catalina.loader.WebappClassLoaderBase [173] -| The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.client.naming.updater] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2083)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-06-25 10:21:44.475 |-WARN  [main] org.apache.catalina.loader.WebappClassLoaderBase [173] -| The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.failover] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2083)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-06-25 10:21:44.476 |-WARN  [main] org.apache.catalina.loader.WebappClassLoaderBase [173] -| The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.push.receiver] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.net.PlainDatagramSocketImpl.receive0(Native Method)
 java.net.AbstractPlainDatagramSocketImpl.receive(AbstractPlainDatagramSocketImpl.java:143)
 java.net.DatagramSocket.receive(DatagramSocket.java:822)
 com.alibaba.nacos.client.naming.core.PushReceiver.run(PushReceiver.java:83)
 java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
 java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
 java.util.concurrent.FutureTask.run(FutureTask.java)
 java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
 java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)
2025-06-25 10:21:44.477 |-WARN  [main] org.apache.catalina.loader.WebappClassLoaderBase [173] -| The web application [ROOT] appears to have started a thread named [nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.lang.Thread.sleep(Native Method)
 com.alibaba.nacos.common.utils.ThreadUtils.sleep(ThreadUtils.java:52)
 com.alibaba.nacos.common.notify.DefaultPublisher.openEventHandler(DefaultPublisher.java:108)
 com.alibaba.nacos.common.notify.DefaultPublisher.run(DefaultPublisher.java:94)
2025-06-25 10:21:44.477 |-WARN  [main] org.apache.catalina.loader.WebappClassLoaderBase [173] -| The web application [ROOT] appears to have started a thread named [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2044)
 java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:403)
 com.alibaba.nacos.common.notify.DefaultPublisher.openEventHandler(DefaultPublisher.java:116)
 com.alibaba.nacos.common.notify.DefaultPublisher.run(DefaultPublisher.java:94)
2025-06-25 10:21:44.479 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Stopping ProtocolHandler ["http-nio-8033"]
2025-06-25 10:21:44.483 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Destroying ProtocolHandler ["http-nio-8033"]
2025-06-25 10:21:53.513 |-WARN  [Thread-29] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-06-25 10:21:53.513 |-WARN  [Thread-5] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-25 10:21:53.513 |-WARN  [Thread-29] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-06-25 10:21:53.514 |-WARN  [Thread-5] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-06-25 10:24:52.986 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-06-25 10:24:52.993 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-06-25 10:24:52.999 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-06-25 10:24:55.029 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$bf188ae1 [80] -| ===配置shiro路由规则====
2025-06-25 10:24:56.103 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-06-25 10:24:56.103 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-06-25 10:24:56.104 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-06-25 10:24:56.197 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-06-25 10:24:58.263 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-06-25 10:24:58.264 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-06-25 10:25:01.122 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-06-25 10:25:01.369 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-06-25 10:25:01.375 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 11.438 seconds (JVM running for 13.925)
2025-06-25 10:25:01.379 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 10:25:01.590 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-06-25 10:25:01.595 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-06-25 10:25:01.613 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@64a74723
2025-06-25 10:25:01.670 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.672 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.673 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.675 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.675 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 10:25:01.675 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 10:25:01.675 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 10:25:01.676 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.678 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.679 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.681 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.681 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 10:25:01.681 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 10:25:01.681 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 10:25:01.682 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.684 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.685 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.687 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.687 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 10:25:01.687 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 10:25:01.687 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 10:25:01.690 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.691 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.693 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.695 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.695 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 10:25:01.695 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 10:25:01.695 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 10:25:01.697 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.699 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.701 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.705 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.705 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 10:25:01.706 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 10:25:01.706 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 10:25:01.709 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.712 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.716 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.718 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.718 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 10:25:01.718 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 10:25:01.718 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-25 10:25:01.719 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.722 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.723 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.724 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-25 10:25:01.724 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-25 10:25:01.724 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-25 10:25:02.551 |-INFO  [RMI TCP Connection(4)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 10:25:02.987 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-06-25 10:25:02.992 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-06-25 19:17:49.978 |-WARN  [Thread-26] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-06-25 19:17:49.978 |-INFO  [Thread-45] com.netflix.loadbalancer.PollingServerListUpdater [53] -| Shutting down the Executor Pool for PollingServerListUpdater
2025-06-25 19:17:49.978 |-WARN  [Thread-5] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-25 19:17:50.001 |-WARN  [Thread-26] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-06-25 19:17:50.033 |-WARN  [Thread-5] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
