2025-06-26 09:34:09.043 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-06-26 09:34:09.056 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-06-26 09:34:09.064 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-06-26 09:34:11.176 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$72e81759 [80] -| ===配置shiro路由规则====
2025-06-26 09:34:12.299 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-06-26 09:34:12.300 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-06-26 09:34:12.300 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-06-26 09:34:12.404 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-06-26 09:34:14.625 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-06-26 09:34:14.626 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-06-26 09:34:18.566 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-06-26 09:34:18.656 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-06-26 09:34:18.668 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 12.044 seconds (JVM running for 14.563)
2025-06-26 09:34:18.672 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-26 09:34:19.058 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-06-26 09:34:19.070 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-06-26 09:34:19.086 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@24032706
2025-06-26 09:34:19.201 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.204 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.207 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.209 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.210 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-26 09:34:19.210 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-26 09:34:19.210 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-26 09:34:19.212 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.215 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.229 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.232 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.232 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-26 09:34:19.234 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-26 09:34:19.234 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-26 09:34:19.239 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.245 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.249 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.257 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.257 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-26 09:34:19.257 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-26 09:34:19.257 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-26 09:34:19.261 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.265 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.275 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.284 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.284 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-26 09:34:19.285 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-26 09:34:19.285 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-26 09:34:19.290 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.297 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.301 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.304 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.304 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-26 09:34:19.304 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-26 09:34:19.304 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-26 09:34:19.308 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.313 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.316 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.317 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.317 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-26 09:34:19.317 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-26 09:34:19.317 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-26 09:34:19.318 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.321 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.327 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.331 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-26 09:34:19.331 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-26 09:34:19.331 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-26 09:34:19.944 |-INFO  [RMI TCP Connection(3)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 09:34:20.278 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-06-26 09:34:20.292 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-06-26 09:51:08.921 |-WARN  [Thread-7] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-26 09:51:08.921 |-INFO  [Thread-48] com.netflix.loadbalancer.PollingServerListUpdater [53] -| Shutting down the Executor Pool for PollingServerListUpdater
2025-06-26 09:51:08.921 |-WARN  [Thread-27] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-06-26 09:51:08.935 |-WARN  [Thread-27] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-06-26 09:51:08.951 |-WARN  [Thread-7] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
