2025-06-30 14:39:01.420 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-06-30 14:39:01.438 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-06-30 14:39:01.446 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-06-30 14:39:03.199 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$c954bf25 [80] -| ===配置shiro路由规则====
2025-06-30 14:39:04.040 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-06-30 14:39:04.041 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-06-30 14:39:04.041 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-06-30 14:39:04.129 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-06-30 14:39:06.093 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-06-30 14:39:06.094 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-06-30 14:39:09.907 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-06-30 14:39:10.084 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-06-30 14:39:10.115 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 11.387 seconds (JVM running for 12.38)
2025-06-30 14:39:10.119 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 14:39:10.681 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-06-30 14:39:10.691 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-06-30 14:39:10.714 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@58df2864
2025-06-30 14:39:11.050 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.060 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.067 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.070 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.070 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-30 14:39:11.070 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 14:39:11.070 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 14:39:11.082 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.087 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.092 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.100 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.100 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-30 14:39:11.100 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 14:39:11.100 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 14:39:11.114 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.118 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.124 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.128 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.128 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-30 14:39:11.128 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 14:39:11.129 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 14:39:11.131 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.138 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.141 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.148 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.148 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-30 14:39:11.148 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 14:39:11.149 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 14:39:11.152 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.158 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.163 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.165 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.165 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-30 14:39:11.165 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 14:39:11.165 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 14:39:11.168 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.171 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.173 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.175 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.175 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-30 14:39:11.175 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 14:39:11.175 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 14:39:11.176 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEFAULT 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.178 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.179 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.180 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: com.netflix.client.ClientException: Load balancer does not have available server for client: ams-crm
2025-06-30 14:39:11.181 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [128] -| 数据库中未找到DEFAULT配置，使用硬编码默认配置
2025-06-30 14:39:11.181 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 14:39:11.635 |-INFO  [RMI TCP Connection(1)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-30 14:39:11.875 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-06-30 14:39:11.878 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-06-30 14:40:06.459 |-WARN  [Thread-5] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-30 14:40:06.461 |-WARN  [Thread-25] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-06-30 14:40:06.461 |-WARN  [Thread-25] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-06-30 14:40:06.460 |-INFO  [Thread-46] com.netflix.loadbalancer.PollingServerListUpdater [53] -| Shutting down the Executor Pool for PollingServerListUpdater
2025-06-30 14:40:06.462 |-WARN  [Thread-5] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-06-30 14:40:06.477 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [90] -| De-registering from Nacos Server now...
2025-06-30 14:40:06.488 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [110] -| De-registration finished.
2025-06-30 17:57:19.590 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi.yml] & group[DEFAULT_GROUP]
2025-06-30 17:57:19.596 |-WARN  [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder [87] -| Ignore the empty nacos configuration and get it based on dataId[ams-tpi-prod.yml] & group[DEFAULT_GROUP]
2025-06-30 17:57:19.601 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [655] -| The following profiles are active: prod
2025-06-30 17:57:20.992 |-INFO  [main] cn.uone.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$2b08213f [80] -| ===配置shiro路由规则====
2025-06-30 17:57:21.789 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Initializing ProtocolHandler ["http-nio-8033"]
2025-06-30 17:57:21.789 |-INFO  [main] org.apache.catalina.core.StandardService [173] -| Starting service [Tomcat]
2025-06-30 17:57:21.790 |-INFO  [main] org.apache.catalina.core.StandardEngine [173] -| Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-06-30 17:57:21.862 |-INFO  [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring embedded WebApplicationContext
2025-06-30 17:57:23.634 |-INFO  [main] cn.uone.application.constant.TransferConfig [107] -| MD5KEY
2025-06-30 17:57:23.635 |-INFO  [main] cn.uone.application.constant.TransferConfig [118] -| msgSrc
2025-06-30 17:57:26.204 |-INFO  [main] org.apache.coyote.http11.Http11NioProtocol [173] -| Starting ProtocolHandler ["http-nio-8033"]
2025-06-30 17:57:26.241 |-INFO  [main] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [75] -| nacos registry, DEFAULT_GROUP ams-tpi ***************:8033 register finished
2025-06-30 17:57:26.246 |-INFO  [main] cn.uone.ams.tpi.TpiApplication [61] -| Started TpiApplication in 7.431 seconds (JVM running for 8.288)
2025-06-30 17:57:26.249 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 17:57:26.393 |-INFO  [main] com.netflix.loadbalancer.BaseLoadBalancer [197] -| Client: ams-crm instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-06-30 17:57:26.397 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [222] -| Using serverListUpdater PollingServerListUpdater
2025-06-30 17:57:26.426 |-INFO  [main] com.netflix.loadbalancer.DynamicServerListLoadBalancer [150] -| DynamicServerListLoadBalancer for client ams-crm initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=ams-crm,current list of Servers=[***************:8031],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:8031;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@4accc3de
2025-06-30 17:57:26.920 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-30 17:57:26.971 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.021 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.076 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.076 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 17:57:27.076 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 17:57:27.122 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-30 17:57:27.163 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.212 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.271 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.271 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 17:57:27.271 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 17:57:27.315 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-30 17:57:27.354 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.410 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.454 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.454 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 17:57:27.454 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 17:57:27.498 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-30 17:57:27.538 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.582 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.639 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.641 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 17:57:27.641 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 17:57:27.686 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-30 17:57:27.721 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.765 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.820 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.821 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 17:57:27.821 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 17:57:27.881 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-30 17:57:27.918 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:27.962 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:28.010 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:28.010 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 17:57:28.010 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [116] -| 开始初始化Cosmic系统配置...
2025-06-30 17:57:28.051 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [150] -| 成功加载配置: DEFAULT
2025-06-30 17:57:28.091 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 DEV 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=DEV&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:28.137 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 TEST 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=TEST&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:28.181 |-WARN  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [152] -| 加载配置 PRODUCTION 失败: [500 ] during [GET] to [http://ams-crm/expense-config-entity/getExpenseConfigByCode?code=PRODUCTION&configType=cosmic] [IExpenseConfigFegin#getExpenseConfigByCode(String,String)]: [{"code":500,"success":false,"message":"访问出错: No message available"}]
2025-06-30 17:57:28.181 |-INFO  [main] cn.uone.ams.tpi.api.cosmic.CosmicBaseApi [132] -| Cosmic系统配置初始化完成，已加载 1 个配置
2025-06-30 17:57:28.329 |-INFO  [RMI TCP Connection(1)-127.0.0.1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] [173] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-30 17:57:28.443 |-INFO  [boundedElastic-1] io.lettuce.core.EpollProvider [101] -| Starting without optional epoll library
2025-06-30 17:57:28.445 |-INFO  [boundedElastic-1] io.lettuce.core.KqueueProvider [101] -| Starting without optional kqueue library
2025-06-30 17:58:11.184 |-WARN  [Thread-21] com.alibaba.nacos.common.notify.NotifyCenter [145] -| [NotifyCenter] Start destroying Publisher
2025-06-30 17:58:11.184 |-WARN  [Thread-4] com.alibaba.nacos.common.http.HttpClientBeanHolder [108] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-30 17:58:11.184 |-INFO  [Thread-38] com.netflix.loadbalancer.PollingServerListUpdater [53] -| Shutting down the Executor Pool for PollingServerListUpdater
2025-06-30 17:58:11.187 |-WARN  [Thread-21] com.alibaba.nacos.common.notify.NotifyCenter [162] -| [NotifyCenter] Destruction of the end
2025-06-30 17:58:11.189 |-WARN  [Thread-4] com.alibaba.nacos.common.http.HttpClientBeanHolder [114] -| [HttpClientBeanHolder] Destruction of the end
2025-06-30 17:58:11.242 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [90] -| De-registering from Nacos Server now...
2025-06-30 17:58:11.251 |-INFO  [SpringContextShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry [110] -| De-registration finished.
