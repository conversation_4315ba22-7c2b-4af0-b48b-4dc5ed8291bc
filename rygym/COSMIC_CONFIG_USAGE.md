# Cosmic系统配置数据库化改造说明

## 概述
`CosmicBaseApi` 已从硬编码配置改为从数据库动态获取配置，支持多环境、多项目的灵活配置管理。

## 主要改动

### 1. 配置获取方式变更
- **原方式**: 硬编码静态常量
- **新方式**: 通过 `IExpenseConfigFegin` 从数据库获取配置
- **配置类型**: `ApiTypeEnum.COSMIC`
- **缓存机制**: 配置缓存1小时，提高性能

### 2. 方法签名变更
多数公共方法新增了 `configCode` 参数的重载版本：

```java
// 原方法（保持向后兼容）
public String getToken() throws Exception

// 新方法
public String getToken(String configCode) throws Exception

// 原方法（保持向后兼容）
public RestResponse settleTypeQuery()

// 新方法
public RestResponse settleTypeQuery(String configCode)
```

### 3. 配置参数说明
数据库配置JSON格式：
```json
{
    "url": "http://edstest.xmgdjt.com.cn:9000/cosmic/{}?appKey=Fy2jZ7xKuc58",
    "clientId": "DMK",
    "clientSecret": "Qffhrweio123@fhdsjbn", 
    "username": "duomikegongyuxitong",
    "accountId": "1951600800203540480"
}
```

## 数据库配置

### 配置表结构
使用现有的 `t_expense_config` 表：
- `expense_project_code`: 配置编码（如：DEFAULT, PROJECT_A, PRODUCTION）
- `config_type`: 固定为 'cosmic'
- `config_content`: JSON格式的配置内容

### 配置示例
参考 `cosmic_config_example.sql` 文件中的示例配置。

## 使用方式

### 1. 使用默认配置
```java
@Autowired
private CosmicBaseApi cosmicBaseApi;

// 使用默认配置获取token
String token = cosmicBaseApi.getToken(); // 等同于 getToken("DEFAULT")

// 使用默认配置查询结算方式
RestResponse response = cosmicBaseApi.settleTypeQuery();
```

### 2. 使用指定配置
```java
// 使用项目A的配置
String token = cosmicBaseApi.getToken("PROJECT_A");

// 使用生产环境配置查询
RestResponse response = cosmicBaseApi.settleTypeQuery("PRODUCTION");

// 使用特定配置进行基础数据查询
RestResponse response = cosmicBaseApi.baseDataQuery(
    CosmicBaseUrl.ADMIN_ORG, 
    queryData, 
    "PROJECT_A"
);
```

### 3. 其他业务类中的调用示例
```java
@Service
public class SomeBusinessService {
    
    @Autowired
    private CosmicBaseApi cosmicBaseApi;
    
    public void processWithProjectConfig(String projectCode) {
        try {
            // 根据项目编码获取对应配置的token
            String token = cosmicBaseApi.getToken(projectCode);
            
            // 使用项目特定配置查询数据
            RestResponse response = cosmicBaseApi.settleTypeQuery(projectCode);
            
            // 处理业务逻辑...
        } catch (Exception e) {
            // 错误处理
        }
    }
}
```

## 向后兼容性
- 所有原有的无参数方法仍然可用，默认使用 "DEFAULT" 配置
- 现有调用代码无需修改即可继续工作
- 新功能可逐步采用带配置参数的方法

## 配置管理建议

### 1. 配置编码命名规范
- `DEFAULT`: 默认配置
- `DEV`: 开发环境
- `TEST`: 测试环境  
- `PRODUCTION`: 生产环境
- `PROJECT_[项目名]`: 项目特定配置

### 2. 配置更新
- 配置变更后会在1小时内自动生效（缓存过期）
- 如需立即生效，可重启应用或清除相关缓存

### 3. 安全考虑
- `clientSecret` 等敏感信息建议加密存储
- 不同环境使用不同的密钥和账户

## 错误处理
- 当指定的配置编码不存在时，会使用默认值
- 配置格式错误时会记录日志并降级到默认配置
- 网络异常时会通过现有的异常处理机制返回错误信息

## 性能优化
- 配置信息缓存1小时，减少数据库查询
- Token按配置编码分别缓存，避免冲突
- 支持不同配置的并发访问 