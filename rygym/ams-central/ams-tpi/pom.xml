<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ams-central</artifactId>
        <groupId>cn.uone</groupId>
        <version>1.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ams-tpi</artifactId>
    <description>第三方接口服务</description>

    <properties>
        <!-- 安全依赖版本控制 -->
        <fastjson.version>2.0.54</fastjson.version>
        <shiro.version>1.13.0</shiro.version>
        <mysql.connector.version>8.4.0</mysql.connector.version>
        <snakeyaml.version>1.26</snakeyaml.version>
        <okhttp.version>4.12.0</okhttp.version>
        <log4j.version>2.21.1</log4j.version>
        <velocity.version>2.3</velocity.version>
        <logback.version>1.2.12</logback.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.uone</groupId>
            <artifactId>ams-bean</artifactId>
            <version>${uone.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.uone</groupId>
            <artifactId>uone-shiro-starter</artifactId>
        </dependency>

<!--        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>spring-cloud-starter-huawei-service-engine</artifactId>
            <version>1.5.1-Hoxton</version>
        </dependency>-->

        <dependency>
            <groupId>com.domeke</groupId>
            <artifactId>alihouse</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.uone</groupId>
            <artifactId>taobao</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.uone</groupId>
            <artifactId>mogoroom</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>${snakeyaml.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>

        <!-- 法大大 -->
        <dependency>
            <groupId>org.apache.james</groupId>
            <artifactId>apache-mime4j</artifactId>
            <version>0.6</version>
        </dependency>

        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.0</version>
        </dependency>

        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>1.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.1</version>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>cn.uone.fadada</groupId>-->
        <!--<artifactId>fadada_api_sdk</artifactId>-->
        <!--<version>2.0.1</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>cn.uone.fadada</groupId>
            <artifactId>fadada_api_sdk</artifactId>
            <version>2.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.54</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>${shiro.version}</version>
        </dependency>


        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.0.0</version>
        </dependency>
        <!--<dependency>
            <groupId>com.nuonuo</groupId>
            <artifactId>open-sdk</artifactId>
            <version>1.0.5</version>
        </dependency>-->
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>

        <!-- axis 1.4 jar start -->
        <!--<dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>commons-discovery</groupId>
            <artifactId>commons-discovery</artifactId>
            <version>0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis-jaxrpc</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis-saaj</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>wsdl4j</groupId>
            <artifactId>wsdl4j</artifactId>
            <version>1.4</version>
        </dependency>-->
        <!-- axis 1.4 jar end -->

        <!-- mqtt -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jcraft.jzlib</groupId>
            <artifactId>com.springsource.com.jcraft.jzlib</artifactId>
            <version>1.0.7</version>
        </dependency>

        <!-- 阿里云短信-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 契约锁-->
        <dependency>
            <groupId>com.domeke</groupId>
            <artifactId>qiyuesuo</artifactId>
            <version>3.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.domeke</groupId>
            <artifactId>qiyuesuo-private</artifactId>
            <version>4.3.7</version>
        </dependency>

        <!-- minio存储桶 -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>7.1.0</version>
        </dependency>

        <!--嘉讯MAS短信 -->
        <dependency>
            <groupId>jaxun</groupId>
            <artifactId>mas</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>jaxun</groupId>
            <artifactId>mysql.driver</artifactId>
            <version>3.1.14</version>
        </dependency>

        <!--航信 -->
        <dependency>
            <groupId>com.nuonuo</groupId>
            <artifactId>open-sdk</artifactId>
            <version>1.0.5.2</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2.2.5.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
            </exclusions>
        </dependency>



        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>

        <!-- SpringBoot Admin Client -->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>2.2.3</version>
        </dependency>

        <!-- 工行sdk -->
        <dependency>
            <groupId>com.icbc</groupId>
            <artifactId>hsm-software-share</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.icbc</groupId>
            <artifactId>icbc-api-sdk-cop</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.icbc</groupId>
            <artifactId>icbc-api-sdk-cop-io</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.icbc</groupId>
            <artifactId>icbc-ca</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.icbc</groupId>
            <artifactId>InfosecCrypto</artifactId>
            <version>1.2.0</version>
        </dependency>

        <!-- 添加JSch依赖 -->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>

        <!-- 触点门禁sdk -->
        <dependency>
            <groupId>sdk.trudian</groupId>
            <artifactId>tdcloud</artifactId>
            <version>v1.0.1</version>
        </dependency>

        <!--触点门禁 直连sdk-->
        <dependency>
            <groupId>sdk.trudian</groupId>
            <artifactId>td-direct</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--图片压缩-->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>

        <!--银企直连-->
        <dependency>
            <groupId>sdk.dcfirm</groupId>
            <artifactId>dcfirmsdk-all</artifactId>
            <version>1.7</version>
            <exclusions>
                <!-- 排除第三方SDK的SLF4J绑定，避免冲突 -->
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
       <!-- <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>2.3.3</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.2.11</version>
        </dependency>-->
        <!-- Netty -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.100.Final</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- JAXB -->
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>3.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>3.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Bouncy Castle -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.76</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources/</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>excel/*/*.*</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources/</directory>
                <filtering>false</filtering>
                <includes>
                    <include>excel/*/*.*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 拷贝jar包到build/lib目录下 -->
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <overWriteReleases>true</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>false</overWriteIfNewer>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <!-- 配置主程序入口，及classpath -->
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>cn.uone.ams.tpi.TpiApplication</mainClass>
                        </manifest>
                        <!--                        <manifestEntries>-->
                        <!--                            &lt;!&ndash; 配置jar包资源文件目录 &ndash;&gt;-->
                        <!--                            <Class-Path>META-INF/</Class-Path>-->
                        <!--                        </manifestEntries>-->
                    </archive>
                    <!-- 将jar包里的所有资源文件排除掉 -->
                    <excludes>
                        <exclude>**/bootstrap-dev.yml</exclude>
                        <exclude>**/bootstrap-prod.yml</exclude>
                        <exclude>**/bootstrap-test.yml</exclude>
                        <exclude>**/bootstrap-dmk.yml</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
