package cn.uone.ams.tpi.api;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.ams.tpi.properties.WechatProp;
import cn.uone.cache.util.CacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName WxReqSubMsgApi
 * @Description TODO 微信订阅消息接口
 * <AUTHOR>
 * @Date 2020/10/26 15:19
 * @Version 1.0
 */
@Component
@Slf4j
public class WxReqSubMsgApi {

    private static String mpAppId;
    private static String mpAppSecret;

    /**
     * 初始化参数
     * @param config
     */
    public void initConfig(JSONObject config){
        this.mpAppId = config.getStr("mpAppId");
        this.mpAppSecret = config.getStr("mpAppSecret");
    }

    public String getToken(){
        String reqUrl = StrUtil.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={}&secret={}",mpAppId,mpAppSecret);
        String res = HttpUtil.get(reqUrl);
        //System.out.println(res);
        JSONObject resJson = JSONUtil.parseObj(res);
        String token = resJson.getStr("access_token");
        return token;
    }

    public JSONObject sendSubscribe(String openId, String title,Map<String,Object> param){
        Map<String,Object> paras = getTemplateData(title, param);
        paras.put("touser",openId);
        paras.put("template_id",getMessageTemplateId(title));
        paras.put("miniprogram_state","developer");//developer为开发版；trial为体验版；formal为正式版
        String res = HttpRequest.post("https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token="+getToken())
                .body(JSONUtil.toJsonStr(paras),"application/json")
                .execute().body();
        Console.log("{}-微信消息发送：{}",title,res);
        JSONObject resJson = JSONUtil.parseObj(res);
        return resJson;
    }

    /**
     * 账单通知模板
     * @return
     */
    private JSONObject getBillTemplate(Map<String,Object> paras){
        String sourceName = paras.get("sourceName").toString();
        String payableTime = paras.get("payableTime").toString();
        String payment = paras.get("payment").toString();
        String renterName = paras.get("renterName").toString();
        StringBuffer sbf = new StringBuffer();
        sbf.append("{\"thing2\":{\"value\":\"{}\"}");
        sbf.append(",\"date3\":{\"value\":\"{}\"}");
        sbf.append(",\"amount4\":{\"value\":\"{}\"}");
        sbf.append(",\"thing6\":{\"value\":\"{}\"}}");
        String str = StrUtil.format(sbf.toString(),sourceName,payableTime,payment,renterName);
        return JSONUtil.parseObj(str);
    }

    /**
     * 获取模板内容
     * @return
     */
    private Map<String,Object> getTemplateData(String title,Map<String,Object> paras){
        String str = null;
        String page = null;
        switch (title) {
            case "合同待签字提醒":
                String sourceName = paras.get("sourceName").toString();
                String remark = paras.get("remark").toString();
                str = StrUtil.format("{\"thing1\":{\"value\":\"{}\"},\"thing2\":{\"value\":\"{}\"}",sourceName,remark);
                page = "pages2/personal/contract/contract";
                break;
            default:
                break;
        }
        Map<String,Object> map = new HashMap<>();
        map.put("data",JSONUtil.parseObj(str));
        map.put("page",page);
        return map;
    }

    /**
     * 获取模板id
     * @return
     */
    private String getMessageTemplateId(String title){
        Map<String,String> map = (Map<String, String>) CacheUtil.get("WECHAT_MSG_TEMPLATE_"+mpAppId);
        if(map == null){
            String res = HttpUtil.get("https://api.weixin.qq.com/wxaapi/newtmpl/gettemplate?access_token="+getToken());
            JSONObject obj = JSONUtil.parseObj(res);
            int errcode = obj.getInt("errcode");
            if(errcode == 0){
                JSONArray dataArr = obj.getJSONArray("data");
                for(int i = 0;i < dataArr.size();i++){
                    JSONObject dataObj = dataArr.getJSONObject(i);
                    map.put(dataObj.getStr("title"),dataObj.getStr("priTmplId"));
                }
                CacheUtil.putEx("WECHAT_MSG_TEMPLATE_"+mpAppId,map,1l, TimeUnit.DAYS);
            }
        }
        return map.get(title);
    }

    public static void main(String[] args) {
        //sendSubscribe();
    }
}
