package cn.uone.ams.tpi.api;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.tpi.contractRegistration.ContractRegistrationVo;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ContractRegistrationInterface {

    // 接口地址前缀
    private static final String TEST_URL_PREFIX = "http://222.76.242.143:7080";
    private static final String FORMAL_URL_PREFIX = "https://fwzl.szjj.xm.gov.cn:8101";

    // 接口路径
    private static final String TOKEN_PATH = "/platform-api/updt-auth/oauth/token";
    private static final String FILE_UPLOAD_PATH = "/platform-api/updt-file/repository/file/mongo/upload";
    private static final String CONTRACT_FILING_PATH = "/platform-api/updt-house-third/covenant/filings";

    // 获取token接口参数
    private static final String CLIENT_ID = "admin";
    private static final String CLIENT_SECRET = "nveeGLmc6FejlIxfHnudag==";
    private static final String GRANT_TYPE = "password";
    private static final String SCOPE = "all";

    // 用于存储token信息
    private static String token;
    private static String tokenHead;

    public static void main(String[] args) {
        getToken("xmaimeng","jR8fI872UTyFz6v5oChXLQ==");
        if(StrUtil.isNotBlank(token)){
            uploadFile(FileUtil.file("C:\\Users\\<USER>\\Desktop\\123.docx"));
        }
    }

    // 获取token
    public static boolean getToken(String username, String password) {
        try {
            // 构建请求参数
            String requestData = "username=" + username + "&password=" + password + "&grant_type=" + GRANT_TYPE + "&scope=" + SCOPE + "&client_id=" + CLIENT_ID + "&client_secret=" + CLIENT_SECRET;

            // 发送POST请求获取token
            URL url = new URL(TEST_URL_PREFIX + TOKEN_PATH);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setDoOutput(true);

            try (DataOutputStream dos = new DataOutputStream(connection.getOutputStream())) {
                dos.writeBytes(requestData);
            }

            // 解析响应获取token
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                Gson gson = new Gson();
                Map<String, Object> tokenResponse = gson.fromJson(response.toString(), HashMap.class);
                if (tokenResponse != null && "200.0".equals(tokenResponse.get("code").toString())) {
                    token = (String) tokenResponse.get("data.token");
                    tokenHead = (String) tokenResponse.get("data.tokenHead");
                    return true;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    // 文件上传
    public static String uploadFile(File file) {
        try {
            // 构建请求头
            URL url = new URL(TEST_URL_PREFIX + FILE_UPLOAD_PATH);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", tokenHead + token);
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + UUID.randomUUID().toString());

            // 设置请求体
            connection.setDoOutput(true);
            try (DataOutputStream dos = new DataOutputStream(connection.getOutputStream())) {
                // 文件部分
                dos.writeBytes("--" + connection.getRequestProperty("Content-Type").substring(connection.getRequestProperty("Content-Type").indexOf("boundary=") + 9) + "\r\n");
                dos.writeBytes("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"\r\n");
                dos.writeBytes("Content-Type: " + URLConnection.guessContentTypeFromName(file.getName()) + "\r\n");
                dos.writeBytes("\r\n");

                try (FileInputStream fis = new FileInputStream(file)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        dos.write(buffer, 0, bytesRead);
                    }
                }

                dos.writeBytes("\r\n--" + connection.getRequestProperty("Content-Type").substring(connection.getRequestProperty("Content-Type").indexOf("boundary=") + 9) + "--\r\n");
            }

            // 获取文件标识码
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                Gson gson = new Gson();
                Map<String, Object> fileUploadResponse = gson.fromJson(response.toString(), HashMap.class);
                if (fileUploadResponse != null && "200".equals(fileUploadResponse.get("code"))) {
                    return (String) fileUploadResponse.get("data.sfid");
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    // 推送合同备案
    public static boolean pushContractRegistration(ContractRegistrationVo data) {
        try {
            // 将数据转换为JSON字符串并进行两次Base64编码
            Gson gson = new Gson();
            String jsonData = gson.toJson(data);
            String encodedData = Base64.getEncoder().encodeToString(jsonData.getBytes());
            encodedData = Base64.getEncoder().encodeToString(encodedData.getBytes());

            // 构建请求参数
            String requestData = "jsonData=" + encodedData;

            // 发送POST请求推送合同备案
            URL url = new URL(TEST_URL_PREFIX + CONTRACT_FILING_PATH);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", tokenHead + " " + token);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setDoOutput(true);

            try (DataOutputStream dos = new DataOutputStream(connection.getOutputStream())) {
                dos.writeBytes(requestData);
            }

            // 解析响应判断推送结果
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                Gson responseGson = new Gson();
                Map<String, Object> filingResponse = responseGson.fromJson(response.toString(), HashMap.class);
                return filingResponse != null && "200".equals(filingResponse.get("code")) && (boolean) filingResponse.get("data");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }
}