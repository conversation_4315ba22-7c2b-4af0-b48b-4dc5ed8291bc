package cn.uone.ams.tpi.api;

import cn.hutool.core.util.StrUtil;
import cn.uone.ams.tpi.kingdee.login.EASLoginProxy;
import cn.uone.ams.tpi.kingdee.login.WSContext;
import cn.uone.ams.tpi.properties.XyKingdeeProp;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import javax.xml.namespace.QName;
import java.net.URL;

/**
 * @ClassName XyKingdeeApi
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/11 16:00
 * @Version 1.0
 */
@Component
public class XyKingdeeApi {
    private String url;
    private String user;
    private String pwd;
    private String slnName;
    private String dcName;
    private String language;
    private static int dbType = 2;
    private static EASLoginProxy loginProxy = null;
    private static WSContext wsCtx = null;
    private static final String ERR_LOGININFO = "Eas登录失败";

    public XyKingdeeApi(XyKingdeeProp xyKingdeeProp){
        this.url = xyKingdeeProp.getUrl();
        this.user = xyKingdeeProp.getUser();
        this.pwd = xyKingdeeProp.getPwd();
        this.slnName = xyKingdeeProp.getSlnName();
        this.dcName = xyKingdeeProp.getDcName();
        this.language = xyKingdeeProp.getLanguage();
    }

    public static void main(String[] args) {
        //长租付款单 json
        String BILLBOSTYPE_CZPAYBILL="{ \"bosType\": \"32C1ADA8\", \"company\": \"101001\", \"data\": { \"account\": \"**********\", \"amount\": \"10\", \"collectionAccount\": \"**********\", \"customer\": \"111\", \"entrys\": [ { \"amount\": \"100\", \"costDescription\": \"这是备注\", \"expenseType\": \"0101\" }, { \"amount\": \"100\", \"costDescription\": \"这是备注\", \"expenseType\": \"0101\" } ], \"payableAmoun\": \"100\", \"paymentBank\": \"************\", \"paymentDate\": \"2021-01-21\", \"product\": \"161PJ01\", \"project\": \"611PJ01\", \"receivingBank\": \"************\", \"subject\": \"100101\", \"type\": \"SR0502\" }, \"id\": \"*********\", \"number\": \"************\" }";
        //长租收款单 json
        String BILLBOSTYPE_CZRECEIVINGBILL="{ \"bosType\": \"72EBBD20\", \"company\": \"101001\", \"data\": { \"channel\":\"微信\", \"amount\":\"100\", \"serviceCharge\":\"200\", \"totalAmount\":\"300\", \"entryDate\":\"2021-01-22\", \"entrys\": [ { \"type\": \"SR01\", \"amount\": \"1000\", \"account\": \"**********\", \"collectionDate\": \"2021-01-23\", \"customer\":\"111\", \"product\":\"161PJ01\", \"project\":\"161PJ01\" }, { \"type\": \"SR01\", \"amount\": \"1000\", \"account\": \"**********\", \"collectionDate\": \"2021-01-23\", \"customer\":\"111\", \"product\":\"161PJ01\", \"project\":\"161PJ01\" } ], }, \"id\": \"2222222\", \"number\": \"************\" }";
        //长租应收账单管理
        String BILLBOSTYPE_CZACCOUNTSRECEIVABLE="{ \"bosType\": \"F0C2D029\", \"company\": \"101001\", \"data\": { \"period\":\"202106\", \"project\":\"161PJ01\", \"entrys\": [ { \"paymentType\": \"SR03\", \"amount\": \"1000\", \"taxRate\": \"0.06\", \"taxAmount\": \"2021\", \"noTaxAmount\":\"120\", \"customer\":\"111\", \"product\":\"161PJ01\" }, { \"paymentType\": \"SR03\", \"amount\": \"1000\", \"taxRate\": \"0.06\", \"taxAmount\": \"2021\", \"noTaxAmount\":\"120\", \"customer\":\"111\", \"product\":\"161PJ01\" } ] }, \"id\": \"2222222\", \"number\": \"************\" }";
        // 长租转款单
        String BILLBOSTYPE_CZTRANSFER="{ \"bosType\": \"21BEDA32\", \"company\": \"101001\", \"data\": { \"period\":\"202106\", \"project\":\"161PJ01\", \"entrys\": [ { \"customer\": \"111\", \"product\": \"161PJ01\", \"project\": \"161PJ01\", \"outType\": \"SR01\", \"outAmount\":\"120\", \"intoType\":\"SR01\", \"intoAmount\":\"200\", \"intoCustomer\":\"111\", \"intoProduct\":\"161PJ01\", \"intoProject\":\"161PJ01\", \"intoTime\":\"2021-01-23\", \"remarks\":\"备注备注1\" }, { \"customer\": \"111\", \"product\": \"161PJ01\", \"project\": \"161PJ01\", \"outType\": \"SR01\", \"outAmount\":\"120\", \"intoType\":\"SR01\", \"intoAmount\":\"200\", \"intoCustomer\":\"111\", \"intoProduct\":\"161PJ01\", \"intoProject\":\"161PJ01\", \"intoTime\":\"2021-01-23\", \"remarks\":\"备注备注2\" } ] }, \"id\": \"2222222\", \"number\": \"************\" }";
        // 长租开票信息
        String BILLBOSTYPE_CZBILLINGINFO="{ \"bosType\": \"7DEA2A22\", \"company\": \"101001\", \"data\": { \"startPeriod\":\"2021-06-01\", \"endPeriod\":\"2021-06-30\", \"project\":\"161PJ01\", \"entrys\": [ { \"paymentType\": \"SR01\", \"customer\": \"111\", \"startPeriod\":\"2021-06-01\", \"endPeriod\":\"2021-06-30\", \"taxAmount\":\"100\", \"amount\":\"2000\" }, { \"paymentType\": \"SR01\", \"customer\": \"111\", \"startPeriod\":\"2021-06-01\", \"endPeriod\":\"2021-06-30\", \"taxAmount\":\"100\", \"amount\":\"2000\" } ] }, \"id\": \"2222222\", \"number\": \"************\" }";
        // 收入摊销
        String BILLBOSTYPE_CZINCOMEAMORTIZATION="{ \"bosType\": \"1E904F4F\", \"company\": \"101001\", \"data\": { \"project\":\"161PJ01\", \"dyPeriod\":\"202106\", \"pushPeriod\":\"202106\", \"entrys\": [ { \"amount\": \"1000\", \"customer\": \"111\", \"type\":\"SR01\", \"period\":\"202103\", \"product\":\"161PJ01\" }, { \"amount\": \"1000\", \"customer\": \"111\", \"type\":\"SR01\", \"period\":\"202103\", \"product\":\"161PJ01\" } ] }, \"id\": \"2222222\", \"number\": \"************\" }";
        //kingdeePush(BILLBOSTYPE_CZPAYBILL);
    }
    public JSONObject kingdeePush(String json,String kingdeeId){
        return kingdeeCall(json,kingdeeId,true);
    }

    public boolean isExist(String kingdeeId){
        if(StrUtil.isNotBlank(kingdeeId)){
            JSONObject res = kingdeeCall(null,kingdeeId,false);
            if("S".equals(res.getString("result"))){
                return true;
            }
        }
        return false;
    }

    private JSONObject kingdeeCall(String json,String kingdeeId,boolean isDoPush) {
       /* JSONObject res = new JSONObject();
        try {
            //setParameter();
            //1.登录
            Service s = new Service();
            Call call=(Call)s.createCall();
            call.setOperationName("login");
            call.setTargetEndpointAddress(url + "EASLogin?wsdl");
            call.setReturnType(new QName("urn:client","WSContext"));
            call.setReturnClass(WSContext.class);
            call.setReturnQName(new QName("","loginReturn"));
            call.setTimeout(Integer.valueOf(1000*600000*60));
            call.setMaintainSession(true);
            //登陆接口参数
            WSContext rs = (WSContext)call.invoke(new Object[]{user, pwd, slnName, dcName, language, dbType});
            System.out.println("Eas登录成功sessionId ====" + rs. getSessionId());
            if(rs. getSessionId() == null){
                System.out.println(ERR_LOGININFO);
                res.put("result","E");
                res.put("msg",ERR_LOGININFO);
                return res;
            }
            //清理
            call.clearOperation();
            // 2.查询单据是否存在
            //设置登录返回的session在soap头 "http://login.webservice.bos.kingdee.com"是固定的
            SOAPHeaderElement header=new SOAPHeaderElement("http://login.webservice.bos.kingdee.com","SessionId", rs.getSessionId());
            call.addHeader(header);
            if(StrUtil.isNotBlank(kingdeeId)){
                call.setOperationName("queryBillExist");
                call.setTargetEndpointAddress(url + "WSXyCzInteractFacade?wsdl");
                call.setReturnQName(new QName("","queryBillExistReturn"));
                call.setTimeout(Integer.valueOf(1000*600000*60));
                call.setMaintainSession(true);
                //接口参数
                String aa=(String)call.invoke(new Object[]{kingdeeId} );
                System.out.println("查询单据是否存在"+aa);
                JSONObject isExist = JSON.parseObject(aa);
                if(!isDoPush){
                    return isExist;
                }
                if("S".equals(isExist.getString("result"))){
                    res.put("result","E");
                    res.put("msg","该条记录已推送过");
                    return res;
                }
                //清理
                call.clearOperation();
            }
            // 3.推送单据
            call.setOperationName("syncBillInfo");
            call.setTargetEndpointAddress(url + "WSXyCzInteractFacade?wsdl");
            call.setReturnQName(new QName("","syncBillInfoReturn"));
            call.setTimeout(Integer.valueOf(1000*600000*60));
            call.setMaintainSession(true);
            //设置登录返回的session在soap头 "http://login.webservice.bos.kingdee.com"是固定的
            header=new SOAPHeaderElement("http://login.webservice.bos.kingdee.com","SessionId", rs.getSessionId());
            call.addHeader(header);
            //接口参数
            String result=(String)call.invoke(new Object[]{json} );
            System.out.println(result);
            System.out.println("推送单据数据"+result);
            res = JSON.parseObject(result);
        }catch (Exception e){
            e.printStackTrace();
            res.put("result","E");
            res.put("msg","推送接口出错");
            return res;
        }
        return res;*/
        return null;
    }

    /**
     * 客户端登录金蝶EAS
     *
     * @return
     * <AUTHOR>
     */

    public boolean loginRemoteEAS() {
        try {
            URL endpoint = new URL(url + "EASLogin");
            //loginProxy = new EASLoginProxyServiceLocator() .getEASLogin(endpoint);
            //wsCtx = loginProxy.login(user, pwd, slnName, dcName, language, dbType);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        //获取session
        //System.out.println("Eas登录成功sessionId ====" + wsCtx.getSessionId());
        return true;
    }



    /**
     * @return void
     * <AUTHOR>
     * @Description 设置参数
     * @Param []
     **/
    public void setParameter() {
        user = "xydczx";
        pwd ="123456";
        url = "http://***********:6888/ormrpc/services/";
        //url = "http://127.0.0.1:6888/ormrpc/services/";
        dcName = "dc3uat6";
        language = "L2";
        slnName = "eas";
    }
}
