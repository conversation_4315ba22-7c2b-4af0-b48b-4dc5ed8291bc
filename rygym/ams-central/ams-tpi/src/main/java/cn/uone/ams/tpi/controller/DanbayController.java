package cn.uone.ams.tpi.controller;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.ams.tpi.properties.DanbayProp;
import cn.uone.application.constant.BaseConstants;
import cn.uone.cache.util.CacheUtil;
import cn.uone.fegin.sys.ISysInterfaceMsgFegin;
import cn.uone.fegin.tpi.IDanbayFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import io.swagger.annotations.Api;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Api(value = "蛋贝设备接口", tags = {"蛋贝设备接口"})
@RestController
@RequestMapping("/Danbay")
public class DanbayController extends BaseController implements IDanbayFegin {

    @Autowired
    private ISysInterfaceMsgFegin sysInterfaceMsgFegin;

    @Autowired
    private DanbayProp danbayProp;


    @PostMapping("/testToken")
    @UonePermissions
    public RestResponse testToken() throws Exception {
        String token = getToken();
        return RestResponse.success(token);
    }

    /**
     * 水电表读数获取
     *
     * @return
     */
    @Override
    @GetMapping("/getRead")
    @UonePermissions
    public RestResponse getRead(String code) throws Exception {
        String url = danbayProp.getBaseUrl() + "deviceInfo/getNewDegree";
        Map<String, Object> map = new HashMap<>();
        HttpRequest hq = getHttpRequest(url, map);
        map.put("deviceId", code);
        hq.form(map);
        HttpResponse hp = hq.execute();
        JSONObject json = JSONUtil.parseObj(hp.body());
        String state = json.getStr("status");
        sysInterfaceMsgFegin.createLog(map.toString(), json.toString(), "蛋贝水电表查询报文");
        if (!"200".equals(state)) {
            return RestResponse.failure("读数失败,原因为:" + json.get("message"));
        }
        JSONObject data = (JSONObject) json.get("result");
        return resultFor(map, data, "readData");
    }

    /**
     * 阀门打开
     * action 1 是开 0是关
     *
     * @return
     */
    @Override
    @GetMapping("/openClose")
    @UonePermissions
    public RestResponse openClose(String code, String action) throws Exception {
        for (int i = 0; i < 3; i++) {
            String gate = gateControl(code, action);
            if (action == "1") {
                //因不同型号设备对合闸支持差异，有“自动合闸”和“手动合闸”两种合闸方式，建议合闸时同时调用“自动合闸”和“手动合闸”两个命令。
                if ("200".equals(gate)) {
                    Thread.sleep(2000);
                    String state = getGateStatus(code);
                    if (!BaseConstants.BOOLEAN_OF_TRUE.equals(state)) {
                        Thread.sleep(2000);
                        //调用“手动合闸”。
                        String gate2 = gateControl(code, "2");
                        if ("200".equals(gate2)) {
                            Thread.sleep(2000);
                            String state2 = getGateStatus(code);
                            if (!BaseConstants.BOOLEAN_OF_TRUE.equals(state2)) {
                                //若仍未合闸成功，重复以上步骤3次
                                Thread.sleep(2000);
                            }
                        }
                    } else {
                        return RestResponse.success();
                    }
                } else {
                    break;
                }
            } else {
                if ("200".equals(gate)) {
                    return RestResponse.success();
                } else {
                    break;
                }
            }
        }
        return RestResponse.failure("操作失败");
    }

    //电表拉合闸
    public String gateControl(String code, String action) throws Exception {
        String url = danbayProp.getBaseUrl() + "deviceInfo/energy/gateControl";
        Map<String, Object> map = new HashMap<>();
        HttpRequest hq = getHttpRequest(url, map);
        map.put("deviceId", code);
        map.put("control_type", action);
        hq.form(map);
        HttpResponse hp = hq.execute();
        JSONObject json = JSONUtil.parseObj(hp.body());
        String state = json.getStr("status");
        sysInterfaceMsgFegin.createLog(map.toString(), json.toString(), "蛋贝水电表开关报文");
        return state;
    }

    //查询电表状态
    public String getGateStatus(String code) throws Exception {
        String url = danbayProp.getBaseUrl() + "deviceInfo/energy/getGateStatus";
        Map<String, Object> map = new HashMap<>();
        HttpRequest hq = getHttpRequest(url, map);
        map.put("deviceId", code);
        hq.form(map);
        HttpResponse hp = hq.execute();
        JSONObject json = JSONUtil.parseObj(hp.body());
        String state = json.getStr("status");
        sysInterfaceMsgFegin.createLog(map.toString(), json.toString(), "蛋贝水电表查询报文");
        if (!"200".equals(state)) {
            return "-1";
        }
        JSONObject data = (JSONObject) json.get("result");
        return data.get("status").toString();
    }


    @Override
    @RequestMapping("/authorization")
    @UonePermissions
    public RestResponse authorization(String code, String password, String startDate, String endDate) throws Exception {
        String url = danbayProp.getBaseUrl() + "deviceCtrl/lockPwd/addPwdWithDate";
        Map<String, Object> map = new HashMap<>();
        HttpRequest hq = getHttpRequest(url, map);
        map.put("deviceId", code);
        map.put("password", password);
        map.put("pwdType", "3");
        map.put("beginTime", startDate);
        map.put("endTime", endDate);

        hq.form(map);
        HttpResponse hp = hq.execute();
        JSONObject json = JSONUtil.parseObj(hp.body());
        String state = json.getStr("status");
        sysInterfaceMsgFegin.createLog(map.toString(), json.toString(), "蛋贝门锁授权报文");
        if (!"200".equals(state)) {
            return RestResponse.failure("授权失败,原因为:" + json.get("message"));
        }
        JSONObject data = (JSONObject) json.get("result");
        return resultFor(map, data, "password");
    }

    @Override
    @PostMapping("/frozen")
    @UonePermissions
    public RestResponse frozen(String code, String pwdId) throws Exception {
        String url = danbayProp.getBaseUrl() + "deviceCtrl/lockPwd/delPwd";
        Map<String, Object> map = new HashMap<>();
        HttpRequest hq = getHttpRequest(url, map);
        map.put("deviceId", code);
        map.put("pwdID", pwdId);
        map.put("pwdType", "3");

        hq.form(map);
        HttpResponse hp = hq.execute();
        JSONObject json = JSONUtil.parseObj(hp.body());
        String state = json.getStr("status");
        sysInterfaceMsgFegin.createLog(map.toString(), json.toString(), "蛋贝门锁冻结报文");
        if (!"200".equals(state)) {
            return RestResponse.failure("授权失败,原因为:" + json.get("message"));
        }
        JSONObject data = (JSONObject) json.get("result");
        return resultFor(map, data, "");
    }

    @Override
    @PostMapping("/getTempPassword")
    @UonePermissions
    public RestResponse getTempPassword(String code) throws Exception {
        String password = RandomUtil.randomNumbers(6);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startDate = format.format(new Date());
        String endDate = format.format(DateUtils.addHours(new Date(), 2));
        String url = danbayProp.getBaseUrl() + "deviceCtrl/lockPwd/addPwdWithDate";
        Map<String, Object> map = new HashMap<>();
        HttpRequest hq = getHttpRequest(url, map);
        map.put("deviceId", code);
        map.put("password", password);
        map.put("pwdType", "0");
        map.put("beginTime", startDate);
        map.put("endTime", endDate);

        hq.form(map);
        HttpResponse hp = hq.execute();
        JSONObject json = JSONUtil.parseObj(hp.body());
        String state = json.getStr("status");
        sysInterfaceMsgFegin.createLog(map.toString(), json.toString(), "蛋贝门锁一次性密码报文");
        if (!"200".equals(state)) {
            return RestResponse.failure("授权失败,原因为:" + json.get("message"));
        }
        JSONObject data = (JSONObject) json.get("result");
        return resultFor(map, data, "password");
    }

    @Override
    @PostMapping("/getLockRecords")
    @UonePermissions
    public RestResponse getLockRecords(String code, String beginDate, String endDate) throws Exception{
        RestResponse response = new RestResponse();
        String url = danbayProp.getBaseUrl() + "deviceInfo/getLockRecordsByDeviceId";
        Map<String, Object> map = new HashMap<>();
        HttpRequest hq = getHttpRequest(url, map);
        map.put("deviceId", code);
        map.put("beginDate", beginDate);
        map.put("endDate", endDate);
//        map.put("type",type);
        hq.form(map);
        HttpResponse hp = hq.execute();
        JSONObject json = JSONUtil.parseObj(hp.body());
        String state = json.getStr("status");
        sysInterfaceMsgFegin.createLog(map.toString(), json.toString(), "蛋贝开门记录读数报文");
        if (!"200".equals(state)) {
            return RestResponse.failure("读数失败,原因为:" + json.get("message"));
        }
        JSONArray result = json.getJSONArray("result");
        return response.setData(result);
    }


    public HttpRequest getHttpRequest(String url, Map<String, Object> map) throws Exception {
        HttpRequest hq = HttpUtil.createPost(url);
        String token = getToken();
        map.put("mtoken", token);
        return hq;
    }

    private String getToken() throws Exception {
        String token = (String) CacheUtil.get("DanbayToken");
        if (StrUtil.isBlank(token)) {
            String url = danbayProp.getBaseUrl() + "loginAccess";
            HttpRequest hq = HttpUtil.createPost(url);
            Map<String, Object> map = new HashMap<>();
//            map.put("mc_username", "dbUpnchoDDZibnNf");
//            map.put("mc_password", "uzbOPVebRQSmRnclTuvFBVbttNoVvcGy");
            map.put("mc_username", danbayProp.getApiId());
            map.put("mc_password", danbayProp.getApiSecret());
            map.put("random_code", "1");
            hq.form(map);
            HttpResponse hp = hq.execute();
            JSONObject json = JSONUtil.parseObj(hp.body());
            String state = json.getStr("status");
            sysInterfaceMsgFegin.createLog(map.toString(), json.toString(), "蛋贝缓存报文");
            if (!"200".equals(state)) {
                throw new Exception("蛋贝口获取token失败");
            }
            String data = json.getStr("result");
            Map<String, Object> mapR = JSONUtil.parseObj(data);
            System.out.println("返回结果:" + data);
            token = mapR.get("mtoken").toString();
            CacheUtil.putEx("DanbayToken", token, 1200l);
        }
        return token;
    }

    /**
     * 返回类型，成功就返回读数，失败就返回错误信息
     *
     * @param data
     * @return
     */
    public RestResponse resultFor(Map<String, Object> map, Map<String, Object> data, String readName) {
        RestResponse response = new RestResponse();
        response.setData(data);
        if ("".equals(readName)) {
            return response.setSuccess(true);
        } else if ("password".equals(readName)) {
            response.put("password", map.get("password").toString());
            if (data.containsKey("pwdID")) {
                response.put("passwordId", data.get("pwdID").toString());
            }
            return response.setSuccess(true);
        } else {
            //水电表读数
            response.put("read", data.get("electricQuantity").toString());
            return response.setSuccess(true).setData(data.get("electricQuantity").toString());
        }
    }

}
