package cn.uone.ams.tpi.controller.cosmic;

import cn.uone.ams.tpi.api.cosmic.IncomeApi;
import cn.uone.bean.entity.tpi.cosmic.income.IncomeQueryDto;
import cn.uone.bean.entity.tpi.cosmic.income.IncomeSaveDto;
import cn.uone.fegin.tpi.cosmic.IIncomeFeign;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.RestResponse;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/cosmic/income")
public class IncomeController implements IIncomeFeign {

    @Autowired
    private IncomeApi incomeApi;


    @Override
    @PostMapping("/billSaveBatch")
    @UonePermissions
    public RestResponse billSaveBatch(List<IncomeSaveDto> dtoList) {
        return incomeApi.receivableBillSave(dtoList);
    }

    @Override
    @PostMapping("/billQuery")
    public RestResponse billQuery(IncomeQueryDto queryDto) {
        return incomeApi.receivableBillQuery(queryDto);
    }
}
