package cn.uone.ams.tpi.api;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;

public class SecureTool {

    private static BouncyCastleProvider bouncyCastleProvider = null;

    public static synchronized BouncyCastleProvider getInstance() {
        if (bouncyCastleProvider == null) {
            bouncyCastleProvider = new BouncyCastleProvider();
            Security.addProvider(bouncyCastleProvider);
        }
        return bouncyCastleProvider;
    }

    public byte[] decrypt(byte[] cipherByte,String sessionKey) throws Exception {
        return sm4Decrypt(cipherByte,sessionKey);
    }

    /**
     * SM4解密
     *
     * @param cipherByte
     * @return
     * @throws Exception
     */
    private byte[] sm4Decrypt(byte[] cipherByte,String sessionKey) throws Exception {
        byte[] sm4Key = new byte[16];
        System.arraycopy(sessionKey.getBytes(), 0, sm4Key, 0, sm4Key.length);
        //SM4解密
        SecretKeySpec key = new SecretKeySpec(sm4Key, "SM4");
        Cipher cipher = Cipher.getInstance("SM4/ECB/NOPadding", SecureTool.getInstance());
        cipher.init(Cipher.DECRYPT_MODE, key);
        return cipher.doFinal(cipherByte);
    }

}