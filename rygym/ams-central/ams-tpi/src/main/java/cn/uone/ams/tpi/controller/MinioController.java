package cn.uone.ams.tpi.controller;

import cn.uone.ams.tpi.api.minio.MinioClientApi;
import cn.uone.fegin.tpi.IMinioFegin;
import cn.uone.web.base.RestResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/minio")
@Slf4j
public class MinioController implements IMinioFegin {

    @Resource
    private MinioClientApi minioClientApi;


    @Override
    @ApiOperation("Minio上传文件")
    @PostMapping("/uploadFile")
    public RestResponse uploadFile(@RequestPart MultipartFile file) {
        return minioClientApi.uploadFile(file);
    }

    @Override
    @ApiOperation("Minio上传文件")
    @PostMapping("/uploadFileByUrl")
    public RestResponse uploadFileByUrl(@RequestParam("fileUrl") String fileUrl,@RequestParam("extName") String extName) {
        return minioClientApi.uploadFile(fileUrl, extName);
    }

    @Override
    @ApiOperation("Minio上传文件")
    @PostMapping("/uploadFileByBytes")
    public RestResponse uploadFileByBytes(@RequestParam("bytes") byte[] bytes,@RequestParam("extName") String extName,@RequestParam("contentType") String contentType) {
        return minioClientApi.uploadFile(bytes, extName, contentType);
    }


    @Override
    @ApiOperation("Minio获取文件输入流")
    @PostMapping("/getFileByteArray")
    public byte[] getFileByteArray(@RequestParam("minioFileName") String minioFileName) {
        return minioClientApi.getFileByteArray(minioFileName);
    }

    @Override
    @ApiOperation("Minio获取文件url")
    @PostMapping("/getFileUrl")
    public RestResponse getFileUrl(@RequestParam("minioFileName") String minioFileName) {
        return minioClientApi.getFileUrl(minioFileName);
    }

    @Override
    @ApiOperation("Minio获取文件url")
    @PostMapping("/getUrl")
    public String getUrl(@RequestParam("minioFileName") String minioFileName) {
        return minioClientApi.getUrl(minioFileName);
    }

    @Override
    @ApiOperation("Minio下载文件")
    @PostMapping("/downloadFile")
    public RestResponse downloadFile(HttpServletResponse response,@RequestParam("minioFileName") String minioFileName) {
        return minioClientApi.downloadFile(response,minioFileName);
    }

    @Override
    @ApiOperation("Minio下载文件")
    @PostMapping("/downloadFileToPath")
    public RestResponse downloadFile(@RequestParam("minioFileName") String minioFileName,@RequestParam("filePath") String filePath) {
        return minioClientApi.downloadFile(minioFileName,filePath);
    }

    @Override
    @ApiOperation("Minio删除文件")
    @PostMapping("/deleteFile")
    public RestResponse deleteFile(@RequestParam("minioFileName") String minioFileName) {
        return minioClientApi.deleteFile(minioFileName);
    }

    @Override
    @ApiOperation("Minio下载视频")
    @PostMapping("/downloadVideo")
    public void downloadVideo(HttpServletResponse response, @RequestParam("range")String range,@RequestParam("fileName")String fileName) {
        minioClientApi.downloadVideo(response,range,fileName);
    }
}
