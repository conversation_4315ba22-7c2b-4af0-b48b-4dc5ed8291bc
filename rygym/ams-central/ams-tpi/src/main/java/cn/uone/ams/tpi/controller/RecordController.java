package cn.uone.ams.tpi.controller;

import cn.uone.ams.tpi.api.RecordApi;
import cn.uone.bean.entity.business.res.vo.HousesourseVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.tpi.record.RecordPo;
import cn.uone.fegin.bus.ISysFileFegin;
import cn.uone.fegin.tpi.IRecordFegin;
import cn.uone.web.base.RestResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class RecordController implements IRecordFegin {

    @Autowired
    private ISysFileFegin sysFileFegin;

    @Override
    @ApiOperation("备案")
    @RequestMapping(value = "/record/putOnRecord", method = RequestMethod.POST)
    public RestResponse putOnRecord(@RequestBody RecordPo po) {
        try {
            RecordApi.up(po);
        } catch (Exception e) {
            return RestResponse.failure(e.getMessage());
        }

        return RestResponse.success();
    }

    @RequestMapping(value = "/record/uploadFile", method = RequestMethod.POST)
    public RestResponse uploadFile(String url,String username ,String password) throws Exception {
        return RestResponse.success().setData(RecordApi.upload(url,username,password));
    }

    @RequestMapping(value = "/record/getUsername", method = RequestMethod.GET)
    public String getUsername() {
        return RecordApi.getUsername();
    }

    @RequestMapping(value = "/record/upline", method = RequestMethod.GET)
    public boolean upline(@RequestBody HousesourseVo vo,@RequestParam("gpzt") String gpzt) {
        boolean b=false;
        try {
            List<SysFileEntity> entityList = sysFileFegin.thirdImageList(vo.getThemeId());
            b= RecordApi.upline(vo,gpzt,entityList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return b;
    }
}
