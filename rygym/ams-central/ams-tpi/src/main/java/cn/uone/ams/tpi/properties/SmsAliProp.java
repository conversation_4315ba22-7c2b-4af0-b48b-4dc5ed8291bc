package cn.uone.ams.tpi.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * @ClassName AliSmsProp
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/20 16:24
 * @Version 1.0
 */
@Data
public class SmsAliProp {

    private String accessKeyId;
    private String accessKeySecret;
    private String signName;
}
