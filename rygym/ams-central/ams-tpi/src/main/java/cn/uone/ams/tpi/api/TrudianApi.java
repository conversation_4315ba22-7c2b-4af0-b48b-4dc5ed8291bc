package cn.uone.ams.tpi.api;

import cn.hutool.core.lang.Console;
import cn.hutool.json.JSONUtil;
import cn.uone.web.base.RestResponse;
import com.trudian.tdcloud.sdk.TDCloudApp;
import com.trudian.tdcloud.sdk.TDCloudResult;
import com.trudian.tdcloud.sdk.request.*;
import com.trudian.tdcloud.sdk.util.TDJsonUtil;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.util.Date;
import java.util.Random;

/**
 * 触点出入口门禁接口
 */
@Component
public class TrudianApi {


    public static void main(String[] args) {
        //qrcode("","","");
        //addFace();
        //addFaceBase64();
        //delFace();
        //addPushAC();
        //reboot();
    }

    /**
     * 生成开门二维码
     */
    public RestResponse qrcode(String appId,String secretKey,String localIds){
        TDCloudApp app = new TDCloudApp(appId, secretKey);
        GenerateQrCodeRequest request = new GenerateQrCodeRequest();
        // 在有效时间内一次次有效
        request.setType(0);
        // 设置有效期开始时间为当前
        request.setStartTime((int) (new Date().getTime() / 1000));
        // 设置有效期结束时间为一天
        request.setEndTime((int) (new Date().getTime() / 1000) + 86400);
        //二维码ID主要用于标识本次二维码的唯一性，具体值可根据实际设置
        int qrId = new Random().nextInt(2147483646) + 1;
        request.setQrId(qrId);
        request.setLocalIds(localIds);
        Console.log("触点出入口门禁-生成开门二维码请求：{}", JSONUtil.toJsonStr(request));
        TDCloudResult result = app.invoke(request);
        Console.log("触点出入口门禁-生成开门二维码返回：{}",TDJsonUtil.transferToJson(result));
        if(result.getCode() == 0){
            return RestResponse.success().setData(result.getData());
        }
        return RestResponse.failure(result.getMsg());
    }

    /**
     * 注册人脸
     * @return
     */
    public RestResponse addFace(String appId,String secretKey,String localId,String cardSn,String url){
        //TDCloudApp app = new TDCloudApp("10000115", "b1c272a2797769ab3ba46d274198a17d");
        TDCloudApp app = new TDCloudApp(appId, secretKey);
        AddFaceRequest request=new AddFaceRequest();
        //request.setLocalId("2137368614180511");
        //request.setCardSN("B3A710D4");
        request.setLocalId(localId);
        request.setCardSN(cardSn);
        //链接需要是公网链接
        request.setFaceUrl1(url);
        request.setVerify(1);
        Console.log("触点出入口门禁-人脸注册请求：{}", JSONUtil.toJsonStr(request));
        TDCloudResult result = app.invoke(request);
        if(result == null){
            for(int i=0;i<3;i++){
                result = app.invoke(request);
                if(result != null){
                    break;
                }
            }
        }
        Console.log("触点出入口门禁-人脸注册返回：{}",TDJsonUtil.transferToJson(result));
        if(result.getCode() == 0){
            return RestResponse.success().setData(result.getData());
        }
        return RestResponse.failure(result.getMsg());
    }

    /**
     * 删除人脸
     * @return
     */
    public RestResponse delFace(String appId,String secretKey,String localId,String cardSn){
        //TDCloudApp app = new TDCloudApp("10000115", "b1c272a2797769ab3ba46d274198a17d");
        TDCloudApp app = new TDCloudApp(appId, secretKey);
        DelFaceRequest request = new DelFaceRequest();
        request.setLocalId(localId);
        request.setCardSN(cardSn);
        Console.log("触点出入口门禁-删除人脸请求：{}", JSONUtil.toJsonStr(request));
        TDCloudResult result = app.invoke(request);
        Console.log("触点出入口门禁-删除人脸返回：{}",TDJsonUtil.transferToJson(result));
        if(result.getCode() == 0){
            return RestResponse.success().setData(result.getData());
        }
        return RestResponse.failure(result.getMsg());
    }

    /**
     * 重启门禁
     * @return
     */
    public RestResponse reboot(String appId,String secretKey,String localId){
        TDCloudApp app = new TDCloudApp(appId, secretKey);
        RebootRequest request=new RebootRequest();
        request.setLocalId(localId);
        Console.log("触点出入口门禁-门禁重启请求：{}", JSONUtil.toJsonStr(request));
        TDCloudResult result = app.invoke(request);
        Console.log("触点出入口门禁-门禁重启返回：{}",TDJsonUtil.transferToJson(result));
        if(result.getCode() == 0){
            return RestResponse.success().setData(result.getData());
        }
        return RestResponse.failure(result.getMsg());
    }

    /**
     * 增加推送门禁
     * @return
     */
    public static RestResponse addPushAC(){
        TDCloudApp app = new TDCloudApp("10000115", "b1c272a2797769ab3ba46d274198a17d");
        AddPushACRequest request=new AddPushACRequest();
        //request.setLocalId("2137368614180511");
        request.setLocalId("2137991214180904");
        //request.setLocalId("2137990114180904");
        TDCloudResult result = app.invoke(request);
        System.out.println(TDJsonUtil.transferToJson(result));
        return null;
    }

    /**
     * 注册人脸
     * @return
     */
    public static RestResponse addFaceBase64(){
        TDCloudApp app = new TDCloudApp("10000115", "b1c272a2797769ab3ba46d274198a17d");
        AddFaceBase64Request request = new AddFaceBase64Request();
        request.setLocalId("2137368614180511");
        request.setCardSN("B3A710D4");

        try {
            request.setBase641(getFileBase64(new File("C:\\Users\\<USER>\\Desktop\\rl3.jpg")));
        } catch (Exception e) {
            e.printStackTrace();
        }
        request.setVerify(0);
        TDCloudResult result = app.invoke(request);
        System.out.println(TDJsonUtil.transferToJson(result));
        return null;
    }

    private static String getFileBase64(File file) throws Exception {
        FileInputStream inputFile = new FileInputStream(file);
        byte[] buffer = new byte[(int) file.length()];
        inputFile.read(buffer);
        inputFile.close();
        return Base64.encodeBase64String(buffer);
    }

}
