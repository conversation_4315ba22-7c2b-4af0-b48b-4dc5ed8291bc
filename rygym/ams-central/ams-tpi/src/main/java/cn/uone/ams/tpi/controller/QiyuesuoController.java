package cn.uone.ams.tpi.controller;


import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.uone.ams.tpi.api.qiyuesuo.QiyuesuoSaasApi;
import cn.uone.ams.tpi.api.qiyuesuo.QiyuesuoSignApi;
import cn.uone.bean.entity.tpi.qiyuesuo.SignContractVo;
import cn.uone.fegin.tpi.IQiyuesuoFegin;
import cn.uone.web.base.RestResponse;
import com.qiyuesuo.sdk.v2.bean.Contract;
import com.qiyuesuo.sdk.v2.bean.Signatory;
import com.qiyuesuo.sdk.v2.response.ContractPageResult;
import com.qiyuesuo.sdk.v2.response.DocumentAddResult;
import com.qiyuesuo.sdk.v2.response.SdkResponse;
import com.qiyuesuo.sdk.v2.utils.CryptUtils;
import com.qiyuesuo.sdk.v2.utils.MD5;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileNotFoundException;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/qiyuesuo")
public class QiyuesuoController implements IQiyuesuoFegin {
    private static final Logger log = LoggerFactory.getLogger(FadadaController.class);
    @Autowired
    private QiyuesuoSaasApi qiyuesuoSaasApi;
    @Autowired
    private QiyuesuoSignApi qiyuesuoSignApi;

    @RequestMapping("/querySaasCompanyDetail")
    @Override
    public RestResponse querySaasCompanyDetail(@RequestParam("companyName") String companyName) {
        return qiyuesuoSaasApi.querySaasCompanyDetail(companyName);
    }

    @RequestMapping("/getSaasCompanyAuthUrl")
    @Override
    public RestResponse getSaasCompanyAuthUrl(@RequestParam("name") String name, @RequestParam("contact")String contact, @RequestParam("contactType")String contactType, @RequestParam("companyName")String companyName) {
        return qiyuesuoSaasApi.getSaasCompanyAuthUrl(name,contact,contactType,companyName);
    }

    @RequestMapping("/getSaasCompanyAccessUrl")
    @Override
    public RestResponse getSaasCompanyAccessUrl(@RequestParam("companyId") String companyId, @RequestParam("contact")String contact, @RequestParam("contactType")String contactType) {
        return qiyuesuoSaasApi.getSaasCompanyAccessUrl(companyId, contact, contactType);
    }

    @RequestMapping("/initSdkClient")
    @Override
    public void initSdkClient(@RequestParam("accessKey")String accessKey, @RequestParam("accessSecret")String accessSecret) {
        qiyuesuoSignApi.initSdkClient(accessKey,accessSecret);
    }

    @RequestMapping("/getPersonalTicket")
    @Override
    public String getPersonalTicket(@RequestParam("contact")String contact, @RequestParam("contactType")String contactType) {
        return qiyuesuoSignApi.getPersonalTicket(contact,contactType);
    }

    /**
     * 发起合同
     * @param signContractVo
     * @return
     */
    @RequestMapping("/initiateContract")
    @Override
    public RestResponse initiateContract(@RequestBody SignContractVo signContractVo) {
        return qiyuesuoSignApi.initiateContract(signContractVo);
    }

    /**
     * 创建合同草稿
     * @param signContractVo
     * @return
     */
    @RequestMapping("/draftContract")
    @Override
    public RestResponse draftContract(@RequestBody SignContractVo signContractVo) {
        SdkResponse<Contract> responseObj = qiyuesuoSignApi.draftContract(signContractVo);
        if(responseObj.getCode() != 0) {
            Console.log("创建合同草稿失败，错误码:{}，错误信息:{}", responseObj.getCode(), responseObj.getMessage());
            return RestResponse.failure(StrUtil.format("创建合同草稿失败，错误码:{}，错误信息:{}", responseObj.getCode(), responseObj.getMessage()));
        }
        Contract result = responseObj.getResult();
        Long contractId = result.getId();
        Long actionId = null;
        Long signatoryId = null;
        List<Signatory> signatories = result.getSignatories();
        if("PERSONAL".equals(signContractVo.getType())) {
            for (Signatory signatory : signatories) {
                if ("PERSONAL".equals(signatory.getTenantType())) {
                    signatoryId = signatory.getId();
                } else if ("COMPANY".equals(signatory.getTenantType())) {
                    actionId = signatory.getActions().get(0).getId();
                }
            }
        }else{
            signatoryId = signatories.get(0).getActions().get(0).getId();
            actionId = signatories.get(1).getActions().get(0).getId();
        }
        Console.log("创建合同草稿成功，合同ID:{}", contractId);
        return RestResponse.success().setAny("contractId",contractId).setAny("signatoryId",signatoryId).setAny("actionId",actionId);
    }

    /**
     * 用文件添加合同文档
     * @param contractId
     * @param contractCode
     * @param fileUrl
     * @return
     */
    @RequestMapping("/addDocument")
    @Override
    public RestResponse addDocument(@RequestParam("contractId") Long contractId,@RequestParam("contractCode") String contractCode,@RequestParam("fileUrl") String fileUrl) {
        SdkResponse<DocumentAddResult> docRes = qiyuesuoSignApi.addDocument(contractId,contractCode,fileUrl);
        if(docRes.getCode() != 0) {
            Console.log("添加合同文档失败，错误码:{}，错误信息:{}", docRes.getCode(), docRes.getMessage());
            return RestResponse.failure(StrUtil.format("添加合同文档失败，错误码:{}，错误信息:{}", docRes.getCode(), docRes.getMessage()));
        }
        DocumentAddResult docResult = docRes.getResult();
        Long documentId = docResult.getDocumentId();
        Console.log("添加合同文档成功，文档ID:{}", documentId);
        return RestResponse.success().setAny("documentId",documentId);
    }

    /**
     * 发起合同
     * @param contractId
     * @param documentId
     * @param actionId
     * @param signatoryId
     * @return
     */
    @RequestMapping("/sendContract")
    @Override
    public RestResponse sendContract(@RequestParam("contractId") Long contractId,@RequestParam("documentId") Long documentId,@RequestParam(value = "actionId",required = false) Long actionId,@RequestParam(value = "signatoryId",required = false) Long signatoryId) {
        SdkResponse<Object> sendRes = qiyuesuoSignApi.sendContract(contractId,documentId,actionId,signatoryId);
        if(sendRes.getCode() != 0) {
            Console.log("发起合同失败，错误码:{}，错误信息:{}", sendRes.getCode(), sendRes.getMessage());
            return RestResponse.failure(StrUtil.format("发起合同失败，错误码:{}，错误信息:{}", sendRes.getCode(), sendRes.getMessage()));
        }
        Console.log("合同发起成功");
        return RestResponse.success().setAny("contractId",contractId);
    }

    /**
     * 下载合同
     * @param documentId
     * @return
     */
    @RequestMapping("/downloadDoc")
    @Override
    public byte[] downloadDoc(@RequestParam("documentId") Long documentId) {
        Console.log("下载合同文档{}。。。", documentId);
        byte[] result = null;
        try {
            result = qiyuesuoSignApi.downloadDoc(documentId);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        Console.log("下载合同成功");
        return result;
    }

    @RequestMapping("/getContractSignTicket")
    @Override
    public String getContractSignTicket(@RequestParam("contractId")Long contractId,@RequestParam("contact")String contact, @RequestParam("contactType")String contactType) {
        return qiyuesuoSignApi.getContractSignTicket(contractId,contact,contactType);
    }

    /**
     * 获取签署页面
     * @param contractId
     * @param contact
     * @param contactType
     * @return
     */
    @RequestMapping("/getSignContractPageUrl")
    @Override
    public RestResponse getSignContractPageUrl(@RequestParam("contractId") Long contractId,@RequestParam("contact") String contact,@RequestParam("contactType") String contactType) {
        SdkResponse<ContractPageResult> responseObj = qiyuesuoSignApi.getSignContractPageUrl(contractId,contact,contactType);
        if(responseObj.getCode() != 0) {
            Console.log("获取签署页面失败，错误码:{}，错误信息:{}", responseObj.getCode(), responseObj.getMessage());
            return RestResponse.failure(StrUtil.format("获取签署页面失败，错误码:{}，错误信息:{}", responseObj.getCode(), responseObj.getMessage()));
        }
        ContractPageResult result = responseObj.getResult();
        Console.log("获取签署页面成功");
        return RestResponse.success().setAny("pageUrl",result.getPageUrl());
    }

    @RequestMapping("/toMd5")
    @Override
    public String toMd5(@RequestParam("plainText") String plainText) {
        return qiyuesuoSaasApi.toMd5(plainText);
    }

    @RequestMapping("/aesDeryptBySaas")
    @Override
    public String aesDerypt(@RequestParam("encrypt") String encrypt) throws Exception {
        return qiyuesuoSaasApi.aesDerypt(encrypt);
    }
    @RequestMapping("/aesDeryptBySign")
    @Override
    public String aesDerypt(@RequestParam("encrypt") String encrypt,@RequestParam("secret") String secret) throws Exception {
        return qiyuesuoSaasApi.aesDerypt(encrypt, secret);
    }

    @RequestMapping("/getPersonalAuthResult")
    @Override
    public RestResponse getPersonalAuthResult(@RequestParam("authId") String authId) {
        return qiyuesuoSignApi.getPersonalAuthResult(authId);
    }

    /**
     * 撤回合同
     * @param contractId
     * @param reason
     * @return
     */
    @RequestMapping("/invalidContract")
    @Override
    public RestResponse invalidContract(@RequestParam("contractId") Long contractId,@RequestParam("reason") String reason) {
        return qiyuesuoSignApi.invalidContract(contractId,reason);
    }

}
