package cn.uone.ams.tpi.api;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.ams.tpi.properties.SmsAliProp;
import cn.uone.ams.tpi.properties.SmsDmkProp;
import cn.uone.cache.util.CacheUtil;
import cn.uone.web.base.RestResponse;
import com.itextpdf.text.Header;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName AliSMSApi
 * @Description 对接domeke SAAS平台的阿里云短信接口
 * <AUTHOR>
 * @Date 2023/11/20 15:16
 * @Version 1.0
 */
@Component
public class SmsDmkApi {

    @Autowired
    SmsDmkProp smsDmkProp;

    /*public static void setSmsDmkProp(SmsDmkProp smsDmkProp){
        SmsDmkApi.smsDmkProp = smsDmkProp;
    }*/

    /**
     * 获取验证码code
     * @param codeKey
     * @return
     * @throws Exception
     */
    private String getCode(String codeKey) throws Exception {
        String code = (String) CacheUtil.get("SMSDMK_CODE_"+codeKey);
        //String code = null;
        if(code == null){
            String result = HttpRequest.get(smsDmkProp.getApi()+"/uaa/getCodeForTpi")
                    .form("codeKey",codeKey)
                    .execute().body();
            JSONObject json = JSONUtil.parseObj(result);
            if(200 == json.getInt("code")){
                code = json.getStr("data");
                CacheUtil.putEx("SMSDMK_CODE_"+codeKey, code,300l);
            }else{
                throw new Exception(json.getStr("message"));
            }
        }
        return code;
    }

    /**
     * 获取token
     * @param codeKey
     * @return
     * @throws Exception
     */
    private String getToken(String codeKey) throws Exception {
        String token = (String) CacheUtil.get("SMSDMK_TOKEN_"+codeKey);
        //String token = null;
        if(token == null){
            String code = getCode(codeKey);
            String url = smsDmkProp.getApi()+"/uaa/login";
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("username", smsDmkProp.getUsername());
            paramMap.put("password", smsDmkProp.getPassword());
            paramMap.put("code", code);
            paramMap.put("codeKey", codeKey);
            paramMap.put("type", "1");
            String result = HttpRequest.post(url)
                    .form(paramMap)//表单内容
                    .timeout(20000)//超时，毫秒
                    .execute().body();
            Console.log(StrUtil.format("domeke短信获取token：{}",result));
            JSONObject json = JSONUtil.parseObj(result);
            if(200 == json.getInt("code")){
                token = json.getJSONObject("data").getStr("uone_token");
                CacheUtil.putEx("SMSDMK_TOKEN_"+codeKey, token,300l);
            }else{
                throw new Exception(json.getStr("message"));
            }
        }
        return token;
    }

    /**
     * 发送短信
     * @param smsCode
     * @param mobile
     * @param param
     * @return
     * @throws Exception
     */
    public RestResponse sendSms(String smsCode, String mobile, String param) throws Exception {
        String token = getToken(mobile);
        String url = smsDmkProp.getApi()+"/sys/sms/sendBySaasCompanyId";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("saasCompanyId", smsDmkProp.getSaasCompanyId());
        paramMap.put("smsCode", smsCode);
        paramMap.put("mobile", mobile);
        paramMap.put("paramsJson", param);
        Console.log(StrUtil.format("domeke短信发送请求：{}",paramMap.toString()));
        String result = HttpRequest.post(url)
                .header("uone_token", token)
                .form(paramMap)//表单内容
                .timeout(20000)//超时，毫秒
                .execute().body();
        Console.log(StrUtil.format("domeke短信发送返回：{}",result));
        JSONObject json = JSONUtil.parseObj(result);
        if(200 == json.getInt("code")){
            return RestResponse.success("成功");
        }else{
            return RestResponse.failure("失败");
        }
    }

    /*public static void main(String[] args) throws Exception {
        SmsDmkProp smsDmkProp = new SmsDmkProp();
        smsDmkProp.setApi1("https://demo.domeke.com/crm");
        smsDmkProp.setApi2("https://demo.domeke.com/business");
        smsDmkProp.setUsername("admin");
        smsDmkProp.setPassword("Wyry20088@@");
        smsDmkProp.setSaasCompanyId("7ce0f85d91fa914e29f5607efd4d1d55");
        setSmsDmkProp(smsDmkProp);
        RestResponse res = sendSms("97220","***********","{\"code\":\"1234\"}");
        Console.log(JSONUtil.toJsonStr(res));
    }*/
}
