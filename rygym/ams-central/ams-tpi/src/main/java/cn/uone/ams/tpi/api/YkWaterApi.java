package cn.uone.ams.tpi.api;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.web.base.RestResponse;
import com.google.common.collect.Maps;
import okhttp3.*;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Component
public class YkWaterApi {
    private static final String APP_ID = "iw69243448551580";
    private static final String APP_SECRET = "xstgnksjv5fhkvnfpb2lkn956nv5z9cn";
    private static final String API_VERSION = "v2.0.0";
    private static final String API_URL = "https://ykwater.ceeqee.com/openapi";

    public static void main(String[] args) {
        //getMeters("0","100");
        JSONObject config = new JSONObject();
        config.put("appId", APP_ID);
        config.put("appSecret", APP_SECRET);
        config.put("apiVersion", API_VERSION);
        String macs="181202313400,181202313469,181202313408,181202313476,181202313410,181202313514,181202313275,181202313426,181202313525,181202313540,181202313388,181202313549,181202313317,181202313239,181202313459,181202313542,181202313495,181202313228,181202313274,181202313464,181202313532,181202313438,181202313255,181202313204,181202313444,181202313258,181202313432,181202313381,181202313387,181202313552,181202313423,181202313302,181202313343,181202313429,181202313522,181202313271,181202313477,181202313499,181202313330,181202313337,181202313364,181202313505,181202313227,181202313520,181202313401,181202313528,181202313252,181202313529,181202313313,181202313461,181202313463,181202313270&,181202313217,181202313322,181202313570,181202313493,181202313556,181202313541,181202313507,181202313500,181202313416,181202313389,181202313374,181202313492,181202313340,181202313405,181202313295,181202313366,181202313205,181202313404,181202313310,181202313375,181202313326,181202313251,181202313567,181202313352,181202313242,181202313362,181202313496,181202313435,181202313564,181202313305,181202313312,181202313481,181202313342,181202313257,181202313323,181202313296,181202313363,181202313281,181202313399,181202313393,181202313367,181202313537,181202313319,181202313382,181202313396,181202313240,181202313231,181202313261";
        //String macs="181202313400,181202313469,181202313408,181202313476,181202313410,181202313514,181202313275,181202313426,181202313525,181202313540,181202313388,181202313549,181202313317,181202313239,181202313459,181202313542,181202313495,181202313228,181202313274,181202313464,181202313532,181202313438,181202313255,181202313204,181202313444,181202313258,181202313432,181202313381,181202313387,181202313552,181202313423,181202313302,181202313343,181202313429,181202313522,181202313271,181202313477,181202313499,181202313330,181202313337,181202313364,181202313505,181202313227,181202313520,181202313401,181202313528,181202313252,181202313529,181202313313,181202313461,181202313463,181202313270&,181202313217,181202313322,181202313570,181202313493,181202313556,181202313541,181202313507,181202313500,181202313416,181202313389,181202313374,181202313492,181202313340,181202313405,181202313295,181202313366,181202313205,181202313404,181202313310,181202313375,181202313326,181202313251,181202313567,181202313352,181202313242,181202313362,181202313496,181202313435,181202313564,181202313305,181202313312,181202313481,181202313342,181202313257";
        //String macs="181202313400,181202313469,181202313408,181202313476,181202313410,181202313514,181202313275,181202313426,181202313525,181202313540,181202313388,181202313549,181202313317,181202313239,181202313459,181202313542,181202313495,181202313228,181202313274,181202313464,181202313532,181202313438,181202313255,181202313204,181202313444";
        meterReportDetails(config,macs,"2025-01-01","2025-03-12");
        //meterState(config,"181202313557","opened");
    }

    public static Response getMeters(JSONObject config,String start,String limit){
        Map<String, Object> queryParams = Maps.newHashMap();
        queryParams.put("start", start);
        queryParams.put("limit", limit);
        getRequest(config,"/meters",queryParams);
        return null;
    }

    /**
     * 上报数据
     * @param startDay
     * @param endDay
     * @return
     */
    public static RestResponse meterReportDetails(JSONObject config,String macs,String startDay, String endDay){
        Map<String, Object> queryParams = Maps.newHashMap();
        queryParams.put("startDay", startDay);
        queryParams.put("endDay", endDay);
        queryParams.put("macs", macs);
        queryParams.put("start", 0);
        queryParams.put("limit", 100);
        String result = getRequest(config,"/meter-report-details",queryParams,true);
        JSONObject json = JSONUtil.parseObj(result);
        JSONArray data = json.getJSONArray("data");
        int count = json.getInt("count");
        if(count > 1){
            for(int i = 1; i < count; i++){
                queryParams.put("macs", macs);
                queryParams.put("start", i*100);
                String iResult = getRequest(config,"/meter-report-details",queryParams,true);
                JSONObject iJson = JSONUtil.parseObj(iResult);
                JSONArray iData = iJson.getJSONArray("data");
                data.addAll(iData);
            }
        }
        Console.log(data);
        return RestResponse.success().setData(data);
    }

    /**
     * 阀控
     * @param config
     * @param mac    表设备号
     * @param state  目标阀状态  opened  closed
     *
     * @return
     */
    public RestResponse meterState(JSONObject config,String mac,String state){
        String path = StrUtil.format("/meters/{}/valve-states/to/{}",mac,state);
        String result = putRequest(config,path,null);
        JSONObject json = JSONUtil.parseObj(result);
        Integer taskId = json.getInt("taskId");
        if(taskId != null && taskId == 0){
            return RestResponse.success().setData(json);
        }
        return RestResponse.failure(json.getStr("message"));
    }

    private static String getRequest(JSONObject config,String path,Map<String, Object> queryParams){
        return getRequest(config,path,queryParams,false);
    }

    private static String getRequest(JSONObject config,String path,Map<String, Object> queryParams,boolean isPage){
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sign = null;
        String mackeys = null;
        try {
            String[] macs = queryParams.get("macs").toString().split(",");
            queryParams.put("macs", macs[0]);
            sign = sign(config,path,queryParams,timestamp);
            StringBuffer sbf = new StringBuffer();
            for(String mac : macs){
                sbf.append("&macs=").append(mac);
            }
            mackeys = sbf.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        queryParams.remove("macs");
        String queryStr = HttpUtil.toParams(queryParams)+mackeys;
        Console.log("请求参数：{}",queryStr);
        HttpRequest request = HttpRequest.get(API_URL+path+"?"+queryStr)
                .header("X-App-Id", config.getStr("appId"))
                .header("X-Api-Version", config.getStr("apiVersion"))
                .header("X-Sign-Type", "sha1")
                .header("X-Timestamp", timestamp)
                .header("X-Sign", sign)
                //.form(queryParams)//表单内容
                .timeout(20000);//超时，毫秒
        HttpResponse response = request.execute();
        String result = response.body();
        if(isPage){
            String page = response.header("X-Pagination-Info");
            Console.log("无忧抄表分页：{}",page);
            JSONObject resultObject = new JSONObject();
            String[] pageArr = page.split(",");
            for (String str : pageArr) {
                String[] strArr = str.trim().split("=");
                String key = strArr[0].replace("\"", "");
                String value = strArr[1].replace("\"", "");
                resultObject.put(key, Integer.valueOf(value));
            }
            resultObject.put("data", result);
            result = JSONUtil.toJsonStr(resultObject);
        }
        Console.log("无忧抄表返回：{}",result);
        return result;
    }
    private static String putRequest(JSONObject config,String path,Map<String, Object> queryParams){
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sign = null;
        try {
            sign = sign(config,path,queryParams,timestamp);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        String result = HttpRequest.put(API_URL+path)
                .header("X-App-Id", config.getStr("appId"))
                .header("X-Api-Version", config.getStr("apiVersion"))
                .header("X-Sign-Type", "sha1")
                .header("X-Timestamp", timestamp)
                .header("X-Sign", sign)
                .execute().body();
        Console.log("无忧抄表返回：{}",result);
        return result;
    }

    /**
     * 签名
     * @param path
     * @param queryParams
     * @param timestamp
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String sign(JSONObject config,String path, Map<String, Object> queryParams, String timestamp) throws NoSuchAlgorithmException {

        // 将参数按照字典顺序排序
        Map<String, String> params = new TreeMap<>();
        // 公共参数
        //params.put("appId", APP_ID);
        //params.put("appSecret", APP_SECRET);
        //params.put("apiVersion", API_VERSION);
        params.put("appId", config.getStr("appId"));
        params.put("appSecret", config.getStr("appSecret"));
        params.put("apiVersion", config.getStr("apiVersion"));
        params.put("signType", "sha1");
        params.put("timestamp", timestamp);
        // 请求路径
        params.put("path", path);
        // 查询参数
        if (queryParams != null) {
            for (Map.Entry<String, Object> entry : queryParams.entrySet()) {
                if (entry.getValue() != null) {
                    params.put(entry.getKey(), entry.getValue().toString());
                }
            }
        }
        // 请求参数
        /*if (body != null && !body.isEmpty()) {
            params.put("body", body);
        }*/

        // 构建待签名字符串
        StringBuilder signedTextBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            signedTextBuilder.append(entry.getValue()).append("&");
        }
        String signedText = signedTextBuilder.toString();
        signedText = signedText.substring(0, signedText.length() - 1); // 移除最后一个 '&'
        Console.log("无忧抄表请求：{}", signedText);

        // SHA-1 加密
        MessageDigest md = MessageDigest.getInstance("SHA-1");
        byte[] sha1Bytes = md.digest(signedText.getBytes());
        StringBuilder signatureBuilder = new StringBuilder(40);
        for (byte b : sha1Bytes) {
            signatureBuilder.append(String.format("%02x", b));
        }

        return signatureBuilder.toString();
    }
}
