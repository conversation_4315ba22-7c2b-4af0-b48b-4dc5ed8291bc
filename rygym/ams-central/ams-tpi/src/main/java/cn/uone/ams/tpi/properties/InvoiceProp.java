package cn.uone.ams.tpi.properties;

import lombok.Data;

@Data
public class InvoiceProp {
    //身份认证码
    String identity;
    String createUrl;
    String queryUrl;
    String invalidUrl;
    String saletaxnum;
    String saleaccount;
    String salephone;
    String saleaddress;
    //发票类型，1:正票;2：红票
    String kptype;
    //开票员
    String clerk;
    //税率
    String taxrate;
    //税收分类编码
    String spbm;
    //发票行性质，0:正常行;1:折扣行;2:被折扣行
    String fphxz;
}
