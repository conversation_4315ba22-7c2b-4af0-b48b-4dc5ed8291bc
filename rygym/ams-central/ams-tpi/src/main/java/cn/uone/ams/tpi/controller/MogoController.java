package cn.uone.ams.tpi.controller;

import cn.uone.ams.tpi.api.MogoHouseApi;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.bean.entity.business.mogo.MogoGiEntity;
import cn.uone.bean.entity.business.mogo.MogoManagerEntity;
import cn.uone.bean.entity.business.res.ResSourcePublishEntity;
import cn.uone.bean.entity.business.res.vo.HousesourseVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.fegin.bus.IMogoGiFegin;
import cn.uone.fegin.bus.IMogoManagerFegin;
import cn.uone.fegin.bus.IResSourcePublishFegin;
import cn.uone.fegin.bus.ISysFileFegin;
import cn.uone.fegin.sys.ISysInterfaceMsgFegin;
import cn.uone.fegin.tpi.IMogoFegin;
import com.mogoroom.openapi.exception.MogoApiException;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/mogo")
public class MogoController implements IMogoFegin {
    @Autowired
    private ISysFileFegin sysFileFegin;
    @Autowired
    private IMogoGiFegin mogoGiFegin;
    @Autowired
    private IResSourcePublishFegin resSourcePublishFegin;
    @Autowired
    private IMogoManagerFegin mogoManagerFegin;
    @Autowired
    private ISysInterfaceMsgFegin sysInterFaceMsgFegin;

    @RequestMapping(value = "/upline")
    public JSONObject upLine(@RequestBody HousesourseVo vo,@RequestParam(value = "type") String type) {
        JSONObject res=new JSONObject();
        res.put("success",true);
        res.put("msg","");
        JSONObject  ret=new JSONObject();
        int cityid=100194;//厦门市
        int districtId=201285;//湖里区
        MogoGiEntity entity=mogoGiFegin.getGi(vo.getCity(),vo.getDistrict());
        ResSourcePublishEntity publishEntity=resSourcePublishFegin.sourcePublish(vo.getId(),type);
        if(entity!=null){
            cityid=Integer.valueOf(entity.getCityId());
            districtId=Integer.valueOf(entity.getDistrictId());
        }
        try {
            ret= MogoHouseApi.addCommunity(vo,cityid,districtId);
            if(ret.get("errorCode").equals("0000")) {
                publishEntity.setThirdPartyCommunity(ret.get("communityId") + "");
                resSourcePublishFegin.update(publishEntity);
                //管家备案
                ret = manager(vo, ret);
                if(ret.getString("errorCode").equals("0000")){
                if (StringUtils.isBlank(vo.getThirdPartyRoom())) {
                    //房源添加
                    ret = MogoHouseApi.addCentRoom(vo, 1);
                    if (ret.getString("errorCode").equals("0000")) {
                        publishEntity.setThirdPartyRoom(ret.getString("roomNo"));
                        publishEntity.setPublishTarget(BaseConstants.BOOLEAN_OF_TRUE);
                        resSourcePublishFegin.update(publishEntity);
                        List<SysFileEntity> entityList = sysFileFegin.thirdImageList(vo.getThemeId());
                        ret = MogoHouseApi.uploadImageUrl(vo, entityList);
                        if (ret.getString("errorCode").equals("0000")) {
                            publishEntity.setThirdPartyImages(ret.getString("imags"));
                            resSourcePublishFegin.update(publishEntity);
                        }
                        res.put("msg", ret.getString("errorMessage"));
                    } else {
                        res.put("success", false);
                        res.put("msg", ret.getString("errorMessage"));
                    }
                } else {
                    ret = MogoHouseApi.editCentRoom(vo);
                    if (!ret.getString("errorCode").equals("0000")) {
                        res.put("success", false);
                        res.put("msg", ret.getString("errorMessage"));
                    }
                }
            }else{
                    res.put("success", false);
                    res.put("msg", ret.getString("errorMessage"));
                }
            }else{
                res.put("success",false);
                res.put("msg",ret.getString("errorMessage"));
            }
        } catch (MogoApiException e) {
            res.put("success",false);
            res.put("msg",ret.getString("errorMessage"));
            e.printStackTrace();
        }
        return res;
    }

    //管家备案
    public JSONObject manager(HousesourseVo hs,JSONObject res){
      MogoManagerEntity managerEntity= mogoManagerFegin.getByTel(hs.getManageTel());
        try {
            if(managerEntity!=null){
                if(!hs.getManageName().equals(managerEntity.getName())){
                    res=MogoHouseApi.updateRoomManager(hs);
                    if("0000".equals(res.get("errorCode"))){
                        mogoManagerFegin.addOrUpdate(hs.getManageTel(),hs.getManageName(),managerEntity.getId());
                    }
                }
            }else {
                res=MogoHouseApi.addRoomManager(hs);
                if ("0000".equals(res .get("errorCode"))){
                    mogoManagerFegin.addOrUpdate(hs.getManageTel(),hs.getManageName(),null);
                }
            }
        } catch (MogoApiException e) {
            e.printStackTrace();
        }
        return  res;
    }

    @RequestMapping(value = "/downline")
    public JSONObject downline(String sourceId, String type) {
        JSONObject res=new JSONObject();
        res.put("success",true);
        HousesourseVo vo=resSourcePublishFegin.thirdPartyInfo(sourceId,type);
        vo.setState(SourceStateEnum.RENT.getValue());
        try {
            JSONObject jsonObject=  MogoHouseApi.editCentRoom(vo);
            if(!"0000".equals(jsonObject.get("errorCode"))){
                res.put("success",false);
                res.put("msg",jsonObject.getString("errorMessage"));
            }
        } catch (MogoApiException e) {
            e.printStackTrace();
        }
        return res;
    }
}
