package cn.uone.ams.tpi.controller;

import cn.uone.ams.tpi.api.qyWechat.QywxMessageApi;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.fegin.tpi.IQywxMessageFegin;
import cn.uone.web.base.RestResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/qywxMessage")
public class QywxMessageController implements IQywxMessageFegin {

    @Autowired
    private QywxMessageApi qywxMessageApi;

    @ApiOperation("企业微信发送文本消息")
    @RequestMapping("/sendTextMessage")
    public RestResponse sendTextMessage(@RequestParam("touser") String touser,@RequestParam("corpid") String corpid,@RequestParam("agentid") String agentid,@RequestParam("agentsecret") String agentsecret,@RequestParam("content") String content) {
        return qywxMessageApi.sendTextMessage(touser,corpid,agentid,agentsecret,content);
    }

    @ApiOperation("企业微信发送文本卡片消息")
    @RequestMapping("/sendTextCardMessage")
    public RestResponse sendTextCardMessage(@RequestParam("touser")String touser,@RequestParam("corpid")String corpid,@RequestParam("agentid")String agentid,@RequestParam("agentsecret")String agentsecret,@RequestParam("title")String title,@RequestParam("description")String description,@RequestParam("url")String url) {
        return qywxMessageApi.sendTextCardMessage(touser,corpid,agentid,agentsecret,title,description,url);
    }

}
