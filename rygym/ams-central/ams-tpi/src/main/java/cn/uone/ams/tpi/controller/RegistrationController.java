package cn.uone.ams.tpi.controller;

import cn.uone.ams.tpi.api.ContractRegistrationApi;
import cn.uone.bean.entity.tpi.contractRegistration.ContractRegistrationVo;
import cn.uone.bean.entity.tpi.record.RecordConfigVo;
import cn.uone.fegin.bus.ISysFileFegin;
import cn.uone.fegin.tpi.IContractRegFegin;
import cn.uone.web.base.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RegistrationController implements IContractRegFegin {

    @Autowired
    ContractRegistrationApi contractRegistrationApi;

    @RequestMapping(value = "/registration/sendFile", method = RequestMethod.POST)
    public RestResponse sendFile(RecordConfigVo configVo)  {
        return contractRegistrationApi.sendFile(configVo);
    }


    @RequestMapping(value = "/registration/pushContractRegistration", method = RequestMethod.POST)
    public RestResponse pushContractReg(ContractRegistrationVo vo) {
        return RestResponse.success().setData(ContractRegistrationApi.pushContractRegistration(vo));
    }



}
