package cn.uone.ams.tpi.controller;


import cn.uone.ams.tpi.api.SmsJxMasApi;
import cn.uone.fegin.tpi.ISmsJxMasFegin;
import cn.uone.web.base.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
@RestController
@RequestMapping("/smsJxMas")
public class SmsJxMasController implements ISmsJxMasFegin {

    @Autowired
    private SmsJxMasApi smsJxMasApi;

    @RequestMapping("/sendMas")
    public RestResponse sendMas(@RequestParam("mobile") String mobile, @RequestParam("content") String content, @RequestParam("smID") Long smID) {
        return smsJxMasApi.sendMas(mobile,content,smID);
    }


}
