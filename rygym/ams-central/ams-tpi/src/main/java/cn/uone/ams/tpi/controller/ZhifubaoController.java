package cn.uone.ams.tpi.controller;

import cn.hutool.core.util.StrUtil;
import cn.uone.ams.tpi.api.ZhifubaoHouseApi;
import cn.uone.application.constant.BaseConstants;
import cn.uone.bean.entity.business.res.ResSourcePublishEntity;
import cn.uone.bean.entity.business.res.vo.HousesourseVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.fegin.bus.IResSourcePublishFegin;
import cn.uone.fegin.bus.ISysFileFegin;
import cn.uone.fegin.sys.ISysInterfaceMsgFegin;
import cn.uone.fegin.tpi.IZhifubaoHouseFegin;
import com.alipay.api.AlipayApiException;
import com.google.common.base.Joiner;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/zhifubao")
public class ZhifubaoController implements IZhifubaoHouseFegin {

    @Autowired
    private ISysFileFegin sysFileFegin;
    @Autowired
    private IResSourcePublishFegin resSourcePublishFegin;
    @Autowired
    private ISysInterfaceMsgFegin sysInterFaceMsgFegin;
    @Autowired
    private ZhifubaoHouseApi zhifubaoHouseApi;

    private static final  String COMM_REQ_ID = "comm_req_id";

    @PostMapping(value = "/upline")
    public JSONObject upLine(@RequestBody HousesourseVo vo,@RequestParam(value = "type") String type){
        JSONObject res=new JSONObject();
        ResSourcePublishEntity publishEntity=resSourcePublishFegin.sourcePublish(vo.getId(),type);
        try {
            //小区同步
            res = zhifubaoHouseApi.syncCommunity(vo);
            if(!res.getBoolean("success")) {
                return res;
            }
            sysInterFaceMsgFegin.createLog(res.getString(COMM_REQ_ID),res.getString(COMM_REQ_ID) , "支付宝小区同步,房源id"+vo.getId());
            //图片
            List<String> imageList = new ArrayList<>();
            List<SysFileEntity> pics=sysFileFegin.thirdImageList(vo.getThemeId());
            for (SysFileEntity image : pics) {
                imageList.add(image.getPath());
            }
            vo.setThirdPartyImages(Joiner.on(",").join(imageList));
            //发布房源(默认上架)
             res =zhifubaoHouseApi.syncConcentration(vo);
            if(res.getBoolean("success")) {
                publishEntity.setThirdPartyCommunity(res.getString(COMM_REQ_ID));
                publishEntity.setThirdPartyRoom(StrUtil.isBlank(vo.getThirdPartyRoom())?vo.getId():vo.getThirdPartyRoom());
                publishEntity.setThirdPartyImages(vo.getThirdPartyImages());
                publishEntity.setPublishTarget(BaseConstants.BOOLEAN_OF_TRUE);
                resSourcePublishFegin.update(publishEntity);
            }
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return res;
    }

    @PostMapping(value = "/downline")
    public JSONObject downline(String sourceId, String type) {
        JSONObject res=null;
        HousesourseVo vo=resSourcePublishFegin.thirdPartyInfo(sourceId,type);
        try {
             res= zhifubaoHouseApi.syncState(vo.getThirdPartyRoom(),0);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return res;
    }
}
