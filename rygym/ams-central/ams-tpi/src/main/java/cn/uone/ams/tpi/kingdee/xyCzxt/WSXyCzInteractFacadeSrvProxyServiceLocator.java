/**
 * WSXyCzInteractFacadeSrvProxyServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.uone.ams.tpi.kingdee.xyCzxt;

/*
public class WSXyCzInteractFacadeSrvProxyServiceLocator extends org.apache.axis.client.Service implements WSXyCzInteractFacadeSrvProxyService {

    public WSXyCzInteractFacadeSrvProxyServiceLocator() {
    }


    public WSXyCzInteractFacadeSrvProxyServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public WSXyCzInteractFacadeSrvProxyServiceLocator(String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for WSXyCzInteractFacade
    private String WSXyCzInteractFacade_address = "http://127.0.0.1:6888/ormrpc/services/WSXyCzInteractFacade";

    public String getWSXyCzInteractFacadeAddress() {
        return WSXyCzInteractFacade_address;
    }

    // The WSDD service name defaults to the port name.
    private String WSXyCzInteractFacadeWSDDServiceName = "WSXyCzInteractFacade";

    public String getWSXyCzInteractFacadeWSDDServiceName() {
        return WSXyCzInteractFacadeWSDDServiceName;
    }

    public void setWSXyCzInteractFacadeWSDDServiceName(String name) {
        WSXyCzInteractFacadeWSDDServiceName = name;
    }

    public WSXyCzInteractFacadeSrvProxy getWSXyCzInteractFacade() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(WSXyCzInteractFacade_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getWSXyCzInteractFacade(endpoint);
    }

    public WSXyCzInteractFacadeSrvProxy getWSXyCzInteractFacade(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            WSXyCzInteractFacadeSoapBindingStub _stub = new WSXyCzInteractFacadeSoapBindingStub(portAddress, this);
            _stub.setPortName(getWSXyCzInteractFacadeWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setWSXyCzInteractFacadeEndpointAddress(String address) {
        WSXyCzInteractFacade_address = address;
    }

    */
/**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     *//*

    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (WSXyCzInteractFacadeSrvProxy.class.isAssignableFrom(serviceEndpointInterface)) {
                WSXyCzInteractFacadeSoapBindingStub _stub = new WSXyCzInteractFacadeSoapBindingStub(new java.net.URL(WSXyCzInteractFacade_address), this);
                _stub.setPortName(getWSXyCzInteractFacadeWSDDServiceName());
                return _stub;
            }
        }
        catch (Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    */
/**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     *//*

    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        String inputPortName = portName.getLocalPart();
        if ("WSXyCzInteractFacade".equals(inputPortName)) {
            return getWSXyCzInteractFacade();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://127.0.0.1:6888/ormrpc/services/WSXyCzInteractFacade", "WSXyCzInteractFacadeSrvProxyService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://127.0.0.1:6888/ormrpc/services/WSXyCzInteractFacade", "WSXyCzInteractFacade"));
        }
        return ports.iterator();
    }

    */
/**
    * Set the endpoint address for the specified port name.
    *//*

    public void setEndpointAddress(String portName, String address) throws javax.xml.rpc.ServiceException {
        
if ("WSXyCzInteractFacade".equals(portName)) {
            setWSXyCzInteractFacadeEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    */
/**
    * Set the endpoint address for the specified port name.
    *//*

    public void setEndpointAddress(javax.xml.namespace.QName portName, String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
*/
