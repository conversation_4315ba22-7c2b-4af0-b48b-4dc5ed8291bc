package cn.uone.ams.tpi;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages = {"cn.uone.*"})
@EnableFeignClients(basePackages = {"cn.uone.*"})
@EnableDiscoveryClient
public class TpiApplication {
    public static void main(String[] args) {
        System.setProperty("rocketmq.client.log.loadconfig","false");
        SpringApplication.run(TpiApplication.class, args);
    }
}
