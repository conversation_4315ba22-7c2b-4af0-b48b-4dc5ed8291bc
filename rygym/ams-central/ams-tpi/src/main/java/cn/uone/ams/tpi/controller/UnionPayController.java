package cn.uone.ams.tpi.controller;

import cn.uone.ams.tpi.api.UnionPayApi;
import cn.uone.ams.tpi.properties.UnionPayProp;
import cn.uone.bean.entity.tpi.unionPay.UnionPayVo;
import cn.uone.fegin.tpi.UnionPayFegin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 从漳州城投工程迁移过来
 * caizhanghe edit 2024-05-30
 */
@RestController
@RequestMapping("/unionPay")
public class UnionPayController implements UnionPayFegin {

    @Autowired
    private UnionPayApi unionPayApi;

    @Autowired
    private UnionPayProp prop;

    @RequestMapping(value = "/orderPay",method = RequestMethod.POST)
    public Map<String,String> orderPay(String totalAmount, String merOrderId, String openid) throws Exception {
        return unionPayApi.pay(totalAmount,merOrderId,openid);
    }

    @RequestMapping(value = "/getUnionPayProp",method = RequestMethod.GET)
    public Map<String,String> getUnionPayProp(){
        Map<String,String> map = new HashMap<>();
        map.put("encryptionKey",prop.getEncryptionKey());
        map.put("sourceCode",prop.getSourceCode());
        return map;
    }

    @RequestMapping(value = "/pay",method = RequestMethod.POST)
    public Map<String, String> pay(@RequestBody UnionPayVo unionPayVo) throws Exception {
        return unionPayApi.pay(unionPayVo);
    }

    @RequestMapping(value = "/refund",method = RequestMethod.POST)
    public Map<String, String> refund(@RequestBody UnionPayVo unionPayVo) throws Exception {
        return unionPayApi.refund(unionPayVo);
    }

    @RequestMapping(value = "/h5Pay",method = RequestMethod.POST)
    public String h5Pay(@RequestBody UnionPayVo unionPayVo) {
        return unionPayApi.h5Pay(unionPayVo);
    }

}
