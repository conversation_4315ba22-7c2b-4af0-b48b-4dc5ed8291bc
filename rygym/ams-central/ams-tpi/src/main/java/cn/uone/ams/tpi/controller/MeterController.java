package cn.uone.ams.tpi.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.ams.tpi.properties.ZdyProp;
import cn.uone.application.enumerate.ZDYSupplierTypeEnum;
import cn.uone.fegin.bus.IMeterTaskFegin;
import cn.uone.fegin.sys.ISysInterfaceMsgFegin;
import cn.uone.fegin.tpi.IMeterFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * supplierType是供应商，例如云丁的设备就是"yunding"（必填）
 * devicecode就是设备编号devicecode（必填）
 * mobile就是租客的手机号，用于门禁的操作
 * passwordid为密码id，仅冻结门锁时需要传入
 * 返回格式根据readName设置，"readData"=返回String读数，""=返回String"成功"
 * "password"=返回整个data对象，里面password必存在，passwordid仅在门禁授权存在
 */
@Api(value = "设备接口", tags = {"设备接口"})
@RestController
@RequestMapping("/meter")
public class MeterController extends BaseController implements IMeterFegin {

    //private static final String BASEURL = "http://hardware.uone.cn:81/api/";
//    private static final String BASEURL = "http://api.amctex.com.cn/api/";

    @Autowired
    private ZdyProp zdyProp;


    @Autowired
    private ISysInterfaceMsgFegin sysInterFaceMsgFegin;

    @Autowired
    private IMeterTaskFegin iMeterTaskFegin;


    //    测试方法start
    public RestResponse test(String deviceCode, String supplierType, String passwordid) {
        String url = zdyProp.getUrl();
        url += "lock/auth";
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        map.put("passwordid", passwordid);

        HttpRequest hq = new HttpRequest(url).method(Method.POST);
        hq.form(map);
        HttpResponse hp = hq.execute();
        String body = hp.body();
        Map<String, Object> mapR = JSONUtil.parseObj(body);
        RestResponse response = new RestResponse();
        response.putAll(map);
        if (mapR.get("state").equals(true)) {
            response.put("message", mapR.get("message").toString());
            return response.setSuccess(true);
        } else {
            return RestResponse.failure(mapR.get("message").toString());
        }
    }
//
//    public static void main(String[] args) {
//        RestResponse restResponse = test("DD:18:49:B5:79:F1", "zelkova1", "1296378400");
//        System.out.print(JSONUtil.toJsonPrettyStr(restResponse));
//    }

    /**
     * 水电表读数获取
     *
     * @param isWater   true是水表，false是电表
     * @param deviceCode  设备编号
     * @param supplierType 供应商
     * @return
     */
    @Override
    @ApiOperation(value = "水电表读数")
    @PostMapping("/getRead")
    @UonePermissions
    public RestResponse getRead(Boolean isWater, String deviceCode, String supplierType) {
        String url = zdyProp.getUrl();
        String note = ZDYSupplierTypeEnum.getNameByValue(supplierType);
        if (isWater) {
            url += "watermeter/read";
            note += "水表读数获取报文";
        } else {
            url += "elemeter/read";
            note += "电表读数获取报文";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        Map<String, Object> mapR = requestPost(map, url, note);
        return resultFor(map, mapR, "readData");
    }

    /**
     * 水表阀门打开（电表合闸）
     *
     * @param isWater
     * @param deviceCode
     * @param supplierType
     * @return
     */
    @Override
    @PostMapping("/meterOpen")
    public RestResponse meterOpen(Boolean isWater, String deviceCode, String supplierType) {
        String url = zdyProp.getUrl();
        String note = ZDYSupplierTypeEnum.getNameByValue(supplierType);
        if (isWater) {
            url += "watermeter/open";
            note += "水表阀门打开报文";
        } else {
            url += "elemeter/open";
            note += "电表合闸报文";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        Map<String, Object> mapR = requestPost(map, url, note);
        return resultFor(map, mapR, "");
    }

    /**
     * 水表阀门关闭（电表跳闸）
     *
     * @param isWater
     * @param deviceCode
     * @param supplierType
     * @return
     */
    @Override
    @PostMapping("/meterClose")
    public RestResponse meterClose(Boolean isWater, String deviceCode, String supplierType) {
        String url = zdyProp.getUrl();
        String note = ZDYSupplierTypeEnum.getNameByValue(supplierType);
        if (isWater) {
            url += "watermeter/close";
            note += "水表阀门关闭报文";
        } else {
            url += "elemeter/close";
            note += "电表跳闸报文";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        Map<String, Object> mapR = requestPost(map, url, note);
        return resultFor(map, mapR, "");
    }

    /**
     * 授权
     * @param deviceCode
     * @param supplierType
     * @param password 密码为6位密码
     * @param remark 房源名字，例：UONE闽南院子-1栋-420。设备类型为榉树时必填，其他可为null
     * @return
     */
    @Override
    @PostMapping("/authorization")
    @UonePermissions
    public RestResponse authorization(String deviceCode, String supplierType, String password, String remark, String endDate) {
        String url = zdyProp.getUrl() + "lock/auth";

        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        if (StrUtil.isNotBlank(password)) {
            map.put("password", password);
        }
        if (StrUtil.isNotBlank(remark)) {
            map.put("remark", remark);
        }
        if (StrUtil.isNotBlank(endDate)) {
            map.put("enddate", endDate);
        }
        Map<String, Object> mapR = requestPost(map, url, ZDYSupplierTypeEnum.getNameByValue(supplierType) + "门锁授权报文");
        if (supplierType.equals(ZDYSupplierTypeEnum.ZHIDAYUN.getValue()) && (Boolean) mapR.get("state")) {
            JSONObject data = (JSONObject) mapR.get("data");
            iMeterTaskFegin.saveOne(deviceCode, data.get("taskid").toString(), data.get("passwordid").toString());
        }
        return resultFor(map, mapR, "password");
    }

    /**
     * 冻结
     *
     * @param deviceCode
     * @param supplierType
     * @return
     */
    @Override
    @PostMapping("/frozen")
    @UonePermissions
    public RestResponse frozen(String deviceCode, String supplierType, String passwordid, String remark) {
        String url = zdyProp.getUrl() + "lock/frozen";
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        map.put("passwordid", passwordid);
        if (StrUtil.isNotBlank(remark)) {
            map.put("remark", remark);
        }
        Map<String, Object> mapR = requestPost(map, url, ZDYSupplierTypeEnum.getNameByValue(supplierType) + "门锁冻结报文");
        if (supplierType.equals(ZDYSupplierTypeEnum.ZHIDAYUN.getValue()) && (Boolean) mapR.get("state")) {
            JSONObject data = (JSONObject) mapR.get("data");
            iMeterTaskFegin.saveOne(deviceCode, data.get("taskid").toString(), passwordid);
        }
        return resultFor(map, mapR, "");
    }

    /**
     * 临时密码
     *
     * @param deviceCode
     * @param supplierType
     * @return
     */
    @Override
    @PostMapping("/getTempPassword")
    @UonePermissions
    public RestResponse getTempPassword(String deviceCode, String supplierType) {
        String url = zdyProp.getUrl() + "lock/temppassword";
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        Map<String, Object> mapR = requestPost(map, url, ZDYSupplierTypeEnum.getNameByValue(supplierType) + "临时密码报文");
        return resultFor(map, mapR, "password");
    }

    /**
     * 设备绑定（榉树）
     *
     * @param deviceCode
     * @param supplierType
     * @return
     */
    @Override
    @PostMapping("/zelkovaBind")
    public RestResponse zelkovaBind(String deviceCode, String supplierType) {
        String url = zdyProp.getUrl() + "lock/bind";
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        Map<String, Object> mapR = requestPost(map, url, ZDYSupplierTypeEnum.getNameByValue(supplierType) + "门禁设备绑定报文");
        return resultFor(map, mapR, "");
    }

    /**
     * 设备解除绑定(榉树)
     *
     * @param deviceCode
     * @param supplierType
     * @return
     */
    @Override
    @PostMapping("/zelkovaUnBind")
    public RestResponse zelkovaUnBind(String deviceCode, String supplierType) {
        String url = zdyProp.getUrl() + "lock/unbind";
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        Map<String, Object> mapR = requestPost(map, url, ZDYSupplierTypeEnum.getNameByValue(supplierType) + "门禁解除绑定报文");
        return resultFor(map, mapR, "");
    }

    /**
     * 门禁授权
     *
     * @param deviceCode
     * @param supplierType
     * @param mobile
     * @return
     */
    @Override
    @RequestMapping("/doorAuthority")
    public RestResponse doorAuthority(String deviceCode, String supplierType, String mobile) {
        String url = zdyProp.getUrl() + "door/auth";
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        map.put("mobile", mobile);
        Map<String, Object> mapR = requestPost(map, url, ZDYSupplierTypeEnum.getNameByValue(supplierType) + "门禁授权报文");
        return resultFor(map, mapR, "");
    }

    /**
     * 门禁解除授权
     *
     * @param deviceCode
     * @param supplierType
     * @param mobile
     * @return
     */
    @Override
    @PostMapping("/doorFrozen")
    public RestResponse doorFrozen(String deviceCode, String supplierType, String mobile) {
        String url = zdyProp.getUrl() + "door/unauth";
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        map.put("mobile", mobile);
        Map<String, Object> mapR = requestPost(map, url, ZDYSupplierTypeEnum.getNameByValue(supplierType) + "门禁解除授权报文");
        return resultFor(map, mapR, "");
    }

    /**
     * 打开门禁
     *
     * @param deviceCode
     * @param supplierType
     * @param mobile
     * @return
     */
    @Override
    @RequestMapping("/doorOpen")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse doorOpen(String deviceCode, String supplierType, String mobile) {
        String url = zdyProp.getUrl() + "door/open";
        Map<String, Object> map = new HashMap<>();
        map.put("deviceid", deviceCode);
        map.put("partnerid", supplierType);
        map.put("mobile", mobile);
        Map<String, Object> mapR = requestPost(map, url, ZDYSupplierTypeEnum.getNameByValue(supplierType) + "打开门禁报文");
        return resultFor(map, mapR, "");
    }

    /**
     * 传入参数，访问接口取回读数
     *
     * @param map 参数
     * @param url 接口地址
     * @return
     */
    public JSONObject requestPost(Map<String, Object> map, String url, String note) {

        HttpRequest hq = new HttpRequest(url).method(Method.POST);
        hq.form(map);
        HttpResponse hp = hq.execute();
        String body = hp.body();
        JSONObject json = JSONUtil.parseObj(body);
        //报文保存到日志表中
        JSONObject jsonMap = new JSONObject(map);
        sysInterFaceMsgFegin.createLog(jsonMap.toString(), json.toString(), note);
        return json;
    }

    /**
     * 返回类型，成功就返回读数，失败就返回错误信息
     *
     * @param mapR
     * @return
     */
    public RestResponse resultFor(Map<String, Object> map, Map<String, Object> mapR, String readName) {
        RestResponse response = new RestResponse();
        response.putAll(map);
        if (mapR.get("state").equals(true)) {
            if ("".equals(readName)) {
                return response.setSuccess(true);
            } else if ("password".equals(readName)) {
                Map<String, Object> data = (Map<String, Object>) mapR.get("data");
                response.put("password", data.get("password").toString());
                if (data.containsKey("passwordid")) {
                    response.put("passwordId", data.get("passwordid").toString());
                }
                return response.setSuccess(true);
            } else {
                //水电表读数
                Map<String, Object> data = (Map<String, Object>) mapR.get("data");
                response.put("read", data.get("readData").toString());
                return response.setSuccess(true);
            }
        } else {
            return RestResponse.failure(mapR.get("message").toString());
        }
    }
//    测试end
}
