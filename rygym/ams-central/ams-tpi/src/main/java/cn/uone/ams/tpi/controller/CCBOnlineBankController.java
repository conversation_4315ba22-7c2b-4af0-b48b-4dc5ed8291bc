package cn.uone.ams.tpi.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.ams.tpi.api.CCBOnlineBankApi;
import cn.uone.application.constant.TransferConfig;
import cn.uone.application.enumerate.order.AccountTypeEnum;
import cn.uone.application.enumerate.order.FailTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.TransferResultEnum;
import cn.uone.bean.entity.business.bil.BilInterfaceMsgEntity;
import cn.uone.bean.entity.business.bil.BilTransferEntity;
import cn.uone.bean.entity.business.bil.vo.BilTransferFileVo;
import cn.uone.bean.entity.business.bil.vo.BilTransferVo;
import cn.uone.fegin.bus.IBilInterfaceMsgFegin;
import cn.uone.fegin.bus.IBilTransferFegin;
import cn.uone.fegin.tpi.ICCBOnlineBankFegin;
import cn.uone.util.CodeUtil;
import cn.uone.web.util.XmlUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.Socket;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by xmlin on 2019-01-03.
 */
@RestController
@RequestMapping("/CCBOnlineBank")
@Slf4j
public class CCBOnlineBankController implements ICCBOnlineBankFegin {

    @Autowired
    private IBilTransferFegin bilTransferFegin;
    @Autowired
    private IBilInterfaceMsgFegin bilInterfaceMsgFegin;

    @Override
    @RequestMapping(value ="/getReq6W8040Xml",method= RequestMethod.POST)
    public String getReq6W8040Xml(@RequestBody List<String> fileCtxList,@RequestParam("requestSn") String requestSn, @RequestParam("amount") BigDecimal amount,
                                  @RequestParam("count") Integer count) {
        return CCBOnlineBankApi.getReq6W8040Xml(requestSn, amount, count, fileCtxList);
    }

    @Override
    @RequestMapping(value = "/getReq6W8020Xml", method = RequestMethod.POST)
    public String getReq6W8020Xml(@RequestParam("transferSn") String transferSn, @RequestParam("accountCode") String accountCode, @RequestParam("accountName") String accountName,
                                  @RequestParam("branchCode") String branchCode, @RequestParam("bankBranch") String bankBranch, @RequestParam("amount") BigDecimal amount,
                                  @RequestParam("note") String note, @RequestParam("serialNo") String serialNo) {
        return CCBOnlineBankApi.getReq6W8020Xml(transferSn, accountCode, accountName, branchCode, bankBranch, amount, note, serialNo);
    }

    @Override
    @RequestMapping(value ="/getReq6W0800Xml",method= RequestMethod.POST)
    public String getReq6W0800Xml(@RequestParam("requestSn") String requestSn, @RequestParam("batchName") String batchName, @RequestParam("s") String s, @RequestParam("i") Integer i) {
        return CCBOnlineBankApi.getReq6W0800Xml(requestSn, batchName, s, i);
    }

    @Override
    @RequestMapping(value = "/getReq6W1101Xml",method= RequestMethod.POST)
    public String getReq6W1101Xml(@RequestParam("requestSn") String requestSn, @RequestParam("accNo") String accNo, @RequestParam("accName") String accName) {
        return CCBOnlineBankApi.getReq6W1101Xml(requestSn, accNo, accName);
    }

    @Override
    @RequestMapping(value ="/getTxInfoByClientSocket",method= RequestMethod.POST)
    public Map<String, Object> getTxInfoByClientSocket(@RequestBody String reqXml, @RequestParam("tranCode") String tranCode) {
        Socket socket = null;
        BufferedReader in = null;
        PrintWriter out = null;
        Map<String, Object> txInfo = Maps.newHashMap();
        try {
            InetAddress address = InetAddress.getByName(TransferConfig.HOST);
            socket = new Socket(address, TransferConfig.PORT);
            log.info(socket + "已连接服务器");
            in = new BufferedReader(new InputStreamReader(socket.getInputStream(), "GB18030"));
            out = new PrintWriter(new BufferedWriter(new OutputStreamWriter(socket.getOutputStream(), "GB18030")), true);
            out.println(reqXml);
            log.info("发送：" + reqXml);
            StringBuffer resXmlBuf = new StringBuffer();
            while (true) {
                String readStr = in.readLine();
                if (StrUtil.isNotEmpty(readStr)) {
                    resXmlBuf.append(readStr);
                    if (readStr.endsWith("</TX>"))
                        break;
                }
            }
            String resXml = resXmlBuf.toString();
            log.info("接收：" + resXml);
            txInfo = getTxInfo(resXml, tranCode, null);
        } catch (Exception e) {
            e.printStackTrace();
            //ExceptionKit.add(e, ExceptionKit.UONEJOB);
        } finally {
            try {
                if (null != in)
                    in.close();
                if (null != out)
                    out.close();
                if (null != socket) {
                    socket.close();
                    log.info(socket + "连接已关闭");
                } else {
                    log.info("连接不存在");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return txInfo;
    }

    @Override
    @RequestMapping(value ="/clientSocket",method= RequestMethod.POST)
    public void clientSocket(@RequestBody String reqXml, @RequestParam("tranCode") String tranCode, @RequestParam("requestSn") String requestSn) {
        Thread t = new Thread(new Runnable() {
            public void run() {
                Socket socket = null;
                BufferedReader in = null;
                PrintWriter out = null;
                try {
                    InetAddress address = InetAddress.getByName(TransferConfig.HOST);
                    try {
                        socket = new Socket(address, TransferConfig.PORT);
                    } catch (Exception e) {
                        rollbackData(requestSn);
                        throw new Exception(e);
                    }
                    log.info(socket + "已连接服务器");
                    in = new BufferedReader(new InputStreamReader(socket.getInputStream(), "GB18030"));
                    out = new PrintWriter(new BufferedWriter(new OutputStreamWriter(socket.getOutputStream(), "GB18030")), true);
                    out.println(reqXml);
                    log.info(socket + "发送：\n" + XmlUtil.formatXml(reqXml, "GB18030"));
                    StringBuffer resXmlBuf = new StringBuffer();
                    while (true) {
                        String readStr = in.readLine();
                        if (StrUtil.isNotEmpty(readStr)) {
                            resXmlBuf.append(readStr);
                            if (readStr.endsWith("</TX>"))
                                break;
                        }
                    }
                    String resXml = resXmlBuf.toString();
                    log.info(socket + "接收：\n" + XmlUtil.formatXml(resXml, "GB18030"));

                    BilInterfaceMsgEntity entity = new BilInterfaceMsgEntity();
                    entity.setRequestMsg(reqXml);
                    entity.setResponseMsg(resXml.substring(0, 500));
                    entity.setSign("socket:" + InetAddress.getLocalHost().getHostAddress());
                    entity.setCode(requestSn);
                    entity.setNote("建行转账接口-" + tranCode + "-" + requestSn);
                    bilInterfaceMsgFegin.addInterfaceMsg(entity);

                    Map<String, Object> txInfo = getTxInfo(resXml, tranCode, requestSn);

                    if (TransferConfig.TRAN_6W8040.equals(tranCode) || TransferConfig.TRAN_6W8020.equals(tranCode)) {
                        doRes6W8040Notify(txInfo);
                    } else if (TransferConfig.TRAN_6W0800.equals(tranCode)) {
                        doRes6W0800Notify(txInfo, requestSn);
                    } else {
                        log.info(txInfo.toString());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    try {
                        if (null != in)
                            in.close();
                        if (null != out)
                            out.close();
                        if (null != socket) {
                            socket.close();
                            log.info(socket + "连接已关闭");
                        } else {
                            log.info("连接不存在");
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
        t.start();
    }

    @Override
    @RequestMapping(value = "/queryBySn", method = RequestMethod.POST)
    public Map<String, Object> queryBySn(@RequestBody String reqXml, @RequestParam("refundSn") String refundSn) {
        BufferedReader in = null;
        PrintWriter out = null;
        try {
            InetAddress address = InetAddress.getByName(TransferConfig.HOST);
            Socket socket = new Socket(address, TransferConfig.PORT);
            in = new BufferedReader(new InputStreamReader(socket.getInputStream(), "GB18030"));
            out = new PrintWriter(new BufferedWriter(new OutputStreamWriter(socket.getOutputStream(), "GB18030")), true);
            out.println(reqXml);
            StringBuffer resXmlBuf = new StringBuffer();
            while (true) {
                String readStr = in.readLine();
                if (StrUtil.isNotEmpty(readStr)) {
                    resXmlBuf.append(readStr);
                    if (readStr.endsWith("</TX>"))
                        break;
                }
            }
            String resXml = resXmlBuf.toString();
            BilInterfaceMsgEntity entity = new BilInterfaceMsgEntity();
            entity.setRequestMsg(reqXml);
            Map<String, Object> resMap = XmlUtil.xml2map(resXml);
            Map<String, Object> tx = (Map<String, Object>) resMap.get("TX");
            String return_msg = (String) tx.get("RETURN_MSG");
            entity.setResponseMsg(return_msg);
            entity.setSign("socket:" + InetAddress.getLocalHost().getHostAddress());
            entity.setCode(refundSn);
            entity.setNote("建行转账接口-6W0800-" + refundSn);
            bilInterfaceMsgFegin.addInterfaceMsg(entity);
            Map<String, Object> txInfo = getMapByKey(resXml, "TX_INFO");
            return txInfo;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }finally {
            try {
                if(in != null){
                    in.close();
                }
                if(out != null){
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private Map<String, Object> getMapByKey(String resXml, String key) {
        Map<String, Object> resMap = XmlUtil.xml2map(resXml);
        Map<String, Object> tx = (Map<String, Object>) resMap.get("TX");
        String resp_code = (String) tx.get("RETURN_CODE");
        if (Arrays.asList("0130Z110B550", "XNAP1000UNON").equals(resp_code)) {
            return null;
        } else {
            Map<String, Object> txInfo = (Map<String, Object>) tx.get(key);
            return txInfo;
        }
    }

    private void doRes6W8040Notify(Map<String, Object> txInfo) throws Exception {
        Object obj = txInfo.get("ERROR_LIST");
        if (ObjectUtil.isNotNull(obj)) {
            String className = obj.getClass().getName();
            List<Map<String, String>> errorList = Lists.newArrayList();
            if ("java.util.ArrayList".equals(className)) {
                errorList = (List<Map<String, String>>) obj;
            } else {
                Map<String, String> map = (Map<String, String>) obj;
                errorList.add(map);
            }
            if (null != errorList && errorList.size() > 0) {
                for (Map<String, String> errorMap : errorList) {
                    String serialNo = errorMap.get("SERIAL_NUM");
                    String respMsg = errorMap.get("ERROR_MSG");
                    readCallBack(serialNo, "error", respMsg);
                }
            }
        }
    }

    private void doRes6W0800Notify(Map<String, Object> txInfo, String batchName) throws Exception {
        int curPage = Integer.valueOf(txInfo.get("CUR_PAGE").toString());
        int pageCount = Integer.valueOf(txInfo.get("PAGE_COUNT").toString());
        Object obj = txInfo.get("PAYEELIST");
        String classname = obj.getClass().getName();
        List<Map<String, String>> payeeList = Lists.newArrayList();
        if ("java.util.ArrayList".equals(classname)) {
            payeeList = (List<Map<String, String>>) obj;
        } else {
            Map<String, String> map = (Map<String, String>) obj;
            payeeList.add(map);
        }
        for (Map<String, String> payeeMap : payeeList) {
            String serialno = payeeMap.get("SERIAL");
            String resp_state = payeeMap.get("STATUS");
            String resp_msg = payeeMap.get("ERR_MSG");
            readCallBack(serialno, resp_state, resp_msg);
        }
        if (curPage < pageCount) {
            String requestSn = CodeUtil.createRequestSn(17);
            String reqXml = getReq6W0800Xml(requestSn, batchName, "", curPage + 1);
            clientSocket(reqXml, TransferConfig.TRAN_6W0800, batchName);
        }
    }

    //获取回调报文中的TX_INFO
    private Map<String, Object> getTxInfo(String resXml, String tranCode, String requestSn) throws Exception {
        Map<String, Object> resMap = XmlUtil.xml2map(resXml);
        Map<String, Object> tx = (Map<String, Object>) resMap.get("TX");
        String resp_code = (String) tx.get("RETURN_CODE");
        //tranCode = TRAN_6W1101 不走
        if (!TransferConfig.TRAN_6W1101.equals(tranCode) && !"000000".equals(resp_code)) {
            //0130Z110B550代表网银服务器未收到（6W8040）的原批量转账请求，对应提示信息为"没有查到符合条件的转账支付信息"
            if ("0130Z110B550".equals(resp_code)) {
                rollbackData(requestSn);
            }
            String resp_msg = tx.get("RETURN_MSG").toString();
            throw new Exception(requestSn + "批次:" + resp_code + "-" + resp_msg);
        }
        Map<String, Object> txInfo = (Map<String, Object>) tx.get("TX_INFO");
        return txInfo;
    }

    //批次异常回滚数据
    private void rollbackData(String batchName) {
        if (StrUtil.isNotEmpty(batchName)) {
            List<BilTransferEntity> bilTransferEntityList = bilTransferFegin.getListBySn(batchName);
            for (BilTransferEntity mt : bilTransferEntityList) {
                mt.setSn(null);
                bilTransferFegin.update(mt);
            }
        }
    }

    @Override
    @RequestMapping(value = "/sendOffer",method= RequestMethod.POST)
    public Map<String, Object> sendOffer(@RequestBody List<BilTransferVo> list, @RequestParam("requestSn") String requestSn, @RequestParam("payeeType") String payeeType) {
        Map<String, Object> sendMap = Maps.newHashMap();
        int ordersLength = 0;// 报盘总笔数
        BigDecimal ordersPayment = BigDecimal.ZERO;// 报盘总金额
        int serial = 0;
        List<String> fileCtxList = Lists.newArrayList();
        // 循环账单列表
        for (BilTransferVo transfer : list) {
            BilTransferEntity entity = bilTransferFegin.getById(transfer.getId());
            serial = serial + 1;// 每一笔流水号自增
            String serialno = CodeUtil.createSerialNo(requestSn, serial);// 流水号
            entity.setSerialNo(serialno);
            entity.setTransferTime(new Date());
            entity.setSn(requestSn);
            bilTransferFegin.update(entity);

            BigDecimal payment = transfer.getPayment();// 报盘单笔金额
            // 组装报盘文件
            String bankaccount = transfer.getAccountCode();// 账号
            String accountname = transfer.getAccountName();// 账户名
            String depositBank_no = transfer.getBranchCode();// 开户行-支付行号
            String depositBank_name = transfer.getBranchName();// 开户行名称
            String roomcode = null;// 摘要
            if (StrUtil.isEmpty(transfer.getOrderId())) {
                roomcode = AccountTypeEnum.getNameByValue(transfer.getAccountType()) + "汇总";
            } else {
                roomcode = transfer.getAddress()+"，"+OrderTypeEnum.getNameByValue(transfer.getOrderType())+"，"+transfer.getCode();
            }
            BilTransferFileVo vo = new BilTransferFileVo();
            vo.setNo(serialno);
            vo.setPayerAccount(TransferConfig.PAYER_ACCOUNT);
            vo.setPayerName(TransferConfig.PAYER_NAME);
            vo.setPayeeType(payeeType);
            vo.setPayeeAccount(bankaccount);
            vo.setPayeeName(accountname);
            vo.setPayeeBankName(depositBank_name);
            vo.setPayeeUnionNo(depositBank_no);
            vo.setUserSerialNo(serialno);
            vo.setAmount(payment);
            vo.setPurpose("转账");
            vo.setNote(roomcode);
            fileCtxList = vo.addToFileCxtList(fileCtxList);
            ordersLength = ordersLength + 1;
            ordersPayment = ordersPayment.add(payment);
        }
        if (fileCtxList.size() > 1) {
            sendMap.put("amount", ordersPayment);
            sendMap.put("count", ordersLength);
            sendMap.put("fileCtxList", fileCtxList);
            sendMap.put("requestSn", requestSn);
        }
        return sendMap;
    }

    //批量代付回盘文件-每一笔读取
    private void readCallBack(String serialNo, String respState, String respMsg) {
        String respType = "E";
        String result = TransferResultEnum.RESULTFAIL.getValue();
        BilTransferEntity bilTransfer = bilTransferFegin.getByNo(serialNo);
        if ("4".equals(respState) || "2".equals(respState) || "3".equals(respState)) {
            respType = "S";
            result = TransferResultEnum.RESULTTRUE.getValue();
            respMsg = "成功";
            bilTransfer.setFailType(null);
            //更新子账单转账状态
            bilTransferFegin.updateTransferState(bilTransfer);
        } else if ("9".equals(respState) || "0".equals(respState) || "1".equals(respState) || "6".equals(respState)) {
            respType = "W";
            result = TransferResultEnum.RESULTUNSURENESS.getValue();
            bilTransfer.setFailType(FailTypeEnum.THREE.getValue());
        } else {
            bilTransfer.setFailType(FailTypeEnum.TWO.getValue());
        }
        bilTransfer.setRemark(respType + "-" + respState + "-" + respMsg);
        bilTransfer.setResult(result);
        bilTransferFegin.update(bilTransfer);
    }
}
