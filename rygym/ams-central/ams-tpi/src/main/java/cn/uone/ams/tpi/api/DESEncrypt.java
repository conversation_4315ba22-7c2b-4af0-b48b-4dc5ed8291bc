package cn.uone.ams.tpi.api;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.web.util.MD5Util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;

/**
 * @Title: DES可逆加密算法：
 * @Description:
 * @Version:1.1
 */
public class DESEncrypt {
    private static String strDefaultKey = "c1#3E^0h";
    private Cipher encryptCipher = null;
    private Cipher decryptCipher = null;

    /**
     * 默认构造方法，使用默认密钥
     *
     * @throws Exception
     */
    public DESEncrypt() throws Exception {
        this(strDefaultKey);
    }

    /**
     * 指定密钥构造方法
     *
     * @param strKey 指定的密钥
     * @throws Exception
     */
    public DESEncrypt(String strKey) {
        try {
            Key key = getKey(strKey.getBytes());

            encryptCipher = Cipher.getInstance("DES");
            encryptCipher.init(Cipher.ENCRYPT_MODE, key);

            decryptCipher = Cipher.getInstance("DES");
            decryptCipher.init(Cipher.DECRYPT_MODE, key);
        } catch (Exception e) {

        }
    }

    /**
     * 将byte数组转换为表示16进制值的字符串， 如：byte[]{8,18}转换为：0813， 和public static byte[]
     * hexStr2ByteArr(String strIn) 互为可逆的转换过程
     *
     * @param arrB 需要转换的byte数组
     * @return 转换后的字符串
     * @throws Exception 本方法不处理任何异常，所有异常全部抛出
     */
    public static String byteArr2HexStr(byte[] arrB) {
        int iLen = arrB.length;
        // 每个byte用两个字符才能表示，所以字符串的长度是数组长度的两倍
        StringBuffer sb = new StringBuffer(iLen * 2);
        for (int i = 0; i < iLen; i++) {
            int intTmp = arrB[i];
            // 把负数转换为正数
            while (intTmp < 0) {
                intTmp = intTmp + 256;
            }
            // 小于0F的数需要在前面补0
            if (intTmp < 16) {
                sb.append("0");
            }
            sb.append(Integer.toString(intTmp, 16));
        }
        return sb.toString();
    }

    /**
     * 将表示16进制值的字符串转换为byte数组， 和public static String byteArr2HexStr(byte[] arrB)
     * 互为可逆的转换过程
     *
     * @param strIn 需要转换的字符串
     * @return 转换后的byte数组
     * @throws Exception 本方法不处理任何异常，所有异常全部抛出
     */
    public static byte[] hexStr2ByteArr(String strIn) throws Exception {
        byte[] arrB = strIn.getBytes();
        int iLen = arrB.length;

        // 两个字符表示一个字节，所以字节数组长度是字符串长度除以2
        byte[] arrOut = new byte[iLen / 2];
        for (int i = 0; i < iLen; i = i + 2) {
            String strTmp = new String(arrB, i, 2);
            arrOut[i / 2] = (byte) Integer.parseInt(strTmp, 16);
        }
        return arrOut;
    }

    public static void main(String[] args) throws Exception {
        String md5 = "KLkbQql61587696983185gj_6075_6366";
        String yuanmd5 = MD5Util.md5Str(md5);
        System.out.println("MD：" + yuanmd5);
        /*======================加密===========================*/
        //私钥
        String key = "KLkbQql6";
        //原文
        String str = "<EMAIL>";
        System.out.println("原文：" + str);
        //加密
        System.out.println("私钥key:" + key);
        DESEncrypt desPlus2 = new DESEncrypt(key);

        String e2 = desPlus2.encrypt(str);
        System.out.println("密文:" + e2);
        System.out.println("*************************");
        /*======================加密结束===========================*/

        String url = "http://ops.huohetech.com:80/login";
        HttpRequest hq = HttpUtil.createPost(url);
        hq.contentType("application/json");
        hq.header("version", "1.0");
        hq.header("s_id", "f6a806e3293ddef938a08ff47fa65b0e");
        hq.body("{account:\"***********\",password:\"ccbd412c078d680efe4688c37e78e35f\"}");
        HttpResponse hp = hq.execute();
        JSONObject json = JSONUtil.parseObj(hp.body());
        System.out.println("返回结果:" + json);
        String data = json.getStr("data");

//		/*======================解密=============================*/
//		//私钥
//		String key2 = "KLkbQql6";
//
//		//需要解密的密文
//		String pwdText = "98987a1103832c9109f47423f655de72";
//
//		//创建加解密对象
//		DESEncrypt des = new DESEncrypt(key2);
//
//		//使用解密方法
//		String yuanWen = des.decrypt(pwdText);
//
//		//输出
//		System.out.println("密文："+pwdText);
//
//		System.out.println("私钥key：" + key2);
//
//		System.out.println("解密后："+yuanWen);
//
//
//		/*======================解密结束==========================*/
    }

    /**
     * 加密字节数组
     *
     * @param arrB 需加密的字节数组
     * @return 加密后的字节数组
     * @throws Exception
     */
    public byte[] encrypt(byte[] arrB) {
        try {
            return encryptCipher.doFinal(arrB);
        } catch (Exception e) {
            System.out.println("Ex:" + e.getMessage());
        }
        return null;
    }

    /**
     * 加密字符串
     *
     * @param strIn 需加密的字符串
     * @return 加密后的字符串
     * @throws Exception
     */
    public String encrypt(String strIn) {
        return byteArr2HexStr(encrypt(strIn.getBytes()));
    }

    /**
     * 解密字节数组
     *
     * @param arrB 需解密的字节数组
     * @return 解密后的字节数组
     * @throws Exception
     */
    public byte[] decrypt(byte[] arrB) throws Exception {
        return decryptCipher.doFinal(arrB);
    }

    /**
     * 解密字符串
     *
     * @param strIn 需解密的字符串
     * @return 解密后的字符串
     * @throws Exception
     */
    public String decrypt(String strIn) throws Exception {
        return new String(decrypt(hexStr2ByteArr(strIn)));
    }

    /**
     * 从指定字符串生成密钥，密钥所需的字节数组长度为8位 不足8位时后面补0，超出8位只取前8位
     *
     * @param arrBTmp 构成该字符串的字节数组
     * @return 生成的密钥
     * @throws Exception
     */
    private Key getKey(byte[] arrBTmp) throws Exception {
        // 创建一个空的8位字节数组（默认值为0）
        byte[] arrB = new byte[8]; // 将原始字节数组转换为8位
        for (int i = 0; i < arrBTmp.length && i < arrB.length; i++) {
            arrB[i] = arrBTmp[i];
        }
        // 生成密钥
        Key key = new SecretKeySpec(arrB, "DES");
        return key;
    }

}