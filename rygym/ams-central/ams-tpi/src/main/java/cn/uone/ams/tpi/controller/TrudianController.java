package cn.uone.ams.tpi.controller;

import cn.hutool.core.lang.Console;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.ams.tpi.api.Trudian<PERSON>pi;
import cn.uone.ams.tpi.api.TrudianDirectConnectionApi;
import cn.uone.ams.tpi.api.minio.MinioClientApi;
import cn.uone.application.enumerate.ApiTypeEnum;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.cache.util.CacheUtil;
import cn.uone.fegin.bus.IResProjectFegin;
import cn.uone.fegin.crm.IExpenseConfigFegin;
import cn.uone.fegin.crm.ISysCompanyFegin;
import cn.uone.fegin.tpi.ITrudianFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * supplierType是供应商，例如云丁的设备就是"yunding"（必填）
 * devicecode就是设备编号devicecode（必填）
 * mobile就是租客的手机号，用于门禁的操作
 * passwordid为密码id，仅冻结门锁时需要传入
 * 返回格式根据readName设置，"readData"=返回String读数，""=返回String"成功"
 * "password"=返回整个data对象，里面password必存在，passwordid仅在门禁授权存在
 */
@Api(value = "设备接口", tags = {"设备接口"})
@RestController
@RequestMapping("/trudian")
public class TrudianController extends BaseController implements ITrudianFegin {

    @Autowired
    private TrudianApi trudianApi;
    @Autowired
    private TrudianDirectConnectionApi trudianDirectConnectionApi;
    @Autowired
    IExpenseConfigFegin expenseConfigFegin;
    @Autowired
    IResProjectFegin resProjectFegin;
    @Autowired
    ISysCompanyFegin sysCompanyFegin;
    @Autowired
    private MinioClientApi minioClientApi;


    /**
     * 生成开门二维码
     * @param expenseProjectId
     * @return
     */
    @Override
    @ApiOperation(value = "生成开门二维码")
    @PostMapping("/qrcode")
    public RestResponse qrcode(@RequestParam("expenseProjectId") String expenseProjectId,@RequestParam("localIds") String localIds) {
        JSONObject config = getTrudianDirectConfig(expenseProjectId);
        return trudianDirectConnectionApi.qrcode(config.getStr("appId"), config.getStr("secretKey"), config.getStr("apiUrl"), localIds, null, null);
//        return trudianDirectConnectionApi.qrcode(config.getStr("ap"),localIds,null);
//        return trudianApi.qrcode(config.getStr("appId"), config.getStr("secretKey"),localIds);
    }

    /**
     * 注册人脸
     * @param projectId
     * @return
     */
    @Override
    @ApiOperation(value = "注册人脸")
    @PostMapping("/addFace")
    public RestResponse addFace(@RequestParam("projectId") String projectId,@RequestParam("localId") String localId,@RequestParam("cardSN") String cardSN,@RequestParam("url") String url) {
        ResProjectEntity project = resProjectFegin.getById(projectId);
        SysCompanyEntity company = sysCompanyFegin.getById(project.getCompanyId());
        String exeProjectId = company.getTopId();
        JSONObject trudianDirectConfig = getTrudianDirectConfig(exeProjectId);
        String appId = trudianDirectConfig.getStr("appId");
        String secretKey = trudianDirectConfig.getStr("secretKey");
        String apiUrl = trudianDirectConfig.getStr("apiUrl");
        RestResponse response = trudianDirectConnectionApi.regFace(appId, secretKey, apiUrl, localId, cardSN, cardSN, url);
        /*if (BooleanUtil.isTrue(response.getSuccess())) {
            return trudianDirectConnectionApi.reboot(appId, secretKey, apiUrl, localId);
        }*/
        return response;
//        JSONObject config = getConfig(exeProjectId);
//        String appId = config.getStr("appId");
//        String secretKey = config.getStr("secretKey");
//        trudianApi.addFace(appId,secretKey,localId,cardSN,url);
//        trudianApi.reboot(appId,secretKey,localId);
//        return RestResponse.success();
    }

    @PostMapping("/test")
    @UonePermissions
    public RestResponse test(@RequestParam String url) {
        return trudianDirectConnectionApi.regFace("2137990114180904", "16A46EQ6", "16A46EQ6", url);
    }

    /**
     * 删除人脸
     * @param projectId
     * @return
     */
    @Override
    @ApiOperation(value = "删除人脸")
    @PostMapping("/delFace")
    public RestResponse delFace(@RequestParam("projectId") String projectId,@RequestParam("localId") String localId,@RequestParam("cardSN") String cardSN) {
        ResProjectEntity project = resProjectFegin.getById(projectId);
        SysCompanyEntity company = sysCompanyFegin.getById(project.getCompanyId());
        String exeProjectId = company.getTopId();
        JSONObject trudianDirectConfig = getTrudianDirectConfig(exeProjectId);
        //JSONObject config = getConfig(exeProjectId);
        String appId = trudianDirectConfig.getStr("appId");
        String secretKey = trudianDirectConfig.getStr("secretKey");
        String apiUrl = trudianDirectConfig.getStr("apiUrl");
        //trudianApi.delFace(appId,secretKey,localId,cardSN);
        trudianDirectConnectionApi.delFace(appId,secretKey,apiUrl,localId,cardSN,cardSN);
        return RestResponse.success();
    }

    /**
     * 触点直连接入->回调
     *
     * @param type    回调事件类型
     * @param localId 门禁机序列号
     */
    @ApiOperation(value = "回调")
    @PostMapping("/notify")
    @UonePermissions
    public void notify(HttpServletRequest request, HttpServletResponse response, @RequestParam String type, @RequestParam String localId) {
        trudianDirectConnectionApi.notify(request, response, type, localId);
    }

    /**
     * 获取接口参数配置
     *
     * @param apiChannel
     * @return
     */
    private JSONObject getTrudianDirectConfig(String apiChannel) {
        JSONObject config = JSONUtil.parseObj(CacheUtil.get(ApiTypeEnum.TRUDIAN_DIRECT.name() + StringPool.UNDERSCORE + apiChannel));
        if (MapUtil.isEmpty(config)) {
            config = expenseConfigFegin.getExpenseConfigByPro(apiChannel, ApiTypeEnum.TRUDIAN_DIRECT.getValue());
            CacheUtil.putEx(ApiTypeEnum.TRUDIAN_DIRECT.name() + StringPool.UNDERSCORE + apiChannel, config, 1L, TimeUnit.DAYS);
        }
        Console.log("触点出入口门禁-直连配置:{}", JSONUtil.toJsonStr(config));
        return config;
    }


    /**
     * 获取接口参数配置
     * @param apiChannel
     * @return
     */
    private JSONObject getConfig(String apiChannel){
        JSONObject config = JSONUtil.parseObj(CacheUtil.get("TRUDIAN_"+apiChannel));
        if(config == null || config.isEmpty()){
            config = expenseConfigFegin.getExpenseConfigByPro(apiChannel, ApiTypeEnum.TRUDIAN.getValue());
            CacheUtil.putEx("TRUDIAN_"+apiChannel,config,1l, TimeUnit.DAYS);
        }
        Console.log("触点出入口门禁配置:{}",JSONUtil.toJsonStr(config));
        return config;
    }
}
