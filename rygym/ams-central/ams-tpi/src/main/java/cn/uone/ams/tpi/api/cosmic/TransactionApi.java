package cn.uone.ams.tpi.api.cosmic;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import cn.uone.bean.entity.tpi.cosmic.transaction.TransactionCreditedQueryDto;
import cn.uone.web.base.RestResponse;
import lombok.Getter;
import org.springframework.stereotype.Component;

@Component
public class TransactionApi extends CosmicBaseApi {

    /**
     * 交易明细待认领查询
     *
     * @param dto 查询body
     * @return BaseQueryPageVo<T> T类型TransactionCreditedQueryVo
     */
    public RestResponse transactionDetailCreditedQuery(TransactionCreditedQueryDto dto) {
        dto.setSourceSystem(CLIENT_ID);
        return reqPost(TransactionUrlEnum.TRANSACTION_DETAIL_CREDITED_QUERY.getUrl(), dto.beanToMap(), null);
    }

    /**
     * 交易明细保存
     *
     * @param bankId 交易明细唯一标识
     * @return baseSaveVo
     * {
     * "number":"MXLX-********-**********",
     * "billStatus":true,
     * "billIndex":"0",
     * "id":"1934010095989971968",
     * "type":"Update",
     * "errors":[]
     * }
     */
    public RestResponse transactionDetailRewrite(String bankId) {
        JSONObject data = new JSONObject();
        data.put("data", MapUtil.builder("xmgd_sourcesystem", CLIENT_ID)
                .put("xmgd_ebankid", bankId)
                .build());
        return reqPost(TransactionUrlEnum.TRANSACTION_DETAIL_REWRITE.getUrl(), data, null);
    }

    @Getter
    private enum TransactionUrlEnum {

        TRANSACTION_DETAIL_CREDITED_QUERY("bei_transdetail_credited_query", "bei/transdetailClaimQuery", "交易明细待认领查询"),

        TRANSACTION_DETAIL_REWRITE("cas_paytransdetail_save(cas_paytransdetail_save)", "bei/transdetailRewrite", "交易明细保存"),
        ;

        private String number;

        private String url;

        private String description;

        TransactionUrlEnum(String number, String url, String description) {
            this.number = number;
            this.url = url;
            this.description = description;
        }
    }


}
