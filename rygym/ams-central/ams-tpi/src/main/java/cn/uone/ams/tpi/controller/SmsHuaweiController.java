package cn.uone.ams.tpi.controller;

import cn.uone.ams.tpi.api.SmsHuaweiApi;
import cn.uone.fegin.tpi.ISmsAliFegin;
import cn.uone.fegin.tpi.ISmsHuaweiFegin;
import cn.uone.web.base.RestResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/smsHuawei")
@Slf4j
public class SmsHuaweiController implements ISmsHuaweiFegin {

    @Resource
    private SmsHuaweiApi api;

    @PostMapping("/sendSms")
    @ApiOperation("发送华为云短信")
    public void sendSms(@RequestParam("receiver") String receiver,
                        @RequestParam("templateId") String templateId,
                        @RequestParam("templateParas") String templateParas,
                        @RequestParam("type") String type) throws Exception {
        api.sendSms(receiver,templateId,templateParas,type);
    }

}
