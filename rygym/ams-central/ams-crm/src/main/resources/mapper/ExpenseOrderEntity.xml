<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.crm.dao.TExpenseOrderDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.crm.ExpenseOrderEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="order_code" property="orderCode" />
        <result column="order_type" property="orderType" />
        <result column="pay_type" property="payType" />
        <result column="payer_id" property="payerId" />
        <result column="payer_name" property="payerName" />
        <result column="pay_amount" property="payAmount" />
        <result column="activity_amount" property="activityAmount" />
        <result column="pay_time" property="payTime" />
        <result column="seq_id" property="seqId" />
        <result column="state" property="state" />
        <result column="remark" property="remark" />
        <result column="activity_id" property="activityId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        order_code, order_type, pay_type, payer_id, payer_name, pay_amount, activity_amount, pay_time, seq_id, state, remark, activity_id
    </sql>

    <select id="getTotalAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(t.`pay_amount`),0) totalAmount FROM t_expense_order t
        WHERE 1=1 and t.`state`='20' and t.payer_id = #{payerId}
    </select>

    <select id="selectPage" resultMap="BaseResultMap">
        SELECT t.* from t_expense_order t
        WHERE 1=1 and t.payer_id = #{searchVo.payerId}
        <if test="searchVo.orderCode != null and searchVo.orderCode != ''">
            and t.order_code like CONCAT('%', #{searchVo.orderCode}, '%')
        </if>
        <if test="searchVo.seqId != null and searchVo.seqId != ''">
            and t.seq_id like CONCAT('%', #{searchVo.seqId}, '%')
        </if>
        <if test="searchVo.orderType != null and searchVo.orderType != ''">
            AND t.order_type = #{searchVo.orderType}
        </if>
        <if test="searchVo.payType != null and searchVo.payType != ''">
            AND t.pay_type = #{searchVo.payType}
        </if>
        <if test="searchVo.state != null and searchVo.state != ''">
            AND t.state = #{searchVo.state}
        </if>
        <if test="searchVo.startDate != null and searchVo.startDate != ''">
            AND <![CDATA[date(t.pay_time) >= date(#{searchVo.startDate}) ]]>
        </if>
        <if test="searchVo.endDate != null and searchVo.endDate != ''">
            AND <![CDATA[date(t.pay_time) <= date(#{searchVo.endDate}) ]]>
        </if>
        order by t.create_date desc
    </select>

</mapper>
