<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.crm.dao.ExpenseConfigDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.crm.ExpenseConfigEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="expense_project_id" property="expenseProjectId" />
        <result column="expense_project_code" property="expenseProjectCode" />
        <result column="sys_company_id" property="sysCompanyId" />
        <result column="config_type" property="configType" />
        <result column="config_name" property="configName" />
        <result column="config_content" property="configContent" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        expense_project_id, expense_project_code, sys_company_id, config_type, config_name, config_content, remark
    </sql>

</mapper>
