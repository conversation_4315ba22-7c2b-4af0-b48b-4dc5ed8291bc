<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.crm.dao.DeptDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.crm.DeptEntity">
        <result column="id" property="id"/>
        <result column="create_date" property="createDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="name" property="name"/>
        <result column="director_id" property="directorId"/>
        <result column="director_name" property="directorName"/>
        <result column="parent_code" property="parentCode"/>
        <result column="code_link" property="codeLink"/>
        <result column="code" property="code"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_date,
        update_by,
        name, director_id, director_name, parent_code, code_link, code
    </sql>


    <select id="getUserDepts" resultType="cn.uone.bean.entity.crm.DeptEntity">
        select * from sys_dept where 1=1
        and code in
        <foreach collection="codes" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getDeptTree" resultType="cn.uone.bean.entity.crm.DeptEntity">
        select *  from sys_dept where 1=1
        <choose>
            <when test="parentCode != null and parentCode != ''">
                and parent_code = #{parentCode}
            </when>
            <otherwise>
                and parent_code is null
            </otherwise>
        </choose>
    </select>


</mapper>
