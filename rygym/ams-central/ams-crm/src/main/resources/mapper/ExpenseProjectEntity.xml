<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.crm.dao.TExpenseProjectDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.crm.ExpenseProjectEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="project_name" property="projectName" />
        <result column="project_type" property="projectType" />
        <result column="company_name" property="companyName" />
        <result column="company_code" property="companyCode" />
        <result column="bank_code" property="bankCode" />
        <result column="bank_name" property="bankName" />
        <result column="bank_card" property="bankCard" />
        <result column="contacts" property="contacts" />
        <result column="tel" property="tel" />
        <result column="legal_person" property="legalPerson" />
        <result column="expense_amount" property="expenseAmount" />
        <result column="expense_balance" property="expenseBalance" />
        <result column="activity_amount" property="activityAmount" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="state" property="state" />
        <result column="remark" property="remark" />
        <result column="url" property="url" />
        <result column="sign_price" property="signPrice" />
        <result column="sms_price" property="smsPrice" />
        <result column="source_num" property="sourceNum" />
        <result column="opt_name" property="optName" />
        <result column="opt_tel" property="optTel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        project_name, project_type, company_name, company_code, bank_code, bank_name, bank_card, contacts, tel, legal_person, expense_amount, expense_balance, activity_amount, start_date, end_date, state, remark,
        url,sign_price,sms_price,source_num,opt_name,opt_tel
    </sql>

    <select id="getByUserId" resultMap="BaseResultMap">
        SELECT t.* FROM t_expense_project t,sys_company c,sys_user u
        WHERE t.id=c.`top_id` AND c.id=u.`company_id` AND u.id = #{userId}
    </select>

</mapper>
