package cn.uone.crm.controller;


import cn.uone.application.enumerate.LxDocTypeEnum;
import cn.uone.fegin.crm.ILxDocFegin;
import cn.uone.fegin.tpi.IQyWechatFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
@RestController
@RequestMapping("/lxdoc")
@Api("乐享文档管理")
public class LxDocController extends BaseController implements ILxDocFegin {

    @Autowired
    private IQyWechatFegin qyWechatFegin;

    @Override
    @RequestMapping(value = "/getLxDoc", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse getLxDoc(String type) {
        List<Map<String, Object>> list = new ArrayList<>();
        try{
            //现在枚举对应的id已改为分类id而不是文档id，因此该方法不再用
            String categoryId = LxDocTypeEnum.getValueByEnum(type);
            list = qyWechatFegin.getLxDocList(categoryId);
        }catch (Exception e){
            e.printStackTrace();
        }
        return RestResponse.success().setData(list);
    }

}
