package cn.uone.crm.service;

import cn.uone.bean.entity.crm.QywxAgentEntity;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
public interface IQywxAgentService extends IService<QywxAgentEntity> {
    RestResponse syncQywxUser(String cropid, String agentsecret, String deptIds);
    QywxAgentEntity getByUserId(String userId);

    List<QywxAgentEntity> getByUserIds(List<String> userIds);
}
