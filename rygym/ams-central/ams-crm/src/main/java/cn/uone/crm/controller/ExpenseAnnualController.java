package cn.uone.crm.controller;


import cn.uone.bean.entity.crm.*;
import cn.uone.crm.service.ISysCompanyService;
import cn.uone.crm.service.IExpenseAnnualService;
import cn.uone.crm.service.IUserService;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@RestController
@RequestMapping("/t-expense-annual-entity")
public class ExpenseAnnualController extends BaseController {

    @Autowired
    ISysCompanyService sysCompanyService;
    @Autowired
    IUserService userService;
    @Autowired
    IExpenseAnnualService expenseAnnualService;

    @RequestMapping("/selectPage")
    public RestResponse selectPage(Page page, ExpenseAnnualSearchVo searchVo) {
        RestResponse response = new RestResponse();
        String userId = UoneSysUser.id();
        UserEntity user = userService.getById(userId);
        SysCompanyEntity company = sysCompanyService.getById(user.getCompanyId());
        searchVo.setCompanyId(company.getTopId());
        IPage<ExpenseAnnualEntity> data = expenseAnnualService.selectPage(page, searchVo);
        return response.setSuccess(true).setData(data);
    }

    /**
     * 备注
     */
    @RequestMapping("/addRemark")
    public RestResponse addRemark(String id, String remark) {
        RestResponse response = new RestResponse();
        try {
            ExpenseAnnualEntity entity = expenseAnnualService.getById(id);
            entity.setRemark(remark);
            expenseAnnualService.updateById(entity);
            response.setSuccess(true).setMessage("操作成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }
}
