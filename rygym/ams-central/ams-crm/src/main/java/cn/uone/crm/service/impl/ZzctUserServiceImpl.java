package cn.uone.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.sys.vo.TreeVo;
import cn.uone.bean.entity.crm.*;
import cn.uone.bean.parameter.CheckInUserPo;
import cn.uone.bean.parameter.DataScopeVo;
import cn.uone.cache.util.CacheUtil;
import cn.uone.crm.dao.DataScopeDao;
import cn.uone.crm.dao.DeptDao;
import cn.uone.crm.dao.RoleDao;
import cn.uone.crm.dao.UserDao;
import cn.uone.crm.service.ISysMsgTemplateService;
import cn.uone.crm.service.IUserService;
import cn.uone.crm.service.IZzctSysMsgTemplateService;
import cn.uone.crm.service.IZzctUserService;
import cn.uone.fegin.bus.IExUserFegin;
import cn.uone.fegin.tpi.IQyWechatFegin;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.mybatis.inerceptor.DataScopeTypeEnum;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.util.ShiroUser;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.AlgorUtil;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
@Service
public class ZzctUserServiceImpl extends ServiceImpl<UserDao, UserEntity> implements IZzctUserService {

    Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RoleDao roleDao;
    @Autowired
    private DataScopeDao dataScopeDao;

    @Autowired
    private IQyWechatFegin qyWechatFegin;
    @Autowired
    private DeptDao deptDao;
    @Autowired
    private IZzctSysMsgTemplateService zzctSysMsgTemplateService;

    @Autowired
    private IExUserFegin exUserFegin;

    public RestResponse getRestResponse(String vild, String tel) {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("mobile", tel);
            params.put("code", vild); // 模板变量

            params.put("template_code", "97211"); // 模板code
            CacheUtil.putEx(BaseConstants.TEL_SMS_HEADER + tel, vild, 3000l);
            zzctSysMsgTemplateService.send(params);
            //CacheUtil.putEx(BaseConstants.TEL_SMS_HEADER + tel, vild, 3000l);
            return RestResponse.success().setMessage("短信已发送至手机号：" + tel + ",请注意查收");
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure("发送短信失败！").code(406);
        }
    }

}
