package cn.uone.crm.controller;


import cn.uone.application.enumerate.expense.ExpenseProjectTypeEnum;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.bean.entity.crm.ExpenseProjectEntity;
import cn.uone.crm.service.ISysCompanyService;
import cn.uone.crm.service.IExpenseProjectService;
import cn.uone.fegin.bus.IResProjectFegin;
import cn.uone.fegin.bus.IResSourceFegin;
import cn.uone.fegin.crm.IExpenseProjectFegin;
import cn.uone.web.base.BaseController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@RestController
@RequestMapping("/t-expense-project-entity")
public class ExpenseProjectController extends BaseController implements IExpenseProjectFegin {

    @Autowired
    IExpenseProjectService expenseProjectService;
    @Autowired
    ISysCompanyService sysCompanyService;
    @Autowired
    IResProjectFegin resProjectFegin;
    @Autowired
    IResSourceFegin resSourceFegin;

    @Override
    @RequestMapping("/getById")
    public ExpenseProjectEntity getById(String id) {
        return expenseProjectService.getById(id);
    }

    @Override
    @RequestMapping("/getByCompanyName")
    public ExpenseProjectEntity getByCompanyName(String companyName) {
        QueryWrapper query = new QueryWrapper();
        query.eq("company_name",companyName);
        return expenseProjectService.getOne(query);
    }

    @Override
    @RequestMapping("/saveOrUpdate")
    public boolean saveOrUpdate(ExpenseProjectEntity entity) {
        return expenseProjectService.saveOrUpdate(entity);
    }

    @Override
    @RequestMapping("/isOverSourceNum")
    public boolean isOverSourceNum(@RequestParam("projectId") String projectId, @RequestParam("addNum")int addNum) {
        ResProjectEntity projectEntity = resProjectFegin.getById(projectId);
        SysCompanyEntity companyEntity = sysCompanyService.getById(projectEntity.getCompanyId());
        if(companyEntity == null){
            return false;
        }
        String expenseProjectId = companyEntity.getTopId();
        ExpenseProjectEntity expenseProject = expenseProjectService.getById(expenseProjectId);
        if(!ExpenseProjectTypeEnum.SAAS.getValue().equals(expenseProject.getProjectType())){
            return false;
        }
        QueryWrapper query = new QueryWrapper();
        query.eq("top_id",expenseProject.getId());
        List<SysCompanyEntity> companyList = sysCompanyService.list(query);
        List<String> companyIds = companyList.stream()
                .map(SysCompanyEntity::getId)
                .collect(Collectors.toList());
        int sourceNum = resSourceFegin.getCountByCompanyIds(companyIds);
        return sourceNum + addNum > expenseProject.getSourceNum();
    }

    @Override
    @RequestMapping("/getByUserId")
    public ExpenseProjectEntity getByUserId(@RequestParam("userId") String userId) {
        return expenseProjectService.getByUserId(userId);
    }
}
