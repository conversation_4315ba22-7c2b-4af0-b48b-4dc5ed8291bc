package cn.uone.crm.controller;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.RenterType;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.cache.util.CacheUtil;
import cn.uone.crm.service.IRenterService;
import cn.uone.crm.service.ISysMsgTemplateService;
import cn.uone.crm.service.IUserService;
import cn.uone.crm.util.AesCbcUtil;
import cn.uone.fegin.bus.IDevPropertyDeviceFegin;
import cn.uone.fegin.bus.ISysMsgFegin;
import cn.uone.fegin.tpi.IWechatFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.JWTUtil;
import cn.uone.shiro.util.ShiroUser;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.AlgorUtil;
import cn.uone.util.CodeUtil;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.PassWordCreateUtil;
import cn.uone.web.util.UoneHeaderUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;


@RestController
@RequestMapping("/custom/sec")
public class CustomSecuityController {

    private Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IRenterService renterService;

    @Autowired
    private IWechatFegin wechatFegin;

    @Autowired
    private IUserService userService;

    @Autowired
    private IDevPropertyDeviceFegin devPropertyDeviceFegin;
    @Autowired
    private ISysMsgTemplateService sysMsgTemplateService;


    @ApiOperation("获取OPERID")
    @RequestMapping(value = "/getOpenIdByCode", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse getOpenIdByCode(@RequestParam(value="pcode",required = false) String pcode,@RequestParam("code") String code) {
        String openId = wechatFegin.getOpenIdByCode(pcode,code).getStr("openid");
        return RestResponse.success().setData(openId);
    }

    @ApiOperation("判断当前登录用户是否已认证")
    @UonePermissions(LoginType.CUSTOM)
    @RequestMapping(value = "/isVerify", method = RequestMethod.GET)
    public RestResponse isVerify() throws Exception {
        RenterEntity renter = renterService.getRenterById(UoneSysUser.id());
        if (null == renter) {
            return RestResponse.failure("请重新绑定").code(1001);
        }
        return RestResponse.success().setData(renter.getIsVerify());
    }

    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @UonePermissions()
    @ApiOperation("前台用户登录")
    public RestResponse login(@RequestParam("username") String username,
                              @RequestParam(value = "password", required = false) String password,
                              @RequestParam(value = "code", required = false) String code,
                              @RequestParam(value = "qysCompanyId", required = false) String qysCompanyId) {
        if (StrUtil.isBlank(username)) {
            return RestResponse.failure("请输入用户名").code(406);
        }

        if (StrUtil.isBlank(password) && StrUtil.isBlank(code)) {
            return RestResponse.failure("请使用密码登录或使用短信验证码登录").code(406);
        }

        RenterEntity renter = renterService.getRenterByTelAndType(username, RenterType.COMMON.getValue());

        if (StrUtil.isNotBlank(code)) { //短信验证码登录
            String vildCode = (String) CacheUtil.get(BaseConstants.TEL_SMS_HEADER + username);
            if (!code.equals(vildCode)) {
                return RestResponse.failure("短信验证码错误或已过期（5分钟内）！").code(406);
            }
            if (renter == null) {
                password = PassWordCreateUtil.createPassWord(6);
                log.info("账号：" + username + ",密码：" + password);
                // 创建会员
                renter = new RenterEntity();
                renter.setName(username)
                        .setUsername(username)
                        .setTel(username)
                        .setType(RenterType.COMMON.getValue())
                        .setSalt(AlgorUtil.getSalt())
                        .setPassword(AlgorUtil.entryptPassword(password, renter.getSalt()));
                renterService.save(renter);
                try {
                    sysMsgTemplateService.sendCustomerRegInfo(renter.getTel(), renter.getName(), renter.getUsername(), password, renter.getType(),qysCompanyId);
                } catch (Exception e) {
                    log.error(username + " 注册信息短信发送失败");
                }
            }
        } else { //密码登录
            if (null == renter) {
                return RestResponse.failure("用户未注册").code(406);
            }
            password = AlgorUtil.entryptPassword(password, renter.getSalt());
            if (!password.equals(renter.getPassword())) {
                return RestResponse.failure("密码错误").code(406);
            }
        }

        String token = JWTUtil.createToken(username, getShiroUser(renter));
        Map<String, String> map = Maps.newHashMap();
        map.put(BaseConstants.HTTP_HEADER_NAME, token);
        return RestResponse.success().setData(map).code(200);
    }

    @RequestMapping(value = "/register", method = RequestMethod.POST)
    @UonePermissions()
    @ApiOperation("前台用户注册")
    public RestResponse register(@RequestBody RenterEntity renter,@RequestParam(value = "code") String code,@RequestParam(value = "codeKey") String codeKey) {
        if (StrUtil.isBlank(code)) {
            return RestResponse.failure("请输入图片验证码").code(406);
        }
        if (!CacheUtil.validCaptcha(codeKey, code)) {
            return RestResponse.failure("请输入正确的图片验证码").code(406);
        }
        // 验证手机号是否已注册
        RenterEntity entity = renterService.getRenterByTelAndType(renter.getUsername(), RenterType.COMMON.getValue());
        if(entity != null){
            return RestResponse.failure("该手机号已注册").code(406);
        }
        // 创建会员

        renter.setType(RenterType.COMMON.getValue())
                .setSalt(AlgorUtil.getSalt())
                .setPassword(AlgorUtil.entryptPassword(renter.getPassword(), renter.getSalt()));
        renterService.save(renter);
        String token = JWTUtil.createToken(renter.getUsername(), getShiroUser(renter));
        Map<String, String> map = Maps.newHashMap();
        map.put(BaseConstants.HTTP_HEADER_NAME, token);
        return RestResponse.success("注册成功").setData(map).code(200);
    }

    @RequestMapping(value = "/company/login", method = RequestMethod.POST)
    @UonePermissions()
    @ApiOperation("机构用户登录")
    public RestResponse companyLogin(@RequestParam("username") String username,
                                     @RequestParam(value = "password") String password, @RequestParam(value = "code") String code, @RequestParam("codeKey") String codeKey) {
        if (StrUtil.isBlank(code)) {
            return RestResponse.failure("请输入图片验证码").code(406);
        }
        //验证图片验证码
        if (!CacheUtil.validCaptcha(codeKey, code)) {
            return RestResponse.failure("请输入正确的图片验证码").code(406);
        }

        RenterEntity renter = renterService.getRenterByUsername(username, RenterType.ENTERPRISE.getValue());
        if (renter == null) {
            return RestResponse.failure("用户名或密码错误！").code(406);
        }
        //密码加密验证
        password = AlgorUtil.entryptPassword(password, renter.getSalt());
        if (!renter.getPassword().equals(password)) {
            return RestResponse.failure("用户名或密码错误").code(406);
        } else {
            String token = JWTUtil.createToken(username, getShiroUser(renter));
            Map<String, String> map = Maps.newHashMap();
            map.put(BaseConstants.HTTP_HEADER_NAME, token);
            return RestResponse.success().setData(map).code(200);
        }
    }


    @RequestMapping(value = "/sendSmsCodeByEnter", method = RequestMethod.POST)
    @UonePermissions
    public RestResponse sendSmsCodeByEnter(String loginName,String code, String codeKey){

        if (StrUtil.isBlank(code)) {
            return RestResponse.failure("请输入图片验证码").code(406);
        }
        if (!CacheUtil.validCaptcha(codeKey, code)) {
            return RestResponse.failure("请输入正确的图片验证码").code(406);
        }

        String vild = CodeUtil.getRandom(6);

        RenterEntity user =  renterService.getRenterByUsername(loginName,RenterType.ENTERPRISE.getValue());
        if(ObjectUtil.isNull(user)){
            return RestResponse.failure("未查找到用户名为："+loginName+"的机构会员用户");
        }
        if(StrUtil.isBlank(user.getTel())){
            return RestResponse.failure(loginName+"用户未设置手机号");
        }
        String tel = user.getTel();
        log.info("手机：" +tel + "验证码为：" + vild);
        //发送手机短信并保存验证码到缓存
        UserEntity userEntity = userService.getById(user.getCreateBy());
        return userService.getRestResponse(vild, tel, userEntity.getCompanyId());
    }

    @RequestMapping(value = "/forgetPwd", method = RequestMethod.POST)
    @UonePermissions(LoginType.CUSTOM)
    @ApiOperation("用户忘记密码")
    public RestResponse forgetPassword(String tel, String smsCode, String newpass, String checkpass) throws Exception {
        if (StrUtil.isBlank(tel)) {
            return RestResponse.failure("请输入手机号").code(406);
        }
        String vildCode = (String) CacheUtil.get(BaseConstants.TEL_SMS_HEADER + tel);
        if (!smsCode.equals(vildCode)) {
            return RestResponse.failure("短信验证码错误或已过期（5分钟内）！").code(406);
        }
        if (StrUtil.isBlank(newpass) || newpass.length() < 6) {
            return RestResponse.failure("请输入新密码（长度大于6个字符）").code(406);
        }
        if (!newpass.equals(checkpass)) {
            return RestResponse.failure("两次密码输入不一致，请重新确认").code(406);
        }
        // 获取租客用户信息
        RenterEntity renter = renterService.getRenterByTel(tel);
        if(renter == null){
            return RestResponse.failure("请手机号还未注册").code(406);
        }
        renter.setPassword(AlgorUtil.entryptPassword(newpass, renter.getSalt()));
        renter.updateById();
        return RestResponse.success("修改密码成功");
    }

    @RequestMapping(value = "/company/update", method = RequestMethod.POST)
    @UonePermissions(LoginType.CUSTOM)
    @ApiOperation("机构用户修改密码")
    public RestResponse updatePassword(String old, String newpass, String checkpass) throws Exception {
        if (StrUtil.isBlank(old)) {
            return RestResponse.failure("请输入旧密码");
        }
        RenterEntity renter = renterService.getRenterById(UoneSysUser.id());

        String entpass = AlgorUtil.entryptPassword(old, renter.getSalt());
        if (!entpass.equals(renter.getPassword())) {
            return RestResponse.failure("旧密码不正确");
        }

        if (StrUtil.isBlank(newpass) || newpass.length() < 6) {
            return RestResponse.failure("请输入新密码（长度大于6个字符）");
        }
        if (!newpass.equals(checkpass)) {
            return RestResponse.failure("两次密码输入不一致，请重新确认");
        }
        renter.setPassword(AlgorUtil.entryptPassword(newpass, renter.getSalt()));
        renter.updateById();
        return RestResponse.success("修改密码成功");
    }
    @RequestMapping(value = "/updateName", method = RequestMethod.POST)
    @UonePermissions(LoginType.CUSTOM)
    @ApiOperation("机构用户修改姓名昵称")
    public RestResponse updateName(String name) throws Exception {
        if (StrUtil.isBlank(name)) {
            return RestResponse.failure("请输入姓名");
        }
        RenterEntity renter = renterService.getRenterById(UoneSysUser.id());
        renter.setName(name);
        renter.setNickName(name);
        renter.updateById();
        return RestResponse.success("名称修改成功");
    }


    @RequestMapping(value = "/sendSmsCode", method = RequestMethod.POST)
    @UonePermissions()
    @ApiOperation("发送手机验证码")
    public RestResponse sendSmsCode(String tel, String code, String codeKey, @RequestParam(required = false) String qysCompanyId) {

        if (StrUtil.isBlank(tel)) {
            return RestResponse.failure("请输入手机号").code(406);
        }
        if (StrUtil.isBlank(code)) {
            return RestResponse.failure("请输入图片验证码").code(406);
        }
        if (!CacheUtil.validCaptcha(codeKey, code)) {
            return RestResponse.failure("请输入正确的图片验证码").code(406);
        }

        String vild = CodeUtil.getRandom(6);

        Console.log("手机：{},验证码为：{},qysCompanyId:{}",tel,vild,qysCompanyId);
        //发送手机短信并保存验证码到缓存
        return userService.getRestResponseByQys(vild, tel, qysCompanyId);

    }

    @RequestMapping(value = "/sendTelCode", method = RequestMethod.POST)
    @UonePermissions()
    @ApiOperation("发送手机验证码")
    public RestResponse sendTelCode(String tel, String projectId) {

        if (StrUtil.isBlank(tel)) {
            return RestResponse.failure("请输入手机号").code(406);
        }

        String vild = CodeUtil.getRandom(6);

        log.info("手机：" + tel + "验证码为：" + vild);
        //发送手机短信并保存验证码到缓存
        return userService.getRestResponseByProjectId(vild, tel, projectId);

    }

    @GetMapping("/showUser")
    public RestResponse showUser() {
        ShiroUser shiroUser = UoneSysUser.ShiroUser();
        if(shiroUser != null && StringUtils.isNotBlank(shiroUser.getId())){
            try {
                RenterEntity renter = renterService.getRenterById(shiroUser.getId());
                if(renter != null){
                    shiroUser.setNickName(renter.getName());
                }
            } catch (BusinessException e) {
                e.printStackTrace();
            }
        }
        return RestResponse.success().setData(UoneSysUser.ShiroUser());
    }

    @GetMapping("/renter")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse renter() throws Exception {
        return RestResponse.success().setData(renterService.getRenterById(UoneSysUser.id()));
    }

    @RequestMapping(value = "/logout", method = RequestMethod.GET)
    public RestResponse logout() {
        String token = UoneHeaderUtil.getToken();
        String loginType = JWTUtil.getLoginType(token) + ":";
        CacheUtil.delete(JWTUtil.REDIS_NAMESPACE + loginType + token);
        return RestResponse.success();
    }

    @ApiOperation("获取微信用户手机号码")
    @RequestMapping(value = "/getPhoneNumber", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse getPhoneNumber(@RequestParam("code") String code,@RequestParam("encryptedData")String encryptedData,@RequestParam("iv")String iv) {
        String appId ="wx9bbdaa4812b5a613";
        String secret = "6333a96a604118e4f22b8abbac1f397c";
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid="+appId+"&secret="+secret+"&js_code="+code+"&grant_type=authorization_code";
        RestTemplate restTemplate = new RestTemplate();
        String str = restTemplate.getForObject(url, String.class);
        // 解析相应内容（转换成json对象）
        JSONObject json = new JSONObject(str);
        // 获取会话密钥（session_key）
        String session_key = json.get("session_key").toString();
        // 用户的唯一标识（openid）
        String openid = (String) json.get("openid");
        Map map = new HashMap();
        //对encryptedData加密数据进行AES解密
        try {
            String result = AesCbcUtil.decrypt(encryptedData, session_key, iv, "UTF-8");
            if (null != result && result.length() > 0) {
                map.put("status", 1);
                map.put("msg", "解密成功");

                JSONObject userInfoJSON = new JSONObject(result);
                Map userInfo = new HashMap();
                userInfo.put("openId", openid);
                userInfo.put("watermark", userInfoJSON.get("watermark"));
                userInfo.put("phoneNumber", userInfoJSON.get("phoneNumber"));
                userInfo.put("purePhoneNumber", userInfoJSON.get("purePhoneNumber"));
                userInfo.put("countryCode", userInfoJSON.get("countryCode"));
                userInfo.put("nickName", userInfoJSON.get("nickName"));
                userInfo.put("gender", userInfoJSON.get("gender"));
                userInfo.put("city", userInfoJSON.get("city"));
                userInfo.put("province", userInfoJSON.get("province"));
                userInfo.put("country", userInfoJSON.get("country"));
                userInfo.put("avatarUrl", userInfoJSON.get("avatarUrl"));
                // 解密unionId & openId;

                userInfo.put("unionId", userInfoJSON.get("unionId"));
                map.put("userInfo", userInfo);
            } else {
                map.put("status", 0);
                map.put("msg", "解密失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return RestResponse.success().setData(map);
    }

    @ApiOperation("小程序授权登录")
    @RequestMapping(value = "/wechatLogin", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse wechatLogin(@RequestParam(value="pcode",required = false) String pcode,@RequestParam("code") String code,@RequestParam(required = false) String unionId) {
        String openId = wechatFegin.getOpenIdByCode(pcode,code).getStr("openid");
        log.info("开始获取openid");
        if (StrUtil.isBlank(openId)) {
            return RestResponse.failure("小程序登录授权失败");
        }

        log.info("获取到,openid:"+openId);
        RenterEntity renter = renterService.getOne(new QueryWrapper<RenterEntity>().eq("openid", openId));
        log.info("查询租客信息,openid:"+openId);
        if (null == renter) {
            //未查找到用户，让用户去绑定手机号
            return RestResponse.success().code(406).setData(openId);
        }
        if(StrUtil.isNotBlank(unionId)&&StrUtil.isBlank(renter.getUnionId())){
            renter.setUnionId(unionId);
            renter.updateById();
        }
        ShiroUser user = getShiroUser(renter);
        String token = JWTUtil.createToken(renter.getUsername(), user);
        Map<String, Object> map = Maps.newHashMap();
        map.put(BaseConstants.HTTP_HEADER_NAME, token);
        map.put("userInfo", user);
        return RestResponse.success().setData(map).code(200);
    }

    @ApiOperation("微信公众号授权登录")
    @RequestMapping(value = "/weixinLogin", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse weixinLogin(@RequestParam("code") String code,@RequestParam(required = false) String unionId) {
        String openId = wechatFegin.getOpenIdByWeixinCode(code).getStr("openid");
        log.info("开始获取openid");
        if (StrUtil.isBlank(openId)) {
            return RestResponse.failure("微信公众号登录授权失败");
        }

        log.info("获取到,openid:"+openId);
        RenterEntity renter = renterService.getOne(new QueryWrapper<RenterEntity>().eq("openid", openId));
        log.info("查询租客信息,openid:"+openId);
        if (null == renter) {
            //未查找到用户，让用户去绑定手机号
            return RestResponse.success().code(406).setData(openId);
        }
        if(StrUtil.isNotBlank(unionId)&&StrUtil.isBlank(renter.getUnionId())){
            renter.setUnionId(unionId);
            renter.updateById();
        }
        ShiroUser user = getShiroUser(renter);
        String token = JWTUtil.createToken(renter.getUsername(), user);
        Map<String, Object> map = Maps.newHashMap();
        map.put(BaseConstants.HTTP_HEADER_NAME, token);
        map.put("userInfo", user);
        return RestResponse.success().setData(map).code(200);
    }

    @ApiOperation("对用户绑定微信OpenId")
    @RequestMapping(value = "/forward", method = RequestMethod.POST)
    @UonePermissions
    public RestResponse forward(@RequestParam("openId") String openId, @RequestParam("tel") String tel, @RequestParam("smsCode") String smsCode,@RequestParam(required = false) String name,@RequestParam(required = false) String unionId,@RequestParam(value = "qysCompanyId", required = false) String qysCompanyId) {

        if (StrUtil.isBlank(tel)) {
            return RestResponse.failure("请输入正确的手机号").code(406);
        }
        /*if (StrUtil.isBlank(smsCode)) {
            return RestResponse.failure("请输入正确的短信验证码").code(406);
        }*/

        String vildCode = (String) CacheUtil.get(BaseConstants.TEL_SMS_HEADER + tel);
        /*if (!smsCode.equals(vildCode)) {
            return RestResponse.failure("短信验证码错误或已过期（5分钟内）！").code(406);
        }*/

        RenterEntity renter = renterService.getRenterByTelAndType(tel, RenterType.COMMON.getValue());
        if (null == renter) {
            String password = PassWordCreateUtil.createPassWord(6);
            if(StrUtil.isBlank(name)){
                name = tel;
            }
            // 创建会员
            renter = new RenterEntity();
            renter.setName(name)
                    .setUsername(tel)
                    .setTel(tel)
                    .setType(RenterType.COMMON.getValue())
                    .setSalt(AlgorUtil.getSalt())
                    .setOpenid(openId)
                    .setUnionId(unionId)
                    .setPassword(AlgorUtil.entryptPassword(password, renter.getSalt()))
                    .setAgreementState(1);
            renterService.save(renter);
            try {
                sysMsgTemplateService.sendCustomerRegInfo(renter.getTel(), renter.getName(), renter.getUsername(), password, renter.getType(),qysCompanyId);
            } catch (Exception e) {
                log.error(tel + " 注册信息短信发送失败");
            }
        } else {
            if (StrUtil.isNotBlank(renter.getOpenid())) {
                return RestResponse.failure("该手机号已有绑定微信！").code(406);
            } else {
                renter.setOpenid(openId).updateById();
                renter.setAgreementState(1).updateById();
            }
        }

        String token = JWTUtil.createToken(renter.getUsername(), getShiroUser(renter));
        Map<String, String> map = Maps.newHashMap();
        map.put(BaseConstants.HTTP_HEADER_NAME, token);
        return RestResponse.success().setData(map).code(200);
    }

    private ShiroUser getShiroUser(RenterEntity renter) {
        ShiroUser suser = new ShiroUser();
        suser.setId(renter.getId());
        suser.setLoginName(renter.getUsername());
        if(StrUtil.isNotBlank(renter.getName())){
            suser.setNickName(renter.getName());
        }else{
            suser.setNickName(renter.getUsername());
        }
        suser.setTel(renter.getTel());
        suser.setLoginType(LoginType.CUSTOM);
        suser.setLock(false);
        suser.setUnionId(renter.getUnionId());
        suser.setExternalUserid(renter.getExternalUserid());
        return suser;
    }

    @ApiOperation("获取项目列表")
    @RequestMapping(value = "/getProjects", method = RequestMethod.GET)
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse getProjects() {
        return RestResponse.success().setData(userService.getProjects(false));
    }

    @ApiOperation("条件获取分区列表")
    @RequestMapping(value = "/getAreasByFilter", method = RequestMethod.GET)
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse getAreasByFilter(String projectId,
                                         @RequestParam(value = "propertyNature", required = false, defaultValue = "false") String propertyNature) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("projectId", projectId);
        if (StringUtils.isNotBlank(propertyNature)) {
            List<String> propertyNatures = Arrays.asList(propertyNature.split(","));
            map.put("propertyNature", propertyNatures);
        }
        return RestResponse.success().setData(userService.getAreas(map, false));
    }

    @RequestMapping(value = "/updataTel", method = RequestMethod.POST)
    @UonePermissions(LoginType.CUSTOM)
    @ApiOperation("小程序用户修改手机号")
    @UoneLog("小程序用户修改手机号")
    @Transactional
    public RestResponse updataTel(String tel, String smsCode) throws Exception {
        if (StrUtil.isBlank(tel)) {
            return RestResponse.failure("请输入手机号");
        }
        String vildCode = (String) CacheUtil.get(BaseConstants.TEL_SMS_HEADER + tel);
        if (!smsCode.equals(vildCode)) {
            return RestResponse.failure("短信验证码错误或已过期（5分钟内）！").code(406);
        }
        String regexMobile = "^[1]([3-9])[0-9]\\d{8}$";
        if (!Pattern.matches(regexMobile, tel)) {
            return RestResponse.failure("手机号码格式有误");
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("tel", tel);
        wrapper.eq("type", RenterType.COMMON.getValue());
        wrapper.ne("id", UoneSysUser.id());
        RenterEntity renterEntity = renterService.getOne(wrapper);
        if (ObjectUtil.isNotNull(renterEntity)) {
            return RestResponse.failure("该手机号码已注册，无法绑定");
        }
        RenterEntity renter = renterService.getRenterById(UoneSysUser.id());
        renter.setFadadaCode("");
        if (BaseConstants.BOOLEAN_OF_TRUE.equals(renter.getIsVerify())) {
            renter.setIsVerify("0");
            renter.setSerialNo("");
        }
        String oldTel = renter.getTel();
        renter.setTel(tel).setUsername(tel).updateById();
        //对新旧号码进行冻结和授权
        RestResponse response = devPropertyDeviceFegin.changeTelToXt(renter.getId(), oldTel, tel);
        if (!response.getSuccess()) {
            return response;
        }
        //手机更新后把缓存里对应的用户信息改变
        ShiroUser shiroUser = getShiroUser(renter);
        JWTUtil.putUserInfo(shiroUser);
        return RestResponse.success().setData(shiroUser);
    }

    @RequestMapping(value = "/outBind", method = RequestMethod.POST)
    @UonePermissions(LoginType.CUSTOM)
    @UoneLog("微信解绑")
    public RestResponse outBind(){
        renterService.setOpenidNULL(UoneSysUser.id());
        String token = UoneHeaderUtil.getToken();
        String loginType = JWTUtil.getLoginType(token) + ":";
        CacheUtil.delete(JWTUtil.REDIS_NAMESPACE + loginType + token);

        return RestResponse.success("解绑成功");
    }

    /**
     * 设置unionId
     * @param encryptedData
     * @param iv
     * @param code
     * @return
     * @throws BusinessException
     */
    @RequestMapping( "/encryptedData")
    @UonePermissions
    public RestResponse encryptedData(String pcode,String encryptedData,String iv,String code) throws BusinessException {
        String sessionKey = wechatFegin.getOpenIdByCode(pcode,code).getStr("session_key");
        String unionId = wechatFegin.getEncryptedData(encryptedData,sessionKey,iv);
        //renterService.getRenterById(UoneSysUser.id()).setUnionId(unionId).updateById();
        return RestResponse.success().setData(unionId);
    }

    /**
     * 微信公众号获取用户信息
     * @param openId
     * @return
     * @throws BusinessException
     */
    @RequestMapping( "/getUserInfoByWeixin")
    @UonePermissions
    public RestResponse getUserInfoByWeixin(String openId) {
        JSONObject json = wechatFegin.getUserInfo(openId);
        return RestResponse.success().setData(json);
    }
}
