package cn.uone.crm;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages={"cn.uone.*"})
@EnableFeignClients(basePackages = {"cn.uone.*"})
@EnableDiscoveryClient
@MapperScan({"cn.uone.crm.dao","cn.uone.crm.*.dao"})
public class CrmApplication {
    public static void main(String[] args) {
        System.setProperty("rocketmq.client.log.loadconfig","false");
        SpringApplication.run(CrmApplication.class, args);
    }
}
