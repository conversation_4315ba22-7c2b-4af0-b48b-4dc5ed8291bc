package cn.uone.crm.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.crm.RgDeptEntity;
import cn.uone.crm.service.IRgDeptService;
import cn.uone.crm.service.IUserService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 部门表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-7
 */
@RestController
@RequestMapping("/rg/dept")
public class RgDeptController extends BaseController {

    @Autowired
    private IRgDeptService service;
    @Autowired
    private IUserService userService;

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation(value = "获取部门列表")
    @UonePermissions
    public RestResponse list(RgDeptEntity entity) {
        QueryWrapper<RgDeptEntity> wrapper = new QueryWrapper<>();

        wrapper.orderByDesc("create_date");
        if (StrUtil.isNotBlank(entity.getDeptName())) {
            wrapper.like("dept_name", "%" + entity.getDeptName() + "%");
        }
        if (StrUtil.isNotBlank(entity.getStatus())) {
            wrapper.like("status", "%" + entity.getStatus() + "%");
        }
        List<RgDeptEntity> list = service.list(wrapper);
        return RestResponse.success().setData(list);
    }

    @GetMapping("/page")
    public RestResponse page(Page page, RgDeptEntity entity) {
        QueryWrapper<RgDeptEntity> wrapper = new QueryWrapper<>();

        wrapper.orderByDesc("create_date");
        if (StrUtil.isNotBlank(entity.getDeptName())) {
            wrapper.like("dept_name", "%" + entity.getDeptName() + "%");
        }
        if (StrUtil.isNotBlank(entity.getStatus())) {
            wrapper.like("status", "%" + entity.getStatus() + "%");
        }
        IPage<RgDeptEntity> p = service.page(page, wrapper);
        return RestResponse.success().setData(p);
    }

    /**
     * 获取信息
     *
     * @param keyword
     * @param flag
     * @return 资产信息列表 分页
     */
    @GetMapping("/keyword")
    public RestResponse keywordList(Page page, @Param("keyword") String keyword, @Param("flag") String flag) {
        QueryWrapper<RgDeptEntity> wrapper = new QueryWrapper<>();


        if (StrUtil.isNotBlank(keyword)) {
            wrapper.like("dept_name", "%" + keyword + "%");
        }
        List<HashMap<String, String>> list = new ArrayList<>();
        IPage<RgDeptEntity> p = service.page(page, wrapper);
        for (RgDeptEntity entity : p.getRecords()) {
            HashMap<String, String> map = new HashMap<>();
            map.put("id", entity.getId());
            map.put("name", entity.getDeptName());
            list.add(map);
        }

        return RestResponse.success().setData(list);
    }

    /**
     * 获取部门列表信息
     *
     * @param keyword
     * @param flag
     * @return 部门列表 不分页
     */
    @GetMapping("/getDeptList")
    public RestResponse getDeptList(Page page, @Param("keyword") String keyword, @Param("flag") String flag) {
        QueryWrapper<RgDeptEntity> wrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(keyword)) {
            wrapper.like("dept_name", "%" + keyword + "%");
        }
        List<HashMap<String, String>> list = new ArrayList<>();
        List<RgDeptEntity> deptList = service.list(wrapper);
        for (RgDeptEntity entity : deptList) {
            HashMap<String, String> map = new HashMap<>();
            map.put("value", entity.getId());
            map.put("name", entity.getDeptName());
            list.add(map);
        }
        return RestResponse.success().setData(list);
    }

    @PostMapping("/save")
    public RestResponse save(RgDeptEntity entity) {
        entity.setStatus("0");
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    //
    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids) {
        service.removeByIds(ids);
        return RestResponse.success();
    }

    //获取所有用户
    @GetMapping("getAllUser")
    public RestResponse getAllUser() {
        return RestResponse.success().setData(userService.getAllUser());
    }

    //获取所有部门
    @GetMapping("getAllDept")
    public RestResponse getAllDept(String id) {
        return RestResponse.success().setData(service.getAllDept(id));
    }

    @GetMapping("deptTree")
    public RestResponse deptTree(RgDeptEntity dept) {
        return RestResponse.success().setData(service.selectDeptTreeList(dept));
    }
}
