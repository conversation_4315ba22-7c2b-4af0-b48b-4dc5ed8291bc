package cn.uone.crm.controller;


import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.bean.entity.crm.ExpenseProjectEntity;
import cn.uone.bean.entity.crm.QiyuesuoAuthEntity;
import cn.uone.crm.service.ISysCompanyService;
import cn.uone.crm.service.IExpenseProjectService;
import cn.uone.crm.service.IQiyuesuoAuthService;
import cn.uone.fegin.bus.IResProjectFegin;
import cn.uone.fegin.crm.IQiyuesuoAuthFegin;
import cn.uone.fegin.tpi.IQiyuesuoFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-31
 */
@RestController
@RequestMapping("/t-qiyuesuo-auth-entity")
public class QiyuesuoAuthController extends BaseController implements IQiyuesuoAuthFegin {
    @Autowired
    IQiyuesuoFegin qiyuesuoFegin;
    @Autowired
    IResProjectFegin resProjectFegin;
    @Autowired
    IExpenseProjectService expenseProjectService;
    @Autowired
    ISysCompanyService sysCompanyService;
    @Autowired
    IQiyuesuoAuthService qiyuesuoAuthService;

    @Override
    @RequestMapping("/getByCompanyId")
    public QiyuesuoAuthEntity getByCompanyId(@RequestParam("companyId") String companyId){
        QueryWrapper query = new QueryWrapper();
        query.eq("qys_company_id",companyId);
        QiyuesuoAuthEntity qiyuesuoAuthEntity = qiyuesuoAuthService.getOne(query);
        return qiyuesuoAuthEntity;
    }

    @Override
    @RequestMapping("/getByExpenseProjectId")
    public QiyuesuoAuthEntity getByExpenseProjectId(@RequestParam("expenseProjectId") String expenseProjectId){
        QueryWrapper query = new QueryWrapper();
        query.eq("expense_project_id",expenseProjectId);
        QiyuesuoAuthEntity qiyuesuoAuthEntity = qiyuesuoAuthService.getOne(query);
        return qiyuesuoAuthEntity;
    }


    /**
     * 企业认证接口回调
     * @param signature
     * @param timestamp
     * @param content
     * @return
     */
    @PostMapping("/saasAuthCallback")
    @UonePermissions
    public String saasAuthCallback(String signature,String timestamp,String content){
        Console.log("企业认证返回报文：signature：{}，timestamp：{}，content：{}",signature,timestamp,content);
        Map result = new HashMap();
        try {
            String checkSign = qiyuesuoFegin.toMd5(timestamp);
            if(!signature.equals(checkSign)){
                Console.log("验签失败");
                return "faile";
            }
            String derypt = qiyuesuoFegin.aesDerypt(content);
            Console.log("企业认证返回报文：{}",derypt);
            //....业务逻辑编写
            JSONObject corpAuthDetail = JSONUtil.parseObj(derypt);
            String companyId = corpAuthDetail.getStr("companyId");
            String companyName = corpAuthDetail.getStr("companyName");
            Integer status = corpAuthDetail.getInt("status");
            Integer actionEvent = corpAuthDetail.getInt("actionEvent");
            QueryWrapper query = new QueryWrapper();
            query.eq("company_name",companyName);
            ExpenseProjectEntity expenseProjectEntity = expenseProjectService.getOne(query);
            if(expenseProjectEntity==null){
                Console.log("企业{}不存在",companyName);
                return "fail";
            }
            QueryWrapper query1 = new QueryWrapper();
            query1.eq("expense_project_id",expenseProjectEntity.getId());
            QiyuesuoAuthEntity qiyuesuoAuthEntity = qiyuesuoAuthService.getOne(query1);
            if(qiyuesuoAuthEntity==null){
                Console.log("企业{}丢失认证信息",companyName);
                return "fail";
            }
            String remark = null;
            switch (actionEvent){
                case 0:
                    remark = actionEvent+":提交基本信息";
                    break;
                case 1:
                    remark = actionEvent+":基本信息审核通过";
                    break;
                case 2:
                    remark = actionEvent+":基础信息审核失败";
                    break;
                case 4:
                    remark = actionEvent+":授权书审核失败";
                    break;
                case 7:
                    remark = actionEvent+":认证成功";
                    break;
                default:
                    break;
            }
            if(StrUtil.isNotBlank(companyId)){
                qiyuesuoAuthEntity.setQysCompanyId(companyId);
            }
            String qysAuthState = status==0?"CERTIFYING":status==1?"AUTH_SUCCESS":"UNREGISTERED";
            qiyuesuoAuthEntity.setQysAuthState(qysAuthState);
            qiyuesuoAuthEntity.setRemark(remark);
            qiyuesuoAuthService.updateById(qiyuesuoAuthEntity);
            return "success";
        }catch (Exception e) {
            return "fail";
        }
    }

    /**
     * 企业授权接口回调
     * @param signature
     * @param timestamp
     * @param content
     * @return
     */
    @PostMapping("/saasAccessCallback")
    @UonePermissions
    public String saasAccessCallback(String signature,String timestamp,String content){
        Console.log("企业授权返回报文：signature：{}，timestamp：{}，content：{}",signature,timestamp,content);
        Map result = new HashMap();
        try {
            String checkSign = qiyuesuoFegin.toMd5(timestamp);
            if(!signature.equals(checkSign)){
                Console.log("验签失败");
                return "faile";
            }
            String derypt = qiyuesuoFegin.aesDerypt(content);
            Console.log("企业授权返回报文：{}",derypt);
            //....业务逻辑编写
            JSONObject resJson = JSONUtil.parseObj(derypt);
            String companyId = resJson.getStr("companyId");
            String companyName = resJson.getStr("companyName");
            String accessToken = resJson.getStr("accessToken");
            String accessSecret = resJson.getStr("accessSecret");
            QueryWrapper query = new QueryWrapper();
            query.eq("qys_company_id",companyId);
            QiyuesuoAuthEntity qiyuesuoAuthEntity = qiyuesuoAuthService.getOne(query);
            if(qiyuesuoAuthEntity==null){
                Console.log("企业{}丢失认证信息",companyName);
                return "fail";
            }
            qiyuesuoAuthEntity.setQysAuthState("ACCESS_SUCCESS");
            qiyuesuoAuthEntity.setQysAccessToken(accessToken);
            qiyuesuoAuthEntity.setQysAccessSecret(accessSecret);
            qiyuesuoAuthService.updateById(qiyuesuoAuthEntity);
            return "success";
        }catch (Exception e) {
            return "fail";
        }
    }

    /**
     * 获取企业契约锁认证信息
     * @return
     */
    @GetMapping("/getQiyuesuoAuth")
    public RestResponse getQiyuesuoAuth(){
        String projectId = UoneHeaderUtil.getProjectId();
        ResProjectEntity resProject = resProjectFegin.getById(projectId);
        SysCompanyEntity company = sysCompanyService.getById(resProject.getCompanyId());
        QueryWrapper query = new QueryWrapper();
        query.eq("expense_project_id",company.getTopId());
        QiyuesuoAuthEntity qiyuesuoAuthEntity = qiyuesuoAuthService.getOne(query);
        return RestResponse.success().setData(qiyuesuoAuthEntity);
    }

    /**
     * 获取认证链接
     * @param name
     * @param contact
     * @param contactType
     * @param companyName
     * @return
     */
    @PostMapping("/getSaasCompanyAuthUrl")
    public RestResponse getSaasCompanyAuthUrl(@RequestParam("name") String name, @RequestParam("contact") String contact, @RequestParam("contactType") String contactType, @RequestParam("companyName") String companyName){
        QueryWrapper query = new QueryWrapper();
        query.eq("company_name",companyName);
        ExpenseProjectEntity projectEntity = expenseProjectService.getOne(query);
        if(projectEntity == null){
            return RestResponse.failure("系统不存在该公司");
        }
        RestResponse res = qiyuesuoFegin.querySaasCompanyDetail(companyName);
        if(!res.getSuccess()){
            return res;
        }
        QueryWrapper query1 = new QueryWrapper();
        query1.eq("expense_project_id",projectEntity.getId());
        QiyuesuoAuthEntity qiyuesuoAuthEntity = qiyuesuoAuthService.getOne(query1);
        if(qiyuesuoAuthEntity == null){
            qiyuesuoAuthEntity = new QiyuesuoAuthEntity();
        }
        qiyuesuoAuthEntity.setQysContactName(name);
        qiyuesuoAuthEntity.setQysContact(contact);
        qiyuesuoAuthEntity.setQysContactType(contactType);
        qiyuesuoAuthEntity.setExpenseProjectId(projectEntity.getId());
        JSONObject data = JSONUtil.parseObj(res.get("data"));
        String status = data.getStr("status");
        if("AUTH_SUCCESS".equals(status)){
            qiyuesuoAuthEntity.setQysAuthState("AUTH_SUCCESS");
            qiyuesuoAuthEntity.setQysCompanyId(data.getStr("id"));
            qiyuesuoAuthService.saveOrUpdate(qiyuesuoAuthEntity);
            return RestResponse.success("该公司已认证").setAny("status","AUTH_SUCCESS");
        }
        RestResponse res1 = qiyuesuoFegin.getSaasCompanyAuthUrl(name,contact,contactType,companyName);
        if(!res.getSuccess()){
            return res;
        }
        qiyuesuoAuthService.saveOrUpdate(qiyuesuoAuthEntity);
        return RestResponse.success().setAny("pageUrl",res1.get("data")).setAny("status","UNREGISTERED");
    }

    /**
     * 获取授权链接
     * @param authId
     * @return
     */
    @PostMapping("/getSaasCompanyAccessUrl")
    public RestResponse getSaasCompanyAccessUrl(@RequestParam("authId") String authId){
        QiyuesuoAuthEntity qiyuesuoAuthEntity = qiyuesuoAuthService.getById(authId);
        if(qiyuesuoAuthEntity == null){
            return RestResponse.failure("该公司认证信息丢失");
        }
        RestResponse res = qiyuesuoFegin.getSaasCompanyAccessUrl(qiyuesuoAuthEntity.getQysCompanyId(),qiyuesuoAuthEntity.getQysContact(),qiyuesuoAuthEntity.getQysContactType());
        if(!res.getSuccess()){
            return res;
        }
        return RestResponse.success().setAny("pageUrl",res.get("data"));
    }


}
