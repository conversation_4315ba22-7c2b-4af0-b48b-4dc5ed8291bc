package cn.uone.crm.service;

import cn.uone.bean.entity.crm.SysMsgTemplateEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * 从漳州城投工程迁移过来
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-29
 * caizhanghe edit 2024-05-22
 */
public interface IZzctSysMsgTemplateService extends IService<SysMsgTemplateEntity> {

    SysMsgTemplateEntity getByCode(String code, Map<String, Object> params);

    void send(Map<String, Object> params) throws Exception;


}
