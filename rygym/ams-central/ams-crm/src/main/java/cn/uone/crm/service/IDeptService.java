package cn.uone.crm.service;

import cn.uone.bean.entity.business.sys.vo.TreeVo;
import cn.uone.bean.entity.crm.DeptEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 工作流部门表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-12
 */
public interface IDeptService extends IService<DeptEntity> {
    List<DeptEntity> getUserDepts(List<String> code);
    List<TreeVo> getDeptTree(String parentCode);
}
