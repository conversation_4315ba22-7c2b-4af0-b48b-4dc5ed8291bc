package cn.uone.crm.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.vo.QyWechatUserVo;
import cn.uone.bean.entity.crm.DeptEntity;
import cn.uone.bean.entity.crm.RoleEntity;
import cn.uone.bean.entity.crm.TagEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.crm.dao.DeptDao;
import cn.uone.crm.dao.RoleDao;
import cn.uone.crm.dao.TagDao;
import cn.uone.crm.dao.UserDao;
import cn.uone.crm.service.IQyWechatService;
import cn.uone.fegin.bus.ISysMsgFegin;
import cn.uone.fegin.tpi.IQyWechatFegin;
import cn.uone.fegin.tpi.IQywxBaseFegin;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.AlgorUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class QyWechatServiceImpl implements IQyWechatService {

    /**
     * 根部门ID
     **/
    @Value("${qyWechat.sourceDeptId}")
    public String SOURCE_DEPT_ID;
    /**
     * 协作部门ID
     **/
    @Value("${qyWechat.sourceXiezuoDeptId}")
    public String SOURCE_XIEZUO_DEPT_ID;



    Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    ISysMsgFegin msgFegin;

    @Autowired
    DeptDao deptDao;

    @Autowired
    TagDao tagDao;

    @Autowired
    UserDao userDao;

    @Autowired
    RoleDao roleDao;

    @Autowired
    IQyWechatFegin wechatFegin;



    /*@Override
    public void sync() {
        //JSONArray depats = wechatFegin.getDepartments(SOURCE_DEPT_ID);//企业微信 全部部门
        wechatFegin.sendMessage("@all","Hello,world");
    }*/


    @Override
    public void sync() {
        syncDept(); //同步部门数据
        //syncTag(); //同步标签数据
        //syncEmployee(); //同步员工数据
        //setDeptLeader();//设置部门领导
    }

    /**
     * 同步部门数据
     */
    @Transactional
    void syncDept() {
        JSONArray depats = wechatFegin.getDepartments(SOURCE_DEPT_ID);//企业微信 全部部门
//        JSONArray xdepats = wechatFegin.getXieZuoDepartments(SOURCE_XIEZUO_DEPT_ID);//企业微信 协作部门
        deptDao.delete(new UpdateWrapper<>());//删除全部部门
//        depats.addAll(0, xdepats);
        for (Object object : depats) {
            JSONObject jo = JSONUtil.parseObj(object);
            String name = jo.getStr("name");
            String id = jo.getStr("id");
            String parentid = jo.getStr("parentid");
            //源部门（XX资管） 不保存
            if (SOURCE_DEPT_ID.equals(id)) {
                continue;
            }
            /*if (SOURCE_XIEZUO_DEPT_ID.equals(id)) {
                //协作部门修改父部门为XX资管
                parentid = SOURCE_DEPT_ID;
            }*/
            DeptEntity dept = new DeptEntity();
            dept.setCode(id).setName(name);
            if (StrUtil.isNotBlank(parentid) && !SOURCE_DEPT_ID.equals(parentid)) {
                dept.setParentCode(parentid);
            }
            dept.insert();
        }
    }

    @Transactional
    void syncTag() {
        JSONArray wachet = wechatFegin.getTagList();//企业微信 全部标签
        tagDao.delete(new UpdateWrapper<TagEntity>());//删除全部标签
        for (Object object : wachet) {
            JSONObject jo = JSONUtil.parseObj(object);
            String tagId = jo.getStr("tagid");
            String tagName = jo.getStr("tagname");
            TagEntity tag = new TagEntity();
            tag.setTagId(tagId).setName(tagName).insert();
        }
    }

    /***
     * 同步标签与员工关系
     * @方法名称 syncTagsAndUser
     * @功能描述 <pre></pre>
     * @作者 WU
     * @创建时间 2018年10月29日 下午3:59:23
     * @return
     */
    private Map<String, String> syncTagsAndUser() {
        List<TagEntity> tags = tagDao.selectList(new QueryWrapper<>());
        Map<String, String> umap = Maps.newHashMap();
        for (TagEntity emptag : tags) {
            String tagId = emptag.getTagId();
            JSONArray ja = wechatFegin.getTagUsers(tagId);
            for (Object object : ja) {
                JSONObject jo = JSONUtil.parseObj(object);
                String tuserid = jo.getStr("userid");
                if (StrUtil.isBlank(umap.get(tuserid))) {
                    umap.put(tuserid, tagId);
                } else {
                    String userTags = umap.get(tuserid) + "," + tagId;
                    umap.put(tuserid, userTags);
                }
            }
        }
        return umap;
    }

    @Transactional
    void syncEmployee() {
        //读取XX资管下的所有员工
        JSONArray array = wechatFegin.getUsersByDeptId(SOURCE_DEPT_ID, true);

        List<UserEntity> users = userDao.selectList(new QueryWrapper<>());

        Map<String, String> tumap = syncTagsAndUser();

        /** 员工删除逻辑处理 **/
        //查询企业微信ID不为null的员工信息
        List<UserEntity> users2 = userDao.selectList(new QueryWrapper<UserEntity>().isNotNull("qywechat").ne("qywechat", ""));
        for (UserEntity user : users2) {
            boolean isdel = true;
            for (Object object : array) {
                JSONObject jo = JSONUtil.parseObj(object);
                QyWechatUserVo wechatUser = jo.toBean(QyWechatUserVo.class);
                if (wechatUser.getUserid().equals(user.getQywechat())) { //库里的用户Qywechat值与同步过来的userId对比,如果有,isdel就是false,不用删除.
                    isdel = false;
                }
            }
            //同步后如果企业微信通讯录里没有相同的qywechat，说明员工已离职，则删掉。
            if (isdel) {
                user.setLoginName(user.getId() + "-del");
                user.setRealName(user.getRealName() + "-离职");
                //user.updateById();
                user.deleteById();
            }
        }

        for (Object object : array) {
            JSONObject jo = JSONUtil.parseObj(object);
            QyWechatUserVo wechatUser = jo.toBean(QyWechatUserVo.class);
            if (null != tumap) {
                wechatUser.setTagid(tumap.get(wechatUser.getUserid()));
            }
            boolean issave = true;
            for (UserEntity user : users) {
                String qywechatid = user.getQywechat();
                String username = user.getRealName();
                String userId = user.getId();
                if (StrUtil.isBlank(qywechatid) && StrUtil.isNotBlank(username)) {  //库里有用户名,但是没有qyWechat的
                    if (username.trim().contains(wechatUser.getName())) {    //并且名字也相同,则进行update,不要save.
                        updateEmp(userId, wechatUser);
                        issave = false;
                    }
                } else if (qywechatid.equals(wechatUser.getUserid())) { //或者qyWechat 与同步过来的值相同的,也进行update,不要save
                    updateEmp(userId, wechatUser);
                    issave = false;
                } else if (user.getLoginName().equals(wechatUser.getMobile())) { //登陆名(手机号) 与同步过来的值相同的,也进行update,不要save
                    //已存在手机号则修改为最新的信息
                    updateEmp(userId, wechatUser);
                    issave = false;
                }

            }
            if (issave) {//新增员工
                saveEmp(wechatUser);
            }
        }

    }


    private void saveEmp(QyWechatUserVo wechatUser) {
        UserEntity emp = getEmployee(null, wechatUser);
        emp.updateById();
    }

    private void updateEmp(String userId, QyWechatUserVo wechatUser) {
        UserEntity user = userDao.selectById(userId);
        user = getEmployee(user, wechatUser);
        user.updateById();
    }

    /***
     * 组装员工信息
     * @方法名称 getEmployee
     * @功能描述 <pre></pre>
     * @作者 WU
     * @创建时间 2018年6月28日 下午5:06:14
     * @param emp
     * @param wechatUser
     * @return
     */
    private UserEntity getEmployee(UserEntity emp, QyWechatUserVo wechatUser) {

        //判断是否要新增后台系统用户
        emp = saveUser(emp, wechatUser);
        String position = wechatUser.getPosition();
        emp.setTagId(wechatUser.getTagid());
        emp.setQywechat(wechatUser.getUserid());
        emp.setLoginName(wechatUser.getMobile());
        if(StrUtil.isBlank(wechatUser.getMobile())){
            emp.setLoginName(wechatUser.getUserid());
        }
        emp.setTel(wechatUser.getMobile());
        emp.setDept(wechatUser.getDepartmentStr());
        emp.setJob(position);
        emp.setRealName(wechatUser.getName());
        emp.setLeader(wechatUser.getIsleader());
        if (StrUtil.isNotBlank(position)) {//获取到的职位不为空，根据职位名称设置用户权限
            List<RoleEntity> roles = roleDao.selectList(new QueryWrapper<RoleEntity>().eq("name", position));
            if (null != roles && roles.size() > 0) {
                RoleEntity role = roles.get(0);
                //查询用户是否已有绑定权限，未有绑定权限，则绑定到该员工职位名称相同的角色
                if (0 == roleDao.countUserRole(emp.getId(), null)) {
                    Map<String, String> param = Maps.newHashMap();
                    param.put("userId", emp.getId());
                    param.put("roleId", role.getId());
                    List<Map<String, String>> list = Lists.newArrayList();
                    list.add(param);
                    roleDao.saveUserRole(list);
                }
            }
        }
        return emp;
    }

    private UserEntity saveUser(UserEntity user, QyWechatUserVo wechatUser) {
        if (user == null) {
            user = new UserEntity();
//            String password = PassWordCreateUtil.createPassWord(6).toLowerCase();
            String password = "123456";
            user.setLoginName(wechatUser.getMobile())
                    .setSalt(AlgorUtil.getSalt())
                    .setPassword(AlgorUtil.entryptPassword(password, user.getSalt()))
                    .setTel(wechatUser.getMobile())
                    .setEmail(wechatUser.getEmail())
                    .setRealName(wechatUser.getName())
                    .setNickName(wechatUser.getName())
                    .setIcon(wechatUser.getAvatar());
            user.insert();
            //发送短信
            /*try {
                msgFegin.sendRegInfo(wechatUser.getMobile(), wechatUser.getName(), user.getTel(), password);
            } catch (Exception e) {
                log.error(wechatUser.getMobile() + "短信发送失败", e);
            }*/
        }
        return user;
    }


    @Transactional
    void setDeptLeader() {
        List<DeptEntity> depts = deptDao.selectList(new QueryWrapper<>());
        for (DeptEntity dept : depts) {
            JSONArray array = wechatFegin.getUsersByDeptId(dept.getCode(), false);
            UserEntity leader = null;
            if (null != array) {
                for (Object object : array) {
                    JSONObject jo = JSONUtil.parseObj(object);
                    QyWechatUserVo wechatUser = jo.toBean(QyWechatUserVo.class);
                    if (wechatUser.getIsleader()) {//是上级身份
                        String wechatuserid = wechatUser.getUserid();
                        leader = userDao.selectOne(new QueryWrapper<UserEntity>().eq("qywechat", wechatuserid));
                        if (leader != null) {
                            break;
                        }
                    }
                }
            }
            //查找出部门上级用户
            if (leader != null) {
                dept.setDirectorId(leader.getId());
                dept.setDirectorName(leader.getRealName());
                dept.updateById();
            }
        }
    }

}
