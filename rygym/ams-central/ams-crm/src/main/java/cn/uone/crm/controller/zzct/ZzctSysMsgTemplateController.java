package cn.uone.crm.controller.zzct;


import cn.hutool.json.JSONUtil;
import cn.uone.bean.entity.crm.SysMsgTemplateEntity;
import cn.uone.crm.service.ISysMsgTemplateService;
import cn.uone.crm.service.IZzctSysMsgTemplateService;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.crm.IZzctSysMsgTemplateFegin;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 从漳州城投迁移过来
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-29
 * caizhanghe edit 2024-05-28
 */
@RestController
@RequestMapping("/zzct/sys/sms")
public class ZzctSysMsgTemplateController extends BaseController implements IZzctSysMsgTemplateFegin {

    @Autowired
    private IZzctSysMsgTemplateService zzctSysMsgTemplateService;

    @Override
    @RequestMapping("/send")
    public void send(@RequestBody Map<String, Object> params) throws Exception {
        zzctSysMsgTemplateService.send(params);
    }

}
