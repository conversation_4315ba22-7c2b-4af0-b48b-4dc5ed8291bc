package cn.uone.crm.service;

import cn.uone.bean.entity.business.sys.vo.TreeVo;
import cn.uone.bean.entity.crm.ProjectEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.bean.entity.crm.ZoneEntity;
import cn.uone.bean.parameter.DataScopeVo;
import cn.uone.shiro.util.ShiroUser;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
public interface IZzctUserService extends IService<UserEntity> {


    RestResponse getRestResponse(String vild, String tel);

}
