package cn.uone.crm.dao;


import cn.uone.bean.entity.crm.CityEntity;
import cn.uone.bean.entity.crm.DataScopeEntity;
import cn.uone.bean.entity.crm.ProjectEntity;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * [项目\分区]数据权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-19
 */
public interface DataScopeDao extends BaseMapper<DataScopeEntity> {

    /**
     * 获取用户数据权限下的城市（包括项目）
     *
     * @param dataScope
     * @return
     */
    List<CityEntity> getScopeCitys(DataScope dataScope);

    /**
     * 获取用户数据权限下的项目
     *
     * @param dataScope
     * @return
     */
    List<ProjectEntity> getScopeProjects(DataScope dataScope);


    /**
     * 获取包括所有城市
     * @param dataScope
     * @return
     */
    List<CityEntity> getScopeProjectsByAllCity(DataScope dataScope);
}
