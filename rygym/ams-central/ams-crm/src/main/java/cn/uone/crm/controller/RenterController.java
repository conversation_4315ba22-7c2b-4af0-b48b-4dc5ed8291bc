package cn.uone.crm.controller;


import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.IdTypeEnum;
import cn.uone.application.enumerate.RenterType;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.crm.ExternalUserRelEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.tpi.fadada.Person;
import cn.uone.bean.entity.tpi.fadada.ReqApplyCert;
import cn.uone.bean.entity.tpi.fadada.ReqIdentification;
import cn.uone.bean.entity.tpi.fadada.Result;
import cn.uone.cache.util.CacheUtil;
import cn.uone.crm.dao.RenterDao;
import cn.uone.crm.service.IExternalUserRelService;
import cn.uone.crm.service.IRenterService;
import cn.uone.crm.service.ISysMsgTemplateService;
import cn.uone.crm.service.IUserService;
import cn.uone.crm.util.FadadaVo;
import cn.uone.fegin.bus.IContContractFegin;
import cn.uone.fegin.bus.ISysMsgFegin;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.tpi.IFadadaFegin;
import cn.uone.fegin.tpi.IQyWechatFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.AlgorUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.calc.Constants;
import cn.uone.web.calc.Digests;
import cn.uone.web.calc.Encodes;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.micrometer.core.instrument.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 前端控制器
 * 租客用户
 * </p>updateRenter
 *
 * <AUTHOR>
 * @since 2018-12-14
 */

@Api(value = "租客用户", tags = "租客用户新增、修改等功能")
@RestController
@RequestMapping("/renter")
public class RenterController extends BaseController implements IRenterFegin {

    @Value("${fadada.jumpUrl}")
    private String jumpUrl;

    private static final Logger log = LoggerFactory.getLogger(RenterController.class);

    @Autowired
    private IRenterService renterService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IContContractFegin contContractFegin;
    @Resource
    private IFadadaFegin fadadaFegin;
    @Autowired
    private IExternalUserRelService externalUserRelService;
    @Autowired
    private IQyWechatFegin qyWechatFegin;
    @Autowired
    private ISysMsgTemplateService sysMsgTemplateService;

    @UoneLog("新增租客用户")
    @Override
    @RequestMapping("/add")
    public RestResponse add(@RequestBody RenterEntity renterEntity) throws Exception {
        String password = renterEntity.getPassword();
        renterEntity.setPassword(AlgorUtil.entryptPassword(password, renterEntity.getSalt()));
        // 新增租客用户
        renterService.addRenter(renterEntity);
        try {
            sysMsgTemplateService.sendRenterRegInfo(renterEntity.getTel(), renterEntity.getName(), renterEntity.getUsername(), password, renterEntity.getType(),renterEntity.getProjectId());
        } catch (Exception e) {
            log.error(renterEntity.getUsername() + " 注册信息短信发送失败");
        }

        return RestResponse.success().setData(renterEntity);
    }

    @Override
    @PostMapping("/get")
    public RestResponse get(String renterId) throws Exception {
        // 获取租客用户信息
        RenterEntity renterEntity = renterService.getRenterById(renterId);

        return RestResponse.success().setData(renterEntity);
    }

    @Override
    @RequestMapping("/getByTelAndType")
    public RenterEntity getByTelAndType(String tel, String type) {
        // 获取租客用户信息
        RenterEntity renterEntity = renterService.getRenterByTelAndType(tel,type);
        return renterEntity;
    }

    @RequestMapping("/getByTelAndTypeResponse")
    public RestResponse getByTelAndTypeResponse(String tel, String type) {
        // 获取租客用户信息
        return RestResponse.success().setData(this.getByTelAndType(tel,type));
    }

    @Override
    @RequestMapping("/queryByUserName")
    public RenterEntity queryByUserName(String userName, String type){
        // 获取租客用户信息
        RenterEntity renterEntity = renterService.queryByUserName(userName,type);
        return renterEntity;
    }

    @PostMapping("/queryByTel")
    public RestResponse queryByTel(String tel) throws Exception {
        // 获取租客用户信息
        RenterEntity renterEntity = renterService.getRenterByTel(tel);
        return RestResponse.success().setData(renterEntity);
    }

    @Override
    @PostMapping("/update")
    public RenterEntity update(@RequestBody RenterEntity renterEntity) {
        renterService.updateById(renterEntity);
        return renterEntity;
    }

    @RequestMapping("/queryAgreement")
    public RestResponse queryAgreement(String renterId) throws Exception {
        RestResponse response = new RestResponse();
        return response.setSuccess(true).setData(renterService.queryAgreement(renterId));
    }

    @RequestMapping("/updateParams")
    public RestResponse updateParams(RenterEntity renterEntity) {
        RestResponse response = new RestResponse();
        boolean flag = StrUtil.isBlank(renterEntity.getIdType()) || IdTypeEnum.IDENTITY_CARD.getValue().equals(renterEntity.getIdType());
        if(StrUtil.isNotBlank(renterEntity.getIdNo())
                && flag
                && !isValidPassport(renterEntity.getIdNo())
                && !IdcardUtil.isValidCard(renterEntity.getIdNo())
                && !IdcardUtil.isValidTWCard(renterEntity.getIdNo())
                && !IdcardUtil.isValidHKCard(renterEntity.getIdNo())){
            return response.setSuccess(false).setMessage("无效的身份证号码");
        }
        renterService.updateById(renterEntity);
        return response.setSuccess(true).setData(renterEntity);
    }

    /**
     * 验证护照号
     * @param passport 护照号
     * @return 是否有效
     */
    private  boolean isValidPassport(String passport) {
        Pattern pattern = Pattern.compile("^[a-zA-Z0-9]{5,17}$");
        Matcher matcher = pattern.matcher(passport);
        return matcher.matches(); // 验证护照号格式
    }

    @Override
    @RequestMapping(value = "/getById")
    @UonePermissions
    public RenterEntity getById(String singerId) {
        return renterService.getById(singerId);
    }

    @PostMapping(value = "/listByIds")
    public Collection<RenterEntity> listByIds(@RequestBody List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return renterService.listByIds(ids);
    }

    @Override
    @RequestMapping(value = "/getByAuthId")
    @UonePermissions(LoginType.CUSTOM)
    public RenterEntity getByAuthId(String authId) {
        QueryWrapper query = new QueryWrapper();
        query.eq("fadada_code",authId);
        return renterService.getOne(query);
    }

    @Override
    @RequestMapping(value = "/getByTel")
    public RenterEntity getByTel(String tel) throws Exception {
        return renterService.getRenterByTel(tel);
    }


    @PostMapping("/getBySourceId")
    public RestResponse getBySourceId(String sourceId) {
        //根据房源获取有效合同
        RestResponse response = new RestResponse();
        ContContractEntity contract = contContractFegin.getSuccessContractBySourceId(sourceId);
        if (ObjectUtil.isNotNull(contract)) {
            RenterEntity renter = renterService.getById(contract.getSignerId());
            response.setSuccess(true);
            response.setData(renter);
            return response;
        }
        return response.setSuccess(false).setMessage("未找到签约用户！");
    }

    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, String keyWord, @RequestParam(value = "type", required = false) String type, @RequestParam(value = "propertyDeviceId" ,required = false)String propertyDeviceId) {
        RestResponse response = new RestResponse();
        Map<String, Object> map = Maps.newHashMap();
        if (StringUtils.isNotBlank(keyWord)) {
            map.put("keyWord", keyWord);
        }
        if (StringUtils.isNotBlank(type)) {
            map.put("type", type);
        }
        if (StringUtils.isNotBlank(propertyDeviceId)){
            map.put("propertyDeviceId",propertyDeviceId);
        }
        return response.setSuccess(true).setData(renterService.findByCondition(page, map));
    }


    @ApiOperation("获取项目列表")
    @RequestMapping("/custom/getProjects")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse getProjects(@RequestParam(value = "isScope", required = false, defaultValue = "false") Boolean isScope) {
        return RestResponse.success().setData(userService.getProjects(isScope));
    }

    @ApiOperation("获取分区列表")
    @RequestMapping("/custom/getAreas")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse getAreas(String projectId, @RequestParam(value = "isScope", required = false, defaultValue = "false") Boolean isScope) {
        return RestResponse.success().setData(userService.getAreas(projectId, isScope, false));
    }

    @Override
    @RequestMapping("/getRenter")
    public RestResponse query(String tel, @RequestParam(required = false) String type) {
        // 获取租客用户信息
        QueryWrapper query = new QueryWrapper();
        query.eq("tel", tel);
        if (StringUtils.isNotBlank(type)) {
            query.eq("type", type);
        }
        RenterEntity renterEntity = renterService.getOne(query);

        return RestResponse.success().setData(renterEntity);
    }

    // 获取实名认证地址
    @UonePermissions(LoginType.CUSTOM)
    @RequestMapping("/getACurl")
    public RestResponse getACurl(@RequestParam("name") String name, @RequestParam("idNo") String idNo) throws Exception {

        // 判断该租客是否已注册法大大客户编号
        RenterEntity renterEntity = renterService.getRenterById(UoneSysUser.id());

        //法大大企业认证
        //作为企业认证使用
        if ("4".equals(renterEntity.getTel())) {
            return getCompanyACurl(name, idNo);
        }
        if ("2".equals(renterEntity.getIsVerify())) {
            return RestResponse.success(fadadaFegin.beforAuthsign(renterEntity.getFadadaCode(), jumpUrl));
        }
        if ("3".equals(renterEntity.getIsVerify())) {
            return fadadaFegin.certinfo(renterEntity.getSerialNo(), "2");
        }

        if (StrUtil.isBlank(renterEntity.getFadadaCode())) {
            // 注册法大大客户编号
            String fadadaCode = fadadaFegin.register("1",UoneSysUser.id()).get("data").toString();
            renterEntity.setFadadaCode(fadadaCode);
            renterEntity.setSerialNo(renterEntity.getId());
            renterService.updateById(renterEntity);
        }


        // 获取法大大个人实名认证地址
        //法大大实名认证的时候姓名和电话有值的话照片识别后不会覆盖，因此传空值
        ReqIdentification identification = new ReqIdentification();
        identification.setCustomerName(renterEntity.getName());
        identification.setCustomerId(renterEntity.getFadadaCode());
        identification.setCustomerIdentType(IdTypeEnum.IDENTITY_CARD.getValue());
        identification.setCustomerIdentNo(renterEntity.getIdNo());
        identification.setMobile(UoneSysUser.tel());
        //identification.setReturnUrl(jumpUrl);

        RestResponse restResponse = fadadaFegin.getPersonVerifyUrl(identification);
        //获取短链接
        if (restResponse.getSuccess()) {
            Map map = (Map) restResponse.get("data");
            String url = map.get("url").toString();
            log.info("url:" + url);
            RestResponse response = fadadaFegin.getShortUrl(url);
            if (response.getSuccess()) {
                url = response.get("data").toString();
                RestResponse res = fadadaFegin.sendSMS(UoneSysUser.tel(), url);
                if (!res.getSuccess()) {
                    return res;
                }
            } else {
                return response;
            }
        } else {
            return restResponse;
        }

        return restResponse;
    }

    // 获取企业认证地址
    @RequestMapping("/getCompanyACurl")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse getCompanyACurl(@RequestParam("name") String name, @RequestParam("idNo") String idNo) throws Exception {
        // 判断该租客是否已注册法大大客户编号
        RenterEntity renterEntity = renterService.getRenterById(UoneSysUser.id());

        if (StrUtil.isBlank(renterEntity.getFadadaCode())) {
            //如果是通过18659204845进行帮助企业认证，那直接先设置好假的openid在serial_no上辅助注册法大大编号
            String openId = renterEntity.getSerialNo();
            // 注册法大大客户编号
            String fadadaCode = fadadaFegin.register("2",openId).get("data").toString();
            renterEntity.setFadadaCode(fadadaCode);

            renterService.updateById(renterEntity);
        }

        // 获取法大大个人实名认证地址
        ReqIdentification identification = new ReqIdentification();
        identification.setCustomerName(name);
        identification.setCustomerId(renterEntity.getFadadaCode());
        identification.setCustomerIdentType(IdTypeEnum.IDENTITY_CARD.getValue());
        identification.setCustomerIdentNo(idNo);
        identification.setMobile(UoneSysUser.tel());
        identification.setReturnUrl(jumpUrl);
        RestResponse restResponse = fadadaFegin.getCompanyVerifyUrl(identification);

        return restResponse;
    }

    // 获取企业认证地址
    @RequestMapping("/getCompanyACurlWeb")
    @UonePermissions
    public RestResponse getCompanyACurlWeb(@RequestParam("name") String name, @RequestParam("idNo") String idNo, @RequestParam("id") String id) throws Exception {
        // 判断该租客是否已注册法大大客户编号
        RenterEntity renterEntity = renterService.getRenterById(id);

        if (StrUtil.isBlank(renterEntity.getFadadaCode())) {
            // 注册法大大客户编号
            String fadadaCode = fadadaFegin.register("2",UoneSysUser.id()).get("data").toString();
            renterEntity.setFadadaCode(fadadaCode);

            renterService.updateById(renterEntity);
        }

        // 获取法大大个人实名认证地址
        ReqIdentification identification = new ReqIdentification();
        identification.setCustomerName(name);
        identification.setCustomerId(renterEntity.getFadadaCode());
        identification.setCustomerIdentType(IdTypeEnum.IDENTITY_CARD.getValue());
        identification.setCustomerIdentNo(idNo);
        identification.setMobile(UoneSysUser.tel());
        identification.setReturnUrl(jumpUrl);
        RestResponse restResponse = fadadaFegin.getCompanyVerifyUrl(identification);

        return restResponse;
    }

    // 获取企业认证地址
    @RequestMapping("/getApplyCert")
    @UonePermissions
    public RestResponse getApplyCert(@RequestParam("customerId") String customerId, @RequestParam("verifiedSerialno") String verifiedSerialno) {

        RestResponse restResponse = fadadaFegin.getApplyCert(customerId, verifiedSerialno);

        return restResponse;
    }

    @ApiOperation(value = "实名认证异步回调")
    @RequestMapping(value = "/getPersonVerifyCallback", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @UonePermissions
    public void getPersonVerifyCallback(Result result) throws UnsupportedEncodingException {
        log.info("实名认证异步回调:" + result);
        // 判断是否成功
        if ("2".equals(result.getStatus())) {
            // 查询个人实名认证信息
            String jsonString = JSONObject.toJSONString(fadadaFegin.certinfo(result.getSerialNo(), "1").get("data"));
            Person person = JSONObject.parseObject(JSONObject.parseObject(jsonString).getString("person"), Person.class);

            // 实名证书申请
            ReqApplyCert applyCert = new ReqApplyCert();
            applyCert.setCustomerId(result.getCustomerId());
            applyCert.setVerifiedSeriaino(result.getSerialNo());

//            RestResponse restResponse = fadadaFegin.applyCert(applyCert);
            RenterEntity renterEntity = renterService.getOne(new QueryWrapper<RenterEntity>().eq("fadada_code", result.getCustomerId()));
            // 修改租客信息，以实名认证为主
            if (null != renterEntity) {
                renterEntity.setTel(person.getMobile())
                        .setName(person.getPersonName())
                        .setIdNo(person.getIdCard())
                        .setSerialNo(result.getSerialNo())
                        .setAddress(person.getAddress())
                        .setIsVerify(BaseConstants.BOOLEAN_OF_TRUE);
                renterEntity.updateById();
            }


        }

    }

    @Override
    @RequestMapping(value = "/initAdd", method = RequestMethod.GET)
    public void initAdd(@RequestBody RenterEntity renterEntity) {
        renterService.saveOrUpdate(renterEntity);
    }

    @Override
    @RequestMapping("/initGetByParam")
    public boolean initGetByParam(String tel, String type,String id) {
        RenterEntity renterEntity=renterService.getRenterByTelAndType(tel,type);
        if(ObjectUtil.isNull(renterEntity)){
            return false;
        }else{
            renterService.updateId(id,renterEntity.getId());
            return true;
        }

    }

    @RequestMapping("/queryByCondition")
    public List<RenterEntity> queryByCondition(@RequestBody RenterEntity renterEntity) {
        QueryWrapper queryWrapper =new QueryWrapper();
        if(StrUtil.isNotBlank(renterEntity.getUnionId())){
            queryWrapper.eq("union_id",renterEntity.getUnionId());
        }
        if(StrUtil.isNotBlank(renterEntity.getExternalUserid()) && renterEntity.getExternalUserid().equals(BaseConstants.BOOLEAN_OF_TRUE)){
            queryWrapper.isNotNull("external_userid");
        }
        return renterService.list(queryWrapper);
    }

    /**
     * 获取外部联系人
     * @param renterEntity
     * @return
     */
    @RequestMapping("/getByParams")
    @UonePermissions
    public RestResponse getByParams(RenterEntity renterEntity) {
        QueryWrapper queryWrapper =new QueryWrapper();
        if(StrUtil.isNotBlank(renterEntity.getUnionId())){
            queryWrapper.eq("union_id",renterEntity.getUnionId());
        }
        if(StrUtil.isNotBlank(renterEntity.getExternalUserid())){
            queryWrapper.eq("external_userid",renterEntity.getExternalUserid());
        }
        RenterEntity entity=renterService.getOne(queryWrapper);
        if(ObjectUtil.isNull(entity)){
           cn.hutool.json.JSONObject exuser =  qyWechatFegin.getCustomInfo(renterEntity.getExternalUserid()).getJSONObject("external_contact");
            if(ObjectUtil.isNotNull(exuser.get("unionid"))){
                entity = renterService.getOne(new QueryWrapper<RenterEntity>().eq("union_id",exuser.get("unionid")));
            }
        }
        return RestResponse.success().setData(entity);
    }

    @RequestMapping("/saveExRel")
    public void saveExRel(@RequestBody List<ExternalUserRelEntity> rels) {
        externalUserRelService.remove(new QueryWrapper<ExternalUserRelEntity>());
        externalUserRelService.saveBatch(rels);
    }

    @RequestMapping("/getExRel")
    public List<ExternalUserRelEntity> getExRel() {
        return externalUserRelService.getall();
    }

    @RequestMapping(value = "/updateRenter", method = RequestMethod.POST)
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse updateRenter(String id, String sex, String address, String email) {
        RenterEntity entity = new RenterEntity();
        entity.setId(id);
        entity.setSex(sex);
        if (StrUtil.isNotBlank(address)) {
            entity.setAddress(address);
        } else {
            entity.setAddress(address);
        }
        if (StrUtil.isNotEmpty(email)) {
            String regexEmail = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
            if (!Pattern.matches(regexEmail, email)) {
                return RestResponse.failure("邮箱格式有误");
            }
            entity.setEmail(email);
        } else {
            entity.setEmail(email);
        }
        entity.insertOrUpdate();
        return RestResponse.success();
    }
    @RequestMapping(value = "/resetPass", method = RequestMethod.POST)
    @UonePermissions
    public RestResponse resetPass(String loginName,String smsCode,String newPwd,String surePwd){
        if(!newPwd.equals(surePwd)){
            return  RestResponse.success("两次密码输入不一致");
        }
        RenterEntity user =  renterService.getRenterByUsername(loginName, RenterType.ENTERPRISE.getValue());
        if(ObjectUtil.isNull(user)){
            return RestResponse.failure("未查找到用户名为："+loginName+"的机构会员用户");
        }
        if(StrUtil.isBlank(user.getTel())){
            return RestResponse.failure(loginName+"用户未设置手机号");
        }
        if (StrUtil.isBlank(smsCode)) {
            return RestResponse.failure("请输入验证码");
        }
        String tel = user.getTel();

        String vildCode = (String) CacheUtil.get(BaseConstants.TEL_SMS_HEADER + tel);
        if (!smsCode.equals(vildCode)) {
            return RestResponse.failure("短信验证码错误或已过期（5分钟内）！");
        }

        user.setPassword(newPwd);

        byte[] salt = Digests.generateSalt(Constants.SALT_SIZE);
        user.setSalt(Encodes.encodeHex(salt));
        byte[] hashPassword = Digests.sha1(user.getPassword().getBytes(), salt, Constants.HASH_INTERATIONS);
        user.setPassword(Encodes.encodeHex(hashPassword));
        user.updateById();

        return RestResponse.success("修改密码成功");
    }

    @RequestMapping(value = "/untying", method = RequestMethod.POST)
    @UoneLog("租客管理微信解绑")
    public RestResponse untying(String renterId){
        renterService.setOpenidNULL(renterId);
        return RestResponse.success("成功解绑！");
    }

    @Override
    @RequestMapping(value = "/getRenterAll",method = RequestMethod.GET)
    public List<Map<String,Object>> getRenterAll() {
        return renterService.getAll();
    }

    /**
     * 修改租客备注
     * 单条修改
     * @param id
     * @param remark
     * @return
     * <AUTHOR> 2023-12-20
     */
    @RequestMapping(value = "/updateRenterRemark", method = RequestMethod.POST)
    @UoneLog("租客管理添加备注")
    @Transactional
    public RestResponse updateRenterRemark(String id, String remark) {
        RestResponse response = new RestResponse();
        try {
            RenterEntity entity = this.getById(id);
            entity.setRemark(remark);
            entity.insertOrUpdate();
            response.setSuccess(true).setMessage("提交成功！");
        } catch (Exception e){
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 录入员工信息
     * @param filePath
     * @return
     */
    @RequestMapping("/uploadRenters")
    @UonePermissions
    @Transactional
    public RestResponse uploadRenters(String filePath) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(filePath);
        List<Map<String, Object>> readAll = reader.readAll();

        List<RenterEntity> list = Lists.newArrayList();
        for (Map<String, Object> map:readAll) {
            String tel = String.valueOf(map.get("联系方式").toString());
            String name = (String) map.get("姓名");
            String sex = (String) map.get("性别");
            RenterEntity renter = renterService.getRenterByTel(tel);
            if(renter == null){
                renter = new RenterEntity();
                renter.setNickName(name);
                renter.setUsername(tel);
            }
            renter.setName(name);
            renter.setTel(tel);
            renter.setSex("男".equals(sex)?"1":"0");
            renter.setType("2");
            renter.setTalentType("7");
            list.add(renter);
        }
        if(list.size()>0)
            renterService.saveOrUpdateBatch(list,list.size());
        return RestResponse.success();
    }
}
