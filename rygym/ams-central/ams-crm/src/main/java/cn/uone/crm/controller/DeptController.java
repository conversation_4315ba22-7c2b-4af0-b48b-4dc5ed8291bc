package cn.uone.crm.controller;


import cn.uone.bean.entity.business.sys.vo.TreeVo;
import cn.uone.bean.entity.crm.DeptEntity;
import cn.uone.crm.service.IDeptService;
import cn.uone.fegin.crm.IDeptFegin;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 工作流部门表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-12
 */
@RestController
@RequestMapping("/dept")
public class DeptController extends BaseController implements IDeptFegin {
    @Autowired
    private IDeptService deptService;


    @Override
    @PostMapping("/get")
    public DeptEntity getDeptByCode(String code) {
        QueryWrapper<DeptEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("code", code);
        return deptService.getOne(queryWrapper);
    }

    @RequestMapping("/getDeptTree")
    public RestResponse getDeptTree() {
        TreeVo vo =new TreeVo();
        vo.setTitle("XX地产");
        vo.setName("XX地产");
        vo.setSpread(true);
        List<TreeVo> childs =deptService.getDeptTree(null);
        vo.setChildren(childs);
        List<TreeVo> trees = Lists.newArrayList();
        trees.add(vo);
        return RestResponse.success().setData(trees);
    }

    @RequestMapping("/getDept")
    public RestResponse getDept() {
        return RestResponse.success().setData(deptService.list(new QueryWrapper<DeptEntity>()));
    }
}
