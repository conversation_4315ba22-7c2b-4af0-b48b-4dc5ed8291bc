spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://127.0.0.1:8848
        namespace: f9cabb73-130b-4a70-b059-34c263ceb01e
      config:
        file-extension: yml
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
    servicecomb:
      credentials:
        enabled: true
        accessKey: AC1ZXJHXRIHP3EGLRD9P
        secretKey: knPrUsCIpybic4Dlo0y5rDqSSPoJY66mJCCxaofu
        AccessKeyID: LTAI4FkvG45AXrQ144ArMtHn
        AccessKeySecret: ******************************
        akskCustomCipher: default
        project: cn-east-3