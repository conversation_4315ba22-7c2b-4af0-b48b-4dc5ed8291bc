spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://127.0.0.1:8852
        namespace: eea822eb-327d-4936-816c-67691044f28d
      config:
        file-extension: yml
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
    servicecomb:
      credentials:
        enabled: true
        accessKey: AC1ZXJHXRIHP3EGLRD9P
        secretKey: knPrUsCIpybic4Dlo0y5rDqSSPoJY66mJCCxaofu
        AccessKeyID: LTAI4FkvG45AXrQ144ArMtHn
        AccessKeySecret: ******************************
        akskCustomCipher: default
        project: cn-east-3
