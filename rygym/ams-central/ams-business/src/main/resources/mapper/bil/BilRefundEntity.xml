<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.bil.dao.BilRefundDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.bil.BilRefundEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="tk_order_id" property="tkOrderId" />
        <result column="sk_order_id" property="skOrderId" />
        <result column="payment" property="payment" />
        <result column="mer_order_id" property="merOrderId" />
        <result column="seq_id" property="seqId" />
        <result column="total_amount" property="totalAmount" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_order_id" property="refundOrderId" />
        <result column="refund_target_order_id" property="refundTargetOrderId" />
        <result column="refund_status" property="refundStatus" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        tk_order_id, sk_order_id, payment, mer_order_id, seq_id, total_amount, refund_amount, refund_order_id, refund_target_order_id, refund_status, remark
    </sql>

</mapper>
