<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.bil.dao.BilOrderDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.bil.BilOrderEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="code" property="code"/>
        <result column="merge_code" property="mergeCode"/>
        <result column="source_id" property="sourceId"/>
        <result column="contract_id" property="contractId"/>
        <result column="discount_log_id" property="discountLogId"/>
        <result column="payment" property="payment"/>
        <result column="payable_payment" property="payablePayment"/>
        <result column="actual_payment" property="actualPayment"/>
        <result column="pay_way" property="payWay"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_state" property="payState"/>
        <result column="order_type" property="orderType"/>
        <result column="trade_code" property="tradeCode"/>
        <result column="payer_id" property="payerId"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="invoice_state" property="invoiceState"/>
        <result column="is_push" property="push"/>
        <result column="push_time" property="pushTime"/>
        <result column="is_first" property="first"/>
        <result column="is_discard" property="discard"/>
        <result column="is_initial" property="initial"/>
        <result column="remark" property="remark"/>
        <result column="ccb_bill_id" property="ccbBillId"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="transfer_mode" property="transferMode"/>
        <result column="report_date" property="reportDate"/>
        <result column="invoice_payment" property="invoicePayment"/>
        <result column="demand_payment_count" property="demandPaymentCount"/>
        <result column="old_renter_id" property="oldRenterId"/>
        <result column="yhPayment" property="yhPayment"/>
        <result column="mer_order_id" property="merOrderId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        demand_payment_count,
        code, merge_code, source_id, contract_id, discount_log_id, payment,payable_payment,actual_payment, pay_way,
        pay_time,pay_state,order_type,trade_code,payer_id, invoice_type,
        invoice_state, is_push, is_first, is_discard, is_initial, remark,
        transfer_state,ccb_bill_id,report_date,invoice_payment,old_renter_id
    </sql>


    <resultMap id="IncludeItemResultMap" type="cn.uone.bean.entity.business.bil.BilOrderEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="code" property="code"/>
        <result column="merge_code" property="mergeCode"/>
        <result column="source_id" property="sourceId"/>
        <result column="contract_id" property="contractId"/>
        <result column="discount_log_id" property="discountLogId"/>
        <result column="payment" property="payment"/>
        <result column="payable_payment" property="payablePayment"/>
        <result column="actual_payment" property="actualPayment"/>
        <result column="pay_way" property="payWay"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_state" property="payState"/>
        <result column="order_type" property="orderType"/>
        <result column="trade_code" property="tradeCode"/>
        <result column="payer_id" property="payerId"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="invoice_state" property="invoiceState"/>
        <result column="is_push" property="push"/>
        <result column="is_first" property="first"/>
        <result column="is_discard" property="discard"/>
        <result column="is_initial" property="initial"/>
        <result column="remark" property="remark"/>
        <result column="transfer_state" property="transferState"/>
        <result column="apply_inv_time" property="applyInvTime"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="invoice_payment" property="invoicePayment"/>
        <result column="demand_payment_count" property="demandPaymentCount"/>
        <result column="old_renter_id" property="oldRenterId"/>
        <collection property="items" ofType="cn.uone.bean.entity.business.bil.BilOrderItemEntity">
            <result column="oi.id" property="id"/>
            <result column="oi.create_by" property="createBy"/>
            <result column="oi.create_date" property="createDate"/>
            <result column="oi.update_by" property="updateBy"/>
            <result column="oi.update_date" property="updateDate"/>
            <result column="oi.order_id" property="orderId"/>
            <result column="oi.code" property="code"/>
            <result column="oi.bank_serial_code" property="bankSerialCode"/>
            <result column="oi.payment" property="payment"/>
            <result column="oi.order_item_type" property="orderItemType"/>
            <result column="oi.start_time" property="startTime"/>
            <result column="oi.end_time" property="endTime"/>
            <result column="oi.arrive_time" property="arriveTime"/>
            <result column="oi.price" property="price"/>
            <result column="oi.num" property="num"/>
            <result column="oi.start_read" property="startRead"/>
            <result column="oi.end_read" property="endRead"/>
            <result column="oi.device_id" property="deviceId"/>
        </collection>
    </resultMap>

    <sql id="Include_Item_Column_List">
        o.id,
        o.create_by,
        o.create_date,
        o.update_by,
        o.update_date,
        o.code,o.merge_code, o.source_id, o.contract_id, o.discount_log_id, o.payment,o.payable_payment,o.actual_payment,o.pay_way,
        o.pay_time,o.pay_state,o.order_type,o.trade_code,o.payer_id,o.invoice_type,
        o.invoice_state,o.is_push,o.is_first,o.is_discard,o.is_initial,o.remark,
        o.transfer_state,o.apply_inv_time,o.invoice_payment,
        ifnull(d.discount_amount,dis.discount_amount) discount_amount,
        oi.id as "oi.id",
        oi.create_by as "oi.create_by",
        oi.create_date as "oi.create_date",
        oi.update_by as "oi.update_by",
        oi.update_date as "oi.update_date",
        oi.order_id as "oi.order_id",oi.code as "oi.code",oi.payment as "oi.payment",oi.order_item_type as "oi.order_item_type",
        oi.start_time as "oi.start_time",oi.end_time as "oi.end_time",oi.price as "oi.price",oi.num as "oi.num",
        oi.start_read as "oi.start_read",oi.end_read as "oi.end_read",oi.device_id as "oi.device_id",oi.arrive_time as "oi.arrive_time"
    </sql>
    <!-- vo映射结果 -->
    <resultMap id="BilOrderVoMap" type="cn.uone.bean.entity.business.bil.vo.BilOrderVo" extends="BaseResultMap">
        <result column="address" property="address"/>
        <result column="payer" property="payer"/>
        <result column="singer" property="singer"/>
        <result column="emp_name" property="empName"/>
        <result column="emp_tel" property="empTel"/>
        <result column="payer_type" property="payerType"/>
        <result column="contract_code" property="contractCode"/>
        <result column="contract_type" property="contractType"/>
        <result column="is_organize" property="isOrganize"/>
        <result column="isPayable" property="isPayable"/>
        <result column="time" property="time"/>
        <result column="name" property="name"/>
        <result column="taxpayer_code" property="taxpayerCode"/>
        <result column="enterprise_address" property="enterpriseAddress"/>
        <result column="enterprise_tel" property="enterpriseTel"/>
        <result column="enterprise_account" property="enterpriseAccount"/>
        <result column="enterprise_bank" property="enterpriseBank"/>
        <result column="projectName" property="projectName"/>
        <result column="partitionName" property="partitionName"/>
        <result column="cash_pledge" property="cashPledge"/>
        <result column="price" property="price"/>
        <result column="transfer_time" property="transferTime"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="signer" property="signer"/>
        <result column="tel" property="tel"/>
        <result column="discountId" property="discountId"/>
        <result column="discountCode" property="discountCode"/>
        <result column="audit_result" property="auditResult"/>
        <result column="intention" property="intention"/>
        <result column="pay_type" property="payType"/>
        <result column="contract_id" property="contractId"/>
        <result column="mt_payment" property="mtPayment"/>
        <result column="noTaxPrice" property="noTaxPrice"/>
        <result column="cur_payment" property="curPayment"/>
        <result column="cur_month" property="curMonth"/>
        <result column="cur_pay" property="curPay"/>
        <result column="sswq" property="sswq"/>
        <result column="yswq" property="yswq"/>
        <result column="tax" property="tax"/>
        <result column="cState" property="cState"/>
        <result column="checkout_date" property="checkoutDate"/>
        <result column="tax_number" property="invoiceNo"/>
        <result column="tax_payment" property="invoiceAmount"/>
        <result column="tax_amount" property="taxAmount"/>
        <result column="area" property="area"/>
        <result column="tax_point" property="taxPoint"/>
        <result column="code" property="code"/>
        <result column="order_type" property="orderType"/>
        <result column="create_date" property="createDate"/>
        <result column="payment" property="payment"/>
        <result column="pay_state" property="payState"/>
        <result column="pay_time" property="payTime"/>
        <result column="payable_time" property="payableTime"/>
        <result column="sDate" property="sDate"/>
        <result column="eDate" property="eDate"/>
        <result column="tax_amount" property="taxAmount"/>
        <result column="source_ids" property="sourceIds"/>
    </resultMap>

    <!-- vo映射结果 -->
    <resultMap id="DailyOrderVoMap" type="cn.uone.bean.entity.business.bil.vo.DailyOrderVo" extends="BaseResultMap">
        <result column="projectName" property="projectName"/>
        <result column="rentName" property="rentName"/>
        <result column="sourceName" property="sourceName"/>
        <result column="depositPayment" property="depositPayment"/>
        <result column="rentPayment" property="rentPayment"/>
        <result column="intentionPayment" property="intentionPayment"/>
        <result column="waterPayment" property="waterPayment"/>
        <result column="electricPayment" property="electricPayment"/>
        <result column="allPayment" property="allPayment"/>
        <result column="allFee" property="allFee"/>
        <result column="remarks" property="remarks"/>
    </resultMap>

    <!-- vo映射结果 -->
    <resultMap id="OverdueBilVoMap" type="cn.uone.bean.entity.business.bil.vo.BilOverdueVo" extends="BaseResultMap">
        <result column="overdue" property="overdue"/>
        <result column="payablePayment" property="payablePayment"/>
        <result column="payableTime" property="payableTime"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="openid" property="openid"/>
        <result column="code" property="code"/>
        <result column="contractCode" property="contractCode"/>
        <result column="renterId" property="renterId"/>
        <result column="orderType" property="orderType"/>
        <result column="id" property="id"/>
    </resultMap>

    <resultMap id="ItemWithOrderVo" type="cn.uone.bean.entity.business.bil.vo.BilOrderItemWithOrderVo">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="order_id" property="orderId"/>
        <result column="code" property="code"/>
        <result column="payment" property="payment"/>
        <result column="order_item_type" property="orderItemType"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="price" property="price"/>
        <result column="num" property="num"/>
        <result column="start_read" property="startRead"/>
        <result column="end_read" property="endRead"/>
        <result column="device_id" property="deviceId"/>
        <result column="bank_serial_code" property="bankSerialCode"/>
        <result column="invoice_id" property="invoiceId"/>
        <association property="bilOrderVo" javaType="cn.uone.bean.entity.business.bil.vo.BilOrderVo">
            <result column="address" property="address"/>
            <result column="payer" property="payer"/>
            <result column="singer" property="singer"/>
            <result column="emp_name" property="empName"/>
            <result column="emp_tel" property="empTel"/>
            <result column="payer_type" property="payerType"/>
            <result column="contract_code" property="contractCode"/>
            <result column="contract_type" property="contractType"/>
            <result column="is_organize" property="isOrganize"/>
            <result column="isPayable" property="isPayable"/>
            <result column="time" property="time"/>
            <result column="name" property="name"/>
            <result column="taxpayer_code" property="taxpayerCode"/>
            <result column="enterprise_address" property="enterpriseAddress"/>
            <result column="enterprise_tel" property="enterpriseTel"/>
            <result column="enterprise_account" property="enterpriseAccount"/>
            <result column="enterprise_bank" property="enterpriseBank"/>
            <result column="projectName" property="projectName"/>
            <result column="projectCode" property="projectCode"/>
            <result column="partitionName" property="partitionName"/>
            <result column="platform" property="platform"/>
            <result column="cash_pledge" property="cashPledge"/>
            <result column="price" property="price"/>
            <result column="paperCode" property="paperCode"/>
            <result column="transfer_time" property="transferTime"/>
            <result column="start_date" property="startDate"/>
            <result column="end_date" property="endDate"/>
            <result column="signer" property="signer"/>
            <result column="tel" property="tel"/>
            <result column="discountId" property="discountId"/>
            <result column="discountCode" property="discountCode"/>
            <result column="audit_result" property="auditResult"/>
            <result column="intention" property="intention"/>
            <result column="pay_type" property="payType"/>
            <result column="contract_id" property="contractId"/>
            <result column="mt_payment" property="mtPayment"/>
            <result column="noTaxPrice" property="noTaxPrice"/>
            <result column="cur_payment" property="curPayment"/>
            <result column="cur_month" property="curMonth"/>
            <result column="cur_pay" property="curPay"/>
            <result column="sswq" property="sswq"/>
            <result column="yswq" property="yswq"/>
            <result column="tax" property="tax"/>
            <result column="cState" property="cState"/>
            <result column="checkout_date" property="checkoutDate"/>
            <result column="tax_number" property="invoiceNo"/>
            <result column="tax_payment" property="invoiceAmount"/>
            <result column="tax_amount" property="taxAmount"/>
            <result column="area" property="area"/>
            <result column="tax_point" property="taxPoint"/>
            <result column="orderCode" property="code"/>
            <result column="order_type" property="orderType"/>
            <result column="create_date(1)" property="createDate"/>
            <result column="orderPayment" property="payment"/>
            <result column="pay_state" property="payState"/>
            <result column="pay_time" property="payTime"/>
            <result column="payable_time" property="payableTime"/>
            <result column="sDate" property="sDate"/>
            <result column="eDate" property="eDate"/>
            <result column="payer_id" property="payerId"/>
            <result column="source_ids" property="sourceIds"/>
        </association>
    </resultMap>

    <select id="countBilOrderByMap" resultType="java.lang.Integer">
        select count(1)
        from t_bil_order o
        LEFT JOIN t_cont_contract c on o.contract_id = c.id
        LEFT JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN v_sys_renter r on r.id = o.payer_id
        LEFT JOIN v_sys_renter r2 on r2.id = c.signer_id
        where 1=1
        #project_datascope#
        <include refid="searchCondition"/>
    </select>

    <select id="getMonthData" resultType="cn.uone.bean.entity.business.bil.vo.BilCountVo">
        select
        IFNULL(sum(if(o.pay_state='20',1,0)),0) as monthPayed,
        IFNULL(sum(if(o.pay_state='10',1,0)),0) as monthUnPayed
        from t_bil_order o
        LEFT JOIN v_res_source s on o.source_id = s.id
        where 1=1 and DATE_FORMAT(o.payable_time,'%Y-%m')=DATE_FORMAT(#{date},'%Y-%m')
        #project_datascope#
    </select>

    <select id="getMonthDatas" resultType="cn.uone.bean.entity.business.bil.vo.BilCountVo">
        select
        IFNULL(sum(if(o.pay_state='20',1,0)),0) as monthPayed,
        IFNULL(sum(if(o.pay_state='10',1,0)),0) as monthUnPayed
        from t_bil_order o
        LEFT JOIN v_res_source s on o.source_id = s.id
        left join t_cont_contract c on o.contract_id=c.id
        where 1=1 and DATE_FORMAT(o.payable_time,'%Y-%m')=DATE_FORMAT(#{map.date},'%Y-%m')
        #project_datascope#
        <if test="map.projectId != null and map.projectId != ''">
            and s.project_id = #{map.projectId}
        </if>
        <if test="map.manager != null and map.manager != ''">
            and c.manager = #{map.manager}
        </if>
    </select>

    <select id="selectBilOrderByMap" parameterType="java.util.Map" resultMap="BilOrderVoMap">
        select o.id,o.approval_state,o.code,c.paper_code paperCode,oit.sDate,oit.eDate,
        o.create_date,o.order_type,o.payment,o.payable_payment,o.actual_payment,o.pay_way,
        o.pay_state,o.pay_time,o.invoice_type,o.trade_code,o.transfer_mode,o.ccb_bill_id,o.mer_order_id,
        o.invoice_state,o.is_push,o.push_time,o.payer_id,r.time,o.payable_time,o.invoice_payment,
        COALESCE(TimeStampDiff(HOUR,r.urge_time,now())>=pa.param_value,1,0) isPayable,
        o.source_id,o.contract_id,d.discount_amount,l.code discountCode,d.id discountId,
        GROUP_CONCAT(s.source_name order by s.source_name asc) as address,r.name as payer,IFNULL(r2.name,r3.name) as singer,c.signer_id as singer_id,
        IFNULL(c.contract_code,'') contract_code,c.contract_type,c.is_organize,c.tax_point,s.project_id,
        w.audit_result ,o.intention , vu.real_name ,ttb.transfer_time ,o.bills_state ,o.bills_code,
        s.projectName,CONCAT(s.partitionName,s.code) partitionName,
        DATE_FORMAT(o.pay_time , '%Y-%m-%d') payTimeStr,ROUND((o.payment-o.payment*100/(100+c.tax_point)),2) tax_amount,
        sum(s.area) area,csr.source_id source_ids,r2.tax_org_name as taxOrgName,c.platform
        <include refid="searchListCondition"/>
    </select>

    <select id="countPaymentByMap" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        select sum(tt.payment) from
        (select o.payment
        <include refid="searchListCondition"/>) tt
    </select>

    <select id="getStatisticsVoByMap" parameterType="java.util.Map" resultType="cn.uone.bean.entity.business.bil.vo.BilOrderStatisticsVo">
        select ifnull(sum(tt.payment),0) totalPayment,ifnull(sum(tt.actual_payment),0) totalActualPayment from
        (select o.payment,o.actual_payment
        <include refid="searchListCondition"/>) tt
    </select>

    <sql id="searchListCondition">
        from t_bil_order o
        LEFT JOIN (SELECT oi.order_id ,MIN(oi.start_time) sDate,MAX(oi.end_time) eDate
        FROM t_bil_order_item oi GROUP BY oi.order_id) oit on  oit.order_id = o.id
        LEFT JOIN t_bil_discount_log l on o.discount_log_id = l.id
        LEFT JOIN t_bil_discount d on l.discount_id = d.id
        LEFT JOIN t_cont_contract c on o.contract_id = c.id
        LEFT JOIN t_cont_contract_source_rel csr ON csr.contract_id = c.id
        LEFT JOIN t_base_car car ON car.contract_source_id = csr.id
        LEFT JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_res_plan_partition p on s.partition_id = p.id
        LEFT JOIN t_res_project_para pa on p.project_id= pa.project_id and pa.param_code = 'XMCS_0052'
        LEFT JOIN v_sys_renter r on r.id = o.payer_id
        left join v_sys_user vu on vu.id = o.create_by
        LEFT JOIN t_cont_contract_info r2 on r2.contract_id = c.id
        LEFT JOIN v_sys_renter r3 on c.signer_id = r3.id
        LEFT JOIN (select t.rel_id,t.audit_result from (select wt.rel_id,wt.audit_result from t_bpm_workflow wt order by wt.update_date) t group by t.rel_id) w on w.rel_id=o.id
        left join t_bil_transfer ttb on ttb.order_id = o.id
        where 1=1 and (o.payment != 0 or o.order_type ='20')
        <if test="map.searchVo.fixSort == null or map.searchVo.fixSort == ''">
            #project_datascope#
        </if>
        <if test="map.searchVo.state != null and map.searchVo.state != ''">
            AND o.pay_state = #{map.searchVo.state}
        </if>
        <if test="map.searchVo.state == null or map.searchVo.state == ''">
            AND o.pay_state != '40'
        </if>
        <if test="map.searchVo.sDateStart != null">
            AND date_format(oit.sDate,'%Y-%m-%d') &gt;= date_format(#{map.searchVo.sDateStart},'%Y-%m-%d')
        </if>
        <if test="map.searchVo.sDateEnd != null">
            AND date_format(oit.sDate,'%Y-%m-%d') &lt;= date_format(#{map.searchVo.sDateEnd},'%Y-%m-%d')
        </if>
        <if test="map.searchVo.eDateStart != null">
            AND date_format(oit.eDate,'%Y-%m-%d') &gt;= date_format(#{map.searchVo.eDateStart},'%Y-%m-%d')
        </if>
        <if test="map.searchVo.eDateEnd != null">
            AND date_format(oit.eDate,'%Y-%m-%d') &lt;= date_format(#{map.searchVo.eDateEnd},'%Y-%m-%d')
        </if>
        <if test="map.searchVo.paperCode != null and map.searchVo.paperCode != ''">
            and c.paper_code like CONCAT('%', #{map.searchVo.paperCode}, '%')
        </if>
        <include refid="searchCondition"/>
        group by o.id
        <choose>
            <when test="map.searchVo.orderName !=null and map.searchVo.orderSortType != null ">
                <if test="map.searchVo.orderName == 'address'">
                    order by s.source_name ${map.searchVo.orderSortType}
                </if>
                <if test="map.searchVo.orderName == 'paperCode'">
                    order by c.paper_code  ${map.searchVo.orderSortType}
                </if>
                <if test="map.searchVo.orderName == 'sDate'">
                    order by oit.sDate  ${map.searchVo.orderSortType}
                </if>
                <if test="map.searchVo.orderName == 'eDate'">
                    order by oit.eDate  ${map.searchVo.orderSortType}
                </if>
                <if test="map.searchVo.orderName == 'payState'">
                    order by o.pay_state  ${map.searchVo.orderSortType}
                </if>
            </when>
            <otherwise>
                order by c.paper_code desc,oit.sDate desc
            </otherwise>
        </choose>
    </sql>

    <select id="selectBilOrderAndItemByMap" parameterType="java.util.Map" resultMap="BilOrderVoMap">
        select o.id,o.code,o.order_type,o.create_date,o.payment,o.pay_state,o.pay_time,oit.start_time as sDate,
        oit.end_time as eDate,o.payable_time,o.payable_payment,o.actual_payment,o.mer_order_id
        from t_bil_order o
        LEFT JOIN (SELECT oi.order_id ,MIN(oi.start_time) start_time,MAX(oi.end_time) end_time
        FROM t_bil_order_item oi GROUP BY oi.order_id) oit on  oit.order_id = o.id
        where 1=1
        <if test="map.fixSort == null or map.fixSort == ''">
            #project_datascope#
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            AND o.contract_id=#{map.contractId}
        </if>
        <if test="map.isDiscard != null and map.isDiscard != ''">
            AND o.is_discard=#{map.isDiscard}
        </if>
        order by oit.start_time asc
    </select>

    <select id="queryList" parameterType="java.util.Map" resultMap="BilOrderVoMap">
        select o.id,o.approval_state,o.code,
        o.create_date,o.order_type,o.payment,o.payable_payment,o.actual_payment,
        o.pay_state,o.pay_time,o.invoice_type,o.trade_code,o.transfer_mode,
        o.invoice_state,o.is_push,o.push_time,o.payer_id,r.time,o.payable_time,o.invoice_payment,
        COALESCE(TimeStampDiff(HOUR,r.urge_time,now())>=pa.param_value,1,0) isPayable,
        o.source_id,o.contract_id,d.discount_amount,l.code discountCode,d.id discountId,
        s.source_name as address,r.name as payer,r2.name as singer,
        IFNULL(c.contract_code,'') contract_code,c.contract_type,c.is_organize
        from t_bil_order o
        LEFT JOIN t_bil_discount_log l on o.discount_log_id = l.id
        LEFT JOIN t_bil_discount d on l.discount_id = d.id
        LEFT JOIN t_cont_contract c on o.contract_id = c.id
        LEFT JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_res_plan_partition p on s.partition_id = p.id
        LEFT JOIN t_res_project_para pa on p.project_id= pa.project_id and pa.param_code = 'XMCS_0052'
        LEFT JOIN v_sys_renter r on r.id = o.payer_id
        LEFT JOIN v_sys_renter r2 on r2.id = c.signer_id
        where 1=1
        <include refid="searchCondition"/>
        order by o.is_push desc,o.push_time desc,o.create_date desc
    </select>



    <select id="bilExport" parameterType="java.util.Map" resultMap="BilOrderVoMap">
        select o.id,o.approval_state,IF(s.source_type='2',(SELECT bc.num FROM t_base_car bc,t_cont_contract_source_rel sr
        WHERE sr.id=bc.contract_source_id AND sr.contract_id=c.id LIMIT 1),s.code )as address,o.code,
        o.create_date,o.order_type,o.payment,o.payable_payment,o.actual_payment,o.pay_way,o.remark,
        o.pay_state,o.pay_time,o.invoice_type,o.trade_code,o.invoice_payment,o.mer_order_id,
        o.invoice_state,o.is_push,o.payer_id,r.time,e.name,e.taxpayer_code,e.enterprise_address,e.enterprise_tel,
        e.enterprise_account,e.enterprise_bank,s.projectName,s.partitionName,c.contract_code,csr.cash_pledge,IF(l.price is NULL,(SELECT hl.price FROM
        (SELECT * FROM t_cont_rent_ladder ORDER BY start_date DESC LIMIT 1)hl WHERE hl.contract_source_id=csr.id),l.price)price,
        (SELECT MAX(end_time) FROM t_bil_order_item WHERE order_id=o.id AND o.order_type in('20','240','30','301','501')) end_date,
        (SELECT MIN(start_time) FROM t_bil_order_item WHERE order_id=o.id AND o.order_type in('20','240','30','301','501')) start_date,d.discount_amount
        ,bt.transfer_time,r.`name` as signer ,r.tel ,c.pay_type ,s.area,
        DATE_FORMAT((SELECT MAX(end_time) FROM t_bil_order_item WHERE order_id=o.id AND o.order_type in('20','240','30','301','501')),'%Y-%m-%d') endTimeStr,
        DATE_FORMAT((SELECT MIN(start_time) FROM t_bil_order_item WHERE order_id=o.id AND o.order_type in('20','240','30','301','501')),'%Y-%m-%d') startTimeStr,
        DATE_FORMAT(c.start_date ,'%Y-%m-%d') cStartTime ,
        DATE_FORMAT(o.pay_time , '%Y-%m-%d') payTimeStr,
        DATE_FORMAT(c.end_date , '%Y-%m-%d') cEndTime,
        o.bills_code,o.order_type, c.state cState,tbi.tax_payment,tbi.tax_number,tbi.tax_amount ,
        o.contract_id,c.paper_code paperCode,r2.tax_org_name as taxOrgName,c.platform
        from t_bil_order o
        LEFT JOIN t_cont_contract c on o.contract_id = c.id
        LEFT JOIN (SELECT * FROM t_cont_contract_source_rel csr GROUP BY csr.contract_id)csr on csr.contract_id=c.id
        LEFT JOIN t_cont_rent_ladder l ON l.contract_source_id=csr.id AND o.create_date BETWEEN l.start_date AND l.end_date
        LEFT JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_res_plan_partition p on s.partition_id = p.id
        LEFT JOIN v_sys_renter r on r.id = o.payer_id
        LEFT JOIN t_cont_contract_info r2 on r2.contract_id = c.id
        LEFT JOIN t_base_enterprise e on e.renter_id=c.signer_id
        LEFT JOIN t_bil_discount_log bdl on  bdl.order_id=o.id
        LEFT JOIN t_bil_discount d on d.id=bdl.discount_id
        LEFT JOIN t_bil_transfer bt on bt.order_id=o.id
        left join t_bil_invoice tbi on tbi.order_id = o.id
        where 1=1 and o.payment != 0 and o.pay_state not in('40')
        #project_datascope#
        <include refid="searchCondition"/>
        order by o.is_push desc,o.push_time desc,o.create_date desc
    </select>

    <select id="getBillsPdf" parameterType="java.util.Map" resultMap="BilOrderVoMap">
        select r.name as signer , r.tel , o.bills_code , f.url address
        from t_sys_file f , t_bil_order o
        LEFT JOIN t_cont_contract c on o.contract_id = c.id
        LEFT JOIN (SELECT * FROM t_cont_contract_source_rel csr GROUP BY csr.contract_id)csr on csr.contract_id=c.id
        LEFT JOIN t_cont_rent_ladder l ON l.contract_source_id=csr.id AND o.create_date BETWEEN l.start_date AND l.end_date
        LEFT JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_res_plan_partition p on s.partition_id = p.id
        LEFT JOIN v_sys_renter r on r.id = o.payer_id
        LEFT JOIN t_cont_contract_info r2 on r2.contract_id = c.id
        LEFT JOIN t_base_enterprise e on e.renter_id=c.signer_id
        LEFT JOIN t_bil_discount_log bdl on  bdl.order_id=o.id
        LEFT JOIN t_bil_discount d on d.id=bdl.discount_id
        LEFT JOIN t_bil_transfer bt on bt.order_id=o.id
        where 1=1 and o.payment != 0 and f.from_id = o.id and f.type = '50'
        #project_datascope#
        <include refid="searchCondition"/>
        order by o.is_push desc,o.push_time desc,o.create_date desc
    </select>


    <select id="finaceExport" parameterType="java.util.Map" resultMap="BilOrderVoMap">
        select o.pay_state ,s.projectName ,s.partitionName ,o.id ,
        s.`code`  as address ,r.`name` as signer,
        DATE_FORMAT((SELECT MIN(start_time) FROM t_bil_order_item WHERE order_id=o.id AND o.order_type='20'),'%Y-%m-%d') startTimeStr,
        DATE_FORMAT((SELECT MAX(end_time) FROM t_bil_order_item WHERE order_id=o.id AND o.order_type='20'),'%Y-%m-%d') endTimeStr,
        DATE_FORMAT(oi.start_time,'%Y-%m-%d') cur_month,
        oi.start_time start_date,
        s.area , csr.cash_pledge, oi.payment mt_payment ,
        round(oi.payment/(1+ rpi.`param_value`) ,2) noTaxPrice, o.payment cur_payment ,
        if(o.pay_state ='20',o.payment ,0) cur_pay ,
        if(wqsszj.payment is null,0,wqsszj.payment) sswq,
        if(wqyszj.payment is null,0,wqyszj.payment) yswq,
        o.`code` ,
        DATE_FORMAT(c.start_date ,'%Y-%m-%d') cStartTime ,
        DATE_FORMAT(c.end_date , '%Y-%m-%d') cEndTime ,
        o.remark,
        rpi.`param_value` tax,
        oi.start_time,
        c.pay_type,
        c.state cState,
        tbr.checkout_date,
        oi.payment price
        from t_bil_order_item oi
        left join t_bil_order o on o.id = oi.order_id
        left join (select t.source_id , sum(t.payment) payment from t_bil_order t where 1 = 1
        <if test="map.searchVo.finacelExportDate == null">
            and DATE_FORMAT(t.payable_time,'%Y%m') &lt; DATE_FORMAT(CURDATE(),'%Y%m')
        </if>
        <if test="map.searchVo.finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(t.payable_time,'%Y%m') < DATE_FORMAT(#{map.searchVo.finacelExportDate},'%Y%m') ]]>
        </if>
        <if test="map.searchVo.finacelExportDate == null">
            and DATE_FORMAT(t.pay_time,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
        </if>
        <if test="map.searchVo.finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(t.pay_time,'%Y%m') = DATE_FORMAT(#{map.searchVo.finacelExportDate},'%Y%m') ]]>
        </if>
        and t.order_type ='20' and t.is_push = '1' and t.pay_state = '20' group by t.source_id
        ) wqsszj on wqsszj.source_id = o.source_id
        left join ( select t.source_id , sum(t.payment) payment from t_bil_order t where 1=1
        <if test="map.searchVo.finacelExportDate == null">
            and DATE_FORMAT(t.payable_time,'%Y%m') &lt; DATE_FORMAT(CURDATE(),'%Y%m')
        </if>
        <if test="map.searchVo.finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(t.payable_time,'%Y%m') < DATE_FORMAT(#{map.searchVo.finacelExportDate},'%Y%m') ]]>
        </if>
        and t.order_type ='20' and t.is_push = '1' and t.pay_state = '10' group by t.source_id ) wqyszj on wqyszj.source_id = o.source_id
        LEFT JOIN t_cont_contract c on o.contract_id = c.id
        LEFT JOIN (SELECT * FROM t_cont_contract_source_rel csr GROUP BY csr.contract_id)csr on csr.contract_id=c.id
        LEFT JOIN t_biz_release tbr ON tbr.contract_id = c.id
        LEFT JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_res_plan_partition p on s.partition_id = p.id
        LEFT JOIN t_res_project_para rpi ON rpi.`project_id`=s.`project_id` AND rpi.`param_code`='XMCS_0101'
        LEFT JOIN v_sys_renter r on r.id = o.payer_id
        LEFT JOIN t_cont_contract_info r2 on r2.contract_id = c.id
        LEFT JOIN t_base_enterprise e on e.renter_id=c.signer_id
        LEFT JOIN t_bil_discount_log bdl on  bdl.order_id=o.id
        LEFT JOIN t_bil_discount d on d.id=bdl.discount_id
        LEFT JOIN t_bil_transfer bt on bt.order_id=o.id
        where 1=1 and o.payment != 0 and o.order_type = '20' and oi.order_id = o.id

        <if test="map.searchVo.finacelExportDate == null">
            and DATE_FORMAT(oi.start_time,'%Y%m')=DATE_FORMAT(CURDATE(),'%Y%m')
        </if>
        <if test="map.searchVo.finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(oi.start_time,'%Y%m') = DATE_FORMAT(#{map.searchVo.finacelExportDate},'%Y%m') ]]>
        </if>
        #project_datascope#
        <include refid="searchCondition"/>
        order by o.is_push desc,o.push_time desc,o.create_date desc
    </select>

    <select id="countEmpOrderByMap" resultType="java.math.BigDecimal">
        select sum(payment)
        from t_bil_order o
        LEFT JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_cont_contract c on o.contract_id = c.id
        LEFT JOIN t_emp_user u on o.payer_id = u.renter_id
        LEFT JOIN t_cont_frame_contract f on f.id=c.frame_contract_id
        where 1=1 and o.pay_state in ('10','20')
        <include refid="searchEmpCondition"/>
    </select>

    <!--分页查询(企业端)-->
    <select id="selectEmpOrderByMap" parameterType="java.util.Map" resultMap="BilOrderVoMap">
        select o.id,o.code,o.create_date,s.source_name as address,o.invoice_payment,
        u.name as emp_name,u.tel as emp_tel,o.payment,o.payable_payment,o.actual_payment,
        o.order_type,o.pay_state,if(o.payer_id = c.signer_id,1,2) as payer_type
        from t_bil_order o
        LEFT JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_cont_contract c on o.contract_id = c.id
        LEFT JOIN t_emp_user u on o.payer_id = u.renter_id
        LEFT JOIN t_cont_frame_contract f on f.id=c.frame_contract_id
        where 1=1 and o.pay_state in ('10','20') and f.state='1'
        <include refid="searchEmpCondition"/>
        order by o.is_push desc,o.push_time desc,o.create_date desc
    </select>

    <sql id="searchEmpCondition">
        <if test="map.searchVo.userId != null and map.searchVo.userId != ''">
            AND c.signer_id = #{map.searchVo.userId}
        </if>
        <if test="map.searchVo.orderCode != null and map.searchVo.orderCode != ''">
            and o.code like CONCAT('%', #{map.searchVo.orderCode}, '%')
        </if>
        <if test="map.searchVo.contractCode != null and map.searchVo.contractCode != ''">
            and (c.contract_code like CONCAT('%', #{map.searchVo.contractCode}, '%')
            or f.code like CONCAT('%', #{map.searchVo.contractCode}, '%'))
        </if>
        <if test="map.searchVo.empName != null and map.searchVo.empName != ''">
            and u.name like CONCAT('%', #{map.searchVo.empName}, '%')
        </if>
        <if test="map.searchVo.empTel != null and map.searchVo.empTel != ''">
            and u.tel like CONCAT('%', #{map.searchVo.empTel}, '%')
        </if>
        <if test="map.searchVo.sourceName != null and map.searchVo.sourceName != ''">
            and s.source_name like CONCAT('%', #{map.searchVo.sourceName}, '%')
        </if>
        <if test="map.searchVo.state != null and map.searchVo.state != ''">
            AND o.pay_state = #{map.searchVo.state}
        </if>
        <if test="map.searchVo.isPush != null and !map.searchVo.isPush.isEmpty()">
            and o.is_push = #{map.searchVo.isPush}
        </if>
    </sql>

    <sql id="searchCondition">
        <if test="map.searchVo.isFixLastOrder != null and map.searchVo.isFixLastOrder != ''">
            and o.pay_state = '10' AND o.order_type in('20','21','40') and o.is_discard ='0'
            AND NOT EXISTS (SELECT 1 FROM t_approval_commit ac
            LEFT JOIN t_approval_other_rel aor ON aor.approval_id = ac.id
            WHERE  o.id =aor.other_id AND ac.type in ('6','15')  AND ac.`status` in ('0','1','2'))
            AND NOT EXISTS(SELECT 1 FROM t_cont_par cp WHERE cp.type = '5' AND cp.value='2' AND cp.contract_id = o.contract_id)
            AND EXISTS (SELECT 1 FROM (SELECT * FROM(SELECT  o.source_id,o.id,o.order_type,o.contract_id FROM (SELECT * FROM t_bil_order WHERE
            order_type in('20','21','40'))o
            LEFT JOIN t_bil_order_item i ON i.order_id = o.id
            ORDER BY i.end_time DESC)o  GROUP BY o.order_type,o.contract_id,o.source_id)wai WHERE wai.id = o.id )
        </if>
        <if test="map.searchVo.projectId != null and map.searchVo.projectId != ''">
            AND s.project_id = #{map.searchVo.projectId}
        </if>
        <if test="map.searchVo.taxOrgName != null and map.searchVo.taxOrgName != ''">
            AND r2.tax_org_name  like CONCAT('%', #{map.searchVo.taxOrgName}, '%')
        </if>
        <if test="map.searchVo.platform != null and map.searchVo.platform != ''">
            AND c.platform = #{map.searchVo.platform}
        </if>
        <if test="map.searchVo.floor != null and map.searchVo.floor != ''">
            and s.floor=#{map.searchVo.floor}
        </if>
        <if test="map.searchVo.partitionId != null and map.searchVo.partitionId != ''">
            AND s.partition_id = #{map.searchVo.partitionId}
        </if>
        <if test="map.searchVo.keyWord != null and map.searchVo.keyWord != ''">
            and (
            s.source_name like CONCAT('%', #{map.searchVo.keyWord}, '%')
            or r.name like CONCAT('%', #{map.searchVo.keyWord}, '%')
            or r2.name like CONCAT('%', #{map.searchVo.keyWord}, '%')
            or r.tel like CONCAT('%', #{map.searchVo.keyWord}, '%')
            or r2.tel like CONCAT('%', #{map.searchVo.keyWord}, '%')
            or o.bills_code like CONCAT('%', #{map.searchVo.keyWord}, '%')
            <if test="map.searchVo.isCarKeyWord != null and map.searchVo.isCarKeyWord != ''">
                or car.num like CONCAT('%',#{map.searchVo.keyWord}, '%')
            </if>
            )
        </if>
        <if test="map.searchVo.propertyNature != null and map.searchVo.propertyNature != ''">
            AND p.property_nature = #{map.searchVo.propertyNature}
        </if>
        <if test="map.searchVo.contractCode != null and map.searchVo.contractCode != ''">
            AND c.contract_code like CONCAT('%', #{map.searchVo.contractCode}, '%')
        </if>
        <if test="map.searchVo.platform != null and map.searchVo.platform != ''">
            AND c.platform = #{map.searchVo.platform}
        </if>
        <if test="map.searchVo.platformCode != null and map.searchVo.platformCode != ''">
            AND c.platform_code LIKE CONCAT('%', #{map.searchVo.platformCode}, '%')
        </if>
        <if test="map.searchVo.isOrganize != null and map.searchVo.isOrganize != ''">
            AND c.is_organize = #{map.searchVo.isOrganize}
        </if>
        <if test="map.searchVo.orderCode != null and map.searchVo.orderCode != ''">
            AND o.code like CONCAT('%', #{map.searchVo.orderCode}, '%')
        </if>
        <if test="map.searchVo.orderType != null and map.searchVo.orderType != ''">
            AND o.order_type = #{map.searchVo.orderType}
        </if>
        <if test="map.searchVo.orderItemType != null and map.searchVo.orderItemType != ''">
            AND EXISTS  ( select 1 from t_bil_order_item oi where oi.order_id = o.id and oi.order_item_type = #{map.searchVo.orderItemType} )
        </if>
        <if test="map.searchVo.billsState != null and map.searchVo.billsState != ''">
            AND o.bills_state = #{map.searchVo.billsState}
        </if>
        <if test="map.searchVo.intention != null and map.searchVo.intention != ''">
            AND o.intention in ('1','5','2','4')
        </if>
        <if test="map.searchVo.billInfo != null and map.searchVo.billInfo != ''">
            AND o.order_type in ('5','10')
        </if>
        <if test="map.searchVo.daily != null and map.searchVo.daily != ''">
            and o.trade_code is not null
        </if>
        <if test="map.searchVo.approvalState != null and map.searchVo.approvalState != ''">
            AND (o.approval_state = #{map.searchVo.approvalState}
            <if test="map.searchVo.approvalState.indexOf('9'.toString()) != -1">
                or o.approval_state is null
            </if>
            )
        </if>
        <if test="map.searchVo.orderTypes != null and map.searchVo.orderTypes.size() > 0">
            AND o.order_type in
            <foreach collection="map.searchVo.orderTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.searchVo.approvalStates != null and map.searchVo.approvalStates.size() > 0">
            AND (o.approval_state in
            <foreach collection="map.searchVo.approvalStates" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
            <if test="map.searchVo.approvalStates.indexOf('9'.toString()) != -1">
                or o.approval_state is null
            </if>
            )
        </if>
        <if test="map.searchVo.isApproval != null and map.searchVo.isApproval != ''">
            <if test="map.searchVo.isApproval=='0'.toString()">
                and (o.approval_state is null or o.approval_state in ('2','3','4'))
            </if>
        </if>
        <if test="map.searchVo.state != null and map.searchVo.state != ''">
            AND o.pay_state = #{map.searchVo.state}
        </if>
        <if test="map.searchVo.states != null and map.searchVo.states.size() > 0">
            AND o.pay_state in
            <foreach collection="map.searchVo.states" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.searchVo.startCreateDate != null">
            AND <![CDATA[date(o.create_date) >= date(#{map.searchVo.startCreateDate}) ]]>
        </if>
        <if test="map.searchVo.endCreateDate != null">
            AND <![CDATA[date(o.create_date) <= date(#{map.searchVo.endCreateDate}) ]]>
        </if>
        <if test="map.searchVo.startPayTime != null">
            AND <![CDATA[date(o.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
        </if>
        <if test="map.searchVo.endPayTime != null">
            AND <![CDATA[date(o.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
        </if>
        <if test="map.searchVo.isPush != null and !map.searchVo.isPush.isEmpty()">
            and o.is_push = #{map.searchVo.isPush}
        </if>
        <if test="map.searchVo.isPush == null">
            and o.is_push = '1'
        </if>
        <if test="map.searchVo.startPushTime != null">
            AND <![CDATA[date(o.push_time) >= date(#{map.searchVo.startPushTime}) ]]>
        </if>
        <if test="map.searchVo.endPushTime != null">
            AND <![CDATA[date(o.push_time) <= date(#{map.searchVo.endPushTime}) ]]>
        </if>
        <if test="map.searchVo.startPayableTime != null">
            AND <![CDATA[date(o.payable_time) >= date(#{map.searchVo.startPayableTime}) ]]>
        </if>
        <if test="map.searchVo.endPayableTime != null">
            AND <![CDATA[date(o.payable_time) <= date(#{map.searchVo.endPayableTime}) ]]>
        </if>
        <if test="map.searchVo.invoiceState != null and map.searchVo.invoiceState != ''">
            AND o.invoice_state = #{map.searchVo.invoiceState}
        </if>
        <if test="map.searchVo.invoiceType != null and map.searchVo.invoiceType != ''">
            AND o.invoice_type = #{map.searchVo.invoiceType}
        </if>
        <if test="map.searchVo.payWay != null and map.searchVo.payWay != ''">
            AND o.pay_way = #{map.searchVo.payWay}
        </if>
        <if test="map.searchVo.isDiscard != null and map.searchVo.isDiscard != ''">
            AND o.is_discard = #{map.searchVo.isDiscard}
        </if>
        <if test="map.searchVo.isInitial != null and map.searchVo.isInitial != ''">
            AND o.is_initial = #{map.searchVo.isInitial}
        </if>
        <if test="map.searchVo.userId != null and map.searchVo.userId != ''">
            AND o.payer_id = #{map.searchVo.userId}
        </if>
        <if test="map.searchVo.isFirst != null and map.searchVo.isFirst != ''">
            AND o.is_first = #{map.searchVo.isFirst}
        </if>
        <if test="map.searchVo.contractId != null and map.searchVo.contractId != ''">
            AND o.contract_id = #{map.searchVo.contractId}
        </if>
        <if test="map.searchVo.sourceId != null and map.searchVo.sourceId != ''">
            AND o.source_id = #{map.searchVo.sourceId}
        </if>
        <if test="map.searchVo.payer != null and map.searchVo.payer != ''">
            AND r.name like CONCAT('%', #{map.searchVo.payer}, '%')
        </if>
        <if test="map.searchVo.tradeCode != null and map.searchVo.tradeCode != ''">
            AND o.trade_code = #{map.searchVo.tradeCode}
        </if>
        <if test="map.searchVo.isCar != null and !map.searchVo.isCar.isEmpty()">
            <if test="map.searchVo.isCar=='0'.toString()">
                and s.source_type not in ('2')
            </if>
            <if test="map.searchVo.isCar=='1'.toString()">
                and s.source_type in ('2')
            </if>
        </if>
        <if test="map.searchVo.isDiscount != null and !map.searchVo.isDiscount.isEmpty()">
            <if test="map.searchVo.isDiscount=='0'.toString()">
                and o.discount_log_id is null
            </if>
            <if test="map.searchVo.isDiscount=='1'.toString()">
                and o.discount_log_id is not null
            </if>
        </if>
        <if test="map.searchVo.ids != null and map.searchVo.ids.size() > 0">
            AND o.id in
            <foreach collection="map.searchVo.ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.searchVo.transferMode != null and map.searchVo.transferMode != ''">
            AND o.transfer_mode = #{map.searchVo.transferMode}
        </if>
        <!--<if test="map.searchVo.isShort != null and map.searchVo.isShort != ''">
            and c.is_short = #{map.searchVo.isShort}
        </if>-->
        <if test="map.searchVo.contractFlag != null and map.searchVo.contractFlag != ''">
            AND o.contract_id is null
        </if>
        <if test="map.searchVo.financeExport != null and map.searchVo.financeExport != ''">
            and (c.state in ('6','8','14','16')
               or (c.state = '7' AND <![CDATA[DATE_FORMAT(tbr.checkout_date,'%Y%m') = DATE_FORMAT(#{map.searchVo.finacelExportDate},'%Y%m') ]]>))
        </if>
        <if test="map.searchVo.itemMonth != null and map.searchVo.itemMonth != ''">
            AND ( (o.order_type not in('5','10') and DATE_FORMAT((select min(start_time) from t_bil_order_item where order_id=o.id),'%Y-%m') = #{map.searchVo.itemMonth})
                 or (o.order_type in('5','10') and DATE_FORMAT(o.payable_time,'%Y-%m') = #{map.searchVo.itemMonth})
            )
        </if>
    </sql>

    <!--查询退款信息-->
    <select id="selectRefundInfoByOrderId" resultType="java.util.HashMap">
        select o.payment,o.actual_payment as actualPayment,
        co.bank_deposit as bankDeposit,co.bank_name as bankName,co.account
        from t_bil_order o LEFT JOIN t_biz_check_out co on o.id=co.order_id
        where 1=1
        <if test="map.orderId != null and map.orderId != ''">
            AND o.id = #{map.orderId}
        </if>
    </select>

    <!--查询企业合同下的所有未付帐单数-->
    <select id="countUnPayNum" resultType="java.util.HashMap">
        select
        count(1) as unPayNum
        from t_bil_order o
        LEFT JOIN t_cont_contract c on c.id=o.contract_id
        where 1=1 and o.pay_state = '10' and o.is_push='1' and o.order_type in("5","20","10","110")
        <if test="map.frameContractId != null and map.frameContractId != ''">
            AND c.frame_contract_id = #{map.frameContractId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            AND o.contract_id = #{map.contractId}
        </if>
    </select>


    <!--查询退款信息-->
    <update id="selectRefundInfoByOrderId1">
        UPDATE t_bil_order
            SET actual_payment = NULL,pay_time = NULL
            WHERE
                id =  #{map.id}
    </update>

    <!--获取需要转账的非生活费用账单-->
    <select id="getAuditOrders" resultMap="BaseResultMap">
        select
        DISTINCT o.*
        from t_bil_order o
        LEFT JOIN t_bil_order_item i ON o.id = i.order_id
        LEFT JOIN t_bil_transfer t ON o.id = t.order_id
        where o.order_type not in ('30','40','80','110','130','140','180','190','210','211','220')
        and t.id is null
        and o.pay_state in('20')
        and i.transfer_state = '0'
        AND o.transfer_mode = '2'
        and o.payment > 0
        and o.pay_time &gt;= date('2019-10-01')
        and o.pay_time &lt;= DATE_SUB(NOW(),INTERVAL 1 DAY)
        <!-- 第三方合同不转 -->
        and EXISTS (select 1 from t_cont_contract c where o.contract_id = c.id and c.platform  &lt;&gt; '1')
        and not EXISTS (select 1 from t_res_source s1,t_cont_contract_source_rel
        cs,t_cont_contract c,t_cont_temp tp
        where 1=1
        and tp.project_id = s1.project_id
        and cs.source_id = s1.id
        and cs.contract_id = c.id
        and c.id = o.contract_id
        and c.is_organize = '0'
        and c.contract_type = '1'
        and tp.id = c.contract_templet_id
        and tp.is_push_cont_other='1'
        and tp.cont_platform_name='2'
        and s1.id = o.source_id)
    </select>

    <!--据账单类型及开始时间查询子账单数量-->
    <select id="countOrderByTypeAndTime" resultType="java.lang.Integer">
        select count(1) from t_bil_order o
        left join t_cont_contract_source_rel r on o.contract_id = r.contract_id
        where o.pay_state !='40'
        <if test="map.type != null and map.type != ''">
            and o.order_type = #{map.type}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        <if test="map.isFirst != null and map.isFirst != ''">
            and o.is_first = #{map.isFirst}
        </if>
    </select>

    <select id="isExitOrder" parameterType="java.util.Map" resultMap="BilOrderVoMap">
        select t1.* from t_bil_order t1
        inner join t_bil_order_item t2 on t2.order_id = t1.id
        where t1.pay_state != '40'
        and t2.payment > 0
        and t2.order_item_type = #{map.orderItemType}
        and t1.contract_id = #{map.contractId}
        and t1.source_id = #{map.sourceId}
        and ((t2.start_time &lt;= str_to_date(#{map.endtime},'%Y-%m-%d')
        and t2.end_time  &gt;=  str_to_date(#{map.endtime},'%Y-%m-%d')) or
        (t2.end_time  &gt;=  str_to_date(#{map.starttime},'%Y-%m-%d')
        and t2.start_time &lt;= str_to_date(#{map.starttime},'%Y-%m-%d')))
    </select>

    <select id="getRefundDoposit" resultType="java.math.BigDecimal">
        select sum(oi.payment) from t_bil_order o LEFT JOIN t_bil_order_item oi on o.id = oi.order_id
        where o.order_type in ('130','140','150','210','30','40','211') and o.is_discard='0'  and oi.order_item_type in ('110','115')
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
    </select>

    <select id="getRefundTurnover" resultType="java.math.BigDecimal">
        select sum(oi.payment) from t_bil_order o LEFT JOIN t_bil_order_item oi on o.id = oi.order_id
        where o.order_type='150' and o.is_initial =1 and o.is_discard='0'
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
    </select>

    <select id="getDoposit" resultType="java.math.BigDecimal">
        select sum(oi.payment) from t_bil_order o LEFT JOIN t_bil_order_item oi on o.id = oi.order_id
        where o.order_type='5' and o.pay_state='20' and oi.order_item_type='110'
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        and o.is_discard = '0'
    </select>

    <select id="getIncDoposit" resultType="java.math.BigDecimal">
        select sum(oi.payment) from t_bil_order o LEFT JOIN t_bil_order_item oi on o.id = oi.order_id
        where o.order_type='5'  and o.pay_state='20'
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        and o.is_discard = '0'
    </select>


    <select id="getTurnover" resultType="java.math.BigDecimal">
        select sum(oi.payment) from t_bil_order o LEFT JOIN t_bil_order_item oi on o.id = oi.order_id
        where o.order_type='6'  and o.pay_state='20'
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        and o.is_discard = '0'
    </select>

    <select id="getReleaseOrderCode" resultType="java.lang.String">
        select group_concat(code)as c from t_bil_order o where 1=1
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        and o.order_type in ('130','140','150','210','30','40','211') and is_initial =1 and is_discard=0
    </select>

    <select id="getCancelOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o inner join t_bil_order_item oi
        where o.pay_state='10'
        and o.order_type in ('20','30','40','80')
        and DATE_FORMAT(oi.end_time, '%Y-%m-%d') >= DATE_FORMAT(#{map.checkoutDate}, '%Y-%m-%d')
        and o.id = oi.order_id
        and o.contract_id = #{map.contractId}
        and o.is_discard = '0'
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
    </select>

    <update id="batchCancelRent">
        update t_bil_order o join t_bil_order_item oi
        set o.pay_state='40'
        where o.pay_state='10'
        and o.order_type='20'
        and DATE_FORMAT(oi.end_time, '%Y-%m-%d') >= DATE_FORMAT(#{map.checkoutDate}, '%Y-%m-%d')
        and o.id = oi.order_id
        and o.contract_id = #{map.contractId}
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
    </update>

    <update id="batchCancelLiving">
        update t_bil_order o
        set o.pay_state='40'
        where o.pay_state='10'
        and o.order_type in ('30','40','80')
        and DATE_FORMAT(o.create_date, '%Y-%m-%d') &gt;= DATE_FORMAT(DATE_ADD(#{map.checkoutDate},interval -day(#{map.checkoutDate})+1 day), '%Y-%m-%d')
        and o.contract_id = #{map.contractId}
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
    </update>

    <update id="batchCancelDeposit">
        update t_bil_order o
        set o.pay_state='40'
        where o.pay_state='10'
        and o.order_type in ('5','6')
        and o.contract_id = #{map.contractId}
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
    </update>

    <select id="getUnPayRentOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o left join t_bil_order_item oi on o.id = oi.order_id
        where 1=1 and o.order_type='20' and o.pay_state in ('10','30')
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        and DATE_FORMAT(oi.end_time , '%Y-%m-%d')  &lt; DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and o.id not in (
        select o1.id from t_bil_order o1 left join t_bil_order_item oi1 on o1.id = oi1.order_id
        where 1=1
        and DATE_FORMAT(oi1.start_time , '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and DATE_FORMAT(oi1.end_time , '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        <if test="map.sourceId != null and map.sourceId != ''">
            and o1.source_id = #{map.sourceId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and o1.contract_id = #{map.contractId}
        </if>
        )
        and o.is_discard = '0'
    </select>

    <select id="getUnPayLiveingOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o left join t_bil_order_item oi on o.id = oi.order_id
        where 1=1 and o.order_type in ('30','40','80') and o.pay_state in ('10','30')
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        and DATE_FORMAT(oi.end_time , '%Y-%m-%d')  &lt; DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and o.id not in (
        select o1.id from t_bil_order o1 inner join t_bil_order_item oi1 on o1.id = oi1.order_id
        where 1=1
        and DATE_FORMAT(oi1.start_time , '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and DATE_FORMAT(oi1.end_time , '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        <if test="map.sourceId != null and map.sourceId != ''">
            and o1.source_id = #{map.sourceId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and o1.contract_id = #{map.contractId}
        </if>
        )
        and o.is_discard = '0'
    </select>

    <select id="getUnPaySubsidyOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o left join t_bil_order_item oi on o.id = oi.order_id
        where 1=1 and o.order_type in ('21') and o.pay_state in ('10','30')
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        and DATE_FORMAT(oi.end_time , '%Y-%m-%d')  &lt; DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and o.id not in (
        select o1.id from t_bil_order o1 inner join t_bil_order_item oi1 on o1.id = oi1.order_id
        where 1=1
        and DATE_FORMAT(oi1.start_time , '%Y-%m-%d') &lt;= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and DATE_FORMAT(oi1.end_time , '%Y-%m-%d') &gt;= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        <if test="map.sourceId != null and map.sourceId != ''">
            and o1.source_id = #{map.sourceId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and o1.contract_id = #{map.contractId}
        </if>
        )
        and o.is_discard = '0'
        and o.payer_id not in ('78fa7355b70b1740d409dd8d949f92cc','b1a2dd56565100d3be707973e6e8c376')
        union
        select o.* from t_bil_order o
        INNER JOIN t_bil_order_item oi on o.id = oi.order_id
        where 1=1 and o.order_type = '21'
        and o.pay_state in ('10','30')
        and DATE_FORMAT(oi.end_time,'%Y-%m-%d')  >= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d') >= DATE_FORMAT( oi.start_time,'%Y-%m-%d')
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        and o.is_discard = '0'
        and o.payer_id not in ('78fa7355b70b1740d409dd8d949f92cc','b1a2dd56565100d3be707973e6e8c376')
        group by o.id limit 1
    </select>

    <select id="selectOrderIncludeItem" resultMap="IncludeItemResultMap">
        select <include refid="Include_Item_Column_List"/>
        from  t_bil_order_item oi,t_bil_order o
        left join (select ABS(sum(payment)) discount_amount,order_id from t_bil_order_item where order_item_type = '360' group by order_id) dis on dis.order_id = o.id
        left join ( select dl.id,d.discount_amount from t_bil_discount_log dl,t_bil_discount d where dl.discount_id =d.id) d on d.id = o.discount_log_id
        where o.id = oi.order_id AND o.pay_state &lt;&gt; '40'
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.orderType != null and map.orderType != ''">
            AND o.order_type = #{map.orderType}
        </if>
        <if test="map.orderItemType != null and map.orderItemType != ''">
            AND oi.order_item_type = #{map.orderItemType}
        </if>
        <if test="map.state != null and map.state != ''">
            AND o.pay_state = #{map.state}
        </if>
        <if test="map.startCreateDate != null">
            AND <![CDATA[date(o.create_date) >= date(#{map.startCreateDate}) ]]>
        </if>
        <if test="map.endCreateDate != null">
            AND <![CDATA[date(o.create_date) <= date(#{map.endCreateDate}) ]]>
        </if>
        <if test="map.startCreateDate != null">
            AND <![CDATA[date(o.payable_time) >= date(#{map.startPayableTime}) ]]>
        </if>
        <if test="map.endCreateDate != null">
            AND <![CDATA[date(o.payable_time) <= date(#{map.endPayableTime}) ]]>
        </if>
        <if test="map.startPayTime != null">
            AND <![CDATA[date(o.pay_time) >= date(#{map.startPayTime}) ]]>
        </if>
        <if test="map.endPayTime != null">
            AND <![CDATA[date(o.pay_time) <= date(#{map.endPayTime}) ]]>
        </if>

        <if test="map.itemEndDate != null">
            AND <![CDATA[date(oi.end_time) <= date(#{map.itemEndDate}) ]]>
        </if>

        <if test="map.maxItemStartDate != null">
            AND <![CDATA[date(oi.start_time) <= date(#{map.maxItemStartDate}) ]]>
        </if>
        <if test="map.itemMonth != null ">
            AND date_format(oi.start_time,'%Y-%m') = #{map.itemMonth}
        </if>

        <if test="map.states != null and map.states.size() > 0">
            AND o.pay_state in
            <foreach collection="map.states" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="map.orderItemTypes != null and map.orderItemTypes != ''">
            AND oi.order_item_type  in
            <foreach collection="map.orderItemTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.orderTypes != null and map.orderTypes.size() > 0">
            AND o.order_type in
            <foreach collection="map.orderTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </select>


    <!-- 查询部分支付 -->
    <select id="selectPartOrderIncludeItem" resultMap="IncludeItemResultMap">
        select o.id,
            o.create_by,
            o.create_date,
            o.update_by,
            o.update_date,
            o.code,o.merge_code, o.source_id, o.contract_id, o.discount_log_id, o.payment,o.payable_payment,
            ifnull(t.payment,o.actual_payment) actual_payment,
            o.pay_way,
            o.pay_time,o.pay_state,o.order_type,o.trade_code,o.payer_id,o.invoice_type,
            o.invoice_state,o.is_push,o.is_first,o.is_discard,o.is_initial,o.remark,
            o.transfer_state,o.apply_inv_time,
            oi.id as "oi.id",
            oi.create_by as "oi.create_by",
            oi.create_date as "oi.create_date",
            oi.update_by as "oi.update_by",
            oi.update_date as "oi.update_date",
            oi.order_id as "oi.order_id",oi.code as "oi.code",oi.payment as "oi.payment",oi.order_item_type as "oi.order_item_type",
            oi.start_time as "oi.start_time",oi.end_time as "oi.end_time",oi.price as "oi.price",oi.num as "oi.num",
            oi.start_read as "oi.start_read",oi.end_read as "oi.end_read",oi.device_id as "oi.device_id",oi.arrive_time as "oi.arrive_time"
        from  t_bil_order_item oi,t_bil_order o
        left join (select count(1) pay_num,sum(payment) payment,opi.order_id from t_bil_order_pay_info opi
                    where  exists (select 1 from t_approval_commit ac where ac.id = opi.approval_id and ac.status = '2')
                        <if test="map.startPayTime != null">
                            AND <![CDATA[date(opi.pay_time) >= date(#{map.startPayTime}) ]]>
                        </if>
                        <if test="map.endPayTime != null">
                            AND <![CDATA[date(opi.pay_time) <= date(#{map.endPayTime}) ]]>
                        </if>
                    group by order_id
        ) t  on t.order_id = o.id
        where o.id = oi.order_id AND o.pay_state = '30'
        <if test="map.sourceId != null and map.sourceId != ''">
            and o.source_id = #{map.sourceId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.orderType != null and map.orderType != ''">
            AND o.order_type = #{map.orderType}
        </if>
        <if test="map.orderItemType != null and map.orderItemType != ''">
            AND oi.order_item_type = #{map.orderItemType}
        </if>

        <if test="map.startCreateDate != null">
            AND <![CDATA[date(o.create_date) >= date(#{map.startCreateDate}) ]]>
        </if>
        <if test="map.endCreateDate != null">
            AND <![CDATA[date(o.create_date) <= date(#{map.endCreateDate}) ]]>
        </if>
        <if test="map.startCreateDate != null">
            AND <![CDATA[date(o.payable_time) >= date(#{map.startPayableTime}) ]]>
        </if>
        <if test="map.endCreateDate != null">
            AND <![CDATA[date(o.payable_time) <= date(#{map.endPayableTime}) ]]>
        </if>

        <if test="map.itemEndDate != null">
            AND <![CDATA[date(oi.end_time) <= date(#{map.itemEndDate}) ]]>
        </if>

        <if test="map.maxItemStartDate != null">
            AND <![CDATA[date(oi.start_time) <= date(#{map.maxItemStartDate}) ]]>
        </if>
        <if test="map.itemMonth != null ">
            AND date_format(oi.start_time,'%Y-%m') = #{map.itemMonth}
        </if>

        <if test="map.orderItemTypes != null and map.orderItemTypes != ''">
            AND oi.order_item_type  in
            <foreach collection="map.orderItemTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.orderTypes != null and map.orderTypes.size() > 0">
            AND o.order_type in
            <foreach collection="map.orderTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </select>

    <select id="getOrderByMap" resultMap="BaseResultMap">
        select o.* from t_bil_order o
        LEFT JOIN t_bil_order_item i on o.id = i.order_id
        where o.contract_id = #{map.contractId}
        and o.order_type = #{map.orderType}
        <if test="map.orderItemType != null and map.orderItemType != ''">
            and i.order_item_type = #{map.orderItemType}
        </if>
        <if test="map.startTime != null and map.endTime != null">
            AND <![CDATA[
              (date(#{map.endTime}) >= date(i.start_time) and date(#{map.endTime}) <= date(i.end_time)
              or
              date(#{map.startTime}) >= date(i.start_time) and date(#{map.startTime}) <= date(i.end_time)
               or
              date(#{map.startTime}) >= date(i.start_time) and date(#{map.endTime}) <= date(i.end_time)
               or
              date(#{map.startTime}) <= date(i.start_time) and date(#{map.endTime}) >= date(i.end_time) )
            ]]>
        </if>
        <if test="map.stopTime != null and map.stopTime != ''">
            AND <![CDATA[
                date(i.start_time) <= date(#{map.stopTime})
                and date(i.end_time) >= date(#{map.stopTime})
            ]]>
        </if>
        limit 1
    </select>

    <select id="getOrderByMap1" resultType="cn.uone.bean.entity.business.bil.BilOrderEntity">
        select o.* from t_bil_order o
        LEFT JOIN t_bil_order_item i on o.id = i.order_id
        where o.contract_id = #{map.contractId}
        and o.order_type = #{map.orderType}
        <if test="map.orderItemType != null and map.orderItemType != ''">
            and i.order_item_type = #{map.orderItemType}
        </if>
        <if test="map.startTime != null and map.endTime != null">
            AND <![CDATA[
              date(#{map.startTime}) = date(i.start_time) and date(#{map.endTime}) = date(i.end_time)
            ]]>
        </if>
        limit 1
    </select>





    <select id="getNeedPushOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o
        left join t_cont_contract c on o.contract_id=c.id
        where 1 = 1
        and o.is_discard = '0' and o.pay_state != '40' and o.is_push = '0'
        and (c.state = '6' or c.state = '13' or c.state='15' or c.state='12' or c.state='14')
        and <![CDATA[date(o.push_time) <= date(NOW())
        ]]>
    </select>


    <select id="getNeedSyncOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o
        inner JOIN t_res_source s on o.source_id = s.id
        inner JOIN t_cont_contract c on o.contract_id = c.id
        inner JOIN t_cont_par t ON t.contract_id = c.id and t.type=5 and t.value=2
        where o.is_sync = 0
        and o.order_type not in ('130','140','210','211')
        and c.contract_type = 1
        and c.is_organize = 0
    </select>

    <select id="getCurrentRentOrder" resultMap="BaseResultMap">
        select o.*,SUM(IF(oi.`order_item_type` NOT IN('111','241','447','201'),oi.`payment`,0)) yhPayment
        from t_bil_order o
        INNER JOIN t_bil_order_item oi on o.id = oi.order_id
        where 1=1 and o.order_type = '20'
        <if test="map.payState!= null and map.payState != '' ">
            and o.pay_state = #{map.payState}
        </if>
        and EXISTS(select 1 from t_bil_order_item i where i.order_id=o.id and
            DATE_FORMAT(i.end_time,'%Y-%m-%d')  >= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
            and DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d') >= DATE_FORMAT( i.start_time,'%Y-%m-%d'))
        and o.contract_id = #{map.contractId}
        and o.source_id = #{map.sourceId}
        and o.is_discard = '0'
        group by o.id limit 1
    </select>

    <select id="getCurrentReserve" resultMap="BaseResultMap">
        select o.* from t_bil_order o
        where 1=1 and o.order_type = '10'
        and o.source_id = #{map.sourceId}
        and o.is_discard = '0'
        order by o.create_date desc  limit 1
    </select>

    <select id="getCurrentLiveOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o
        INNER JOIN t_bil_order_item oi on o.id = oi.order_id
        where 1=1
        <if test="map.orderType!= null and map.orderType != '' ">
            and o.order_type = #{map.orderType}
        </if>
        <if test="map.payState!= null and map.payState != '' ">
            and o.pay_state = #{map.payState}
        </if>
        and DATE_FORMAT(oi.end_time,'%Y-%m-%d')  >= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d') >= DATE_FORMAT( oi.start_time,'%Y-%m-%d')
        <if test="map.contractId!= null and map.contractId != '' ">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId!= null and map.sourceId != '' ">
            and o.source_id = #{map.sourceId}
        </if>
        and o.is_discard = '0'
        and o.pay_state != '40'
        <if test="map.orderItemType!= null and map.orderItemType != '' ">
            and oi.order_item_type = #{map.orderItemType}
        </if>
        <if test="map.orderItemTypes != null and map.orderItemTypes.size() > 0">
            and oi.order_item_type in
            <foreach collection="map.orderItemTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by o.id
    </select>

    <select id="getCurrentLiveOrderPb" resultMap="BaseResultMap">
        select o.* from t_bil_order o
        INNER JOIN t_bil_order_item oi on o.id = oi.order_id
        where 1=1
        <if test="map.orderType!= null and map.orderType != '' ">
            and o.order_type = #{map.orderType}
        </if>
        <if test="map.payState!= null and map.payState != '' ">
            and o.pay_state = #{map.payState}
        </if>
        <if test="map.contractId!= null and map.contractId != '' ">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId!= null and map.sourceId != '' ">
            and o.source_id = #{map.sourceId}
        </if>
        and o.is_discard = '0'
        and o.pay_state != '40'
        <if test="map.orderItemType!= null and map.orderItemType != '' ">
            and oi.order_item_type = #{map.orderItemType}
        </if>
        group by o.id
    </select>

    <select id="getCurrentSubsidyOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o
        INNER JOIN t_bil_order_item oi on o.id = oi.order_id
        where 1=1 and o.order_type = '21'
        <if test="map.payState!= null and map.payState != '' ">
            and o.pay_state = #{map.payState}
        </if>
        and DATE_FORMAT(oi.end_time,'%Y-%m-%d')  >= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d') >= DATE_FORMAT( oi.start_time,'%Y-%m-%d')
        and o.contract_id = #{map.contractId}
        and o.source_id = #{map.sourceId}
        and o.is_discard = '0'
        <if test="map.payerId!= null and map.payerId != '' ">
            and o.payer_id = #{map.payerId}
        </if>
        group by o.id limit 1
    </select>

    <select id="queryApproval" resultMap="BaseResultMap">
        select o.* from t_bil_order o
        where o.pay_state in ('10','30')
        and o.is_push='1'

        <if test="map.contractId!= null and map.contractId != '' ">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId!= null and map.sourceId != '' ">
            and o.source_id = #{map.sourceId}
        </if>
    </select>


    <select id="queryUnPushOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o INNER JOIN t_cont_contract c on o.contract_id = c.id  and c.state in ('6')
        where o.is_push = 0 and o.is_discard ='0' and o.pay_state !='40'
    </select>

    <update id="deleteCheckoutOrder">
        update t_bil_order set is_initial =0 ,is_discard=1
        WHERE order_type in ('130','140','150','210','30','40','211')
        <if test="map.sourceId != null and map.sourceId != ''">
            and source_id = #{map.sourceId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            and contract_id = #{map.contractId}
        </if>
    </update>


    <select id="getSubsidyOrder" resultType="cn.uone.bean.entity.business.bil.BilOrderEntity">
        select * from t_bil_order o  where order_type = '21'
        and exists (select 1 from t_bil_order_item oi where oi.order_id = o.id and date(oi.start_time)>='2020-07-01' )
    </select>

    <select id="getCancelSubsidyOrder" resultMap="BaseResultMap">
        select o.* from t_bil_order o left join  t_bil_order_item oi on o.id = oi.order_id
        where order_type = '21' and o.pay_state = '10'  and o.is_discard = '0'
        <if test="map.contractId!= null and map.contractId != '' ">
            and o.contract_id = #{map.contractId}
        </if>
        <if test="map.sourceId!= null and map.sourceId != '' ">
            and o.source_id = #{map.sourceId}
        </if>
        and DATE_FORMAT(oi.start_time,'%Y-%m-%d') >= DATE_FORMAT(#{map.checkoutDate},'%Y-%m-%d')
        and o.payer_id not in ('78fa7355b70b1740d409dd8d949f92cc','b1a2dd56565100d3be707973e6e8c376')
    </select>




    <select id="selectAllBilByMap" resultMap="BilOrderVoMap" parameterType="java.util.Map">
    select o.id,o.approval_state,o.code, o.create_date,o.order_type,o.payment,o.payable_payment,
    o.actual_payment, o.pay_state,o.pay_time,o.invoice_type,o.trade_code,o.transfer_mode, o.invoice_state,
    o.is_push,o.push_time,o.payer_id,o.intention,r.time,o.payable_time,o.invoice_payment,
    COALESCE(TimeStampDiff(HOUR,r.urge_time,now())>=pa.param_value,1,0) isPayable,
    o.source_id,o.contract_id,d.discount_amount,l.code discountCode,d.id discountId,
    s.source_name as address,r.name as payer,r2.name as singer,c.signer_id as singer_id,
    IFNULL(c.contract_code,'') contract_code,c.contract_type,c.is_organize,s.project_id, w.audit_result
    ,min(oi.start_time) sDate,max(oi.end_time) eDate
    from t_bil_order o
    LEFT JOIN t_bil_order_item oi on oi.order_id = o.id
    LEFT JOIN t_bil_discount_log l on o.discount_log_id = l.id
    LEFT JOIN t_bil_discount d on l.discount_id = d.id
    LEFT JOIN t_cont_contract c on o.contract_id = c.id
    LEFT JOIN t_cont_contract_source_rel csr ON csr.contract_id = c.id AND csr.source_id = o.source_id
    LEFT JOIN t_base_car car ON car.contract_source_id = csr.id
    LEFT JOIN v_res_source s on o.source_id = s.id
    LEFT JOIN t_res_plan_partition p on s.partition_id = p.id
    LEFT JOIN t_res_project_para pa on p.project_id= pa.project_id and pa.param_code = 'XMCS_0052'
    LEFT JOIN v_sys_renter r on r.id = o.payer_id
    LEFT JOIN t_cont_contract_info r2 on r2.contract_id = c.id
    LEFT JOIN (select t.rel_id,t.audit_result
    from (select wt.rel_id,wt.audit_result from t_bpm_workflow wt order by wt.update_date) t group by t.rel_id) w
    on w.rel_id=o.id
    where 1=1 and o.payment != 0
    <if test="map.searchVo.contractId != null and map.searchVo.contractId != ''">
                   AND o.contract_id = #{map.searchVo.contractId}
    </if>
    <if test="map.searchVo.states != null and map.searchVo.states.size() > 0">
                   AND o.pay_state in
    <foreach close=")" separator="," open="(" item="item" index="index" collection="map.searchVo.states"> #{item} </foreach>
    </if>
    <if test="map.searchVo.keyWord == 'makeInvoice'">
                   AND o.pay_state = '20'
                   AND o.payable_payment &gt; 0
                   AND o.order_type not in ('140','150','200','201')
    </if>
    <if test="map.searchVo.isPush != null and !map.searchVo.isPush.isEmpty()">
                   and o.is_push = #{map.searchVo.isPush}
    </if>
    <if test="map.searchVo.isDiscard != null and map.searchVo.isDiscard != ''">
                   AND o.is_discard = #{map.searchVo.isDiscard}
    </if>
    <if test="map.searchVo.userId != null and map.searchVo.userId != ''">
                   AND o.payer_id = #{map.searchVo.userId}
    </if>
    <if test="map.searchVo.orderType != null and map.searchVo.orderType != ''">
                   AND o.order_type = #{map.searchVo.orderType}
    </if>
    <if test="map.searchVo.contractFlag != null and map.searchVo.contractFlag != ''">
        AND o.contract_id is null
    </if>
    <if test="map.searchVo.daily != null and map.searchVo.daily != ''">
        AND ( o.order_type != '30'  or (o.order_type ='30' and o.pay_state ='10' ))
    </if>
    group by o.id
    <if test="map.searchVo.fixSort != null and map.searchVo.fixSort != ''">
                   ORDER BY o.is_push desc,c.contract_code,o.order_type ASC
    </if>
    <if test="map.searchVo.fixSort == null or map.searchVo.fixSort == ''">
          order by o.pay_state,o.is_push desc,o.push_time desc,o.create_date desc
    </if>
    </select>

    <select id="selectPartBilByMap" resultMap="BilOrderVoMap" parameterType="java.util.Map">
        select o.id,o.approval_state,o.code, o.create_date,o.order_type,o.payment,o.payable_payment,
        o.actual_payment, o.pay_state,o.pay_time,o.invoice_type,o.trade_code,o.transfer_mode, o.invoice_state,
        o.is_push,o.push_time,o.payer_id,o.intention,r.time,o.payable_time,o.invoice_payment,
        COALESCE(TimeStampDiff(HOUR,r.urge_time,now())>=pa.param_value,1,0) isPayable,
        o.source_id,o.contract_id,d.discount_amount,l.code discountCode,d.id discountId,
        s.source_name as address,r.name as payer,r2.name as singer,c.signer_id as singer_id,
        IFNULL(c.contract_code,'') contract_code,c.contract_type,c.is_organize,s.project_id, w.audit_result
        from t_bil_order o
        LEFT JOIN t_bil_discount_log l on o.discount_log_id = l.id
        LEFT JOIN t_bil_discount d on l.discount_id = d.id
        LEFT JOIN t_cont_contract c on o.contract_id = c.id
        LEFT JOIN t_cont_contract_source_rel csr ON csr.contract_id = c.id AND csr.source_id = o.source_id
        LEFT JOIN t_base_car car ON car.contract_source_id = csr.id
        LEFT JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_res_plan_partition p on s.partition_id = p.id
        LEFT JOIN t_res_project_para pa on p.project_id= pa.project_id and pa.param_code = 'XMCS_0052'
        LEFT JOIN v_sys_renter r on r.id = o.payer_id
        LEFT JOIN t_cont_contract_info r2 on r2.contract_id = c.id
        LEFT JOIN (select t.rel_id,t.audit_result
        from (select wt.rel_id,wt.audit_result from t_bpm_workflow wt order by wt.update_date) t group by t.rel_id) w
        on w.rel_id=o.id
        where 1=1 and o.payment !=0
        <if test="map.searchVo.contractId != null and map.searchVo.contractId != ''">
            AND o.contract_id = #{map.searchVo.contractId}
        </if>
        <if test="map.searchVo.states != null and map.searchVo.states.size() > 0">
            AND o.pay_state in
            <foreach close=")" separator="," open="(" item="item" index="index" collection="map.searchVo.states"> #{item} </foreach>
        </if>
        <if test="map.searchVo.keyWord == 'makeInvoice'">
            AND o.pay_state = '20'
            AND o.payable_payment &gt; 0
            AND o.order_type not in ('140','150','200','201')
        </if>
        <if test="map.searchVo.isPush != null and !map.searchVo.isPush.isEmpty()">
            and o.is_push = #{map.searchVo.isPush}
        </if>
        <if test="map.searchVo.isDiscard != null and map.searchVo.isDiscard != ''">
            AND o.is_discard = #{map.searchVo.isDiscard}
        </if>
        <if test="map.searchVo.userId != null and map.searchVo.userId != ''">
            AND o.payer_id = #{map.searchVo.userId}
        </if>
        <if test="map.searchVo.orderType != null and map.searchVo.orderType != ''">
            AND o.order_type = #{map.searchVo.orderType}
        </if>
        <if test="map.searchVo.contractFlag != null and map.searchVo.contractFlag != ''">
            AND o.contract_id is null
        </if>
        <if test="map.searchVo.daily != null and map.searchVo.daily != ''">
            AND ( o.order_type != '30'  or o.data_from = '1' )
        </if>
        <if test="map.searchVo.fixSort != null and map.searchVo.fixSort != ''">
            ORDER BY c.contract_code,o.order_type ASC
        </if>
        <if test="map.searchVo.fixSort == null or map.searchVo.fixSort == ''">
            order by o.is_push desc,o.push_time desc,o.create_date desc
        </if>
        limit 0,5
    </select>

    <select id="getStatistics" resultType="cn.uone.bean.entity.business.bil.vo.BilCountVo">
        select
        IFNULL(sum(if(DATE_FORMAT(o.payable_time,'%Y-%m')=DATE_FORMAT(now(),'%Y-%m'),o.payment,0)),0) as monthPayment,
        IFNULL(sum(if(DATE_FORMAT(o.payable_time,'%Y')=DATE_FORMAT(now(),'%Y'),o.payment,0)),0) as yearPayment,
        ifnull(sum(if((DATEDIFF(o.payable_time,now()) &lt; 0 ) and o.pay_state = '10' ,1,0)),0) overdueNum,
        ifnull(sum(if((DATEDIFF(o.payable_time,now()) = 0) ,o.payment,0)),0) payment,
        ifnull(sum(if((DATEDIFF(o.payable_time,now()) = 0 and o.pay_state = '20') ,o.payment,0)),0) paidPayment,
        IFNULL(sum(if(DATE_FORMAT(o.payable_time,'%Y-%m')=DATE_FORMAT(now(),'%Y-%m') and o.pay_state = '20',o.payment,0)),0) as monthPaidPayment,
        IFNULL(sum(if(DATE_FORMAT(o.payable_time,'%Y')=DATE_FORMAT(now(),'%Y') and o.pay_state = '20',o.payment,0)),0) as yearPaidPayment
        from t_bil_order o
        left join t_res_source s ON s.id=o.source_id
        left join t_res_project p ON p.id=s.project_id
        where o.is_push='1' and o.is_discard = '0'
        <if test="map.projectId != null or map.projectId != ''">
            and p.id = #{map.projectId}
        </if>
    </select>

    <select id="getReserveCount" resultType="cn.uone.bean.entity.business.bil.vo.BilCountVo">
        select count(1) reserve
        from t_bil_order t
        left  join v_res_source vrs on vrs.id = t.source_id
        where 1 = 1
        and DATEDIFF(t.pay_time,now()) = 0
        and pay_state = '20'
        <if test="map.orderType != null or map.orderType != ''">
            and t.order_type = #{map.orderType}
        </if>
        <if test="map.projectId != null or map.projectId != ''">
            and vrs.project_id = #{map.projectId}
        </if>
    </select>

    <select id="getDailyByCondition" parameterType="java.util.Map" resultMap="DailyOrderVoMap">
        select rentName , sourceName , rentPayment,ROUND(rentPayment*0.003,2) rentFee ,depositPayment,ROUND(depositPayment*0.003,2) depositFee ,
        intentionPayment,ROUND(intentionPayment*0.003,2) intentionFee ,waterPayment, ROUND(waterPayment*0.003,2) waterFee ,electricPayment, ROUND(electricPayment*0.003,2) electricFee
        ,ROUND(rentPayment*0.997,2)+ROUND(depositPayment*0.997,2)+ROUND(intentionPayment*0.997,2)+ROUND(waterPayment*0.997,2)+ROUND(electricPayment*0.997,2) allPayment
        ,ROUND(rentPayment*0.003,2)+ROUND(depositPayment*0.003,2)+ROUND(intentionPayment*0.003,2)+ROUND(waterPayment*0.003,2)+ROUND(electricPayment*0.003,2) allFee
        , partition_id , code , projectName
        from (
        select 	v.`name` rentName , CONCAT(s.partitionName,s.code) sourceName , s.partition_id , s.code ,s.projectName ,
        (select IFNULL(sum(t.payment),0) from t_bil_order t where t.source_id = o.source_id and t.order_type = '20' and t.pay_state ='20' and o.trade_code is not null
            <if test="map.searchVo.startPayTime != null">
                AND <![CDATA[date(t.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
            </if>
            <if test="map.searchVo.endPayTime != null">
                AND <![CDATA[date(t.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
            </if>
            <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
                and t.pay_time = TO_DAYS(CURDATE())
            </if>
        ) rentPayment ,
        (select IFNULL(sum(t.payment),0) from t_bil_order t where t.source_id = o.source_id and t.order_type = '5' and t.pay_state ='20' and o.trade_code is not null
            <if test="map.searchVo.startPayTime != null">
                AND <![CDATA[date(t.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
            </if>
            <if test="map.searchVo.endPayTime != null">
                AND <![CDATA[date(t.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
            </if>
            <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
                and t.pay_time = TO_DAYS(CURDATE())
            </if>
        ) depositPayment ,
        (select IFNULL(sum(t.payment),0) from t_bil_order t where t.source_id = o.source_id and t.order_type = '10' and t.pay_state ='20' and o.trade_code is not null
            <if test="map.searchVo.startPayTime != null">
                AND <![CDATA[date(t.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
            </if>
            <if test="map.searchVo.endPayTime != null">
                AND <![CDATA[date(t.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
            </if>
            <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
                and t.pay_time = TO_DAYS(CURDATE())
            </if>
        ) intentionPayment ,
        '0' waterPayment ,
        '0' electricPayment
        from t_bil_order o , v_sys_renter v , v_res_source s
          left join t_res_project p on p.id = s.project_id
        where o.payer_id = v.id and s.id = o.source_id and o.payment != 0 and o.trade_code is not null
        <if test="map.searchVo.startPayTime != null">
            AND <![CDATA[date(o.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
        </if>
        <if test="map.searchVo.endPayTime != null">
            AND <![CDATA[date(o.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
        </if>
        <if test="map.searchVo.projectId != null and map.searchVo.projectId != ''">
            AND s.project_id = #{map.searchVo.projectId}
        </if>
        <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
            and o.pay_time = TO_DAYS(CURDATE())
        </if>
        <if test="map.searchVo.floor != null and map.searchVo.floor != ''">
            and s.floor=#{map.searchVo.floor}
        </if>
        <if test="map.searchVo.partitionId != null and map.searchVo.partitionId != ''">
            AND s.partition_id = #{map.searchVo.partitionId}
        </if>
        <if test="map.searchVo.keyWord != null and map.searchVo.keyWord != ''">
            and (
            s.source_name like CONCAT('%', #{map.searchVo.keyWord}, '%')
            or v.name like CONCAT('%', #{map.searchVo.keyWord}, '%')
            )
        </if>
        <if test="map.searchVo.fixSort == null or map.searchVo.fixSort == ''">
            #project_datascope#
        </if>
        group by o.source_id

        union all

        select 	v.`name` rentName , CONCAT(s.partitionName,s.code) sourceName , s.partition_id , s.code ,s.projectName,
        '0' rentPayment ,
        '0' depositPayment ,
        '0' intentionPayment ,
        (select IFNULL(sum(s.payment),0) from t_bil_invest s where s.source_id = o.source_id and s.order_type = '333' and s.pay_state ='20'
            <if test="map.searchVo.startPayTime != null">
                AND <![CDATA[date(s.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
            </if>
            <if test="map.searchVo.endPayTime != null">
                AND <![CDATA[date(s.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
            </if>
            <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
                and s.pay_time = TO_DAYS(CURDATE())
            </if>
        )  waterPayment ,
        (select IFNULL(sum(t.payment),0) from t_bil_invest t where t.source_id = o.source_id and t.order_type = '444' and t.pay_state ='20'
            <if test="map.searchVo.startPayTime != null">
                AND <![CDATA[date(t.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
            </if>
            <if test="map.searchVo.endPayTime != null">
                AND <![CDATA[date(t.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
            </if>
            <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
                and t.pay_time = TO_DAYS(CURDATE())
            </if>
        )  electricPayment
        from t_bil_invest o , v_sys_renter v , v_res_source s
            left join t_res_project p on p.id = s.project_id
        where o.payer_id = v.id and s.id = o.source_id and o.payment != 0
        <if test="map.searchVo.startPayTime != null">
            AND <![CDATA[date(o.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
        </if>
        <if test="map.searchVo.endPayTime != null">
            AND <![CDATA[date(o.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
        </if>
        <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
            and o.pay_time = TO_DAYS(CURDATE())
        </if>
        <if test="map.searchVo.projectId != null and map.searchVo.projectId != ''">
            AND s.project_id = #{map.searchVo.projectId}
        </if>
        <if test="map.searchVo.floor != null and map.searchVo.floor != ''">
            and s.floor=#{map.searchVo.floor}
        </if>
        <if test="map.searchVo.partitionId != null and map.searchVo.partitionId != ''">
            AND s.partition_id = #{map.searchVo.partitionId}
        </if>
        <if test="map.searchVo.keyWord != null and map.searchVo.keyWord != ''">
            and (
            s.source_name like CONCAT('%', #{map.searchVo.keyWord}, '%')
            or v.name like CONCAT('%', #{map.searchVo.keyWord}, '%')
            )
        </if>
        <if test="map.searchVo.fixSort == null or map.searchVo.fixSort == ''">
            #project_datascope#
        </if>
        group by o.source_id
        ) daily
        order by daily.partition_id , daily.code
    </select>

    <select id="getSummaryByCondition" parameterType="java.util.Map" resultMap="DailyOrderVoMap">
        select
        name projectName ,
        IFNULL(rentPayment,0) rentPayment,
        IFNULL(depositPayment,0) depositPayment,
        IFNULL(intentionPayment,0) intentionPayment,
        IFNULL(waterPayment,0) waterPayment,
        IFNULL(electricPayment,0) electricPayment ,
        ROUND((IFNULL(rentPayment, 0) + IFNULL(depositPayment, 0) + IFNULL(intentionPayment, 0) + IFNULL(waterPayment, 0) + IFNULL(electricPayment, 0))*0.997,2) allPayment,
        ROUND((IFNULL(rentPayment, 0) + IFNULL(depositPayment, 0) + IFNULL(intentionPayment, 0) + IFNULL(waterPayment, 0) + IFNULL(electricPayment, 0))*0.003,2) allFee
        from (
        select p.`name` ,
        (select rent from (select IFNULL(sum(t.payment),0) rent, v.project_id from t_bil_order t , v_res_source v where t.source_id = v.id
        and t.order_type = '20' and t.pay_state ='20'
        <if test="map.searchVo.startPayTime != null">
            AND <![CDATA[date(t.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
        </if>
        <if test="map.searchVo.endPayTime != null">
            AND <![CDATA[date(t.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
        </if>
        <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
            and t.pay_time = TO_DAYS(CURDATE())
        </if>
        group by v.project_id ) zujin where zujin.project_id = p.id
        ) rentPayment ,
        (select deposit from (select IFNULL(sum(t.payment),0) deposit, v.project_id from t_bil_order t , v_res_source v where t.source_id = v.id
        and t.order_type = '5' and t.pay_state ='20'
        <if test="map.searchVo.startPayTime != null">
            AND <![CDATA[date(t.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
        </if>
        <if test="map.searchVo.endPayTime != null">
            AND <![CDATA[date(t.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
        </if>
        <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
            and t.pay_time = TO_DAYS(CURDATE())
        </if>
        group by v.project_id
        ) yajin where yajin.project_id = p.id
        ) depositPayment ,
        (select intention from (select IFNULL(sum(t.payment),0) intention, v.project_id from t_bil_order t , v_res_source v where t.source_id = v.id
        and t.order_type = '10' and t.pay_state ='20'
        <if test="map.searchVo.startPayTime != null">
            AND <![CDATA[date(t.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
        </if>
        <if test="map.searchVo.endPayTime != null">
            AND <![CDATA[date(t.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
        </if>
        <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
            and t.pay_time = TO_DAYS(CURDATE())
        </if>group by v.project_id ) yixiang where yixiang.project_id = p.id
        ) intentionPayment ,
        (select water from (select IFNULL(sum(t.payment),0) water, v.project_id from t_bil_invest t , v_res_source v where t.source_id = v.id
        and t.order_type = '333' and t.pay_state ='20'
        <if test="map.searchVo.startPayTime != null">
            AND <![CDATA[date(t.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
        </if>
        <if test="map.searchVo.endPayTime != null">
            AND <![CDATA[date(t.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
        </if>
        <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
            and t.pay_time = TO_DAYS(CURDATE())
        </if>
        group by v.project_id ) shuifei where shuifei.project_id = p.id
        ) waterPayment ,
        (select electric from (select IFNULL(sum(t.payment),0) electric, v.project_id from t_bil_invest t , v_res_source v where t.source_id = v.id
        and t.order_type = '444' and t.pay_state ='20'
        <if test="map.searchVo.startPayTime != null">
            AND <![CDATA[date(t.pay_time) >= date(#{map.searchVo.startPayTime}) ]]>
        </if>
        <if test="map.searchVo.endPayTime != null">
            AND <![CDATA[date(t.pay_time) <= date(#{map.searchVo.endPayTime}) ]]>
        </if>
        <if test="map.searchVo.startPayTime == null and map.searchVo.endPayTime == null">
            and t.pay_time = TO_DAYS(CURDATE())
        </if>
        group by v.project_id ) dianfei where dianfei.project_id = p.id
        ) electricPayment
        from t_res_project p
        ) sumery
    </select>

    <select id="getOverdueOrder" resultMap="BaseResultMap">
        select * from t_bil_order t where 1 = 1  and t.is_push = '1'
          and t.payable_time >= CURDATE() and t.is_discard = '0'
          and  t.pay_state = '10' and t.payer_id =#{renterId}
    </select>

    <select id="getLastLifeFee" resultMap="BaseResultMap">
        select o.contract_id , o.source_id , oi.order_item_type order_type , oi.payment  from t_bil_order o , t_bil_order_item oi
            where oi.order_id = o.id and o.pay_state = '20' and oi.order_item_type in ('30','50') and DATE_FORMAT(oi.create_date,'%Y-%m-%d') = '2022-10-24'
    </select>

    <select id="getOverdueBil" resultMap="OverdueBilVoMap">
        select v.`name` ,  v.tel , v.openid , o.`code` , c.contract_code , o.payable_payment , o.payer_id renterId , o.order_type orderType ,
             DATEDIFF(now(),o.payable_time) overdue, o.payable_time,o.id
        from t_cont_contract c , t_bil_order o , v_sys_renter v
             where c.id = o.contract_id and o.pay_state = '10' and o.is_push = '1' and v.id = o.payer_id and c.state = '6'
                and ((c.is_organize = '0' and c.rent_pay_payer = '1') or (c.is_organize ='1' and c.rent_pay_payer = '2'))
                and CURDATE() >= o.payable_time
    </select>


    <select id="hydropowerExport" parameterType="java.util.Map" resultMap="BilOrderVoMap">
        select * from (
        select v.partitionName , v.`code` ,c.contract_code ,r.`name` , '水费' as '水费' ,o.pay_time as 'month' ,
        sum(o.payment) useFee , czsj.czje ,czye.czye , 0 as 'bhs'
        from t_bil_order_item oi , v_res_source v  , t_cont_contract c , v_sys_renter r ,t_bil_order o
        left join (select bi.contract_id , sum(bi.payment) czje from t_bil_invest bi
        where bi.pay_state ='20' and bi.order_type = '333' GROUP BY bi.contract_id ) czsj on czsj.contract_id = o.contract_id
        left join (select ab.contract_id , ab.balance czye from t_account_balance ab
        where ab.order_type = '333' ) czye on czye.contract_id = o.contract_id
        where oi.order_id = o.id and oi.order_item_type = '30' and o.trade_code is null  and o.is_discard = '0'
        and o.pay_state ='20' and v.id = o.source_id and c.id = o.contract_id and r.id = o.payer_id
        <if test="projectId != null and projectId != ''">
            and v.project_id = #{projectId}
        </if>
        <if test="finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(o.pay_time,'%Y%m') < DATE_FORMAT(#{finacelExportDate},'%Y%m') ]]>
        </if>
        GROUP BY o.contract_id
        union
        select v.partitionName , v.`code` ,c.contract_code ,r.`name` , '电费' as '电费' , o.pay_time as 'month' ,
        sum(o.payment) useFee , czsj.czje ,czye.czye  , ROUND(sum(o.payment)/1.13,2) as 'bhs'
        from t_bil_order_item oi , v_res_source v  , t_cont_contract c , v_sys_renter r ,t_bil_order o
        left join (select bi.contract_id , sum(bi.payment) czje from t_bil_invest bi
        where bi.pay_state ='20' and bi.order_type = '444' GROUP BY bi.contract_id ) czsj on czsj.contract_id = o.contract_id
        left join (select ab.contract_id , ab.balance czye from t_account_balance ab
        where ab.order_type = '444' ) czye on czye.contract_id = o.contract_id
        where oi.order_id = o.id and oi.order_item_type = '50' and o.trade_code is null  and o.is_discard = '0'
        and o.pay_state ='20' and v.id = o.source_id and c.id = o.contract_id and r.id = o.payer_id
        <if test="projectId != null and projectId != ''">
            and v.project_id = #{projectId}
        </if>
        <if test="finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(o.pay_time,'%Y%m') < DATE_FORMAT(#{finacelExportDate},'%Y%m') ]]>
        </if>
        GROUP BY o.contract_id
        UNION
        select v.partitionName , v.`code` ,c.contract_code ,r.`name` , '公摊电费' as 'gtdf' ,o.pay_time as 'month' ,
        sum(o.payment) useFee , 0 as 'czje' ,0 as czye , ROUND(sum(o.payment)/1.06,2) as 'bhs'
        from t_bil_order_item oi , v_res_source v  , t_cont_contract c , v_sys_renter r ,t_bil_order o
        where oi.order_id = o.id and oi.order_item_type = '200' and o.trade_code is not null  and o.is_discard = '0'
        and o.pay_state ='20' and v.id = o.source_id and c.id = o.contract_id and r.id = o.payer_id
        <if test="projectId != null and projectId != ''">
            and v.project_id = #{projectId}
        </if>
        <if test="finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(o.pay_time,'%Y%m') < DATE_FORMAT(#{finacelExportDate},'%Y%m') ]]>
        </if>
        GROUP BY o.contract_id
        UNION
        select v.partitionName , v.`code` ,c.contract_code ,r.`name` , '公摊水费' as 'gtsf' , o.pay_time as 'month' ,
        sum(o.payment) useFee , 0 as 'czje' ,0 as czye, 0 as 'bhs'
        from t_bil_order_item oi , v_res_source v  , t_cont_contract c , v_sys_renter r ,t_bil_order o
        where oi.order_id = o.id and oi.order_item_type = '190' and o.trade_code is not null  and o.is_discard = '0'
        and o.pay_state ='20' and v.id = o.source_id and c.id = o.contract_id and r.id = o.payer_id
        <if test="projectId != null and projectId != ''">
            and v.project_id = #{projectId}
        </if>
        <if test="finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(o.pay_time,'%Y%m') < DATE_FORMAT(#{finacelExportDate},'%Y%m') ]]>
        </if>
        and v.project_id = 'ae59d89a3cf6ad39f59ce935a072c0d3'
        GROUP BY o.contract_id
        UNION
        select v.partitionName , v.`code` ,c.contract_code ,r.`name` , '微信电费缴费' as 'wxdfjf', o.pay_time as 'month' ,
        sum(o.payment) useFee , 0 as 'czje' ,0 as czye , ROUND(sum(o.payment)/1.13,2) as 'bhs'
        from t_bil_order_item oi , v_res_source v  , t_cont_contract c , v_sys_renter r ,t_bil_order o
        where oi.order_id = o.id and oi.order_item_type = '50' and o.trade_code is not null  and o.is_discard = '0'
        and o.pay_state ='20' and v.id = o.source_id and c.id = o.contract_id and r.id = o.payer_id
        <if test="projectId != null and projectId != ''">
            and v.project_id = #{projectId}
        </if>
        <if test="finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(o.pay_time,'%Y%m') < DATE_FORMAT(#{finacelExportDate},'%Y%m') ]]>
        </if>
        GROUP BY o.contract_id
        UNION
        select v.partitionName , v.`code` ,c.contract_code ,r.`name` , '微信水费缴费' as 'wxsfjf', o.pay_time as 'month' ,
        sum(o.payment) useFee , 0 as 'czje' ,0 as czye , 0 as 'bhs'
        from t_bil_order_item oi , v_res_source v  , t_cont_contract c , v_sys_renter r ,t_bil_order o
        where oi.order_id = o.id and oi.order_item_type = '30' and o.trade_code is not null  and o.is_discard = '0'
        and o.pay_state ='20' and v.id = o.source_id and c.id = o.contract_id and r.id = o.payer_id
        <if test="projectId != null and projectId != ''">
            and v.project_id = #{projectId}
        </if>
        <if test="finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(o.pay_time,'%Y%m') < DATE_FORMAT(#{finacelExportDate},'%Y%m') ]]>
        </if>
        GROUP BY o.contract_id
        UNION
        select v.partitionName , v.`code` ,c.contract_code ,r.`name` , '微信公摊电费缴费' as 'wxdfjf', o.pay_time as 'month' ,
        sum(o.payment) useFee , 0 as 'czje' ,0 as czye, ROUND(sum(o.payment)/1.06,2) as 'bhs'
        from t_bil_order_item oi , v_res_source v  , t_cont_contract c , v_sys_renter r ,t_bil_order o
        where oi.order_id = o.id and oi.order_item_type = '200' and o.trade_code is not null  and o.is_discard = '0'
        and o.pay_state ='20' and v.id = o.source_id and c.id = o.contract_id and r.id = o.payer_id
        <if test="projectId != null and projectId != ''">
            and v.project_id = #{projectId}
        </if>
        <if test="finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(o.pay_time,'%Y%m') < DATE_FORMAT(#{finacelExportDate},'%Y%m') ]]>
        </if>
        GROUP BY o.contract_id
        UNION
        select v.partitionName , v.`code` ,c.contract_code ,r.`name` , '微信公摊水费缴费' as 'wxsfjf', o.pay_time as 'month' ,
        sum(o.payment) useFee , 0 as 'czje' ,0 as czye, 0 as 'bhs'
        from t_bil_order_item oi , v_res_source v  , t_cont_contract c , v_sys_renter r ,t_bil_order o
        where oi.order_id = o.id and oi.order_item_type = '190' and o.trade_code is not null  and o.is_discard = '0'
        and o.pay_state ='20' and v.id = o.source_id and c.id = o.contract_id and r.id = o.payer_id
        <if test="projectId != null and projectId != ''">
            and v.project_id = #{projectId}
        </if>
        <if test="finacelExportDate != null">
            AND <![CDATA[DATE_FORMAT(o.pay_time,'%Y%m') < DATE_FORMAT(#{finacelExportDate},'%Y%m') ]]>
        </if>
        GROUP BY o.contract_id
        ) t order by t.partitionName , t.`code`
    </select>

    <select id="getOverdueFee" resultType="java.util.Map">
        select sum(t.payment) as arrearage,count(t.id) as  overdue from
        t_bil_order t left join t_res_source s on s.id=t.source_id
        where
        is_push = '1'
        and
        pay_state = '10'
        and payable_time  &lt;  now()
        <if test="projectId != null and projectId != ''">
            and s.project_id = #{projectId}
        </if>
    </select>

    <select id="selectIntentForAudit" resultType="java.util.Map">
        SELECT o.id,o.`ccb_bill_id` as contractId,o.code,o.payment,o.pay_time,o.pay_state,s.`source_name`,r.name,r.tel,
               date_format(o.create_date,'%Y-%m-%d') create_date,o.remark
        FROM t_bil_order o,v_res_source s,v_sys_renter r
        WHERE 1=1 AND o.source_id=s.id AND o.`payer_id` = r.id
        AND o.`order_type`='200'
        AND o.`payable_payment` &lt; 0
        #project_datascope#
        <if test="map.payState != null and map.payState != ''">
            AND o.`pay_state` = #{map.payState}
        </if>
        <if test="map.project_id != null and map.project_id != ''">
            AND s.project_id = #{map.project_id}
        </if>
        <if test="map.keyWord != null and map.keyWord != ''">
            AND (s.source_name like CONCAT('%', #{map.keyWord}, '%')
            or r.name like CONCAT('%', #{map.keyWord}, '%'))
        </if>
        <if test="map.payStates != null and map.payStates.size() > 0">
            AND o.pay_state in
            <foreach collection="map.payStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.contractIds != null and map.contractIds.size() > 0">
            AND o.id in
            <foreach collection="map.contractIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY o.`update_date` DESC
    </select>


    <select id="getOrderList" resultMap="BilOrderVoMap">
        SELECT t.*,
               DATE_FORMAT(MIN(i.start_time),'%Y-%m-%d') start_date,
               DATE_FORMAT(MAX(i.`end_time`),'%Y-%m-%d') end_date
        FROM t_bil_order t,t_bil_order_item i
        WHERE t.id=i.order_id
        <if test="map.orderId != null and map.orderId != ''">
            AND  t.id=#{map.orderId}
        </if>
        <if test="map.contractId != null and map.contractId != ''">
            AND  t.contract_id=#{map.contractId}
        </if>
        <if test="map.orderType != null and map.orderType != ''">
            AND t.`order_type` = #{map.orderType}
        </if>
        <if test="map.payState != null and map.payState != ''">
            AND t.`pay_state` = #{map.payState}
        </if>
        <if test="map.yearMonth != null and map.yearMonth != ''">
            AND DATE_FORMAT(t.`push_time`, '%Y%m') &gt;= #{map.yearMonth}
        </if>
        GROUP BY t.id ORDER BY i.`start_time`
    </select>

    <select id="getLastPaidOrderList" resultType="java.util.Map">
        SELECT o.id,oi.payment,DATE_FORMAT(oi.start_time,'%Y-%m-%d') start_time,DATE_FORMAT(oi.end_time,'%Y-%m-%d') end_time,o.order_type
        FROM t_bil_order o
                 LEFT JOIN (SELECT MIN(t.start_time) start_time,MAX(t.end_time) end_time,SUM(t.payment) payment,
                                   t.order_id FROM t_bil_order_item t WHERE t.`order_item_type` NOT IN('111','241','447','201')
                            GROUP BY t.order_id) oi ON o.id=oi.order_id
        WHERE o.`pay_state` IN ('20','90') AND o.`order_type` NOT IN('130','140')
          AND o.contract_id = #{contractId}
          AND DATE_FORMAT(oi.`end_time`,'%Y-%m-%d') &gt;= #{checkoutDate}
        ORDER BY oi.start_time,o.order_type
    </select>

    <select id="getToRefundOrderList" resultType="java.util.Map">
        SELECT c.id contractId,c.`paper_code` paperCode,o.id orderId,o.code orderCode,
        ABS(o.`payable_payment`) payment,s.`source_name` sourceName,s.`source_type` sourceType,r.name renterName,
        o.`pay_time` refundTime,o.remark,o.`trade_code` tradeCode,o.pay_state payState,
        br.`payment` refundAmount,br.refund_way refundWay
        FROM t_cont_contract c,v_res_source s,v_sys_renter r,t_bil_order o
        LEFT JOIN t_bil_refund br ON br.`tk_order_id`=o.id
        WHERE c.id=o.`contract_id` AND o.`pay_state` in ('60','70')
        AND o.`source_id`=s.id AND o.`payer_id`=r.id
        <if test="map.projectId != null and map.projectId != ''">
            AND s.project_id = #{map.projectId}
        </if>
        <if test="map.searchVo.orderCode != null and map.searchVo.orderCode != ''">
            AND o.code  like CONCAT('%', #{map.searchVo.orderCode}, '%')
        </if>
        <if test="map.searchVo.sourceName != null and map.searchVo.sourceName != ''">
            AND s.source_name  like CONCAT('%', #{map.searchVo.sourceName}, '%')
        </if>
        <if test="map.searchVo.contractCode != null and map.searchVo.contractCode != ''">
            AND c.paper_code  like CONCAT('%', #{map.searchVo.contractCode}, '%')
        </if>
        <if test="map.searchVo.singer != null and map.searchVo.singer != ''">
            AND r.name   like CONCAT('%', #{map.searchVo.singer}, '%')
        </if>
        <if test="map.searchVo.state != null and map.searchVo.state != ''">
            AND o.pay_state = #{map.searchVo.state}
        </if>

        <if test="map.searchVo.endPayTime != null">
            AND o.pay_time &lt; #{map.searchVo.endPayTime}
        </if>
        <if test="map.searchVo.startPayTime != null">
            AND o.pay_time &gt;= #{map.searchVo.startPayTime}
        </if>
        ORDER BY o.code
    </select>

    <select id="getByPark" resultType="cn.uone.bean.entity.business.bil.vo.ParkOrderVo">
        SELECT a.license,a.car_owner singer,a.car_owner_tel tel,o.*,oi.start_time startTime,oi.end_time endTime
        FROM t_bil_order o
        LEFT JOIN t_bil_order_item oi on oi.order_id = o.id
        LEFT JOIN t_parking_apply a on o.contract_id = a.id
        where o.order_type = '448'
        <if test="map.searchVo.license != null and map.searchVo.license != ''">
            and (a.license  like CONCAT('%', #{map.searchVo.license}, '%')
            OR a.auxiliary_license like CONCAT('%', #{map.searchVo.license}, '%'))
        </if>
        <if test="map.searchVo.singer != null and map.searchVo.singer != ''">
            and a.car_owner like CONCAT('%', #{map.searchVo.singer}, '%')
        </if>
        order by o.create_date desc
    </select>

    <select id="getPayStateByContractId" resultType="java.lang.String">
        select pay_state as payState from t_bil_order o
        where 1=1 and o.contract_id = #{contractId}
        order by o.create_date asc limit 1
    </select>

    <select id="getByParkOrder"  parameterType="java.util.Map" resultType="cn.uone.bean.entity.business.bil.vo.ParkOrderVo">
        SELECT a.license,a.car_owner singer,a.car_owner_tel tel,o.*,oi.start_time startTime,oi.end_time endTime,o.pay_way
        FROM t_bil_order o
        LEFT JOIN t_bil_order_item oi on oi.order_id = o.id
        LEFT JOIN t_parking_apply a on o.contract_id = a.id
        where o.order_type = '448'
        <if test="map.searchVo.license != null and map.searchVo.license != ''">
            and a.license  like CONCAT('%', #{map.searchVo.license}, '%')
        </if>
        <if test="map.searchVo.singer != null and map.searchVo.singer != ''">
            and a.car_owner like CONCAT('%', #{map.searchVo.singer}, '%')
        </if>
        <if test="map.searchVo.ids != null and map.searchVo.ids.size() > 0">
            AND o.id in
            <foreach collection="map.searchVo.ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by o.create_date desc
    </select>

    <select id="countPayAndClear" resultType="java.util.Map">
        SELECT p.name,COUNT(1) total,SUM(IF(t.`pay_state`='20',1,0)) paied,
        SUM(IF(t.`pay_state`='10' AND t.`payable_time` &lt; NOW(),1,0)) overdue,
        SUM(IF(t.`pay_state`='20' AND t.`payable_time` &lt; t.`pay_time`,1,0)) clear,
        SUM(IF(t.`pay_state`='20',t.`actual_payment`,0)) payment
        FROM t_bil_order t
        LEFT JOIN t_res_source s ON s.id=t.`source_id`
        LEFT JOIN t_res_project p ON p.id=s.`project_id`
        LEFT JOIN t_sys_area a ON a.`id`=p.`city_id`
        WHERE 1=1 AND t.`is_push`='1'
        <if test="cityCode !=null and cityCode !=''">
            AND a.code=#{cityCode}
        </if>
        GROUP BY p.id
    </select>

    <select id="countRentOrders" resultType="java.util.Map">
        SELECT DATE_FORMAT(i.`start_time`,'%Y%m') yearMonth,SUM(i.payment) payment
        FROM t_bil_order t,t_bil_order_item i
        WHERE t.id=i.`order_id` AND t.order_type='20' AND t.`pay_state` IN ('20','90')
        <if test="year !=null and year !=''">
            AND DATE_FORMAT(i.`start_time`,'%Y')=#{year}
        </if>
        <!--需确认是否添加该判断-->
        <!-- AND i.`start_time` &lt;= now()-->
        GROUP BY DATE_FORMAT(i.`start_time`,'%Y%m')
        ORDER BY DATE_FORMAT(i.`start_time`,'%Y%m')
    </select>
    <select id="queryGuidaoOrder" resultMap="IncludeItemResultMap">
        SELECT o.id, is_sync, ccb_bill_id, o.code, merge_code, source_id,
        contract_id, discount_log_id, payable_payment, actual_payment,
        o.payment, trade_code, pay_way, account_id,
        pay_unit, pay_state, pay_time, order_type,
        payer_id, data_from, invoice_type, invoice_state,
        invoice_payment, approval_state, is_push, push_time,
        payable_time, clean_time, cancel_time, is_discard,
        is_first, is_initial, o.remark,
        o.transfer_state, o.create_by, o.create_date, o.update_by, o.update_date,
        apply_inv_time, transfer_mode, report_date,
        intention, bills_code, bills_state,
        demand_payment_count, old_renter_id, mer_order_id,
        oi.id,oi.order_id,oi.create_date,oi.create_by,oi.update_date,oi.update_by,
        oi.payment,oi.order_item_type,oi.start_time,oi.end_time,oi.arrive_time,oi.price,
        oi.num,oi.start_read,oi.end_read,oi.device_id
        FROM t_bil_order o LEFT JOIN t_bil_order_item oi ON o.id = oi.order_id
        LEFT JOIN t_res_source s ON o.source_id = s.id
        LEFT JOIN t_res_project p ON p.id = s.project_id
        WHERE p.code IN ('AMYN','AMBH')
        <if test="endTime != null">
            AND o.payable_time &lt; #{endTime}
        </if>
        <if test="startTime != null">
            AND o.payable_time &gt;= #{startTime}
        </if>
        AND o.pay_state NOT IN
        <foreach collection="payStates" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getSdOrderList" resultType="java.util.Map">
        SELECT t.`order_type` orderType,t.`payment`,i.`start_read` startRead,i.`end_read` endRead,
               i.`start_time` startTime,
               i.`end_time` endTime,
               DATE_FORMAT(t.`create_date`,'%Y-%m-%d') createDate,
               s.`source_name`
        FROM t_bil_order t
                 LEFT JOIN t_bil_order_item i ON i.`order_id`=t.`id`
                 LEFT JOIN v_res_source s ON s.id=t.`source_id`
        WHERE 1=1
        <if test="map.orderType !=null and map.orderType !=''">
            AND t.`order_type` = #{map.orderType}
        </if>
        <if test="map.payWay !=null and map.payWay !=''">
            AND t.`pay_way` = #{map.payWay}
        </if>
        <if test="map.payerId !=null and map.payerId !=''">
            AND t.`payer_id` = #{map.payerId}
        </if>
        <if test="map.contractId !=null and map.contractId !=''">
            AND t.contract_id = #{map.contractId}
        </if>
        ORDER BY i.`start_time` DESC
    </select>
        <!--
    查询符合条件的 BilOrderItemWithOrderVo 列表
    @param direction 价格方向，大于 0 表示正价，等于 0 表示无限制，小于 0 表示负价
    @param projectCodes 项目代码列表
    @param orderItemTypes 订单项目类型列表
    @param startTime 应付时间开始范围
    @param endTime 应付时间结束范围
    @param ids 订单项目 ID 列表
    @return 符合条件的 ItemWithOrderVo 结果列表
    -->
    <select id="getItemWithOrderVoList" resultMap="ItemWithOrderVo">
        SELECT
        MAX(oi.id) AS id,
        o.id AS order_id,
        MAX(oi.code) AS code,
        SUM(oi.payment) AS payment,
        MAX(oi.fix_before) AS fix_before,
        MAX(oi.fix_after) AS fix_after,
        MAX(oi.order_item_type) AS order_item_type,
        MIN(oi.start_time) AS start_time,
        MAX(oi.end_time) AS end_time,
        MAX(oi.price) AS price,
        SUM(oi.num) AS num,
        MAX(oi.start_read) AS start_read,
        MAX(oi.end_read) AS end_read,
        MAX(oi.device_id) AS device_id,
        MAX(oi.transfer_state) AS transfer_state,
        MAX(oi.arrive_code) AS arrive_code,
        MAX(oi.merchant_id) AS merchant_id,
        MAX(oi.arrive_time) AS arrive_time,
        MAX(oi.bank_serial_code) AS bank_serial_code,
        MAX(oi.create_by) AS create_by,
        MAX(oi.create_date) AS create_date,
        MAX(oi.update_by) AS update_by,
        MAX(oi.update_date) AS update_date,
        MAX(oi.invoice_id) AS invoice_id,
        MAX(oi.state_owned_assets_push) AS state_owned_assets_push,
        o.approval_state,
        o.code AS ordercode,
        c.paper_code AS papercode,
        o.create_date,
        o.order_type,
        o.payment AS orderpayment,
        o.payable_payment,
        o.pay_way,
        o.pay_state,
        o.pay_time,
        o.payable_time,
        o.invoice_type,
        o.trade_code,
        o.transfer_mode,
        o.ccb_bill_id,
        o.mer_order_id,
        o.invoice_state,
        o.is_push,
        o.push_time,
        o.payer_id,
        r.time,
        o.invoice_payment,
        o.contract_id,
        s.source_name AS address,
        r.name AS payer,
        IFNULL(r2.name, r3.name) AS singer,
        c.signer_id AS singer_id,
        IFNULL(c.contract_code, '') AS contract_code,
        c.contract_type,
        c.is_organize,
        c.tax_point,
        s.project_id,
        o.intention,
        vu.real_name,
        ttb.transfer_time,
        o.bills_state,
        o.bills_code,
        s.projectname,
        CONCAT(s.partitionname, s.code) AS partitionname,
        DATE_FORMAT(o.pay_time, '%Y-%m-%d') AS paytimestr,
        ROUND((oi.payment - oi.payment * 100 / (100 + IFNULL(c.tax_point, 0))), 2) AS tax_amount,
        s.area,
        csr.source_id AS source_ids,
        r2.tax_org_name AS taxorgname,
        p.code AS projectcode,
        c.platform            <!-- 平台信息 -->
        FROM
        t_bil_order o
        LEFT JOIN
        t_bil_order_item oi ON o.id = oi.order_id
        LEFT JOIN
        t_cont_contract c ON o.contract_id = c.id
        LEFT JOIN
        t_cont_contract_source_rel csr ON csr.contract_id = c.id
        LEFT JOIN
        v_res_source s ON o.source_id = s.id
        LEFT JOIN
        t_res_project p ON p.id = s.project_id
        LEFT JOIN
        v_sys_renter r ON r.id = o.payer_id
        LEFT JOIN
        v_sys_user vu ON vu.id = o.create_by
        LEFT JOIN
        t_cont_contract_info r2 ON r2.contract_id = c.id
        LEFT JOIN
        v_sys_renter r3 ON c.signer_id = r3.id
        LEFT JOIN
        t_bil_transfer ttb ON ttb.order_id = o.id
        WHERE
        o.pay_state NOT IN ('40','70') <!-- 支付状态不在 40 和 70 范围内 -->
        AND oi.payment > 0      <!-- 订单项目单价不为空 -->
        AND NOT EXISTS(SELECT 1 FROM t_cosmic_income ci WHERE ci.source_bill_number = oi.order_id ) <!-- 不存在对应的宇宙收入记录 -->
<!--        <choose>-->
<!--            &lt;!&ndash; 根据 direction 参数筛选价格方向 &ndash;&gt;-->
<!--            <when test="direction != null and direction > 0">-->
<!--                AND oi.price &gt; 0  &lt;!&ndash; 正价项目 &ndash;&gt;-->
<!--            </when>-->
<!--            <when test="direction == 0">-->
<!--                &lt;!&ndash; 不添加价格方向筛选条件 &ndash;&gt;-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                AND oi.price &lt; 0  &lt;!&ndash; 负价项目 &ndash;&gt;-->
<!--            </otherwise>-->
<!--        </choose>-->
        <if test="projectCodes != null and projectCodes.size()>0">
            AND p.code IN
            <foreach collection="projectCodes" separator="," open="(" close=")" item="projectCode">
                #{projectCode}
            </foreach>
            <!-- 项目代码在指定列表内 -->
        </if>
        <if test="orderItemTypes != null and orderItemTypes.size()> 0">
            AND oi.order_item_type IN
            <foreach collection="orderItemTypes" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
            <!-- 订单项目类型在指定列表内 -->
        </if>
        <if test="startTime != null">
            AND o.payable_time &gt;= #{startTime} <!-- 应付时间大于等于开始时间 -->
        </if>
        <if test="endTime != null">
            AND o.payable_time &lt; #{endTime} <!-- 应付时间小于结束时间 -->
        </if>
        <if test="ids != null and ids.size()>0">
            AND oi.id IN
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            <!-- 订单项目 ID 在指定列表内 -->
        </if>
        GROUP BY
        o.id,
        o.create_date
        ORDER BY
        o.create_date DESC
        limit 1;

    </select>

    <!-- 
    根据应付时间(payable_time)查询可生成应付单的订单数据
    @param startTime 开始时间
    @param endTime 结束时间
    @param orderItemTypes 订单项类型列表
    @param projectCodes 项目代码列表
    @return 符合条件的订单项与订单组合数据列表
    -->
    <select id="getItemWithOrderVoListByPayableTime" resultMap="ItemWithOrderVo">
        SELECT
        oi.id,
        o.id AS order_id,
        oi.code,
        oi.payment,
        oi.fix_before,
        oi.fix_after,
        oi.order_item_type,
        oi.start_time,
        oi.end_time,
        oi.price,
        oi.num,
        oi.start_read,
        oi.end_read,
        oi.device_id,
        oi.transfer_state,
        oi.arrive_code,
        oi.merchant_id,
        oi.arrive_time,
        oi.bank_serial_code,
        oi.create_by,
        oi.create_date,
        oi.update_by,
        oi.update_date,
        oi.invoice_id,
        oi.state_owned_assets_push,
        o.approval_state,
        o.code AS ordercode,
        c.paper_code AS papercode,
        o.create_date,
        o.payer_id,
        o.order_type,
        o.payment AS orderpayment,
        o.payable_payment,
        o.pay_way,
        o.pay_state,
        o.pay_time,
        o.payable_time,
        o.invoice_type,
        o.trade_code,
        o.transfer_mode,
        o.ccb_bill_id,
        o.mer_order_id,
        o.invoice_state,
        o.is_push,
        o.push_time,
        o.payer_id,
        r.time,
        o.invoice_payment,
        o.contract_id,
        o.source_id,
        s.source_name AS address,
        r.name AS payer,
        IFNULL(r2.name, r3.name) AS singer,
        c.signer_id AS singer_id,
        IFNULL(c.contract_code, '') AS contract_code,
        c.contract_type,
        c.is_organize,
        c.tax_point,
        s.project_id,
        o.intention,
        vu.real_name,
        ttb.transfer_time,
        o.bills_state,
        o.bills_code,
        s.projectname,
        CONCAT(s.partitionname, s.code) AS partitionname,
        DATE_FORMAT(o.pay_time, '%Y-%m-%d') AS paytimestr,
        ROUND((oi.payment - oi.payment * 100 / (100 + IFNULL(c.tax_point, 0))), 2) AS tax_amount,
        s.area,
        csr.source_id AS source_ids,
        r2.tax_org_name AS taxorgname,
        p.code AS projectcode,
        c.platform
        FROM
        t_bil_order o
        INNER JOIN
        t_bil_order_item oi ON o.id = oi.order_id
        LEFT JOIN
        t_cont_contract c ON o.contract_id = c.id
        LEFT JOIN
        t_cont_contract_source_rel csr ON csr.contract_id = c.id
        LEFT JOIN
        v_res_source s ON o.source_id = s.id
        LEFT JOIN
        t_res_project p ON p.id = s.project_id
        LEFT JOIN
        v_sys_renter r ON r.id = o.payer_id
        LEFT JOIN
        v_sys_user vu ON vu.id = o.create_by
        LEFT JOIN
        t_cont_contract_info r2 ON r2.contract_id = c.id
        LEFT JOIN
        v_sys_renter r3 ON c.signer_id = r3.id
        LEFT JOIN
        t_bil_transfer ttb ON ttb.order_id = o.id
        WHERE
        o.pay_state NOT IN ('40','70')
        AND NOT EXISTS(
            SELECT 1 FROM t_cosmic_accounts_payable ap 
            WHERE ap.source_bill_number = oi.id
        ) <!-- 确保不存在对应的应付单记录 -->
        <if test="projectCodes != null and projectCodes.size()>0">
            AND p.code IN
            <foreach collection="projectCodes" separator="," open="(" close=")" item="projectCode">
                #{projectCode}
            </foreach>
        </if>
        <if test="orderItemTypes != null and orderItemTypes.size()> 0">
            AND oi.order_item_type IN
            <foreach collection="orderItemTypes" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <!-- 根据应付时间查询 -->
        <if test="startTime != null">
            AND o.payable_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND o.payable_time &lt; #{endTime}
        </if>
        ORDER BY o.payable_time, o.create_date
        limit 1;
    </select>


</mapper>
