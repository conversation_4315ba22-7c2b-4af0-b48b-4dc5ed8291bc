<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.bil.dao.BilOrderItemPreviewDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.bil.BilOrderItemPreviewEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="order_id" property="orderId" />
        <result column="code" property="code" />
        <result column="payment" property="payment" />
        <result column="fix_before" property="fixBefore" />
        <result column="fix_after" property="fixAfter" />
        <result column="order_item_type" property="orderItemType" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="price" property="price" />
        <result column="num" property="num" />
        <result column="start_read" property="startRead" />
        <result column="end_read" property="endRead" />
        <result column="device_id" property="deviceId" />
        <result column="transfer_state" property="transferState" />
        <result column="arrive_code" property="arriveCode" />
        <result column="merchant_id" property="merchantId" />
        <result column="arrive_time" property="arriveTime" />
        <result column="bank_serial_code" property="bankSerialCode" />
        <result column="invoice_id" property="invoiceId" />
        <result column="state_owned_assets_push" property="stateOwnedAssetsPush" />
        <result column="price_strategy_id" property="priceStrategyId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        order_id, code, payment, fix_before, fix_after, order_item_type, start_time, end_time, price, num, start_read, end_read, device_id, transfer_state, arrive_code, merchant_id, arrive_time, bank_serial_code, invoice_id, state_owned_assets_push, price_strategy_id
    </sql>

</mapper>
