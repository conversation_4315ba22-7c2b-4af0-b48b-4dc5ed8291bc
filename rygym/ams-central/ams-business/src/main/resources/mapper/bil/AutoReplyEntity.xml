<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.bil.dao.AutoReplyDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.bil.AutoReplyEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="key_words" property="keyWords" />
        <result column="reply" property="reply" />
        <result column="type" property="type" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        key_words, reply, type
    </sql>

    <select id="getByKeyWord" resultType="cn.uone.bean.entity.business.bil.AutoReplyEntity">
        select * from t_auto_reply t
        where t.key_words = #{keyWord}
        order by t.create_date desc
        limit 0,1
    </select>

    <select id="queryAutoReplyPage" resultType="cn.uone.bean.entity.business.bil.AutoReplyEntity">
        select * from t_auto_reply t
        WHERE 1 = 1
        <if test="map.searchVo.keyWords != null and map.searchVo.keyWords != ''">
            AND t.key_words = #{map.searchVo.keyWords}
        </if>
    </select>

</mapper>
