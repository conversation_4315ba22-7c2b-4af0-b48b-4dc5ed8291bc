<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.res.dao.ResRepairDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.res.ResRepairEntity">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="project_id" property="projectId"/>
        <result column="partition_id" property="partitionId"/>
        <result column="source_id" property="sourceId"/>
        <result column="requester" property="requester"/>
        <result column="tel" property="tel"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="position" property="position"/>
        <result column="type" property="type"/>
        <result column="user_id" property="userId"/>
        <result column="renter_id" property="renterId"/>
        <result column="summary" property="summary"/>
        <result column="state" property="state"/>
        <result column="manager_id" property="managerId"/>
        <result column="manager" property="manager"/>
        <result column="deal_summary" property="dealSummary" />
        <result column="re_without_renter" property="reWithoutRenter" />
        <result column="repair_item_type" property="repairItemType" />
        <result column="comment" property="comment" />
        <result column="comment_date" property="commentDate" />
        <result column="checked_date" property="checkedDate" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,code,area,
        project_id,partition_id,source_id,requester,tel,manager_id,manager,
        create_by,
        create_date,
        update_by,
        update_date,
        position, type, user_id, renter_id, summary, state, deal_summary,re_without_renter,repair_item_type,comment,comment_date,checked_date
    </sql>

    <resultMap id="selectVoMap" type="cn.uone.bean.entity.business.res.vo.SelectVo">
        <result column="value" property="value"/>
        <result column="name" property="name"/>
    </resultMap>

    <resultMap id="resRepairMap" type="cn.uone.bean.entity.business.res.vo.ResRepairEntityVo" extends="BaseResultMap">
        <collection property="list" javaType="java.util.ArrayList" column="id" select="querySysFile"/>
    </resultMap>
        <resultMap type="java.lang.String" id="querySysFileMap">
        <result column="url" property="url"/>
    </resultMap>

    <select id="countRepairNum" resultType="java.util.Map">
       select ifnull(sum(if(state = '1',1,0)),0) waitNum,
			 ifnull(sum(if(state = '2',1,0)),0) tobeNum
      from t_res_repair t  where 1=1 #project_datascope#
        <if test="userId != null and userId != ''">
            and (t.user_id = #{userId} or t.manager_id =#{userId})
        </if>
        <if test="projectId != null and projectId != ''">
            and t.project_id = #{projectId}
        </if>
    </select>

    <select id="getStatistics" resultType="java.util.Map">
        select ifnull(sum(if(state = '1',1,0)),0) waitNum,
        ifnull(sum(if(state = '2',1,0)),0) tobeNum,
        ifnull(sum(if(state in('1','2'),1,0)),0) total
        from t_res_repair t  where 1=1 #project_datascope#
        and t.project_id = #{map.projectId}
        <choose>
            <when test="map.repairType !=null and map.repairType!=''">
                and t.type=#{map.repairType}
            </when>
            <otherwise>
                AND t.type in ('1','3')
            </otherwise>
        </choose>
    </select>


    <select id="queryPage" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>,DATE_FORMAT(t.create_date,'%Y-%m-%d') createTime , DATE_FORMAT(t.update_date,'%Y-%m-%d') updateTime
        from t_res_repair t
        where 1=1  #project_datascope#
        <if test="vo.requester !=null and vo.requester!=''">
            and t.requester like CONCAT('%',#{vo.requester},'%')
        </if>

        <if test="vo.manager !=null and vo.manager!=''">
            and t.manager like CONCAT('%',#{vo.manager},'%')
        </if>

        <if test="vo.projectId !=null and vo.projectId!=''">
            and t.project_id = #{vo.projectId}
        </if>
        <if test="vo.sourceId !=null and vo.sourceId!=''">
            and t.source_id = #{vo.sourceId}
        </if>
        <!--<if test="vo.userId !=null and vo.userId!=''">
            and (t.user_id = #{vo.userId} or t.manager_id =#{vo.userId})
        </if>-->
        <if test="vo.statein != null and vo.statein != ''">
            AND t.state in
            <foreach collection="vo.statein" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.state !=null and vo.state !=''">
            and t.state =#{vo.state}
        </if>
        <if test="vo.types != null and vo.types != ''">
            AND t.type in
            <foreach collection="vo.types" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.type !=null and vo.type!=''">
            and t.type=#{vo.type}
        </if>
        <if test="vo.userType!=null and vo.userType!=''">
            <if test="vo.userType=='User'">
                and t.user_id is not null
            </if>
            <if test="vo.userType=='Custom'">
                and t.renter_id is not null
            </if>
        </if>
        <if test="vo.startTime!=null and vo.startTime!=''">
            and t.create_date &gt; #{vo.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="vo.endTime !=null and vo.endTime !=''">
            and t.create_date &lt; #{vo.endTime,jdbcType=TIMESTAMP}
        </if>

        <if test="vo.updateStartTime!=null and vo.updateStartTime!=''">
            and date(t.update_date) &gt;= #{vo.updateStartTime} and t.state='4'
        </if>
        <if test="vo.updateEndTime !=null and vo.updateEndTime !=''">
            and date(t.update_date) &lt;= #{vo.updateEndTime} and t.state='4'
        </if>
        <if test="vo.keyword !='' and vo.keyword !=null">
            and (t.code like CONCAT('%',#{vo.keyword},'%') or t.summary like CONCAT('%',#{vo.keyword},'%'))
        </if>
        <choose>
            <when test="vo.state !=null and vo.state !=''">
                <choose>
                    <when test="vo.state == 4">
                        order by create_date desc
                    </when>
                    <otherwise>
                        order by create_date asc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by create_date desc
            </otherwise>
        </choose>
    </select>
<!--projects,partitions,codes这个3个的select查询中的map参数不要加上if判断-->
<!--    <select id="projects" resultMap="selectVoMap" parameterType="java.util.Map">-->
<!--        SELECT-->
<!--            c.id VALUE,-->
<!--            c.`name`-->
<!--        FROM-->
<!--            t_cont_contract t,-->
<!--            t_cont_contract_source_rel a,-->
<!--            t_res_source b,-->
<!--            t_res_project c-->
<!--        WHERE-->
<!--            t.id = a.contract_id-->
<!--        AND a.source_id = b.id-->
<!--        AND b.project_id = c.id-->
<!--        AND t.signer_id=#{map.renterId}-->
<!--        GROUP BY VALUE,`name`-->
<!--    </select>-->
    <!--12/11 原来是根据签约的合同来选择可以提交报修的门店，改成根据房源入住状态来选择门店-->
    <select id="projects" resultMap="selectVoMap" parameterType="java.util.Map">
        SELECT
            p.id VALUE,
            p.`name`
        FROM
            t_res_project p
        LEFT JOIN t_res_source  s ON s.project_id=p.id
        LEFT JOIN t_cont_contract_source_rel cs ON cs.source_id=s.id
        LEFT JOIN t_cont_contract cc ON cc.id = cs.contract_id
        LEFT JOIN t_cont_check_in_house ch ON ch.contract_source_id=cs.id
        LEFT JOIN t_cont_check_in_user cu ON cu.renter_id=ch.renter_id
        WHERE
        cu.is_checkin='1'
        AND cc.state in ('6','8','14','12')
        AND ch.renter_id=#{map.renterId}
        GROUP BY VALUE,`name`
    </select>


    <select id="partitions" resultMap="selectVoMap" parameterType="java.util.Map">
        SELECT
            c.id VALUE,
            c.`name`
        FROM
            t_cont_contract t,
            t_cont_contract_source_rel a,
            t_res_source b,
            t_res_plan_partition c
        WHERE
            t.id = a.contract_id
        AND a.source_id = b.id
        AND b.partition_id = c.id
        AND t.signer_id=#{map.renterId}
        AND c.project_id=#{map.projectId}
        GROUP BY VALUE,`name`
    </select>
    <select id="codes" resultMap="selectVoMap" parameterType="java.util.Map">
        SELECT
            b.id VALUE,
            b.code name
        FROM
            t_cont_contract t,
            t_cont_contract_source_rel a,
            t_res_source b
        WHERE
            t.id = a.contract_id
        AND a.source_id = b.id
        AND t.signer_id=#{map.renterId}
        AND b.partition_id=#{map.partitionId}
        AND t.state in ('6','8','12','14')
        <if test="map.state !='' and map.state !=null">
            AND b.state=#{map.state}
        </if>
        <if test="map.sourceType !='' and map.sourceType !=null">
            AND b.source_type=#{map.sourceType}
        </if>
        GROUP BY VALUE,`name`
    </select>

    <select id="getCodes" resultMap="selectVoMap" parameterType="java.util.Map">
        SELECT
            s.id VALUE,
            s.source_name name
        from t_cont_check_in_user t  , t_cont_contract_source_rel tc , t_cont_contract c ,v_res_source s
        WHERE t.contract_source_id = tc.id and tc.contract_id = c.id and tc.source_id = s.id
            AND c.state in ('6','8','14','12') and t.is_checkin = '1'
            AND s.project_id =#{map.projectId}
            AND t. renter_id=#{map.renterId}
    </select>

    <select id="queryResRepair" resultMap="resRepairMap">
        select * from t_res_repair where id=#{id}
    </select>
    <select id="querySysFile" resultMap="querySysFileMap" >
        select url from t_sys_file where from_id=#{id}
    </select>
    <select id="getAreaByDataScopeAndFilter" resultType="cn.uone.bean.entity.crm.ZoneEntity">
        select
        p.id,
        p.id as "value",
        p.project_id,p.name,p.property_nature,p.property_type,p.cert_code,p.property_owner
        from v_res_plan_partition p
        where 1 = 1
        <if test="map.projectId !=null and map.projectId !=''">
            and p.project_id = #{map.projectId}
        </if>
        <if test="map.propertyNature !=null">
            and p.property_nature in
            <foreach item="item" index="index" collection="map.propertyNature" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        #zone_datascope#
    </select>

    <select id="getAreaByFilter" resultType="cn.uone.bean.entity.crm.ZoneEntity">
        select
        p.id as "value",
        p.name
        from v_res_plan_partition p
        where 1 = 1
        <if test="map.projectId !=null and map.projectId !=''">
            and p.project_id = #{map.projectId}
        </if>
        <if test="map.propertyNature !=null">
            and p.property_nature in
            <foreach item="item" index="index" collection="map.propertyNature" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.haveCar==0">
            and p.property_nature!='2'
        </if>
    </select>

    <select id="countTotalByMonths" resultType="java.util.Map">
        SELECT CONVERT(DATE_FORMAT(t.`create_date`,'%m'),SIGNED) i,
        CONVERT(COUNT(1),SIGNED) num
        FROM t_res_repair t
        LEFT JOIN t_res_project p ON p.id=t.`project_id`
        LEFT JOIN t_sys_area a ON a.`id`=p.`city_id`
        WHERE 1=1 AND DATE_FORMAT(t.`create_date`,'%Y')=DATE_FORMAT(CURDATE(),'%Y')
        <if test="cityCode !=null and cityCode !=''">
            AND a.code=#{cityCode}
        </if>
        <if test="types !=null">
            and t.type in
            <foreach item="item" index="index" collection="types" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY DATE_FORMAT(t.`create_date`,'%m')
    </select>
    <select id="countRepairByMonths" resultType="java.util.Map">
        SELECT CONVERT(DATE_FORMAT(t.`update_date`,'%m'),SIGNED) i,
        CONVERT(SUM(IF(t.state IN('1','2','3'),0,1)),SIGNED) num
        FROM t_res_repair t
        LEFT JOIN t_res_project p ON p.id=t.`project_id`
        LEFT JOIN t_sys_area a ON a.`id`=p.`city_id`
        WHERE 1=1 AND DATE_FORMAT(t.`update_date`,'%Y')=DATE_FORMAT(CURDATE(),'%Y')
        <if test="cityCode !=null and cityCode !=''">
            AND a.code=#{cityCode}
        </if>
        <if test="types !=null">
            and t.type in
            <foreach item="item" index="index" collection="types" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY DATE_FORMAT(t.`update_date`,'%m')
    </select>

    <select id="getBySourceId" resultType="cn.uone.bean.entity.business.res.ResRepairEntity">
        SELECT *
        from t_res_repair
        where
        source_id = #{sourceId}
        and state  in('1', '2', '0')
        <if test="isClean ==null || isClean ==''">
            AND type in('1', '3')
        </if>
        <if test="isClean !=null and isClean !=''">
            AND type = '5'
        </if>
    </select>

    <select id="getRepairUsersByMap" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT u.* FROM
        (SELECT t.id,t.real_name realName,COUNT(r.id) num FROM v_sys_user t
        LEFT JOIN t_res_repair r ON t.id=r.`manager_id` AND r.state IN('0','2')
        WHERE 1=1
        <if test="map.userIdList !=null and map.userIdList !=''">
            and t.id in
            <foreach item="item" index="index" collection="map.userIdList" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
        GROUP BY t.id) u ORDER BY u.num;
    </select>

</mapper>
