<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.res.dao.ResHouseTypeDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.res.ResHouseTypeEntity">
        <result column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="name" property="name"/>
        <result column="room" property="room"/>
        <result column="hall" property="hall"/>
        <result column="toilet" property="toilet"/>
        <result column="restaurant" property="restaurant"/>
        <result column="kitchen" property="kitchen"/>
        <result column="balcony" property="balcony"/>
        <result column="public_hall" property="publicHall"/>
        <result column="public_toilet" property="publicToilet"/>
        <result column="public_balcony" property="publicBalcony"/>
        <result column="public_restaurant" property="publicRestaurant"/>
        <result column="public_kitchen" property="publicKitchen"/>
        <result column="price_range" property="priceRange"/>
        <result column="lease_duration" property="leaseDuration"/>
        <result column="pay_type" property="payType"/>
        <result column="quit_rule" property="quitRule"/>
        <result column="area_range" property="areaRange"/>
        <result column="vr_path" property="vrPath"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,project_id,restaurant,kitchen,public_restaurant,public_kitchen,
        create_by,
        create_date,
        update_by,
        update_date,
        name, room, hall, toilet, balcony, public_hall, public_toilet, public_balcony,lease_duration,pay_type,quit_rule,area_range,vr_path
    </sql>

    <resultMap id="resHouseTypeVo" type="cn.uone.bean.entity.business.res.vo.ResHouseTypeVo" extends="BaseResultMap">
        <result column="pic" property="pic"/>
        <result column="name" property="name"/>
        <result column="project_id" property="projectId"/>
        <result column="address" property="address"/>
        <result column="project_id" property="projectId"/>
        <result column="minPrice" property="minPrice"/>
        <result column="maxPrice" property="maxPrice"/>
        <result column="minArea" property="minArea"/>
        <result column="maxArea" property="maxArea"/>
        <result column="info" property="info"/>
        <result column="projectName" property="projectName"/>
        <result column="address" property="address"/>
        <result column="noStock" property="noStock"/>
        <result column="distance" property="distance"/>
        <result column="isCollected" property="isCollected"/>
        <result column="alias" property="alias"/>
        <result column="priceMin" property="priceMin"/>
        <collection property="files" ofType="cn.uone.bean.entity.business.sys.SysFileEntity">
            <id property="id" column="fileId"/>
            <result property="type" column="type"/>
            <result property="url" column="url"/>
        </collection>
    </resultMap>


    <resultMap id="resHouseTypeVo2" type="cn.uone.bean.entity.business.res.vo.ResHouseTypeVo" extends="BaseResultMap">
        <result column="pic" property="pic"/>
        <result column="name" property="name"/>
        <result column="project_id" property="projectId"/>
        <result column="address" property="address"/>
    </resultMap>



    <select id="listPage" parameterType="java.util.Map" resultMap="resHouseTypeVo2">
        select t.*,f.url as pic,f.id as file_id from t_res_house_type t
        left join t_sys_file f on f.from_id=t.id and f.type='15'
        where 1=1
        <if test="searchVo.id != null and searchVo.id != ''">
            AND t.id = #{searchVo.id}
        </if>
        <if test="searchVo.name != null and searchVo.name != ''">
            AND t.name like "%"#{searchVo.name}"%"
        </if>
        <if test="searchVo.projectId != null and searchVo.projectId != ''">
            AND t.project_id = #{searchVo.projectId}
        </if>
        <if test="searchVo.keyWord != null and searchVo.keyWord != ''">
            and t.name like CONCAT('%', #{searchVo.keyWord}, '%')
        </if>
        <if test="searchVo.room != null and searchVo.room != ''">
            AND t.room = #{searchVo.room}
        </if>
        <if test="searchVo.hall != null and searchVo.hall != ''">
            AND t.hall = #{searchVo.hall}
        </if>
        <if test="searchVo.toilet != null and searchVo.toilet != ''">
            AND t.toilet = #{searchVo.toilet}
        </if>
        <if test="searchVo.kitchen != null and searchVo.kitchen != ''">
            AND t.kitchen = #{searchVo.kitchen}
        </if>
        <if test="searchVo.restaurant != null and searchVo.restaurant != ''">
            AND t.restaurant = #{searchVo.restaurant}
        </if>
        <if test="searchVo.balcony != null and searchVo.balcony != ''">
            AND t.balcony = #{searchVo.balcony}
        </if>
        <if test="searchVo.publicHall != null and searchVo.publicHall != ''">
            AND t.public_hall = #{searchVo.publicHall}
        </if>
        <if test="searchVo.publicRestaurant != null and searchVo.publicRestaurant != ''">
            AND t.public_restaurant = #{searchVo.publicRestaurant}
        </if>
        <if test="searchVo.publicKitchen != null and searchVo.publicKitchen != ''">
            AND t.public_kitchen = #{searchVo.publicKitchen}
        </if>
        <if test="searchVo.publicToilet != null and searchVo.publicToilet != ''">
            AND t.public_toilet = #{searchVo.publicToilet}
        </if>
        <if test="searchVo.publicBalcony != null and searchVo.publicBalcony != ''">
            AND t.public_balcony = #{searchVo.publicBalcony}
        </if>
        group by t.id
        order by t.create_date desc
    </select>


    <select id="allTypesPage" parameterType="java.util.Map" resultMap="resHouseTypeVo">
        select CONCAT(t.room,"室",t.hall,"厅",t.toilet,"卫") as info,t.id,t.project_id,
        t.name,t.price_range,t.area_range,t.alias,f.url,f.id as fileId,f.type,p.name as projectName,p.address as address,
        MIN(sc.price)minPrice,MAX(sc.price)maxPrice,MIN(s.area)minArea,MAX(s.area)maxArea,
        IF(
        EXISTS(
        SELECT s2.id from v_res_source s2
        LEFT JOIN t_res_house_type t2 on t2.id=s2.house_type_id
        WHERE s2.state='0' and t2.id=t.id
        ),FALSE,TRUE)noStock,
        ROUND(st_distance_sphere(POINT(#{searchVo.longitude}, #{searchVo.latitude}),POINT(p.`longitude`, p.`latitude`))/1000,1) AS distance,
        IF(c.state='1',TRUE,FALSE) isCollected,SUBSTRING_INDEX(t.price_range,'-',1) priceMin,t.vr_path
        from t_res_house_type t
        left join t_sys_file f on f.from_id=t.id and f.type='47'
        left join t_res_project p on t.project_id=p.id
        left join v_res_source s on s.house_type_id=t.id
        left join t_res_source_configure sc on sc.source_id=s.id
        left join t_res_collect c on c.collect_id=t.id and c.collect_type='2'
        where 1=1
        <if test="searchVo.isHot != null and searchVo.isHot != ''">
          and EXISTS (SELECT 1 FROM t_res_house_type_hot h WHERE h.type_id=t.id)
        </if>
        <if test="searchVo.keyWord != null and searchVo.keyWord != ''">
            and (LOCATE(#{searchVo.keyWord},t.name)
            or LOCATE(#{searchVo.keyWord},p.name)
            or LOCATE(#{searchVo.keyWord},p.address)
            or LOCATE(#{searchVo.keyWord},CONCAT(t.room,"室",t.hall,"厅",t.toilet,"卫")))
        </if>
        <if test="searchVo.projectId != null and searchVo.projectId != ''">
            and t.project_id=#{searchVo.projectId}
        </if>
        group by t.id
        order by t.create_date desc
    </select>



    <select id="getTypeDetails" parameterType="java.util.Map" resultMap="resHouseTypeVo2">
        select CONCAT(t.room,"室",t.hall,"厅",t.toilet,"卫") as info,t.summary,t.id,t.project_id,
        t.name,t.alias,f.url as pic,f.id as file_id,p.name as projectName,p.address as address,
        p.flag_conf flagConf,p.longitude,p.latitude,t.type_service typeService,
        MIN(sc.price)minPrice,MAX(sc.price)maxPrice,MIN(s.area)minArea,MAX(s.area)maxArea,
        SUBSTRING_INDEX(t.price_range,'-',1) priceMin,t.lease_duration,t.pay_type,t.quit_rule,t.area_range areaRange,t.vr_path
        from t_res_house_type t
        left join t_sys_file f on f.from_id=t.id and f.type='15'
        left join t_res_project p on t.project_id=p.id
        left join v_res_source s on s.house_type_id=t.id
        left join t_res_source_configure sc on sc.source_id=s.id
        where t.id=#{id}
    </select>
    <select id="getLetter" parameterType="java.util.Map" resultMap="resHouseTypeVo2">
        select name from t_res_house_type where
          project_id=#{projectId} and getLetter(name)=#{code} order by
           cast(substring(name, LENGTH(#{code})+1) as SIGNED INTEGER)
           desc
    </select>
    <select id="ccbRoomType" resultType="cn.uone.bean.entity.business.res.vo.ResHouseTypeVo">
            SELECT t.*,p.ccb_project_id,p.ccb_community_id,s.max_area,s.min_area,s.max_price,s.min_price
            FROM t_res_house_type t
            LEFT JOIN t_res_project p ON p.id = t.project_id
            LEFT JOIN (SELECT s.house_type_id,MAX(c.price)max_price,MIN(c.price)min_price,
            MAX(s.area)max_area,MIN(s.area)min_area
            FROM t_res_source s,t_res_source_configure c
            WHERE s.id = c.source_id GROUP BY s.house_type_id)s ON s.house_type_id = t.id
            where t.id = #{id}
    </select>

    <select id="typeList" parameterType="java.util.Map" resultMap="resHouseTypeVo2">
        select CONCAT(t.room,"室",t.hall,"厅",t.toilet,"卫",'',IFNULL(t.alias,'')) as info,t.*
               from t_res_house_type t
               where t.project_id=#{projectId}
    </select>

    <select id="allList"  resultMap="resHouseTypeVo2">
        select CONCAT(t.room,"室",t.hall,"厅",t.toilet,"卫",'',IFNULL(t.alias,'')) as info,t.*
        from t_res_house_type t
    </select>
</mapper>
