<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.res.dao.ResSourceConfigureRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.res.ResSourceConfigureRecordEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="source_id" property="sourceId" />
        <result column="price" property="price" />
        <result column="year_increase" property="yearIncrease" />
        <result column="summary" property="summary" />
        <result column="year_num" property="yearNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        source_id, price, year_increase, summary,year_num
    </sql>

    <select id="pageList"  resultType="cn.uone.bean.entity.business.res.ResSourceConfigureRecordEntity">
        select *,date_format(create_date,'%Y-%m-%d') as modify_time from t_res_source_configure_record
          where source_id=#{sourceId}
          order by create_date desc
    </select>
</mapper>
