<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.res.dao.ResSourcePublishDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.res.ResSourcePublishEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="source_id" property="sourceId"/>
        <result column="publish_target" property="publishTarget"/>
        <result column="title" property="title"/>
        <result column="rent_type" property="rentType"/>
        <result column="rent_mode" property="rentMode"/>
        <result column="is_short_rent" property="shortRent"/>
        <result column="manage_name" property="manageName"/>
        <result column="manage_tel" property="manageTel"/>
        <result column="start_date" property="startDate"/>
        <result column="taobao" property="taobao"/>
        <result column="pay_method_one" property="payMethodOne"/>
        <result column="pay_method_two" property="payMethodTwo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        source_id, publish_target, title, rent_type, rent_mode, is_short_rent, manage_name, manage_tel, start_date, taobao, pay_method_one, pay_method_two
    </sql>

    <select id="list"  resultType="cn.uone.bean.entity.business.res.vo.ResSourcePublishVo">
        select  s.*,rsc.price
        ,rsc.deposit,rsc.approval_state as approvalState
        ,if(s.source_type = '1',rht.name,CONCAT(rht.name,"(私有:",rht.room,"室",rht.toilet,"卫",rht.hall,"厅",
        if(rht.public_hall is null and rht.public_toilet is null,"",",共用"),IFNULL(rht.public_hall,""),
        if(rht.public_hall is null,"","厅"),IFNULL(rht.public_toilet,""),if(rht.public_hall is null,"","卫"),")")) AS `house_type_name`
        from v_res_source s
        left join  t_res_source_check sc on sc.source_id=s.id
        left join  t_res_source_configure rsc on rsc.source_id=s.id
        left join  t_res_house_type rht on rht.id=s.house_type_id
        where 1=1 #project_datascope# AND s.source_type!=2
        <if test="map.checkState != null and map.checkState != ''">
            AND sc.state= '1'
        </if>
        <if test="map.isShort != null">
            and s.is_short = #{map.isShort}
        </if>
        <if test="map.projectId != null and map.projectId != ''">
            AND s.project_id=#{map.projectId}
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND s.partition_id=#{map.partitionId}
        </if>
        <if test="map.floor != null and map.floor != ''">
            AND s.floor=#{map.floor}
        </if>
        <if test="map.state != null and map.state != ''">
            AND s.state=#{map.state}
        </if>
        <if test="map.code != null and map.code != ''">
            AND s.code like "%"#{map.code}"%"
        </if>
        <if test="map.publishState != null and map.publishState != ''">
            <if test="map.platform == null or map.platform == ''">
                <choose>
                <when test="map.publishState == 0">
                    and (s.publish_target=#{map.publishState}
                    and (s.zhifubao_publish_target=#{map.publishState}  or s.zhifubao_publish_target is null)
                    and (s.xianyu_publish_target=#{map.publishState}  or s.xianyu_publish_target is null)
                    and (s.mogo_publish_target=#{map.publishState}  or s.mogo_publish_target is null)
                    and (s.xmzl_publish_target=#{map.publishState}  or s.xmzl_publish_target is null)
                    and (s.ccb_publish_target=#{map.publishState}  or s.ccb_publish_target is null)
                    )
                </when>
                    <otherwise>
                        and (s.publish_target=#{map.publishState}
                        or s.zhifubao_publish_target=#{map.publishState}
                        or s.xianyu_publish_target=#{map.publishState}
                        or s.mogo_publish_target=#{map.publishState}
                        or s.xmzl_publish_target=#{map.publishState}
                        or s.ccb_publish_target=#{map.publishState})
                    </otherwise>
                </choose>

            </if>
            <if test="map.platform != null and map.platform != ''">
                and  <if test="map.publishState == 0">(</if>
                s.${map.platform}=#{map.publishState}
                <if test="map.publishState == 0"> or s.${map.platform} is null)
                </if>
             </if>
        </if>
        order by s.source_name
    </select>


    <select id="template"  resultType="cn.uone.bean.entity.business.res.vo.ResSourcePublishVo">
        select  s.*,rsc.price
        ,rsc.deposit,p.*
        from v_res_source s
        left join  t_res_source_check sc on sc.source_id=s.id
        left join  t_res_source_configure rsc on rsc.source_id=s.id
        left join  t_res_source_publish p  on p.source_id=s.id and  p.type = #{map.type}
        where 1=1 #project_datascope# and sc.state= '1' AND s.source_type!=2
        and (s.${map.platform} = '0' or s.${map.platform} is null)
        <if test="map.projectId != null and map.projectId != ''">
            AND s.project_id=#{map.projectId}
        </if>
        <if test="map.partitionId != null and map.partitionId != ''">
            AND s.partition_id=#{map.partitionId}
        </if>
        <if test="map.floor != null and map.floor != ''">
            AND s.floor=#{map.floor}
        </if>
        <if test="map.state != null and map.state != ''">
            AND s.state=#{map.state}
        </if>
        <if test="map.code != null and map.code != ''">
            AND s.code like "%"#{map.code}"%"
        </if>
        order by s.create_date desc
    </select>

    <select id="getBySourceId"  resultType="cn.uone.bean.entity.business.res.ResSourcePublishEntity">
        select *,DATE_FORMAT(start_date,"%Y-%m-%d")as time  from t_res_source_publish where source_id=#{sourceId} and type = #{type} limit 1
    </select>

    <update id="batchUpdate" >
        update t_res_source_publish
        <trim prefix="set" suffixOverrides=",">
            <if test="map.title != null and map.title != ''">title = #{map.title},</if>
            <if test="map.rentType != null and map.rentType != ''">rent_type = #{map.rentType},</if>
            <if test="map.rentMode != null and map.rentMode != ''">rent_mode = #{map.rentMode},</if>
            <if test="map.isShortRent != null and map.isShortRent != ''">is_short_rent = #{map.isShortRent},</if>
            <if test="map.totalFloor != null and map.totalFloor != ''">total_floor = #{map.totalFloor},</if>
            <if test="map.startDate != null">start_date = #{map.startDate},</if>
            <if test="map.manageName != null and map.manageName != ''">manage_name = #{map.manageName},</if>
            <if test="map.manageTel != null and map.manageTel != ''">manage_tel = #{map.manageTel},</if>
            <if test="map.taobao != null and map.taobao != ''">taobao = #{map.taobao},</if>
            <if test="map.payMethodOne != null and map.payMethodOne != ''">pay_method_one = #{map.payMethodOne},</if>
            <if test="map.payMethodXm != null and map.payMethodXm != ''">pay_method_xm = #{map.payMethodXm},</if>
            <if test="map.buildingNature != null and map.buildingNature != ''">building_nature = #{map.buildingNature},</if>
        </trim>
        where source_id=#{map.sourceId}
    </update>

    <select id="thirdPartyInfo"  resultType="cn.uone.bean.entity.business.res.vo.HousesourseVo">
        SELECT s.*,sp.total_floor,sc.low_price,sc.price
		,sc.deposit,sc.pet_kept,sc.house_conf,sc.public_conf,
		sc.house_label,sc.summary,p.address,p.latitude,p.longitude
		,ht.room,ht.hall,ht.toilet,sp.rent_type,sp.rent_mode,sp.is_short_rent,
		sp.manage_name,sp.title,sp.manage_tel,DATE_FORMAT(sp.start_date,"%Y-%m-%d")start_date,sp.start_date JYSJ,
		sp.taobao,sp.pay_method_one,sp.pay_method_two,t.name theme_name,
		sp.third_party_community,sp.third_party_room,a1.area_name province,
		a2.area_name city ,a3.area_name district,sp.third_party_layout,
		sp.third_party_images,sp.pay_method_xm,ht.kitchen,sp.building_nature,t.summary theme_desc,i.area_code,i.street_code,
		s.ccb_publish_target,s.xmzl_publish_target,s.mogo_publish_target,s.xianyu_publish_target,s.zhifubao_publish_target
		FROM v_res_source s
		LEFT JOIN t_res_source_publish sp ON sp.source_id=s.id and sp.type=#{map.type}
		LEFT JOIN t_res_source_configure sc on sc.source_id=s.id
		LEFT JOIN t_res_project p on p.id=s.project_id
		LEFT JOIN t_res_project_info i on p.id=i.project_id
		LEFT JOIN t_res_house_type ht on ht.id=s.house_type_id
		LEFT JOIN t_res_theme t on t.id=s.theme_id
		LEFT JOIN t_sys_area a1 on a1.id = p.province_id AND a1.level='1'
		LEFT JOIN t_sys_area a2 on a2.id = p.city_id AND a2.level='2'
		LEFT JOIN t_sys_area a3 on a3.id = p.district_id AND a3.level='3'
		   where s.id=#{map.sourceId}
    </select>
    <select id="exsitXYCommunity" resultType="cn.uone.bean.entity.business.res.vo.HousesourseVo">
        select sp.third_party_community  FROM v_res_source s
        LEFT JOIN t_res_source_publish sp ON sp.source_id=s.id and sp.type=#{type}
        where s.project_id=#{projectId} and sp.third_party_community is not null
    </select>
    <select id="exsitXYLayout" resultType="cn.uone.bean.entity.business.res.vo.HousesourseVo">
          select sp.third_party_layout  FROM v_res_source s
          LEFT JOIN t_res_source_publish sp ON sp.source_id=s.id and sp.type=#{type}
            where s.house_type_id=#{houseTypeId} and sp.third_party_layout is not null
    </select>
</mapper>
