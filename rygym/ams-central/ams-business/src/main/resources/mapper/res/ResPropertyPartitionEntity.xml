<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.res.dao.ResPropertyPartitionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.res.ResPropertyPartitionEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="project_id" property="projectId"/>
        <result column="name" property="name"/>
        <result column="property_nature" property="propertyNature"/>
        <result column="property_type" property="propertyType"/>
        <result column="cert_code" property="certCode"/>
        <result column="property_owner" property="propertyOwner"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        project_id, name, property_nature, property_type, cert_code, property_owner
    </sql>

    <select id="partitionWithOwner" resultType="java.util.HashMap" parameterType="String">
            select p.*,o.name as owner_name from t_res_property_partition p
              left join  t_res_project_owner o on p.property_owner = o.id
              where 1=1
            AND p.project_id = #{id}

    </select>

</mapper>
