<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.xhcosmic.dao.CosmicReimbursementDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.xhcosmic.CosmicReimbursementEntity">
        <result column="id" property="id"/>
        <result column="create_date" property="createDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="bill_no" property="billNo"/>
        <result column="is_push" property="isPush"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="source_bill_type" property="sourceBillType"/>
        <result column="source_bill_number" property="sourceBillNumber"/>
        <result column="creator_number" property="creatorNumber"/>
        <result column="modifier_number" property="modifierNumber"/>
        <result column="dept_number" property="deptNumber"/>
        <result column="date_field" property="dateField"/>
        <result column="audit_date" property="auditDate"/>
        <result column="account_org_number" property="accountOrgNumber"/>
        <result column="biz_type_number" property="bizTypeNumber"/>
        <result column="attachment_num" property="attachmentNum"/>
        <result column="apply_date" property="applyDate"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="auditor_number" property="auditorNumber"/>
        <result column="description" property="description"/>
        <result column="create_time" property="createTime"/>
        <result column="book_date" property="bookDate"/>
        <result column="voucher_no" property="voucherNo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_date,
        update_by,
        bill_no,is_push, source_system,
        book_date,voucher_no,source_bill_type, source_bill_number, creator_number, modifier_number, dept_number, date_field, audit_date, account_org_number, biz_type_number, attachment_num, apply_date, modify_time, auditor_number, description, create_time
    </sql>

</mapper>
