<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.xhcosmic.dao.CosmicPayApplyDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.xhcosmic.CosmicPayApplyEntity">
        <result column="id" property="id"/>
        <result column="create_date" property="createDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="bill_no" property="billNo"/>
        <result column="deal_bill_no" property="dealBillNo"/>
        <result column="voucher_num" property="voucherNum"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="source_bill_type" property="sourceBillType"/>
        <result column="source_bill_number" property="sourceBillNumber"/>
        <result column="apply_date" property="applyDate"/>
        <result column="pay_org_number" property="payOrgNumber"/>
        <result column="apply_org_number" property="applyOrgNumber"/>
        <result column="creator_number" property="creatorNumber"/>
        <result column="payment_type_number" property="paymentTypeNumber"/>
        <result column="payment_identify_number" property="paymentIdentifyNumber"/>
        <result column="paid_status" property="paidStatus"/>
        <result column="payee_amount" property="payeeAmount"/>
        <result column="is_dev_fee" property="isDevFee"/>
        <result column="new_apply_cause" property="newApplyCause"/>
        <result column="url" property="url"/>
        <result column="text_field" property="textField"/>
        <result column="combo_field" property="comboField"/>
        <result column="is_strike_balance" property="isStrikeBalance"/>
        <result column="payee_bank_num" property="payeeBankNum"/>
        <result column="payee_name" property="payeeName"/>
        <result column="payee_bank_name" property="payeeBankName"/>
        <result column="act_pay_amt" property="actPayAmt"/>
        <result column="local_amt" property="localAmt"/>
        <result column="exchange" property="exchange"/>
        <result column="currency_number" property="currencyNumber"/>
        <result column="currency_name" property="currencyName"/>
        <result column="settle_type_number" property="settleTypeNumber"/>
        <result column="settle_type_name" property="settleTypeName"/>
        <result column="description" property="description"/>
        <result column="auditor_number" property="auditorNumber"/>
        <result column="auditor_name" property="auditorName"/>
        <result column="creator_name" property="creatorName"/>
        <result column="modifier_number" property="modifierNumber"/>
        <result column="modifier_name" property="modifierName"/>
        <result column="payer_acct_bank_number" property="payerAcctBankNumber"/>
        <result column="payer_acct_bank_name" property="payerAcctBankName"/>
        <result column="payer_bank_number" property="payerBankNumber"/>
        <result column="payer_bank_name" property="payerBankName"/>
        <result column="complete_del" property="completeDel"/>
        <result column="back_type" property="backType"/>
        <result column="fail_reason" property="failReason"/>
        <result column="back_col_no" property="backColNo"/>
        <result column="modifity_time" property="modifityTime"/>
        <result column="if_update" property="ifUpdate"/>
        <result column="is_push" property="isPush"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_date,
        update_by,
        bill_no, deal_bill_no, voucher_num, source_system, source_bill_type, source_bill_number, apply_date, pay_org_number, apply_org_number, creator_number, payment_type_number, payment_identify_number, paid_status, payee_amount, is_dev_fee, new_apply_cause, url, text_field, combo_field, is_strike_balance, payee_bank_num, payee_name, payee_bank_name, act_pay_amt, local_amt, exchange, currency_number, currency_name, settle_type_number, settle_type_name, description, auditor_number, auditor_name, creator_name, modifier_number, modifier_name, payer_acct_bank_number, payer_acct_bank_name, payer_bank_number, payer_bank_name, complete_del, back_type, fail_reason, back_col_no, modifity_time, if_update, is_push
    </sql>

</mapper>
