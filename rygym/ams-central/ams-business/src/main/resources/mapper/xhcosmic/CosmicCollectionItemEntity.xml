<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.xhcosmic.dao.CosmicCollectionItemDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.xhcosmic.CosmicCollectionItemEntity">
        <result column="id" property="id"/>
        <result column="create_date" property="createDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="collection_id" property="collectionId"/>
        <result column="contract_number" property="contractNumber"/>
        <result column="contract_name" property="contractName"/>
        <result column="project_number" property="projectNumber"/>
        <result column="project_name" property="projectName"/>
        <result column="channel_number" property="channelNumber"/>
        <result column="channel_name" property="channelName"/>
        <result column="biz_type_number" property="bizTypeNumber"/>
        <result column="biz_type_name" property="bizTypeName"/>
        <result column="source_method_number" property="sourceMethodNumber"/>
        <result column="source_method_name" property="sourceMethodName"/>
        <result column="building_and_unit_number" property="buildingAndUnitNumber"/>
        <result column="building_and_unit_name" property="buildingAndUnitName"/>
        <result column="financial_org_number" property="financialOrgNumber"/>
        <result column="financial_org_name" property="financialOrgName"/>
        <result column="settle_org_number" property="settleOrgNumber"/>
        <result column="settle_org_name" property="settleOrgName"/>
        <result column="expense_item_number" property="expenseItemNumber"/>
        <result column="expense_item_name" property="expenseItemName"/>
        <result column="receivable_amt" property="receivableAmt"/>
        <result column="cost_center_number" property="costCenterNumber"/>
        <result column="cost_center_name" property="costCenterName"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_date,
        update_by,
        collection_id, contract_number, contract_name, project_number, project_name, channel_number, channel_name, biz_type_number, biz_type_name, source_method_number, source_method_name, building_and_unit_number, building_and_unit_name, financial_org_number, financial_org_name, settle_org_number, settle_org_name, expense_item_number, expense_item_name, receivable_amt, cost_center_number, cost_center_name, remark
    </sql>

</mapper>
