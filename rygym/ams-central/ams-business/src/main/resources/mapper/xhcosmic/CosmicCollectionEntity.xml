<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.xhcosmic.dao.CosmicCollectionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.xhcosmic.CosmicCollectionEntity">
        <result column="id" property="id"/>
        <result column="create_date" property="createDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="receivable_number" property="receivableNumber"/>
        <result column="is_push" property="isPush"/>
        <result column="is_dev_free" property="isDevFree"/>
        <result column="special_type" property="specialType"/>
        <result column="special_bill_number" property="specialBillNumber"/>
        <result column="biz_date" property="bizDate"/>
        <result column="receiving_type_number" property="receivingTypeNumber"/>
        <result column="payer_type" property="payerType"/>
        <result column="txt_description" property="txtDescription"/>
        <result column="org_number" property="orgNumber"/>
        <result column="open_org_number" property="openOrgNumber"/>
        <result column="account_bank_number" property="accountBankNumber"/>
        <result column="account_cash_number" property="accountCashNumber"/>
        <result column="payer_name" property="payerName"/>
        <result column="payer_acct_bank_num" property="payerAcctBankNum"/>
        <result column="payer_bank_name" property="payerBankName"/>
        <result column="settle_type_number" property="settleTypeNumber"/>
        <result column="url" property="url"/>
        <result column="act_rec_amt" property="actRecAmt"/>
        <result column="voucher_number" property="voucherNumber"/>
        <result column="start_date" property="startDate"/>
        <result column="finish_date" property="finishDate"/>
        <result column="bill_no" property="billNo"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="source_bill_type" property="sourceBillType"/>
        <result column="source_bill_number" property="sourceBillNumber"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_date,
        update_by,
        receivable_number, is_push, is_dev_free, special_type, special_bill_number, biz_date, receiving_type_number, payer_type, txt_description, org_number, open_org_number, account_bank_number, account_cash_number, payer_name, payer_acct_bank_num, payer_bank_name, settle_type_number, url, act_rec_amt, voucher_number, start_date, finish_date, bill_no, source_system, source_bill_type, source_bill_number
    </sql>
    
    <!-- 根据交易明细ID查询关联的应收单ID列表 -->
    <select id="findIncomeIdsByTransactionId" resultType="java.lang.String">
        SELECT income_id
        FROM t_cosmic_income_transaction_rel
        WHERE transaction_id = #{transactionId}
    </select>

</mapper>
