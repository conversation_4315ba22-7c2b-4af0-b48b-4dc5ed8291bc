<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.xhcosmic.dao.CosmicPayApplyInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.xhcosmic.CosmicPayApplyInfoEntity">
        <result column="id" property="id"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="pay_apply_id" property="payApplyId"/>
        <result column="settlement_type_number" property="settlementTypeNumber"/>
        <result column="payee_amount" property="payeeAmount"/>
        <result column="priority" property="priority"/>
        <result column="payee_type" property="payeeType"/>
        <result column="payee_name" property="payeeName"/>
        <result column="payee_acc_bank_num" property="payeeAccBankNum"/>
        <result column="account_name" property="accountName"/>
        <result column="remark" property="remark"/>
        <result column="payee_bank_number" property="payeeBankNumber"/>
        <result column="special_scene" property="specialScene"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        update_date,
        create_by,
        update_by,
        pay_apply_id, settlement_type_number, payee_amount, priority, payee_type, payee_name, payee_acc_bank_num, account_name, remark, payee_bank_number, special_scene
    </sql>

</mapper>
