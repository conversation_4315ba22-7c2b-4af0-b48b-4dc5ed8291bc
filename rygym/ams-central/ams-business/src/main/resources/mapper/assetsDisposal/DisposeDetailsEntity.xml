<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.assetsDisposal.dao.DisposeDetailsDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.assetsDisposal.DisposeDetailsEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="create_date" property="createDate" />
        <result column="disposal_id" property="disposalId" />
        <result column="source_type" property="sourceType" />
        <result column="source_unit" property="sourceUnit" />
        <result column="accept_time" property="acceptTime" />
        <result column="accept_inventory" property="acceptInventory" />
        <result column="leftover_problem" property="leftoverProblem" />
        <result column="evaluated_price" property="evaluatedPrice" />
        <result column="floor_price" property="floorPrice" />
        <result column="disposal_way" property="disposalWay" />
        <result column="listed_institution" property="listedInstitution" />
        <result column="acquiring_party" property="acquiringParty" />
        <result column="turnover" property="turnover" />
        <result column="selling_time" property="sellingTime" />
        <result column="disposal_earnings" property="disposalEarnings" />
        <result column="pledge_object" property="pledgeObject" />
        <result column="mortgage_amount" property="mortgageAmount" />
        <result column="financing_institution" property="financingInstitution" />
        <result column="mortgage_term" property="mortgageTerm" />
        <result column="creditor" property="creditor" />
        <result column="change_cause" property="changeCause" />
        <result column="change_content" property="changeContent" />
        <result column="change_time" property="changeTime" />
        <result column="released_mortgage_object" property="releasedMortgageObject" />
        <result column="released_mortgage_time" property="releasedMortgageTime" />
        <result column="released_mortgage_result" property="releasedMortgageResult" />
        <result column="type" property="type" />
        <result column="lease_start" property="leaseStart" />
        <result column="lease_end" property="leaseEnd" />
        <result column="leaseholder" property="leaseholder" />
        <result column="lease_price" property="leasePrice" />
        <result column="remark" property="remark" />
        <result column="property_info" property="propertyInfo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        update_date,
        disposal_id, source_type, property_info,source_unit, accept_time, accept_inventory, leftover_problem, evaluated_price, floor_price, disposal_way, listed_institution, acquiring_party, turnover, selling_time, disposal_earnings, pledge_object, mortgage_amount, financing_institution, mortgage_term, creditor, change_cause, change_content, change_time, released_mortgage_object, released_mortgage_time, released_mortgage_result, type, lease_start, lease_end, leaseholder, lease_price, remark, create_date
    </sql>

    <delete id="delByDisposalId" >
    delete from t_dispose_details
    where  disposal_id = #{disposalId}
    </delete>

    <select id="getByType" parameterType="java.lang.String" resultMap="BaseResultMap">
    select s.* ,d.property_info
    from t_dispose_details s
    left join t_disposal d on  d.id = s.disposal_id
    where 1=1
    and s.type = #{type}
    </select>
</mapper>
