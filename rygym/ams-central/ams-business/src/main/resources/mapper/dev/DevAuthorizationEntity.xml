<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.dev.dao.DevAuthorizationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.dev.DevAuthorizationEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="property_device_id" property="propertyDeviceId" />
        <result column="renter_id" property="renterId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        property_device_id, renter_id
    </sql>


    <resultMap id="queryDevAuthorizationLinkUserMap" type="cn.uone.bean.entity.business.dev.vo.DevAuthorizationEntityVo" extends="BaseResultMap">
        <result column="real_name" property="realName"/>
        <result column="userName" property="userName"/>
        <result column="tel" property="tel"/>
    </resultMap>
    <resultMap id="queryDevAuthorizationDoorMap" type="cn.uone.bean.entity.business.dev.vo.DevAuthorizationEntityVo" extends="BaseResultMap">
        <result column="summary" property="summary"/>
    </resultMap>
    <resultMap id="queryDoorMap" type="cn.uone.bean.entity.business.dev.vo.MyDoorVo">
        <result column="projectId" property="projectId"/>
        <result column="projectName" property="projectName"/>
        <result column="partitionId" property="partitionId"/>
        <result column="partitionName" property="partitionName"/>
        <result column="code" property="code"/>
    </resultMap>

    <select id="queryDevAuthorizationLinkUser" resultMap="queryDevAuthorizationLinkUserMap">
        SELECT
            t.*, a.real_name,
            b.name userName,
            b.tel
        FROM
        t_dev_authorization t
        LEFT JOIN
        v_sys_user a on t.create_by = a.id
        LEFT JOIN
        v_sys_renter b on t.renter_id = b.id
        where 1=1 AND B.tel is not null
        <if test="map.keyvalue !=null and map.keyvalue!=''">
            and (b.name like CONCAT('%',#{map.keyvalue},'%') or b.tel like CONCAT('%',#{map.keyvalue},'%'))
        </if>
        <if test="map.propertyDeviceId !=null and map.propertyDeviceId!=''">
            and t.property_device_id =#{map.propertyDeviceId}
        </if>
    </select>

    <select id="queryDevAuthorizationDoor" resultMap="queryDevAuthorizationDoorMap" parameterType="java.util.Map">
        SELECT
            t.*, a.summary
        FROM
            t_dev_authorization t,
            t_dev_property_device a
        WHERE
            t.property_device_id = a.id
        <if test="map.renterId !=null and map.renterId != ''">
            AND t.renter_id=#{map.renterId}
        </if>
        <if test="map.projectId !=null and map.projectId != ''">
            AND a.project_id=#{map.projectId}
        </if>
    </select>

    <select id="queryDoor" resultMap="queryDevAuthorizationDoorMap" parameterType="java.util.Map">
        select da.*,dpd.summary from t_dev_authorization da
        LEFT JOIN t_dev_property_device dpd ON dpd.id=da.property_device_id WHERE 1=1
        <if test="map.renterId !=null and map.rentenId !=''">
            AND da.renter_id=#{map.renterId}
        </if>
    </select>
</mapper>
