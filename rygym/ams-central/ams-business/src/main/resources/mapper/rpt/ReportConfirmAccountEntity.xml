<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.rpt.dao.ReportConfirmAccountDao">


    <select id="findConfirmByCondition" resultType="cn.uone.bean.entity.business.report.vo.ConfirmAccountVo">


    select * from (
        SELECT
        o.id,
        o. CODE,
        s.source_name AS sourceName,
        r. NAME,
        i.id AS orderItemId,
        i.start_time AS startTime,
        i.end_time AS endTime ,
        i.order_item_type AS orderItemType,
        t.transfer_type AS transferType,
        sum(o.actual_payment) AS payment,
        t.transfer_time AS transferTime,
        i.arrive_time AS arriveTime,
        i.arrive_code AS arriveCode,
        'order' as payType,
        '1' as accountType
        FROM
        t_bil_order o
        LEFT JOIN t_cont_contract c ON c.id = o.contract_id
        LEFT JOIN v_sys_renter r ON r.id = c.signer_id
        LEFT JOIN t_bil_order_item i ON o.id = i.order_id
        LEFT JOIN v_res_source s ON o.source_id = s.id
        LEFT JOIN t_res_plan_partition pp ON pp.id = s.partition_id
        LEFT JOIN t_bil_transfer t ON o.id = t.order_id
        LEFT JOIN t_base_account a ON a.id = t.account_id
        LEFT JOIN t_base_bank_branch bb ON a.branch_id = bb.id
        WHERE 1 = 1
        AND if(o.order_type = '20',o.actual_payment,i.payment) > 0
        AND i.transfer_state = 1
        AND t.transfer_type IN ('1', '2', '9')
        <include refid="searchCondition"/>
        GROUP BY o. CODE
union
        select 	o.id,
        o.CODE,
        s.source_name AS sourceName,
        r.NAME,
        null AS orderItemId,
        null AS startTime,
        null AS endTime,
        o.order_type AS orderItemType,
        o.transfer_type AS transferType,
        o.payment  AS payment,
        o.transfer_time AS transferTime,
        o.arrive_time AS arriveTime,
        o.arrive_code AS arriveCode,
        'invest' as payType,
        '2' as accountType
        from t_bil_invest o
        LEFT JOIN t_cont_contract c ON c.id = o.contract_id
        LEFT JOIN v_sys_renter r ON r.id = c.signer_id
        LEFT JOIN v_res_source s ON o.source_id = s.id
        LEFT JOIN t_res_plan_partition pp ON pp.id = s.partition_id
        LEFT JOIN t_base_account a ON a.id = o.account_id
        LEFT JOIN t_base_bank_branch bb ON a.branch_id = bb.id
        where
        o.payment > 0
        AND o.transfer_state = 1
            <!--<if test="searchPo.accountType != null and searchPo.accountType != ''">
                AND a.account_type = #{searchPo.accountType}
            </if>-->
            <if test="searchPo.transferType != null and searchPo.transferType != ''">
                AND o.transfer_type = #{searchPo.transferType}
            </if>
            <if test="searchPo.projectId != null and searchPo.projectId != ''">
                AND s.project_id = #{searchPo.projectId}
            </if>
            <if test="searchPo.partitionId != null and searchPo.partitionId != ''">
                AND s.partition_id = #{searchPo.partitionId}
            </if>
            <if test="searchPo.keyWord != null and searchPo.keyWord != ''">
                and (
                s.source_name like CONCAT('%', #{searchPo.keyWord}, '%')
                or r.name like CONCAT('%', #{searchPo.keyWord}, '%')
                or a.code like CONCAT('%', #{searchPo.keyWord}, '%')
                or bb.name like CONCAT('%', #{searchPo.keyWord}, '%')
                or o.arrive_code like CONCAT('%', #{searchPo.keyWord}, '%')
                )
            </if>
            <if test="searchPo.contractCode != null and searchPo.contractCode != ''">
                AND c.contract_code like CONCAT('%', #{searchPo.contractCode}, '%')
            </if>
            <if test="searchPo.orderCode != null and searchPo.orderCode != ''">
                AND o.code like CONCAT('%', #{searchPo.orderCode}, '%')
            </if>
            <if test="searchPo.startTransferTime != null">
                AND <![CDATA[date(o.transfer_time) >= date(#{searchPo.startTransferTime}) ]]>
            </if>
            <if test="searchPo.endTransferTime != null">
                AND <![CDATA[date(o.transfer_time) <= date(#{searchPo.endTransferTime}) ]]>
            </if>
            <if test="searchPo.orderConfirmState != null and !searchPo.orderConfirmState.isEmpty()">
                <if test="searchPo.orderConfirmState=='0'.toString()">
                    and o.arrive_code is null
                </if>
                <if test="searchPo.orderConfirmState=='1'.toString()">
                    and o.arrive_code is not null
                </if>
            </if>
            <if test="searchPo.transferConfirmState != null and !searchPo.transferConfirmState.isEmpty()">
                <if test="searchPo.transferConfirmState=='0'.toString()">
                    and o.arrive_code is null
                </if>
                <if test="searchPo.transferConfirmState=='1'.toString()">
                    and o.arrive_code is not null
                </if>
            </if>
        ) t1
        where 1=1
        <if test="searchPo.payType!='' and searchPo.payType!=null">
            and t1.payType=#{searchPo.payType}
        </if>
        <if test="searchPo.accountType!='' and searchPo.accountType!=null">
            and t1.accountType=#{searchPo.accountType}
        </if>

        order by t1.transferTime desc
    </select>

    <select id="getArriveTotal" resultType="java.math.BigDecimal">

        select sum(payment) payment from (
            select sum(o.actual_payment) as payment,
            'order' as payType ,
            '1' as accountType
            from t_bil_order o
            left join t_cont_contract c on c.id = o.contract_id
            left join v_sys_renter r on r.id = c.signer_id
            left join t_bil_order_item i on o.id = i.order_id
            left join v_res_source s on o.source_id = s.id
            left join t_res_plan_partition pp on pp.id = s.partition_id
            left join t_bil_transfer t on o.id = t.order_id
            left join t_base_account a on a.id = t.account_id
            left join t_base_bank_branch bb on a.branch_id = bb.id
            WHERE 1 = 1
            AND if(o.order_type = '20',o.actual_payment,i.payment) > 0
            AND i.transfer_state = 1
            AND t.transfer_type IN ('1', '2', '9')
            <if test="searchPo.totalOrderConfirmState != null and !searchPo.totalOrderConfirmState.isEmpty()">
                <if test="searchPo.totalOrderConfirmState=='0'.toString()">
                    and i.arrive_code is null
                </if>
                <if test="searchPo.totalOrderConfirmState=='1'.toString()">
                    and i.arrive_code is not null
                </if>
            </if>
            <if test="searchPo.orderConfirmState != null and !searchPo.orderConfirmState.isEmpty()">
                <if test="searchPo.orderConfirmState=='0'.toString()">
                    and i.arrive_code is null
                </if>
                <if test="searchPo.orderConfirmState=='1'.toString()">
                    and i.arrive_code is not null
                </if>
            </if>
            <include refid="searchCondition"/>

        union

            select 	sum(o.payment) payment,
            'invest' as payType,
            '2' as accountType
        from t_bil_invest o
            LEFT JOIN t_cont_contract c ON c.id = o.contract_id
            LEFT JOIN v_sys_renter r ON r.id = c.signer_id
            LEFT JOIN v_res_source s ON o.source_id = s.id
            LEFT JOIN t_res_plan_partition pp ON pp.id = s.partition_id
            LEFT JOIN t_base_account a ON a.id = o.account_id
            LEFT JOIN t_base_bank_branch bb ON a.branch_id = bb.id
            where
            o.payment > 0
            AND o.transfer_state = 1
            <if test="searchPo.transferType != null and searchPo.transferType != ''">
                AND o.transfer_type = #{searchPo.transferType}
            </if>
            <if test="searchPo.projectId != null and searchPo.projectId != ''">
                AND s.project_id = #{searchPo.projectId}
            </if>
            <if test="searchPo.partitionId != null and searchPo.partitionId != ''">
                AND s.partition_id = #{searchPo.partitionId}
            </if>
            <if test="searchPo.keyWord != null and searchPo.keyWord != ''">
                and (
                s.source_name like CONCAT('%', #{searchPo.keyWord}, '%')
                or r.name like CONCAT('%', #{searchPo.keyWord}, '%')
                or a.code like CONCAT('%', #{searchPo.keyWord}, '%')
                or bb.name like CONCAT('%', #{searchPo.keyWord}, '%')
                or o.arrive_code like CONCAT('%', #{searchPo.keyWord}, '%')
                )
            </if>
            <if test="searchPo.contractCode != null and searchPo.contractCode != ''">
                AND c.contract_code like CONCAT('%', #{searchPo.contractCode}, '%')
            </if>
            <if test="searchPo.orderCode != null and searchPo.orderCode != ''">
                AND o.code like CONCAT('%', #{searchPo.orderCode}, '%')
            </if>
            <if test="searchPo.startTransferTime != null">
                AND <![CDATA[date(o.transfer_time) >= date(#{searchPo.startTransferTime}) ]]>
            </if>
            <if test="searchPo.endTransferTime != null">
                AND <![CDATA[date(o.transfer_time) <= date(#{searchPo.endTransferTime}) ]]>
            </if>
            <if test="searchPo.orderConfirmState != null and !searchPo.orderConfirmState.isEmpty()">
                <if test="searchPo.orderConfirmState=='0'.toString()">
                    and o.arrive_code is null
                </if>
                <if test="searchPo.orderConfirmState=='1'.toString()">
                    and o.arrive_code is not null
                </if>
            </if>
            <if test="searchPo.totalOrderConfirmState != null and !searchPo.totalOrderConfirmState.isEmpty()">
                <if test="searchPo.totalOrderConfirmState=='0'.toString()">
                    and o.arrive_code is null
                </if>
                <if test="searchPo.totalOrderConfirmState=='1'.toString()">
                    and o.arrive_code is not null
                </if>
            </if>
            <if test="searchPo.transferConfirmState != null and !searchPo.transferConfirmState.isEmpty()">
                <if test="searchPo.transferConfirmState=='0'.toString()">
                    and o.arrive_code is null
                </if>
                <if test="searchPo.transferConfirmState=='1'.toString()">
                    and o.arrive_code is not null
                </if>
            </if>

        ) t1
        where 1=1
        <if test="searchPo.payType!='' and searchPo.payType!=null">
            and t1.payType=#{searchPo.payType}
        </if>
        <if test="searchPo.accountType!='' and searchPo.accountType!=null">
            and t1.accountType=#{searchPo.accountType}
        </if>

    </select>

    <!--银联划付-->
    <select id="findUnionPayCondition" resultType="cn.uone.bean.entity.business.report.vo.ConfirmAccountVo">
        select
        t.transfer_time as transferTime,t.id as transferId,t.arrive_code as arriveCode,
        t.payment as transferPayment,s.account_type as accountType,t.service_fee serviceFee ,
        s.code as accountCode, bb.name AS branchName,merchant_id as merchantId ,t.trading_date
        from t_bil_transfer t
        left join t_base_account s on s.id = t.account_id
        left join t_base_bank_branch bb on s.branch_id = bb.id
        where t.transfer_type = 3

        <if test="searchPo.merchantIds != null and searchPo.merchantIds.size() > 0">
            AND s.merchant_id in
            <foreach collection="searchPo.merchantIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="transferSearchCondition"/>
        order by t.transfer_time desc
    </select>

    <select id="getUnionPayTotal" resultType="java.math.BigDecimal">
        select
        sum(t.payment) as unionPayTotal
        from t_bil_transfer t
        left join t_base_account s on s.id = t.account_id
        left join t_base_bank_branch bb on s.branch_id = bb.id
        where t.transfer_type = 3
        <if test="searchPo.merchantIds != null and searchPo.merchantIds.size() > 0">
            AND s.merchant_id in
            <foreach collection="searchPo.merchantIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="transferSearchCondition"/>
    </select>

    <!--建行转账 确认支付-->
    <select id="findCcbCondition" resultType="cn.uone.bean.entity.business.report.vo.ConfirmAccountVo">
        select
        o.id,t.transfer_time as transferTime,t.id as transferId,t.arrive_code as arriveCode,
        t.payment as transferPayment,a.account_type as accountType,
        a.code as accountCode, bb.name AS branchName,merchant_id as merchantId
        from t_bil_transfer t
        left join t_base_account a on a.id = t.account_id
        left join t_base_bank_branch bb on a.branch_id = bb.id
        left join t_bil_order o on o.id = t.order_id
        left join v_res_source s on o.source_id = s.id
        where a.account_type in ('20','21','160')
        and t.transfer_type in ('2','9') and t.result = 10
        <if test="searchPo.merchantIds != null and searchPo.merchantIds.size() > 0">
            AND a.merchant_id in
            <foreach collection="searchPo.merchantIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="transferSearchCondition"/>
        order by t.transfer_time desc
    </select>

    <select id="getCcbTotal" resultType="java.math.BigDecimal">
        select
        sum(t.payment) as ccbTotal
        from t_bil_transfer t
        left join t_base_account a on a.id = t.account_id
        left join t_base_bank_branch bb on a.branch_id = bb.id
        left join t_bil_order o on o.id = t.order_id
        left join v_res_source s on o.source_id = s.id
        where a.account_type in ('20','21','160')
        and t.transfer_type in ('2','9') and t.result = 10
        <if test="searchPo.merchantIds != null and searchPo.merchantIds.size() > 0">
            AND a.merchant_id in
            <foreach collection="searchPo.merchantIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="transferSearchCondition"/>
    </select>

    <!--获取相邻日期-->
    <select id="getLatelyPayTime" resultType="java.util.Date">
        SELECT date_format(t.payTime, '%Y-%m-%d')  FROM
        (
            SELECT
                date_format(o.pay_time, '%Y-%m-%d') payTime,
                TimeStampDiff(DAY,date_format(o.pay_time, '%Y-%m-%d'),#{transferTime}) diff,
                sum(i.payment) AS payment
            FROM
                t_bil_order o
            LEFT JOIN t_bil_order_item i ON o.id = i.order_id
            LEFT JOIN v_res_source s ON o.source_id = s.id
            LEFT JOIN t_bil_transfer t ON o.id = t.order_id
        WHERE 1 = 1
        and o.order_type in ('20','21','130','140') AND i.order_item_type in ('20','21','360')
        <if test="accountType != null and !accountType.isEmpty()">
            <choose>
                <when test="accountType == 21">
                    and o.order_type in ('21') AND i.order_item_type in ('21')
                </when>
                <when test="accountType == 20||accountType == 160">
                    and o.order_type in ('20','130','140') AND i.order_item_type in ('20','360')
                </when>
            </choose>
        </if>
            AND IF (o.order_type = '20',o.actual_payment,i.payment) > 0
            AND i.transfer_state = 1 AND t.transfer_type IN ('1', '2', '9')
            AND t.transfer_type = '1' AND s.project_id = #{projectId}
            AND i.arrive_code IS NULL
            GROUP BY payTime
            HAVING payment = #{transferPayment}  AND diff &gt; 0 AND diff &lt; 10
        ) t
        ORDER BY t.payTime DESC limit 1
    </select>

    <!--获取相邻日期-->
    <select id="getLatelyPayTimeCz" resultType="java.util.Date">
        SELECT date_format(t.payTime, '%Y-%m-%d')  FROM
        (
        SELECT
        date_format(o.pay_time, '%Y-%m-%d') payTime,
        TimeStampDiff(DAY,date_format(o.pay_time, '%Y-%m-%d'),#{transferTime}) diff,
        sum(o.payment) AS payment
        FROM
        t_bil_invest o
        LEFT JOIN v_res_source s ON o.source_id = s.id
        LEFT JOIN t_bil_transfer t ON o.id = t.order_id
        WHERE 1 = 1
        AND t.transfer_type IN ('1', '2', '9')
        AND t.transfer_type = '1'
        AND s.project_id = #{projectId}
        GROUP BY payTime
        HAVING payment = #{transferPayment}  AND diff &gt; 0 AND diff &lt; 10
        ) t
        ORDER BY t.payTime DESC limit 1
    </select>






    <sql id="searchCondition">
        <if test="searchPo.accountType != null and !searchPo.accountType.isEmpty()">
            <choose>
                <when test="searchPo.accountType == 21">
                    and o.order_type in ('21') AND i.order_item_type in ('21')
                </when>
                <when test="searchPo.accountType == 20||searchPo.accountType == 160">
                    and o.order_type in ('20','130','140') AND i.order_item_type in ('20','360')
                </when>
            </choose>
        </if>
        <if test="searchPo.orderId != null and searchPo.orderId != ''">
            AND o.id = #{searchPo.orderId}
        </if>
        <if test="searchPo.transferType != null and searchPo.transferType != ''">
            AND t.transfer_type = #{searchPo.transferType}
        </if>
        <if test="searchPo.projectId != null and searchPo.projectId != ''">
            AND s.project_id = #{searchPo.projectId}
        </if>
        <if test="searchPo.partitionId != null and searchPo.partitionId != ''">
            AND s.partition_id = #{searchPo.partitionId}
        </if>
        <if test="searchPo.propertyNature != null and searchPo.propertyNature != ''">
            and pp.property_nature = #{searchPo.propertyNature}
        </if>
        <if test="searchPo.keyWord != null and searchPo.keyWord != ''">
            and (
            s.source_name like CONCAT('%', #{searchPo.keyWord}, '%')
            or r.name like CONCAT('%', #{searchPo.keyWord}, '%')
            or a.code like CONCAT('%', #{searchPo.keyWord}, '%')
            or bb.name like CONCAT('%', #{searchPo.keyWord}, '%')
            or i.arrive_code like CONCAT('%', #{searchPo.keyWord}, '%')
            )
        </if>
        <if test="searchPo.contractCode != null and searchPo.contractCode != ''">
            AND c.contract_code like CONCAT('%', #{searchPo.contractCode}, '%')
        </if>
        <if test="searchPo.orderCode != null and searchPo.orderCode != ''">
            AND o.code like CONCAT('%', #{searchPo.orderCode}, '%')
        </if>
        <if test="searchPo.startTransferTime != null">
            AND <![CDATA[date(t.transfer_time) >= date(#{searchPo.startTransferTime}) ]]>
        </if>
        <if test="searchPo.endTransferTime != null">
            AND <![CDATA[date(t.transfer_time) <= date(#{searchPo.endTransferTime}) ]]>
        </if>
        <if test="searchPo.orderConfirmState != null and !searchPo.orderConfirmState.isEmpty()">
            <if test="searchPo.orderConfirmState=='0'.toString()">
                and i.arrive_code is null
            </if>
            <if test="searchPo.orderConfirmState=='1'.toString()">
                and i.arrive_code is not null
            </if>
        </if>
        <if test="searchPo.transferConfirmState != null and !searchPo.transferConfirmState.isEmpty()">
            <if test="searchPo.transferConfirmState=='0'.toString()">
                and t.arrive_code is null
            </if>
            <if test="searchPo.transferConfirmState=='1'.toString()">
                and t.arrive_code is not null
            </if>
        </if>
    </sql>

    <sql id="transferSearchCondition">
        <if test="searchPo.projectId != null and searchPo.projectId != ''">
            AND s.project_id = #{searchPo.projectId}
        </if>
        <if test="searchPo.startTransferTime != null">
            AND <![CDATA[date(t.transfer_time) >= date(#{searchPo.startTransferTime}) ]]>
        </if>
        <if test="searchPo.endTransferTime != null">
            AND <![CDATA[date(t.transfer_time) <= date(#{searchPo.endTransferTime}) ]]>
        </if>
        <if test="searchPo.keyWord != null and searchPo.keyWord != ''">
            and (
            a.code like CONCAT('%', #{searchPo.keyWord}, '%')
            or bb.name like CONCAT('%', #{searchPo.keyWord}, '%')
            )
        </if>
        <if test="searchPo.transferConfirmState != null and !searchPo.transferConfirmState.isEmpty()">
            /*因为mybatis会把'0'解析为字符，java是强类型语言，所以不能这样写*/
            <if test="searchPo.transferConfirmState=='0'.toString()">
                and t.arrive_code is null
            </if>
            <if test="searchPo.transferConfirmState=='1'.toString()">
                and t.arrive_code is not null
            </if>
        </if>
    </sql>

</mapper>
