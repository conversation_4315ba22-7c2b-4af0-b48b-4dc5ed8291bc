<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.rpt.dao.RptContAccountDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.rpt.RptContAccountEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="report_date" property="reportDate" />
        <result column="project_id" property="projectId" />
        <result column="partition_id" property="partitionId" />
        <result column="source_id" property="sourceId" />
        <result column="contract_id" property="contractId" />
        <result column="contract_type" property="contractType" />
        <result column="source_name" property="sourceName" />
        <result column="source_code" property="sourceCode" />
        <result column="is_organize" property="isOrganize" />
        <result column="is_subsidy" property="subsidy" />
        <result column="area" property="area" />
        <result column="signer_name" property="signerName" />
        <result column="cont_code" property="contCode" />
        <result column="cont_state" property="contState" />
        <result column="rent" property="rent" />
        <result column="cont_sub" property="contSub" />
        <result column="real_rent" property="realRent" />
        <result column="cont_start_date" property="contStartDate" />
        <result column="cont_end_date" property="contEndDate" />
        <result column="pay_type" property="payType" />
        <result column="owe_last_rent" property="oweLastRent" />
        <result column="owe_last_sub" property="oweLastSub" />
        <result column="owe_last_cus_rent" property="oweLastCusRent" />
        <result column="this_must_rent" property="thisMustRent" />
        <result column="this_must_sub" property="thisMustSub" />
        <result column="this_must_cus_rent" property="thisMustCusRent" />
        <result column="this_real_rent" property="thisRealRent" />
        <result column="this_real_sub" property="thisRealSub" />
        <result column="this_real_dis" property="thisRealDis" />
        <result column="this_real_cus_rent" property="thisRealCusRent" />
        <result column="this_real_start_time" property="thisRealStartTime" />
        <result column="this_real_end_time" property="thisRealEndTime" />
        <result column="this_real_end_date" property="thisRealEndDate"/>
        <result column="this_real_pay_time" property="thisRealPayTime" />
        <result column="this_real_ca_time" property="thisRealCaTime" />
        <result column="out_cus_rent" property="outCusRent" />
        <result column="out_start_time" property="outStartTime" />
        <result column="out_end_time" property="outEndTime" />
        <result column="out_time" property="outTime" />
        <result column="owe_this_rent" property="oweThisRent" />
        <result column="owe_this_sub" property="oweThisSub" />
        <result column="owe_this_cus_rent" property="oweThisCusRent" />
        <result column="this_real_pay_time_str" property="thisRealPayTimeStr"/>
        <result column="this_real_period" property="thisRealPeriod"/>
        <result column="this_real_ca_time_str" property="thisRealCaTimeStr"/>

    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        report_date, project_id, partition_id, source_id, contract_id, contract_type, source_name, source_code, is_organize, is_subsidy, area, signer_name, cont_code, cont_state, rent, cont_sub, real_rent, cont_start_date, cont_end_date, pay_type, owe_last_rent, owe_last_sub, owe_last_cus_rent, this_must_rent, this_must_sub, this_real_end_date,this_must_cus_rent, this_real_rent, this_real_sub, this_real_dis, this_real_cus_rent, this_real_start_time, this_real_end_time, this_real_pay_time, this_real_ca_time, out_cus_rent, out_start_time, out_end_time, out_time, owe_this_rent, owe_this_sub, owe_this_cus_rent,out_start_time checkOutPeriod,this_real_period,this_real_ca_time_str,this_real_pay_time_str
    </sql>


    <select id="page" resultType="cn.uone.bean.entity.business.rpt.RptContAccountEntity">
        SELECT
        rpa.id,
        rpa.report_date,
        rpa.project_id,
        rpa.partition_id,
        rpa.source_id,
        rpa.contract_id,
        rpa.contract_type,
        s.source_name,
        s.code source_code,
        rpa.is_organize,
        rpa.is_subsidy,
        rpa.area,
        rpa.signer_name,
        rpa.cont_code,
        rpa.cont_state,
        rpa.rent,
        rpa.cont_sub,
        rpa.real_rent,
        rpa.cont_start_date,
        rpa.cont_end_date,
        rpa.pay_type,
        rpa.owe_last_rent,
        rpa.owe_last_sub,
        rpa.owe_last_cus_rent,
        rpa.this_must_rent,
        rpa.this_must_sub,
        rpa.this_must_cus_rent,
        rpa.this_real_rent,
        rpa.this_real_sub,
        rpa.this_real_dis,
        rpa.this_real_cus_rent,
        rpa.this_real_start_time,
        rpa.this_real_end_time,
        rpa.this_real_end_date,
        rpa.this_real_pay_time,
        rpa.this_real_ca_time,
        rpa.out_cus_rent,
        rpa.out_start_time,
        rpa.out_end_time,
        rpa.out_time,
        rpa.owe_this_rent,
        rpa.owe_this_sub,
        rpa.owe_this_cus_rent,
        rpa.out_start_time checkOutPeriod,
        rpa.this_real_period,rpa.this_real_ca_time_str,rpa.this_real_pay_time_str
        FROM
        v_res_source s
        LEFT JOIN t_rpt_cont_account rpa ON rpa.source_id = s.id
        <if test="po.reportDate != null">
            AND rpa.report_date BETWEEN #{po.startDate} and #{po.endDate}
        </if>
        WHERE 1 = 1

        <if test="po.projectId != null and po.projectId != '' ">
            AND s.project_id = #{po.projectId}
        </if>
        <if test="po.partitionId != null and po.partitionId != '' ">
            AND s.partition_id = #{po.partitionId}
        </if>

        <if test="po.contCode != null and po.contCode != '' ">
            AND rpa.cont_code like CONCAT('%', #{po.contCode}, '%')
        </if>

        <if test="po.signer != null and po.signer != '' ">
            AND rpa.signer_name like CONCAT('%', #{po.signer}, '%')
        </if>

        <if test="po.sourceCode != null and po.sourceCode != '' ">
            AND s.code like CONCAT('%', #{po.sourceCode}, '%')
        </if>
        <if test="po.sourceType != null and po.sourceType != '' ">
            AND s.source_type = #{po.sourceType}
        </if>
        <if test="po.subsidy != null and po.subsidy != '' ">
            AND rpa.is_subsidy =#{po.subsidy}
        </if>

        <if test="po.keyWord != null and po.keyWord != '' ">
            AND ( rpa.cont_code like CONCAT('%', #{po.keyWord}, '%')
            or rpa.signer_name like CONCAT('%', #{po.keyWord}, '%')
            or s.source_name like CONCAT('%', #{po.keyWord}, '%')
            )
        </if>
        <if test="po.state == 1">
            and rpa.cont_state = '已生效'
        </if>
        <if test="po.state == 2">
            and rpa.cont_state = '新签'
        </if>
        <if test="po.state == 3">
            and rpa.cont_state = '退租中'
        </if>
        <if test="po.state == 4">
            and rpa.cont_state = '已退租'
        </if>
        ORDER BY IF(s.source_type &lt;&gt; '2', 0, 1 ) ASC,
        lpad(s.partitionName, 10, '0') asc,
        lpad(s.code, 10, '0') asc
    </select>

    <select id="selectCont" resultMap="cn.uone.business.cont.dao.ContContractDao.BaseCSResultMap">
        SELECT
        <include refid="cn.uone.business.cont.dao.ContContractDao.Base_Column_CS_SQL"/>
        FROM t_cont_contract c,t_cont_contract_source_rel cs,t_res_source s
        WHERE c.id = cs.contract_id and cs.source_id = s.id and c.state in ('6','7','8')
        and  (
        case when c.state = '7' then (EXISTS (select 1 from t_biz_release r where r.contract_id = c.id and date_format(r.checkout_date,'%Y-%m') = #{month}))
        when c.state in ('6','8') then (date_format(c.start_date,'%Y-%m')&lt;= #{month} and date_format(c.end_date,'%Y-%m')>= #{month})
        end )
        and EXISTS (select 1 from t_res_project p where s.project_id = p.id AND p.operate_state = '0')
    </select>

    <delete id="delByMonth" parameterType="java.lang.String">
        delete from t_rpt_cont_account where date_format(report_date,'%Y-%m') = #{month}
    </delete>

</mapper>
