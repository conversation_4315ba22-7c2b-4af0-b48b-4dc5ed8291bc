<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.rpt.dao.ReportDepositDao">

    <select id="selectPage" resultType="cn.uone.bean.entity.business.report.vo.DepositStatisticsVo">
        select c.id contract_id,cs.source_id,
                c.contract_code,
                c.contract_type,
                c.is_organize,
                ci.name,
                cs.source_name,
                c.state contract_state,
                c.start_date,
                c.end_date,
                c.pay_type,
                cs.cash_pledge,
                co.deposit,
                co.deduct,
                if(c.state = '7' and -cs.refund_deposit+co.deduct&lt;0,-cs.refund_deposit+co.deduct,0) outDeposit,
                co.deposit_pay_time,
                co.out_pay_time,
                co.audit_state,
                co.change_state
        from t_cont_contract c,t_cont_contract_info ci,v_cont_contract_source cs
        left join(
            SELECT o.contract_id,o.source_id,
                    min(if(order_type ='5'  and o.pay_state = '20',ifnull(o.pay_time,o.create_date),null)) deposit_pay_time,
                    min(if(order_type in ('130','140','210'),o.pay_time,null)) out_pay_time,
					sum(if(order_type ='5' and o.pay_state = '20',oi.yajin,0)) deposit,
					sum(if(order_type in ('130','140','210') and o.pay_state in ('20','70'),oi.d,0)) deduct,
					sum(if(order_type in ('130','140','210') and o.pay_state in ('20','70'),oi.yajin+oi.d,0)) outDeposit,
					max(if(order_type in ('130','140','210'),if(o.pay_state in ('20','70'),1,0),null)) audit_state,
                    max(if(order_type = '210',1,0)) change_state
            FROM t_bil_order o,
                  (select order_id,sum(if(order_item_type = '110',payment,0)) yajin,
                          if(sum(if(order_item_type != '110',payment,0))>0,sum(if(order_item_type != '110',payment,0)),0) d
                  from t_bil_order_item group by order_id) oi
            WHERE o.id = oi.order_id
                and o.order_type in ('5','130','140','210')
                and o.pay_state != '40'
            group by o.contract_id,o.source_id
        ) co on co.contract_id = cs.contract_id and co.source_id = cs.source_id
        where  cs.contract_id = c.id and ci.contract_id = c.id
              and c.state in ('6','7','8') #project_datascope# #zone_datascope#
        <if test="report.sourceType != null and report.sourceType != ''">
            and cs.source_type = #{report.sourceType}
        </if>
        <if test="report.projectId != null and report.projectId != ''">
            and cs.project_id = #{report.projectId}
        </if>
        <if test="report.partitionId != null and report.partitionId != ''">
            and cs.partition_id = #{report.partitionId}
        </if>
        <if test="report.contractState != null and report.contractState != ''">
            and c.state = #{report.contractState}
        </if>
        <if test="report.startTime != null and report.startTime != ''">
            and c.start_date &gt;= #{report.startTime}
        </if>
        <if test="report.endTime != null and report.endTime != ''">
            and c.end_date &lt;= concat(#{report.endTime},' 23:59:59.999')
        </if>
        <if test="report.auditState == 10">
            and co.audit_state = '1'
        </if>
        <if test="report.auditState == 20">
            and co.audit_state = '0'
        </if>
        <if test="report.auditState == 30">
            and co.audit_state is null
        </if>

        <if test="report.keyword != null and report.keyword != ''">
            and (c.contract_code like CONCAT('%', #{report.keyword}, '%') or ci.name like CONCAT('%', #{report.keyword}, '%') or cs.source_name like CONCAT('%', #{report.keyword}, '%'))
        </if>
        ORDER BY IF(cs.source_type &lt;&gt; '2', 0, 1 ) ASC,
                lpad(cs.partitionName, 10, '0') asc,
                lpad(cs.CODE, 10, '0') asc,
                c.create_date DESC

    </select>

</mapper>
