<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.rpt.dao.ReportConfirmRefundDao">


    <select id="report" resultType="cn.uone.bean.entity.business.report.vo.ConfirmRefundVo">
        SELECT o.pay_state,r1.source_id new_source_id,c.is_organize,s.source_type,new.is_organize new_is_organize,s.id source_id,c.contract_type,new.contract_type new_contract_type,CONCAT(s.projectName,'-',s.partitionName,'-',IF(s.source_type ='2',ifnull(car.num,s.code),s.code))code,
        c.id contract_id,c.contract_code,sr.`name`,o.`code` order_code,o.id order_id,o.order_type,
        IF(s.source_type='2',ocwzujin.actual_payment,ozujin.actual_payment)yszj,
        IF(s.source_type='2',DATE_FORMAT(ocwzujin.start_time, '%Y-%m-%d'),DATE_FORMAT(ozujin.start_time, '%Y-%m-%d'))yszj_start_time,
        IF(s.source_type='2',DATE_FORMAT(ocwzujin.end_time, '%Y-%m-%d'),DATE_FORMAT(ozujin.end_time, '%Y-%m-%d'))yszj_end_time,
        IF(o.order_type = '200',dj.payment,ifnull(oyj.payment,0)) ysyj,
        if(o.order_type='200',DATE_FORMAT(o.create_date, '%Y-%m-%d'),DATE_FORMAT(co1.checkout_date, '%Y-%m-%d')) out_or_change_time,IF(o.order_type='210',IF(oihf.payment &lt; 0,oihf.payment,0),IF(oi.payment &lt; 0,oi.payment,0))ytzj,
        IF(o.order_type='210',IF(oihf.payment &lt; 0,DATE_FORMAT(oihf.start_time, '%Y-%m-%d'),''),IF(oi.payment &lt; 0,DATE_FORMAT(oi.start_time, '%Y-%m-%d'),''))ytzj_start_time,
        IF(o.order_type='210',IF(oihf.payment &lt; 0,DATE_FORMAT(oihf.end_time, '%Y-%m-%d'),''),IF(oi.payment &lt; 0,DATE_FORMAT(oi.end_time, '%Y-%m-%d'),''))ytzj_end_time,
        IF(o.order_type = '210',ifnull(oyj.payment,0),IF(o.order_type = '200',djtk.PAYMENT,if(oyj1.payment is null,0,ifnull(oyj.payment,0))))ytyj,
        IF(o.order_type != '210',if(oyj1.payment is null,ifnull(oyj.payment,0),0),0)	fmyj,
        IF(o.order_type='210',IF(oihf.payment &gt;  0,oihf.payment,0),IF(oi.payment &gt; 0,oi.payment,0))cqzj,
        IF(o.order_type='210',IF(oihf.payment &gt;  0,DATE_FORMAT(oihf.start_time, '%Y-%m-%d'),''),IF(oi.payment &gt; 0,DATE_FORMAT(oi.start_time, '%Y-%m-%d'),''))cqzj_start_time,
        IF(o.order_type='210',IF(oihf.payment &gt;  0,DATE_FORMAT(oihf.end_time, '%Y-%m-%d'),''),IF(oi.payment &gt; 0,DATE_FORMAT(oi.end_time, '%Y-%m-%d'),''))cqzj_end_time,
        IF(oihf.payment &gt; 0 or oi.payment &gt; 0,DATE_FORMAT(ts.transfer_time,'%Y-%m-%d %H:%i:%s'),'')cqzj_bj_time,
        IF(o.order_type='210',IF(oihf.payment &gt; 0,IF(oihf.payment-ifnull(oyj.payment,0) &gt; 0,oihf.payment-ifnull(oyj.payment,0),0),0),IF (co1.confirm_reason != '1',IF(oi.payment &gt;  0,IF(oi.payment-ifnull(oyj.payment,0) &gt; 0,oi.payment-ifnull(oyj.payment,0),0),0),IF(oi.payment &gt;  0,oi.payment,0)))cqzj_sj,
        ohff.payment hff,owyf.payment wyf,owxj.payment wxj,otnsf.payment tnsf,otndf.payment tndf,
        otnmq.payment tnmq,owyj.payment wyj,owlf.payment wlf,osf.payment sf,odf.payment df,oqsf.payment qsf,owxf.payment wxf,
        omjf.payment mjf,oqtf.payment qtf,
        IFNULL(owyf.payment,0)+IFNULL(owxj.payment,0)+IFNULL(otnsf.payment,0)+IFNULL(otndf.payment,0)+IFNULL(otnmq.payment,0)+
        IFNULL(owyj.payment,0)+IFNULL(owlf.payment,0)+IFNULL(osf.payment,0)+IFNULL(odf.payment,0)+IFNULL(oqsf.payment,0)+
        IFNULL(owxf.payment,0)+IFNULL(omjf.payment,0)+IFNULL(oqtf.payment,0) hj,
        DATE_FORMAT(o.pay_time,'%Y-%m-%d %H:%i:%s')pay_time,new.contract_code new_contract_code,new.id new_contract_id,
        CASE WHEN	o.order_type='210' THEN '换房'
        WHEN	o.order_type='200' THEN ''
        WHEN	co1.confirm_reason = '0' THEN '合同到期'
        WHEN	co1.confirm_reason = '1' THEN '违约'
        WHEN	co1.confirm_reason = '2' THEN '协议退租'
        WHEN	co1.confirm_reason = '3' THEN '跑路'
        END memo,co1.confirm_reason
        FROM t_bil_order o
        LEFT JOIN v_res_source s on s.id=o.source_id
        left join t_res_plan_partition pp on pp.id=s.partition_id
        LEFT JOIN t_cont_contract c on c.id=o.contract_id
        LEFT JOIN t_cont_contract_source_rel csr ON csr.contract_id = c.id AND csr.source_id = o.source_id
        LEFT JOIN t_base_car car ON car.contract_source_id = csr.id
        left join t_bil_order dj on dj.contract_id=o.contract_id and  dj.order_type = '10'
        INNER JOIN v_sys_renter sr on sr.id=c.signer_id
        LEFT JOIN (SELECT oyi.payment,oyj.id,oyj.contract_id,oyj.source_id FROM t_bil_order oyj
        LEFT JOIN t_bil_order_item oyi on oyi.order_id=oyj.id AND oyi.order_item_type='110'
        WHERE oyj.order_type='5' AND oyj.pay_state = '20'
        )oyj  ON oyj.contract_id = o.contract_id AND oyj.source_id=o.source_id
        LEFT JOIN t_bil_order_item oyj1 ON oyj1.order_id = o.id AND oyj1.order_item_type = '110'
        LEFT JOIN (SELECT os.source_id,os.actual_payment,os.contract_id,MIN(start_time)start_time,MAX(end_time)end_time
        from(SELECT o.* from(SELECT * FROM t_bil_order WHERE ORDER_TYPE='20' AND  pay_state ='20' ORDER BY create_date DESC)o GROUP BY o.contract_id,o.source_id)os
        LEFT JOIN t_bil_order_item oizj ON os.id=oizj.order_id AND oizj.order_item_type='20'
        WHERE 1=1 GROUP BY os.contract_id,os.source_id
        )ozujin ON ozujin.contract_id=o.contract_id and ozujin.source_id=o.source_id
        LEFT JOIN (SELECT os.actual_payment,os.contract_id,MIN(start_time)start_time,MAX(end_time)end_time
        from(SELECT o.* from(SELECT * FROM t_bil_order WHERE ORDER_TYPE in ('20','160')  AND  pay_state ='20'  ORDER BY create_date DESC)o GROUP BY o.contract_id)os
        LEFT JOIN t_bil_order_item oizj ON os.id=oizj.order_id AND oizj.order_item_type in ('20','280')
        WHERE 1=1 GROUP BY os.contract_id
        )ocwzujin ON ocwzujin.contract_id=o.contract_id
        LEFT JOIN t_biz_release co1 on co1.contract_id=o.contract_id  AND co1.state != '9' and IF(o.order_type = '210',co1.type = '3',co1.type = '0')
        LEFT JOIN t_biz_change bz on  bz.contract_id=o.contract_id AND bz.state != '30'
        LEFT JOIN t_cont_contract new on  new.id=bz.new_contract_id
        LEFT JOIN t_cont_contract_source_rel r1 on r1.contract_id=new.id
        LEFT JOIN t_bil_order_item oi ON oi.order_id=o.id AND IF(s.source_type='2',oi.order_item_type in ('290','20'),oi.order_item_type in ('170','20'))  AND oi.PAYMENT != 0
        LEFT JOIN t_bil_order_item oihf ON oihf.order_id=o.id AND oihf.order_item_type='20' and oihf.PAYMENT != 0
        LEFT JOIN t_bil_order_item ohff ON ohff.order_id=o.id AND ohff.order_item_type='370' and ohff.PAYMENT != 0
        LEFT JOIN t_bil_order_item owyf ON owyf.order_id=o.id AND owyf.order_item_type in ('60','180') and owyf.PAYMENT != 0
        LEFT JOIN t_bil_order_item owxj ON owxj.order_id=o.id AND owxj.order_item_type='210' and owxj.PAYMENT != 0
        LEFT JOIN t_bil_order_item otnsf ON otnsf.order_id=o.id AND otnsf.order_item_type='120' and otnsf.PAYMENT != 0
        LEFT JOIN t_bil_order_item otndf ON otndf.order_id=o.id AND otndf.order_item_type='140' and otndf.PAYMENT != 0
        LEFT JOIN t_bil_order_item otnmq ON otnmq.order_id=o.id AND otnmq.order_item_type='160' and otnmq.PAYMENT != 0
        LEFT JOIN t_bil_order_item owyj ON owyj.order_id=o.id AND owyj.order_item_type='70' and owyj.PAYMENT != 0
        LEFT JOIN t_bil_order_item owlf ON owlf.order_id=o.id AND owlf.order_item_type='130' and owlf.PAYMENT != 0
        LEFT JOIN t_bil_order_item osf ON osf.order_id=o.id AND osf.order_item_type='30' and osf.PAYMENT != 0
        LEFT JOIN t_bil_order_item odf ON odf.order_id=o.id AND odf.order_item_type='50' and odf.PAYMENT != 0
        LEFT JOIN t_bil_order_item oqsf ON oqsf.order_id=o.id AND oqsf.order_item_type='330' and oqsf.PAYMENT != 0
        LEFT JOIN t_bil_order_item owxf ON owxf.order_id=o.id AND owxf.order_item_type='80' and owxf.PAYMENT != 0
        LEFT JOIN t_bil_order_item omjf ON omjf.order_id=o.id AND omjf.order_item_type='310' and omjf.PAYMENT != 0
        LEFT JOIN t_bil_order_item oqtf ON oqtf.order_id=o.id AND oqtf.order_item_type='100' and oqtf.PAYMENT != 0
        LEFT JOIN t_bil_order_item djtk ON djtk.order_id=o.id AND djtk.order_item_type='340'	and djtk.PAYMENT !=  0
        LEFT JOIN t_bil_transfer ts on ts.order_id=o.id and ts.result = '10'
        WHERE o.order_type in ('140','210','200')
        AND o.pay_state in ('60','70')
        <if test="po.projectId != null and po.projectId != '' ">
            AND s.project_id = #{po.projectId}
        </if>
        <if test="po.refundStartTime != null and po.refundStartTime != '' ">
            and date_format(o.pay_time,'%Y-%m-%d') &gt;= date_format(#{po.refundStartTime},'%Y-%m-%d')
        </if>
        <if test="po.refundEndTime != null and po.refundEndTime != '' ">
            and date_format(o.pay_time,'%Y-%m-%d') &lt;= date_format(#{po.refundEndTime},'%Y-%m-%d')
        </if>
        <if test="po.orderStartTime != null and po.orderStartTime != '' ">
            AND if(o.order_type='200', date_format(o.create_date,'%Y-%m-%d') &gt;= date_format(#{po.orderStartTime},'%Y-%m-%d'), date_format(co1.checkout_date,'%Y-%m-%d') &gt;= date_format(#{po.orderStartTime},'%Y-%m-%d'))
        </if>
        <if test="po.orderEndTime != null and po.orderEndTime != '' ">
            AND if(o.order_type='200',  date_format(o.create_date,'%Y-%m-%d') &lt;= date_format(#{po.orderEndTime},'%Y-%m-%d'), date_format(co1.checkout_date,'%Y-%m-%d') &lt;= date_format(#{po.orderEndTime},'%Y-%m-%d'))
        </if>
        <if test="po.propertyNature != null and po.propertyNature != '' ">
           and pp.property_nature=#{po.propertyNature}
        </if>
        <if test="po.partition != null and po.partition != '' ">
            and pp.id=#{po.partition}
        </if>
        <if test="po.orderType != null and po.orderType != '' ">
            and o.order_type=#{po.orderType}
        </if>
        <if test="po.payState != null and po.payState != '' ">
            and o.pay_state=#{po.payState}
        </if>
        order by s.partitionName,s.code asc
    </select>

</mapper>
