<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.flow.dao.SysExpressionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.flow.SysExpressionEntity">
    <result column="id" property="id" />
    <result column="create_date" property="createDate" />
    <result column="update_date" property="updateDate" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
        <result column="name" property="name" />
        <result column="expression" property="expression" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        update_date,
        create_by,
        update_by,
        name, expression, status, remark
    </sql>

</mapper>
