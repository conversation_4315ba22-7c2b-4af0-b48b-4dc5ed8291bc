<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.task.dao.TaskOptionContentDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.task.TaskOptionContentEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="option_type_id" property="optionTypeId" />
        <result column="content" property="content" />
        <result column="remark" property="remark" />
        <result column="type_name" property="typeName" />
        <result column="sequence" property="sequence" />
        <result column="option_value" property="optionValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        option_type_id,content, remark,type_name,sequence,option_value
    </sql>

    <select id="getOptionValue" resultMap="BaseResultMap">
        select c.* from t_task_option_content c, t_task_option_type t
        where  t.id = #{optionId}
        and  t.id = c.option_type_id
        order by sequence
    </select>
</mapper>
