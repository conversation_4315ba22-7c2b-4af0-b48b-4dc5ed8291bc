<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.task.dao.TaskItemDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.task.TaskItemEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="task_name" property="taskName" />
        <result column="task_description" property="taskDescription" />
        <result column="task_type" property="taskType" />
        <result column="remark" property="remark" />
        <result column="sublistId" property="sublistId" />
        <result column="result_type" property="resultType" />
        <result column="result_title" property="resultTitle" />
        <result column="option_id" property="optionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        task_name,task_description, task_type, remark,result_type,result_title,option_id
    </sql>


    <select id="selectPageByPlanId" resultMap="BaseResultMap">
        SELECT *
        FROM `t_task_item`
        WHERE
        1=1
        <if test="planId != null  and planId != ''">
            and  id not in (select item_id  FROM t_task_plan_sublist WHERE plan_id = #{planId})
        </if>
        <if test="taskType != null  and taskType != ''">
            and task_type = #{taskType}
        </if>

    </select>

    <select id="selectSublistByPlanId" resultMap="BaseResultMap">
        SELECT i.*,s.id as sublistId
        FROM t_task_item i
        left join t_task_plan_sublist s on s.item_id = i.id
        where
        1=1
        and s.plan_id = #{planId}
        and i.id in  (SELECT item_id FROM t_task_plan_sublist where plan_id = #{planId})
    </select>

    <select id="getByPlanId" resultMap="BaseResultMap">
        select i.* from  t_task_plan p,t_task_item i ,t_task_plan_sublist s
        where  p.id = #{planId}
        and  i.id = s.item_id
        and  p.id = s.plan_id
    </select>

</mapper>
