<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.task.dao.TaskDistributeDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.task.TaskDistributeEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="executor_id" property="executorId" />
        <result column="plan_id" property="planId" />
        <result column="name" property="name" />
        <result column="tel" property="tel" />
        <result column="role" property="role" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        executor_id,plan_id, remark,name,tel,role
    </sql>

    <delete id='delDistributeByPlanId'>
      DELETE FROM t_task_distribute WHERE plan_id =#{planId}
    </delete>

    <select id="getExecutorIds" resultType="java.lang.String"  resultMap="BaseResultMap">
        select executor_id
        from t_task_distribute
        where 1=1
        and plan_id = #{planId}
    </select>

    <select id="getExecutorByPlanId" resultMap="BaseResultMap">
        SELECT *
        FROM t_task_distribute
        where
        1=1
        and plan_id = #{planId}
    </select>
</mapper>
