<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.applyCard.dao.ApplyCardEfficientDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.applyCard.ApplyCardEfficientEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="student_id" property="studentId" />
        <result column="name" property="name" />
        <result column="applicant_id" property="applicantId" />
        <result column="gender" property="gender" />
        <result column="identity_card" property="identityCard" />
        <result column="type" property="type" />
        <result column="use_state" property="useState" />
        <result column="expiry_date" property="expiryDate" />
        <result column="main_or_assistant" property="mainOrAssistant" />
        <result column="dept" property="dept" />
        <result column="project_name" property="projectName" />
        <result column="project_id" property="projectId" />
        <result column="partition_name" property="partitionName" />
        <result column="partition_id" property="partitionId" />
        <result column="source_code" property="sourceCode" />
        <result column="source_id" property="sourceId" />
        <result column="pay_state" property="payState" />
        <result column="tel" property="tel" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        tel,
        student_id, name, applicant_id, gender, identity_card, type, use_state, expiry_date, main_or_assistant, dept, project_name, project_id, partition_name, partition_id, source_code, source_id, pay_state, remark
    </sql>


    <select id="getListByPage" resultMap="BaseResultMap">
        SELECT * FROM `t_apply_card_efficient` a
        where
        1=1
        <if test="entity.projectName !=null and entity.projectName !=''">
            and a.project_name = #{entity.projectName}
        </if>
        <if test="entity.partitionName !=null and entity.partitionName !=''">
            and a.partition_name = #{entity.partitionName}
        </if>
        <if test="entity.sourceCode !=null and entity.sourceCode !=''">
            and a.source_code = #{entity.sourceCode}
        </if>
        <if test="entity.name !=null and entity.name !=''">
            and a.name like "%"#{entity.name}"%"
        </if>
        ORDER BY a.project_name,a.partition_name,a.source_code
    </select>

    <delete id="removeAll" >
        DELETE FROM t_apply_card_efficient
    </delete>
</mapper>
