<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.face.dao.FaceAuditDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.face.FaceAuditEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="contract_id" property="contractId" />
        <result column="paper_code" property="paperCode" />
        <result column="project_id" property="projectId" />
        <result column="source_id" property="sourceId" />
        <result column="source_name" property="sourceName" />
        <result column="renter_id" property="renterId" />
        <result column="renter_name" property="renterName" />
        <result column="renter_tel" property="renterTel" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="state" property="state" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        contract_id, paper_code, project_id, source_id, source_name, renter_id, renter_name, renter_tel, start_date, end_date, state, remark
    </sql>

    <select id="selectFaceAuditList" resultType="java.util.Map">
        SELECT t.id checkInId,t.`renter_id` renterId,t.name,t.tel,IFNULL(fa.state,0) state,f.url,cs.contract_id contractId,
               cs.source_id sourceId,c.`paper_code` paperCode,
               DATE_FORMAT(c.`start_date`,'%Y-%m-%d') startDate,
               DATE_FORMAT(c.`end_date`,'%Y-%m-%d') endDate,
               s.`source_name` sourceName,s.project_id projectId,fa.`card_sn` cardSn,fa.id,fa.remark
        FROM t_cont_check_in_user t
                 LEFT JOIN t_cont_contract_source_rel cs ON cs.id=t.`contract_source_id`
                 LEFT JOIN t_cont_contract c ON c.id=cs.`contract_id`
                 LEFT JOIN v_res_source s ON s.id=cs.source_id
                 LEFT JOIN t_face_audit fa ON fa.`contract_id`=cs.`contract_id` AND fa.`renter_id`=t.`renter_id`
                 LEFT JOIN t_sys_file f ON f.`from_id`=fa.id AND f.type='face'
        WHERE t.type='10' AND c.`state` IN ('5','6')
        <if test="map.signerId != null and map.signerId != ''">
            AND c.`signer_id`= #{map.signerId}
        </if>
        <if test="map.renterId != null and map.renterId != ''">
            AND t.renter_id = #{map.renterId}
        </if>
        <if test="map.checkInId != null and map.checkInId != ''">
            AND t.id = #{map.checkInId}
        </if>
        order by c.paper_code
    </select>

</mapper>
