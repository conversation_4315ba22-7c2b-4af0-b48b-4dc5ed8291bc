<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.apro.dao.ApprovalProjectParaDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.apro.ApprovalProjectParaEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="project_id" property="projectId" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="approval_temp_id" property="approvalTempId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        project_id, type, name, approval_temp_id
    </sql>

    <select id="pageList" resultType="cn.uone.bean.entity.business.apro.ApprovalProjectParaEntity">
        select t.id,t.project_id,t.type,t.approval_temp_id,t.`name`,t.create_by,t.create_date,t.update_date,u.real_name AS update_by from
        t_approval_project_para t
        left join v_sys_user u on u.id = t.update_by
        where  1=1
        <if test="map.projectId != null and map.projectId != ''">
            AND t.project_id = #{map.projectId}
        </if>
    </select>

</mapper>
