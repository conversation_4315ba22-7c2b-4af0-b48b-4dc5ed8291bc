<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.apro.dao.ApprovalDetailDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.apro.ApprovalDetailEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="approval_node_id" property="approvalNodeId"/>
        <result column="name" property="name"/>
        <result column="userid" property="userid"/>
        <result column="party" property="party"/>
        <result column="status" property="status"/>
        <result column="speech" property="speech"/>
        <result column="optime" property="optime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        approval_node_id, name, userid, party, status, speech, optime
    </sql>

    <select id="approvalOrderConfirmInfo" resultType="cn.uone.bean.entity.business.bil.vo.BilOrderVo">
          select o.*,v.source_name address,c.contract_code,r.name singer,
        date_format(o.create_date,'%Y-%m-%d')order_time,date_format(o.pay_time,'%Y-%m-%d')apply_time
     from t_bil_order o
             left  join   v_res_source v on v.id=o.source_id
                    LEFT JOIN t_cont_contract c on c.id=o.contract_id
                    left JOIN v_sys_renter r on r.id = c.signer_id
            where o.id=#{orderid}
    </select>

</mapper>
