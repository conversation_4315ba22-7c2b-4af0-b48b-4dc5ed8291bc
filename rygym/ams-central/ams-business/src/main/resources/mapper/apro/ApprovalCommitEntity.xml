<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.apro.dao.ApprovalCommitDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.apro.ApprovalCommitEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="type" property="type"/>
        <result column="code" property="code"/>
        <result column="code_id" property="codeId"/>
        <result column="table_name" property="tableName"/>
        <result column="status" property="status"/>
        <result column="title" property="title"/>
        <result column="title1" property="title1"/>
        <result column="title2" property="title2"/>
        <result column="title3" property="title3"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        type, code, code_id, table_name, status, title, title1, title2, title3
    </sql>

    <select id="commitByme" resultType="cn.uone.bean.entity.business.apro.vo.ApprovalCommitVo">
        select ac.*,u.nick_name,date_format(ac.create_date,'%Y-%m-%d')time,date_format(ac.apply_time,'%Y-%m-%d')sq_time
        from t_approval_commit ac
        LEFT JOIN v_sys_user u on u.id=ac.userid
        LEFT JOIN v_reit_reimbursement t on t.id = ac.code_id
        left join v_sys_renter r on r.id = ac.signer
        where 1=1
        <if test="map.userid != null and map.userid != ''">
            and ac.userid= #{map.userid}
        </if>
        <if test="map.codeId != null and map.codeId != ''">
            and ac.code_id= #{map.codeId}
        </if>
        <if test="map.type != null and map.type != ''">
            and ac.type= #{map.type}
        </if>
        <if test="map.state != null and map.state != ''">
            <choose>
                <when test="map.state == '5'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '6'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '7'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('7')
                </when>
                <when test="map.state == '8'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('7')
                </when>
                <otherwise>
                    and ac.status= #{map.state}
                </otherwise>
            </choose>
        </if>
        <if test="map.payUnit != null and map.payUnit != ''">
            and t.pay_unit= #{map.payUnit}
        </if>
        <if test="map.reimProject != null and map.reimProject != ''">
            and t.project_id= #{map.reimProject}
        </if>
        <if test="map.project != null and map.project != ''">
            and EXISTS (SELECT 1 FROM t_bil_order o,v_res_source v
            WHERE v.id = o.source_id AND FIND_IN_SET(o.id,ac.code_id)>0 AND ac.type in ('6','7','15')
            and v.project_id= #{map.reimProject}
            )
        </if>
        <if test="map.keyWord != null and map.keyWord != ''">
            and (ac.title like "%"#{map.keyWord}"%" or ac.approval_num like "%"#{map.keyWord}"%"
            or ac.pay_unit like "%"#{map.keyWord}"%" or u.nick_name like "%"#{map.keyWord}"%"
            or r.name like "%"#{map.keyWord}"%"
            )
        </if>
        order by ac.create_date desc
    </select>

    <select id="toSubmit" resultType="cn.uone.bean.entity.business.apro.vo.ApprovalCommitVo">
        select ac.*,CONCAT(u.nick_name,'的')nick_name from t_approval_commit ac
        LEFT JOIN v_sys_user u on u.id=ac.userid
        LEFT JOIN v_reit_reimbursement t on t.id = ac.code_id
        left join v_sys_renter r on r.id = ac.signer
        where 1=1 AND ac.status='0' and ac.userid= #{map.userid}
        <if test="map.type != null and map.type != '' and  map.type !=0 ">
            and ac.type= #{map.type}
        </if>
        <if test="map.payUnit != null and map.payUnit != ''">
            and t.pay_unit= #{map.payUnit}
        </if>
        <if test="map.reimProject != null and map.reimProject != ''">
            and t.project_id= #{map.reimProject}
        </if>
        <if test="map.project != null and map.project != ''">
            and EXISTS (SELECT 1 FROM t_bil_order o,v_res_source v
            WHERE v.id = o.source_id AND FIND_IN_SET(o.id,ac.code_id)>0 AND ac.type in ('6','7','15')
            and v.project_id= #{map.reimProject}
            )
        </if>
        <if test="map.keyWord != null and map.keyWord != ''">
            and (ac.title like "%"#{map.keyWord}"%" or ac.approval_num like "%"#{map.keyWord}"%"
            or ac.pay_unit like "%"#{map.keyWord}"%" or u.nick_name like "%"#{map.keyWord}"%"
            or r.name like "%"#{map.keyWord}"%"
            )
        </if>
        ORDER BY ac.create_date,ac.update_date desc
    </select>

    <select id="toApproval" resultType="cn.uone.bean.entity.business.apro.vo.ApprovalCommitVo">
        select ac.*,CONCAT(u1.nick_name,'的')nick_name from t_approval_commit ac
        LEFT JOIN t_approval_detail d on d.`code`=ac.`code` AND d.`status`='1'
        INNER JOIN v_sys_user u on u.qywechat=d.userid
        INNER JOIN v_sys_user u1 on u1.id=ac.userid
        LEFT JOIN v_reit_reimbursement t on t.id = ac.code_id
        left join v_sys_renter r on r.id = ac.signer
        where 1=1 AND u.id= #{map.userid} and ac.status= '1'
        <if test="map.type != null and map.type != ''">
            and ac.type= #{map.type}
        </if>
        <if test="map.payUnit != null and map.payUnit != ''">
            and t.pay_unit= #{map.payUnit}
        </if>
        <if test="map.reimProject != null and map.reimProject != ''">
            and t.project_id= #{map.reimProject}
        </if>
        <if test="map.project != null and map.project != ''">
            and EXISTS (SELECT 1 FROM t_bil_order o,v_res_source v
            WHERE v.id = o.source_id AND FIND_IN_SET(o.id,ac.code_id)>0 AND ac.type in ('6','7','15')
            and v.project_id= #{map.reimProject}
            )
        </if>
        <if test="map.keyWord != null and map.keyWord != ''">
            and (ac.title like "%"#{map.keyWord}"%" or ac.approval_num like "%"#{map.keyWord}"%"
            or ac.pay_unit like "%"#{map.keyWord}"%" or u.nick_name like "%"#{map.keyWord}"%"
            or r.name like "%"#{map.keyWord}"%"
            )
        </if>
    </select>

    <select id="mytodo" resultType="cn.uone.bean.entity.business.apro.vo.ApprovalCommitVo">
        SELECT a.* from (
        select ac.*,CONCAT(u.nick_name,'的')nick_name from t_approval_commit ac
        LEFT JOIN v_sys_user u on u.id=ac.userid
        LEFT JOIN v_reit_reimbursement t on t.id = ac.code_id
        left join v_sys_renter r on r.id = ac.signer
        where 1=1 AND ac.status='0' and ac.userid= #{map.userid}
        <if test="map.type != null and map.type != ''">
            and ac.type= #{map.type}
        </if>
        <if test="map.payUnit != null and map.payUnit != ''">
            and t.pay_unit= #{map.payUnit}
        </if>
        <if test="map.reimProject != null and map.reimProject != ''">
            and t.project_id= #{map.reimProject}
        </if>
        <if test="map.project != null and map.project != ''">
            and EXISTS (SELECT 1 FROM t_bil_order o,v_res_source v
            WHERE v.id = o.source_id AND FIND_IN_SET(o.id,ac.code_id)>0 AND ac.type in ('6','7','15')
            and v.project_id= #{map.reimProject}
            )
        </if>
        <if test="map.keyWord != null and map.keyWord != ''">
            and (ac.title like "%"#{map.keyWord}"%" or ac.approval_num like "%"#{map.keyWord}"%"
            or ac.pay_unit like "%"#{map.keyWord}"%" or u.nick_name like "%"#{map.keyWord}"%"
            or r.name like "%"#{map.keyWord}"%" )
        </if>
        <if test="map.state != null and map.state != ''">
            <choose>
                <when test="map.state == '5'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '6'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '7'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('7')
                </when>
                <when test="map.state == '8'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('7')
                </when>
                <otherwise>
                    and ac.status= #{map.state}
                </otherwise>
            </choose>
        </if>
        UNION
        select ac.*,CONCAT(u1.nick_name,'的')nick_name from t_approval_commit ac
        LEFT JOIN t_approval_detail d on d.`code`=ac.`code` AND d.`status`='1'
        LEFT JOIN v_sys_user u on u.qywechat=d.userid
        LEFT JOIN v_sys_user u1 on u1.id=ac.userid
        LEFT JOIN v_reit_reimbursement t on t.id = ac.code_id
        left join v_sys_renter r on r.id = ac.signer
        where 1=1 AND u.id= #{map.userid}
        <if test="map.type != null and map.type != '' and  map.type !=0 ">
            and ac.type= #{map.type}
        </if>
        <if test="map.payUnit != null and map.payUnit != ''">
            and t.pay_unit= #{map.payUnit}
        </if>
        <if test="map.reimProject != null and map.reimProject != ''">
            and t.project_id= #{map.reimProject}
        </if>
        <if test="map.project != null and map.project != ''">
            and EXISTS (SELECT 1 FROM t_bil_order o,v_res_source v
            WHERE v.id = o.source_id AND FIND_IN_SET(o.id,ac.code_id)>0 AND ac.type in ('6','7','15')
            and v.project_id= #{map.reimProject}
            )
        </if>
        <if test="map.keyWord != null and map.keyWord != ''">
            and (ac.title like "%"#{map.keyWord}"%" or ac.approval_num like "%"#{map.keyWord}"%"
            or ac.pay_unit like "%"#{map.keyWord}"%" or u.nick_name like "%"#{map.keyWord}"%"
            or r.name like "%"#{map.keyWord}"%")
        </if>
        <if test="map.state != null and map.state != ''">
            <choose>
                <when test="map.state == '5'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '6'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '7'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('7')
                </when>
                <when test="map.state == '8'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('7')
                </when>
                <otherwise>
                    and ac.status= #{map.state}
                </otherwise>
            </choose>
        </if>

        UNION
        select ac.*,CONCAT(u1.nick_name,'的')nick_name from t_approval_commit ac
        LEFT JOIN (SELECT dd.* from
        (SELECT * from t_approval_detail WHERE status='2' ORDER BY sort desc)dd GROUP BY dd.code)d on d.`code`=ac.`code`
        LEFT JOIN v_sys_user u on u.qywechat=d.userid
        LEFT JOIN v_sys_user u1 on u1.id=ac.userid
        LEFT JOIN v_reit_reimbursement t on t.id = ac.code_id
        left join v_sys_renter r on r.id = ac.signer
        where 1=1 AND ac.`status`='2' AND u.id= #{map.userid} AND (ac.pay_status = '0' or ac.pay_status is null)
        AND ac.type in ('1','3','5')
        <if test="map.type != null and map.type != '' and  map.type !=0 ">
            and ac.type= #{map.type}
        </if>
        <if test="map.payUnit != null and map.payUnit != ''">
            and t.pay_unit= #{map.payUnit}
        </if>
        <if test="map.reimProject != null and map.reimProject != ''">
            and t.project_id= #{map.reimProject}
        </if>
        <if test="map.project != null and map.project != ''">
            and EXISTS (SELECT 1 FROM t_bil_order o,v_res_source v
            WHERE v.id = o.source_id AND FIND_IN_SET(o.id,ac.code_id)>0 AND ac.type in ('6','7','15')
            and v.project_id= #{map.reimProject}
            )
        </if>
        <if test="map.keyWord != null and map.keyWord != ''">
            and (ac.title like "%"#{map.keyWord}"%" or ac.approval_num like "%"#{map.keyWord}"%"
            or ac.pay_unit like "%"#{map.keyWord}"%" or u.nick_name like "%"#{map.keyWord}"%"
            or r.name like "%"#{map.keyWord}"%"
            )
        </if>
        <if test="map.state != null and map.state != ''">
            <choose>
                <when test="map.state == '5'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '6'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '7'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('7')
                </when>
                <when test="map.state == '8'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('7')
                </when>
                <otherwise>
                    and ac.status= #{map.state}
                </otherwise>
            </choose>
        </if>

        )a GROUP BY a.id ORDER BY a.create_date,a.update_date desc
    </select>

    <select id="myjoin" resultType="cn.uone.bean.entity.business.apro.vo.ApprovalCommitVo">
        select ac.*,CONCAT(u1.nick_name,'的')nick_name from t_approval_commit ac
        LEFT JOIN v_sys_user u1 on u1.id=ac.userid
        LEFT JOIN t_approval_detail d on d.code=ac.code
        LEFT JOIN v_sys_user u on u.qywechat=d.userid
        LEFT JOIN v_reit_reimbursement t on t.id = ac.code_id
        left join v_sys_renter r on r.id = ac.signer
        where 1=1 AND u.id=#{map.userid}
        <if test="map.type != null and map.type != ''">
            and ac.type= #{map.type}
        </if>
        <if test="map.state != null and map.state != ''">
            <choose>
                <when test="map.state == '5'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '6'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('1','6')
                </when>
                <when test="map.state == '7'.toString">
                    and ac.pay_status='0' and ac.status = '2' and ac.type in('7')
                </when>
                <when test="map.state == '8'.toString">
                    and ac.pay_status='1' and ac.status = '2' and ac.type in('7')
                </when>
                <otherwise>
                    and ac.status= #{map.state}
                </otherwise>
            </choose>
        </if>
        <if test="map.payUnit != null and map.payUnit != ''">
            and t.pay_unit= #{map.payUnit}
        </if>
        <if test="map.reimProject != null and map.reimProject != ''">
            and t.project_id= #{map.reimProject}
        </if>
        <if test="map.project != null and map.project != ''">
            and EXISTS (SELECT 1 FROM t_bil_order o,v_res_source v
            WHERE v.id = o.source_id AND FIND_IN_SET(o.id,ac.code_id)>0 AND ac.type in ('6','7','15')
            and v.project_id= #{map.reimProject}
            )
        </if>
        <if test="map.keyWord != null and map.keyWord != ''">
            and (ac.title like "%"#{map.keyWord}"%" or ac.approval_num like "%"#{map.keyWord}"%"
            or ac.pay_unit like "%"#{map.keyWord}"%" or u.nick_name like "%"#{map.keyWord}"%"
            or r.name like "%"#{map.keyWord}"%"
            )
        </if>
        GROUP BY ac.id
        order by ac.create_date desc
    </select>

    <select id="getDesignContractByid" resultType="cn.uone.bean.entity.business.apro.vo.ApprovalCommitVo">
          select ac.*,t.content,u.login_name as username from t_approval_commit ac
            LEFT JOIN t_cont_design_contract t on  t.id=ac.code_id
            LEFT JOIN v_sys_user u on u.id=ac.create_by
           where 1=1 and ac.id=#{id} and ac.type='2'
    </select>

    <select id="getAlreadyPayAll" resultType="cn.uone.bean.entity.business.apro.vo.ApprovalCommitVo">
        select IFNULL(sum(ac.price),0)price_all from t_approval_commit ac
        where 1=1 and ac.status='2' and ac.pay_time is not null
        <if test="map.codeId != null and map.codeId != ''">
            and ac.code_id= #{map.codeId}
        </if>
        <if test="map.type != null and map.type != ''">
            and ac.type= #{map.type}
        </if>
    </select>

    <update id="upApprovalState">
        update ${table} set ${key} = #{status} where id=#{id}
    </update>

    <select id="getQyUserInfo" resultType="cn.uone.bean.entity.crm.UserEntity">
        select  * from v_sys_user where  qywechat = #{qywechat}
    </select>

    <select id="getDepositInfo" resultType="java.util.Map">
        SELECT r.contract_id,GROUP_CONCAT(s.source_name)source_name,u.name,s.project_id
        FROM t_cont_contract_source_rel r,v_res_source s,t_cont_contract c,v_sys_renter u
        WHERE r.source_id=s.id AND c.id=r.contract_id AND u.id=c.signer_id
           and r.contract_id= #{contractId}
         GROUP BY r.contract_id
    </select>

    <select id="getByFun" resultType="cn.uone.bean.entity.business.apro.ApprovalCommitEntity">
        SELECT t.* FROM t_approval_commit t
        WHERE FIND_IN_SET(#{codeId},t.code_id)>0
        <if test="type != null and type != ''">
            and t.type= #{type}
        </if>
        ORDER BY t.create_date DESC LIMIT 1
    </select>

    <select id="getAllAppro" resultType="cn.uone.bean.entity.business.apro.ApprovalCommitEntity">
        SELECT t.* FROM t_approval_commit t
        WHERE FIND_IN_SET(#{codeId},t.code_id)>0
        <if test="type != null and type != ''">
            and t.type= #{type}
        </if>
        ORDER BY t.create_date DESC
    </select>

    <select id="getOrderCode" resultType="java.util.Map">
        SELECT GROUP_CONCAT(code) codes FROM t_bil_order WHERE
        id in
        <foreach collection="codeId" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getOrderCodeIds" resultType="java.lang.String">
        select code_id from t_approval_commit where type in
        <foreach collection="types" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and status = 2 and apply_time > '2020-06-01'
    </select>

    <insert id="saveApprovalRel" >
       INSERT INTO t_approval_other_rel  (id,approval_id,other_id) VALUES(MD5(RAND()),#{approvalId},#{otherId})
    </insert>

    <delete id="deleteRel">
        DELETE  FROM t_approval_other_rel
    </delete>


</mapper>
