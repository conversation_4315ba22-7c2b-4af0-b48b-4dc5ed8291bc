<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.cont.dao.ContTempDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.cont.ContTempEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="project_id" property="projectId"/>
        <result column="name" property="name" />
        <result column="subject_type" property="subjectType"/>
        <result column="type" property="type" />
        <result column="annex_type" property="annexType" />
        <result column="customer_type" property="customerType" />
        <result column="is_talent" property="isTalent" />
        <result column="subsidy_price" property="subsidyPrice" />
        <result column="subsidy_person" property="subsidyPerson" />
        <result column="is_push_cont_other" property="isPushContOther" />
        <result column="cont_platform_name" property="contPlatformName" />
        <result column="is_auto_order" property="isAutoOrder" />
        <result column="is_can_pay_order" property="isCanPayOrder" />
        <result column="is_push_order_other" property="isPushOrderOther" />
        <result column="order_platform_name" property="orderPlatformName" />
        <result column="state" property="state"/>
        <result column="gu_id" property="guId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        project_id,state,gu_id,
        name, subject_type, type, annex_type, customer_type, is_talent, subsidy_price, subsidy_person, is_push_cont_other, cont_platform_name, is_auto_order, is_can_pay_order, is_push_order_other, order_platform_name
    </sql>

    <sql id="Base_Column_List_a">
        t.id,
        t.create_by,
        t.create_date,
        t.update_by,
        t.update_date,
        t.project_id,t.state,
        t.name, t.subject_type, t.type, t.annex_type, t.customer_type, t.is_talent, t.subsidy_price, t.subsidy_person, t.is_push_cont_other, t.cont_platform_name, t.is_auto_order, t.is_can_pay_order, t.is_push_order_other, t.order_platform_name
    </sql>

    <select id="list" resultType="cn.uone.bean.entity.business.cont.ContTempEntity">
        select <include refid="Base_Column_List_a"/>,ifnull(u.real_name,u.nick_name) updater from t_cont_temp t left join v_sys_user u on u.id = t.update_by
        where 1=1
        #project_datascope#
        <if test="t.name != null and t.name != ''">
            AND t.name LIKE CONCAT('%', #{t.name}, '%')
        </if>
        <if test="t.projectId != null and t.projectId != ''">
            AND t.project_id = #{t.projectId}
        </if>
        <if test="t.type != null and t.type != ''">
            AND t.type = #{t.type}
        </if>
        <if test="t.customerType != null and t.customerType != ''">
            AND t.customer_type = #{t.customerType}
        </if>
        <if test="t.isTalent != null and t.isTalent != ''">
            AND t.is_talent = #{t.isTalent}
        </if>
        <if test="t.subjectType != null and t.subjectType != ''">
            AND t.subject_type = #{t.subjectType}
        </if>
        <if test="t.state != null and t.state != ''">
            AND t.state = #{t.state}
        </if>
        <if test="t.types != null and t.types.length > 0">
            AND t.type in
            <foreach collection="t.types" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t.update_date DESC

    </select>

</mapper>
