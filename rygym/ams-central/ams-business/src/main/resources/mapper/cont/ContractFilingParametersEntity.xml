<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.cont.dao.ContractFilingParametersDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.cont.ContractFilingParametersEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="project_id" property="projectId" />
        <result column="ssxmmc" property="ssxmmc" />
        <result column="tyshxydm" property="tyshxydm" />
        <result column="sqrxm" property="sqrxm" />
        <result column="sqrsjh" property="sqrsjh" />
        <result column="sqrzjlx" property="sqrzjlx" />
        <result column="sqrzjh" property="sqrzjh" />
        <result column="qszjlx" property="qszjlx" />
        <result column="qszjbh" property="qszjbh" />
        <result column="baBzdz" property="baBzdz" />
        <result column="baXzqh" property="baXzqh" />
        <result column="baZj" property="baZj" />
        <result column="baCj" property="baCj" />
        <result column="fwxz" property="fwxz" />
        <result column="fwyt" property="fwyt" />
        <result column="wqS" property="wqS" />
        <result column="wqTing" property="wqTing" />
        <result column="wqW" property="wqW" />
        <result column="wqC" property="wqC" />
        <result column="zlfs" property="zlfs" />
        <result column="bw" property="bw" />
        <result column="zxcd" property="zxcd" />
        <result column="jzmj" property="jzmj" />
        <result column="cqrxm" property="cqrxm" />
        <result column="cqrzjlx" property="cqrzjlx" />
        <result column="cqrzjhm" property="cqrzjhm" />
        <result column="cqzsmj" property="cqzsmj" />
        <result column="hirerType" property="hirerType" />
        <result column="hirerName" property="hirerName" />
        <result column="hirerPhone" property="hirerPhone" />
        <result column="hirerCardType" property="hirerCardType" />
        <result column="hirerCardNum" property="hirerCardNum" />
        <result column="hirerCardFile" property="hirerCardFile" />
        <result column="hirerAgentName" property="hirerAgentName" />
        <result column="hirerAgentPhone" property="hirerAgentPhone" />
        <result column="hirerAgentCardType" property="hirerAgentCardType" />
        <result column="hirerAgentCardNum" property="hirerAgentCardNum" />
        <result column="hirerAgentCardNum" property="hirerAgentCardNum" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        record_id_prefix,
        project_id,tyshxydm,ssxmmc, sqrxm, sqrsjh, sqrzjlx, sqrzjh, qszjlx, qszjbh, baBzdz, baXzqh, baZj, baCj, fwxz, fwyt, wqS, wqTing, wqW, wqC, zlfs, bw, zxcd, jzmj, cqrxm, cqrzjlx, cqrzjhm, cqzsmj, hirerType, hirerName, hirerPhone, hirerCardType, hirerCardNum, hirerCardFile, hirerAgentName, hirerAgentPhone, hirerAgentCardType, hirerAgentCardNum, remark
    </sql>

</mapper>
