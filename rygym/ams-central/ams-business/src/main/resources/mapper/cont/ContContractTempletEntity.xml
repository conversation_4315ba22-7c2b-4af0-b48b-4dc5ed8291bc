<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.cont.dao.ContContractTempletDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.cont.ContContractTempletEntity">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="name" property="name"/>
        <result column="contract_templet_type" property="contractTempletType"/>
        <result column="customer_type" property="customerType"/>
        <result column="project_id" property="projectId"/>
        <result column="pet_kept" property="petKept"/>
        <result column="is_talent" property="isTalent"/>
        <result column="content" property="content"/>
        <result column="subsidy_price" property="subsidyprice"/>
        <result column="subsidy_person" property="subsidyPerson"/>
        <result column="costId" property="costId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        A.id,
        A.type,
        A.create_by,
        A.create_date,
        A.update_by,
        A.update_date,
        A.name,
        A.contract_templet_type,
        A.customer_type,
        A.project_id,
        A.pet_kept,
        A.is_talent,
        A.content,
        A.subsidy_price,
        A.subsidy_person,
        B.name AS projectName
    </sql>

    <!-- 查询合同模板（分页） -->
    <select id="selectPageLists"  resultType="cn.uone.bean.entity.business.cont.vo.ContContractTempletVo">
        SELECT <include refid="Base_Column_List" />
        ,(SELECT group_concat(tr.cost_id) FROM t_cont_templet_cost_rel tr WHERE tr.templet_id = a.id) AS 'cost_id'
        FROM t_cont_contract_templet A
        LEFT JOIN t_res_project B ON A.project_id = B.id
        WHERE 1 = 1 #project_datascope# AND A.project_id = #{map.projectId}
        <if test="map.name != null and map.name != ''">
            AND A.name LIKE CONCAT('%', #{map.name}, '%')
        </if>
        <if test="map.contractTempletType != null and map.contractTempletType != ''">
            AND A.contract_templet_type = #{map.contractTempletType}
        </if>
        <if test="map.isTalent != null and map.isTalent != ''">
            AND A.is_talent = #{map.isTalent}
        </if>
        <if test="map.type != null and map.type != ''">
            AND A.type = #{map.type}
        </if>
        ORDER BY A.update_date DESC
    </select>

    <!-- 下拉框 -->
    <select id="select" resultType="cn.uone.bean.entity.business.cont.ContContractTempletEntity">
        SELECT
            id,
            name
        FROM t_cont_contract_templet
        WHERE 1 = 1 #project_datascope#
            AND project_id = #{projectId}
            <if test="customerType != null and customerType != ''">
                AND customer_type = #{customerType}
            </if>
            <if test="contractTempletType != null and contractTempletType != ''">
                AND contract_templet_type = #{contractTempletType}
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
    </select>
    <select id="selectContractTempletBySource"
            resultType="cn.uone.bean.entity.business.cont.ContContractTempletEntity">
        SELECT * FROM t_cont_contract_templet
        where 1=1
        <if test="map.isTalent != null and map.isTalent != ''">
            AND is_talent = #{map.isTalent}
        </if>
        <if test="map.sourceType != null and map.sourceType != ''">
            AND contract_templet_type = #{map.sourceType}
        </if>
        <if test="map.projectId != null and map.projectId != ''">
            AND project_id = #{map.projectId}
        </if>
        <if test="map.customerType != null and map.customerType != ''">
            AND customer_type = #{map.customerType}
        </if>
        <if test="map.type != null and map.type != ''">
            AND type = #{map.type}
        </if>
        <if test="map.contractTempletType != null and map.contractTempletType != ''">
            AND contract_templet_type = #{map.contractTempletType}
        </if>
    </select>

    <select id="selectContractTempletBySourceId"
            resultType="cn.uone.bean.entity.business.cont.ContContractTempletEntity">
        SELECT c.* FROM t_cont_contract_templet c,t_res_source s
        where c.project_id = s.project_id AND s.id = #{map.sourceId}
        <if test="map.isTalent != null and map.isTalent != ''">
            AND is_talent = #{map.isTalent}
        </if>
        <if test="map.tempType != null and map.tempType != ''">
            AND contract_templet_type = #{map.tempType}
        </if>
        <if test="map.customerType != null and map.customerType != ''">
            AND c.customer_type = #{map.customerType}
        </if>
        <if test="map.type != null and map.type != ''">
            AND c.type = #{map.type}
        </if>
    </select>

</mapper>
