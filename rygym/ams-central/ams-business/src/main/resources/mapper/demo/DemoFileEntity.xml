<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.demo.dao.DemoFileDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.demo.DemoFileEntity">
    <result column="id" property="id" />
        <result column="title" property="title" />
        <result column="contract_code" property="contractCode" />
        <result column="url" property="url" />
        <result column="category_id" property="categoryId" />
    </resultMap>

    <resultMap id="FileResultMap" type="cn.uone.business.demo.vo.FileVo">

        <result column="title" property="title" />
        <result column="contract_code" property="contractCode" />
        <result column="url" property="url" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
    </resultMap>



    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        title,
        contract_code,
        url, category_id
    </sql>


    <select id="listPage" resultMap="FileResultMap">
       select f.title,f.contract_code,f.url,f.category_id,c.category_name from demo_file f join demo_file_category c
       on f.category_id=c.category_id where f.contract_code=#{contractCode}
    </select>

</mapper>
