<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.demo.dao.DemoOwnerDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.demo.DemoOwnerEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="name" property="ownerName" />
        <result column="id_type" property="idType" />
        <result column="id_no" property="idNo" />
        <result column="province_id" property="provinceId" />
        <result column="city_id" property="cityId" />
        <result column="district_id" property="districtId" />
        <result column="address" property="address" />
        <result column="postcode" property="postcode" />
        <result column="contact_name" property="contactName" />
        <result column="tel" property="tel" />
        <result column="email" property="email" />
        <result column="bank_account" property="bankAccount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        name, id_type, id_no, province_id, city_id, district_id, address, postcode, contact_name, tel, email, bank_account
    </sql>

</mapper>
