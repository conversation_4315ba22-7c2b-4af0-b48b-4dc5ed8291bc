<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.demo.dao.DemoContractDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.demo.DemoContractEntity">
    <result column="id" property="id" />
    <result column="create_date" property="createDate" />
    <result column="create_by" property="createBy" />
    <result column="update_date" property="updateDate" />
    <result column="update_by" property="updateBy" />
        <result column="contract_code" property="contractCode" />
        <result column="project_id" property="projectId" />
        <result column="source_id" property="sourceId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="price" property="price" />
        <result column="pay_type" property="payType" />
        <result column="owner_id" property="ownerId" />
    </resultMap>
    <resultMap id="contractResultMap" type="cn.uone.business.demo.vo.ContractVo">
        <result column="contract_code" property="contractCode" />
        <result column="name" property="projectName" />
        <result column="code" property="roomCode" />
        <result column="owner_name" property="ownerName" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="price" property="price" />
        <result column="pay_type" property="simPayType" />
        <result column="state" property="state" />

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_date,
        update_by,
        contract_code, project_id, room_num, start_time, end_time, price, pay_type
    </sql>
    <select id="listPage" resultMap="contractResultMap">

--            t_rpt_contract as c
--            t_res_project as p
--            t_res_project_owner as po
--            t_res_property_partition  as pp
--            t_res_source as s
--            p,po,pp关联,得到po.ownerName,po.bank_account
--            p,s关联,得到s.code(roomCode)

      select c.id,c.contract_code,p.name,s.code,po.name as owner_name,c.start_time,c.end_time,c.price,c.pay_type,c.state,po.bank_account
        from t_rpt_contract c join t_res_project p on c.project_id=p.id
        join t_res_property_partition pp on p.id=pp.project_id
        join t_res_project_owner po on pp.property_owner=po.id
        left join t_res_source s on c.source_id = s.id
        where 1=1
<!--        <if test="map.contractCode!= null">-->
<!--            and (LOCATE(#{map.contractCode},contract_code)-->
<!--            or LOCATE(#{map.name},name) or  LOCATE(#{map.ownerName},owner_name) or room_code=#{map.roomCode})-->
<!--        </if>-->
            and c.project_id=#{projectId}
        <if test="search!= null">
            and (LOCATE(#{search},contract_code)
            or LOCATE(#{search},p.name) or LOCATE(#{search},po.name) or LOCATE(#{search},s.code))
        </if>


    </select>
    

</mapper>
