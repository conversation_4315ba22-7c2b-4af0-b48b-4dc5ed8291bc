<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.demo.dao.ScoreDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.demo.ScoreEntity">
        <result column="stu_id" property="stuId" />
        <result column="c_id" property="cId" />
        <result column="score" property="score" />
    </resultMap>
    <resultMap id="ScoreResultMap" type="cn.uone.business.demo.vo.StudentVo">
        <result column="stu_id" property="stuId" />
        <result column="stud_name" property="studName" />
        <result column="c_name" property="cname" />
        <result column="score" property="score" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        stu_id, c_id, score
    </sql>

    <update id="updateScore">
        update demo_score
        set
        <if test="score!=null">
            score=#{score}
        </if>
        where stu_id=#{stuId} and c_id=(select c_id from demo_course where c_name=#{cname})
    </update>

    <select id="listPage" resultMap="ScoreResultMap">
        select st.stu_id,st.stud_name,c.c_name,sc.score from demo_score sc,demo_course c,demo_student st
            where st.stu_id=sc.stu_id and c.c_id=sc.c_id
    </select>

    <select id="listSelect" resultMap="ScoreResultMap">
        select st.stu_id,st.stud_name,c.c_name,sc.score
        from demo_student st
        left join demo_score sc on st.stu_id=sc.stu_id
        left join demo_course c on c.c_id=sc.c_id
        where st.stud_name=#{studName}
    </select>

</mapper>
