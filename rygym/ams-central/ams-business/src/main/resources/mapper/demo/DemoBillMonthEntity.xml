<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.demo.dao.DemoBillMonthDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.demo.DemoBillMonthEntity">
    <result column="id" property="id" />
    <result column="create_date" property="createDate" />
    <result column="create_by" property="createBy" />
    <result column="update_date" property="updateDate" />
    <result column="update_by" property="updateBy" />
        <result column="order_code" property="orderCode" />
        <result column="price" property="price" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_date,
        update_by,
        order_code, price, start_time, end_time
    </sql>
    <delete id="deleteBill">
            delete from t_rpt_bill_month where order_code in
            <foreach collection="idList" item="orderCode" separator="," open="(" close=")">
                #{orderCode}
            </foreach>
    </delete>
    <select id="listSelect" resultMap="BaseResultMap">
        select * from t_rpt_bill_month where order_code=#{orderCode}
    </select>
</mapper>
