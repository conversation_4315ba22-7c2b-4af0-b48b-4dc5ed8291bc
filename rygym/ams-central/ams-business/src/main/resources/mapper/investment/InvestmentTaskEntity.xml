<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.investment.dao.InvestmentTaskDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.investment.InvestmentTaskEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="task_code" property="taskCode" />
        <result column="task_name" property="taskName" />
        <result column="task_type" property="taskType" />
        <result column="task_source" property="taskSource" />
        <result column="task_state" property="taskState" />
        <result column="task_priority" property="taskPriority" />
        <result column="task_describe" property="taskDescribe" />
        <result column="task_assignor" property="taskAssignor" />
        <result column="task_owner" property="taskOwner" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="complete_progress" property="completeProgress" />
        <result column="task_attachment" property="taskAttachment" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        task_code,task_name,task_type,task_source,task_state,task_priority,task_describe,task_assignor,task_owner,start_date,end_date,complete_progress,task_attachment,remark
    </sql>

    <select id="getPageByParams" resultType="cn.uone.bean.entity.business.investment.vo.InvestmentTaskVo">
        SELECT s.*,v1.real_name as taskAssignorName,v2.real_name as taskOwnerName
        FROM t_investment_task s
        left join v_sys_user v1 on s.task_assignor=v1.id
        left join v_sys_user v2 on s.task_owner=v2.id
        WHERE 1=1
        <if test="map.taskCode != null and map.taskCode != ''">
            AND s.task_code like "%"#{map.taskCode}"%"
        </if>
        <if test="map.taskName != null and map.taskName != ''">
            AND s.task_name like "%"#{map.taskName}"%"
        </if>
        <if test="map.taskType != null and map.taskType != ''">
            AND s.task_type = #{map.taskType}
        </if>
        <if test="map.taskSource != null and map.taskSource != ''">
            AND s.task_source = #{map.taskSource}
        </if>
        <if test="map.taskState != null and map.taskState != ''">
            AND s.task_state = #{map.taskState}
        </if>
        <if test="map.taskPriority != null and map.taskPriority != ''">
            AND s.task_priority = #{map.taskPriority}
        </if>
        <if test="map.startDateStart != null">
            AND <![CDATA[date(s.start_date) >= date(#{map.startDateStart}) ]]>
        </if>
        <if test="map.startDateEnd != null">
            AND <![CDATA[date(s.start_date) <= date(#{map.startDateEnd}) ]]>
        </if>
        <if test="map.endDateStart != null">
            AND <![CDATA[date(s.end_date) >= date(#{map.endDateStart}) ]]>
        </if>
        <if test="map.endDateEnd != null">
            AND <![CDATA[date(s.end_date) <= date(#{map.endDateEnd}) ]]>
        </if>
        order by s.create_date desc
    </select>

    <select id="getTaskByParams" resultType="cn.uone.bean.entity.business.investment.vo.InvestmentTaskVo">
        SELECT s.id,s.task_name
        FROM t_investment_task s
        WHERE 1=1
        <if test="map.taskCode != null and map.taskCode != ''">
            AND s.task_code like "%"#{map.taskCode}"%"
        </if>
        <if test="map.taskName != null and map.taskName != ''">
            AND s.task_name like "%"#{map.taskName}"%"
        </if>
        <if test="map.taskType != null and map.taskType != ''">
            AND s.task_type = #{map.taskType}
        </if>
        order by s.create_date desc
    </select>

</mapper>
