<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.investment.dao.InvestmentRegisterDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.investment.InvestmentRegisterEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="id_type" property="idType" />
        <result column="id_number" property="idNumber" />
        <result column="tel" property="tel" />
        <result column="policy" property="policy" />
        <result column="register_time" property="registerTime" />
        <result column="status" property="status" />
        <result column="tail_after" property="tailAfter" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        code, name, type, id_type, id_number, tel, policy, register_time, status, tail_after, remark
    </sql>

</mapper>
