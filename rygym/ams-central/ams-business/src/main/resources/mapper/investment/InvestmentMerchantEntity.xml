<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.investment.dao.InvestmentMerchantDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.investment.InvestmentMerchantEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="merchant_name" property="merchantName" />
        <result column="user_name" property="userName" />
        <result column="tel" property="tel" />
        <result column="business_type" property="businessType" />
        <result column="brand_name" property="brandName" />
        <result column="brand_level" property="brandLevel" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        merchant_name, user_name, tel, business_type, brand_name, brand_level, remark
    </sql>

</mapper>
