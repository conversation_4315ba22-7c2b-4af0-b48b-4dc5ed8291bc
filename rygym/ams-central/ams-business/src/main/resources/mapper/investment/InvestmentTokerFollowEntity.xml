<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.investment.dao.InvestmentTokerFollowDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.investment.InvestmentTokerFollowEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="toker_id" property="tokerId" />
        <result column="pattern" property="pattern" />
        <result column="follow_time" property="followTime" />
        <result column="tracker" property="tracker" />
        <result column="tracker_id" property="trackerId" />
        <result column="situation" property="situation" />
        <result column="intention_level" property="intentionLevel" />
        <result column="client_opinion" property="clientOpinion" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_date" property="createDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        update_date,
        toker_id, pattern, follow_time, tracker, tracker_id, situation, intention_level, client_opinion, status, remark, create_date
    </sql>

</mapper>
