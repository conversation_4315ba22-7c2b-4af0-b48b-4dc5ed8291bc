<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.investment.dao.InvestmentResourceDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.investment.InvestmentResourceEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="name" property="name" />
        <result column="business_name" property="businessName" />
        <result column="business_id" property="businessId" />
        <result column="category_name" property="categoryName" />
        <result column="category_id" property="categoryId" />
        <result column="symbiosis" property="symbiosis" />
        <result column="intention_level" property="intentionLevel" />
        <result column="price_start" property="priceStart" />
        <result column="price_end" property="priceEnd" />
        <result column="area" property="area" />
        <result column="open_shop_year" property="openShopYear" />
        <result column="before_cooperation" property="beforeCooperation" />
        <result column="manage_model" property="manageModel" />
        <result column="dependency" property="dependency" />
        <result column="synopsis" property="synopsis" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_date" property="createDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        update_date,
        name,status, business_name, business_id, category_name, category_id, symbiosis, intention_level, price_start, price_end, area, open_shop_year, before_cooperation, manage_model, dependency, synopsis, remark, create_date
    </sql>

</mapper>
