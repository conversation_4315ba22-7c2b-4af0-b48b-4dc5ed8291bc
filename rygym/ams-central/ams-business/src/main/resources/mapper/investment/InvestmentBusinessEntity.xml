<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.investment.dao.InvestmentBusinessDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.investment.InvestmentBusinessEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="business_name" property="businessName" />
        <result column="remark" property="remark" />
        <result column="create_date" property="createDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        update_date,
        business_name, remark, create_date
    </sql>

</mapper>
