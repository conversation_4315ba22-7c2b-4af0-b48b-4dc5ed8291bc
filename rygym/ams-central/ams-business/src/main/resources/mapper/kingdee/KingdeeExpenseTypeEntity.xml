<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.kingdee.dao.KingdeeExpenseTypeDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.kingdee.KingdeeExpenseTypeEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="order_item_type" property="orderItemType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_date,
        t.update_by,
        t.update_date,
        t.code, t.name, t.type, t.order_item_type
    </sql>

    <select id="getList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from t_kingdee_expense_type t
        where 1=1
        <if test="map.type != null and map.type != ''">
            AND t.type = #{map.type}
        </if>
        group by t.code
    </select>

</mapper>
