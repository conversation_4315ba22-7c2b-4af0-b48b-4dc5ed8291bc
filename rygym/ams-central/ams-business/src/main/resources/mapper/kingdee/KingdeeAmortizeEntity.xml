<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.kingdee.dao.KingdeeAmortizeDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.kingdee.KingdeeAmortizeEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="number" property="number" />
        <result column="company" property="company" />
        <result column="push_period" property="pushPeriod" />
        <result column="dy_period" property="dyPeriod" />
        <result column="project" property="project" />
        <result column="project_id" property="projectId" />
        <result column="kingdee_id" property="kingdeeId" />
        <result column="company_name" property="companyName" />
    </resultMap>

    <resultMap id="BaseResultVoMap" type="cn.uone.bean.entity.business.kingdee.vo.KingdeeAmortizeVo" extends="BaseResultMap">
        <collection property="entrys" column="{amortizeId=id}"
                    select="cn.uone.business.kingdee.dao.KingdeeAmortizeItemDao.getListByAmortizeId">
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_date,
        t.update_by,
        t.update_date,
        t.number, t.company, t.push_period, t.dy_period, t.project, t.project_id,t.kingdee_id
    </sql>

    <select id="selectVoById" resultMap="BaseResultVoMap">
        select <include refid="Base_Column_List" />
        from t_kingdee_amortize t
        where 1=1 and t.id=#{id}
    </select>

    <select id="selectKingdeeAmortizeByMap" parameterType="java.util.Map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />,cc.param_name company_name
        from t_kingdee_amortize t
        LEFT JOIN t_res_project_para cc ON cc.`param_value`=t.`company` AND cc.param_code='KINGDEE_COMPANY' AND cc.`project_id`=t.`project_id`
        where 1=1
        #project_datascope#
        <if test="map.searchVo.projectId != null and map.searchVo.projectId != ''">
            AND t.project_id = #{map.searchVo.projectId}
        </if>
        <if test="map.searchVo.id != null and map.searchVo.id != ''">
            AND t.id = #{map.searchVo.id}
        </if>
        <if test="map.searchVo.number != null and map.searchVo.number != ''">
            and t.number like CONCAT('%', #{map.searchVo.number}, '%')
        </if>
        <if test="map.searchVo.company != null and map.searchVo.company != ''">
            and t.company = #{map.searchVo.company}
        </if>
        order by t.create_date,t.dy_period
    </select>

</mapper>
