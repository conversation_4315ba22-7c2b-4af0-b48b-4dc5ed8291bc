<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.kingdee.dao.KingdeeReceivableItemDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.kingdee.KingdeeReceivableItemEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="receivable_id" property="receivableId" />
        <result column="payment_type" property="paymentType" />
        <result column="cz_type" property="czType" />
        <result column="amount" property="amount" />
        <result column="tax_rate" property="taxRate" />
        <result column="tax_amount" property="taxAmount" />
        <result column="no_tax_amount" property="noTaxAmount" />
        <result column="customer" property="customer" />
        <result column="product" property="product" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_date,
        t.update_by,
        t.update_date,
        t.receivable_id, t.payment_type,t.cz_type, t.amount, t.tax_rate, t.tax_amount, t.no_tax_amount, t.customer, t.product
    </sql>

    <select id="getListByReceivableId" parameterType="java.util.Map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from t_kingdee_receivable_item t
        where 1=1
        <if test="receivableId != null and receivableId != ''">
            AND t.receivable_id = #{receivableId}
        </if>
        order by t.product,customer,t.payment_type
    </select>

    <select id="getVoListByReceivableId" parameterType="java.util.Map" resultType="cn.uone.bean.entity.business.kingdee.vo.KingdeeReceivableItemVo">
        select <include refid="Base_Column_List" />,kp.name productName,kc.name customerName,ket.name paymentTypeName
        from t_kingdee_receivable_item t
        LEFT JOIN t_kingdee_product kp ON kp.`code`=t.`product`
        LEFT JOIN (select code,`name` from t_kingdee_customer group by code) kc ON kc.code=t.`customer`
        LEFT JOIN (select code,`name` from t_kingdee_expense_type group by code) ket on ket.code=t.payment_type
        where 1=1
        <if test="receivableId != null and receivableId != ''">
            AND t.receivable_id = #{receivableId}
        </if>
        order by t.product,customer,t.payment_type
    </select>

    <select id="getVoList" parameterType="java.util.Map" resultType="cn.uone.bean.entity.business.kingdee.vo.KingdeeReceivableItemVo">
        select <include refid="Base_Column_List" />,kp.name productName,kc.name customerName,ket.name paymentTypeName
        from t_kingdee_receivable_item t
        LEFT JOIN t_kingdee_product kp ON kp.`code`=t.`product`
        LEFT JOIN (select code,`name` from t_kingdee_customer group by code) kc ON kc.code=t.`customer`
        LEFT JOIN (select code,`name` from t_kingdee_expense_type group by code) ket on ket.code=t.payment_type
        where 1=1
        <if test="map.searchVo.receivableId != null and map.searchVo.receivableId != ''">
            AND t.receivable_id = #{map.searchVo.receivableId}
        </if>
        <if test="map.searchVo.paymentType != null and map.searchVo.paymentType != ''">
            AND t.payment_type = #{map.searchVo.paymentType}
        </if>
        <if test="map.searchVo.product != null and map.searchVo.product != ''">
            AND t.product = #{map.searchVo.product}
        </if>
        order by t.product,t.customer,t.payment_type
    </select>

</mapper>
