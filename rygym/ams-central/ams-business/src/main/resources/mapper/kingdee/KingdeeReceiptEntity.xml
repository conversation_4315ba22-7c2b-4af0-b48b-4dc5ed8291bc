<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.kingdee.dao.KingdeeReceiptDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.kingdee.KingdeeReceiptEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="number" property="number" />
        <result column="company" property="company" />
        <result column="channel" property="channel" />
        <result column="amount" property="amount" />
        <result column="service_charge" property="serviceCharge" />
        <result column="entry_date" property="entryDate" />
        <result column="project_id" property="projectId" />
        <result column="rel_id" property="relId" />
        <result column="rel_type" property="relType" />
        <result column="company_name" property="companyName" />
        <result column="bank_account" property="bankAccount" />
    </resultMap>

    <resultMap id="BaseResultVoMap" type="cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptVo" extends="BaseResultMap">
        <collection property="entrys" column="{receiptId=id}"
                    select="cn.uone.business.kingdee.dao.KingdeeReceiptItemDao.getListByReceiptId">
        </collection>
    </resultMap>
    <resultMap id="BaseResultVoMapForUnion" type="cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptVo" extends="BaseResultMap">
        <collection property="entrys" column="number"
                    select="cn.uone.business.kingdee.dao.KingdeeReceiptItemDao.getListForCreateByNumber">
        </collection>
    </resultMap>
    <resultMap id="BaseResultVoMapForConfirm" type="cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptVo" extends="BaseResultMap">
        <result column="order_id" property="orderId" />
        <collection property="entrys" column="{orderId=order_id}"
                    select="cn.uone.business.kingdee.dao.KingdeeReceiptItemDao.getListForCreateByOrderId">
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_date,
        t.update_by,
        t.update_date,
        t.number, t.company, t.channel, t.amount, t.service_charge, t.entry_date, t.project_id,t.rel_id,t.bank_account
    </sql>

    <select id="selectVoById" resultMap="BaseResultVoMap">
        select <include refid="Base_Column_List" />
        from t_kingdee_receipt t
        where 1=1 and t.id=#{id}
    </select>

    <select id="selectVoByMap" resultMap="BaseResultVoMap">
        select <include refid="Base_Column_List" />
        from t_kingdee_receipt t
        where 1=1
        <if test="map.searchVo.company != null and map.searchVo.company != ''">
            and t.company = #{map.searchVo.company}
        </if>
        <if test="map.searchVo.startEntryDate != null">
            AND <![CDATA[date(t.entry_date) >= date(#{map.searchVo.startEntryDate}) ]]>
        </if>
        <if test="map.searchVo.endEntryDate != null">
            AND <![CDATA[date(t.entry_date) <= date(#{map.searchVo.endEntryDate}) ]]>
        </if>
    </select>

    <select id="selectKingdeeReceiptByMap" parameterType="java.util.Map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />,IF(t.`rel_type`='1',a.`name`,cc.name) company_name
        from t_kingdee_receipt t
        LEFT JOIN t_bil_order_confirm cc ON cc.`id`=t.`rel_id` AND t.`rel_type`='2'
        LEFT JOIN t_bil_transfer s ON s.id=t.`rel_id` AND t.`rel_type`='1'
        LEFT JOIN t_base_account a ON a.id=s.`account_id`
        where 1=1
        #project_datascope#
        <if test="map.searchVo.projectId != null and map.searchVo.projectId != ''">
            AND t.project_id = #{map.searchVo.projectId}
        </if>
        <if test="map.searchVo.id != null and map.searchVo.id != ''">
            AND t.id = #{map.searchVo.id}
        </if>
        <if test="map.searchVo.number != null and map.searchVo.number != ''">
           and t.number like CONCAT('%', #{map.searchVo.number}, '%')
        </if>
        <if test="map.searchVo.company != null and map.searchVo.company != ''">
            and t.company = #{map.searchVo.company}
        </if>
        <if test="map.searchVo.channel != null and map.searchVo.channel != ''">
            and t.channel = #{map.searchVo.channel}
        </if>
        <if test="map.searchVo.entryDate != null">
            AND <![CDATA[date(t.entry_date) = date(#{map.searchVo.entryDate}) ]]>
        </if>
        <if test="map.searchVo.startEntryDate != null">
            AND <![CDATA[date(t.entry_date) >= date(#{map.searchVo.startEntryDate}) ]]>
        </if>
        <if test="map.searchVo.endEntryDate != null">
            AND <![CDATA[date(t.entry_date) <= date(#{map.searchVo.endEntryDate}) ]]>
        </if>
        order by t.entry_date
    </select>

    <select id="getUnionTransfersByArriveTime" resultMap="BaseResultVoMapForUnion">
        SELECT t.`arrive_code` number,DATE_FORMAT(t.`arrive_time`,'%Y-%m-%d') entry_date,
        t.`payment` amount,t.`payment` total_amount,s.`company_code` company,s.`project_id`,s.code bank_account,'银联商务' channel,
        t.id rel_id,'1' rel_type,t.order_id
        FROM t_bil_transfer t
        LEFT JOIN t_base_account s ON s.id = t.account_id
        WHERE 1=1
        AND NOT EXISTS(SELECT 1 FROM t_kingdee_receipt r WHERE r.rel_id=t.id)
        AND t.`transfer_type`= 3
        AND t.`arrive_code` IS NOT NULL
        AND DATE_FORMAT(t.`arrive_time`,'%Y-%m-%d') = #{arriveTime}
    </select>
    <select id="getOrderConfirmByApplyTime" resultMap="BaseResultVoMapForConfirm">
        SELECT t.`trade_code` number,DATE_FORMAT(t.`apply_time`,'%Y-%m-%d') entry_date,t.`price` amount,t.`price` total_amount,t.code company,s.`project_id`,t.card bank_account,'线下转账' channel,
        t.id rel_id,'2' rel_type,t.order_id
        FROM t_bil_order_confirm t
        LEFT JOIN t_bil_order o ON o.id=SUBSTRING_INDEX(t.`order_id`,',',1)
        LEFT JOIN t_res_source s ON s.id=o.`source_id`
        WHERE t.state='1'
        AND NOT EXISTS(SELECT 1 FROM t_kingdee_receipt r WHERE r.rel_id=t.id)
        AND DATE_FORMAT(t.`update_date`,'%Y-%m-%d') = #{applyTime}
    </select>

    <select id="getUnionTransferById" resultMap="BaseResultVoMapForUnion">
        SELECT t.`arrive_code` number,DATE_FORMAT(t.`arrive_time`,'%Y-%m-%d') entry_date,
        t.`payment` amount,t.service_fee service_charge,s.`company_code` company,s.`project_id`,s.code bank_account,'银联商务' channel,
        t.id rel_id,'1' rel_type
        FROM t_bil_transfer t
        LEFT JOIN t_base_account s ON s.id = t.account_id
        WHERE 1=1
        AND NOT EXISTS(SELECT 1 FROM t_kingdee_receipt r WHERE r.rel_id=t.id)
        AND t.`transfer_type`= 3
        AND t.`arrive_code` IS NOT NULL
        AND t.id = #{id}
    </select>
    <select id="getOrderConfirmById" resultMap="BaseResultVoMapForConfirm">
        SELECT t.`trade_code` number,DATE_FORMAT(t.`apply_time`,'%Y-%m-%d') entry_date,t.`price` amount,0 as service_charge,t.code company,s.`project_id`,t.card bank_account,'线下转账' channel,
        t.id rel_id,'2' rel_type,t.order_id
        FROM t_bil_order_confirm t
        LEFT JOIN t_bil_order o ON o.id=SUBSTRING_INDEX(t.`order_id`,',',1)
        LEFT JOIN t_res_source s ON s.id=o.`source_id`
        WHERE t.state='1'
        AND NOT EXISTS(SELECT 1 FROM t_kingdee_receipt r WHERE r.rel_id=t.id)
        AND t.id = #{id}
    </select>

</mapper>
