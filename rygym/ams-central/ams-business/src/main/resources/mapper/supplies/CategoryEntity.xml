<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.supplies.dao.CategoryDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.supplies.CategoryEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="name" property="name" />
        <result column="is_detail" property="isDetail" />
        <result column="type" property="type" />
        <result column="state" property="state" />
        <result column="remark" property="remark" />
        <result column="total_quantity" property="totalQuantity" />
        <result column="recipients_quantity" property="recipientsQuantity" />
        <result column="lend_quantity" property="lendQuantity" />
        <result column="now_quantity" property="nowQuantity" />
        <result column="early_warning" property="earlyWarning" />
        <result column="company_id" property="companyId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        company_id,name,is_detail, type, state,remark,total_quantity,recipients_quantity,lend_quantity,now_quantity,early_warning,quota
    </sql>

    <select id="getApplyByUser" resultType="cn.uone.bean.entity.business.supplies.vo.CategoryVo">
        select * from (
        select t.applicant_id , t.item_name itemName, t.quantity , t.apply_time applyTime,
            t.auditor , t.audit_time auditTime,  t.state states  ,'采购' types , t.create_date
         from t_purchase_apply t
        union all
        select t.applicant_id , t.item_name itemName, t.quantity , t.apply_time applyTime,
            t.auditor , t.audit_time auditTime,  t.state states,'领用' types, t.create_date
         from t_collect_apply t
        ) s where 1 = 1 and s.applicant_id = #{userId}

        order by s.create_date desc
    </select>
</mapper>
