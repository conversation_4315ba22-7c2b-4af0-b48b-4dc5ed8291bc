<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.supplies.dao.SuppliesCheckSublistDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.supplies.SuppliesCheckSublistEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="check_id" property="checkId" />
        <result column="asserts_id" property="assertsId" />
        <result column="stock_quantity" property="stockQuantity" />
        <result column="actual_quantity" property="actualQuantity" />
        <result column="state" property="state" />
        <result column="quantity_variance" property="quantityVariance" />
        <result column="cause" property="cause" />
        <result column="dispose" property="dispose" />
        <result column="handler" property="handler" />
        <result column="handler_id" property="handlerId" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="remark" property="remark" />
        <result column="create_date" property="createDate" />
        <result column="is_abnormal" property="isAbnormal" />
        <result column="company_id" property="companyId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        update_date,
        company_id,check_id, is_abnormal,asserts_id, stock_quantity, actual_quantity, state, quantity_variance, cause, dispose, handler, handler_id, name, type, remark, create_date
    </sql>
    <delete id="delForCheckId">
        delete FROM t_supplies_check_sublist where
        check_id in
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
