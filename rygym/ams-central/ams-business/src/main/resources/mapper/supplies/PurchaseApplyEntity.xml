<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.supplies.dao.PurchaseApplyDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.supplies.PurchaseApplyEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="title" property="title" />
        <result column="applicant" property="applicant" />
        <result column="mobile" property="mobile" />
        <result column="apply_time" property="applyTime" />
        <result column="state" property="state" />
        <result column="audit_time" property="auditTime" />
        <result column="item_name" property="itemName" />
        <result column="price" property="price" />
        <result column="quantity" property="quantity" />
        <result column="material_category_id" property="materialCategoryId" />
        <result column="auditor" property="auditor" />
        <result column="auditor_id" property="auditorId" />
        <result column="applicant_id" property="applicantId" />
        <result column="quota" property="quota" />
        <result column="dept_lead" property="deptLead" />
        <result column="dept_lead_id" property="deptLeadId" />
        <result column="dept_lead_data" property="deptLeadData" />
        <result column="branched_lead" property="branchedLead" />
        <result column="branched_lead_id" property="branchedLeadId" />
        <result column="branched_lead_data" property="branchedLeadData" />
        <result column="general_manager" property="generalManager" />
        <result column="general_manager_id" property="generalManagerId" />
        <result column="general_manager_data" property="generalManagerData" />
        <result column="flow_node" property="flowNode" />
        <result column="dept_result" property="deptResult" />
        <result column="branched_result" property="branchedResult" />
        <result column="general_result" property="generalResult" />
        <result column="special" property="special" />
        <result column="dept_cause" property="deptCause" />
        <result column="branched_cause" property="branchedCause" />
        <result column="general_cause" property="generalCause" />
        <result column="company_id" property="companyId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        company_id,title,applicant, mobile, apply_time,state,audit_time,item_name,price,quantity,material_category_id,auditor,auditor_id,applicant_id,
        dept_lead,dept_lead_id,dept_lead_data,branched_lead,branched_lead_id,branched_lead_data,general_manager,general_manager_id,general_manager_data,flow_node,
         deptResult,branchedResult,generalResult,special,dept_cause,branched_cause,general_cause
    </sql>

    <select id="getListByPage" resultMap="BaseResultMap">
    select p.*,a.quota as quota FROM t_purchase_apply p
    left join t_base_asserts a on a.id = p.material_category_id
    where
    1=1
    <if test="purchaseApplyEntity.itemName !=null and purchaseApplyEntity.itemName !=''">
        and p.item_name like "%"#{purchaseApplyEntity.itemName}"%"
    </if>
    <if test="purchaseApplyEntity.applicant !=null and purchaseApplyEntity.applicant !=''">
        and p.applicant like "%"#{purchaseApplyEntity.applicant}"%"
    </if>
    <if test="purchaseApplyEntity.state =='审批'">
        and p.auditor_id = #{purchaseApplyEntity.auditorId}
        and p.state in ('0','1')
    </if>
    ORDER BY p.create_date desc
    </select>

    <select id="getQuantityByState" resultType="java.lang.Integer">
    SELECT count(id)
    FROM t_purchase_apply
    where
    (state = 0 or state = 1)
    and  material_category_id = #{materialCategoryId}
    </select>

</mapper>
