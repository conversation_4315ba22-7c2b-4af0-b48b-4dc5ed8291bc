<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.temporary.dao.TemporaryResideDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.temporary.TemporaryResideEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="project_id" property="projectId" />
        <result column="source_id" property="sourceId" />
        <result column="state" property="state" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="renter_id" property="renterId" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        project_id, source_id, state, start_time, end_time, renter_id, remark
    </sql>

    <select id="findByCondition" resultType="cn.uone.bean.entity.business.temporary.TemporaryResideEntity">
        select s.*,u.real_name applicat,r.name renter,CONCAT(C.name,"-",D.name,"-",rs.code) AS houseName
        from t_temporary_reside s
        LEFT JOIN v_sys_user u ON s.create_by = u.id
        LEFT JOIN v_sys_renter r ON s.renter_id=r.id
        LEFT JOIN  t_res_source rs ON s.source_id=rs.id
        LEFT JOIN t_res_plan_partition D ON rs.partition_id = D.id
        LEFT JOIN t_res_project C ON rs.project_id = C.id
        where 1 = 1
        <if test="map.searchVo.projectId != null and map.searchVo.projectId != ''">
            AND s.project_id = #{map.searchVo.projectId}
        </if>
        <if test="map.searchVo.renter != null and map.searchVo.renter != ''">
            and r.name like  CONCAT('%', #{map.searchVo.renter}, '%')
        </if>
        <if test="map.searchVo.houseName != null and map.searchVo.houseName != ''">
            and ( C.name like  CONCAT('%', #{map.searchVo.houseName}, '%') or
            D.name like  CONCAT('%', #{map.searchVo.houseName}, '%') or
            rs.code like  CONCAT('%', #{map.searchVo.houseName}, '%') )
        </if>
        <if test="map.searchVo.state != null and map.searchVo.state != ''">
            AND s.state = #{map.searchVo.state}
        </if>
        <if test="map.searchVo.ids != null and map.searchVo.ids.size() > 0">
            AND s.id in
            <foreach collection="map.searchVo.ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by s.create_date desc
    </select>

    <select id="getLastOneBySourceId" resultType="cn.uone.bean.entity.business.temporary.TemporaryResideEntity">
        select s.*,u.real_name applicat,r.name renter,CONCAT(C.name,"-",D.name,"-",rs.code) AS houseName
        from t_temporary_reside s
        LEFT JOIN v_sys_user u ON s.create_by = u.id
        LEFT JOIN v_sys_renter r ON s.renter_id=r.id
        LEFT JOIN  t_res_source rs ON s.source_id=rs.id
        LEFT JOIN t_res_plan_partition D ON rs.partition_id = D.id
        LEFT JOIN t_res_project C ON rs.project_id = C.id
        where 1 = 1
        AND s.source_id = #{source_id}
        order by s.create_date desc
        LIMIT 1;
    </select>
</mapper>
