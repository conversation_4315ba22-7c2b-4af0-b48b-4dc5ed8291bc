<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.fixed.dao.AssetInventoryDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.fixed.AssetInventoryEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="inventory_code" property="inventoryCode" />
        <result column="inventory_name" property="inventoryName" />
        <result column="inventory_state" property="inventoryState" />
        <result column="handle_status" property="handleStatus" />
        <result column="inventory_type" property="inventoryType" />
        <result column="inventory_by" property="inventoryBy" />
        <result column="inventory_total" property="inventoryTotal" />
        <result column="inventory_checked" property="inventoryChecked" />
        <result column="inventory_unchecked" property="inventoryUnchecked" />
        <result column="inventory_surplus" property="inventorySurplus" />
        <result column="inventory_loss" property="inventoryLoss" />
        <result column="required_complete_time" property="requiredCompleteTime" />
        <result column="complete_time" property="completeTime" />
        <result column="project_id" property="projectId" />
        <result column="remark" property="remark" />
        <result column="query_property_name" property="queryPropertyName" />
        <result column="query_property_type_ids" property="queryPropertyTypeIds" />
        <result column="query_dept_ids" property="queryDeptIds" />
        <result column="query_asset_location_ids" property="queryAssetLocationIds" />
        <result column="query_member_names" property="queryMemberNames" />
        <result column="query_member_ids" property="queryMemberIds" />
        <result column="query_asset_state_values" property="queryAssetStateValues" />
        <result column="query_asset_state_names" property="queryAssetStateNames" />
        <result column="query_purchase_date_start" property="queryPurchaseDateStart" />
        <result column="query_purchase_date_end" property="queryPurchaseDateEnd" />
        <result column="company_id" property="companyId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        company_id,
        inventory_code,inventory_name,inventory_state,handle_status,inventory_type,inventory_by,inventory_total,
        inventory_checked,inventory_unchecked,inventory_surplus,inventory_loss,required_complete_time,complete_time,project_id,remark,
        query_property_name,query_property_type_ids,query_dept_ids,query_asset_location_ids,
        query_member_names,query_member_ids,query_asset_state_values,query_asset_state_names,
        query_purchase_date_start,query_purchase_date_end
    </sql>

    <select id="pageList" resultType="cn.uone.bean.entity.business.fixed.vo.AssetInventoryVo">
        SELECT s.*,u.real_name as createByUser,u2.real_name as inventoryByName
        FROM t_fixed_asset_inventory s
        LEFT JOIN v_sys_user u ON s.create_by=u.id
        LEFT JOIN v_sys_user u2 ON s.inventory_by=u2.id
        WHERE 1=1
        <if test="map.projectId != null and map.projectId != ''">
            AND s.project_id = #{map.projectId}
        </if>
        <if test="map.inventoryCode != null and map.inventoryCode != ''">
            AND s.inventory_code like "%"#{map.inventoryCode}"%"
        </if>
        <if test="map.inventoryName != null and map.inventoryName != ''">
            AND s.inventory_name like "%"#{map.inventoryName}"%"
        </if>
        <if test="map.inventoryState != null and map.inventoryState != ''">
            AND s.inventory_state = #{map.inventoryState}
        </if>
        <if test="map.handleStatus != null and map.handleStatus != ''">
            AND s.handle_status = #{map.handleStatus}
        </if>
        <if test="map.inventoryType != null and map.inventoryType != ''">
            AND s.inventory_type = #{map.inventoryType}
        </if>
        <if test="map.inventoryBy != null and map.inventoryBy != ''">
            AND s.inventory_by = #{map.inventoryBy}
        </if>
        order by s.create_date desc
    </select>

    <select id="getInventoryByMap" resultType="cn.uone.bean.entity.business.fixed.vo.AssetInventoryVo">
        SELECT s.*,u.real_name as createByUser,u2.real_name as inventoryByName
        FROM t_fixed_asset_inventory s
        LEFT JOIN v_sys_user u ON s.create_by=u.id
        LEFT JOIN v_sys_user u2 ON s.inventory_by=u2.id
        WHERE 1=1
        <if test="map.inventoryId != null and map.inventoryId != ''">
            AND s.id = #{map.inventoryId}
        </if>
        <if test="map.projectId != null and map.projectId != ''">
            AND s.project_id = #{map.projectId}
        </if>
        <if test="map.inventoryCode != null and map.inventoryCode != ''">
            AND s.inventory_code like "%"#{map.inventoryCode}"%"
        </if>
        <if test="map.inventoryName != null and map.inventoryName != ''">
            AND s.inventory_name like "%"#{map.inventoryName}"%"
        </if>
        <if test="map.inventoryState != null and map.inventoryState != ''">
            AND s.inventory_state = #{map.inventoryState}
        </if>
        <if test="map.handleStatus != null and map.handleStatus != ''">
            AND s.handle_status = #{map.handleStatus}
        </if>
        <if test="map.inventoryType != null and map.inventoryType != ''">
            AND s.inventory_type = #{map.inventoryType}
        </if>
        order by s.create_date desc
    </select>

</mapper>
