<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.fixed.dao.FixedStorageDetailDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.fixed.FixedStorageDetailEntity">
    <result column="id" property="id" />
    <result column="create_date" property="createDate" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="storage_id" property="storageId" />
        <result column="property_id" property="propertyId" />
        <result column="storage_state" property="storageState" />
        <result column="handle_status" property="handleStatus" />
        <result column="storage_by" property="storageBy" />
        <result column="project_id" property="projectId" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_by,
        update_date,
        storage_id, property_id, storage_state, handle_status, storage_by, project_id, remark
    </sql>

    <select id="getListByPage" resultType="cn.uone.bean.entity.business.fixed.FixedPropertyEntity">
        SELECT p.*,d.storage_state as storageStatus
        FROM t_fixed_storage_detail d
        LEFT JOIN t_fixed_property p ON d.property_id=p.id
        where 1=1
        <if test="entity.storageId !=null and entity.storageId !=''">
            and storage_id = #{entity.storageId}
        </if>
        ORDER BY d.create_date desc
    </select>

    <select id="checkByCode" resultType="java.lang.Integer">
    select count(*) from t_fixed_storage_detail
    where
    storage_id = #{id} and
    property_id = (select id FROM t_fixed_property where rfid_code = #{code}
    )
    </select>

    <update id="updateByCode">
    update t_fixed_storage_detail set
    storage_state = #{state},storage_by = #{storageBy}
    where
    storage_id = #{id} and
    property_id = (select id FROM t_fixed_property where rfid_code = #{code}
    )
    </update>

    <select id="getQuantity" resultType="cn.uone.bean.entity.business.fixed.vo.StorageStatisticsVo">
    SELECT
        COUNT(*) AS total,
        SUM(CASE WHEN storage_state = 1 THEN 1 ELSE 0 END) AS uncounted,
        SUM(CASE WHEN storage_state = 2 THEN 1 ELSE 0 END) AS haveCounted
    FROM t_fixed_storage_detail
    where
    storage_id = #{storageId}
    </select>

    <select id="getPropertyById" resultType="cn.uone.bean.entity.business.fixed.FixedPropertyEntity">
        SELECT p.*,d.storage_state as storageStatus
        FROM t_fixed_storage_detail d
        LEFT JOIN t_fixed_property p ON d.property_id=p.id
        WHERE 1=1
        and d.storage_id = #{storageId}
        order by d.create_date desc
    </select>



</mapper>
