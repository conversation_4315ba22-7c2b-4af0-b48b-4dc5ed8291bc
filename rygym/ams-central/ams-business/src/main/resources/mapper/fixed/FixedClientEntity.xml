<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.fixed.dao.FixedClientDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.fixed.FixedClientEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="create_date" property="createDate" />
        <result column="client_name" property="clientName" />
        <result column="contacts" property="contacts" />
        <result column="phone" property="phone" />
        <result column="address" property="address" />
        <result column="remark" property="remark" />
        <result column="company_id" property="companyId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        update_date,
        company_id,client_name, contacts, phone, address, remark, create_date
    </sql>



</mapper>
