package cn.uone.business.kingdee.service;

import cn.uone.bean.entity.business.kingdee.KingdeeReceiptEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptSearchVo;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
public interface IKingdeeReceiptService extends IService<KingdeeReceiptEntity> {
    IPage<KingdeeReceiptEntity> findByCondition(Page page, KingdeeReceiptSearchVo kingdeeReceiptVo);
    KingdeeReceiptVo selectVoById(String id);
    List<KingdeeReceiptVo> getUnionTransfersByArriveTime(String arriveTime);
    List<KingdeeReceiptVo> selectVoByMap(Map<String,Object> map);
    List<KingdeeReceiptVo> getOrderConfirmByApplyTime(String applyTime);
    KingdeeReceiptVo getUnionTransferById(@Param("id") String id);
    KingdeeReceiptVo getOrderConfirmById(@Param("id") String id);
}
