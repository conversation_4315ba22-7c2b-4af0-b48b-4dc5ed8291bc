package cn.uone.business.base.service;

import cn.uone.application.enumerate.base.ChargeTypeEnum;
import cn.uone.bean.entity.business.base.BaseChargeItemEntity;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-10
 */
public interface IBaseChargeItemService extends IService<BaseChargeItemEntity> {

    BaseChargeItemEntity getByCondition(BaseChargeItemEntity entity);

    List<BaseChargeItemEntity> findByCondition(BaseChargeItemEntity entity);

    BaseChargeItemEntity getChargeItem(String projectId, String contractType, String isOrg, ChargeTypeEnum type) throws BusinessException;

}
