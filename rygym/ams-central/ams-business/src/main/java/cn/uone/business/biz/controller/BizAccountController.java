package cn.uone.business.biz.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.bean.entity.business.base.BaseBankEntity;
import cn.uone.bean.entity.business.biz.BizAccountEntity;
import cn.uone.bean.entity.business.invoice.vo.InvoiceBuyerVo;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.base.service.IBaseBankService;
import cn.uone.business.biz.service.IBizAccountService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-04
 */
@RestController
@RequestMapping("/biz/account")
public class BizAccountController extends BaseController {

    @Autowired
    private IBizAccountService bizAccountService;
    @Autowired
    private IBaseBankService baseBankService;
    @Autowired
    private IRenterFegin iRenterFegin;

    @ApiOperation("添加银行卡账号")
    @RequestMapping(value = "/saveOrUpdate",method = RequestMethod.POST)
    @UonePermissions(value = LoginType.CUSTOM)
    @CacheLock(prefix = "saveOrUpdate", expire = 30)
    public RestResponse saveOrUpdate(@Validated BizAccountEntity account, BindingResult bindingResult,MultipartFile file) throws Exception {
        if(bindingResult.hasErrors()){
            return RestResponse.failure(bindingResult.getFieldError().getDefaultMessage());
        }
        bizAccountService.addOrUpdateAccount(account,file);
        return RestResponse.success().setData("操作成功");
    }

    @ApiOperation("个人中心添加银行卡号")
    @RequestMapping(value = "/addBankCard",method = RequestMethod.POST)
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse addBankCard(String bankId,String bankCard,String userName,String accountId,String bankNumber ,String bankName){
        BaseBankEntity bb = baseBankService.getById(bankId);
        bb.setCode(bankNumber);
        bb.setName(bankName);
        return RestResponse.success().setData(bizAccountService.addBankCard(bb,bankCard,userName,accountId));
    }

    @ApiOperation("个人中心获取银行卡信息")
    @RequestMapping(value = "/getBankCard",method = RequestMethod.GET)
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse getBankCard(String userName){
        return RestResponse.success().setData(bizAccountService.getBankCard(userName));
    }

    @ApiOperation("查询银行卡号详情")
    @RequestMapping(value = "/detail",method = RequestMethod.GET)
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse detail(String id){
        return RestResponse.success().setData(bizAccountService.detail(id));
    }

    @ApiOperation("租客查看账号")
    @RequestMapping(value = "/list")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse list() throws Exception {
        String renterId = UoneSysUser.id();
        if(StrUtil.isEmpty(renterId)){
            throw new BusinessException("查找不到租客信息");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("isDel",BaseConstants.BOOLEAN_OF_FALSE);
        map.put("renterId",renterId);
        List<BizAccountEntity> list = bizAccountService.queryList(map);
//        QueryWrapper<BizAccountEntity> query = new QueryWrapper<>();
//        query.eq("is_del",BaseConstants.BOOLEAN_OF_FALSE).eq("renter_id",renterId).orderByDesc("update_date");
//        List<BizAccountEntity> list = bizAccountService.list(query);
        return RestResponse.success().setData(list);
    }

    @ApiOperation("租客删除账号")
    @RequestMapping(value = "/delete")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse delete(String id){
        bizAccountService.delete(id);
        return RestResponse.success().setData("删除成功");
    }

    @ApiOperation("通过银行卡id查询银行卡类型名称")
    @RequestMapping(value = "/getBankName")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse getBankName(String bankId){
        return RestResponse.success().setData(baseBankService.getById(bankId));
    }

    @ApiOperation("获取开票的购方信息")
    @RequestMapping(value = "/getBuyerByRenterId",method = RequestMethod.GET)
    public RestResponse getBuyerByRenterId(String renterId) throws Exception {
        RenterEntity renter = iRenterFegin.getById(renterId);
        BizAccountEntity bizAccount = bizAccountService.getByRenterId(renter.getId());
        InvoiceBuyerVo vo = new InvoiceBuyerVo();
        vo.setBuyerTaxNo(renter.getIdNo());
        vo.setBuyerAddress(renter.getAddress());
        vo.setBuyerTelephone(renter.getTel());
        if(bizAccount != null){
            vo.setBuyerName(bizAccount.getName());
            vo.setBuyerBankName(bizAccount.getBankBranch());
            vo.setBuyerBankNumber(bizAccount.getBankCode());
        }
        return RestResponse.success().setData(vo);
    }

}
