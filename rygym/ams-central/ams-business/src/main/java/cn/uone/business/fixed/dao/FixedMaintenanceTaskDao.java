package cn.uone.business.fixed.dao;

import cn.uone.bean.entity.business.fixed.FixedMaintenanceTaskEntity;
import cn.uone.bean.entity.business.fixed.FixedMaintenanceTeamEntity;
import cn.uone.bean.entity.business.fixed.vo.FixedMaintenanceTaskVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 维保任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
public interface FixedMaintenanceTaskDao extends BaseMapper<FixedMaintenanceTaskEntity> {
    IPage<FixedMaintenanceTaskVo> queryByPage(Page page, @Param("map") Map<String,Object> map);
}
