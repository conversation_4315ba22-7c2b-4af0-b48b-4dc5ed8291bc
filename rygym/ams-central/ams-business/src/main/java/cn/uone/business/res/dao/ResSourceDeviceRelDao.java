package cn.uone.business.res.dao;

import cn.uone.bean.entity.business.res.ResSourceDeviceRelEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceDeviceRelVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface ResSourceDeviceRelDao extends BaseMapper<ResSourceDeviceRelEntity> {

    // 获取设备信息
    List<ResSourceDeviceRelVo> getDevice(@Param("sourceId") String sourceId, @Param("isPublic") String isPublic);

    /**
     * 查询智能设备
     *
     * @param sourceId
     * @return
     */
    List<ResSourceDeviceRelVo> selectWatchDeviceBySourceID(@Param("sourceId") String sourceId);

    /**
     * 查询智能设备
     *
     * @param map
     * @return
     */
    List<ResSourceDeviceRelVo> queryDevice(@Param("map") Map<String,Object> map);

    Map<String,Long> havingDevice(@Param("sourceId") String sourceId);
}
