package cn.uone.business.bil.service.impl;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.business.bil.dao.BilRefundConfirmDao;
import cn.uone.business.bil.service.IBilRefundConfirmService;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class BilRefundConfirmServiceImpl extends ServiceImpl<BilRefundConfirmDao, BilOrderEntity> implements IBilRefundConfirmService {

    @Override
    public IPage<BilOrderVo> findByCondition(Page page, BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo", bilOrderSearchVo);
        DataScope dataScope = new DataScope(UoneSysUser.id());
        dataScope.setProAlias("s");
        dataScope.setProjectFieldName("project_id");
        return baseMapper.selectBilOrderByMap(page, map, dataScope);
    }

}
