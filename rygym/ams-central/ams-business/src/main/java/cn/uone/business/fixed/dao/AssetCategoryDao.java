package cn.uone.business.fixed.dao;

import cn.uone.bean.entity.business.fixed.AssetCategoryEntity;
import cn.uone.bean.entity.crm.RgDeptEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 固定资产分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
public interface AssetCategoryDao extends BaseMapper<AssetCategoryEntity> {

    List<Map<String,String>> getAllDept(String id);

}
