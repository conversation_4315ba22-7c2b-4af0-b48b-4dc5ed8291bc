package cn.uone.business.applyCard.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.applyCard.ApplyCardEfficientEntity;
import cn.uone.bean.entity.business.applyCard.ApplyCardEntity;
import cn.uone.bean.entity.business.cont.ContCheckInUserEntity;
import cn.uone.bean.entity.business.res.vo.SelectVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.business.applyCard.service.IApplyCardEfficientService;
import cn.uone.business.applyCard.service.IApplyCardService;
import cn.uone.business.cont.service.IContCheckInUserService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.util.ZipUtils;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@RestController
@RequestMapping("/apply/card")
public class ApplyCardController extends BaseController {

    @Autowired
    private IApplyCardService service;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private IResProjectService projectService;
    @Resource
    private IContCheckInUserService checkInUserService;
    @Autowired
    private IApplyCardEfficientService efficientService;

    @PostMapping("/saveForLogout")
    public RestResponse saveForLogout(@RequestBody ApplyCardEntity entity){
        if(entity.getEfficientEntities() == null){
            return RestResponse.failure("请选择注销人");
        }
        List<ContCheckInUserEntity> checkInUsers = new ArrayList<>();
        List<ApplyCardEfficientEntity> entities = entity.getEfficientEntities();
        for(ApplyCardEfficientEntity e:entities){
            ContCheckInUserEntity check = new ContCheckInUserEntity();
            check.setName(e.getName());
            check.setSex("男".equals(e.getGender())?"1":"2");
            check.setType("10");
            check.setDataFrom("1");
            check.setIdType("1");
            check.setCheckInDate(new Date());
            check.setTel(e.getTel());
            check.setIdNo(e.getIdentityCard());
            check.setState("99");
            check.setContractSourceId(entity.getSourceId());
            checkInUserService.save(check);
            checkInUsers.add(check);
        }
        entity.setCheckInUsers(checkInUsers);
        return save2(entity);
    }


    @PostMapping("/delTenement")
    public RestResponse delTenement(@RequestParam("ids")List<String> ids){
        checkInUserService.changeStateByIds(ids);
        return RestResponse.success();
    }

    @RequestMapping("/projectsAll")
    @ApiOperation("所有项目")
    public RestResponse projectsAll(){
        Map map= Maps.newHashMap();
        List<SelectVo> list=projectService.getAllProject(map);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/partitionAll")
    @ApiOperation("项目下所有楼栋")
    public RestResponse partitionAll(@RequestParam(value = "projectId" ,required = false)String projectId){
        Map map= Maps.newHashMap();
        map.put("projectId",projectId);
        List<SelectVo> list=projectService.getAllPartition(map);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/sourceAll")
    @ApiOperation("楼栋下所有房源")
    public RestResponse sourceAll(@RequestParam(value = "partitionId" ,required = false)String partitionId){
        Map map= Maps.newHashMap();
        map.put("partitionId",partitionId);
        List<SelectVo> list=projectService.getAllSource(map);
        return RestResponse.success().setData(list);
    }

    @PostMapping("/save2")
    public RestResponse save2(@RequestBody ApplyCardEntity entity){
        if(entity.getCheckInUsers() == null){
            return RestResponse.failure("请选择办卡人");
        }
        long timestamp = System.currentTimeMillis();
        String linkId = "AC_" + timestamp;
        return saveApplyCard(linkId,entity);
    }

    @PostMapping("/amend")
    @Transactional
    public RestResponse amend(@RequestBody ApplyCardEntity entity){
        if(entity.getCheckInUsers() == null){
            return RestResponse.failure("请选择办卡人");
        }
        String linkId = entity.getLinkId();
        QueryWrapper<ApplyCardEntity> query = new QueryWrapper<>();
        if(StrUtil.isNotBlank(entity.getLinkId())){
            query.eq("link_id",entity.getLinkId());
        }
        if(service.remove(query)){
            return  saveApplyCard(linkId,entity);
        }
        return RestResponse.failure("修改失败，请联系管理员");
    }


    public RestResponse saveApplyCard(String linkId,ApplyCardEntity entity){
        List<ContCheckInUserEntity> checkInUsers = entity.getCheckInUsers();
        if("1".equals(entity.getCause())){
            int oldAmount = service.getQuantity(entity.getSourceId()) == null?0:service.getQuantity(entity.getSourceId());
            int addAmount = checkInUsers.size();
            if(oldAmount+addAmount>6){
                return RestResponse.failure("办卡数量超过限制");
            }
        }
        for (ContCheckInUserEntity c:checkInUsers) {
            if(isExistAudit(c,entity)){
                return RestResponse.failure(c.getName()+"已申请，请等待审核，勿重复提交!");
            }
            if(!isExist(c,entity)&&!"1".equals(entity.getCause())){
                return RestResponse.failure("该房源无"+c.getName()+"的住宅卡信息，请选择新办！");
            }
            if(isExist(c,entity)&&"1".equals(entity.getCause())){
                return RestResponse.failure("该房源已存在"+c.getName()+"的住宅卡信息，无法新办，请选择其他类型");
            }
            ApplyCardEntity ac = new ApplyCardEntity();
            ac.setProjectName(entity.getProjectName());
            ac.setPartitionName(entity.getPartitionName());
            ac.setPropertyOwner(entity.getPropertyOwner());
            ac.setPropertyOwnerTel(entity.getPropertyOwnerTel());
            ac.setPropertyOwnerCard(entity.getPropertyOwnerCard());
            ac.setLeaser(entity.getLeaser());
            ac.setRenter(entity.getRenter());
            ac.setCode(entity.getCode());
            ac.setCause(entity.getCause());
            ac.setSource(entity.getProjectName()+"-"+entity.getPartitionName()+"-"+entity.getCode());
            ac.setProjectId(entity.getProjectId());
            ac.setPartitionId(entity.getPartitionId());
            ac.setSourceId(entity.getSourceId());
            ac.setDeclareTime(new Date());
            ac.setSourceType(entity.getSourceType());
            ac.setStatus("1");
            ac.setCreateBy(entity.getCreateBy());
            ac.setApplicantId(c.getId());
            ac.setLinkId(linkId);
            if(!"1".equals(entity.getCause())){
               ac.setStudentId(getOldCardNumber(c,entity));
            }
            ac.insert();
        }
        return RestResponse.success().setData(linkId);
    }

    /**
     * 获取原卡号
     * @param entity
     * @return
     */
    public String  getOldCardNumber(ContCheckInUserEntity check, ApplyCardEntity entity){
        QueryWrapper<ApplyCardEfficientEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("source_id",entity.getSourceId());
        entityQueryWrapper.eq("name",check.getName());
        List<ApplyCardEfficientEntity> list = efficientService.list(entityQueryWrapper);
        if(list.size()>0){
            System.out.println("办卡房源人名匹配到多个："+entity.getSourceId());
        }else if(list.size()==0){
            System.out.println("办卡房源人名未匹配到："+entity.getSourceId());
            return "";
        }
        return list.get(0).getStudentId();
    }
    @RequestMapping("/isModifiable")
    public RestResponse isModifiable( ApplyCardEntity entity){
        QueryWrapper<ApplyCardEntity> query = new QueryWrapper<>();
        if(StrUtil.isNotBlank(entity.getLinkId())){
            query.eq("link_id",entity.getLinkId());
        }
        if(StrUtil.isNotBlank(entity.getLinkId())){
            query.ne("status","1");
        }
        List<ApplyCardEntity> list = service.list(query);
        return RestResponse.success().setData(list.size());
    }
    //判断是否存在住宅表信息
    public boolean isExist(ContCheckInUserEntity check,ApplyCardEntity entity){
        ApplyCardEfficientEntity  efficientEntity = efficientService.getOne(new QueryWrapper<ApplyCardEfficientEntity>()
                .eq("name",check.getName())
                .eq("project_name",entity.getProjectName())
                .eq("partition_name",entity.getPartitionName())
                .eq("source_code",entity.getCode()));
        if(efficientEntity!=null){
            return true;
        }else{
            return false;
        }
    }

    //判断是否已提交申请待审核的数据
    public boolean isExistAudit(ContCheckInUserEntity c,ApplyCardEntity e){
//        QueryWrapper<ApplyCardEntity> q = new QueryWrapper<>();
//        q.eq("applicant_id",c.get());
//        q.eq("status","1");
        ApplyCardEntity entity = new ApplyCardEntity();
        entity.setApplicant(c.getName());
        entity.setSourceId(e.getSourceId());
        entity.setStatus("1");
        List<ApplyCardEntity> list2 = service.getListByExcel(entity);
        if(list2.size()>0){
            return true;
        }else{
            return false;
        }
    }

    @GetMapping("/getList")
    public RestResponse getList(Page<ApplyCardEntity> page, ApplyCardEntity entity){
        IPage<ApplyCardEntity> p = service.getListByPage(page,entity);
        List<ApplyCardEntity> list = p.getRecords();
        for (ApplyCardEntity v:list) {
            List<SysFileEntity> file = sysFileService.getListByFromIdAndType(v.getApplicantId(), SysFileTypeEnum.getEnumByValue("79"));
            if(file != null &&  file.size() > 0){
                v.setImgUrl(file.get(0).getPath());
            }
        }

        return RestResponse.success().setData(p);
    }

    @GetMapping("/page")
    @UonePermissions
    public RestResponse page(Page<ApplyCardEntity> page, ApplyCardEntity entity){
        IPage<ApplyCardEntity> p = service.getListByPage(page,entity);
        return RestResponse.success().setData(p);
    }


    @GetMapping("/getByLinkId")
    @UonePermissions
    public RestResponse getByLinkId(ApplyCardEntity entity){
        QueryWrapper<ApplyCardEntity> query = new QueryWrapper<>();
        if(StrUtil.isNotBlank(entity.getLinkId())){
            query.eq("link_id",entity.getLinkId());
        }
        List<ApplyCardEntity> list = service.list(query);
        for (ApplyCardEntity v:list) {
            List<SysFileEntity> file = sysFileService.getListByFromIdAndType(v.getApplicantId(), SysFileTypeEnum.getEnumByValue("79"));
            v.setHeadImages(file);
            List<SysFileEntity> idCardImages = sysFileService.getListByFromIdAndType(v.getApplicantId(), SysFileTypeEnum.getEnumByValue("39"));
            v.setIdCardImages(idCardImages);
            List<SysFileEntity> titleDeedImages = sysFileService.getListByFromIdAndType(v.getLinkId(), SysFileTypeEnum.getEnumByValue("34"));
            v.setTitleDeedImages(titleDeedImages);
            List<SysFileEntity> contractImages = sysFileService.getListByFromIdAndType(v.getLinkId(), SysFileTypeEnum.getEnumByValue("80"));
            v.setContractImages(contractImages);
            List<SysFileEntity> familyImages = sysFileService.getListByFromIdAndType(v.getLinkId(), SysFileTypeEnum.getEnumByValue("81"));
            v.setFamilyImages(familyImages);
        }
        return RestResponse.success().setData(list);
    }

    @GetMapping("/getDetailsById")
    public RestResponse getDetailsById(String id){
        ApplyCardEntity entity = service.getById(id);
        List<SysFileEntity> file = sysFileService.getListByFromIdAndType(entity.getApplicantId(), SysFileTypeEnum.getEnumByValue("79"));
        entity.setHeadImages(file);
        List<SysFileEntity> idCardImages = sysFileService.getListByFromIdAndType(entity.getApplicantId(), SysFileTypeEnum.getEnumByValue("39"));
        entity.setIdCardImages(idCardImages);
        List<SysFileEntity> titleDeedImages = sysFileService.getListByFromIdAndType(entity.getLinkId(), SysFileTypeEnum.getEnumByValue("34"));
        entity.setTitleDeedImages(titleDeedImages);
        List<SysFileEntity> contractImages = sysFileService.getListByFromIdAndType(entity.getLinkId(), SysFileTypeEnum.getEnumByValue("80"));
        entity.setContractImages(contractImages);
        List<SysFileEntity> familyImages = sysFileService.getListByFromIdAndType(entity.getLinkId(), SysFileTypeEnum.getEnumByValue("81"));
        entity.setFamilyImages(familyImages);
        return RestResponse.success().setData(entity);
    }


    @Transactional
    @PostMapping("/audit")
    public RestResponse audit(ApplyCardEntity auditEntity){

        ApplyCardEntity entity = service.getById(auditEntity.getId());
        entity.setStatus(auditEntity.getStatus());
        entity.setRemark(auditEntity.getRemark());
        entity.setExpiryDate(auditEntity.getExpiryDate());
        entity.setAuditTime(new Date());
        //同步生效表
        if("2".equals(auditEntity.getStatus())){
            ApplyCardEfficientEntity efficientEntity = null;
            ContCheckInUserEntity  check =  checkInUserService.getById(entity.getApplicantId());
            if("1".equals(entity.getCause())){
                if(isExist(check,entity)){
                    return RestResponse.failure("该房源已存在"+check.getName()+"的住宅卡信息，无法新办，请勿审核通过");
                }
                //新办
                efficientEntity = new ApplyCardEfficientEntity();
                efficientEntity.setStudentId(entity.getStudentId());//学（工）号
                efficientEntity.setName(check.getName());//姓名
                efficientEntity.setApplicantId(check.getId());//办卡人Id
                efficientEntity.setGender("1".equals(check.getSex())?"男":"女");//性别
                efficientEntity.setIdentityCard(check.getIdNo());//证件号
                efficientEntity.setTel(check.getTel());
                efficientEntity.setType("6类卡");//卡类别
                efficientEntity.setUseState("正常卡");//使用状态
                efficientEntity.setExpiryDate(entity.getExpiryDate());//失效期
                efficientEntity.setMainOrAssistant("主卡");//主副卡标志
                efficientEntity.setDept("住宅卡");
                efficientEntity.setProjectName(entity.getProjectName());//片区
                efficientEntity.setProjectId(entity.getProjectId());//片区id
                efficientEntity.setPartitionName(entity.getPartitionName());//楼栋
                efficientEntity.setPartitionId(entity.getPartitionId());//楼栋id
                efficientEntity.setSourceCode(entity.getCode());//房间号
                efficientEntity.setSourceId(entity.getSourceId());//房源Id
                efficientEntity.insert();
                entity.updateById();
            }else if("2".equals(entity.getCause())){
                //补卡
                efficientEntity = efficientService.getOne(new QueryWrapper<ApplyCardEfficientEntity>()
                        .eq("name",check.getName())
                        .eq("project_name",entity.getProjectName())
                        .eq("partition_name",entity.getPartitionName())
                        .eq("source_code",entity.getCode()));
                if(efficientEntity!=null){
                    efficientEntity.setExpiryDate(entity.getExpiryDate());
//                    efficientEntity.setUseState("挂失卡");
                    efficientEntity.setGender("1".equals(check.getSex())?"男":"女");//性别
                    efficientEntity.setIdentityCard(check.getIdNo());//证件号
                    efficientEntity.setTel(check.getTel());
                    efficientEntity.updateById();
                    entity.updateById();
                }else{
                    return RestResponse.failure("未找到"+entity.getProjectName()+"-"+entity.getPartitionName()+"-"+entity.getCode()+":"+check.getName()+"的住宅卡信息,请勿审核通过");
                }
            }else if("3".equals(entity.getCause())){
                //注销
                efficientEntity = efficientService.getOne(new QueryWrapper<ApplyCardEfficientEntity>()
                        .eq("name",check.getName())
                        .eq("project_name",entity.getProjectName())
                        .eq("partition_name",entity.getPartitionName())
                        .eq("source_code",entity.getCode()));
                if(efficientEntity!=null){
                    entity.updateById();
                    efficientService.removeById(efficientEntity.getId());
                }else{
                    return RestResponse.failure("未找到"+entity.getProjectName()+"-"+entity.getPartitionName()+"-"+entity.getCode()+":"+check.getName()+"的住宅卡信息,请勿审核通过");
                }
            }else if("4".equals(entity.getCause())){
                //延期
                efficientEntity = efficientService.getOne(new QueryWrapper<ApplyCardEfficientEntity>()
                        .eq("name",check.getName())
                        .eq("project_name",entity.getProjectName())
                        .eq("partition_name",entity.getPartitionName())
                        .eq("source_code",entity.getCode()));
                if(efficientEntity!=null){
                    efficientEntity.setExpiryDate(entity.getExpiryDate());
                    efficientEntity.setIdentityCard(check.getIdNo());//证件号
                    efficientEntity.setTel(check.getTel());
                    efficientEntity.setGender("1".equals(check.getSex())?"男":"女");//性别
                    efficientEntity.updateById();
                    entity.updateById();
                }else{
                    return RestResponse.failure("未找到"+entity.getProjectName()+"-"+entity.getPartitionName()+"-"+entity.getCode()+":"+check.getName()+"的住宅卡信息,请勿审核通过");
                }
            }

        }else{
            entity.updateById();
        }

        return RestResponse.success();
    }



    @RequestMapping("/export")
    public void export(HttpServletResponse response, ApplyCardEntity entity) throws Exception {
        List<ApplyCardEntity> list = service.getListByExcel(entity);
        String type = "其他";
        for (int i=0;i<list.size();i++) {
            ApplyCardEntity a = list.get(i);
            a.setSerialNumber(i+1);//序号
            if("1".equals(a.getCause())){
                a.setCause("新办");
                type = "新办";
            }else if("2".equals(a.getCause())){
                a.setCause("补办");
            }else  if("3".equals(a.getCause())){
                a.setCause("注销");
            }else if("4".equals(a.getCause())){
                a.setCause("延期");
            }
        }
        File excelFile;
        if("新办".equals(type)){
            excelFile = generateExcelFileForNew(list);
            String uuid = UUID.randomUUID().toString();
            String zipPath = uuid+".zip";
            File file = downLoadFile(zipPath, list,excelFile);
            response.reset();
            response.setContentType("application/octet-stream;charset=utf-8");
            response.setContentLength((int) file.length());
            String downloadName = "住宅卡申请信息.zip";
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(downloadName.getBytes("utf-8")));
            byte[] buff = new byte[1024];
            BufferedInputStream bis = null;
            OutputStream os ;
            try {
                os = response.getOutputStream();
                bis = new BufferedInputStream(new FileInputStream(file));
                int i = 0;
                while ((i = bis.read(buff)) != -1) {
                    os.write(buff, 0, i);
                    os.flush();
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if(ObjectUtil.isNotNull(bis)){
                        bis.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                // 清理临时文件
                if (excelFile.exists()) excelFile.delete();
                if (file.exists()) file.delete();
            }
        }else{
            excelFile = generateExcelFileForOther(list);
            response.reset();
            response.setContentType("application/octet-stream;charset=utf-8");
            response.setContentLength((int) excelFile.length());
            String downloadName = "住宅卡申请信息.zip";
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(downloadName.getBytes("utf-8")));
            byte[] buff = new byte[1024];
            BufferedInputStream bis = null;
            OutputStream os ;
            try {
                os = response.getOutputStream();
                bis = new BufferedInputStream(new FileInputStream(excelFile));
                int i = 0;
                while ((i = bis.read(buff)) != -1) {
                    os.write(buff, 0, i);
                    os.flush();
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if(ObjectUtil.isNotNull(bis)){
                        bis.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                // 清理临时文件
                if (excelFile.exists()) excelFile.delete();
            }
        }


    }

    private File generateExcelFileForNew(List<ApplyCardEntity> list) throws IOException {
        ClassPathResource templateResource = new ClassPathResource("/excel/export/新办卡.xlsx");
        if (!templateResource.exists()) {
            throw new RuntimeException("Excel 模板文件不存在");
        }

        try (InputStream is = templateResource.getInputStream();
             Workbook workbook = new XSSFWorkbook(is)) {

            Sheet sheet = workbook.getSheetAt(0);
            int startRow = 2;
            // 1. 更新标题日期
            // 查找并替换包含 "date" 的内容为当前时间的年月日
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                for (Cell cell : headerRow) {
                    if (cell.getCellType() == CellType.STRING) {
                        String cellValue = cell.getStringCellValue();
                        if (cellValue != null && cellValue.contains("title")) {
                            cell.setCellValue("校内住宅区常住人员申办校园出入卡_新办    "+sdf.format(new Date()));
                        }
                    }
                }
            }

            // 4. 填充数据行
            for (int i = 0; i < list.size(); i++) {
                ApplyCardEntity item = list.get(i);
                Row newRow = sheet.createRow(startRow + i);
                // 以 1/20 磅为单位设置行高
                newRow.setHeight((short) (30 * 20));

                for (int col = 0; col < 8; col++) {
                    Cell newCell = newRow.createCell(col);
                    // 设置单元格样式
                    CellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.cloneStyleFrom(createBaseCellStyle(workbook));
                    // 设置水平居中对齐
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    // 设置垂直居中对齐
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    newCell.setCellStyle(cellStyle);
                    // 填充数据
                    switch (col) {
                        case 0:
                            newCell.setCellValue(item.getSerialNumber());
                            break;
                        case 1:
                            newCell.setCellValue(item.getProjectName());
                            break;
                        case 2:
                            newCell.setCellValue(item.getPartitionName());
                            break;
                        case 3:
                            newCell.setCellValue(item.getCode());
                            break;
                        case 4:
                            newCell.setCellValue(item.getApplicant());
                            break;
                        case 5:
                            newCell.setCellValue(item.getTel()==null?"":item.getTel().replace("(已删)", ""));
                            break;
                        case 6:
                            newCell.setCellValue(item.getApplicantCard());
                            break;
                        case 7:
                            newCell.setCellValue(item.getExpiryDate());
                            break;
                        default:
                            throw new IllegalArgumentException("无效列索引：" + col);
                    }
                }
            }

            // 6. 生成输出文件
            File excelFile = File.createTempFile("新办卡(" + sdf.format(new Date())+")", ".xlsx");
            try (FileOutputStream fos = new FileOutputStream(excelFile)) {
                workbook.write(fos);
            }
            return excelFile;
        }
    }

    private File generateExcelFileForOther(List<ApplyCardEntity> list) throws IOException {
        ClassPathResource templateResource = new ClassPathResource("/excel/export/住宅卡补卡及注销.xlsx");
        if (!templateResource.exists()) {
            throw new RuntimeException("Excel 模板文件不存在");
        }

        try (InputStream is = templateResource.getInputStream();
             Workbook workbook = new XSSFWorkbook(is)) {

            Sheet sheet = workbook.getSheetAt(0);
            int startRow = 2;

            // 1. 更新标题日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                for (Cell cell : headerRow) {
                    if (cell.getCellType() == CellType.STRING) {
                        String cellValue = cell.getStringCellValue();
                        if (cellValue != null && cellValue.contains("title")) {
                            cell.setCellValue("住宅卡补卡及注销申请表    "+sdf.format(new Date()));
                        }
                    }
                }
            }


            // 4. 填充数据行
            for (int i = 0; i < list.size(); i++) {
                ApplyCardEntity item = list.get(i);
                Row newRow = sheet.createRow(startRow + i);
                // 以 1/20 磅为单位设置行高
                newRow.setHeight((short) (30 * 20));

                for (int col = 0; col < 10; col++) {
                    Cell newCell = newRow.createCell(col);
                    // 设置单元格样式
                    CellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.cloneStyleFrom(createBaseCellStyle(workbook));
                    // 设置水平居中对齐
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    // 设置垂直居中对齐
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    newCell.setCellStyle(cellStyle);
                    // 填充数据
                    switch (col) {
                        case 0:
                            newCell.setCellValue(item.getSerialNumber());
                            break;
                        case 1:
                            newCell.setCellValue(item.getCause());
                            break;
                        case 2:
                            newCell.setCellValue(item.getApplicant());
                            break;
                        case 3:
                            newCell.setCellValue(item.getStudentId());
                            break;
                        case 4:
                            newCell.setCellValue(item.getTel()==null?"":item.getTel().replace("(已删)", ""));
                            break;
                        case 5:
                            newCell.setCellValue(item.getApplicantCard());
                            break;
                        case 6:
                            newCell.setCellValue(item.getProjectName());
                            break;
                        case 7:
                            newCell.setCellValue(item.getPartitionName());
                            break;
                        case 8:
                            newCell.setCellValue(item.getCode());
                            break;
                        case 9:
                            newCell.setCellValue(item.getExpiryDate());
                            break;
                        default:
                            throw new IllegalArgumentException("无效列索引：" + col);
                    }
                }
            }


            // 6. 生成输出文件
            File excelFile = File.createTempFile("住宅卡补卡及注销申请表("+sdf.format(new Date())+")", ".xlsx");
            try (FileOutputStream fos = new FileOutputStream(excelFile)) {
                workbook.write(fos);
            }
            return excelFile;
        }
    }





    // 创建基础单元格样式（含边框）
    private CellStyle createBaseCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        // 设置细边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        // 设置边框颜色
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        return style;
    }

    public File downLoadFile(String zipPath,List<ApplyCardEntity> list,File excelFile) {
        File file = new File(zipPath);
        if (file.exists()) {
            file.delete();
        }
        byte[] buffer = new byte[10240];
        FileOutputStream fos = null;
        ZipOutputStream zos = null;
        try {
            fos = new FileOutputStream(zipPath);
            zos = new ZipOutputStream(fos);
            // 添加 Excel 文件到压缩包
            if (excelFile != null) {
                FileInputStream fis = new FileInputStream(excelFile);
                zos.putNextEntry(new ZipEntry(excelFile.getName()));
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    zos.write(buffer, 0, length);
                }
                zos.closeEntry();
                fis.close();
            }
            if(!CollectionUtils.isEmpty(list)){
                for (int y=0;y<list.size();y++) {
                    ApplyCardEntity a = list.get(y);
                    List<SysFileEntity> photoFile = sysFileService.getListByFromIdAndType(a.getApplicantId(), SysFileTypeEnum.BAREHEADED_PHOTO);//获取免冠照片
                    Map<String, Integer> map = new HashMap<>();
                    for (int i = 0; i < photoFile.size(); i++) {
                        SysFileEntity fileEntity = photoFile.get(i);
                        String url = fileEntity.getPath();
                        InputStream input = ZipUtils.getFileInputStreamUrl(url);
                        if(null != input) {
                            String extension = "jpg";
                            int lastIndex = fileEntity.getUrl().lastIndexOf('.');
                            if (lastIndex != -1) {
                                extension = fileEntity.getUrl().substring(lastIndex + 1);
                            }
                            String fileName = (y + 1) + "-" + a.getApplicant()+"."+extension;
                            //文件名称可能重复
                            Integer integer = map.get(fileName);
                            if(integer == null){
                                integer = 1;
                                map.put(fileName,integer);
                            }else{
                                integer = integer +1;
                                map.put(fileName,integer);
                            }
                            if(integer != 1){
                                int fileNameIndex =  integer - 1;
                                int index = fileName.lastIndexOf(".");
                                if(index >-1){
                                    fileName = fileName.substring(0,index)+"("+fileNameIndex+")"+fileName.substring(index);
                                }else{
                                    fileName +="("+fileNameIndex+")";
                                }
                            }
                            // 直接创建 ZipEntry，不包含文件夹路径
                            zos.putNextEntry(new ZipEntry(fileName));
                            int length;
                            while ((length = input.read(buffer)) > 0) {
                                zos.write(buffer, 0, length);
                            }
                            zos.closeEntry();
                            input.close();
                        }
                    }
                }
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                zos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return new File(zipPath);
    }



}
