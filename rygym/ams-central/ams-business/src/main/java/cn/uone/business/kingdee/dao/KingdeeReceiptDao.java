package cn.uone.business.kingdee.dao;

import cn.uone.bean.entity.business.kingdee.KingdeeReceiptEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Repository
public interface KingdeeReceiptDao extends BaseMapper<KingdeeReceiptEntity> {
    IPage<KingdeeReceiptEntity> selectKingdeeReceiptByMap(Page page, @Param("map") Map<String, Object> map, DataScope dataScope);
    KingdeeReceiptVo selectVoById(@Param("id") String id);
    List<KingdeeReceiptVo> getUnionTransfersByArriveTime(@Param("arriveTime") String arriveTime);
    List<KingdeeReceiptVo> selectVoByMap(@Param("map") Map<String, Object> map);
    List<KingdeeReceiptVo> getOrderConfirmByApplyTime(@Param("applyTime") String applyTime);
    KingdeeReceiptVo getUnionTransferById(@Param("id") String id);
    KingdeeReceiptVo getOrderConfirmById(@Param("id") String id);
}
