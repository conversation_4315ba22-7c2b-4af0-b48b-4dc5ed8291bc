package cn.uone.business.rpt.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.contract.ContractStateEnum;
import cn.uone.application.enumerate.contract.ContractTypeEnum;
import cn.uone.application.enumerate.contract.PayTypeEnum;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.bean.entity.business.base.BaseCarEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractInfoEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.rpt.RptRentAccountEntity;
import cn.uone.bean.entity.job.vo.AsyncResultVo;
import cn.uone.business.base.dao.BaseCarDao;
import cn.uone.business.bil.dao.BilOrderDao;
import cn.uone.business.cont.dao.ContContractInfoDao;
import cn.uone.business.cont.service.IContRentLadderService;
import cn.uone.business.res.dao.ResSourceDao;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Component
public class RentAccountTask {

    @Resource
    private ResSourceDao sourceDao;
    @Autowired
    private IContRentLadderService ladderService;

    @Resource
    private ContContractInfoDao contractInfoDao;

    @Resource
    private BilOrderDao orderDao;

    @Resource
    private BaseCarDao carDao;

    @Resource
    private RentObjUtil util;

    /***
     * 生成租金台账报表对象
     * @param c
     * @return
     */
    @Async
    public CompletableFuture<AsyncResultVo<RptRentAccountEntity>> getAccountByContract(ContContractEntity c, ContContractSourceRelEntity cs, Date startDate) {
        AsyncResultVo<RptRentAccountEntity> result = new AsyncResultVo<>();
        ResSourceVo source = sourceDao.getInfoById(cs.getSourceId());

        /* 合同信息处理 **/
        String contStateStr = ContractStateEnum.getNameByValue(c.getState());
        Date contStartDate = c.getStartDate();
        if (ContractStateEnum.STATUS_TAKE_EFFECT.getValue().equals(c.getState()) && DateUtil.beginOfMonth(contStartDate).getTime() == startDate.getTime()) {
            //已生效合同并且是本月签约的合同状态为新签
            contStateStr = "新签";
        }

        ContRentLadderEntity ladder = ladderService.getRentLadderByContSourceIdAndCreatDate(cs.getId(), startDate);//获取到合同租金
        BigDecimal realRent = ladder.getPrice();
        ContContractInfoEntity contractInfo = contractInfoDao.selectOne(new QueryWrapper<ContContractInfoEntity>().eq("contract_id", c.getId()));
        BigDecimal contSubsidy = BigDecimal.ZERO;
        RptRentAccountEntity entity = new RptRentAccountEntity();
        //基础信息数据
        entity.setSourceName(source.getHouseName())
                .setContractType(c.getContractType())
                .setIsOrganize(c.getIsOrganize())
                .setSourceCode(source.getCode())
                .setProjectId(source.getProjectId())
                .setPartitionId(source.getPartitionId())
                .setIsSubsidy(contractInfo.getIsSubsidy())
                .setReportDate(startDate)
                .setContractId(c.getId())
                .setSourceId(cs.getSourceId())
                .setContCode(c.getContractCode())
                .setContState(contStateStr)
                .setArea(source.getArea())
                .setContStartDate(c.getStartDate())
                .setContEndDate(c.getEndDate())
                .setPayType(PayTypeEnum.getNameByValue(c.getPayType()))
                .setRealRent(realRent);
        if (contractInfo != null) {
            if (null != contractInfo.getSubsidySum()) {
                contSubsidy = contractInfo.getSubsidySum();
            }
            entity.setSignerName(contractInfo.getName());
        }

        if (ContractTypeEnum.CARPORT.getValue().equals(c.getContractType())) {
            BaseCarEntity car = carDao.selectOne(new QueryWrapper<BaseCarEntity>().eq("contract_source_id", cs.getId()));
            if (null != car) {
                entity.setSignerName(entity.getSignerName() + "(" + car.getNum() + ")");
            }
        }
        entity.setRent(realRent.add(contSubsidy))//租金等于合同租金+补贴金额
                .setContSub(contSubsidy);
        //获取截至上期欠缴租金数据
        setOweLast(entity, c.getId(), cs.getSourceId(), startDate, contSubsidy);
        //获取本月实收租金数据
        setThisReal(entity, c.getId(), cs.getSourceId(), startDate, contSubsidy);
        //获取本月应收租金数据
        setThisMust(entity, c.getId(), cs.getSourceId(), startDate, contSubsidy);
        //获取退租情况
        setOutRent(entity, c.getId(), cs.getSourceId());
        //获取补缴情况
        setMakeUp(entity, c.getId(), cs.getSourceId(), startDate);

        //截至本月累计未收 = 截至上月欠缴+本月应收-本月实收
        BigDecimal oweThisRent = entity.getOweLastRent().add(entity.getThisMustRent()).subtract(entity.getThisRealRent());
        BigDecimal oweThisCusRent = entity.getOweLastCusRent().add(entity.getThisMustCusRent()).subtract(entity.getThisRealCusRent());
        BigDecimal oweThisSub = entity.getOweLastSub().add(entity.getThisMustSub()).subtract(entity.getThisRealSub());
        if (oweThisRent.compareTo(BigDecimal.ZERO) < 0) {
            oweThisRent = BigDecimal.ZERO;
        }
        if (oweThisCusRent.compareTo(BigDecimal.ZERO) < 0) {
            oweThisCusRent = BigDecimal.ZERO;
        }
        if (oweThisSub.compareTo(BigDecimal.ZERO) < 0) {
            oweThisSub = BigDecimal.ZERO;
        }
        entity.setOweThisRent(oweThisRent);
        entity.setOweThisCusRent(oweThisCusRent);
        entity.setOweThisSub(oweThisSub);

        //获取截至本月欠缴租金数据（补差）
        setOweThis(entity, c.getId(), cs.getSourceId(), startDate, contSubsidy);

        entity.setThisRealEndDate(util.getTealEndDate(c.getId(), cs.getSourceId()));
        result.setObj(entity);
        return CompletableFuture.completedFuture(result);
    }


    /**
     * 获取截至本月欠缴租金数据
     *
     * @param entity
     * @param contractId
     * @param sourceId
     * @param startDate
     * @param contSubsidy
     */
    private void setOweThis(RptRentAccountEntity entity, String contractId, String sourceId, Date startDate, BigDecimal contSubsidy) {
        BilOrderSearchVo search = new BilOrderSearchVo();
        search.setContractId(contractId);
        search.setSourceId(sourceId);
        List<String> orderTypes = Lists.newArrayList();
        orderTypes.add(OrderTypeEnum.RENT.getValue());
        orderTypes.add(OrderTypeEnum.CHECKOUTSHOU.getValue());
        orderTypes.add(OrderTypeEnum.HUANFANGFEE.getValue());
        search.setOrderTypes(orderTypes);// 账单类型为 租金与退房收款

        List<String> states = Lists.newArrayList();//支付状态为待支付，待退款
        states.add(PayStateEnum.NOPAY.getValue());
        states.add(PayStateEnum.REFUNDPENDING.getValue());
        search.setStates(states);

        search.setItemMonth(DateUtil.format(startDate, "yyyy-MM"));//子账单开始时间为本月

        Date lastMon = DateUtil.endOfDay(DateUtil.offsetDay(startDate, -1));
        search.setEndPayableTime(lastMon);// 账单生成结束日期为上月最后一天

        RentObjUtil.RentObj obj = util.getRentObj(search, contSubsidy);

        if (null != obj.getRent()) {
            entity.setOweThisRent(entity.getOweThisRent().add(obj.getRent()));
        }
        if (null != obj.getCusRent()) {
            entity.setOweThisCusRent(entity.getOweThisCusRent().add(obj.getCusRent()));
        }
        if (null != obj.getSub()) {
            entity.setOweThisSub(entity.getOweThisSub().add(obj.getSub()));
        }

        if(StrUtil.isNotBlank(obj.getRealPeriod())){
            String t = "";
            if(StrUtil.isNotBlank(entity.getOweThisPeriod())){
                t = entity.getOweThisPeriod()+",";
            }
            t+=obj.getRealPeriod();
            entity.setOweThisPeriod(t);
        }
    }

    /**
     * 获取截至上期欠缴租金数据
     *
     * @param entity
     * @param contractId
     * @param sourceId
     * @param startDate
     * @param contSubsidy
     */
    private void setOweLast(RptRentAccountEntity entity, String contractId, String sourceId, Date startDate, BigDecimal contSubsidy) {
        BilOrderSearchVo search = new BilOrderSearchVo();
        search.setContractId(contractId);
        search.setSourceId(sourceId);
        List<String> orderTypes = Lists.newArrayList();
        orderTypes.add(OrderTypeEnum.RENT.getValue());
        orderTypes.add(OrderTypeEnum.CHECKOUTSHOU.getValue());
        orderTypes.add(OrderTypeEnum.HUANFANGFEE.getValue());
        search.setOrderTypes(orderTypes);// 账单类型为 租金与退房收款
//        search.setState(PayStateEnum.NOPAY.getValue()); // 支付状态为待支付

        List<String> states = Lists.newArrayList();//支付状态为已支付，已退款
        states.add(PayStateEnum.NOPAY.getValue());
        states.add(PayStateEnum.REFUNDPENDING.getValue());
        search.setStates(states);
        Date lastMon = DateUtil.endOfDay(DateUtil.offsetDay(startDate, -1));
        search.setEndPayableTime(lastMon);// 账单应付结束日期为上月最后一天
        //search.setMaxItemStartDate(lastMon); //子账单开始时间小于上月最后一天
        RentObjUtil.RentObj obj = util.getRentObj(search, contSubsidy);
        BilOrderEntity partOrder= util.getPartOrder(search);
        BigDecimal notPay = partOrder.getPayment().subtract(partOrder.getActualPayment());

        entity.setOweLastRent(obj.getRent().add(notPay)).setOweLastSub(obj.getSub()).setOweLastCusRent(obj.getCusRent().add(notPay)).setOweThisPeriod(obj.getRealPeriod());

        //补贴账单获取
        BilOrderSearchVo search1 = new BilOrderSearchVo();
        search1.setContractId(contractId);
        search1.setSourceId(sourceId);
        search1.setState(PayStateEnum.NOPAY.getValue());
        search1.setOrderType(OrderTypeEnum.SUBSIDY.getValue());
        search1.setMaxItemStartDate(lastMon);
        List<BilOrderEntity> orders = orderDao.selectOrderIncludeItem(search1);
        BigDecimal sub = BigDecimal.ZERO;
        if(null!=orders&&orders.size()>0){
            for (BilOrderEntity o:orders){
                for (BilOrderItemEntity item :o.getItems()){
                    sub = sub.add(item.getPayment());
                }
            }
        }
        entity.setOweLastSub(sub);
        entity.setOweLastRent(entity.getOweLastCusRent().add(entity.getOweLastSub()));

    }

    private void setThisMust(RptRentAccountEntity entity, String contractId, String sourceId, Date startDate, BigDecimal contSubsidy) {
        BilOrderSearchVo search = new BilOrderSearchVo();
        search.setContractId(contractId);
        search.setSourceId(sourceId);
        List<String> orderTypes = Lists.newArrayList();
        orderTypes.add(OrderTypeEnum.RENT.getValue());
        orderTypes.add(OrderTypeEnum.CHECKOUTTUI.getValue());
        orderTypes.add(OrderTypeEnum.CHECKOUTSHOU.getValue());
        orderTypes.add(OrderTypeEnum.HUANFANGFEE.getValue());
        search.setOrderTypes(orderTypes);// 账单类型为 租金与退房收款
        search.setStartPayableTime(startDate); // 账单应付时间内为本月
        search.setEndPayableTime(DateUtil.endOfMonth(startDate));
        RentObjUtil.RentObj obj = util.getRentObj(search, contSubsidy);
        entity.setThisMustRent(obj.getRent()).setThisMustCusRent(obj.getCusRent());

        //取补贴账单
        BilOrderSearchVo search1 = new BilOrderSearchVo();
        search1.setContractId(contractId);
        search1.setSourceId(sourceId);
        search1.setOrderType(OrderTypeEnum.SUBSIDY.getValue());
        search1.setStartPayableTime(startDate); // 账单应付时间内为本月
        search1.setEndPayableTime(DateUtil.endOfMonth(startDate));
        List<BilOrderEntity> orders = orderDao.selectOrderIncludeItem(search1);
        BigDecimal sub = BigDecimal.ZERO;
        if(null!=orders&&orders.size()>0){
            for (BilOrderEntity o:orders){
                for (BilOrderItemEntity item :o.getItems()){
                    sub = sub.add(item.getPayment());
                }
            }
        }
        entity.setThisMustSub(obj.getSub());

        if (null != entity.getThisMustRent() && entity.getThisMustRent().compareTo(BigDecimal.ZERO) > 0 && entity.getThisMustRent().subtract(entity.getThisRealRent()).compareTo(BigDecimal.ZERO) != 0) {
            String p = entity.getOweThisPeriod();
            if (StrUtil.isNotBlank(p)) {
                p += "," + obj.getRealPeriod();
            } else {
                p = obj.getRealPeriod();
            }
            if (StrUtil.isNotBlank(p) && StrUtil.isNotBlank(entity.getThisRealPeriod())) {
                p = p.replace(entity.getThisRealPeriod(), "");

                if (p.lastIndexOf(",")!=-1&&p.lastIndexOf(",") == p.length() - 1) {
                    try {
                        p = p.substring(0, p.lastIndexOf(","));
                    }catch (Exception e){
                        e.printStackTrace();
                    }

                }
            }
            entity.setOweThisPeriod(p);
        }
    }

    private void setThisReal(RptRentAccountEntity entity, String contractId, String sourceId, Date startDate, BigDecimal contSubsidy) {
        BilOrderSearchVo search = new BilOrderSearchVo();
        search.setContractId(contractId);
        search.setSourceId(sourceId);

        List<String> orderTypes = Lists.newArrayList();
        orderTypes.add(OrderTypeEnum.RENT.getValue());
        orderTypes.add(OrderTypeEnum.CHECKOUTSHOU.getValue());
        orderTypes.add(OrderTypeEnum.HUANFANGFEE.getValue());
        search.setOrderTypes(orderTypes);// 账单类型为 租金与退房收款
        search.setStartPayTime(startDate); // 账单支付时间内为本月
        search.setEndPayTime(DateUtil.endOfMonth(startDate));

        List<String> states = Lists.newArrayList();//支付状态为已支付，已退款
        states.add(PayStateEnum.PAYCONFIR.getValue());
        states.add(PayStateEnum.REFUNDED.getValue());
        search.setStates(states);

        RentObjUtil.RentObj obj = util.getRentObj(search, contSubsidy);
        BilOrderEntity partOrder= util.getPartOrder(search);

        entity.setThisRealCaTime(obj.getArriveTimeStr());
        entity.setThisRealPayTime(obj.getPayTimeStr());
        List<BilOrderEntity> orders = orderDao.selectList(new QueryWrapper<BilOrderEntity>().eq("contract_id", contractId).eq("source_id", sourceId).
                eq("order_type", OrderTypeEnum.SUBSIDY.getValue()).eq("pay_state", PayStateEnum.PAYCONFIR.getValue()).
                le("pay_time",startDate).ge("pay_time",DateUtil.endOfMonth(startDate)));

        if(null!=orders&&orders.size()>0){
            BigDecimal sub = BigDecimal.ZERO;
            for (BilOrderEntity o:orders){
                sub = sub.add(o.getPayablePayment());
            }
            entity.setThisRealSub(sub);
        }else{
            entity.setThisRealSub(BigDecimal.ZERO);
        }

        BigDecimal cusRent = BigDecimal.ZERO;
        BigDecimal rent = BigDecimal.ZERO;
        if(null!=obj.getCusRent()){
            cusRent  = obj.getCusRent().subtract(obj.getDis());
            rent  = obj.getRent().subtract(obj.getDis());
            if(cusRent.compareTo(BigDecimal.ZERO)<0){
                cusRent = BigDecimal.ZERO;
                rent = BigDecimal.ZERO;
            }
        }
        obj.setCusRent(cusRent.add(partOrder.getActualPayment()));
        obj.setRent(rent.add(partOrder.getActualPayment()));

        entity.setThisRealRent(obj.getRent()).setThisRealCusRent(obj.getCusRent()).setThisRealDis(obj.getDis()).setThisRealPeriod(obj.getRealPeriod());
    }

    private void setOutRent(RptRentAccountEntity entity, String contractId, String sourceId) {
        BilOrderSearchVo search = new BilOrderSearchVo();
        search.setContractId(contractId);
        search.setSourceId(sourceId);

        List<String> orderTypes = Lists.newArrayList();
        orderTypes.add(OrderTypeEnum.CHECKOUTSHOU.getValue());
        orderTypes.add(OrderTypeEnum.CHECKOUTTUI.getValue());
        orderTypes.add(OrderTypeEnum.HUANFANGFEE.getValue());
        search.setOrderTypes(orderTypes);// 账单类型为 租金与退房收款
        search.setOrderItemType(OrderItemTypeEnum.RENT.getValue()); //子账单类型为 租金
        List<BilOrderEntity> orders = orderDao.selectOrderIncludeItem(search);
        RentObjUtil.OutObj obj = util.getOutObj(orders, contractId);
        entity.setOutCusRent(obj.getRent()).setOutStartTime(obj.getSDtae()).setOutEndTime(obj.getEDtae()).setRefundTime(obj.getPayTime()).setOutTime(obj.getCheckOutDate());
    }

    private void setMakeUp(RptRentAccountEntity entity, String contractId, String sourceId, Date startDate) {

        BilOrderSearchVo search = new BilOrderSearchVo();
        search.setContractId(contractId);
        search.setSourceId(sourceId);
        List<String> orderTypes = Lists.newArrayList();
        orderTypes.add(OrderTypeEnum.RENT.getValue());
        orderTypes.add(OrderTypeEnum.CHECKOUTSHOU.getValue());
        orderTypes.add(OrderTypeEnum.CHECKOUTTUI.getValue());
        orderTypes.add(OrderTypeEnum.HUANFANGFEE.getValue());
        search.setOrderTypes(orderTypes);// 账单类型为 租金与退房收款
        search.setOrderItemType(OrderItemTypeEnum.RENT.getValue()); //子账单类型为 租金
        search.setState(PayStateEnum.PAYCONFIR.getValue()); // 支付状态为待支付
        search.setItemEndDate(DateUtil.endOfDay(DateUtil.offsetDay(startDate, -1)));
//        search.setEndCreateDate(DateUtil.endOfDay(DateUtil.offsetDay(startDate,-1)));
        search.setStartPayTime(startDate);
        List<BilOrderEntity> orders = orderDao.selectOrderIncludeItem(search);
        BilOrderEntity partOrder= util.getPartOrder(search);
        // 租金
        BigDecimal rent = BigDecimal.ZERO;
        String period = "";
        if (null != orders && orders.size() > 0) {
            for (BilOrderEntity order : orders) {
                List<BilOrderItemEntity> items = order.getItems();
                if (order.getPayment().compareTo(BigDecimal.ZERO) < 0) {
                    continue;
                }
                //账单开始时间
                Date oStartTime = null;
                //账单结束时间
                Date oEndTime = null;
                if (null != items && items.size() > 0) {
                    for (BilOrderItemEntity item : items) {
                        rent = rent.add(item.getPayment());
                        if (null == oStartTime || oStartTime.getTime() > item.getStartTime().getTime()) {
                            oStartTime = item.getStartTime();
                        }
                        if (null == oEndTime || oEndTime.getTime() < item.getEndTime().getTime()) {
                            oEndTime = item.getEndTime();
                        }
                    }
                }
                String a = util.getPeriodStr(oStartTime, oEndTime);
                if (StrUtil.isBlank(period)) {
                    period = a;
                } else {
                    period += "," + a;
                }
            }
        }
        entity.setFillPriorRent(rent.add(partOrder.getActualPayment())).setFillPriorPeriod(period);
    }


}
