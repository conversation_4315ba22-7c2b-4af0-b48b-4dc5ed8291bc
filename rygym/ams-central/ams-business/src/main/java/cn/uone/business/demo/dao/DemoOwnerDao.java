package cn.uone.business.demo.dao;

import cn.uone.bean.entity.business.demo.DemoOwnerEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
public interface DemoOwnerDao extends BaseMapper<DemoOwnerEntity> {
    @Select("select name from t_res_project_owner where id=#{id}")
    String getNameById(@Param("id") String id);

    @Select("select * from t_res_project_owner")
    List<DemoOwnerEntity> queryName();
}
