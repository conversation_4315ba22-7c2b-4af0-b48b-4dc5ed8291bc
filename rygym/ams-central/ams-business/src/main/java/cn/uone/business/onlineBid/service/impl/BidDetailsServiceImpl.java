package cn.uone.business.onlineBid.service.impl;

import cn.uone.bean.entity.business.onlineBid.BidAfficheEntity;
import cn.uone.bean.entity.business.onlineBid.BidDetailsEntity;
import cn.uone.business.onlineBid.dao.BidDetailsDao;
import cn.uone.business.onlineBid.service.IBidDetailsService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 竞价详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Service
public class BidDetailsServiceImpl extends ServiceImpl<BidDetailsDao, BidDetailsEntity> implements IBidDetailsService {

    @Override
    public BidDetailsEntity getByApplyId(String applyId) {
        return baseMapper.getByApplyId(applyId);
    }

    @Override
    public List<BidDetailsEntity> getByBidId(String bidId) {
        return baseMapper.getByBidId(bidId);
    }

    @Override
    public BidDetailsEntity getWinByBidId(String bidId) {
        return baseMapper.getWinByBidId(bidId);
    }

    @Override
    public IPage<BidDetailsEntity> getListByPage(Page page, BidDetailsEntity entity) {
        return baseMapper.getListByPage(page,entity);
    }
}
