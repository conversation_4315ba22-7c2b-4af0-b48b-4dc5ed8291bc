package cn.uone.business.rpt.dao;

import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.bean.entity.business.report.ReportPutAccountEntity;
import cn.uone.bean.entity.business.report.vo.PutAccountVo;
import cn.uone.bean.parameter.PutAccountPo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 收费台账 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
@Repository
public interface ReportPutAccountDao extends BaseMapper<ReportPutAccountEntity> {

    /**
     * 查询租金账单
     *
     * @param orderTypes
     * @param startDate
     * @param endDate
     * @return
     */
    List<ReportPutAccountEntity> findPutAccountRentByMonth(@Param("orderTypes") List<String> orderTypes, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    /***
     * 查询退房账单
     * @param orderTypes
     * @param startDate
     * @param endDate
     * @return
     */
    List<ReportPutAccountEntity> findPutAccountOutByMonth(@Param("orderTypes") List<String> orderTypes, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    IPage<PutAccountVo> putAccountReport(Page page, @Param("po") PutAccountPo po);

    List<PutAccountVo> putAccountReport(@Param("po") PutAccountPo po);

    IPage<BilOrderVo> selectOrderPage(Page page, @Param("map") BilOrderSearchVo search);


    void delByMonth(@Param("month")String month);



}
