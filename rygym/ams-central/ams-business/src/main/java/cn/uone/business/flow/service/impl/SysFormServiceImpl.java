package cn.uone.business.flow.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.flow.SysFormEntity;
import cn.uone.business.flow.dao.SysFormDao;
import cn.uone.business.flow.service.ISysFormService;
import cn.uone.business.flow.util.DesignFormsVo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 流程表单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Service
public class SysFormServiceImpl extends ServiceImpl<SysFormDao, SysFormEntity> implements ISysFormService {
    @Override
    public IPage<SysFormEntity> page(Page page, SysFormEntity entity) {
        QueryWrapper<SysFormEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getFormName())) {
            queryWrapper.like("sys_form.form_name", entity.getFormName());
        }
        if (ObjectUtil.isNotEmpty(entity.getRemark())) {
            queryWrapper.like("sys_form.remark", entity.getRemark());
        }
//        queryWrapper.orderByDesc("sys_form.create_date");
        IPage iPage =  baseMapper.selectPage(page, queryWrapper);

        return iPage;
    }

    @Override
    public List<SysFormEntity> list(SysFormEntity entity) {
        return baseMapper.selectList(new QueryWrapper<>());
    }

    /**
     * 表单赋值
     * @param map 表单
     * @param mapPram 参数
     * @return
     */
    public Map<String,Object> formSet(Map<String,Object> map,Map<String,Object> mapPram){
        System.out.println(JSON.toJSONString(map));
        DesignFormsVo formsVo = JSON.parseObject(JSON.toJSONString(map),DesignFormsVo.class);
        formsVo.setDisabled(true);
        formsVo.setFormBtns(false);
        for(DesignFormsVo.Fields fields : formsVo.getFields()){
            DesignFormsVo.Config config = fields.getConfig();
            for(String a : mapPram.keySet()){
                if( fields.getVModel().equals(a)){
                    config.setDefaultValue(ObjectUtil.isNotEmpty(mapPram.get(a))?mapPram.get(a).toString():"");
                }
            }
        }
        return JSON.parseObject(JSON.toJSONString(formsVo), Map.class);


    }
}
