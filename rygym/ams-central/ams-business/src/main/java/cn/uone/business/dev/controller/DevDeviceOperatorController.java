package cn.uone.business.dev.controller;


import cn.uone.bean.entity.business.dev.DevClassEntity;
import cn.uone.bean.entity.business.dev.DevDeviceEntity;
import cn.uone.bean.entity.business.dev.DevSupplierEntity;
import cn.uone.bean.entity.business.dev.vo.DevDeviceEntityVo;
import cn.uone.bean.entity.business.dev.vo.PurDevDeviceImportVo;
import cn.uone.bean.entity.business.pur.PurPurchaseDetailEntity;
import cn.uone.bean.entity.business.pur.PurPurchaseEntity;
import cn.uone.bean.entity.business.pur.vo.PurPurchaseDetailEntityVo;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.bean.entity.business.res.ResSourceDeviceRelEntity;
import cn.uone.business.dev.service.IDevClassService;
import cn.uone.business.dev.service.IDevDeviceService;
import cn.uone.business.dev.service.IDevSupplierService;
import cn.uone.business.pur.service.IPurPurchaseDetailService;
import cn.uone.business.pur.service.IPurPurchaseService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceDeviceRelService;
import cn.uone.fegin.bus.IDeviceOperatorFegin;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.CacheParam;
import cn.uone.web.util.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Api(value="云丁设备操作",tags={"设备操作"})
@RestController
@RequestMapping("/dev/operator")
public class DevDeviceOperatorController extends BaseController implements IDeviceOperatorFegin {

    @Autowired
    private IDevDeviceService devDeviceService;

    @Autowired
    private IResProjectParaService resProjectParaService;

    @Override
    @RequestMapping("/getDeviceByCode")
    public DevDeviceEntity getDeviceByCode(String code) {
        return devDeviceService.getByCode(code);
    }

    @Override
    @RequestMapping("/getByCodeAndProjectId")
    public ResProjectParaEntity getByCodeAndProjectId(String code, String projectId) {
        return resProjectParaService.getByCodeAndProjectId(code,projectId);
    }
}
