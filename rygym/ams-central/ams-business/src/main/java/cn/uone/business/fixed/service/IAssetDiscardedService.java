package cn.uone.business.fixed.service;

import cn.uone.bean.entity.business.fixed.AssetDiscardedEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 固定资产报废原因表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
public interface IAssetDiscardedService extends IService<AssetDiscardedEntity> {

    IPage<AssetDiscardedEntity> page(Page page, AssetDiscardedEntity entity);

    List<AssetDiscardedEntity> selectList(AssetDiscardedEntity entity);
}
