package cn.uone.business.pbls.dao;

import cn.uone.bean.entity.business.pbls.ParkingSpaceEntity;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
public interface ParkingSpaceDao extends BaseMapper<ParkingSpaceEntity> {

    IPage<ParkingSpaceEntity> selectSpaceByMap(Page page, DataScope dataScope, @Param("map")Map<String, Object> map);

    String getAreaIdByName(String name);
}
