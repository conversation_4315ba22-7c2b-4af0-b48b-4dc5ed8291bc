package cn.uone.business.res.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.contract.ContractTempletTypeEnum;
import cn.uone.application.enumerate.source.SourceTypeEnum;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.res.ResCostConfigureDetailEntity;
import cn.uone.bean.entity.business.res.ResCostConfigureEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.sale.SaleDemandEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContTempService;
import cn.uone.business.cont.service.IContTempletCostRelService;
import cn.uone.business.res.service.IResCostConfigureDetailService;
import cn.uone.business.res.service.IResCostConfigureService;
import cn.uone.business.res.service.IResCostLadderService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sale.dao.SaleDemandDao;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/costConfigure")
public class ResCostConfigureController extends BaseController {

    @Autowired
    private IResCostConfigureService resCostConfigureService;

    @Autowired
    private IResCostConfigureDetailService resCostConfigureDetailService;

    @Autowired
    private IResCostLadderService resCostLadderService;

    @Autowired
    private IContContractService contContractService;
    @Autowired
    private IContTempService contContractTempService;
    @Autowired
    private IContTempletCostRelService contTempletCostRelService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private SaleDemandDao saleDemandDao;


    /**
     * 项目系统发起签约时获取费用配置信息
     *
     * @return
     */
    @RequestMapping("/costConfigureList")
    public RestResponse costConfigureList(@RequestParam(value = "id", required = false) String templateId){
        List<ResCostConfigureEntity> list = new ArrayList<>();
        list = resCostConfigureService.queryByTemplateId(templateId);
        return RestResponse.success().setData(list);
    }


    /**
     * 销售配置费用查询（接口）
     *
     * @return
     */
    @RequestMapping("/queryCostConfigureList")
    public RestResponse queryCostConfigureList(@RequestParam(value = "sourceType", required = false) String sourceType,
                                               @RequestParam(value = "sourceId", required = false) String sourceId,
                                               @RequestParam(value = "customerType", required = false) String customerType,
                                               @RequestParam(value = "isShort", required = false) String isShort,//是否短租
                                               @RequestParam(value = "templateId", required = false) String templateId//合同模板id
    ) {
        List<ContTempEntity> contractTempList = new ArrayList<>();
        List<ResCostConfigureEntity> list = new ArrayList<>();
        String msg = "";
        if (StrUtil.isNotBlank(templateId)) {
            list = resCostConfigureService.queryByTemplateId(templateId);
        } else {
            if (StrUtil.isBlank(sourceType) && StrUtil.isBlank(sourceId) && StrUtil.isBlank(customerType)) {
                return RestResponse.success().setData(list);
            }
            String projectid = UoneHeaderUtil.getProjectId();
            QueryWrapper wrapper = new QueryWrapper();
            if (StrUtil.isNotEmpty(sourceType)) {
                wrapper.eq("type", ContractTempletTypeEnum.getValueByName(sourceType));
            }
            if (StrUtil.isNotEmpty(customerType)) {
                wrapper.eq("customer_type", customerType);
            }
            wrapper.eq("project_id", projectid);
            if (StrUtil.isNotBlank(sourceId)) {
                //判断是否是人才房, 人才房,则只查询人才模版    非人才房,只查询非人才房模板
                ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
                Boolean talent = sourceEntity.getTalent();
                if (BooleanUtils.isTrue(talent)) {
                    msg = "人才合同模板未找到";
                    wrapper.eq("is_talent", "1");
                } else {
                    msg = "非人才合同模板未找到";
                    wrapper.eq("is_talent", "0");
                }
            }
            contractTempList = contContractTempService.list(wrapper);
            if (contractTempList.size() <= 0) {
                if (StrUtil.isNotBlank(msg)) {
                    return RestResponse.failure(msg);
                }
                if (StrUtil.isNotBlank(sourceType)) {
                    return RestResponse.failure(sourceType + "合同对应的合同模板未找到");
                }
                return RestResponse.failure("合同对应的合同模板未找到");

            }
            list = resCostConfigureService.queryByTemplate(contractTempList);
        }
        return RestResponse.success().setData(list);
    }
//
//    /**
//     * 销售配置费用查询（管家）
//     *
//     * @return
//     */
//    @RequestMapping("/getCostConfigureList")
//    public RestResponse getCostConfigureList(String sourceId) {
//        List<Map<String, Object>> list = new ArrayList<>();
//        //因为页面初始化会加载两次，第一次sourceid为空，所以先让第一次过去不报错
//        if (StrUtil.isBlank(sourceId)) {
//            return RestResponse.success().setData(list);
//        }
//        ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
//        ContTempEntity contTempEntity = contContractTempService.getTempleteBySource(sourceEntity);
//        if(ObjectUtil.isNotNull(contTempEntity)){
//            List<ContTempletCostRelEntity> costList = contTempletCostRelService.queryListByTempletId(contTempEntity.getId());
//            for (ContTempletCostRelEntity entity : costList) {
//                ResCostConfigureEntity configureEntity = resCostConfigureService.getById(entity.getCostId());
//                Map map = new HashMap();
//                map.put("value", configureEntity.getId());
//                map.put("name", configureEntity.getName());
//                list.add(map);
//            }
//        }
//        return RestResponse.success().setData(list);
//    }

    /**
     * 根据房源类型销售配置费用查询（接口）
     *
     * @return
     */
    @RequestMapping("/queryListBySourceType")
    public RestResponse queryListBySourceType(String id) {
        QueryWrapper wrapper = new QueryWrapper();
        String projectid = UoneHeaderUtil.getProjectId();
        wrapper.eq("project_id", projectid);
        wrapper.eq("source_type", id);
        wrapper.eq("type", "1");
        List<ResCostConfigureEntity> list = resCostConfigureService.list(wrapper);
        return RestResponse.success().setData(list);
    }

    /**
     * 销售配置费用查询（短租）
     *
     * @return
     */
    @RequestMapping("/queryListShort")
    public RestResponse queryListShort() {
        String projectid = UoneHeaderUtil.getProjectId();
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("project_id", projectid);
        wrapper.eq("type", "1");
        wrapper.eq("source_type", "0");
        wrapper.eq("is_short", "1");
        List<Map<String, Object>> list = resCostConfigureService.listMaps(wrapper);
        for (Map<String, Object> map : list) {
            map.put("value", map.get("id"));
        }
        return RestResponse.success().setData(list);
    }


    @RequestMapping("/queryListByContTempType")
    public RestResponse queryListByContTempType(String id) {

        SourceTypeEnum sourceTypeEnum = ContractTempletTypeEnum.getSourceTypeEnumByEnum(ContractTempletTypeEnum.getEnumByValue(id));

        if(null==sourceTypeEnum){
            return RestResponse.success();
        }

        QueryWrapper wrapper = new QueryWrapper();
        String projectid = UoneHeaderUtil.getProjectId();
        wrapper.eq("project_id", projectid);
        wrapper.eq("source_type", sourceTypeEnum.getValue());
        wrapper.eq("type", "1");
        List<ResCostConfigureEntity> list = resCostConfigureService.list(wrapper);
        return RestResponse.success().setData(list);
    }

    /**
     * 销售配置费用查询
     *
     * @return
     */
    @RequestMapping("/queryList")
    public RestResponse queryList(String projectId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("project_id", projectId);
        wrapper.eq("type", "1");
        List<Map<String, Object>> list = resCostConfigureService.listMaps(wrapper);
        for (Map<String, Object> map : list) {
            map.put("value", map.get("id"));
        }
        return RestResponse.success().setData(list);
    }



    @RequestMapping("/queryPage")
    public RestResponse queryPage(String name, String sourceType, Page page) {
        String projectid = UoneHeaderUtil.getProjectId();
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("project_id", projectid);
        wrapper.eq("type", "1");
        wrapper.orderByDesc("update_date");
        if (StrUtil.isNotBlank(name)) {
            wrapper.like("name", name);
        }
        if (StrUtil.isNotBlank(sourceType)) {
            wrapper.like("source_type", sourceType);
        }
        IPage<ResCostConfigureEntity> list = resCostConfigureService.queryIPage(page, wrapper);
        return RestResponse.success().setData(list);
    }

    /**
     * 销售配置删除数据
     * @param id
     * @return
     */
    @RequestMapping("/delCost")
    public RestResponse delCost(String id) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("cost_configure_id", id);
        //先查出合同判断该费用是否有关联到合同
        List<ContContractEntity> listContract = contContractService.list(wrapper);
        if (listContract.size() > 0) {
            return RestResponse.failure("存在关联的合同，无法删除！");
        }
        List<ResCostConfigureDetailEntity> list = resCostConfigureDetailService.list(wrapper);
        for (ResCostConfigureDetailEntity entity :
                list) {
            QueryWrapper wrapper1 = new QueryWrapper();
            wrapper1.eq("configure_detail_id", entity.getId());
            resCostLadderService.remove(wrapper1);
            resCostConfigureDetailService.removeById(entity.getId());
        }
        resCostConfigureService.removeById(id);
        return RestResponse.success();
    }

    /**
     * 查询销售配置和合同模板是否唯一
     *
     * @return
     */
    @RequestMapping("/queryCostAndTempIsOne")
    public RestResponse queryCostAndTempIsOne(@RequestParam(value = "sourceId", required = false) String sourceId,
                                              @RequestParam(value = "isOrg", required = false) String isOrg,//0 个人    1机构
                                              @RequestParam(value = "type", required = false) String type) {
        List<ContTempEntity> tempList = new ArrayList<>();
        if (StrUtil.isNotBlank(sourceId)) {
            ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
            tempList = contContractTempService.getTempleteBySource(sourceEntity);
        } else {
            ContTempEntity searchVo = new ContTempEntity();
            searchVo.setType(type).setCustomerType(isOrg);
            tempList = contContractTempService.queryByParam(searchVo);
        }
        if (tempList.size() != 1) {
            return RestResponse.success("");
        } else {
            ContTempEntity tempEntity = tempList.get(0);
            List<ResCostConfigureEntity> list = resCostConfigureService.queryByTemplateId(tempEntity.getId());
            if (list.size() != 1) {
                return RestResponse.success("");
            } else {
                Map<String, Object> map = new HashMap<>();
                map.put("tempId", tempEntity.getId());
                map.put("tempName", tempEntity.getName());
                map.put("costId", list.get(0).getId());
                map.put("costList", list);
                return RestResponse.success().setData(map);
            }
        }
    }

    @RequestMapping("/getCostAndTempOne")
    public RestResponse getCostAndTempOne(String sourceId,String renterId) throws Exception {
        Map<String, Object> map = new HashMap<>();
        QueryWrapper<SaleDemandEntity> query = new QueryWrapper<>();
        query.eq("user_id", renterId);
        query.eq("source_id", sourceId);
        SaleDemandEntity saleDemand = saleDemandDao.selectOne(query);
        if(null!=saleDemand && StrUtil.isNotBlank(saleDemand.getTempId()) && StrUtil.isNotBlank(saleDemand.getCostId())){
            map.put("tempId", saleDemand.getTempId());
            map.put("costId", saleDemand.getCostId());
            ContTempEntity tempEntity = contContractTempService.getById(saleDemand.getTempId());
            map.put("tempName", tempEntity.getName());
            List<ResCostConfigureEntity> list = resCostConfigureService.queryByTemplateId(tempEntity.getId());
            map.put("costList", list);
        }else {
            ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
            RenterEntity renter = renterFegin.getById(renterId);
            ContTempEntity tempEntity = contContractTempService.matcherContractTemplet(sourceEntity, renter, "sign");
            if (!ObjectUtil.isNull(tempEntity)) {
                map.put("tempId", tempEntity.getId());
                map.put("tempName", tempEntity.getName());
                List<ResCostConfigureEntity> list = resCostConfigureService.queryByTemplateId(tempEntity.getId());
                map.put("costList", list);
                if (list.size() == 1) {
                    map.put("costId", list.get(0).getId());
                }
            }
        }

        return RestResponse.success().setData(map);
    }

    @RequestMapping("/getShowCost")
    @UonePermissions
    public RestResponse getShowCost(String id) {
        String costs = resCostConfigureService.getShowCost(id);
        return RestResponse.success().setData(costs);
    }

}
