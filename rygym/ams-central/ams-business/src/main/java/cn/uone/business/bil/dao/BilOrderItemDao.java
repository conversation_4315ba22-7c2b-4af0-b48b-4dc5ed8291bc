package cn.uone.business.bil.dao;

import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderItemVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderItemWithOrderVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Repository
public interface BilOrderItemDao extends BaseMapper<BilOrderItemEntity> {

    List<Map<String, String>> findAmountDetails(@Param("map") Map<String, Object> map);

    List<BilOrderItemEntity> getAuditOrderItems(@Param("accountType") String accountType,@Param("projectId") String projectId);

    List<Map<String, Object>> getOrderInfoList(@Param("map") Map<String, Object> map);

    /**
     * 获取某段时间, 费用支出情况
     * @param map
     * @return
     */
    Map<String, Object> getPaymentInfo(@Param("map") Map<String, Object> map);


    List<BilOrderItemEntity> getOrderItemByContractIdAndSlot(@Param("contractId") String contractId,@Param("sourceId") String sourceId,@Param("type") String type, @Param("startDate") String startDate, @Param("endDate") String endDate);

    void deleteCheckoutOrderItem(@Param("map") Map<String, Object> map);

    BilOrderItemEntity getSumZujin(@Param("orderId")String orderId);

    BigDecimal getRentActFee(@Param("orderId")String orderId);

    BigDecimal getRefundDeposit(@Param("contractId")String contractId, @Param("sourceId")String sourceId);

    BilOrderItemEntity getItemByOrderIdAndType(@Param("orderId")String orderId, @Param("orderItemType")String orderItemType);

    List<Map<String,Object>> getItemsByMonth(@Param("yearMonth")String yearMonth,@Param("projectId")String projectId);

    IPage<Map<String,Object>> pageByRecItemId(Page page, @Param("receivableItemId")String receivableItemId);

    List<BilOrderItemVo> getItemVosByOrderId(@Param("orderId") String orderId);

    BilOrderItemVo getItemVoWithoutItemInfoByOrderItemId(@Param("orderItemId") String orderItemId);

    IPage<BilOrderItemVo> getDeviceBill(Page page, @Param("deviceId")String deviceId,@Param("type")String type);

    Map<String, String> getInvoicInfo(@Param("map") Map<String, Object> map);

    int updateParkingOrder(@Param("startTime")Date startTime,@Param("endTime")Date endTime,@Param("contractId")String contractId);

    BilOrderItemEntity getMonthOrderItem(@Param("map") Map<String, Object> map);

    List<BilOrderItemWithOrderVo> getItemWithOrderVoList(@Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                                         @Param("orderItemTypes") List<String> orderItemTypes, @Param("direction") int direction,
                                                         @Param("projectCodes") List<String> projectCodes,
                                                         @Param("filterType") String filterType,
                                                         @Param("orderIds") Collection<String> orderIds);

    List<BilOrderItemEntity> getOrderItemsByMap(@Param("map") Map<String, Object> map);
}
