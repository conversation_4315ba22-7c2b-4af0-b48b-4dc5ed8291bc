package cn.uone.business.kingdee.controller;


import cn.uone.bean.entity.business.kingdee.vo.KingdeePaymentItemSearchVo;
import cn.uone.business.kingdee.service.IKingdeePaymentItemService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@RestController
@RequestMapping("/kingdee-payment-item-entity")
public class KingdeePaymentItemController extends BaseController {
    @Autowired
    private IKingdeePaymentItemService kingdeePaymentItemService;
    /**
     * 列表
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, KingdeePaymentItemSearchVo searchVo) {
        RestResponse response = new RestResponse();
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo",searchVo);
        return response.setSuccess(true).setData(kingdeePaymentItemService.getVoList(page,map));
    }
}
