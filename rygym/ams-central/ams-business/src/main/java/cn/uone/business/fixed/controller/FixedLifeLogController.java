package cn.uone.business.fixed.controller;


import cn.uone.bean.entity.business.fixed.FixedLifeLogEntity;
import cn.uone.business.fixed.service.IFixedLifeLogService;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 生命周期日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@RestController
@RequestMapping("/fixed-life-log-entity")
public class FixedLifeLogController extends BaseController {
    @Resource
    private IFixedLifeLogService iFixedLifeLogService;
    /**
     * 获取信息
     *
     * @param fixedLifeLog
     * @return 标签管理列表 分页
     */
    @GetMapping("/page")
    public RestResponse page(Page page, FixedLifeLogEntity fixedLifeLog) {
        IPage<FixedLifeLogEntity> iPage = iFixedLifeLogService.page(page, fixedLifeLog);

        return RestResponse.success().setData(iPage);
    }
    /**
     * @param fixedLifeLog
     * @return 标签管理列表
     */
    @GetMapping("/list")
    public RestResponse list( FixedLifeLogEntity fixedLifeLog) {
        List<FixedLifeLogEntity> list = iFixedLifeLogService.list( fixedLifeLog);
        return RestResponse.success().setData(list);
    }
    /**
     * 获取信息
     *
     * @param id 主键
     * @return 标签管理列表
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        FixedLifeLogEntity info = iFixedLifeLogService.getById(id);
        return RestResponse.success().setData(info);
    }

    /**
     * 新增
     *
     * @param fixedLifeLog 参数
     * @return 标签管理列表
     */
    @PostMapping("/save")
    public RestResponse save(FixedLifeLogEntity fixedLifeLog) {
        if(iFixedLifeLogService.save(fixedLifeLog)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    /**
     * 修改
     *
     * @param fixedLifeLog 参数
     * @return 标签管理列表
     */
    @PostMapping("/edit")
    public RestResponse edit(FixedLifeLogEntity fixedLifeLog) {
        if(iFixedLifeLogService.updateById(fixedLifeLog)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return 标签管理列表
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        if(iFixedLifeLogService.removeByIds(ids)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
}
