package cn.uone.business.cont.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.IdTypeEnum;
import cn.uone.application.enumerate.RenterType;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.*;
import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.biz.vo.SettleVo;
import cn.uone.bean.entity.business.cont.*;
import cn.uone.bean.entity.business.cont.vo.AlterPriceVo;
import cn.uone.bean.entity.business.cont.vo.ContContractVo;
import cn.uone.bean.entity.business.cont.vo.ContFrameContractSearchVo;
import cn.uone.bean.entity.business.cont.vo.ContFrameContractVo;
import cn.uone.bean.entity.business.res.ResCostConfigureEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.sale.SaleDemandEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.base.service.IBaseEnterpriseService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.biz.service.IBizSettleService;
import cn.uone.business.cont.dao.ContFrameContractDao;
import cn.uone.business.cont.service.*;
import cn.uone.business.res.service.IResCostConfigureService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.util.AlgorUtil;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.PassWordCreateUtil;
import cn.uone.web.util.UoneHeaderUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Service
public class ContFrameContractServiceImpl extends ServiceImpl<ContFrameContractDao, ContFrameContractEntity> implements IContFrameContractService {

    @Autowired
    private IBaseEnterpriseService enterpriseService;
    @Autowired
    private IContTempService contTempService;
    @Autowired
    private ISysFileService fileService;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    private IContContractInfoService contContractInfoService;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private IContContractSourceRelService contractSourceRelService;
    @Autowired
    private IResSourceService  resSourceService;
    @Autowired
    private IBizReleaseService bizReleaseService;
    @Autowired
    private IBizSettleService settleService;
    @Autowired
    private IBilOrderService orderService;
    @Autowired
    private IResCostConfigureService costConfigureService;


    @Override
    public IPage<ContFrameContractVo> selectPages(Page page, ContFrameContractSearchVo param) {
        return baseMapper.queryList(page,param);
    }

    @Override
    public IPage<HashMap> findSourceByCondition(Page page, DataScope dataScope, String id,String sourceName, String partitionId, String floor, String relType, String sourceType, List<String> sourceIds) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("projectId", UoneHeaderUtil.getProjectId());
        map.put("partitionId", partitionId);
        map.put("floor", floor);
        map.put("id", id);
        map.put("relType", relType);
        map.put("sourceIds", sourceIds);
        map.put("sourceType", sourceType);
        map.put("sourceName", sourceName);
        map.put("isShort", "0");
        return baseMapper.findSourceByCondition(page, dataScope, map);
    }

    /**
     * 参数判断;
     * 查询是否存在已关联的合同;
     */
    @Override
    public Boolean existFrameContractByTempId(String tempId) {
        // 查询是否存在已关联的合同;
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("contract_templet_id", tempId);
        List<ContFrameContractEntity> list = this.list(wrapper);
        if (list.size() > 0) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean createContract(ContFrameContractEntity frameContract, BaseEnterpriseEntity enterprise, List<MultipartFile> licenseFiles, List<MultipartFile> contractFiles, List<MultipartFile> annexFiles, String contractVos) {
        try {
            String projectId = UoneHeaderUtil.getProjectId();
            //机构信息
            enterprise.setProjectId(projectId);
            enterprise = enterpriseService.saveOrUpdateEnterprise(enterprise);
            //机构协议
            //公寓平台：前端不再选择主合同模板。后续每个门店只能添加一个主合同模板，添加企业合同时后端自动选择模板
            String tempId = frameContract.getTemplateId();
            if(StrUtil.isBlank(tempId)){
                ContTempEntity searchVo=new ContTempEntity()
                        .setProjectId(projectId)
                        .setType("1")
                        .setSubjectType("2");
                List<ContTempEntity> temps=contTempService.queryByParam(searchVo);
                tempId=temps.get(0).getId();
            }
            frameContract.setContractTempletId(tempId);
            frameContract.setProjectId(projectId);
            frameContract.setSignerId(enterprise.getRenterId());
            frameContract.setState(FrameContractStateEnum.STATUS_INEFFECTIVE.getValue());
            save(frameContract);
            //附件
            fileService.saveFiles(annexFiles, frameContract.getId(), SysFileTypeEnum.SUBJOIN_CONTRACT.getValue());
            fileService.saveFiles(licenseFiles, frameContract.getId(), SysFileTypeEnum.BUSINESS_LICENSE.getValue());
            fileService.saveFiles(licenseFiles, enterprise.getRenterId(), SysFileTypeEnum.BUSINESS_LICENSE.getValue());
            //生成pdf
            if(!contractFiles.isEmpty() && StrUtil.isNotBlank(contractFiles.get(0).getOriginalFilename())){
                fileService.saveFiles(contractFiles, frameContract.getId(), SysFileTypeEnum.CONTRACT.getValue());
            }else{
                contTempService.createFramePdf(frameContract, enterprise);
            }


            //生成子合同
            List<JSONObject> list = (List) JSON.parse(contractVos);
            for (JSONObject jsonObject : list) {
                ContContractVo contractVo = JSONObject.toJavaObject(jsonObject, ContContractVo.class);
                //生成合同
                RenterEntity renter = renterFegin.getById(enterprise.getRenterId());
                ResSourceVo source = resSourceService.getInfoById(contractVo.getSourceId());
                SaleDemandEntity demand = new SaleDemandEntity();
                demand.setSourceId(source.getId());
                demand.setUserId(renter.getId());
                demand.setPayType(frameContract.getPayType());
                demand.setCostId(frameContract.getCostConfigureId());
                demand.setTempId(frameContract.getTemplateId());
                demand.insert();
                AlterPriceVo alterPriceVo = new AlterPriceVo();
                if(null != contractVo.getRentalPrice()){
                    alterPriceVo.setDeposit(contractVo.getRentalPrice());
                    alterPriceVo.setPrice(contractVo.getRentalPrice());
                }
                ContContractEntity contract = contContractService.generateContract(renter, source, contractVo.getContractTempletId(), frameContract.getStartDate(), DateUtil.endOfDay(frameContract.getEndDate()),alterPriceVo);
                contract.setLifePayPayer(frameContract.getLifePayPayer());
                contract.setRentPayPayer(frameContract.getRentPayPayer());
                contract.setFixLifePayPayer(frameContract.getFixLifePayPayer());
                contract.setFrameContractId(frameContract.getId());
                contract.setSignDate(new Date());
                contract.setInvoiceType(contractVo.getInvoiceType());
                contContractService.updateById(contract);
                //开票信息
                ContContractInfoEntity info = contContractInfoService.getByContractId(contract.getId());
                info.setTaxOrgName(contractVo.getTaxOrgName());
                info.setEnterpriseAddress(contractVo.getEnterpriseAddress());
                info.setEnterpriseTel(contractVo.getEnterpriseTel());
                info.setEnterpriseAccount(contractVo.getEnterpriseAccount());
                info.setEnterpriseBank(contractVo.getEnterpriseBank());
                info.setTaxpayerCode(contractVo.getTaxpayerCode());
                contContractInfoService.updateById(info);
                //附件保存
                SysFileEntity file = fileService.getByUrl(contractVo.getUrl());
                if (StrUtil.isNotEmpty(contractVo.getUrl()) && ObjectUtil.isNotNull(file)) {
                    file.setFromId(contract.getId());
                    file.setType(SysFileTypeEnum.SUBJOIN_CONTRACT.getValue());
                    file.updateById();
                }
                //审核合同  1.28审核动作转移到企业用户确认时
//                contContractService.auditContract(contract, source, renter, false);
                //消息推送
                ContContractSourceRelEntity rel = contractSourceRelService.getByContractIdAndSourceId(contract.getId(), source.getId());

//                contTempService.createSignPdf(contract.getId());
//                pushMsgService.createPushMsg(MsgTypeEnum.DAIBANRUZHU.getValue(), null, rel.getSourceId());
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public void validContract(String id) throws Exception {
        //将子合同的审核模块放到确认合同时进行（auditContract）
       QueryWrapper<ContContractEntity> query=new QueryWrapper<>();
       query.eq("frame_contract_id",id);
       List<ContContractEntity> contractList=contContractService.list(query);
       for(ContContractEntity contract:contractList){
         ResSourceEntity source=resSourceService.getSourceByContractId(contract.getId());
         RenterEntity renter=renterFegin.getById(contract.getSignerId());
         contContractService.auditContract(contract,source,renter,true);
       }
       ContFrameContractEntity entity=getById(id);
       entity.setState(FrameContractStateEnum.STATUS_EFFECT.getValue());
       entity.insertOrUpdate();
    }

    @Override
    @Transactional
    public void cancelContract(String id) throws Exception {
        // 1、将主合同设置为失效状态
        ContFrameContractEntity entity=getById(id);
        entity.setState(FrameContractStateEnum.STATUS_CANCEL.getValue());
        entity.insertOrUpdate() ;
        // 2、将对应的子合同设置为失效状态 以及删除对应的合同信息表、合同房源关联表
        QueryWrapper<ContContractEntity> query=new QueryWrapper<>();
        query.eq("frame_contract_id",id);
        List<ContContractEntity> contractList=contContractService.list(query);
        for(ContContractEntity contract:contractList){
            ContContractInfoEntity contractInfoEntity = contContractInfoService.getByContractId(contract.getId());
            contractInfoEntity.deleteById();
            ContContractSourceRelEntity contractSourceRelEntity = contractSourceRelService.getByContractIdAndSourceId(contract.getId(),null);
            contractSourceRelEntity.deleteById();
            contract.setState(ContractStateEnum.STATUS_CANCEL.getValue());
            contract.insertOrUpdate();
        }
    }

    @Override
    @Transactional
    public RestResponse endContract(SettleVo vo) {
        RestResponse response = new RestResponse();
        ContFrameContractEntity frameContract = getById(vo.getFrameContractId());
        Map<String,Object> search=new HashedMap();
        search.put("frameContractId",vo.getFrameContractId());
        Map<String,Object> billMap=orderService.countUnPayNum(search);
        Integer num=Integer.parseInt(billMap.get("unPayNum").toString()) ;
        if(num!=0){
            return response.setSuccess(false).setMessage("该合同下有未结清账单，无法进行终止操作");
        }
        try {
            QueryWrapper<ContContractEntity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("frame_contract_id", vo.getFrameContractId());
            queryWrapper.in("state", ContractStateEnum.STATUS_NO_IN_TIME.getValue(), ContractStateEnum.STATUS_TAKE_EFFECT.getValue(), ContractStateEnum.STATUS_CHECKOUT.getValue());
            //TODO modify by zengguoshen 20211231 start  结束主合同时直接对子合同进行退租操作
            //将合同下的所有子合同找出来
            QueryWrapper<ContContractEntity> query=new QueryWrapper<>();
            query.eq("frame_contract_id",vo.getFrameContractId());
            List<ContContractEntity> contractList=contContractService.list(query);
            //生成release单
            RestResponse releaseResp=new RestResponse();
            if(!CollectionUtils.isEmpty(contractList)){
                releaseResp=bizReleaseService.organizeRelease(contractList);
            }
            //办理退租
            if(releaseResp.getSuccess()){
                for(ContContractEntity contract:contractList){
                    String contractId=contract.getId();
                    BizReleaseEntity release = bizReleaseService.getByContractId(contractId);
                    String releaseId=release.getId();
                    String sourceId=resSourceService.getSourceByContractId(contractId).getId();
                    Map<String, Object> par = new HashMap<>();
                    par.put("contractId",contract.getId());
                    par.put("sourceId",resSourceService.getSourceByContractId(contract.getId()).getId());
                    par.put("checkoutDate",DateUtil.today());
                    par.put("confirmReason", ReleaseReasonEnum.CONTRACT_CHANGE.getValue());
                    Map<String, Object> result = bizReleaseService.calculate(par);
                    //生成结算预览单
                    settleService.settle(releaseId,sourceId,result);
                    //完成租客确认退租，生成账单（原流程是管家办理退租后，生成settle数据，租客确认退租后生成bilorder数据。公寓平台去掉子合同功能，管理办理退租后后端直接生成账单）
                    release.setState(ReleaseStateEnum.CONFIRMED.getValue());
                    bizReleaseService.handleRelease(release);
                    release.updateById();
                }
                if (contContractService.list(queryWrapper).isEmpty()) {
                    frameContract.setState(FrameContractStateEnum.STATUS_TERMINATE.getValue());
                    updateById(frameContract);
                    response.setSuccess(true).setMessage("操作成功！");
                } else {
                    response.setSuccess(false).setMessage("主合同下存在状态不为已退租（未启租/已生效/退租中）的子合同，不允许终止合同！");
                }

            }else{
                response.setSuccess(false);
            }
            //生成维修费，清洁费，门禁卡，其它费账单
            BigDecimal total=vo.getAccessCardFee().add(vo.getOtherFee()).add(vo.getCleanFee()).add(vo.getRepairFee());
            BilOrderEntity mainOrder=orderService.addOrgOrder(total,frameContract,vo);
            //TODO modify by zengguoshen 20xxxxxx  end
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return  response;
    }


    @Override
    @Transactional
    public boolean createMultiSourceContract(ContFrameContractEntity frameContract, RenterEntity renter,List<MultipartFile> licenseFiles, List<MultipartFile> contractFiles, List<MultipartFile> annexFiles, String contractVos) throws Exception {
        RenterEntity oldRenter = renterFegin.getByTelAndType(renter.getTel(), RenterType.COMMON.getValue());
        if(oldRenter == null){
            renter.setType(RenterType.COMMON.getValue())
                    .setUsername(renter.getTel())
                    .setSalt(AlgorUtil.getSalt())
                    .setPassword(PassWordCreateUtil.createPassWord(6)); // 随机密码生成
            renter = JSON.parseObject(JSON.toJSONString(renterFegin.add(renter).get("data")), RenterEntity.class);
        }else{
            BeanUtil.copyProperties(oldRenter,renter);
        }

        //公寓平台：前端不再选择主合同模板。后续每个门店只能添加一个主合同模板，添加企业合同时后端自动选择模板
        String projectId=UoneHeaderUtil.getProjectId();
        ContTempEntity searchVo=new ContTempEntity()
                .setProjectId(projectId)
                .setType("1")
                .setSubjectType("0");
        List<ContTempEntity> temps=contTempService.queryByParam(searchVo);
        ContTempEntity temp = temps.get(0);
        String tempId = temp.getId();
        ResCostConfigureEntity costConfigure = costConfigureService.queryByTemplateId(tempId).get(0);
        frameContract.setContractTempletId(tempId);
        frameContract.setCostConfigureId(costConfigure.getId());
        frameContract.setProjectId(projectId);
        frameContract.setSignerId(renter.getId());
        frameContract.setState(FrameContractStateEnum.STATUS_EFFECT.getValue());
        frameContract.setType(ContractTypeEnum.MULTISOURCE.getValue());
        save(frameContract);
        //附件
        fileService.saveFiles(contractFiles, frameContract.getId(), SysFileTypeEnum.SUB_CONTRACT.getValue());
        fileService.saveFiles(annexFiles, frameContract.getId(), SysFileTypeEnum.SUBJOIN_CONTRACT.getValue());
        //fileService.saveFiles(licenseFiles, frameContract.getId(), SysFileTypeEnum.BUSINESS_LICENSE.getValue());
        //生成pdf
        //contTempService.createFramePdf(frameContract, enterprise);

        //RenterEntity renter = renterFegin.getById(enterprise.getRenterId());
        //生成子合同
        BigDecimal deposit = BigDecimal.ZERO;
        BigDecimal price = BigDecimal.ZERO;
        List<String> sources = Lists.newArrayList();
        List<JSONObject> list = (List) JSON.parse(contractVos);
        ResSourceVo source = null;
        ContContractVo contractVo = null;
        for (JSONObject jsonObject : list) {
            contractVo = JSONObject.toJavaObject(jsonObject, ContContractVo.class);
            //生成合同
            source = resSourceService.getInfoById(contractVo.getSourceId());
            SaleDemandEntity demand = new SaleDemandEntity();
            demand.setSourceId(source.getId());
            demand.setUserId(renter.getId());
            demand.setPayType(frameContract.getPayType());
            demand.setCostId(frameContract.getCostConfigureId());
            demand.setTempId(frameContract.getTemplateId());
            demand.insert();
            if(null != contractVo.getRentalPrice()){
                deposit = deposit.add(contractVo.getRentalPrice());
                price = price.add(contractVo.getRentalPrice());
            }
            sources.add(source.getId());
        }
        AlterPriceVo alterPriceVo = new AlterPriceVo();
        alterPriceVo.setDeposit(deposit);
        alterPriceVo.setPrice(price);
        //ContTempEntity temp = contTempService.matcherContractTemplet(source, renter, "sign");
        ContContractEntity contract = contContractService.generateMultiSourceContract(renter, frameContract, temp, frameContract.getStartDate(), DateUtil.endOfDay(frameContract.getEndDate()),StrUtil.join(",",sources),alterPriceVo);
        contract.setContractCode(frameContract.getCode());
        contract.setLifePayPayer(PayerTypeEnum.STAFF.getValue());
        contract.setRentPayPayer(PayerTypeEnum.STAFF.getValue());
        contract.setFixLifePayPayer(frameContract.getFixLifePayPayer());
        contract.setFrameContractId(frameContract.getId());
        contract.setSignDate(new Date());
        contract.setInvoiceType(frameContract.getInvoiceType());
        contContractService.updateById(contract);
        //开票信息
        /*ContContractInfoEntity info = contContractInfoService.getByContractId(contract.getId());
        info.setTaxOrgName(contractVo.getTaxOrgName());
        info.setEnterpriseAddress(contractVo.getEnterpriseAddress());
        info.setEnterpriseTel(contractVo.getEnterpriseTel());
        info.setEnterpriseAccount(contractVo.getEnterpriseAccount());
        info.setEnterpriseBank(contractVo.getEnterpriseBank());
        info.setTaxpayerCode(contractVo.getTaxpayerCode());
        contContractInfoService.updateById(info);*/
        //附件保存
        SysFileEntity file = fileService.getByUrl(contractVo.getUrl());
        if (StrUtil.isNotEmpty(contractVo.getUrl()) && ObjectUtil.isNotNull(file)) {
            file.setFromId(contract.getId());
            file.setType(SysFileTypeEnum.SUBJOIN_CONTRACT.getValue());
            file.updateById();
        }
        return true;
    }


}
