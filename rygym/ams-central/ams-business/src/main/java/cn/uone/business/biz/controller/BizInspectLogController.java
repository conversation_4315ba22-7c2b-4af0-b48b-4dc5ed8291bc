package cn.uone.business.biz.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.InspectStateEnum;
import cn.uone.application.enumerate.contract.ReleaseStateEnum;
import cn.uone.bean.entity.business.biz.BizInspectEntity;
import cn.uone.bean.entity.business.biz.BizInspectLogEntity;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.business.biz.service.IBizInspectLogService;
import cn.uone.business.biz.service.IBizInspectService;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.cont.service.IContCheckInUserService;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.dev.service.IDevDeviceService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 查房表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-05
 */
@RestController
@RequestMapping("/biz/inspectLog")
public class BizInspectLogController extends BaseController {

    @Autowired
    private IBizInspectService bizInspectService;
    @Autowired
    private IBizInspectLogService bizInspectLogService;
    @Autowired
    private IBizReleaseService bizReleaseService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IContContractSourceRelService contContractSourceRelService;
    @Autowired
    private IContCheckInUserService contCheckInUserService;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private IDevDeviceService deviceService;

    /**
     * 查询
     *
     * @return
     */
    @RequestMapping("/getListByInspectId")
    public RestResponse getListByInspectId(String inspectId) {
        RestResponse response = new RestResponse();
        try {
            List<BizInspectLogEntity> inspectLogList = bizInspectLogService.getListByInspectId(inspectId,true);
            Map<String, Object> resultDataMap = new HashMap<>();
            resultDataMap.put("inspectLogList", inspectLogList);
            response.setSuccess(true).setData(resultDataMap);
        }catch (Exception e) {
            response.setSuccess(false).setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @Transactional(rollbackFor = Exception.class,propagation =Propagation.REQUIRES_NEW)
    public RestResponse save(BizInspectLogEntity inspectLog, HttpServletRequest request) {
        RestResponse response = new RestResponse();
        try {
            if(!inspectLog.getRectify()){
                BizInspectEntity inspect = bizInspectService.getById(inspectLog.getInspectId());
                if(StrUtil.isNotBlank(inspect.getReleaseId())){
                    //不为空说明是个人退租
                    inspect.setState(InspectStateEnum.COMPLETE.getValue()).updateById();
                    BizReleaseEntity release = bizReleaseService.getById(inspect.getReleaseId());
                    release.setState(ReleaseStateEnum.SETTLE.getValue()).updateById();
                    List<ContContractSourceRelEntity> rels = contContractSourceRelService.getListByContractId(release.getContractId());
                    for (ContContractSourceRelEntity rel : rels) {
                        //入住人搬离
                        contCheckInUserService.remove(rel.getId());
                        ResSourceEntity source = resSourceService.getById(rel.getSourceId());
                        source.setCheckIn(false).updateById();
                    }
                }else{
                    //机构搬离
                    inspect.setState(InspectStateEnum.REMOVE.getValue()).updateById();
                }
            }
            bizInspectLogService.save(inspectLog);

            CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());
            commonsMultipartResolver.setDefaultEncoding("utf-8");
            if (commonsMultipartResolver.isMultipart(request)){
                MultipartHttpServletRequest mulReq = (MultipartHttpServletRequest) request;
                Map<String, MultipartFile> map = mulReq.getFileMap();
                for (Map.Entry<String, MultipartFile> entry : map.entrySet()) {
                    sysFileService.saveImg(entry.getValue(), inspectLog.getId(), SysFileTypeEnum.CHECKROOM.getValue(), null);
                }
            }
            response.setSuccess(true).setMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            response.setMessage(e.getMessage());
        }
        return response;
    }


}
