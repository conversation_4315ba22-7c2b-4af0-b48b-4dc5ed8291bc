package cn.uone.business.bil.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.DataFromEnum;
import cn.uone.application.enumerate.IdTypeEnum;
import cn.uone.application.enumerate.MsgTypeEnum;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.base.BizTypeEnum;
import cn.uone.application.enumerate.contract.ContractStateEnum;
import cn.uone.application.enumerate.contract.ContractTypeEnum;
import cn.uone.application.enumerate.contract.InvoiceTypeEnum;
import cn.uone.application.enumerate.contract.PayTypeEnum;
import cn.uone.application.enumerate.contract.PayerTypeEnum;
import cn.uone.application.enumerate.contract.ReleaseTypeEnum;
import cn.uone.application.enumerate.contract.SubjectTypeEnum;
import cn.uone.application.enumerate.order.InvoiceStateEnum;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.order.PayWayEnum;
import cn.uone.application.enumerate.order.TransferTypeEnum;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.bean.entity.business.bil.BilInterfaceMsgEntity;
import cn.uone.bean.entity.business.bil.BilLiveErrorLogEntity;
import cn.uone.bean.entity.business.bil.BilOrderConfirmEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.BilOrderPayInfoEntity;
import cn.uone.bean.entity.business.bil.BilTransferEntity;
import cn.uone.bean.entity.business.bil.vo.BilCountVo;
import cn.uone.bean.entity.business.bil.vo.BilEmpOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.bean.entity.business.bil.vo.BilOverdueVo;
import cn.uone.bean.entity.business.bil.vo.DailyOrderVo;
import cn.uone.bean.entity.business.bil.vo.OrderConfirmPayVo;
import cn.uone.bean.entity.business.bil.vo.OrderImportVo;
import cn.uone.bean.entity.business.bil.vo.ParkOrderVo;
import cn.uone.bean.entity.business.biz.vo.SettleVo;
import cn.uone.bean.entity.business.cont.ContCheckInUserEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContFrameContractEntity;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.cont.ContTempRichEntity;
import cn.uone.bean.entity.business.cont.vo.AlterPriceVo;
import cn.uone.bean.entity.business.invoice.InvoiceAccountEntity;
import cn.uone.bean.entity.business.invoice.InvoiceAccountOrdertypeRelEntity;
import cn.uone.bean.entity.business.invoice.vo.InvoiceBuyerVo;
import cn.uone.bean.entity.business.report.InvoiceEntity;
import cn.uone.bean.entity.business.res.ResProjectCompanyEntity;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResProjectInfoEntity;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceSearchVo;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.sale.SaleDemandEntity;
import cn.uone.bean.entity.business.sys.SysAreaEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.QywxAgentEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.bean.entity.tpi.baiwang.BaiwangInvoiceDetailVo;
import cn.uone.bean.entity.tpi.baiwang.BaiwangInvoiceVo;
import cn.uone.bean.entity.tpi.baiwang.BaiwangLeaseInfoVo;
import cn.uone.bean.entity.tpi.baiwang.BaiwangRedInvoiceDetailVo;
import cn.uone.bean.entity.tpi.baiwang.BaiwangRedInvoiceVo;
import cn.uone.bean.entity.tpi.fadada.ReqExtsignAuto;
import cn.uone.bean.entity.tpi.fadada.ReqUploadDocs;
import cn.uone.business.Guomi.service.IGuomiService;
import cn.uone.business.bil.dao.BilLiveErrorLogDao;
import cn.uone.business.bil.dao.BilOrderConfirmDao;
import cn.uone.business.bil.dao.BilOrderDao;
import cn.uone.business.bil.service.IBilDiscountLogService;
import cn.uone.business.bil.service.IBilInterfaceMsgService;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilOrderPayInfoService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.bil.service.IBilTransferService;
import cn.uone.business.biz.service.IBizAccountService;
import cn.uone.business.cont.dao.ContContractDao;
import cn.uone.business.cont.dao.ContTempDao;
import cn.uone.business.cont.dao.ContTempRichDao;
import cn.uone.business.cont.service.IContCheckInUserService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.cont.service.IContParService;
import cn.uone.business.cont.service.IYngyContTempService;
import cn.uone.business.invoice.service.IInvoiceAccountOrdertypeRelService;
import cn.uone.business.invoice.service.IInvoiceAccountService;
import cn.uone.business.kingdee.service.IKingdeeApiService;
import cn.uone.business.res.service.IResProjectCompanyService;
import cn.uone.business.res.service.IResProjectInfoService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.rpt.service.IReportInvoiceService;
import cn.uone.business.sale.service.ISaleCustomerService;
import cn.uone.business.sys.service.ISysAreaService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.fegin.crm.IQywxAgentFegin;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysCompanyFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.crm.ISysParaFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.fegin.tpi.IBaiwangFegin;
import cn.uone.fegin.tpi.IFadadaFegin;
import cn.uone.fegin.tpi.IQyWechatFegin;
import cn.uone.fegin.tpi.IQywxMessageFegin;
import cn.uone.fegin.tpi.IWechatFegin;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.AddressMergerUtil;
import cn.uone.util.CodeUtil;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import cn.uone.util.PdfUtil;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.DateTimeUtil;
import cn.uone.web.util.SafeCompute;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Service
@Slf4j

public class BilOrderServiceImpl extends ServiceImpl<BilOrderDao, BilOrderEntity> implements IBilOrderService {


    @Autowired
    private BilOrderDao bilOrderDao;
    @Autowired
    @Lazy
    private IContCheckInUserService contCheckInUserService;
    @Autowired
    @Lazy
    private IBilOrderItemService itemService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    @Lazy
    private IContContractService contContractService;
    @Autowired
    @Lazy
    private IBilTransferService bilTransferService;
    @Autowired
    private IBilDiscountLogService bilDiscountLogService;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private ISysPushMsgService sysPushMsgService;
    @Autowired
    private IUserFegin userFegin;
    @Autowired
    private IBilInterfaceMsgService bilInterfaceMsgService;
    @Autowired
    private IContContractSourceRelService contContractSourceRelService;
    @Autowired
    private IResProjectParaService resProjectParaService;
    @Autowired
    @Lazy
    private IGuomiService guomiService;
    @Autowired
    private ISaleCustomerService saleCustomerService;
    @Autowired
    @Lazy
    private IContParService contParService;
    @Autowired
    private ISysParaFegin sysParaFegin;
    @Resource
    private ContContractDao contContractDao;
    @Autowired
    private IBilOrderPayInfoService orderPayInfoService;
    @Resource
    BilLiveErrorLogDao errorLogDao;

    @Resource
    private IWechatFegin wechatFegin;

    @Resource
    private IQyWechatFegin qyWechatFegin;

    @Resource
    private BilOrderConfirmDao orderConfirmDao;

    @Autowired
    private IResProjectCompanyService companyService;

    @Autowired
    private IKingdeeApiService kingdeeApiService;

    @Autowired
    private ISysMsgTemplateFegin sysMsgTemplateFegin;

    @Autowired
    @Lazy
    private IReportInvoiceService reportInvoiceService;

    @Resource
    private ContTempRichDao richDao;

    @Autowired
    private ISysFileService sysFileService;

    @Resource
    private IFadadaFegin fadadaFegin;
    @Autowired
    private IResProjectInfoService resProjectInfoService;
    @Autowired
    private MinioUtil minioUtil;
    @Autowired
    private PdfUtil pdfUtil;
    @Autowired
    private IQywxMessageFegin qywxMessageFegin;
    @Autowired
    private IQywxAgentFegin qywxAgentFegin;
    @Autowired
    private ContTempDao contTempDao;
    @Autowired
    private IYngyContTempService yngyContTempService;
    @Autowired
    IBaiwangFegin baiwangFegin;
    @Autowired
    private IInvoiceAccountOrdertypeRelService invoiceAccountOrdertypeRelService;
    @Autowired
    private IInvoiceAccountService invoiceAccountService;
    @Autowired
    private ISysAreaService sysAreaService;
    @Autowired
    private IBizAccountService bizAccountService;
    @Autowired
    private ISysCompanyFegin sysCompanyFegin;
    @Autowired
    private AddressMergerUtil addressMergerUtil;


    @Override
    public List<BilOrderVo> getOrderList(String contractId, String orderType) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("contractId",contractId);
        map.put("orderType",orderType);
        return baseMapper.getOrderList(map);
    }

    @Override
    public List<BilOrderVo> getOrderList(Map<String,Object> map) {
        return baseMapper.getOrderList(map);
    }

    @Override
    public BilOrderVo getOrderById(String orderId) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("orderId",orderId);
        List<BilOrderVo> list = baseMapper.getOrderList(map);
        if(list ==null || list.size()==0){
            return null;
        }
        return list.get(0);
    }

    @Override
    public BilOrderEntity getYajinOrder(String contractId) {
        QueryWrapper<BilOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("contractId",contractId);
        queryWrapper.eq("orderType",OrderTypeEnum.YAJIN.getValue());
        return getOne(queryWrapper);
    }

    //==以前的接口=========================================================================================================
    @Override
    public List<BilOrderVo> findByCondition(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.selectBilOrderByMap(map, dataScope);
    }

    @Override
    public List<BilOrderVo> queryList(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.queryList(map);
    }

    @Override
    public Integer countByCondition(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.countBilOrderByMap(map, dataScope);
    }

    @Override
    public IPage<BilOrderVo> findByCondition(Page page, BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.selectBilOrderByMap(page, map, dataScope);
    }

    @Override
    public BigDecimal countPayment(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.countPaymentByMap(map,dataScope);
    }

    @Override
    public BigDecimal countEmpOrderByCondition(BilEmpOrderSearchVo bilEmpOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo", bilEmpOrderSearchVo);
        return baseMapper.countEmpOrderByMap(map);
    }

    @Override
    public IPage<BilOrderVo> findEmpOrderByCondition(Page page, BilEmpOrderSearchVo bilEmpOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo", bilEmpOrderSearchVo);
        return baseMapper.selectEmpOrderByMap(page, map);
    }

    @Override
    public RestResponse batchUpdate(List<String> ids, String discard, String remark) {
        RestResponse response = new RestResponse();
        Boolean flag = discard.equals(BaseConstants.BOOLEAN_OF_TRUE);
        for (String id : ids) {
            BilOrderEntity entity = getById(id);
            entity.setDiscard(flag);
            if(flag)
                entity.setPayState(PayStateEnum.CANCEL.getValue());
            else
                entity.setPayState(PayStateEnum.NOPAY.getValue());
            entity.setRemark(remark);
            updateById(entity);
        }
        return response.setSuccess(true).setMessage(flag ? "移入成功！" : "还原成功！");
    }

    @Override
    public List<HashMap> selectRefundInfoByOrderId(String id) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("orderId", id);
        return baseMapper.selectRefundInfoByOrderId(map);
    }

    @Override
    public RestResponse confirmPay(List<OrderConfirmPayVo> vos, String remark, Date payTime, String payWay, String tradeCode, String approvalId) throws Exception {
        RestResponse response = new RestResponse();
        for (OrderConfirmPayVo vo : vos) {
            BilOrderEntity order = this.getById(vo.getId());
            if (!ApprovalStateEnum.COMPLETE.getValue().equals(order.getApprovalState())) {
                return response.setSuccess(false).setMessage("账单" + order.getCode() + "未审核通过！");
            }
            bilDiscountLogService.updateState(Lists.newArrayList(order));
            if (ObjectUtil.isNotNull(vo.getPayment())) {
                if (order.getPayablePayment().compareTo(SafeCompute.add(vo.getPayment(), order.getActualPayment())) != 0) {
                    order.setPayState(PayStateEnum.PART.getValue());
                    order.setActualPayment(SafeCompute.add(vo.getPayment(), order.getActualPayment()));
                    order.updateById();
                    BilOrderPayInfoEntity info = orderPayInfoService.getOne(new QueryWrapper<BilOrderPayInfoEntity>().eq("approval_id", approvalId).eq("order_id", vo.getId()));
                    if (ObjectUtil.isNotNull(info)) {
                        info.setPayTime(payTime);
                        info.setRemark(remark);
                        info.setPayment(vo.getPayment());
                        info.updateById();
                    }
                    return response.setSuccess(true).setMessage("确认支付成功！");
                }
            }
            handleByOrder(order);
            order.setActualPayment(order.getPayablePayment());
            order.setPayState(PayStateEnum.PAYCONFIR.getValue());
            order.setPayTime(payTime);
            order.setRemark(remark);
            order.setPayWay(payWay);
            order.setTradeCode(tradeCode);
            order.updateById();
        }
        return response.setSuccess(true).setMessage("确认支付成功！");
    }

    @Override
    public RestResponse confirmRefund(String id, String remark, BigDecimal payment, Date payTime) {
        RestResponse response = new RestResponse();
        BilOrderEntity order = this.getById(id);
        if (PayStateEnum.REFUNDPENDING.getValue().equals(order.getPayState())) {
            order.setPayState(PayStateEnum.REFUNDED.getValue());
            order.setApprovalState(ApprovalStateEnum.COMPLETE.getValue());
            order.setActualPayment(payment);
            order.setPayTime(payTime);
            order.setRemark(remark);
            order.updateById();
            response.setSuccess(true).setMessage("确认退款成功！");
        } else {
            response.setSuccess(false).setMessage("确认失败，该账单非待退款账单！");
        }
        return response;
    }

    @Override
    public List<BilOrderEntity> getByIds(List<String> ids) {
        QueryWrapper<BilOrderEntity> queryWrapper = new QueryWrapper();
        queryWrapper.in("id", ids);
        return this.list(queryWrapper);
    }

    public BilOrderEntity getByCode(String code) {
        QueryWrapper<BilOrderEntity> wrapper = new QueryWrapper();
        wrapper.eq("code", code);
        return this.getOne(wrapper);
    }

    /**
     * 根据账单编号和支付状态查询账单
     * @param code
     * @param payState
     * @return
     */
    public BilOrderEntity getByCodeAndState(String code,String payState) {
        QueryWrapper<BilOrderEntity> wrapper = new QueryWrapper();
        wrapper.eq("code", code);
        if(StringUtils.isNotBlank(payState)){
            wrapper.eq("pay_state", payState);
        }
        return this.getOne(wrapper);
    }

    @Override
    public BilOrderEntity getByCondition(BilOrderEntity condition) {
        QueryWrapper<BilOrderEntity> queryWrapper = new QueryWrapper();
        if (StrUtil.isNotEmpty(condition.getOrderType())) {
            queryWrapper.eq("order_type", condition.getOrderType());
        }
        if (StrUtil.isNotEmpty(condition.getContractId())) {
            queryWrapper.eq("contract_id", condition.getContractId());
        }
        if (StrUtil.isNotEmpty(condition.getSourceId())) {
            queryWrapper.eq("source_id", condition.getSourceId());
        }
        if (StrUtil.isNotEmpty(condition.getPayState())) {
            queryWrapper.eq("pay_state", condition.getPayState());
        }
        if (StrUtil.isNotEmpty(condition.getPayerId())) {
            queryWrapper.eq("payer_id", condition.getPayerId());
        }
        if (ObjectUtil.isNotNull(condition.getFirst())) {
            if (condition.getFirst()) {
                queryWrapper.eq("is_first", BaseConstants.BOOLEAN_OF_TRUE);
            } else {
                queryWrapper.eq("is_first", BaseConstants.BOOLEAN_OF_FALSE);
            }
        }
        if (ObjectUtil.isNotNull(condition.getPush())) {
            if (condition.getPush()) {
                queryWrapper.eq("is_push", BaseConstants.BOOLEAN_OF_TRUE);
            } else {
                queryWrapper.eq("is_push", BaseConstants.BOOLEAN_OF_FALSE);
            }
        }
        if (StrUtil.isNotEmpty(condition.getDiscountLogId())) {
            queryWrapper.eq("discount_log_id", condition.getDiscountLogId());
        }
        queryWrapper.ne("pay_state", PayStateEnum.CANCEL.getValue());
        queryWrapper.eq("is_discard", BaseConstants.BOOLEAN_OF_FALSE);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<BilOrderEntity> getByContractId(String contractId) {
        QueryWrapper<BilOrderEntity> wrapper = new QueryWrapper();
        wrapper.eq("contract_id", contractId);
        wrapper.eq("is_discard","0");
        return this.list(wrapper);
    }

    @Override
    public List<BilOrderVo> getBillAndItemByContractId(String contractId) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("contractId",contractId);
        map.put("isDiscard","0");
        List<BilOrderVo> list = baseMapper.selectBilOrderAndItemByMap(map);
        return list;
    }

    public List<BilOrderEntity> findOrdersByMergeCode(String mergeCode) {
        QueryWrapper<BilOrderEntity> wrapper = new QueryWrapper();
        wrapper.like("merge_code", mergeCode);
        return this.list(wrapper);
    }

    public List<BilOrderEntity> getOrderListByCode(String orderCode,String payState) {
        List<BilOrderEntity> orderList = Lists.newArrayList();
        //BilOrderEntity orders = getByCode(orderCode);
        BilOrderEntity orders = getByCodeAndState(orderCode,payState);
        if (orders != null) {
            orderList.add(orders);
        } else {
            orderList = findOrdersByMergeCode(orderCode);
        }
        return orderList;
    }


    public BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                                    ContContractEntity contract, String payerId, DataFromEnum dataFromEnum) {
        return saveOrder(orderTypeEnum, isFirst, payment, sourceId, contract, payerId, null, null, dataFromEnum);
    }

    public BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                                    ContContractEntity contract, String payerId, Date orderStartDate, DataFromEnum dataFromEnum) {
        return saveOrder(orderTypeEnum, isFirst, payment, sourceId, contract, payerId, orderStartDate, null, dataFromEnum);
    }

    public BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                                    ContContractEntity contract, String payerId, String remark, DataFromEnum dataFromEnum) {
        return saveOrder(orderTypeEnum, isFirst, payment, sourceId, contract, payerId, null, remark, dataFromEnum);
    }

    private BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                                     ContContractEntity contract, String payerId, Date orderStartDate, String remark, DataFromEnum dataFromEnum) {
        BilOrderEntity entity = new BilOrderEntity();
        entity.setCode(resProjectService.getOrderCodeBySourceId(sourceId,orderTypeEnum.getValue(),"order",orderStartDate));
        entity.setOrderType(orderTypeEnum.getValue());
        entity.setFirst(isFirst);
        entity.setPayment(payment);
        entity.setPayablePayment(payment);
        entity.setSourceId(sourceId);
        entity.setRemark(remark);
        entity.setDataFrom(dataFromEnum.getValue());
        if (payment.compareTo(BigDecimal.ZERO) == 0) {
            if (Arrays.asList(OrderTypeEnum.DEPOSITREFUND.getValue()).contains(orderTypeEnum.getValue())) {
                entity.setPayState(PayStateEnum.REFUNDED.getValue());
            } else {
                entity.setPayState(PayStateEnum.PAYCONFIR.getValue());
            }
            entity.setActualPayment(payment);
            entity.setPayTime(new Date());
        } else if (payment.compareTo(BigDecimal.ZERO) > 0) {
            entity.setPayState(PayStateEnum.NOPAY.getValue());
        } else {
            entity.setPayState(PayStateEnum.REFUNDPENDING.getValue());
        }
        //处理推送时间
        ResSourceVo source = resSourceService.getInfoById(sourceId);
        handleOrderPushTime(entity, orderStartDate, source.getProjectId(), dataFromEnum);
        //处理账单消息
        if (ObjectUtil.isNotNull(contract)) {
            entity.setContractId(contract.getId());
            entity.setPayerId(contract.getSignerId());
            entity = handleOrder(entity, contract);
        }
        if (StrUtil.isNotEmpty(payerId)) {
            entity.setPayerId(payerId);
        }
        //保存主账单
        this.save(entity);
        return entity;
    }

    /**
     * 构建账单（仅用于生成合同pdf的费用清单）
     *
     * @param orderTypeEnum
     * @param payment
     * @return
     */
    public BilOrderEntity buildOrder(OrderTypeEnum orderTypeEnum, BigDecimal payment) {
        BilOrderEntity entity = new BilOrderEntity();
        entity.setOrderType(orderTypeEnum.getValue());
        entity.setPayment(payment);
        entity.setPayablePayment(payment);
        return entity;
    }

    private void handleOrderPushTime(BilOrderEntity entity, Date orderStartDate, String projectId, DataFromEnum dataFromEnum) {

        if (Arrays.asList(OrderTypeEnum.RENT.getValue(), OrderTypeEnum.SUBSIDY.getValue(), OrderTypeEnum.ENERGY.getValue(), OrderTypeEnum.FIXED.getValue()).contains(entity.getOrderType())) {
            if (dataFromEnum.getValue().equals(DataFromEnum.IMPORT.getValue())) {
                entity.setPush(true);
                entity.setPayableTime(new Date());
                entity.setPushTime(new Date());
            } else {
                //获取参数推送日期
                Integer pushD = Integer.parseInt(resProjectParaService.getByCode(ProjectParaEnum.PUSH_TIME.getValue(), projectId));
                //  modify by linderen on 20220207 修改账单生成时 推送账单日期获取方式 TODO
//                Date pushDate = DateUtil.offsetDay(DateUtil.beginOfMonth(DateUtil.offsetMonth(orderStartDate, -1)), pushD - 1);
                Date pushDate = DateUtil.offsetDay(orderStartDate, pushD - 4);
                // end
                entity.setPayableTime(orderStartDate);
                if (entity.getFirst()) {
                    entity.setPush(true);
                    entity.setPushTime(new Date());
                } else {
                    //当前时间小于推送时间
                    if (new DateTime().compareTo(pushDate) < 0) {
                        entity.setPush(false);
                        entity.setPushTime(pushDate);
                    } else {
                        entity.setPush(true);
                        entity.setPushTime(new Date());
                    }
                }
            }
        }
        if (Arrays.asList(OrderTypeEnum.DEPOSIT.getValue(),
                OrderTypeEnum.DEPOSITREFUND.getValue(),
                OrderTypeEnum.YAJIN.getValue(),
                OrderTypeEnum.FEE_YAJIN.getValue(),
                OrderTypeEnum.YAJINCHECKOUTTUI.getValue(),
                OrderTypeEnum.QITAFEE.getValue(),
                OrderTypeEnum.CHECKOUTTUI.getValue(),
                OrderTypeEnum.CHECKOUTSHOU.getValue(),
                OrderTypeEnum.HUANFANGFEE.getValue(),
                OrderTypeEnum.ZHUANZUFEE.getValue()).contains(entity.getOrderType())) {
            entity.setPush(true);
            entity.setPayableTime(new Date());
            entity.setPushTime(new Date());
        }
    }

    public BigDecimal addItem(List<BilOrderItemEntity> items, BilOrderItemEntity item) {
        BigDecimal payment = BigDecimal.ZERO;
        if (null != item && BigDecimal.ZERO.compareTo(item.getPayment()) < 0) {
            items.add(item);
            payment = item.getPayment();
        }
        return payment;
    }

    public BilOrderEntity handleOrder(BilOrderEntity order, ContContractEntity cont) {
        if (BaseConstants.BOOLEAN_OF_TRUE.equals(cont.getIsOrganize())) {
            order.setInvoiceType(cont.getInvoiceType());
            order.setInvoiceState(InvoiceStateEnum.INVOICING.getValue());
        }
        if (Arrays.asList(OrderTypeEnum.RENT.getValue(),
                OrderTypeEnum.ENERGY.getValue(),
                OrderTypeEnum.FIXED.getValue(),
                OrderTypeEnum.GASFEE.getValue(),
                OrderTypeEnum.QITAFEE.getValue(),
                OrderTypeEnum.CHECKOUTSHOU.getValue(),
                OrderTypeEnum.HUANFANGFEE.getValue(),
                OrderTypeEnum.ZHUANZUFEE.getValue()
        ).contains(order.getOrderType()) && order.getPayment().compareTo(BigDecimal.ZERO) > 0 && !order.getFirst() && !DataFromEnum.IMPORT.getValue().equals(order.getDataFrom())) {
            if (ObjectUtil.isNotNull(order.getPush()) && order.getPush() && ObjectUtil.isNotNull(order.getFirst()) && !order.getFirst() && OrderTypeEnum.RENT.getValue().equals(order.getOrderType())) {
                ResSourceVo sourceVo = resSourceService.getInfoById(order.getSourceId());
                this.sendMessage(order.getPayerId(),sourceVo.getProjectId());
            }
        }
        return order;
    }

    @Override
    @Transactional
    public void tradeSuccess(String orderCode, String tradeCode, String payWay, String params, String type) throws Exception {
        //防止并发回调
        synchronized (orderCode.intern()) {
            if ("notify".equals(type)) {
                //重复回调sign不处理
                BilInterfaceMsgEntity bilInterfaceMsg = bilInterfaceMsgService.findBySign(tradeCode);
                if (ObjectUtil.isNull(bilInterfaceMsg)) {
                    List<BilOrderEntity> orderList = getOrderListByCode(orderCode,PayStateEnum.NOPAY.getValue());
                    //1.更新账单状态
                    doNotify(orderList, tradeCode, payWay);
                    //2.更新优惠券状态
                    bilDiscountLogService.updateState(orderList);
                    //String note = payWay.equals(PayWayEnum.WEB.getValue()) ? "[企业]支付成功" : "支付成功";
                    String note = "支付成功";
                    bilInterfaceMsgService.addInterfaceMsg(null, params, tradeCode, orderCode, note);
                }
            }
        }
    }

    public String getPayerId(ContContractEntity cont, ContContractSourceRelEntity cs, String type) {
        String payerId = cont.getSignerId();
        if (PayerTypeEnum.STAFF.getValue().equals(type)) {
            List<ContCheckInUserEntity> unRemove = contCheckInUserService.getUnRemove(cs.getId());
            if (!CollectionUtils.isEmpty(unRemove)) {
                payerId = unRemove.get(0).getRenterId();
            }
        }
        return payerId;
    }

    public void sendMessage(String payerId,String projectId) {
        try {
            RenterEntity renter = renterFegin.getById(payerId);
            Map<String, Object> params = Maps.newHashMap();
            params.put("name", renter.getName());
            params.put("time", DateUtil.today());
            // modify by linderen on 20210714 修改通知方式为公众号通知 start
            sysMsgTemplateFegin.sendByProjectId(projectId,"170549",renter.getTel(), JSONUtil.toJsonStr(params));
            //wechatFegin.sendMsgByTempWithOpenid(renter.getOpenid(),"XX公寓", "提醒签约",
            //        "尊敬的"+renter.getName()+"，您好，"+DateUtil.today()+"账单已生成，请及时缴交，");
            //modify by linderen on 20210714 修改通知方式为公众号通知 end
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<BilOrderEntity> getNeedPushOrder() {
        return baseMapper.getNeedPushOrder();
    }

    @Override
    public List<BilOrderEntity> getNeedSyncOrder() {
        return baseMapper.getNeedSyncOrder();
    }

    @Override
    public BilOrderEntity findByImportVo(String contractId, String orderType, String orderItemType, String startTime, String endTime) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("contractId", contractId);
        map.put("orderType", orderType);
        map.put("orderItemType", orderItemType);
        if (!OrderItemTypeEnum.YAJIN.getValue().equals(orderType) && StrUtil.isNotEmpty(startTime) && StrUtil.isNotEmpty(endTime)) {
            map.put("startTime", startTime);
            map.put("endTime", endTime);
        }
        return baseMapper.getOrderByMap(map);
    }

    @Override
    public BilOrderEntity findByImportVo1(String contractId, String orderType, String orderItemType, String startTime, String endTime) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("contractId", contractId);
        map.put("orderType", orderType);
        map.put("orderItemType", orderItemType);
        if (!OrderItemTypeEnum.YAJIN.getValue().equals(orderType) && StrUtil.isNotEmpty(startTime) && StrUtil.isNotEmpty(endTime)) {
            map.put("startTime", startTime);
            map.put("endTime", endTime);
        }
        return baseMapper.getOrderByMap1(map);
    }


    public void cancelDueOrder() {
        QueryWrapper<BilOrderEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("pay_state", PayStateEnum.NOPAY.getValue());
        queryWrapper.eq("order_type", OrderTypeEnum.DEPOSIT.getValue());
        queryWrapper.lt("create_date", DateUtil.offsetDay(new Date(), -7));
        List<BilOrderEntity> list = this.list(queryWrapper);
        for (BilOrderEntity order : list) {
            order.setPayState(PayStateEnum.CANCEL.getValue());
            order.setCancelTime(new Date());
            order.updateById();
        }
    }


    public String extractNum(String str) {
        str = StrUtil.isBlank(str) ? "0" : str;
        str = str.trim();
        str = str.replaceAll("[^\\d.]+", "");
        return str;
    }

    public boolean judgeTime(String orderItemType, String starttime, String endtime, SimpleDateFormat simple, StringBuffer sb, int index, ContContractEntity contract, ContContractSourceRelEntity rel, Map<String, Object> map) throws ParseException {
        if (contract.getStartDate().getTime() - simple.parse(endtime).getTime() > 0) {
            sb.append("第" + index + "行合同开始时间大于导入月份的最后一天,请检查！<br>");
            return true;
        }
        if (contract.getEndDate().getTime() - simple.parse(starttime).getTime() < 0) {
            sb.append("第" + index + "行合同结束时间小于导入月份的第一天,请检查！<br>");
            return true;
        }
        if (contract.getStartDate().getTime() - simple.parse(starttime).getTime() > 0) {
            starttime = simple.format(contract.getStartDate());
        }
        if (contract.getEndDate().getTime() - simple.parse(endtime).getTime() < 0) {
            endtime = simple.format(contract.getEndDate());
        }
        map.put("orderItemType", orderItemType);
        map.put("sourceId", rel.getSourceId());
        map.put("starttime", starttime);
        map.put("endtime", endtime);
        if (isExitOrders(map)) {
            sb.append("第" + index + "行该合同" + OrderItemTypeEnum.getNameByValue(orderItemType) + "账单已存在！<br>");
            return true;
        }
        return false;
    }

    private void createOrderItem(List<BilOrderItemEntity> orderItemList, SimpleDateFormat out, SimpleDateFormat in, BigDecimal price, BigDecimal num, String orderItemType, String starttime, String endtime) throws ParseException {
        BilOrderItemEntity orderItem = new BilOrderItemEntity();
        orderItem.setStartTime(Timestamp.valueOf(out.format(in.parse(starttime))));
        orderItem.setEndTime(Timestamp.valueOf(out.format(in.parse(endtime))));
        orderItem.setOrderItemType(orderItemType);
        orderItem.setPayment(price);
        orderItem.setNum(num);
        orderItemList.add(orderItem);
    }

    boolean isExitOrders(Map<String, Object> map) {
        return baseMapper.isExitOrder(map).size() > 0;
    }

    @Override
    public Map<String, Object> getRefundDepositAndTurnover(String contractId, String sourceId) {
        Map<String, Object> result = new HashMap<String, Object>();
        boolean refundContainTurnover = true;
        Map<String, Object> query = new HashMap<>();
        query.put("contractId", contractId);
        query.put("sourceId", sourceId);
        //查询 实际退款的押金
        BigDecimal refundDoposit = bilOrderDao.getRefundDoposit(query);
        if (ObjectUtil.isNull(refundDoposit)) {
            refundDoposit = new BigDecimal(0);
            refundContainTurnover = false;
        }
        //查询实际退款的水电周转金
        BigDecimal refundTurnover = bilOrderDao.getRefundTurnover(query);
        if (ObjectUtil.isNull(refundTurnover)) {
            refundTurnover = new BigDecimal(0);
        }
        BigDecimal total = SafeCompute.add(refundDoposit, refundTurnover);
        result.put("refundContainTurnover", refundContainTurnover);
        result.put("refundTotal", total);
        result.put("refundTurnover", refundTurnover);
        result.put("refundDeposit", refundDoposit);
        return result;
    }

    @Override
    public Map<String, Object> getRefundDepositAndTurnover(String contractId) {
        Map<String, Object> result = new HashMap<String, Object>();
        boolean refundContainTurnover = true;
        Map<String, Object> query = new HashMap<>();
        query.put("contractId", contractId);
        //查询 实际退款的押金
        BigDecimal refundDoposit = bilOrderDao.getRefundDoposit(query);
        if (ObjectUtil.isNull(refundDoposit)) {
            refundDoposit = new BigDecimal(0);
        }
        //查询实际退款的水电周转金
        BigDecimal refundTurnover = bilOrderDao.getRefundTurnover(query);
        if (ObjectUtil.isNull(refundTurnover)) {
            refundTurnover = new BigDecimal(0);
            refundContainTurnover = false;
        }
        BigDecimal total = SafeCompute.add(refundDoposit, refundTurnover);
        result.put("refundContainTurnoverAllSource", refundContainTurnover);
        result.put("refundTotalAllSource", total);
        result.put("refundTurnoverAllSource", refundTurnover);
        result.put("refundDepositAllSource", refundDoposit);
        return result;
    }

    @Override
    public Map<String, Object> getDepositAndTurnover(String contractId, String sourceId,String releaseType,Date checkoutDate) {
        Map<String, Object> result = new HashMap<String, Object>();
        boolean containTurnover = true;

        boolean isPayedDeposit = true;
        boolean isPayedTurnover = true;
        Map<String, Object> query = new HashMap<>();
        query.put("contractId", contractId);
        query.put("sourceId", sourceId);
        BigDecimal deposit = null;

        ContContractEntity oldContract = contContractDao.getOldContractByNewContractId(contractId);
        if (ObjectUtil.isNotNull(oldContract) //
                && ContractStateEnum.STATUS_CHECKOUT.getValue().equals(oldContract.getState()) //旧合同是退租中
                && BaseConstants.BOOLEAN_OF_FALSE.equals(oldContract.getIsOrganize()))
            deposit = bilOrderDao.getIncDoposit(query);
        else {
            deposit = bilOrderDao.getDoposit(query);
        }
        if (ObjectUtil.isNull(deposit)) {
            deposit = new BigDecimal(0);
            isPayedDeposit = false;
        }
        //todo 如果提前退租则没收押金
        BigDecimal yajin = deposit;
        if(ReleaseTypeEnum.CHECKOUT.getValue().equals(releaseType)){
            ContContractEntity contractEntity = contContractDao.selectContractById(contractId);
            Date contEndDate = DateUtil.offsetDay(contractEntity.getEndDate(),-1);
            if(checkoutDate.before(contEndDate)){
                deposit = new BigDecimal(0);
                BilOrderItemEntity item = new BilOrderItemEntity();
                item.setPayment(yajin);
                result.put("breakFee",item);
            }
        }
        //todo 如果提前退租则没收押金
        //查询水电周转金
        BigDecimal turnover = bilOrderDao.getTurnover(query);
        if (ObjectUtil.isNull(turnover)) {
            containTurnover = false;
            turnover = new BigDecimal(0);
            isPayedTurnover = false;
        }
        BigDecimal total = SafeCompute.add(deposit, turnover);
        result.put("total", total);
        result.put("containTurnover", containTurnover);
        result.put("turnover", turnover);
        result.put("deposit", yajin);
        result.put("isPayedDeposit", isPayedDeposit);
        result.put("isPayedTurnover", isPayedTurnover);
        return result;
    }

    @Override
    public Map<String, Object> getDepositAndTurnover(String contractId) {
        Map<String, Object> result = new HashMap<String, Object>();
        boolean containTurnover = true;
        boolean isPayedDepositAll = true;
        boolean isPayedTurnoverAll = true;
        Map<String, Object> query = new HashMap<>();
        query.put("contractId", contractId);
        //查询 押金
        BigDecimal deposit = null;

        ContContractEntity oldContract = contContractDao.getOldContractByNewContractId(contractId);
        if (ObjectUtil.isNotNull(oldContract) //
                && ContractStateEnum.STATUS_CHECKOUT.getValue().equals(oldContract.getState()) //旧合同是退租中
                && BaseConstants.BOOLEAN_OF_FALSE.equals(oldContract.getIsOrganize()))
            deposit = bilOrderDao.getIncDoposit(query);
        else {
            deposit = bilOrderDao.getDoposit(query);
        }
        if (ObjectUtil.isNull(deposit)) {
            deposit = BigDecimal.ZERO;
            isPayedDepositAll = false;
        }
        //查询水电周转金
        BigDecimal turnover = bilOrderDao.getTurnover(query);
        if (ObjectUtil.isNull(turnover)) {
            containTurnover = false;
            turnover = new BigDecimal(0);
            isPayedTurnoverAll = false;
        }
        BigDecimal total = SafeCompute.add(deposit, turnover);
        result.put("totalAllSource", total);
        result.put("containTurnoverAllSource", containTurnover);
        result.put("turnoverAllSource", turnover);
        result.put("depositAllSource", deposit);
        result.put("isPayedDepositAll", isPayedDepositAll);
        result.put("isPayedTurnoverAll", isPayedTurnoverAll);
        return result;
    }

    @Override
    public String getReleaseOrderCode(String contractId, String sourceId) {
        Map<String, Object> map = new HashMap<>();
        map.put("contractId", contractId);
        map.put("sourceId", sourceId);
        return baseMapper.getReleaseOrderCode(map);
    }


    @Override
    public List<BilOrderEntity> getCancelOrder(Map<String, Object> map) {
        List<BilOrderEntity> cancelOrders = baseMapper.getCancelOrder(map);
        if (CollectionUtils.isEmpty(cancelOrders)) {
            cancelOrders = new ArrayList<>();
        }

        //未来期的人才补贴预付款
        BilOrderEntity currentSubsidyOrder = baseMapper.getCurrentSubsidyOrder(map);
        if (ObjectUtil.isNotNull(currentSubsidyOrder)) {
            List<String> types = new ArrayList<>();
            types.add(OrderItemTypeEnum.SUBSIDY.getValue());
            List<BilOrderItemEntity> currentItems = itemService.getItemsByOrderId(currentSubsidyOrder.getId(), types);
            map.put("checkoutDate", DateUtil.offsetDay(currentItems.get(currentItems.size() - 1).getEndTime(), 1));
            List<BilOrderEntity> cancelSubsidyOrders = baseMapper.getCancelSubsidyOrder(map);
            cancelOrders.addAll(cancelSubsidyOrders);
        }
        return cancelOrders;
    }

    @Override
    public Map<String, Object> showLastOrder(String contractId, String sourceId) throws Exception {
        log.info("contractId:" + contractId);
        Map<String, Object> result = new HashMap<>();

        BilOrderEntity lastOrder = this.getOne(new QueryWrapper<BilOrderEntity>().eq("contract_id", contractId)
                .eq("source_id", sourceId)
                .ne("order_type", OrderTypeEnum.YAJINCHECKOUTTUI.getValue())
                .eq("is_initial", "1").eq("is_push", BaseConstants.BOOLEAN_OF_TRUE).eq("is_discard", BaseConstants.BOOLEAN_OF_FALSE).orderByDesc("create_date"));
        //log.info(lastOrder.toString());
        if (ObjectUtil.isNotNull(lastOrder)) {
            List<Map<String, String>> amountDetails = itemService.findAmountDetails(lastOrder.getId());
            result.put(lastOrder.getId(), amountDetails);

            result.put("releaseFee", lastOrder);
            log.info(lastOrder.getPayerId());
            String payerId = lastOrder.getPayerId();
            log.info(payerId);
            if (StrUtil.isNotBlank(payerId)) {
                RenterEntity renter = renterFegin.getById(payerId);
                if (ObjectUtil.isNotNull(renter)) {
                    result.put("releaseFeePayerName", renter.getName());
                }
            }

            //查询子账单
            List<BilOrderItemEntity> releaseItemFee = itemService.getItemsByOrderId(lastOrder.getId());
            if (!CollectionUtils.isEmpty(releaseItemFee)) {
                releaseItemFee.forEach(item -> {
                    result.put(item.getOrderItemType(), item);
                });
            }
            result.put("releaseItemFee", releaseItemFee);

            BilOrderItemEntity zujinFee = itemService.getSumZujin(lastOrder.getId());
            result.put(zujinFee.getOrderItemType(), zujinFee);

        }
        //查询水电周转金费用
        BilOrderEntity turnoverFee = this.getOne(new QueryWrapper<BilOrderEntity>().eq("contract_id", contractId)
                .eq("source_id", sourceId).eq("order_type", OrderTypeEnum.YAJINCHECKOUTTUI.getValue()).eq("is_initial", "1").eq("is_push", BaseConstants.BOOLEAN_OF_TRUE).eq("is_discard", BaseConstants.BOOLEAN_OF_FALSE));
        if (ObjectUtil.isNotNull(turnoverFee)) {
            String payerId = turnoverFee.getPayerId();
            if (StrUtil.isNotBlank(payerId)) {
                RenterEntity renter = renterFegin.getById(payerId);
                result.put("turnoverFeePayerName", renter.getName());
            }

            List<Map<String, String>> amountDetails = itemService.findAmountDetails(turnoverFee.getId());
            result.put(turnoverFee.getId(), amountDetails);
            result.put("turnoverFee", turnoverFee);
            List<BilOrderItemEntity> turnoverItemFee = itemService.getItemsByOrderId(turnoverFee.getId());
            result.put("turnoverItemFee", turnoverItemFee);
        }
        return result;
    }

    @Override
    public void cancelDone(String ids, String status, String memo) {
        for (String id : ids.split(",")) {
            cancelOne(id, memo);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelOne(String id, String memo) {
        try {
            BilOrderEntity bil = baseMapper.selectById(id);
            bil.setPayState(PayStateEnum.CANCEL.getValue());
            bil.setCancelTime(new Date());
            bil.setRemark(memo);
            ContContractEntity contract = contContractService.getById(bil.getContractId());
            if (ObjectUtil.isNotNull(contract) && contParService.orderSynToCCB(bil.getContractId()) && bil.getPush()) {
                guomiService.cancelGuomiOrder(bil);
            }
            baseMapper.updateById(bil);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public boolean lastOrderIsCreated(String contractId) {
        List<BilOrderEntity> list = this.getLastOrder(contractId, null);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return true;
    }

    @Override
    public List<BilOrderEntity> getLastOrder(String contractId, String sourceId) {
        QueryWrapper<BilOrderEntity> query = new QueryWrapper<>();
        if (StrUtil.isNotBlank(contractId)) {
            query.eq("contract_id", contractId);
        }
        if (StrUtil.isNotBlank(sourceId)) {
            query.eq("source_id", sourceId);
        }
        query.eq("is_initial", "1").eq("is_discard", "0").eq("is_push", BaseConstants.BOOLEAN_OF_TRUE);
        query.ne("order_type",OrderTypeEnum.RENT.getValue());
        return this.list(query);
    }

    @Override
    @Transactional
    public String importRoomLivingFee(List<OrderImportVo> orders, String importMonth) throws Exception {
        StringBuffer sb = new StringBuffer();
        SimpleDateFormat simple = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        int index = 2;
        Map<String, Object> premonth = DateTimeUtil.getPreTimeOfFirstAndLast(importMonth);
        Map<String, Object> pretwomonth = DateTimeUtil.getPreTimeOfFirstAndLast((String) premonth.get("firstDay"));
        Map<String, Object> thismonth = DateTimeUtil.getTimeOfFirstAndLast(importMonth);
        List<String> fields = new ArrayList<String>() {{
            //add("publicPowerratePayment");
            //add("publicWaterPayment");
            add("roomPowerPayment");
            add("roomWaterPayment");
            //add("publicGasPayment");

            add("propertyPowerfee");
            add("propertyWaterfee");
        }};
        HashMap<String, String> typeMap = new HashMap<String, String>() {{
            //put("publicPowerratePayment", OrderItemTypeEnum.PUBLICPOWERRATE.getValue());
            //put("publicWaterPayment", OrderItemTypeEnum.PUBLICWATER.getValue());
            put("roomPowerPayment", OrderItemTypeEnum.DIANFEI.getValue());
            put("roomWaterPayment", OrderItemTypeEnum.UTILITIES.getValue());
            //put("publicGasPayment", OrderItemTypeEnum.PUBLICGAS.getValue());

            put("propertyPowerfee", OrderItemTypeEnum.PROPERTY_POWERFEE.getValue());
            put("propertyWaterfee", OrderItemTypeEnum.PROPERTY_WATERFEE.getValue());
        }};
        HashMap<String, String> numMap = new HashMap<String, String>() {{
            //put("publicPowerratePayment", "publicPowerrateNum");
            //put("publicWaterPayment", "publicWaterNum");
            put("roomPowerPayment", "roomPowerNum");
            put("roomWaterPayment", "roomWaterNum");
            //put("publicGasPayment", "publicGasNum");

            put("propertyPowerfee", "propertyPowerfee");
            put("propertyWaterfee", "propertyWaterfee");

        }};
        for (OrderImportVo vo : orders) {
            index++;
            if (StringUtils.isBlank(vo.getContractCode())) {
                sb.append("第" + index + "行合同编号不能为空！<br>");
                continue;
            }
            if (StringUtils.isBlank(vo.getContractCode())) {
                sb.append("第" + index + "行合同编号不能为空！<br>");
                continue;
            }
            if (StringUtils.isBlank(vo.getCode())) {
                sb.append("第" + index + "行房间不能为空！<br>");
                continue;
            }
            Map<String, Object> map = Maps.newHashMap();
            ContContractEntity contract = contContractService.getOne(new QueryWrapper<ContContractEntity>().eq("contract_code", vo.getContractCode()).ne("contract_type", ContractTypeEnum.CARPORT.getValue()));
            if (ObjectUtil.isNull(contract)) {
                sb.append("第" + index + "行合同编号不存在！<br>");
                continue;
            }
            map.put("contractId", contract.getId());
            if (!vo.getCode().contains("-")) {
                sb.append("第" + index + "行房间格式错误<br>");
                continue;
            }
            map.put("code", vo.getCode().substring(vo.getCode().indexOf("-") + 1));
            map.put("name", vo.getCode().substring(0, vo.getCode().indexOf("-")));
            map.put("projectId", UoneHeaderUtil.getProjectId());
            ContContractSourceRelEntity rel = contContractSourceRelService.selectByCondition(map);
            if (ObjectUtil.isNull(rel)) {
                sb.append("第" + index + "行房源关系不存在！<br>");
                continue;
            }
            ResProjectParaEntity paraEntity = resProjectParaService.getOne(new QueryWrapper<ResProjectParaEntity>().eq("project_id", rel.getProjectid()).eq("param_code", "XMCS_0053"));
            if (ObjectUtil.isNull(paraEntity)) {
                sb.append("第" + index + "下一期租金及固耗费用推送时间未配置！<br>");
                continue;
            }
            List<BilOrderItemEntity> orderItemList = Lists.newArrayList();
            Map<String, BigDecimal> op = Maps.newHashMap();
            op.put("payment", new BigDecimal(0));

            //公共电费
            initOrderItem(fields, typeMap, numMap, vo, map,
                    premonth, pretwomonth, thismonth, simple, sb, index,
                    contract, rel, orderItemList,
                    format, op);

            if (!CollectionUtils.isEmpty(orderItemList)) {
                String payerId = getPayerId(contract, rel, contract.getLifePayPayer());
                BilOrderEntity order = saveOrder(OrderTypeEnum.ENERGY, false, op.get("payment"), rel.getSourceId(), contract, payerId, vo.getRemark(), DataFromEnum.IMPORT);
                for (BilOrderItemEntity item : orderItemList) {
                    item.setOrderId(order.getId());
                    itemService.save(item);
                }
                guomiService.addGuomiOrder(order);

                Date sDate = simple.parse((String) premonth.get("firstDay"));

                UpdateWrapper<BilLiveErrorLogEntity> wrapper = new UpdateWrapper<>();
                wrapper.eq("contract_id", order.getContractId())
                        .eq("source_id", order.getSourceId())
                        .le("date(create_date)", DateUtil.formatDate(DateUtil.endOfMonth(sDate)))
                        .ge("date(create_date)", DateUtil.formatDate(sDate));
                errorLogDao.delete(wrapper);
            }
        }
        return sb.toString();
    }

    @Override
    @Transactional
    public String importRoomLivingFeeN(List<OrderImportVo> orders, String importMonth ,String monthEnd ) throws Exception {
        StringBuffer sb = new StringBuffer();
        SimpleDateFormat simple = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        int index = 2;
        Map<String, Object> premonth = Maps.newHashMap();
        premonth.put("firstDay", importMonth);
        premonth.put("lastDay", monthEnd);
        Map<String, Object> pretwomonth = Maps.newHashMap();
        pretwomonth.put("firstDay", importMonth);
        pretwomonth.put("lastDay", monthEnd);
        Map<String, Object> thismonth = Maps.newHashMap();
        thismonth.put("firstDay", importMonth);
        thismonth.put("lastDay", monthEnd);
        List<String> fields = new ArrayList<String>() {{
            add("roomPowerPayment");
            add("roomWaterPayment");
            add("propertyPowerfee");
            add("propertyWaterfee");
        }};
        HashMap<String, String> typeMap = new HashMap<String, String>() {{
            put("roomPowerPayment", OrderItemTypeEnum.DIANFEI.getValue());
            put("roomWaterPayment", OrderItemTypeEnum.UTILITIES.getValue());

            put("propertyPowerfee", OrderItemTypeEnum.PROPERTY_POWERFEE.getValue());
            put("propertyWaterfee", OrderItemTypeEnum.PROPERTY_WATERFEE.getValue());
        }};
        HashMap<String, String> numMap = new HashMap<String, String>() {{
            put("roomPowerPayment", "roomPowerNum");
            put("roomWaterPayment", "roomWaterNum");
            put("propertyPowerfee", "propertyPowerfee");
            put("propertyWaterfee", "propertyWaterfee");

        }};
        for (OrderImportVo vo : orders) {
            index++;
            if (StringUtils.isBlank(vo.getContractCode())) {
                sb.append("第" + index + "行合同编号不能为空！<br>");
                continue;
            }
            if (StringUtils.isBlank(vo.getContractCode())) {
                sb.append("第" + index + "行合同编号不能为空！<br>");
                continue;
            }
            if (StringUtils.isBlank(vo.getCode())) {
                sb.append("第" + index + "行房间不能为空！<br>");
                continue;
            }
            Map<String, Object> map = Maps.newHashMap();
            ContContractEntity contract = contContractService.getOne(new QueryWrapper<ContContractEntity>().eq("contract_code", vo.getContractCode()).ne("contract_type", ContractTypeEnum.CARPORT.getValue()));
            if (ObjectUtil.isNull(contract)) {
                sb.append("第" + index + "行合同编号不存在！<br>");
                continue;
            }
            map.put("contractId", contract.getId());
            if (!vo.getCode().contains("-")) {
                sb.append("第" + index + "行房间格式错误<br>");
                continue;
            }
            map.put("code", vo.getCode().substring(vo.getCode().indexOf("-") + 1).trim());
            map.put("name", vo.getCode().substring(0, vo.getCode().indexOf("-")).trim());
            map.put("projectId", UoneHeaderUtil.getProjectId());
            ContContractSourceRelEntity rel = contContractSourceRelService.selectByCondition(map);
            if (ObjectUtil.isNull(rel)) {
                sb.append("第" + index + "行房源关系不存在！<br>");
                continue;
            }
            ResProjectParaEntity paraEntity = resProjectParaService.getOne(new QueryWrapper<ResProjectParaEntity>().eq("project_id", rel.getProjectid()).eq("param_code", "XMCS_0053"));
            if (ObjectUtil.isNull(paraEntity)) {
                sb.append("第" + index + "下一期租金及固耗费用推送时间未配置！<br>");
                continue;
            }
            List<BilOrderItemEntity> orderItemList = Lists.newArrayList();
            Map<String, BigDecimal> op = Maps.newHashMap();
            op.put("payment", new BigDecimal(0));

            //公共电费
            initOrderItem(fields, typeMap, numMap, vo, map,
                    premonth, pretwomonth, thismonth, simple, sb, index,
                    contract, rel, orderItemList,
                    format, op);

            if (!CollectionUtils.isEmpty(orderItemList)) {
                String payerId = getPayerId(contract, rel, contract.getLifePayPayer());
                BilOrderEntity order = saveOrder(OrderTypeEnum.ENERGY, false, op.get("payment"), rel.getSourceId(), contract, payerId, vo.getRemark(), DataFromEnum.IMPORT);
                for (BilOrderItemEntity item : orderItemList) {
                    item.setOrderId(order.getId());
                    itemService.save(item);
                }
                guomiService.addGuomiOrder(order);

                Date sDate = simple.parse((String) premonth.get("firstDay"));

                UpdateWrapper<BilLiveErrorLogEntity> wrapper = new UpdateWrapper<>();
                wrapper.eq("contract_id", order.getContractId())
                        .eq("source_id", order.getSourceId())
                        .le("date(create_date)", DateUtil.formatDate(DateUtil.endOfMonth(sDate)))
                        .ge("date(create_date)", DateUtil.formatDate(sDate));
                errorLogDao.delete(wrapper);
            }
        }
        return sb.toString();
    }

    public void initOrderItem(List<String> fields, Map<String, String> typeMap, Map<String, String> numMap, OrderImportVo vo, Map<String, Object> map,
                              Map<String, Object> premonth, Map<String, Object> pretwomonth, Map<String, Object> thismonth, SimpleDateFormat simple, StringBuffer sb, int index,
                              ContContractEntity contract, ContContractSourceRelEntity rel, List<BilOrderItemEntity> orderItemList,
                              SimpleDateFormat format, Map<String, BigDecimal> op) throws NoSuchFieldException, IllegalAccessException, ParseException {
        for (String f : fields) {
            Class zz = vo.getClass();
            Field field = zz.getDeclaredField(f);
            field.setAccessible(true);
            String starttime = (String) premonth.get("firstDay");
            String endtime = (String) premonth.get("lastDay");
            if (ObjectUtil.isNotEmpty(field.get(vo))) {
                if (f.equals("publicGasPayment")) {
                    starttime = (String) pretwomonth.get("firstDay");
                } else if (f.equals("publicGasPayment")) {
                    starttime = (String) thismonth.get("firstDay");
                    endtime = (String) thismonth.get("lastDay");
                }
                String type = typeMap.get(f);
                if (judgeTime(type, starttime, endtime, simple, sb, index, contract, rel, map)) {
                    break;
                }
                BigDecimal onum = null;
                if (ObjectUtil.isNotNull(numMap.get(f)) && !Arrays.asList("propertyPowerfee", "propertyWaterfee").contains(f)) {
                    Field number = zz.getDeclaredField(numMap.get(f));
                    number.setAccessible(true);
                    if (ObjectUtil.isNotNull(number.get(vo))) {
                        onum = new BigDecimal(extractNum(number.get(vo).toString()));
                    }
                }
                createOrderItem(orderItemList, format, simple, new BigDecimal(extractNum(field.get(vo).toString())), onum, type, (String) map.get("starttime"), (String) map.get("endtime"));
                op.put("payment", op.get("payment").add(new BigDecimal(extractNum(field.get(vo).toString()))));
            }
        }
        ;
    }

    @Override
    public List<BilOrderVo> bilExport(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.bilExport(map, dataScope);
    }

    @Override
    public List<BilOrderVo> finaceExport(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.finaceExport(map, dataScope);
    }

    @Override
    public Map<String, BilOrderEntity> getCurrentAndNextRentOrder(Map<String, Object> map) {
        Map<String, BilOrderEntity> result = new HashMap<>();
        Map<String, Object> query = new HashMap<>();
        query.putAll(map);
        BilOrderEntity currentOrder = this.getCurrentRentOrder(query);
        if (ObjectUtil.isNotNull(currentOrder)) {
            List<String> types = new ArrayList<>();
            types.add(OrderItemTypeEnum.RENT.getValue());
            List<BilOrderItemEntity> currentItems = itemService.getItemsByOrderId(currentOrder.getId(), types);
            currentOrder.setItems(currentItems);
            result.put("currentOrder", currentOrder);
            if (!CollectionUtils.isEmpty(currentItems)) {
                Map<String, Object> par = new HashMap<>();
                par.putAll(map);
                par.put("checkoutDate", DateUtil.offsetDay(currentItems.get(currentItems.size() - 1).getEndTime(), 1));
                BilOrderEntity nextOrder = this.getCurrentRentOrder(par);
                if (ObjectUtil.isNotNull(nextOrder)) {
                    List<BilOrderItemEntity> nextItems = itemService.getItemsByOrderId(nextOrder.getId(), types);
                    nextOrder.setItems(nextItems);
                }
                result.put("nextOrder", nextOrder);
            }
        }
        return result;
    }

    /**
     * 根据合同id.房源id,获取当前账单（首先获取已支付--》部分支付--》未支付--》》已取消）
     *
     * @param map
     * @return
     */
    @Override
    public BilOrderEntity getCurrentRentOrder(Map<String, Object> map) {
        map.put("payState", PayStateEnum.PAYCONFIR.getValue());
        BilOrderEntity currentRentOrder = baseMapper.getCurrentRentOrder(map);
        if (ObjectUtil.isNull(currentRentOrder)) {
            map.put("payState", PayStateEnum.PART.getValue());
            currentRentOrder = baseMapper.getCurrentRentOrder(map);
            if (ObjectUtil.isNull(currentRentOrder)) {
                map.put("payState", PayStateEnum.NOPAY.getValue());
                currentRentOrder = baseMapper.getCurrentRentOrder(map);
                if (ObjectUtil.isNull(currentRentOrder)) {
                    map.put("payState", PayStateEnum.CANCEL.getValue());
                    currentRentOrder = baseMapper.getCurrentRentOrder(map);
                }
            }
        }
        return currentRentOrder;
    }

    @Override
    public BilOrderEntity getCurrentSubsidyOrder(Map<String, Object> map) {
        map.put("payState", PayStateEnum.PAYCONFIR.getValue());
        BilOrderEntity order = baseMapper.getCurrentSubsidyOrder(map);
        if (ObjectUtil.isNull(order)) {
            map.put("payState", PayStateEnum.PART.getValue());
            order = baseMapper.getCurrentSubsidyOrder(map);
            if (ObjectUtil.isNull(order)) {
                map.put("payState", PayStateEnum.NOPAY.getValue());
                order = baseMapper.getCurrentSubsidyOrder(map);
                if (ObjectUtil.isNull(order)) {
                    map.put("payState", PayStateEnum.CANCEL.getValue());
                    order = baseMapper.getCurrentSubsidyOrder(map);
                }
            }
        }
        return order;
    }

    @Override
    public BilOrderEntity getCurrentLiveOrder(Map<String, Object> map) {
        BilOrderEntity currentOrder = baseMapper.getCurrentLiveOrder(map);
        if (ObjectUtil.isNotNull(currentOrder)) {
            List<BilOrderItemEntity> currentItems = itemService.getItemsByOrderId(currentOrder.getId());
            currentOrder.setItems(currentItems);
        }
        return currentOrder;
    }

    @Override
    public BilOrderEntity getCurrentLiveOrderPb(Map<String, Object> map) {
        BilOrderEntity currentOrder = baseMapper.getCurrentLiveOrderPb(map);
        if (ObjectUtil.isNotNull(currentOrder)) {
            List<BilOrderItemEntity> currentItems = itemService.getItemsByOrderId(currentOrder.getId());
            currentOrder.setItems(currentItems);
        }
        return currentOrder;
    }

    private void doNotify(List<BilOrderEntity> orderList, String tradeCode, String payWay) throws Exception {
        for (BilOrderEntity order : orderList) {
            if (PayStateEnum.NOPAY.getValue().equals(order.getPayState())) {
                //根据账单类型处理
                handleByOrder(order);
                order.setActualPayment(order.getPayablePayment());
                order.setPayState(PayStateEnum.PAYCONFIR.getValue());
                order.setPayWay(payWay);
                //更新账单转账状态
                /*if (!OrderTypeEnum.CHECKOUTSHOU.getValue().equals(order.getOrderType())) {
                    //2.插入转账表
                    bilTransferService.saveBilTransfer(order, TransferTypeEnum.UNION_PAY);
                }*/
                order.setTradeCode(tradeCode);
                order.setPayTime(new Date());
                updateById(order);

                //3.付款成功后，自动申请开发票  2021.12.29
                //获得账单关联的合同
                /*try {
                    ContContractEntity contract = contContractService.getById(order.getContractId());
                    //机构合同不开票，押金，定金，退款，充值等非消耗类的账单类型，或账单金额为负数的，不需要开发票
                    if (!Arrays.asList("140", "5", "10", "200", "201", "333", "444","30","31","40","110","130").contains(order.getOrderType()) || order.getPayment().compareTo(new BigDecimal(0)) == -1) {
                        ResSourceEntity resSourceEntity = resSourceService.getById(order.getSourceId());
                        ResProjectParaEntity projectParaEntity =  resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.BIll_VALUE.getValue(),resSourceEntity.getProjectId());
                        if ("1".equals(projectParaEntity.getParamValue()) && null!=contract && "0".equals(contract.getIsOrganize())) {
                            reportInvoiceService.makeInvoice(order);
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }*/
            }
        }
    }

    @Override
    public void handleByOrder(BilOrderEntity order) throws Exception {
        ResSourceVo source = resSourceService.getInfoById(order.getSourceId());
        if (OrderTypeEnum.RENT.getValue().equals(order.getOrderType())) {
            // 如果当前合同还未办理入住自动发短信给房源所在梯位根据（项目排班）的负责管家，通知企业微信签到用户办理入住，
            if (order.getFirst() && BooleanUtil.isFalse(source.getCheckIn())) {
                RenterEntity renter = renterFegin.getById(order.getPayerId());
                String sourceName = source.getHouseName();
                // 企业微信签到用户
                UserEntity user = userFegin.getManager(source.getProjectId(), "handHouse:checkIn", BizTypeEnum.BIZ04.getValue());
                if (ObjectUtil.isNull(user)) {
                    //todo NEW-136 【我的账单】：首期租金账单支付完成后，账单的支付状态未更新
                    user = userFegin.getUserById("183c08b4e179faadd08bbc033cade9a1");
                } else {
                    // 发送短信的内容 “XXX您好，XXXX房间的租客已缴交首期租金可以联系租客办理入住，租客： XXXXXX,电话：XXXXXXX”
                    Map<String, Object> params = new HashMap<String, Object>();
                    //String sendTel = sysParaService.getByCode("SEND_TEL");
                    params.put("mobile", user.getTel());
                    params.put("gjname", user.getRealName());
                    params.put("housename", sourceName);
                    params.put("zkname", renter.getName());
                    params.put("zktel", renter.getTel());
                    params.put("template_code", "97212"); // 模板code
                    // modify by linderen on 20210714 修改通知方式为公众号通知 start
                    //sysMsgTemplateFegin.send(params);
                    String content = StrUtil.format("运营官{}您好，{}房间的租客已缴交首期租金可以联系租客办理入住，租客：{},电话：{}。", user.getRealName(),sourceName,renter.getName(),renter.getTel());
                    QywxAgentEntity agent = qywxAgentFegin.getByUserId(user.getId());
                    qywxMessageFegin.sendTextMessage(user.getQywechat(), agent.getQywxCropId(),agent.getQywxAgentId(),agent.getQywxAgentSecret(),content);
                    sysPushMsgService.createPushMsg(MsgTypeEnum.DAIBANRUZHU.getValue(), user.getId(), source.getId());
                }
            }
        }

        // 定金账单的处理
        /*if (OrderTypeEnum.DEPOSIT.getValue().equals(order.getOrderType())) {
            //1.修改房源状态 变成已预订
            source.setState(SourceStateEnum.BOOKED.getValue());
            resSourceService.updateById(source);
            // TODO 流程修改，定金账单付款后，先不生成合同，交由管家后台生成 v1.1.110
            //意向书企业签章
            ResProjectInfoEntity projectInfo = resProjectInfoService.getProjectInfo(source.getProjectId());
            RenterEntity renter = renterFegin.getById(order.getPayerId());
            this.uploadFdd(order,projectInfo,renter);
            //2.生成合同
//            if (StrUtil.isNotEmpty(order.getContractId())) {
//                throw new BusinessException("该账单已生成过合同！");
//            }
//            RenterEntity renter = renterFegin.getById(order.getPayerId());
//            //获取预定信息
//            SaleDemandEntity demand = saleCustomerService.getDemandBySourceIdAndUserId(source.getId(), renter.getId());
//            if (ObjectUtil.isNull(demand)) {
//                throw new BusinessException("未找到预定信息！");
//            }
//            ContContractEntity contract = new ContContractEntity();
//            if (ObjectUtil.isNotNull(demand.getCashPledge()) && ObjectUtil.isNotNull(demand.getPrice())) {
//                AlterPriceVo alterPriceVo = new AlterPriceVo();
//                alterPriceVo.setPrice(demand.getPrice());
//                alterPriceVo.setDeposit(demand.getCashPledge());
//                contract = contContractService.generateContract(renter, source, demand.getTempId(), null, null, null, alterPriceVo);
//            } else {
//                contract = contContractService.generateContract(renter, source, demand.getTempId(), null, null);
//            }
//            //3.更新账单
//            order.setContractId(contract.getId());
//            this.updateById(order);
//            //4.推送消息
//            sysPushMsgService.createPushMsg(MsgTypeEnum.YUDING.getValue(), null, source.getId());
            // TODO 流程修改，定金账单付款后，先不生成合同，交由管家后台生成 v1.1.110
        }
    */
        //生成票据  轨道支付未正常回调,暂时先关闭，测试没有问题才能开启 caizhanghe edit 2025-03-21
        try {
            if(OrderTypeEnum.DEPOSIT.getValue().equals(order.getOrderType()) || OrderTypeEnum.YAJIN.getValue().equals(order.getOrderType())){
                RenterEntity renter = renterFegin.getById(order.getPayerId());
                toCreateIntention(order,source.getSourceName(),renter.getName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional
    public RestResponse confirm(String id, String companyId) throws Exception {
        BilOrderConfirmEntity confirmEntity = orderConfirmDao.selectById(id);
        confirmEntity.setState("1");
        if (StrUtil.isNotBlank(companyId)) {
            ResProjectCompanyEntity companyEntity = companyService.getById(companyId);
            if (null != companyEntity) {
                confirmEntity.setBank(companyEntity.getBank()).setName(companyEntity.getName())
                        .setCode(companyEntity.getCode()).setCard(companyEntity.getCard());
            }
        }
        confirmEntity.updateById();
        if ("1".equals(confirmEntity.getType())) {
            //退款
            return confirmRefund(confirmEntity.getOrderId(), confirmEntity.getRemark(), confirmEntity.getPrice(), confirmEntity.getApplyTime());
        } else {
            //支付
            List<BilOrderEntity> orders = getByIds(Arrays.asList(confirmEntity.getOrderId().split(",")));
            if (orders.size() == 1) {
                BilOrderEntity order = orders.get(0);
                BigDecimal actualPayment = order.getActualPayment();
                if (null == actualPayment) {
                    actualPayment = BigDecimal.ZERO;
                }
                order.setActualPayment(actualPayment.add(confirmEntity.getPrice()));
                order.setPayState(PayStateEnum.PAYCONFIR.getValue());
                order.setPayTime(confirmEntity.getApplyTime());
                order.setRemark(confirmEntity.getRemark());
                order.setPayWay(confirmEntity.getPayWay());
                order.setTradeCode(confirmEntity.getTradeCode());
                order.updateById();
                handleByOrder(order);
            }

            for (BilOrderEntity order : orders) {
                order.setActualPayment(order.getPayablePayment());
                order.setPayState(PayStateEnum.PAYCONFIR.getValue());
                order.setPayTime(confirmEntity.getApplyTime());
                order.setRemark(confirmEntity.getRemark());
                order.setPayWay(confirmEntity.getPayWay());
                order.setTradeCode(confirmEntity.getTradeCode());
                order.updateById();
                this.handleByOrder(order);
                BilTransferEntity bilTransfer = new BilTransferEntity();//线下收款确认-确认支付-增加账单转账记录 caizhanghe edit 2024-04-15
                bilTransfer.setTransferType(TransferTypeEnum.OTHER.getValue());
                bilTransfer.setPayment(confirmEntity.getPrice());
                bilTransfer.setTransferTime(confirmEntity.getApplyTime());
                bilTransfer.setOrderId(order.getId());
                bilTransfer.setRemark(confirmEntity.getRemark());
                bilTransfer.setApprovalState(ApprovalStateEnum.COMPLETE.getValue());
                bilTransferService.save(bilTransfer);
            }

            kingdeeApiService.createReceiptBill(id, "2");

            return RestResponse.success("确认支付成功");
        }
    }

    @Override
    public List<BilOrderVo> getListByVo(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.selectAllBilByMap(map);
    }

    /**
     * 只查询部分账单
     * @param bilOrderSearchVo
     * @return
     */
    @Override
    public List<BilOrderVo> getPartListByVo(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.selectPartBilByMap(map);
    }

    /*
      生成机构退租时的清洁费，维修费，门禁卡费，其它费四种费用的账单
     */
    @Override
    public BilOrderEntity addOrgOrder(BigDecimal total, ContFrameContractEntity frameContract, SettleVo vo) {
        String code = getById(frameContract.getProjectId()).getCode();
        code = CodeUtil.codeCreate(code + "A");
        BilOrderEntity entity = new BilOrderEntity()
                .setPayState(PayStateEnum.NOPAY.getValue())
                .setPush(true)
                .setPayerId(frameContract.getSignerId())
                .setCode(code)
                .setPayablePayment(total)
                .setPayment(total)
                .setDataFrom(DataFromEnum.INPUT.getValue());
        ;
        entity.insertOrUpdate();
        //生成子账单
        //门禁卡成本
        BigDecimal accessCardFee = vo.getAccessCardFee();
        if (ObjectUtil.isNotNull(accessCardFee)) {
            BilOrderItemEntity item = new BilOrderItemEntity();
            item.setPayment(accessCardFee)
                    .setOrderItemType(OrderItemTypeEnum.MENJINCARDCOST.getValue())
                    .setOrderId(entity.getId());
            item.insertOrUpdate();
        }
        //维修费
        BigDecimal repairFee = vo.getRepairFee();
        if (ObjectUtil.isNotNull(repairFee)) {
            BilOrderItemEntity item = new BilOrderItemEntity();
            item.setPayment(repairFee)
                    .setOrderItemType(OrderItemTypeEnum.WEIXIUPEICHANGJIN.getValue())
                    .setOrderId(entity.getId());
            item.insertOrUpdate();
        }
        //清扫费
        BigDecimal cleanFee = vo.getCleanFee();
        if (ObjectUtil.isNotNull(cleanFee)) {
            BilOrderItemEntity item = new BilOrderItemEntity();
            item.setPayment(cleanFee)
                    .setOrderItemType(OrderItemTypeEnum.SWEEPFEE.getValue())
                    .setOrderId(entity.getId());
            item.insertOrUpdate();
        }

        //其他费用
        BigDecimal otherFee = vo.getOtherFee();
        if (ObjectUtil.isNotNull(otherFee)) {
            BilOrderItemEntity item = new BilOrderItemEntity();
            item.setPayment(otherFee)
                    .setOrderItemType(OrderItemTypeEnum.ELSEFEE.getValue())
                    .setOrderId(entity.getId());
            item.insertOrUpdate();
        }
        return entity;
    }

    @Override
    public BilCountVo getStatistics(Map<String, String> map) {
        //获取逾期账单数及当日应收款
        BilCountVo countVo = baseMapper.getStatistics(map);
        //获取当日实收款
        Map<String, BigDecimal> received = bilTransferService.periodReceive(new Date(), map);
        //BigDecimal monthReceive=bilTransferService.monthReceive(new Date(),map);

        countVo.setReceived(received.get("dayReceived"));
        countVo.setMonthReceived(received.get("monthReceived"));
        countVo.setYearReceived(received.get("yearReceived"));
        //countVo.setMonthReceived(monthReceive);
        return countVo;
    }

    /**
     * 根据请求header过滤项目
     *
     * @param bilOrderSearchVo
     * @return
     */
    private BilOrderSearchVo assembleSearchVo(BilOrderSearchVo bilOrderSearchVo) {
        //后台管理
        if (StrUtil.isNotEmpty(UoneHeaderUtil.getProjectId())) {
            bilOrderSearchVo.setProjectId(UoneHeaderUtil.getProjectId());
        }
        return bilOrderSearchVo;
    }

    /**
     * XX公寓 有用户id 不过滤数据权限
     *
     * @param bilOrderSearchVo
     * @return
     */
    private DataScope getDataScope(BilOrderSearchVo bilOrderSearchVo) {
        DataScope dataScope = null;
        if (StrUtil.isEmpty(bilOrderSearchVo.getUserId())) {
            String id = UoneSysUser.id();
            dataScope = new DataScope(UoneSysUser.id());
            dataScope.setProAlias("s");
            dataScope.setProjectFieldName("project_id");
            dataScope.setParAlias("s");
        }
        return dataScope;
    }


    @Override
    public boolean orderIsAllPayed(String contractId, String sourceId) {
        BilOrderSearchVo par = new BilOrderSearchVo();
        par.setContractId(contractId);
        par.setSourceId(sourceId);
        par.setIsDiscard(BaseConstants.BOOLEAN_OF_FALSE);
        ArrayList<String> strings = new ArrayList<>();
        strings.add(PayStateEnum.NOPAY.getValue());
        strings.add(PayStateEnum.PART.getValue());
        par.setStates(strings);
        List<BilOrderVo> orders = this.queryList(par);
        if (!CollectionUtils.isEmpty(orders)) {
            return false;
        }
        return true;
    }

    @Override
    public void releaseWaterAndEleTurnover(String contractId, String sourceId) throws Exception {
        if (StrUtil.isBlank(contractId)) {
            throw new Exception("合同id不能为空");
        }
        //查找水电周转金是否支付
        QueryWrapper<BilOrderEntity> query = new QueryWrapper<BilOrderEntity>()
                .eq("contract_id", contractId)
                .eq("order_type", OrderTypeEnum.FEE_YAJIN.getValue())//水电周转金账单
                .eq("pay_state", PayStateEnum.PAYCONFIR.getValue());
        if (StrUtil.isNotBlank(sourceId)) {
            query.eq("source_id", sourceId);
        }
        List<BilOrderEntity> feeYaJins = bilOrderDao.selectList(query);//已支付

        //水电周转金额
        if (!CollectionUtils.isEmpty(feeYaJins)) {
            String payerId = sysParaFegin.getByCode("PAYERID");
            Date date = new Date();
            for (BilOrderEntity feeYaJin : feeYaJins) {
                BilOrderEntity order = new BilOrderEntity();
                order.setCode(resProjectService.getOrderCodeBySourceId(feeYaJin.getSourceId(),"O","order"));
                order.setOrderType(OrderTypeEnum.YAJINCHECKOUTTUI.getValue())//水电周转金退款
                        .setPayState(PayStateEnum.REFUNDPENDING.getValue())//待退款状态
                        .setPayment((feeYaJin.getPayment()).negate())//水电周转金账单金额的负数
                        .setPush(true)
                        .setPushTime(date)
                        .setSourceId(feeYaJin.getSourceId())
                        .setInitial(true)
                        .setContractId(contractId)
                        .setPayerId(payerId);
                this.save(order);
                //生成退押金子账单
                BilOrderItemEntity feeRefund = new BilOrderItemEntity();
                feeRefund.setPayment(order.getPayment())
                        .setOrderItemType(OrderItemTypeEnum.YAJINCHECKOUTTUI.getValue())
                        .setOrderId(order.getId());
                itemService.save(feeRefund);
            }
        }
    }

    @Override
    public Map<String, BilOrderEntity> getCurrentAndNextFixedOrder(Map<String, Object> map) {
        Map<String, BilOrderEntity> result = new HashMap<>();

        List<String> orderItemTypes = new ArrayList<>();
        if (ObjectUtil.isNotNull(map.get("orderItemTypes"))) {
            orderItemTypes = (List<String>) map.get("orderItemTypes");
        }
        if (ObjectUtil.isNotNull(map.get("orderItemType"))) {
            orderItemTypes.add(map.get("orderItemType").toString());
        }
        map.put("orderType", OrderTypeEnum.SYNTHESIZE_BASEFEE.getValue());
        BilOrderEntity currentOrder = baseMapper.getCurrentLiveOrder(map);
        if (ObjectUtil.isNotNull(currentOrder)) {
            List<BilOrderItemEntity> currentItems = itemService.getItemsByOrderId(currentOrder.getId(), orderItemTypes);
            currentOrder.setItems(currentItems);

            Map<String, Object> par = new HashMap<>();
            par.putAll(map);
            par.put("checkoutDate", DateUtil.offsetDay(currentItems.get(currentItems.size()-1).getEndTime(), 1));
            BilOrderEntity nextOrder = baseMapper.getCurrentLiveOrder(par);
            if (ObjectUtil.isNotNull(nextOrder)) {
                List<BilOrderItemEntity> nextItems = itemService.getItemsByOrderId(nextOrder.getId(), orderItemTypes);
                nextOrder.setItems(nextItems);
            }
            result.put("nextOrder", nextOrder);
        }
        result.put("currentOrder", currentOrder);
        return result;
    }

    @Override
    public void fixOrderDone(String ids) {
        for (String id : ids.split(",")) {
            BilOrderEntity bil = baseMapper.selectById(id);
            List<BilOrderItemEntity> detail = itemService.list(new QueryWrapper<BilOrderItemEntity>().eq("order_id", bil.getId()));
            BigDecimal total = BigDecimal.ZERO;
            BigDecimal differ = BigDecimal.ZERO;
            for (BilOrderItemEntity d : detail) {
                if (ObjectUtil.isNotNull(d.getFixAfter())) {
                    d.setFixBefore(d.getPayment());
                    total = SafeCompute.add(total, d.getFixAfter());
                    d.setPayment(d.getFixAfter());
                } else {
                    if (!d.getOrderItemType().equals(OrderItemTypeEnum.ACTIVITY.getValue())) {
                        total = SafeCompute.add(total, d.getPayment());
                    }
                }
            }
            differ = SafeCompute.sub(bil.getPayment(), total);
            bil.setPayment(total);
            bil.setPayablePayment(SafeCompute.sub(bil.getPayablePayment(), differ));
            bil.updateById();
            itemService.updateBatchById(detail);
        }
    }

    @Override
    public Map<String, String> isApproval(String contractId, String sourceId) {
        Map<String, String> result = new HashMap<>();

        Map<String, Object> map = new HashMap<>();
        map.put("contractId", contractId);
        //map.put("sourceId",sourceId);
        // TODO modify by linderen  on 20210915 start
        List<BilOrderEntity> orders = bilOrderDao.queryApproval(map);
        //List<BilOrderEntity> orders = null;
        // TODO modify by linderen  on 20210915 end
        if (CollectionUtils.isEmpty(orders)) {
            result.put("code", "1");
        } else {
            result.put("code", "-1");
            result.put("msg", "【存在往期待支付的账单，请在支付后再操作】");
        }
        return result;
    }

    @Override
    public void update1(String id) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("id", id);
        baseMapper.selectRefundInfoByOrderId1(map);
    }

    @Override
    public int setPartNull(String id) {
        return baseMapper.setPartNull(id);
    }

    @Override
    public BigDecimal getTotal(BilOrderSearchVo bilOrderSearchVo) {
        List<BilOrderVo> bilOrderVos = findByCondition(bilOrderSearchVo);
        BigDecimal total = new BigDecimal(0);
        for (BilOrderVo vo : bilOrderVos) {
            total = total.add(vo.getPayment());
        }
        return total;
    }

    @Override
    public BilCountVo getMonthData(Date date) {
        DataScope scope = DataScope.newDataScope(UoneSysUser.id());
        scope.setProAlias("s");
        scope.setProjectFieldName("project_id");
        return baseMapper.getMonthData(scope, date);
    }

    @Override
    public BilCountVo getMonthData(Map<String,Object> maps) {
        DataScope scope = DataScope.newDataScope(UoneSysUser.id());
        scope.setProAlias("s");
        scope.setProjectFieldName("project_id");
        maps.put("date",new Date());
        //maps.put("manager",UoneSysUser.id());//轨道项目,管家端本月账单去掉签约管家查询 caizhanghe edit 2025-04-03
        return baseMapper.getMonthDatas(scope, maps);
    }

    @Override
    public Map<String, Object> countUnPayNum(Map<String, Object> search) {
        return baseMapper.countUnPayNum(search);
    }

    /**
     * 根据意向定金生成对应的签约合同
     *
     * @param paras
     * @return
     */
    @Transactional
    public Map<String, Object> toIntentionOld(Map<String, String> paras) throws Exception {
        Map<String, Object> maps = Maps.newHashMap();
        String orderId = paras.get("orderId");
        String sourceId = paras.get("sourceId");
        String startTime = paras.get("shareStartTime");
        String endTime = paras.get("shareEndTime");
        String discount = paras.get("discount");
        String payType = paras.get("payType");
        BilOrderEntity orderEntity = getById(orderId);
        //1.房源信息
        ResSourceSearchVo vo = new ResSourceSearchVo();
        vo.setId(orderEntity.getSourceId());
        IPage iPage = resSourceService.pricePage(new Page(), vo);
        ResSourceVo source = (ResSourceVo) iPage.getRecords().get(0);
        ResSourceVo newSource = null;
        RenterEntity renter = renterFegin.getById(orderEntity.getPayerId());
        //获取预定信息
        SaleDemandEntity demand = saleCustomerService.getDemandBySourceIdAndUserId(source.getId(), renter.getId());
        if (ObjectUtil.isNull(demand))
            throw new BusinessException("未找到预定信息！");
        // TODO 如果管家选择其它房源进行签约则需要修改原始房型状态在进行合同生成
        if (StrUtil.isNotBlank(sourceId)) { //  如果换房源的情况 是不是存在所换房源 价格不一样 要重新计算房源租金 押金
            //1.修改房源状态变成未出租
            source.setState(SourceStateEnum.UNRENT.getValue());
            resSourceService.updateById(source);
            //2. 新房源状态修改变成已出租
            vo.setId(sourceId);
            IPage iPages = resSourceService.pricePage(new Page(), vo);
            newSource = (ResSourceVo) iPages.getRecords().get(0);
            newSource.setState(SourceStateEnum.BOOKED.getValue());
            resSourceService.updateById(newSource);
            demand.setSourceId(sourceId);
            if(StrUtil.isNotBlank(discount) && StrUtil.isNotBlank(discount)) {
                demand.setDiscount(new BigDecimal(discount).divide(new BigDecimal(100)));
                demand.setPrice(newSource.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP));
                demand.setCashPledge(newSource.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP));
            }
            orderEntity.setSourceId(sourceId);
        }
        if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
            demand.setCheckInTime(DateUtil.parse(startTime));
            demand.setCheckOutTime(DateUtil.parse(endTime));
        }
        // TODO 如果生成合同时 修改了 价格策略 则要重新计算价格
        if(StrUtil.isNotBlank(discount) && StrUtil.isNotBlank(discount) && StrUtil.isBlank(sourceId)) {
            demand.setDiscount(new BigDecimal(discount).divide(new BigDecimal(100),2));
            if(PayTypeEnum.ONE_ONE.getValue().equals(demand.getPayType())){
                demand.setPrice(source.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP).add(new BigDecimal("50")));
                demand.setCashPledge(source.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP).add(new BigDecimal("50")));
            }else{
                demand.setPrice(source.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP));
                demand.setCashPledge(source.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP));
            }
        }
        if(StrUtil.isNotBlank(payType)){
            if(PayTypeEnum.ONE_ONE.getValue().equals(payType) && !PayTypeEnum.ONE_ONE.getValue().equals(demand.getPayType())){
                demand.setPrice(demand.getPrice().add(new BigDecimal(50)));
                demand.setCashPledge(demand.getCashPledge().add(new BigDecimal(50)));
            }
            if(!PayTypeEnum.ONE_ONE.getValue().equals(payType) && PayTypeEnum.ONE_ONE.getValue().equals(demand.getPayType())){
                demand.setPrice(demand.getPrice().subtract(new BigDecimal(50)));
                demand.setCashPledge(demand.getCashPledge().subtract(new BigDecimal(50)));
            }
            demand.setPayType(payType);
        }
        demand.insertOrUpdate();
        ContContractEntity contract = null ;
        if (ObjectUtil.isNotNull(demand.getCashPledge()) && ObjectUtil.isNotNull(demand.getPrice())) {
            AlterPriceVo alterPriceVo = new AlterPriceVo();
            alterPriceVo.setPrice(demand.getPrice());
            alterPriceVo.setDeposit(demand.getCashPledge());
            if(null != newSource)
                contract = contContractService.generateContract(renter, newSource, demand.getTempId(), null, null, null, alterPriceVo);
            else
                contract = contContractService.generateContract(renter, source, demand.getTempId(), null, null, null, alterPriceVo);
        } else {
            if(null!=newSource)
                contract =  contContractService.generateContract(renter, newSource, demand.getTempId(), null, null);
            else
                contract = contContractService.generateContract(renter, source, demand.getTempId(), null, null);
        }
        orderEntity.setIntention("2");
        orderEntity.setContractId(contract.getId());
        updateById(orderEntity);
        maps.put("success","创建合同成功!");
        return maps;
    }

    /**
     * 根据意向定金生成对应的签约合同
     *
     * @param paras
     * @return
     */
    @Override
    @Transactional
    public Map<String, Object> toIntention(Map<String, String> paras) throws Exception {
        Map<String, Object> maps = Maps.newHashMap();
        String orderId = paras.get("orderId");
        String sourceId = paras.get("sourceId");
        String startTime = paras.get("shareStartTime");
        String endTime = paras.get("shareEndTime");
        String discount = paras.get("discount");
        String payType = paras.get("payType");
        BilOrderEntity orderEntity = getById(orderId);
        //1.房源信息
        ResSourceSearchVo vo = new ResSourceSearchVo();
        vo.setId(orderEntity.getSourceId());
        IPage iPage = resSourceService.pricePage(new Page(), vo);
        ResSourceVo source = (ResSourceVo) iPage.getRecords().get(0);
        ResSourceVo newSource = null ;
        RenterEntity renter = renterFegin.getById(orderEntity.getPayerId());
        //获取预定信息
        SaleDemandEntity demand = saleCustomerService.getDemandBySourceIdAndUserId(source.getId(), renter.getId());
        if (ObjectUtil.isNull(demand))
            throw new BusinessException("未找到预定信息！");

        demand.setDiscount(new BigDecimal(discount).divide(new BigDecimal(100)));
        // TODO 如果管家选择其它房源进行签约则需要修改原始房型状态在进行合同生成
        if (StrUtil.isNotBlank(sourceId)) { //  如果换房源的情况 是不是存在所换房源 价格不一样 要重新计算房源租金 押金
            //1.修改房源状态变成未出租
            source.setState(SourceStateEnum.UNRENT.getValue());
            resSourceService.updateById(source);
            //2. 新房源状态修改变成已出租
            vo.setId(sourceId);
            IPage iPages = resSourceService.pricePage(new Page(), vo);
            newSource = (ResSourceVo) iPages.getRecords().get(0);
            newSource.setState(SourceStateEnum.BOOKED.getValue());
            resSourceService.updateById(newSource);
            demand.setSourceId(sourceId);
            // 根据折扣计算租金 押金
            demand.setPrice(newSource.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP));
            demand.setCashPledge(newSource.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP));
            orderEntity.setSourceId(sourceId);
        } else {
            // 根据折扣计算租金 押金
            demand.setPrice(source.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP));
            demand.setCashPledge(source.getPrice().multiply(new BigDecimal(discount)).divide(new BigDecimal(100),0,BigDecimal.ROUND_UP));
        }
        // 根据付款方式 计算租金押金
        if(PayTypeEnum.ONE_ONE.getValue().equals(payType)){
            demand.setPrice(demand.getPrice().add(new BigDecimal("50")));
            demand.setCashPledge(demand.getCashPledge().add(new BigDecimal("50")));
        }
        demand.setPayType(payType);
        // 根据选择的时间修改原先的合同开始结束时间
        demand.setCheckInTime(DateUtil.parse(startTime));
        demand.setCheckOutTime(DateUtil.parse(endTime));
        demand.insertOrUpdate();
        ContContractEntity contract = null ;
        if (ObjectUtil.isNotNull(demand.getCashPledge()) && ObjectUtil.isNotNull(demand.getPrice())) {
            AlterPriceVo alterPriceVo = new AlterPriceVo();
            alterPriceVo.setPrice(demand.getPrice());
            alterPriceVo.setDeposit(demand.getCashPledge());
            if(null != newSource)
                contract = contContractService.generateContract(renter, newSource, demand.getTempId(), null, null, null, alterPriceVo);
            else
                contract = contContractService.generateContract(renter, source, demand.getTempId(), null, null, null, alterPriceVo);
        } else {
            if(null!=newSource)
                contract =  contContractService.generateContract(renter, newSource, demand.getTempId(), null, null);
            else
                contract = contContractService.generateContract(renter, source, demand.getTempId(), null, null);
        }
        orderEntity.setIntention("2");
        orderEntity.setContractId(contract.getId());
        updateById(orderEntity);
        maps.put("success","创建合同成功!");
        return maps;
    }

    /**
     * 意向书
     * @param bill
     * @return
     * @throws Exception
     */
    public SysFileEntity createIntentionPdf(BilOrderEntity bill) throws Exception {
        String sourceId = bill.getSourceId();
        String userId = bill.getPayerId();
        String contractId = bill.getCcbBillId();
        RenterEntity renter = renterFegin.getById(userId);
        if(!"1".equals(renter.getIsVerify())){
            throw new BusinessException("用户未进行实名认证");
        }
        SaleDemandEntity saleDemand = saleCustomerService.getDemandBySourceIdAndUserId(sourceId,userId);
        if(saleDemand == null){
            throw new BusinessException("未查询到相关意向信息");
        }
        ResSourceVo source = resSourceService.getInfoById(sourceId);
        ContContractEntity contract = contContractService.getById(contractId);
        //获取意向书模板
        QueryWrapper<ContTempEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("project_id",source.getProjectId());
        queryWrapper.eq("subject_type",SubjectTypeEnum.INTENTION.getValue());
        List<ContTempEntity> temp = contTempDao.selectList(queryWrapper);
        if(temp.size()<1){
            return null;
        }
        ContTempRichEntity rich = richDao.selectOne(new QueryWrapper<ContTempRichEntity>().eq("temp_id",temp.get(0).getId()));
        //组装参数
        ResProjectParaEntity intentDeadLine = resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.INTENT_DEADLINE.getValue(),source.getProjectId());
        String intentDays = intentDeadLine==null?"7":intentDeadLine.getParamValue();
        String paperCode = contract.getPaperCode();
        Map<String,Object> map = new HashMap<>();
        map.put("intentDays",intentDays);
        map.put("source",source);
        map.put("saleDemand",saleDemand);
        map.put("renter",renter);
        map.put("paperCode",paperCode);
        String html = yngyContTempService.getTempHtmlByIntent(map,rich);
        //获取项目水印地址
        Map<String, Object> params = Maps.newHashMap();
        ResProjectParaEntity projectWatermark = resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.PDF_WATERMARK.getValue(),source.getProjectId());
        String watermarkImage = projectWatermark==null?"":projectWatermark.getParamValue();
        params.put("watermarkImage", watermarkImage);
        String pdfName = renter.getName()+"-"+source.getHouseName()+"-意向书.pdf";
        String url = pdfUtil.pdf(html, params, pdfName);

        //删除旧有意向书
        SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", contractId).eq("type", SysFileTypeEnum.INTENTION.getValue()));
        if (null != sysFileEntity) {
            //FileUtil.delete(sysFileEntity.getUrl());
            minioUtil.delete(sysFileEntity.getUrl());
            sysFileEntity.deleteById();
        }

        SysFileEntity pdfEntity = new SysFileEntity();
        pdfEntity.setFromId(contractId)
                .setName(pdfName)
                .setType(SysFileTypeEnum.INTENTION.getValue())
                .setUrl(url)
                .insert();
        return pdfEntity;
    }

    @Override
    public BilOrderEntity getCurrentReserve(Map<String, Object> maps) {
        return baseMapper.getCurrentReserve(maps);
    }

    @Override
    public BilCountVo getReserveCount(Map<String, String> para) {
        return baseMapper.getReserveCount(para);
    }

    public RestResponse uploadFdd(BilOrderEntity order,ResProjectInfoEntity projectInfo,RenterEntity renter) throws Exception {
        RestResponse response = new RestResponse();
        //法大大合同上传
        ReqUploadDocs reqUploaddocs = new ReqUploadDocs();
        //合同编号
        reqUploaddocs.setContractId(order.getId());
        //合同标题
        reqUploaddocs.setDocTitle(order.getCode());
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("from_id", order.getId());
        wrapper.eq("type", SysFileTypeEnum.INTENTION.getValue());
        SysFileEntity file = sysFileService.getOne(wrapper);
        if (ObjectUtil.isNull(file)) {
            response.setSuccess(false).setMessage("该意向书没有电子意向书，请联系客服！");
            return response;
        }
        reqUploaddocs.setDocUrl(FileUtil.getPath(file.getUrl()));
        reqUploaddocs.setDocType(".pdf");
        log.info("法大大上传合同开始");

        RestResponse rUpload = fadadaFegin.uploaddocs(reqUploaddocs);;
        log.info("返回code:" + rUpload);
        if (!"200".equals(rUpload.get("code").toString())) {
            return rUpload;
        }
        //法大大接口
        ReqExtsignAuto reqExtsignAuto = new ReqExtsignAuto();
        String transactionId = CodeUtil.generateUuid(true);
        //交易号(合同ID作为手动签署的交易号，因此自动签署的交易号随机生成)
        reqExtsignAuto.setTransactionId(transactionId);
        //合同编号
        reqExtsignAuto.setContractId(order.getCode());
        //合同标题
        reqExtsignAuto.setDocTitle(order.getCode());
        //定位关键字
        reqExtsignAuto.setSignKeyword("企业签章");
        //关键字策略
        reqExtsignAuto.setKeywordStrategy("2");//2:最后一个关键字签章
        //客户角色
        reqExtsignAuto.setClientRole("1");
        //客户编号（我们的客户编号）
        reqExtsignAuto.setCustomerId(renter.getFadadaCode());
        log.info("法大大进行自动签署");
        RestResponse rExtsign = fadadaFegin.extsignAuto(reqExtsignAuto, projectInfo.getSignature());
        if (!"200".equals(rExtsign.get("code").toString())) {
            return rExtsign;
        }
        //法大大合同下载接口
        log.info("法大大进行合同下载,合同id:" + order.getCode());
        String url = fadadaFegin.downloaddocs(order.getCode());
        if (StrUtil.isEmpty(url)) {
            response.setSuccess(false).setMessage("法大大合同下载id为空！");
            return response;
        }
        log.info("法大大合同下载结果:" + url);
        //将法大大返回的pdf上传到文件服务器
        //url = FileUtil.save(url, "pdf");
        url = minioUtil.save(url, "pdf");
        SysFileEntity fileEntity = new SysFileEntity();
        //先找到该合同原来对应的pdf，并删除
        List<SysFileEntity> files = sysFileService.getListByFromIdAndType(order.getId(), SysFileTypeEnum.INTENTION);
        if (!files.isEmpty()) {
            fileEntity = files.get(0);
        }
        String fileName = "";
        if (ObjectUtil.isNotNull(fileEntity)) {
            fileName = fileEntity.getName();
            log.info("删除未盖章合同:" + fileEntity.getId());
            sysFileService.delFile(fileEntity);

        }
        //将从法大大返回的pdf作为该合同的新pdf上传
        fileEntity = new SysFileEntity();
        fileEntity.setUrl(url).setFromId(order.getId()).setType(SysFileTypeEnum.INTENTION.getValue()).setName(fileName);
        sysFileService.save(fileEntity);
        return response.setSuccess(true).setData(fileEntity);
    }

    private void toCreateIntention(BilOrderEntity orderEntity ,String sourceName ,String payer){
        SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", orderEntity.getId()).eq("type", SysFileTypeEnum.DEPOSIT_BILL.getValue()));
        String url = "";
        if(sysFileEntity != null){
            return ;
        }
        ResSourceEntity source = resSourceService.getById(orderEntity.getSourceId());
        ResProjectParaEntity resProjectParaEntity = resProjectParaService.getByCodeAndProjectId("BILL_CODE",source.getProjectId());
        String billcodeStr = resProjectParaEntity.getParamValue();
        String prex = billcodeStr.substring(0,6);
        String prexYM = billcodeStr.substring(6,12);
        String yearMonth = DateUtil.format(new Date(),"yyyyMM");
        int billcode = Integer.valueOf(billcodeStr.substring(6));
        String suffix = yearMonth+"0001";
        if(yearMonth.equals(prexYM)){
            suffix = (billcode + 1)+"";
        }
        billcodeStr = prex+suffix;
        resProjectParaEntity.setParamValue(billcodeStr);
        resProjectParaService.saveOrUpdate(resProjectParaEntity);
        orderEntity.setBillsCode(billcodeStr);
        this.saveOrUpdate(orderEntity);
        String drawer = resProjectParaService.getByCodeAndProjectId("DRAWER",source.getProjectId()).getParamValue();
        Map<String,Object> beans = Maps.newHashMap();
        beans.put("username", payer);
        beans.put("year", DateUtil.thisYear());
        beans.put("month", DateUtil.thisMonth()+1);
        beans.put("day", DateUtil.thisDayOfMonth());
        beans.put("price", orderEntity.getPayablePayment());
        beans.put("amountCN", Convert.digitToChinese(orderEntity.getPayablePayment()));
        beans.put("drawer",drawer);
        if("10".equals(orderEntity.getOrderType())){
            beans.put("note", "该意向金在签订正式租赁合同之后将自动结转为房屋押金，押金不足部分将另行缴纳。");
            beans.put("project","意向金");
        }else if("5".equals(orderEntity.getOrderType())){
            beans.put("note", sourceName);
            beans.put("project","押金");
        }
        beans.put("billcode", billcodeStr);
        ResProjectEntity project = resProjectService.getById(source.getProjectId());
        beans.put("sealPic",sysFileService.getByFromIdAndType(project.getCompanyId(),"seal").getPath());
        try {
            String html = ResourceUtil.readUtf8Str("excel/export/bill.html");
            String pdfName = orderEntity.getCode()+"pj.pdf";
            //获取项目水印地址
            ResProjectParaEntity projectWatermark = resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.PDF_WATERMARK.getValue(),source.getProjectId());
            String watermarkImage = projectWatermark==null?"":projectWatermark.getParamValue();
            beans.put("watermarkImage", watermarkImage);
            url = pdfUtil.pdf(html,beans,pdfName);
            SysFileEntity pdfEntity = new SysFileEntity();
            pdfEntity.setFromId(orderEntity.getId())
                    .setName(pdfName)
                    .setType(SysFileTypeEnum.DEPOSIT_BILL.getValue())
                    .setUrl(url)
                    .insert();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public IPage<DailyOrderVo> findDailyByCondition(Page page, BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.getDailyByCondition(page, map, dataScope);
    }

    @Override
    public IPage<DailyOrderVo> findsummaryByCondition(Page page, BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.getSummaryByCondition(page, map, dataScope);
    }

    @Override
    public List<BilOrderVo> getBillPdf(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.getBillsPdf(map, dataScope);
    }

    @Override
    public List<DailyOrderVo> dailyExport(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.getDailyByCondition(map, dataScope);
    }

    @Override
    public List<BilOrderEntity> getOverdueOrder(String renterId) {
        return baseMapper.getOverdueOrder(renterId);
    }

    @Override
    public List<BilOrderEntity> getLastLifeFee() {
        return baseMapper.getLastLifeFee();
    }

    @Override
    public List<BilOverdueVo> getOverdueBil() {

        return baseMapper.getOverdueBil();

    }

    @Override
    public List<BilOrderVo> hydropowerExport(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);

        return baseMapper.hydropowerExport(UoneHeaderUtil.getProjectId(),bilOrderSearchVo.getFinacelExportDate());
    }

    @Override
    public List<Map<String, Object>> countPayAndClear(String cityCode) {
        return baseMapper.countPayAndClear(cityCode);
    }

    @Override
    public Map<String, Object> getOverdueFee(String projectId) {
        return baseMapper.getOverdueFee(projectId);
    }

    @Override
    public IPage<Map<String, Object>> selectPageByIntent(Page page, Map<String, Object> map) {
        DataScope scope = DataScope.newDataScope(UoneSysUser.id());
        scope.setProAlias("s");
        scope.setProjectFieldName("project_id");
        return baseMapper.selectIntentForAudit(page,scope,map);
    }

    @Override
    public void cancelUnpushOrdersByContractId(String contractId) {
        QueryWrapper query = new QueryWrapper();
        query.eq("contract_id",contractId);
        query.eq("is_push","0");
        List<BilOrderEntity> list = baseMapper.selectList(query);
        if(list != null && list.size()>0){
            for(BilOrderEntity order : list){
                // 设置订单状态为已取消
                order.setPayState(PayStateEnum.CANCEL.getValue());
                // 设置取消时间为当前时间，确保数据完整性
                order.setCancelTime(new Date());
            }
            this.updateBatchById(list,list.size());
        }
    }

    @Override
    public List<Map<String, Object>> getLastPaidOrderList(String contractId, String checkoutDate) {
        return baseMapper.getLastPaidOrderList(contractId,checkoutDate);
    }

    /**
     * 预览发票
     * @param invoiceBuyerVo
     * @return
     */
    public RestResponse previewInvoice(InvoiceBuyerVo invoiceBuyerVo) {
        //获取账单信息
        BilOrderVo order = this.getOrderById(invoiceBuyerVo.getOrderId());
        String orderType = order.getOrderType();
        //获取开票信息
        ResSourceVo source = resSourceService.getInfoById(order.getSourceId());
        String projectId = source.getProjectId();

        ResProjectEntity project = resProjectService.getById(projectId);
        SysCompanyEntity company = sysCompanyFegin.getById(project.getCompanyId());

        QueryWrapper query = new QueryWrapper();
        query.eq("project_id",projectId);
        query.eq("order_type",orderType);
        BigDecimal goodsTaxRate = null;//税率
        BigDecimal area = source.getArea();//面积
        if("1a32e5fb9aaef8b0e4901e06310dd237".equals(company.getTopId())){//漳州城投的税率从合同表取
            //税率
            ContContractEntity contract = contContractService.getById(order.getContractId());
            goodsTaxRate = contract.getTaxPoint().divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP);
            query.eq("invoice_tax_rate",goodsTaxRate.toPlainString());
            //计租面积
            area = contract.getLeaseholdArea();
            //获取房源地址
            ContContractSourceRelEntity csr = contContractSourceRelService.getListByContractId(contract.getId()).get(0);
            Map<String,Object> paras = Maps.newHashMap();
            paras.put("sourceIds",csr.getSourceId());
            ResSourceVo resSourceVo = resSourceService.getMultiSourceName(paras);
            String locations = addressMergerUtil.mergerAddress(resSourceVo.getLocations());
            source.setLocations(locations);
        }
        InvoiceAccountOrdertypeRelEntity rel = invoiceAccountOrdertypeRelService.getOne(query);
        if(rel == null){
            return RestResponse.failure("该账单类型未匹配开票账号");
        }
        //税率
        goodsTaxRate = new BigDecimal(rel.getInvoiceTaxRate());
        InvoiceAccountEntity invoiceAccount = invoiceAccountService.getById(rel.getAccountId());

        //含税金额
        BigDecimal goodsTotalPriceTax = order.getPayablePayment();
        //不含税金额
        BigDecimal goodsTotalPrice = goodsTotalPriceTax.divide((BigDecimal.ONE.add(goodsTaxRate)),2,BigDecimal.ROUND_HALF_UP);
        //税额
        BigDecimal goodsTotalTax = goodsTotalPriceTax.subtract(goodsTotalPrice).setScale(2,BigDecimal.ROUND_HALF_UP);
        //获取租客信息
        RenterEntity renter = null;
        try {
            renter = renterFegin.getById(order.getPayerId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        //组装发票信息
        BaiwangInvoiceVo invoiceVo = new BaiwangInvoiceVo();
        invoiceVo.setTaxNo(invoiceAccount.getInvoiceTaxNo());
        //销方信息
        invoiceVo.setSellerName(invoiceAccount.getInvoiceSellerName());
        invoiceVo.setSellerBankName(invoiceAccount.getInvoiceSellerBankName());
        invoiceVo.setSellerBankNumber(invoiceAccount.getInvoiceSellerBankNumber());

        //购方信息
        invoiceVo.setBuyerName(renter.getName());
        //有营业执照的显示税号
        if(IdTypeEnum.BUSINESS_LICENSE.getValue().equals(renter.getIdType())){
            invoiceVo.setBuyerTaxNo(renter.getIdNo());
        }
        //专票的显示银行信息
        if("01".equals(invoiceBuyerVo.getInvoiceTypeCode())){
            invoiceVo.setBuyerTaxNo(invoiceBuyerVo.getBuyerTaxNo());
        }

        BaiwangInvoiceDetailVo detailVo = new BaiwangInvoiceDetailVo();
        detailVo.setGoodsName(rel.getInvoiceGoodsName());
        detailVo.setGoodsTaxRate(goodsTaxRate.toString());
        detailVo.setGoodsTotalPriceTax(goodsTotalPriceTax.toString());
        detailVo.setGoodsTotalPrice(goodsTotalPrice.toString());
        detailVo.setGoodsTotalTax(goodsTotalTax.toString());

        //不动产经营租赁服务
        if("06".equals(rel.getInvoiceSpecialMark())){
            //获取项目信息
            String locations = source.getLocations();
            BaiwangLeaseInfoVo leaseInfoVo = new BaiwangLeaseInfoVo();
            leaseInfoVo.setLeaseDetailAddress(locations);
            leaseInfoVo.setLeaseHoldDateStart(DateUtil.formatDate(order.getStartDate()));
            leaseInfoVo.setLeaseHoldDateEnd(DateUtil.formatDate(order.getEndDate()));
            invoiceVo.setLeaseInfo(leaseInfoVo);

            BigDecimal price = goodsTotalPriceTax.divide(area,12,BigDecimal.ROUND_HALF_UP);
            detailVo.setGoodsPrice(price.toString());
            detailVo.setGoodsQuantity(area.toString());
            detailVo.setGoodsUnit("平方米");
        }
        JSONObject invoiceJson = JSONUtil.parseObj(invoiceVo);
        invoiceJson.put("detail",detailVo);
        invoiceJson.put("invoiceDate",DateUtil.format(new Date(),"yyyy年MM月dd日"));
        invoiceJson.put("priceTaxCn",Convert.digitToChinese(goodsTotalPriceTax));
        invoiceJson.put("invoiceType","01".equals(invoiceBuyerVo.getInvoiceTypeCode())?"增值税专用发票":"普通发票");
        return RestResponse.success().setData(invoiceJson);
    }

    @Override
    @Transactional
    public RestResponse baiwangInvoice(InvoiceBuyerVo invoiceBuyerVo) {
        StringBuffer sbf = new StringBuffer();
        String[] orderIds = invoiceBuyerVo.getOrderId().split(",");
        for(String orderId: orderIds){
            //获取账单信息
            BilOrderVo order = this.getOrderById(orderId);
            
            // 状态检查：确保只有"未开票"状态的订单才能开票
            if (!InvoiceStateEnum.UNBILLED.getValue().equals(order.getInvoiceState())) {
                String currentStatus = InvoiceStateEnum.getNameByValue(order.getInvoiceState());
                return RestResponse.failure("订单[" + order.getCode() + "]当前状态为[" + currentStatus + "]，不允许重复开票");
            }
            
            // 立即更新订单状态为"开票中"，避免并发开票
            BilOrderEntity updateStatusOrder = new BilOrderEntity();
            updateStatusOrder.setId(orderId);
            updateStatusOrder.setInvoiceState(InvoiceStateEnum.INVOICING.getValue());
            boolean updateSuccess = this.updateById(updateStatusOrder);
            if (!updateSuccess) {
                return RestResponse.failure("更新订单状态失败，可能是订单已被修改");
            }
            
            log.info("订单状态已更新为[开票中], 订单号: {}", order.getCode());
            
            String orderType = order.getOrderType();
            //获取开票信息
            ResSourceVo source = resSourceService.getInfoById(order.getSourceId());
            String projectId = source.getProjectId();

            ResProjectEntity project = resProjectService.getById(projectId);
            SysCompanyEntity company = sysCompanyFegin.getById(project.getCompanyId());

            QueryWrapper query = new QueryWrapper();
            query.eq("project_id",projectId);
            query.eq("order_type",orderType);
            BigDecimal goodsTaxRate = null;//税率
            BigDecimal area = source.getArea();//面积
            if("1a32e5fb9aaef8b0e4901e06310dd237".equals(company.getTopId())){//漳州城投的税率从合同表取
                //税率
                ContContractEntity contract = contContractService.getById(order.getContractId());
                goodsTaxRate = contract.getTaxPoint().divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP);
                query.eq("invoice_tax_rate",goodsTaxRate.toPlainString());
                //计租面积
                area = contract.getLeaseholdArea();
                //获取房源地址
                ContContractSourceRelEntity csr = contContractSourceRelService.getListByContractId(contract.getId()).get(0);
                Map<String,Object> paras = Maps.newHashMap();
                paras.put("sourceIds",csr.getSourceId());
                ResSourceVo resSourceVo = resSourceService.getMultiSourceName(paras);
                String locations = addressMergerUtil.mergerAddress(resSourceVo.getLocations());
                source.setLocations(locations);
            }
            InvoiceAccountOrdertypeRelEntity rel = invoiceAccountOrdertypeRelService.getOne(query);
            if(rel == null){
                // 开票失败，回滚状态为未开票
                rollbackInvoiceStatus(orderId, "该账单类型未匹配开票账号");
                return RestResponse.failure("该账单类型未匹配开票账号");
            }
            //税率
            goodsTaxRate = new BigDecimal(rel.getInvoiceTaxRate());
            InvoiceAccountEntity invoiceAccount = invoiceAccountService.getById(rel.getAccountId());
            //查询对应税号下的数电账号当前的登录状态
            RestResponse loginResult = baiwangFegin.getLoginResult("SAAS",invoiceAccount.getInvoiceTaxNo(),invoiceAccount.getInvoiceTerminalCode());
            if(!loginResult.getSuccess()){


                // 开票失败，回滚状态为未开票
                String errorMsg = "百望云登录失败：" + loginResult.getMessage();
                rollbackInvoiceStatus(orderId, errorMsg);
                loginResult.setMessage(errorMsg);
                return loginResult;
            }else{
                /*JSONObject res = JSONUtil.parseObj(loginResult.get("data"));
                if(!"1".equals(res.getStr("loginStatus"))){//未登录
                    return RestResponse.failure("数电账号未登录，请先在百望云平台登录");
                }
                //查询对应税号下的数电账号当前的开票实人认证（刷脸）状态及最近一次认证成功的时间
                RestResponse certifyResult = baiwangFegin.getCertifyResult("SAAS",invoiceAccount.getInvoiceTaxNo(),invoiceAccount.getInvoiceTerminalCode());
                if(!certifyResult.getSuccess()){
                    return certifyResult;
                }else{
                    JSONObject res1 = JSONUtil.parseObj(certifyResult.get("data"));
                    if(!"1".equals(res1.getStr("certificationStatus"))){//未登录
                        return RestResponse.failure("数电账号未认证，请先在百望云平台认证");
                    }
                }*/
            }

            //含税金额
            BigDecimal goodsTotalPriceTax = order.getPayablePayment();
            //不含税金额
            BigDecimal goodsTotalPrice = goodsTotalPriceTax.divide((BigDecimal.ONE.add(goodsTaxRate)),2,BigDecimal.ROUND_HALF_UP);
            //税额
            BigDecimal goodsTotalTax = goodsTotalPriceTax.subtract(goodsTotalPrice).setScale(2,BigDecimal.ROUND_HALF_UP);
            //获取租客信息
            RenterEntity renter = null;
            try {
                renter = renterFegin.getById(order.getPayerId());
            } catch (Exception e) {
                e.printStackTrace();
            }
            //BizAccountEntity bizAccount = bizAccountService.getByRenterId(order.getPayerId());
            //组装发票信息
            BaiwangInvoiceVo invoiceVo = new BaiwangInvoiceVo();
            char randomChar = (char)(new Random().nextInt(26)+'A');
            invoiceVo.setOrderNo(order.getCode()+randomChar);
            invoiceVo.setOrderDateTime(DateUtil.formatDateTime(order.getStartDate()));
            invoiceVo.setInvoiceTerminalCode(invoiceAccount.getInvoiceTerminalCode());
            invoiceVo.setTaxNo(invoiceAccount.getInvoiceTaxNo());
            //销方信息
            invoiceVo.setSellerName(invoiceAccount.getInvoiceSellerName());
            invoiceVo.setSellerAddress(invoiceAccount.getInvoiceSellerAddress());
            invoiceVo.setSellerPhone(invoiceAccount.getInvoiceSellerPhone());
            invoiceVo.setSellerBankName(invoiceAccount.getInvoiceSellerBankName());
            invoiceVo.setSellerBankNumber(invoiceAccount.getInvoiceSellerBankNumber());
            invoiceVo.setDisplaySeller("1");

            invoiceVo.setDrawer(invoiceAccount.getInvoiceDrawer());
            invoiceVo.setPayee(invoiceAccount.getInvoicePayee());
            invoiceVo.setChecker(invoiceAccount.getInvoiceChecker());

            //购方信息
            invoiceVo.setBuyerName(renter.getName());
            invoiceVo.setBuyerAddress(renter.getAddress());
            invoiceVo.setBuyerTelephone(renter.getTel());
            //有营业执照的显示税号
            if(IdTypeEnum.BUSINESS_LICENSE.getValue().equals(renter.getIdType())){
                invoiceVo.setBuyerTaxNo(renter.getIdNo());
            }
            //专票的显示银行信息
            if("01".equals(invoiceBuyerVo.getInvoiceTypeCode())){
                invoiceVo.setBuyerTaxNo(invoiceBuyerVo.getBuyerTaxNo());
                invoiceVo.setBuyerName(invoiceBuyerVo.getBuyerName());
                invoiceVo.setBuyerAddress(invoiceBuyerVo.getBuyerAddress());
                invoiceVo.setBuyerTelephone(invoiceBuyerVo.getBuyerTelephone());
                invoiceVo.setBuyerBankName(invoiceBuyerVo.getBuyerBankName());
                invoiceVo.setBuyerBankNumber(invoiceBuyerVo.getBuyerBankNumber());
                invoiceVo.setDisplayBuyer("1");
            }

            invoiceVo.setInvoiceType("1");
            invoiceVo.setInvoiceTypeCode(invoiceBuyerVo.getInvoiceTypeCode());
            invoiceVo.setPriceTaxMark("1");
            invoiceVo.setPushEmail(renter.getEmail());
            invoiceVo.setPushPhone(renter.getTel());
            invoiceVo.setOrderTotalAmount(order.getPayablePayment().toString());
            BaiwangInvoiceDetailVo detailVo = new BaiwangInvoiceDetailVo();
            detailVo.setGoodsName(rel.getInvoiceGoodsName());
            detailVo.setGoodsCode(rel.getInvoiceGoodsCode());
            detailVo.setGoodsTaxRate(goodsTaxRate.toString());
            detailVo.setGoodsTotalPriceTax(goodsTotalPriceTax.toString());
            detailVo.setGoodsTotalPrice(goodsTotalPrice.toString());
            detailVo.setGoodsTotalTax(goodsTotalTax.toString());

            detailVo.setPreferentialMark(rel.getInvoicePreferentialMark());
            detailVo.setVatSpecialManagement(rel.getInvoiceVatSpecialManagement());

            String remarks = null;
            //不动产经营租赁服务
            if("06".equals(rel.getInvoiceSpecialMark())){
                invoiceVo.setInvoiceSpecialMark("06");
                //获取项目信息
                String locations = source.getLocations();
                if(StrUtil.isBlank(locations)){
                    SysAreaEntity province = sysAreaService.getById(project.getProvinceId());
                    SysAreaEntity city = sysAreaService.getById(project.getCityId());
                    SysAreaEntity district = sysAreaService.getById(project.getDistrictId());
                    locations = province.getAreaName()+city.getAreaName()+district.getAreaName()+project.getAddress()+source.getHouseName();
                }
                String regex = "(.+?)省(.+?)市(.+?)区"; // 适配省份、城市和区县的格式
                String provinceName = ReUtil.get(regex, locations,1)+"省";
                String cityName = ReUtil.get(regex, locations,2)+"市";
                String districtName = ReUtil.get(regex, locations,3)+"区";
                String detailAddress = StrUtil.subAfter(locations, "区", false);
                BaiwangLeaseInfoVo leaseInfoVo = new BaiwangLeaseInfoVo();
                leaseInfoVo.setLeaseAddress(StrUtil.format("{}&{}&{}",provinceName,cityName,districtName));
                leaseInfoVo.setLeaseDetailAddress(detailAddress);
                leaseInfoVo.setLeaseCrossSign("0");
                leaseInfoVo.setLeaseHoldDateStart(DateUtil.formatDate(order.getStartDate()));
                leaseInfoVo.setLeaseHoldDateEnd(DateUtil.formatDate(order.getEndDate()));
                leaseInfoVo.setLeaseAreaUnit("2");
                invoiceVo.setLeaseInfo(leaseInfoVo);

                BigDecimal price = goodsTotalPriceTax.divide(area,12,BigDecimal.ROUND_HALF_UP);
                detailVo.setGoodsPrice(price.toString());
                detailVo.setGoodsQuantity(area.toString());
                detailVo.setGoodsUnit("平方米");
            }
            List<BaiwangInvoiceDetailVo> details = Lists.newArrayList();
            details.add(detailVo);
            invoiceVo.setInvoiceDetailList(details);

            //调用开票接口
            RestResponse res = baiwangFegin.baiwangInvoice("SAAS",invoiceVo);
            JSONObject resData = JSONUtil.parseObj(res.get("data"));
            
            // 根据开票结果更新订单和发票状态
            BilOrderEntity resultOrder = new BilOrderEntity();
            resultOrder.setId(orderId);
            
            if(res.getSuccess()){
                String serialNo = resData.getStr("serialNo");
                InvoiceEntity invoice = new InvoiceEntity();
                invoice.setInvoiceType("1");
                invoice.setOrderId(orderId);
                invoice.setTaxName(detailVo.getGoodsName());
                invoice.setTaxPayment(goodsTotalPriceTax);
                invoice.setTaxDate(new Date());
                invoice.setAddress(source.getHouseName());
                invoice.setTel(renter.getTel());
                invoice.setIdNo(renter.getIdNo());
                invoice.setName(invoiceVo.getSellerName());
                invoice.setBank(invoiceVo.getSellerBankName());
                invoice.setCard(invoiceVo.getSellerBankNumber());
                invoice.setTaxAmount(goodsTotalTax);
                invoice.setSerialNo(serialNo);
                invoice.insertOrUpdate();
                
                resultOrder.setInvoicePayment(order.getPayablePayment());
                resultOrder.setInvoiceType(InvoiceTypeEnum.PERSONAL.getValue());
                // 状态保持"开票中"，后续由定时任务更新为"已开票"
                resultOrder.setInvoiceState(InvoiceStateEnum.INVOICING.getValue());
                
                List<BilOrderItemEntity> entityList = itemService.getItemsByOrderId(order.getId());
                for(BilOrderItemEntity entity:entityList){
                    entity.setInvoiceId(invoice.getId());
                    entity.insertOrUpdate();
                }
                resultOrder.insertOrUpdate();
            }else{
                String message = resData.getStr("subMessage");
                // 开票失败，回滚状态为未开票
                rollbackInvoiceStatus(orderId, message);
                return RestResponse.failure("开票失败: " + message);
            }
        }
        return RestResponse.success();
    }
    
    /**
     * 回滚订单开票状态为未开票
     * @param orderId 订单ID
     * @param errorMsg 错误信息
     */
    private void rollbackInvoiceStatus(String orderId, String errorMsg) {
        try {
            BilOrderEntity order = this.getById(orderId);
            if (order != null) {
                BilOrderEntity updateOrder = new BilOrderEntity();
                updateOrder.setId(orderId);
                updateOrder.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());
                this.updateById(updateOrder);
            }
        } catch (Exception e) {
            log.error("回滚订单开票状态失败: {}", e.getMessage());
        }
    }

    @Override
    @Transactional
    public RestResponse baiwangRedInvoice(String invoiceIds,String remark) {
        StringBuffer sbf = new StringBuffer();
        String[] ids = invoiceIds.split(",");
        for(String invoiceId: ids){
            //获取开票信息
            InvoiceEntity invoice = reportInvoiceService.getById(invoiceId);
            //获取账单信息
            BilOrderVo order = this.getOrderById(invoice.getOrderId());
            String orderType = order.getOrderType();
            //获取开票信息
            ResSourceVo source = resSourceService.getInfoById(order.getSourceId());
            String projectId = source.getProjectId();

            ResProjectEntity project = resProjectService.getById(projectId);
            SysCompanyEntity company = sysCompanyFegin.getById(project.getCompanyId());

            QueryWrapper query = new QueryWrapper();
            query.eq("project_id",projectId);
            query.eq("order_type",orderType);
            BigDecimal goodsTaxRate = null;//税率
            BigDecimal area = source.getArea();//面积
            if("1a32e5fb9aaef8b0e4901e06310dd237".equals(company.getTopId())){//漳州城投的税率从合同表取
                //税率
                ContContractEntity contract = contContractService.getById(order.getContractId());
                goodsTaxRate = contract.getTaxPoint().divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP);
                query.eq("invoice_tax_rate",goodsTaxRate.toPlainString());
                //计租面积
                area = contract.getLeaseholdArea();
            }
            InvoiceAccountOrdertypeRelEntity rel = invoiceAccountOrdertypeRelService.getOne(query);
            if(rel == null){
                return RestResponse.failure("该账单类型未匹配开票账号");
            }
            //税率
            goodsTaxRate = new BigDecimal(rel.getInvoiceTaxRate());
            InvoiceAccountEntity invoiceAccount = invoiceAccountService.getById(rel.getAccountId());
            //查询对应税号下的数电账号当前的登录状态
            RestResponse loginResult = baiwangFegin.getLoginResult("SAAS",invoiceAccount.getInvoiceTaxNo(),invoiceAccount.getInvoiceTerminalCode());
            if(!loginResult.getSuccess()){
                return loginResult;
            }else{
                /*JSONObject res = JSONUtil.parseObj(loginResult.get("data"));
                if(!"1".equals(res.getStr("loginStatus"))){//未登录
                    return RestResponse.failure("数电账号未登录，请先在百望云平台登录");
                }
                //查询对应税号下的数电账号当前的开票实人认证（刷脸）状态及最近一次认证成功的时间
                RestResponse certifyResult = baiwangFegin.getCertifyResult("SAAS",invoiceAccount.getInvoiceTaxNo(),invoiceAccount.getInvoiceTerminalCode());
                if(!certifyResult.getSuccess()){
                    return certifyResult;
                }else{
                    JSONObject res1 = JSONUtil.parseObj(certifyResult.get("data"));
                    if(!"1".equals(res1.getStr("certificationStatus"))){//未登录
                        return RestResponse.failure("数电账号未认证，请先在百望云平台认证");
                    }
                }*/
            }

            //含税金额
            BigDecimal goodsTotalPriceTax = order.getPayablePayment();
            //不含税金额
            BigDecimal goodsTotalPrice = goodsTotalPriceTax.divide((BigDecimal.ONE.add(goodsTaxRate)),2,BigDecimal.ROUND_HALF_UP);
            //税额
            BigDecimal goodsTotalTax = goodsTotalPriceTax.subtract(goodsTotalPrice).setScale(2,BigDecimal.ROUND_HALF_UP);
            //获取租客信息
            RenterEntity renter = null;
            try {
                renter = renterFegin.getById(order.getPayerId());
            } catch (Exception e) {
                e.printStackTrace();
            }

            //组装红票信息
            BaiwangRedInvoiceVo redInvoiceVo = new BaiwangRedInvoiceVo();
            redInvoiceVo.setTaxNo(invoiceAccount.getInvoiceTaxNo());
            char randomChar = (char)(new Random().nextInt(26)+'A');
            redInvoiceVo.setOrderNo(order.getCode()+randomChar);
            redInvoiceVo.setInvoiceTerminalCode(invoiceAccount.getInvoiceTerminalCode());
            redInvoiceVo.setOriginalSerialNo(invoice.getSerialNo());
            redInvoiceVo.setOriginalOrderNo(order.getCode());
            redInvoiceVo.setOriginalDigitInvoiceNo(invoice.getTaxNumber());
            redInvoiceVo.setFastIssueRedType("1");

            BaiwangRedInvoiceDetailVo detailVo = new BaiwangRedInvoiceDetailVo();
            detailVo.setOriginalInvoiceDetailNo("1");
            detailVo.setGoodsName(rel.getInvoiceGoodsName());
            detailVo.setGoodsCode(rel.getInvoiceGoodsCode());
            detailVo.setGoodsTaxRate(goodsTaxRate.toString());
            detailVo.setGoodsTotalPrice(goodsTotalPrice.negate().toString());
            detailVo.setGoodsTotalTax(goodsTotalTax.negate().toString());
            detailVo.setPreferentialMark(rel.getInvoicePreferentialMark());
            detailVo.setVatSpecialManagement(rel.getInvoiceVatSpecialManagement());

            //不动产经营租赁服务
            if("06".equals(rel.getInvoiceSpecialMark())){
                detailVo.setGoodsQuantity(area.negate().toString());
                BigDecimal price = goodsTotalPriceTax.divide(area,12,BigDecimal.ROUND_HALF_UP);
                detailVo.setGoodsPrice(price.toString());
                detailVo.setGoodsUnit("平方米");
            }
            List<BaiwangRedInvoiceDetailVo> details = Lists.newArrayList();
            details.add(detailVo);
            redInvoiceVo.setDetails(details);

            //调用快捷红冲接口
            RestResponse res = baiwangFegin.baiwangFastRed("SAAS",redInvoiceVo);
            JSONObject resData = JSONUtil.parseObj(res.get("data"));
            if(res.getSuccess()){
                String serialNo = resData.getStr("serialNo");
                QueryWrapper query1 = new QueryWrapper();
                query1.eq("order_id",order.getId());
                query1.eq("invoice_type","2");
                InvoiceEntity redInvoice = reportInvoiceService.getOne(query1);
                if(redInvoice == null){
                    redInvoice = new InvoiceEntity();
                }
                redInvoice.setInvoiceType("2");
                redInvoice.setOrderId(order.getId());
                redInvoice.setTaxName(detailVo.getGoodsName());
                redInvoice.setTaxPayment(goodsTotalPriceTax.negate());
                redInvoice.setTaxDate(new Date());
                redInvoice.setAddress(source.getHouseName());
                redInvoice.setTel(renter.getTel());
                redInvoice.setIdNo(renter.getIdNo());
                redInvoice.setName(invoiceAccount.getInvoiceSellerName());
                redInvoice.setBank(invoiceAccount.getInvoiceSellerBankName());
                redInvoice.setCard(invoiceAccount.getInvoiceSellerBankNumber());
                redInvoice.setTaxAmount(goodsTotalTax.negate());
                redInvoice.setSerialNo(serialNo);
                redInvoice.setState(InvoiceStateEnum.REDDING.getValue());
                redInvoice.setTaxNumber(invoice.getTaxNumber());
                redInvoice.insertOrUpdate();
                order.setInvoicePayment(order.getPayablePayment());
                order.setInvoiceType(InvoiceTypeEnum.PERSONAL.getValue());
                order.setInvoiceState(InvoiceStateEnum.REDDING.getValue());
                String originalStr = order.getRemark()==null?"":order.getRemark();;
                String replacedStr = "";
                if(StrUtil.isNotBlank(originalStr) && originalStr.indexOf("【红冲原因") >-1){
                    replacedStr = originalStr.replaceAll("(?<=【红冲原因：)[^】]*(?=】)", remark);
                }else{
                    replacedStr = originalStr+"【红冲原因："+remark+"】";
                }
                order.setRemark(replacedStr);
                order.insertOrUpdate();
            }else{
                String message = resData.getStr("subMessage");
                String originalStr = order.getRemark()==null?"":order.getRemark();
                String replacedStr = "";
                if(StrUtil.isNotBlank(originalStr) && originalStr.indexOf("【红冲失败") >-1){
                    replacedStr = originalStr.replaceAll("(?<=【红冲失败：)[^】]*(?=】)", message);
                }else{
                    replacedStr = originalStr+"【红冲失败："+message+"】";
                }
                order.setInvoiceState(InvoiceStateEnum.REDFAILD.getValue());
                order.setRemark(replacedStr);
                order.insertOrUpdate();
                sbf.append("账单").append(order.getCode()).append("红冲失败：").append(message).append("\r\n");
            }
        }
        if(StrUtil.isNotBlank(sbf.toString())){
            return RestResponse.failure(sbf.toString());
        }
        return RestResponse.success("红冲成功");
    }

    @Override
    public IPage<Map<String, Object>> getToRefundOrderList(Page page,Map<String, Object> map) {
        return baseMapper.getToRefundOrderList(page,map);
    }

    @Override
    public IPage<ParkOrderVo> getByPark(Page page, BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.getByPark(page,map);
    }

    @Override
    public String getPayStateByContractId(String contractId) {
        return baseMapper.getPayStateByContractId(contractId);
    }

    @Override
    public List<ParkOrderVo> getByParkOrder(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.getByParkOrder(map);
    }

    @Override
    public List<Map<String, Object>> countRentOrders(String year) {
        return baseMapper.countRentOrders(year);
    }

    @Override
    public BilOrderEntity getOrderByMap(Map<String, Object> map) {
        return baseMapper.getOrderByMap(map);
    }

    @Override
    public IPage<Map<String, Object>> getSdOrderPage(Page page, String payerId) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("payerId",payerId);
        map.put("payWay", PayWayEnum.BALANCEPAY.getValue());
        return baseMapper.getSdOrderList(page,map);
    }

    @Override
    public List<Map<String, Object>> getSdOrderList(String contractId,String orderType) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("contractId",contractId);
        map.put("orderType",orderType);
        return baseMapper.getSdOrderList(map);
    }

}
