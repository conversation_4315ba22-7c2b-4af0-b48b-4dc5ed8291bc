package cn.uone.business.res.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResPropertyPartitionEntity;
import cn.uone.business.res.service.IResPropertyPartitionService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Api(value = "权属分区接口",tags = "权属分区接口")
@RestController
@RequestMapping("/res-property-partition-entity")
public class ResPropertyPartitionController extends BaseController {

    @Autowired
    private IResPropertyPartitionService resPropertyPartitionService;

    @ApiOperation(value = "获取权属分区")
    @RequestMapping("/queryPropertyPartition")
    public RestResponse queryPropertyPartition(String id) {
        ResPropertyPartitionEntity entity = new ResPropertyPartitionEntity();
        if(StrUtil.isBlank(id)){
            List<ResPropertyPartitionEntity> list = new ArrayList<>();
            return RestResponse.success().setData(list);
        }
        entity.setProjectId(id);
        IPage<ResPropertyPartitionEntity> list = resPropertyPartitionService.queryPropertyPartition(entity);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/queryListByProject")
    public RestResponse queryListByProject(String projectId) {
        List<Map<String, Object>> list = resPropertyPartitionService.listWithOwner(projectId);
        return RestResponse.success().setData(list);
    }

    @ApiOperation(value = "新增权属分区")
    @RequestMapping("/insertPropertyPartition")
    public RestResponse insertPropertyPartition(ResPropertyPartitionEntity entity) {
        if (entity==null){
            return RestResponse.failure("请求对象为空");
        }
        if(StrUtil.isEmpty(entity.getProjectId())){
            return RestResponse.failure("没有选中对应的项目！");
        }
        resPropertyPartitionService.insert(entity);
        return RestResponse.success("新增成功").setData(entity);
    }

    @RequestMapping("/updatePropertyPartition")
    @ApiOperation("修改权属分区")
    public RestResponse updatePropertyPartition(ResPropertyPartitionEntity entity){
        if(entity==null){
            return RestResponse.failure("请求对象为空");
        }
        if(StrUtil.isBlank(entity.getId())){
            return RestResponse.failure("请求的权属分区为空");
        }
        if(StrUtil.isBlank(entity.getProjectId())){
            return RestResponse.failure("请求的权属分区所属项目id为空");
        }
        resPropertyPartitionService.updatePropertyPartition(entity);
        return RestResponse.success("修改成功");
    }

    @RequestMapping("/deletePropertyPartition")
    @ApiOperation("删除权属分区")
    public RestResponse deletePropertyPartition(String id){
        if (StrUtil.isBlank(id)) {
            return RestResponse.failure("分区id为空，删除失败");
        }
        resPropertyPartitionService.removeById(id);
        return RestResponse.success("删除成功");
    }

    @RequestMapping("/updateQueryPropertyPartition")
    @ApiOperation("编辑时获取权属分区")
    public RestResponse updateQueryPropertyPartition(String id){
        ResPropertyPartitionEntity entity= resPropertyPartitionService.queryById(id);
        return RestResponse.success().setData(entity);
    }

    @ApiOperation(value = "权属分区是否存在")
    @RequestMapping("/selectPropertyPartitionExist")
    public RestResponse selectPropertyPartitionExist(String name, String projectId, String id) {
        QueryWrapper<ResProjectEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("name", name);
        wrapper.eq("project_id", projectId);
        wrapper.ne("id", id);
        if(resPropertyPartitionService.propertyPartitionExist(wrapper)){
            return RestResponse.failure("该分区名称已经存在");
        }else{
            return RestResponse.success();
        }
    }

}
