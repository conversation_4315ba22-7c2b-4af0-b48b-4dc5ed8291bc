package cn.uone.business.res.service;

import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.vo.*;
import cn.uone.bean.entity.crm.ProjectEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IResProjectService extends IService<ResProjectEntity> {
    IPage<ResProjectEntity> queryProject(Page page, ResProjectEntity entity);

    IPage<ResProjectEntity> selectPageByDataScope(Page page);

    void deleteProject(String id);

    void updateProject(ResProjectEntity rpe);

    ResProjectEntity getByCondition(ResProjectEntity setCode);

    Boolean projectExist(QueryWrapper<ResProjectEntity> wrapper);

    ResProjectEntity queryById(String id);

    List<ResProjectEntityVo> queryList();

    List<ResProjectEntityVo> queryListByCityCode(String cityCode);

    Map<String,ResProjectEntityVo> queryAll();

    ResProjectEntityVo queryIndexCount();

    List<ResProjectEntityVo> queryRenterList();

    String getProjectIdByContractId(String contractId);
    String getSourceIdByContractId(String contractId);

    String getOrderCodeBySourceId(String sourceId,String billType,String codeType);

    String getOrderCodeBySourceId(String sourceId, String orderType, String codeType, Date startDate);

    String getCombinedOrderNumber(String projectId);

    String getContractCodeByProjectId(String projectId);

    String getFrameContractCodeByProjectId(String projectId);

    ResProjectEntity selectAllAddByID(String id);

    void insertCharge(String projectId);

    IPage<ProjectByHouseTypeVo> getAllProjects(Page page, ProjectSearchVo searchVo);

    ResProjectVo getProjectById(String id);

    List<ProjectEntity> getListByScope(String userId);

    List<ProjectEntity> getByOperateState(String operateState);

    List<SelectVo> getAllProject(Map map);
    List<SelectVo> getAllPartition(Map map);
    List<SelectVo> getAllSource(Map map);
}
