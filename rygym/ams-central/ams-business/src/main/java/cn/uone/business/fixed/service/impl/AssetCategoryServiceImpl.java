package cn.uone.business.fixed.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.fixed.AssetCategoryEntity;
import cn.uone.business.fixed.dao.AssetCategoryDao;
import cn.uone.business.fixed.service.IAssetCategoryService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 固定资产分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Service
public class AssetCategoryServiceImpl extends ServiceImpl<AssetCategoryDao, AssetCategoryEntity> implements IAssetCategoryService {

    @Autowired
    AssetCategoryDao assetCategoryDao;

    @Override
    public IPage<AssetCategoryEntity> page(Page page, AssetCategoryEntity entity) {
        QueryWrapper<AssetCategoryEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getCategoryCode())) {
            queryWrapper.like("t_fixed_asset_category.category_code", entity.getCategoryCode());
        }
        if (ObjectUtil.isNotEmpty(entity.getCategoryName())) {
            queryWrapper.like("t_fixed_asset_category.category_name", entity.getCategoryName());
        }
        queryWrapper.orderByDesc("t_fixed_asset_category.create_date");
        IPage iPage =  baseMapper.selectPage(page, queryWrapper);
        return iPage;
    }

    @Override
    public List<AssetCategoryEntity> selectList(AssetCategoryEntity entity) {
        QueryWrapper<AssetCategoryEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getCategoryCode())) {
            queryWrapper.like("t_fixed_asset_category.category_code", entity.getCategoryCode());
        }
        if (ObjectUtil.isNotEmpty(entity.getCategoryName())) {
            queryWrapper.like("t_fixed_asset_category.category_name", entity.getCategoryName());
        }
        queryWrapper.orderByDesc("t_fixed_asset_category.create_date");
        List list =  baseMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public List<Map<String, String>> getAllDept(String id) {
        return assetCategoryDao.getAllDept(id);
    }
}
