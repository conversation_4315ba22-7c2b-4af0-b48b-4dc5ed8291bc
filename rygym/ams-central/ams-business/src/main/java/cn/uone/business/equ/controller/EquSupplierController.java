package cn.uone.business.equ.controller;


import cn.uone.bean.entity.business.equ.EquSupplierEntity;
import cn.uone.business.equ.service.IEquSupplierService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 供应商表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-19
 */
@RestController
@RequestMapping("/equ-supplier-entity")
public class EquSupplierController extends BaseController {

    @Autowired
    IEquSupplierService equSupplierService;

    @GetMapping("/page")
    public RestResponse page(Page page, EquSupplierEntity entity){
        IPage<EquSupplierEntity> supplierPage = equSupplierService.page(page,entity);
        return RestResponse.success().setData(supplierPage);
    }

    @GetMapping("/getById")
    public RestResponse getById(String id){
       EquSupplierEntity supplier = equSupplierService.getById(id);
        return RestResponse.success().setData(supplier);
    }

    @PostMapping("/addOrUpdate")
    public RestResponse addOrUpdate(EquSupplierEntity entity){
        String projectId = UoneHeaderUtil.getProjectId();
        entity.setProjectId(projectId);
        equSupplierService.saveOrUpdate(entity);
        return RestResponse.success("保存成功");
    }

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        equSupplierService.removeByIds(ids);
        return RestResponse.success("删除成功");
    }

}
