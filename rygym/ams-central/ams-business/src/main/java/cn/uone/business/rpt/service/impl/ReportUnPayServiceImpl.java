package cn.uone.business.rpt.service.impl;

import cn.uone.bean.entity.business.report.vo.UnPayVo;
import cn.uone.bean.parameter.UnPayPo;
import cn.uone.business.rpt.dao.ReportUnPayDao;
import cn.uone.business.rpt.service.IReportUnpayService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class ReportUnPayServiceImpl implements IReportUnpayService {

    @Autowired
    protected ReportUnPayDao unPayDao;

    @Override
    public IPage<UnPayVo> selectPages(Page page, UnPayPo param) {
        return unPayDao.queryList(page,param);
    }

    @Override
    public List<UnPayVo> export(UnPayPo param) {
        return unPayDao.queryList(param);
    }

    @Override
    public BigDecimal getTotalUnpay(UnPayPo param) {
        return unPayDao.getTotalUnpay(param);
    }
}
