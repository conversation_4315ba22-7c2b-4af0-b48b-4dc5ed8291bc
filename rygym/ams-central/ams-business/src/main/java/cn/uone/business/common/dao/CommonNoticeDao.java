package cn.uone.business.common.dao;

import cn.uone.bean.entity.business.common.CommonNoticeEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 布告通知 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
public interface CommonNoticeDao extends BaseMapper<CommonNoticeEntity> {

    Page<CommonNoticeEntity> getPage(@Param("page") Page page, Map<String, Object> map);

    List<CommonNoticeEntity> getList();
}
