package cn.uone.business.pbls.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.uone.application.enumerate.RenterType;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.order.PayWayEnum;
import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import cn.uone.bean.entity.business.bil.AccountBalanceEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.ParkOrderVo;
import cn.uone.bean.entity.business.pbls.ParkingApplyEntity;
import cn.uone.bean.entity.business.pbls.ParkingRecordEntity;
import cn.uone.bean.entity.business.pbls.ParkingSpaceEntity;
import cn.uone.bean.entity.business.pbls.vo.ParkingSearchVo;
import cn.uone.bean.entity.business.res.ResPlanPartitionEntity;
import cn.uone.bean.entity.business.res.vo.SelectVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.business.base.service.IBaseEnterpriseService;
import cn.uone.business.bil.service.IAccountBalanceService;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.pbls.service.IParkingApplyService;
import cn.uone.business.pbls.service.IParkingRecordService;
import cn.uone.business.pbls.service.IParkingSpaceService;
import cn.uone.business.res.service.IResPlanPartitionService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.fegin.tpi.ICottoFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.AlgorUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.CacheParam;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.PassWordCreateUtil;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@RestController
@RequestMapping("/parkingApply")
public class ParkingApplyController extends BaseController {

    @Autowired
    private IParkingApplyService parkingApplyService;
    @Autowired
    private IParkingSpaceService parkingSpaceService;
    @Resource
    private ISysFileService iSysFileService;
    @Autowired
    private IParkingRecordService parkingRecordService;
    @Autowired
    private ICottoFegin cottoFegin ;
    @Autowired
    private IBaseEnterpriseService baseEnterpriseService ;
    @Autowired
    private IAccountBalanceService accountBalanceService;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IBilOrderItemService bilOrderItemService;
    @Autowired
    private ISysFileService fileService;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private IUserFegin userFegin;
//    @Autowired
//    private IWxPayFegin wxPayFegin;
    @Autowired
    private IResPlanPartitionService resPlanPartitionService;

    /**
     * 分页查询(车位申请)
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, ParkingSearchVo parkingSearchVo) {
        RestResponse response = new RestResponse();
        /**
         * 相关参数配置在该处处理
         */
        List<String> states = new ArrayList<>();
        states.add("1");
        states.add("3");
        states.add("4");
        parkingSearchVo.setStates(states);
        return response.setSuccess(true).setData(parkingApplyService.findByCondition(page, parkingSearchVo));
    }
    /**
     * 分页查询（车位管理）
     *
     * @return
     */
    @RequestMapping("/selectParkingRecord")
    public RestResponse selectParkingRecord(Page page, ParkingSearchVo parkingSearchVo) {
        RestResponse response = new RestResponse();
        return response.setSuccess(true).setData(parkingApplyService.selectParkingRecordByMap(page, parkingSearchVo));
    }

    /**
     * 车位申请的车位列表
     *
     * @return
     */
    @RequestMapping("/parkingSpace")
    @ApiOperation("车位列表")
    public RestResponse getParkingSpace() {
        ParkingSpaceEntity parkingSpaceEntity = new ParkingSpaceEntity();
        IPage<ParkingSpaceEntity> iPage = parkingSpaceService.findByCondition(new Page<>(1, 50), parkingSpaceEntity);
        List<SelectVo> list = new ArrayList<>();
        if (iPage != null && ObjectUtil.isNotEmpty(iPage.getRecords())) {
            for (ParkingSpaceEntity parkingSpace : iPage.getRecords()) {
                SelectVo vo = new SelectVo();
                vo.setName(parkingSpace.getName());
                vo.setValue(parkingSpace.getId());
                list.add(vo);
            }
        }
        return RestResponse.success().setData(list);
    }


    /**
     * 车位申请的企业列表
     *
     * @return
     */
    @RequestMapping("/getEnterprise")
    @ApiOperation("企业列表")
    public RestResponse getEnterprise() throws Exception {
        Map<String, Object> map  = new HashMap<>();
        List<BaseEnterpriseEntity> entity =  baseEnterpriseService.selectByList(map);
        List<SelectVo> list = new ArrayList<>();
        if (entity != null && entity.size()>0) {
            for (BaseEnterpriseEntity baseEnterprise : entity) {
                SelectVo vo = new SelectVo();
                vo.setName(baseEnterprise.getName());
//                vo.setValue(baseEnterprise.getId());
                vo.setValue(baseEnterprise.getName());
                list.add(vo);
            }
        }
        SelectVo qt = new SelectVo();
        qt.setName("其他");
        qt.setValue("其他");
        list.add(qt);
        return RestResponse.success().setData(list);
    }
    /**
     * 车位申请的楼栋列表
     *
     * @return
     */
    @RequestMapping("/getBuilding")
    public RestResponse getBuilding() {
        QueryWrapper<ResPlanPartitionEntity> wrapper = new QueryWrapper<ResPlanPartitionEntity>();
        if(StrUtil.isNotBlank(UoneHeaderUtil.getProjectId())){
            wrapper.eq("project_id", UoneHeaderUtil.getProjectId());
        }
        wrapper.orderByAsc("name");
        List<ResPlanPartitionEntity> planList= resPlanPartitionService.list(wrapper);
        List<SelectVo> list = new ArrayList<>();
        if (planList != null && planList.size()>0) {
            for (ResPlanPartitionEntity r : planList) {
                SelectVo vo = new SelectVo();
                vo.setName(r.getName());
                vo.setValue(r.getName());
                list.add(vo);
            }
        }
        return RestResponse.success().setData(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "addParkApply", method = RequestMethod.POST)
    @CacheLock(prefix = "addParkApply", expire = 5)
    @UoneLog("停车位申请信息录入")
    public RestResponse add(ParkingApplyEntity parkingApplyEntity) throws Exception {
        //去除两端空格
        parkingApplyEntity.setLicense(parkingApplyEntity.getLicense().trim());
        if(StrUtil.isNotBlank(parkingApplyEntity.getAuxiliaryLicense())){
            //去除两端空格
            parkingApplyEntity.setAuxiliaryLicense(parkingApplyEntity.getAuxiliaryLicense().trim());
        }
        BilOrderEntity order =null;
        //保存申请表
        parkingApplyEntity.setUserId(UoneSysUser.id());
        ParkingApplyEntity existenceOrNot = parkingApplyService.getByLicense(parkingApplyEntity.getLicense());
        if(existenceOrNot!=null&&StrUtil.isNotBlank(existenceOrNot.getId())){
            parkingApplyEntity.setId(existenceOrNot.getId());
            parkingApplyService.updateById(parkingApplyEntity);
            List<BilOrderEntity> list = bilOrderService.getByContractId(existenceOrNot.getId());
            if(list!=null&&list.size()>0){
                order = list.get(0);
            }else{
                //生成账单
                order = toCreateBilEntity(parkingApplyEntity.getPayment(),parkingApplyEntity);
            }
        }else{
            parkingApplyService.save(parkingApplyEntity);
            //生成账单
            order = toCreateBilEntity(parkingApplyEntity.getPayment(),parkingApplyEntity);
        }
        //线上支付则扣除余额
        RestResponse payInfo = null;
        if("1".equals(parkingApplyEntity.getPayWay())){
            AccountBalanceEntity accountBalanceEntity = accountBalanceService.getOne(new QueryWrapper<AccountBalanceEntity>().eq("signer_id", UoneSysUser.id()).eq("order_type", "555"));
            BigDecimal balance = accountBalanceEntity.getBalance();
            accountBalanceEntity.setBalance(balance.subtract(parkingApplyEntity.getPayment()));
            accountBalanceEntity.insertOrUpdate();
        }else if("0".equals(parkingApplyEntity.getPayWay())){
            RenterEntity renter = renterFegin.getById(UoneSysUser.id());
//            payInfo = wxPayFegin.prepayWithRequestPayment(order.getCode(),parkingApplyEntity.getLicense()+"停车缴费",order.getPayment(),renter.getOpenid());
        }
        return RestResponse.success().setData(parkingApplyEntity).setAny("payInfo",payInfo);
    }

    @RequestMapping(value = "addParkApplyByList", method = RequestMethod.POST)
    @UoneLog("停车位申请信息录入")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse addParkApplyByList(@ModelAttribute ParkingApplyEntity entity, @RequestParam(required = false) List<MultipartFile> imageFiles,
                                           @RequestParam(required = false) List<MultipartFile> auxiliaryFiles) throws Exception {

        entity.setProjectId(UoneHeaderUtil.getProjectId());
        //查询用户 没有则新增
        RenterEntity renter = renterFegin.getByTelAndType(entity.getCarOwnerTel(), RenterType.COMMON.getValue());
        if (null == renter) {
            String password = PassWordCreateUtil.createPassWord(6);
            // 创建会员
            renter = new RenterEntity();
            renter.setName(entity.getCarOwner())
                    .setUsername(entity.getCarOwnerTel())
                    .setTel(entity.getCarOwnerTel())
                    .setType(RenterType.COMMON.getValue())
                    .setSalt(AlgorUtil.getSalt())
                    .setPassword(AlgorUtil.entryptPassword(password, renter.getSalt()))
                    .setAgreementState(1);
            renterFegin.add(renter);
        }
        renter = renterFegin.getByTelAndType(entity.getCarOwnerTel(), RenterType.COMMON.getValue());
        //保存申请表
        entity.setPayWay("2");
        entity.setType("1");
        entity.setUserId(renter.getId());
        if(entity.getStartDate()==null){
            entity.setStartDate(new Date());
        }
        entity.setEndDate(DateUtil.offsetDay(DateUtil.offsetMonth(entity.getStartDate(),entity.getMonthQuantity()+entity.getPresented()),-1));
        UserEntity userEntity= userFegin.getUserById(UoneSysUser.id());
        if(userEntity!=null){
            entity.setApplyName(userEntity.getRealName());
            entity.setTelphone(userEntity.getTel());
            entity.setOperatorId(userEntity.getId());
        }
        parkingApplyService.save(entity);
        //行驶证图片保存
        addOrUpdateFile(entity.getId(),imageFiles,auxiliaryFiles);
        //生成账单
        BilOrderEntity order = toCreateBilEntity(entity.getPayment(),entity);
        order.setPayState(PayStateEnum.PAYCONFIR.getValue());
        bilOrderService.updateById(order);
//        //审核通过
        entity.setStatus("2");
        Map<String,Object> maps = parkingApplyService.auditApply(entity);
        int code = (int) maps.get("code");
        if(code == -1){
            String error = (String) maps.get("error");
            return RestResponse.failure(error);
        }
        return RestResponse.success().setData(maps);
    }



    private BilOrderEntity toCreateBilEntity(BigDecimal allRent , ParkingApplyEntity entity){
        BilOrderEntity orderEntity  = new BilOrderEntity();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");//设置日期格式
        String code = "TC"+ LocalDateTime.now().format(fmt);
        orderEntity.setCode(code);
        orderEntity.setPayablePayment(allRent);//应付金额
        orderEntity.setActualPayment(allRent);//支付金额
        orderEntity.setPayment(allRent);//账单金额
        orderEntity.setPayTime(new Date());//支付时间
        orderEntity.setOrderType("448");//账单类型
        orderEntity.setPush(true);
        orderEntity.setPayerId(entity.getUserId());//付款方
        orderEntity.setPush(true);//是否推送
        orderEntity.setPayableTime(new Date());//应付时间
//        orderEntity.setSourceId(entity.getLicense());//车牌号存入房源号
        orderEntity.setContractId(entity.getId());//申请表id存入合同id
        if("1".equals(entity.getPayWay())){
            orderEntity.setPayWay(PayWayEnum.BALANCEPAY.getValue());//钱包支付
            orderEntity.setPayState("20");//支付状态
        }else if("0".equals(entity.getPayWay())){
            orderEntity.setPayState("10");
            orderEntity.setPayWay(PayWayEnum.WECHAT.getValue());//微信支付
        } else{//微信支付或线下支付
            orderEntity.setPayState("10");
            orderEntity.setPayWay(PayWayEnum.OFFLINE.getValue());//线下支付
        }
        orderEntity.insert();
        BilOrderItemEntity item = new BilOrderItemEntity();
        item.setOrderId(orderEntity.getId());//主表ID
        item.setPayment(allRent);//金额
        item.setOrderItemType("448");//类型
        item.setStartTime(entity.getStartDate());
        item.setEndTime(entity.getEndDate());
        item.insert();
        return orderEntity;
    }

    @RequestMapping(value = "addParkApplyEstimate", method = RequestMethod.POST)
    @CacheLock(prefix = "addParkApplyEstimate", expire = 5)
    public RestResponse addParkApplyEstimate(ParkingApplyEntity parkingApplyEntity){
        //去除两端空格
        parkingApplyEntity.setLicense(parkingApplyEntity.getLicense().trim());
        //判断主车牌是否重复 //判断主车牌是否符合规则
        RestResponse judge = judgeToApply(parkingApplyEntity.getLicense());
        if(!judge.getSuccess()){
            return RestResponse.failure(judge.getMessage());
        }else  if(!isCarNo(parkingApplyEntity.getLicense())){
            return RestResponse.failure(parkingApplyEntity.getLicense()+":车牌格式错误");
        }
        /*int  number =  parkingApplyService.selectByLicense(parkingApplyEntity.getLicense());
        if(number>0){
            return RestResponse.failure(parkingApplyEntity.getLicense()+":车牌号已存在");
        }else  if(!isCarNo(parkingApplyEntity.getLicense())){
            return RestResponse.failure(parkingApplyEntity.getLicense()+":车牌格式错误");
        }*/

        if(StrUtil.isNotBlank(parkingApplyEntity.getAuxiliaryLicense())){
            //去除两端空格
            parkingApplyEntity.setAuxiliaryLicense(parkingApplyEntity.getAuxiliaryLicense().trim());
            RestResponse judge2 = judgeToApply(parkingApplyEntity.getAuxiliaryLicense());
            if(!judge2.getSuccess()){
                return RestResponse.failure(judge2.getMessage());
            }else if(!isCarNo(parkingApplyEntity.getAuxiliaryLicense())){
                return RestResponse.failure(parkingApplyEntity.getAuxiliaryLicense()+":车牌格式错误");
            }
            /*parkingApplyEntity.setAuxiliaryLicense(parkingApplyEntity.getAuxiliaryLicense().trim());
            int  number2 =  parkingApplyService.selectByLicense(parkingApplyEntity.getAuxiliaryLicense());
            if(number2>0){
                return RestResponse.failure(parkingApplyEntity.getAuxiliaryLicense()+":车牌号已存在");
            }else if(!isCarNo(parkingApplyEntity.getAuxiliaryLicense())){
                return RestResponse.failure(parkingApplyEntity.getAuxiliaryLicense()+":车牌格式错误");
            }*/
        }
        //判断停车区域车位是否不足
        ParkingSpaceEntity spaceEntity =  parkingSpaceService.getById(parkingApplyEntity.getParkingSpaceId());
        if(spaceEntity.getNumber()<=parkingRecordService.getByParkSpaceId(parkingApplyEntity.getParkingSpaceId())){
            return RestResponse.failure("所选区域车位不足！");
        }
        return RestResponse.success();
    }

    private RestResponse judgeToApply(String license){
        //判断主车牌是否重复 //判断主车牌是否符合规则
        QueryWrapper<ParkingApplyEntity> query = new QueryWrapper<>();
        query.eq("license",license).or()
                .eq("auxiliary_license",license);
        ParkingApplyEntity oldApply = parkingApplyService.getOne(query);
        if(oldApply != null){
            if("2".equals(oldApply.getStatus())){
                return RestResponse.failure(license+":车牌号已存在");
            }
            List<BilOrderEntity> orderList = bilOrderService.getByContractId(oldApply.getId());
            if(orderList != null && orderList.size()>0){
                BilOrderEntity order = orderList.get(0);
                if(PayWayEnum.WECHAT.getValue().equals(order.getPayWay())
                        && PayStateEnum.NOPAY.getValue().equals(order.getPayState())){
                    bilOrderItemService.removeByCondition(new BilOrderItemEntity().setOrderId(order.getId()));
                    bilOrderService.removeById(order.getId());
                    parkingApplyService.removeById(oldApply.getId());
                }else{
                    return RestResponse.failure(license+":车牌号已存在");
                }
            }
        }
        return RestResponse.success();
    }

    /**车牌正则验证*/
    public boolean isCarNo(String carNo){
        if (carNo.length() >= 7 && carNo.length() <= 8){
            Pattern p = Pattern.compile("^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[a-zA-Z](([ABCDF]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[ABCDF]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})$");
            Matcher m = p.matcher(carNo);
            if (!m.matches()){
                return false;
            }
            return true;
        }else{
            return false;
        }
    }


    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getPageByUser")
    public RestResponse getPageByUser(Page page, ParkingSearchVo parkingSearchVo) {
        RestResponse response = new RestResponse();
        parkingSearchVo.setUserId(UoneSysUser.id());
        return response.setSuccess(true).setData(parkingApplyService.findByCondition(page, parkingSearchVo));
    }

    /**
     * 停车位申请审核
     *
     * @param id
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "auditApply", method = RequestMethod.POST)
    @CacheLock(prefix = "auditApply", expire = 5)
    @UoneLog("停车位申请信息录入")
    public RestResponse auditApply(@CacheParam @RequestParam(value = "id") String id, @RequestParam(value = "status") String status, @RequestParam(value = "remark", required=false) String remark) throws Exception {
        ParkingApplyEntity parkingApplyEntity = parkingApplyService.getById(id);
        UserEntity userEntity= userFegin.getUserById(UoneSysUser.id());
        if(userEntity!=null){
            parkingApplyEntity.setApplyName(userEntity.getRealName());
            parkingApplyEntity.setTelphone(userEntity.getTel());
            parkingApplyEntity.setOperatorId(userEntity.getId());
        }
        parkingApplyEntity.setStatus(status);
        parkingApplyEntity.setRemark(remark);
        Map<String,Object> maps = parkingApplyService.auditApply(parkingApplyEntity);
        int code = (int) maps.get("code");
        if(code == -1){
            String error = (String) maps.get("error");
            return RestResponse.failure(error);
        }
        return RestResponse.success().setData(maps);
    }


    /**
     * 查看 停车位申请详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @UonePermissions(value = LoginType.ANON)
    @RequestMapping("/getParkApply")
    public RestResponse getParkApply(@RequestParam("id") String id) throws Exception {
        ParkingApplyEntity parkingApplyEntity = parkingApplyService.getById(id);
        ParkingSpaceEntity spaceEntity = parkingSpaceService.getById(parkingApplyEntity.getParkingSpaceId());
        parkingApplyEntity.setParkingArea(spaceEntity.getName());
        QueryWrapper<SysFileEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("from_id", parkingApplyEntity.getId());
        List<SysFileEntity> sysFileEntities = iSysFileService.list(queryWrapper);
        for(SysFileEntity sysFileEntity : sysFileEntities){
            if("drivePhoto".equals(sysFileEntity.getType())){
                parkingApplyEntity.setDrivePhotoUrl(sysFileEntity.getPath());
            }
            if("goPhoto".equals(sysFileEntity.getType())){
                parkingApplyEntity.setGoPhotoUrl(sysFileEntity.getPath());
            }
            if("recommendation".equals(sysFileEntity.getType())){
                parkingApplyEntity.setRecommendationUrl(sysFileEntity.getPath());
            }
            if("auxiliary".equals(sysFileEntity.getType())){
                parkingApplyEntity.setAuxiliaryUrl(sysFileEntity.getPath());
            }
        }
        return RestResponse.success().setData(parkingApplyEntity);
    }

    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getApplyVo")
    public RestResponse getApplyVo(ParkingSearchVo parkingSearchVo) {
        RestResponse response = new RestResponse();
        return response.setSuccess(true).setData(parkingApplyService.getApplyVo(parkingSearchVo));
    }

    /**
     * 暂停及恢复
     *
     * @return
     */
    @RequestMapping("/suspendOfRecover")
    public RestResponse suspendOfRecover(String cardId,String flag,String recordId) {
        JSONObject json = cottoFegin.modifyCardStatus(cardId,flag);
        if("0".equals(json.get("resCode"))){
            ParkingRecordEntity parkingRecordEntity = parkingRecordService.getById(recordId);
            if("0".equals(flag)){
                parkingRecordEntity.setStatus("2");
                parkingRecordEntity.updateById();
            }else if("1".equals(flag)){
                parkingRecordEntity.setStatus("1");
                parkingRecordEntity.updateById();
            }
            return RestResponse.success("操作成功");
        }else{
            return RestResponse.failure("操作失败,科拓返回结果提示: "+json.get("resMsg"));
        }

    }

    /**
     * 注销
     *
     * @return
     */
    @RequestMapping("/logout")
    public RestResponse logout(ParkingRecordEntity parkingRecordEntity) {
        if(parkingRecordEntity.updateById()){
            return RestResponse.success();
        }
        return RestResponse.failure("操作失败");
    }


    /**
     * 删除照片
     *
     * @return
     */
    @RequestMapping("/deletePhoto")
    public RestResponse deletePhoto(String type,String id) {
        if("主".equals(type)){
            fileService.delFileByFromIdAndType(id, SysFileTypeEnum.GO_PHOTO);
        }
        if("附".equals(type)){
            fileService.delFileByFromIdAndType(id, SysFileTypeEnum.AUXILIARY_CAR);
        }
        return RestResponse.success();
    }
    @RequestMapping(value = "editPark", method = RequestMethod.POST)
    @UoneLog("停车管理信息修改")
    public RestResponse editPark(@ModelAttribute ParkingApplyEntity entity, @RequestParam(required = false) List<MultipartFile> imageFiles,
                                 @RequestParam(required = false) List<MultipartFile> auxiliaryFiles) throws Exception {

        ParkingApplyEntity old = parkingApplyService.getById(entity.getId());
        if(old!=null){
            ParkingRecordEntity recordEntity = parkingRecordService.getOne(new QueryWrapper<ParkingRecordEntity>().eq("apply_id",old.getId()));
            int i = 0;
            //去除主车牌两端空格
            entity.setLicense(entity.getLicense().trim());
            if(!old.getLicense().equals(entity.getLicense())){
                if(parkingApplyService.selectByLicense(entity.getLicense())>0){
                    return RestResponse.failure(entity.getLicense()+":车牌号已存在");
                }else  if(!isCarNo(entity.getLicense())){
                    return RestResponse.failure(entity.getLicense()+":车牌格式错误");
                }
                recordEntity.setNumberplate(entity.getLicense());
                i++;
            }
            if(StrUtil.isNotBlank(entity.getAuxiliaryLicense())){
                //去除副车牌两端空格
                entity.setAuxiliaryLicense(entity.getAuxiliaryLicense().trim());
                if(!old.getAuxiliaryLicense().equals(entity.getAuxiliaryLicense())){
                    if(parkingApplyService.selectByLicense(entity.getAuxiliaryLicense())>0){
                        return RestResponse.failure(entity.getAuxiliaryLicense()+":车牌号已存在");
                    }else if(!isCarNo(entity.getAuxiliaryLicense())){
                        return RestResponse.failure(entity.getAuxiliaryLicense()+":车牌格式错误");
                    }
                    recordEntity.setAuxiliaryLicense(entity.getAuxiliaryLicense());
                    i++;
                }
            }
            if(!old.getCarOwner().equals(entity.getCarOwner())){
                recordEntity.setCarOwner(entity.getCarOwner());
                i++;
            }
            if(!old.getCarOwnerTel().equals(entity.getCarOwnerTel())){
                recordEntity.setCarOwnerTel(entity.getCarOwnerTel());
                i++;
            }
            if(i>0){
            Map<String,Object> json =   parkingApplyService.editParkInfo(old,entity,recordEntity);
                if("0".equals(json.get("resCode"))){
                    //修改t_parking_record表
                    parkingRecordService.updateById(recordEntity);
                }else{
                    return RestResponse.failure("操作失败,科拓返回结果提示: "+json.get("resMsg"));
                }
            }
            //修改图片
            addOrUpdateFile(entity.getId(),imageFiles,auxiliaryFiles);
            //修改t_parking_apply
            parkingApplyService.updateById(entity);
            return RestResponse.success();
        }else{
            return RestResponse.failure("修改的数据不存在");
        }
    }

    @RequestMapping(value = "editApply", method = RequestMethod.POST)
    @UoneLog("停车申请信息修改")
    public RestResponse editApply(@ModelAttribute ParkingApplyEntity entity, @RequestParam(required = false) List<MultipartFile> imageFiles,
                                  @RequestParam(required = false) List<MultipartFile> auxiliaryFiles){

        ParkingApplyEntity old = parkingApplyService.getById(entity.getId());
        if(old!=null){
            //去除主车牌两端空格
            entity.setLicense(entity.getLicense().trim());
            if(!old.getLicense().equals(entity.getLicense())){
                int  number =  parkingApplyService.selectByLicense(entity.getLicense());
                if(number>0){
                    return RestResponse.failure(entity.getLicense()+":车牌号已存在");
                }else  if(!isCarNo(entity.getLicense())){
                    return RestResponse.failure(entity.getLicense()+":车牌格式错误");
                }
            }
            if(StrUtil.isNotBlank(entity.getAuxiliaryLicense())){
                //去除副车牌两端空格
                entity.setAuxiliaryLicense(entity.getAuxiliaryLicense().trim());
                if(!old.getAuxiliaryLicense().equals(entity.getAuxiliaryLicense())){
                    int  number2 =  parkingApplyService.selectByLicense(entity.getAuxiliaryLicense());
                    if(number2>0){
                        return RestResponse.failure(entity.getAuxiliaryLicense()+":车牌号已存在");
                    }else if(!isCarNo(entity.getAuxiliaryLicense())){
                        return RestResponse.failure(entity.getAuxiliaryLicense()+":车牌格式错误");
                    }
                }
            }
            //更新审核状态及审核意见
            entity.setStatus("1");
            entity.setRemark("");
            //修改图片
            addOrUpdateFile(entity.getId(),imageFiles,auxiliaryFiles);
            parkingApplyService.updateById(entity);
            return RestResponse.success();
        }else{
            return RestResponse.failure("修改的数据不存在");
        }

    }


    public void addOrUpdateFile(String id,List<MultipartFile> imageFiles,List<MultipartFile> auxiliaryFiles){
        if(auxiliaryFiles != null &&  auxiliaryFiles.size() > 0){
            fileService.delFileByFromIdAndType(id, SysFileTypeEnum.AUXILIARY_CAR);
            fileService.saveFiles(auxiliaryFiles, id, SysFileTypeEnum.AUXILIARY_CAR.getValue());
        }
        if(imageFiles != null &&  imageFiles.size() > 0){
            fileService.delFileByFromIdAndType(id, SysFileTypeEnum.GO_PHOTO);
            fileService.saveFiles(imageFiles, id, SysFileTypeEnum.GO_PHOTO.getValue());
        }
    }

    /**
     * 停车账单
     *
     * @return
     */
    @RequestMapping("/getParkOrderList")
    public RestResponse getParkOrderList(Page page, BilOrderSearchVo bilOrderSearchVo) {
        return RestResponse.success().setData(bilOrderService.getByPark(page,bilOrderSearchVo));
    }

    /**
     * 停车账单
     *
     * @return
     */
    @RequestMapping("/getParkOrderPayState")
    public RestResponse getParkOrderPayState(ParkingApplyEntity entity) {
        String payState = bilOrderService.getPayStateByContractId(entity.getId());
        return RestResponse.success().setData(payState);
    }


    /**
     * 停车账单导出
     */
    @UonePermissions(LoginType.USER)
    @RequestMapping("/export")
    public void export(HttpServletResponse response, BilOrderSearchVo param) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        List<ParkOrderVo> list = bilOrderService.getByParkOrder(param);
        beans.put("list", list);
        ExcelRender.me("/excel/export/parkOrder.xlsx").beans(beans).render(response);
    }

    @RequestMapping(value = "payOrder", method = RequestMethod.POST)
    @CacheLock(prefix = "payOrder", expire = 5)
    @UoneLog("停车费支付")
    public RestResponse payOrder(String orderId) throws Exception {
        //生成账单
        BilOrderEntity order = bilOrderService.getById(orderId);
        ParkingApplyEntity parkingApplyEntity = parkingApplyService.getById(order.getContractId());
        //线上支付则扣除余额
        RestResponse payInfo = null;
        if(order != null && order.getPayment().compareTo(BigDecimal.ZERO)==1 && PayStateEnum.NOPAY.getValue().equals(order.getPayState())){
            RenterEntity renter = renterFegin.getById(UoneSysUser.id());
//            payInfo = wxPayFegin.prepayWithRequestPayment(order.getCode(),parkingApplyEntity.getLicense()+"停车缴费",order.getPayment(),renter.getOpenid());
        }
        return RestResponse.success().setData(payInfo);
    }

}
