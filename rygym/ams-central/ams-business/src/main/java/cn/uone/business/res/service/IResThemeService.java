package cn.uone.business.res.service;

import cn.uone.bean.entity.business.res.ResThemeEntity;
import cn.uone.bean.entity.business.res.vo.ResThemeVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IResThemeService extends IService<ResThemeEntity> {
    IPage<ResThemeVo> list(Page page, Map<String, Object> map);

    ResThemeEntity queryone(ResThemeEntity entity);

}
