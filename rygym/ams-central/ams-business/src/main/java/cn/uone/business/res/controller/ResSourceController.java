package cn.uone.business.res.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.RenterType;
import cn.uone.application.enumerate.contract.PayTypeEnum;
import cn.uone.application.enumerate.source.*;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContTalentEntity;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.dev.vo.DevDeviceEntityVo;
import cn.uone.bean.entity.business.res.*;
import cn.uone.bean.entity.business.res.vo.*;
import cn.uone.bean.entity.business.rpt.RptOperateInfoEntity;
import cn.uone.bean.entity.business.sale.SaleCustomerEntity;
import cn.uone.bean.entity.business.sale.SaleDemandEntity;
import cn.uone.bean.entity.business.sys.SysAreaEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.CityEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.bil.service.IBilOrderAutoService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContTalentService;
import cn.uone.business.cont.service.IContTempService;
import cn.uone.business.demo.service.IDemoContractService;
import cn.uone.business.flow.service.IActReDeploymentService;
import cn.uone.business.res.dao.ResProjectDao;
import cn.uone.business.res.dao.ResSourceDao;
import cn.uone.business.res.service.*;
import cn.uone.business.rpt.service.IRptOperateInfoService;
import cn.uone.business.sale.dao.SaleCustomerDao;
import cn.uone.business.sale.dao.SaleDemandDao;
import cn.uone.business.sys.service.ISysAreaService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.util.BaiduMapUtil;
import cn.uone.business.util.FirstCharUtil;
import cn.uone.cache.util.CacheUtil;
import cn.uone.fegin.bus.IResSourceFegin;
import cn.uone.fegin.crm.IExpenseProjectFegin;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.AlgorUtil;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.CacheParam;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/res-source-entity")
public class ResSourceController extends BaseController implements IResSourceFegin {

    private static final Logger log = LoggerFactory.getLogger(ResSourceController.class);

    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IResSourceConfigureService resSourceConfigureService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private IResSourceCheckService resSourceCheckService;
    @Autowired
    private IResSourceDeviceRelService resSourceDeviceRelService;
    @Autowired
    private IResPlanPartitionService resPlanPartitionService;
    @Autowired
    private IResProjectInfoService resProjectInfoService;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    private IBilOrderAutoService bilOrderAutoService;
    @Autowired
    private SaleDemandDao saleDemandDao;
    @Autowired
    private IContTalentService contTalentService;
    @Resource
    private IRenterFegin renterFegin;
    @Autowired
    private IResThemeService resThemeService;
    @Autowired
    private IResHouseTypeService resHouseTypeService;
    @Autowired
    private IResSourceConfigureRecordService resSourceConfigureRecordService;
    @Autowired
    private IContTempService contTempService;
    @Autowired
    private ISysAreaService areaService;
    @Autowired
    private ResSourceDao sourceDao;
    @Autowired
    private ResProjectDao projectDao;
    @Autowired
    private IRptOperateInfoService rptOperateInfoService;
    @Resource
    private SaleCustomerDao customerDao;
    @Resource
    private IResSourceManageService sourceManageService;
    @Autowired
    private ISourceStateDaysService sourceStateDaysService;
    @Autowired
    private MinioUtil minioUtil;
    @Autowired
    private IExpenseProjectFegin expenseProjectFegin;
    @Autowired
    private IResRepairService resRepairService;

    //流程发起注册
    @Resource
    private IActReDeploymentService iActReDeploymentService;

    @Autowired
    private IResSourceConfigureAuditService resSourceConfigureAuditService;

    public static boolean isNumeric(String str) {
        for (int i = 0; i < str.length(); i++) {
            System.out.println(str.charAt(i));
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    @RequestMapping("judgeSourceState")
    public RestResponse judgeSourceState(String sourceId){
        Map<String, Object> result = new HashMap<>();
        ResSourceEntity source = resSourceService.getById(sourceId);
        if (!BaseConstants.BOOLEAN_OF_TRUE.equals(source.getPublishTarget()) && (SourceTypeEnum.HOUSE.getValue().equals(source.getSourceType()))) {
            result.put("msg",source.getCode() + "房源未发布");
            result.put("code","-1");
            return RestResponse.success().setData(result);
        }

        if (SourceSignEnum.NOTRENT.getValue().equals(source.getSourceSign()) || SourceSignEnum.RESERVE.getValue().equals(source.getSourceSign())) {
            result.put("code","-1");
            result.put("msg",source.getCode() + "房源" + SourceSignEnum.getNameByValue(source.getSourceSign()) + ",不能签约");
            return RestResponse.success().setData(result);
        }
        result.put("msg","sucess");
        result.put("code","1");
        return RestResponse.success().setData(result);
    }


    @RequestMapping("pageList")
    public RestResponse pageList(Page page, @RequestParam(required = false) String name, @RequestParam(required = false) String partitionId, Integer isShort,@RequestParam(required = false) String state) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("projectId", UoneHeaderUtil.getProjectId());
        map.put("name", name);
        map.put("partitionId", partitionId);
        map.put("state", state);
        if (ObjectUtil.isNotNull(isShort)) {
            map.put("isShort", isShort);
        }
        return RestResponse.success().setData(resSourceService.pageList(page, map));
    }

    @RequestMapping("isRelation")
    public RestResponse isRelation(ResSourceEntity entity) {
        if (resSourceService.getResSourceEntity(entity) != null) {
            return RestResponse.success().setData(1);
        }
        return RestResponse.success();
    }

    /**
     * 查询房源（分页）
     */
    @PostMapping("/getSourceList")
    public RestResponse getSourceList(HttpServletRequest request) {
        Page<ResSourceEntity> page = new Page<>();
        page.setCurrent(Long.parseLong(request.getParameter("current")));
        page.setSize(Long.parseLong(request.getParameter("size")));

        List<String> idList = new ArrayList();
        if (!StrUtil.isBlank(request.getParameter("ids"))) {
            String[] ids = request.getParameter("ids").split(",");

            for (int i = 0; i < ids.length; i++) {
                idList.add(ids[i]);
            }
        }

        Map<String, Object> map = Maps.newHashMap();
        map.put("ids", idList);
        map.put("partitionId", request.getParameter("partitionId"));
        map.put("floor", request.getParameter("floor"));
        map.put("sourceType", request.getParameter("sourceType"));
        map.put("projectId", UoneHeaderUtil.getProjectId());
        map.put("isUp", "1");
        map.put("publishTarget",request.getParameter("publishTarget"));
        map.put("excludeNotTalent",request.getParameter("excludeNotTalent"));
        map.put("isTalent",request.getParameter("isTalent"));
        IPage<ResSourceVo> iPage = resSourceService.getSource(page, map);

        return RestResponse.success().setData(iPage);
    }

    @RequestMapping("findByKeyword")
    public RestResponse getListByKeyword(String keyword) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("keyword", keyword);
        List<ResSourceVo> voList = resSourceService.getSourceByKey(map);
        return RestResponse.success().setData(voList);
    }

    @RequestMapping("/alterTag")
    public RestResponse alterTag(String id,String tag) throws BusinessException {
        RestResponse response=new RestResponse();
        if(StrUtil.isBlank(id)){
            response.setSuccess(false).setMessage("未传入有效房源参数");
        }
        if(StrUtil.isBlank(tag)){
            tag="0";
        }
        ResSourceEntity entity=resSourceService.getById(id);
        entity.setTag(tag);
        entity.setCcbSourceId(UoneSysUser.id());
        entity.insertOrUpdate();
        //保存报修
        if("1".equals(tag)||"2".equals(tag)||"5".equals(tag)){//1待维修，2待清洁，5漏水房
            ResRepairEntity repairEntity = new ResRepairEntity();
            repairEntity.setProjectId(entity.getProjectId());
            repairEntity.setPartitionId(entity.getPartitionId());
            repairEntity.setSourceId(entity.getId());
            repairEntity.setAllowMe(UoneSysUser.id());
            repairEntity.setStateName("未处理");
            if("1".equals(tag)){
                repairEntity.setType(RepairTypeEnum.SYSREPAIR.getValue());
                repairEntity.setSummary("标注:待维修");
            }else if("2".equals(tag)){
                repairEntity.setType(RepairTypeEnum.CLEAN.getValue());
                repairEntity.setSummary("标注:待清洁");
            }else{
                repairEntity.setType(RepairTypeEnum.SYSREPAIR.getValue());
                repairEntity.setSummary("标注:漏水房");
            }
            resRepairService.add(repairEntity, null);
        }
        return response.setSuccess(true).setMessage("已修改标签");
    }


    @RequestMapping("addOrUpdate")
    public RestResponse addOrUpdate(AddSourceVo vo) {
        if (vo == null) {
            return RestResponse.failure("参数错误");
        }
        String projectId = UoneHeaderUtil.getProjectId();
        if(StringUtils.isBlank(vo.getId()) && expenseProjectFegin.isOverSourceNum(projectId,1)){
            return RestResponse.failure("已达到房源限制数量，无法添加");
        }
        if(StrUtil.isBlank(vo.getAssetCode())){
            String assetCode = "ZC0001";
            QueryWrapper query = new QueryWrapper();
            query.likeRight("asset_code","ZC");
            query.orderByDesc("asset_code");
            ResSourceEntity last = resSourceService.getOne(query);
            if(last != null){
                assetCode = last.getAssetCode();
                int code = Integer.valueOf(StrUtil.removePrefix(assetCode,"ZC"))+1;
                assetCode = "ZC"+StrUtil.fillBefore(code+"",'0',4);
            }
            vo.setAssetCode(assetCode);
        }else{
            vo.setAssetCode(null);
        }
        vo.setProjectId(UoneHeaderUtil.getProjectId());
        ResSourceEntity jg = new ResSourceEntity();
        ResSourceCheckEntity check = null;
        ResSourceConfigureEntity sourceConfig = resSourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", vo.getId()));
        if(sourceConfig == null){
            sourceConfig =  new ResSourceConfigureEntity();
        }
        if (StringUtils.isNotBlank(vo.getId())) {
            jg.setId(vo.getId());
            ResSourceEntity old =  resSourceService.getById(vo.getId());
            if(!old.getIsShort().equals(vo.getIsShort())){
                //重置价格，取消发布
                vo.setPublishTarget(BaseConstants.BOOLEAN_OF_FALSE);
//                ResSourceConfigureEntity  sourceConfig = resSourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", vo.getId()));
//                sourceConfig.setPrice(null);
//                sourceConfig.setDeposit(null);
//                sourceConfig.setLowPrice(null);
//                sourceConfig.updateById();
            }
        } else {
            vo.setIsEffective("1");
            vo.setState(SourceStateEnum.UNRENT.getValue());
            check = new ResSourceCheckEntity();
        }
        jg.setCode(vo.getCode());
        jg.setProjectId(UoneHeaderUtil.getProjectId());
        jg.setSourceType(vo.getSourceType());
        jg.setPartitionId(vo.getPartitionId());
        if (resSourceService.getResSourceEntity(jg) != null) {
            return RestResponse.failure("该房间号已存在！");
        }
        resSourceService.saveOrUpdate(vo);
        if (check != null) {
            check.setSourceId(vo.getId());
            resSourceCheckService.save(check);
        }
        sourceConfig.setLowPrice(vo.getLowPrice());//底价
        sourceConfig.setPrice(vo.getPrice());//对外表价
        if(vo.getYearNum()==null){
            sourceConfig.setYearNum(BigDecimal.ZERO);
        }else{
            sourceConfig.setYearNum(vo.getYearNum());
        }
        if(vo.getYearIncrease()==null) {
            sourceConfig.setYearIncrease(BigDecimal.ZERO);
        }else {
            sourceConfig.setYearIncrease(vo.getYearIncrease());
        }
        if(vo.getDeposit()==null) {
            sourceConfig.setDeposit(BigDecimal.ZERO);
        }else {
            sourceConfig.setDeposit(vo.getDeposit());
        }
        if(vo.getLowPrice()!=null&&vo.getPrice()!=null){
            sourceConfig.setApprovalState("0");
            sourceConfig.setRemark("");
        }else{
            sourceConfig.setApprovalState("");
        }
        sourceConfig.setSourceId(vo.getId());
        resSourceConfigureService.saveOrUpdate(sourceConfig);
        return RestResponse.success("保存成功").setData(vo);
    }


    @RequestMapping("/getSourceInfo")
    public RestResponse getSourceInfo(@RequestParam String sourceId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("sourceId", sourceId);
        return RestResponse.success().setData(resSourceService.pageList(map));
    }

    @RequestMapping("delete")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse delete(@RequestParam String sourceId) {
        if (bilOrderService.getOne(new QueryWrapper<BilOrderEntity>().eq("source_id", sourceId)) != null) {
            return RestResponse.failure("该房源存在账单信息无法删除！");
        }
        //删除配置
        resSourceConfigureService.remove(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id",sourceId));
        //删除设备关联表
        resSourceDeviceRelService.remove(new QueryWrapper<ResSourceDeviceRelEntity>().eq("source_id", sourceId));
        //删除check表
        resSourceCheckService.remove(new QueryWrapper<ResSourceCheckEntity>().eq("source_id", sourceId));
        //删除房源
        ResSourceEntity source = resSourceService.getById(sourceId);
        String partitionId = source.getPartitionId();
        resSourceService.removeById(sourceId);
        resSourceService.updatePartitionArea(partitionId);
        resSourceService.updateHouseQuantity(partitionId);//更新房源套数
        return RestResponse.success("删除成功");
    }

    @RequestMapping("batchParking")
    @CacheLock(prefix = "batchParking", expire = 60)
    @Transactional(rollbackFor = Exception.class)
    public RestResponse batchParking(@CacheParam Integer num, @CacheParam BigDecimal rent, @CacheParam String partitionId) {
        String name = resProjectService.getById(UoneHeaderUtil.getProjectId()).getName();
        String upperCase = ChineseCharToEn.getPinYinHeadChar(name);
        int index = 1;
        QueryWrapper<ResSourceEntity> sourceEntityQueryWrapper = new QueryWrapper<ResSourceEntity>().eq("project_id", UoneHeaderUtil.getProjectId())
                .eq("partition_id", partitionId).eq("source_type","2");
        List<ResSourceEntity> cars = resSourceService.list(sourceEntityQueryWrapper);
        sourceEntityQueryWrapper.like("code", "\\_");
        sourceEntityQueryWrapper.orderByDesc("code");
        ResSourceEntity code = resSourceService.getOne(sourceEntityQueryWrapper);
        String string = null;
        String flag = null;
        if (code != null) {
            string = code.getCode().split("_")[1];
            if (isNumeric(string)) {
                int num1 = Integer.parseInt(string) + 1;
                int num2 = cars.size();
                if (num1 > num2) {
                    flag = String.valueOf(num1);
                } else if (num1 < num2) {
                    flag = String.valueOf(num2);
                } else {
                    flag = String.valueOf(num1);
                }
            }
        } else {
            flag = cars.size() + "";
        }
        int parseInt = Integer.parseInt(flag) - 1;
        for (int i = 0; i < num; i++) {
            parseInt = parseInt + 1;
            ResSourceEntity en = new ResSourceEntity();
            en.setProjectId(UoneHeaderUtil.getProjectId()).setCode(upperCase + "_" + padLeft(parseInt + "")).setSourceType("2")
                    .setPartitionId(partitionId).setIsEffective("1").setState(SourceStateEnum.UNRENT.getValue());
            resSourceService.save(en);
            ResSourceConfigureEntity sc = new ResSourceConfigureEntity();
            sc.setSourceId(en.getId());
            //sc.setDeposit(rent);
            sc.setPrice(rent);
            sc.setSummary("批量生产车位");
            resSourceConfigureService.save(sc);
        }
        return RestResponse.success();
    }

    public String padLeft(String str) {
        while (str.length() < 5) {
            str = "0" + str;
        }
        return str;
    }

    @RequestMapping("updateCar")
    @CacheLock(prefix = "updateCar", expire = 60)
    @Transactional(rollbackFor = Exception.class)
    public RestResponse updateCar(ResSourceEntity entity) {
        if (entity == null) {
            return RestResponse.failure("参数错误");
        }
        QueryWrapper<ResSourceEntity> judge = new QueryWrapper<ResSourceEntity>().eq("project_id", entity.getProjectId())
                .eq("partition_id", entity.getPartitionId()).eq("source_type", "2")
                .eq("code", entity.getCode()).ne("id", entity.getId());
        if (resSourceService.getOne(judge) != null) {
            return RestResponse.failure("车位编号重复！");
        }
        BigDecimal rent = entity.getArea();
        entity.setArea(null);
        resSourceService.updateById(entity);
        List<ResSourceConfigureEntity> res = resSourceConfigureService.list(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", entity.getId()));
        if (res.size() > 0) {
            for (ResSourceConfigureEntity r : res) {
                r.setPrice(rent);
            }
            resSourceConfigureService.updateBatchById(res);
        }

        return RestResponse.success();
    }

    @RequestMapping("getAllInfoById")
    public RestResponse getAllInfoById(@RequestParam String id) {
        Map<String, Object> map = new HashMap<>(1);
        map.put("id", id);
        return RestResponse.success().setData(resSourceService.getAllInfoById(map));
    }

    @PostMapping("uploadFile")
    public RestResponse uploadFile(@RequestParam("file") MultipartFile file, @RequestParam String type, @RequestParam String sourceId) {
        if (file.isEmpty()) {
            return RestResponse.failure("上传失败！");
        }
        //String url = FileUtil.save(file);
        String url = minioUtil.save(file);
        SysFileEntity entity = new SysFileEntity();
        entity.setUrl(url);
        entity.setType(type);
        if (StringUtils.isNotBlank(sourceId)) {
            entity.setFromId(sourceId);
        }
        sysFileService.save(entity);
        return RestResponse.success("上传成功").setData(entity);
    }

    @RequestMapping(value = "/export")
    public void export(HttpServletRequest request, HttpServletResponse response) throws BusinessException {
        Map<String, Object> map = Maps.newHashMap();
        //项目名称
        ResProjectEntity project = resProjectService.getById(UoneHeaderUtil.getProjectId());
        map.put("projectName", project.getName());

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("project_id", project.getId());
        int i = 0;
        //房屋朝向
        List<Map<String, Object>> ol = OrientationEnum.toList();
        String[] so = new String[ol.size()];
        for (Map<String, Object> m : ol) {
            so[i] = (String) m.get("name");
            i++;
        }
        map.put("orientation", StringUtils.join(so, ","));
        //主题名称
        List<ResThemeEntity> themeEntities = resThemeService.list(queryWrapper);
        String[] st = new String[themeEntities.size()];
        i = 0;
        for (ResThemeEntity t : themeEntities) {
            st[i] = t.getName();
            i++;
        }
//            map.put("themeName", StringUtils.join(st, ","));
        //装修程度
        List<Map<String, Object>> od = DegreeEnum.toList();
        i = 0;
        String[] sd = new String[od.size()];
        for (Map<String, Object> m : od) {
            sd[i] = (String) m.get("name");
            i++;
        }
        map.put("degree", StringUtils.join(sd, ","));
        //出租户型
        List<ResHouseTypeEntity> houseTypeEntities = resHouseTypeService.list(queryWrapper);
        String[] sh = new String[houseTypeEntities.size()];
        i = 0;
        for (ResHouseTypeEntity r : houseTypeEntities) {
            sh[i] = r.getName();
            i++;
        }
        map.put("houseType", StringUtils.join(sh, ","));
        //区域名称
        /*queryWrapper.ne("property_nature", PropertyNatureEnum.CAR.getValue());*/
        List<ResPlanPartitionEntity> partitionEntities = resPlanPartitionService.list(queryWrapper);
        String[] sp = new String[partitionEntities.size()];
        i = 0;
        for (ResPlanPartitionEntity p : partitionEntities) {
            sp[i] = p.getName();
            i++;
        }
        map.put("partitionName", StringUtils.join(sp, ","));
        ExcelRender.me("/excel/import/sourceTemplate.xls").beans(map).render(response);
    }


    @RequestMapping(value = "/exportCar")
    public void exportCar(HttpServletRequest request, HttpServletResponse response) throws BusinessException {
        Map<String, Object> map = Maps.newHashMap();
        //项目名称
        ResProjectEntity project = resProjectService.getById(UoneHeaderUtil.getProjectId());
        map.put("projectName", project.getName());
        int i = 0;
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("project_id", project.getId());
        //区域名称
        /* queryWrapper.eq("property_nature", PropertyNatureEnum.CAR.getValue());*/
        List<ResPlanPartitionEntity> partitionEntities = resPlanPartitionService.list(queryWrapper);
        String[] sp = new String[partitionEntities.size()];
        i = 0;
        for (ResPlanPartitionEntity p : partitionEntities) {
            sp[i] = p.getName();
            i++;
        }
        map.put("partitionName", StringUtils.join(sp, ","));
        ExcelRender.me("/excel/import/carTemplate.xls").beans(map).render(response);
    }

    @RequestMapping(value = "/statistics")
    public RestResponse statistics(Page page) {
        Map<String, Object> map = new HashMap<>(1);
        map.put("projectId", UoneHeaderUtil.getProjectId());
        return RestResponse.success().setData(resSourceService.statistics(page, map));
    }


    @RequestMapping("pricePage")
    public RestResponse pricePage(Page page, ResSourceSearchVo vo) {
        IPage iPage = resSourceService.queryListByPartitionId(page, vo);
        List<PartitionFloorSourceVo> list = iPage.getRecords();
        for (PartitionFloorSourceVo floorVo : list) {
            vo.setFloor(floorVo.getFloor());
            List<ResSourceVo> sourceVoList = resSourceService.priceNotPage(vo);
            for (ResSourceVo sourceVo : sourceVoList) {
                SourceStateDaysEntity s = sourceStateDaysService.getBySourceIdAndState(sourceVo.getId(),sourceVo.getState());
                if(s==null){
                    sourceVo.setDays(0);
                }else{
                    sourceVo.setDays(s.getDays());
                }
                sourceVo.setSourceTypeName(SourceTypeEnum.getNameByValue(sourceVo.getSourceType()));
                sourceVo.setFace(OrientationEnum.getNameByValue(sourceVo.getOrientation()));
            }
            floorVo.setSources(sourceVoList);
        }
        return RestResponse.success().setData(iPage);
    }

    @PostMapping("setSourceEffective")
    @CacheLock(prefix = "setSourceEffective", expire = 60)
    public RestResponse setSourceEffective(@CacheParam String sourceId, String effective) {
        ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
        if ("0".equals(effective)) {
            if (SourceStateEnum.RENT.getValue().equals(sourceEntity.getState())) {
                return RestResponse.failure("该房源已出租，不能标记为无效房源!");
            }
        }
        sourceEntity.setIsEffective(effective);
        resSourceService.updateById(sourceEntity);
        return RestResponse.success();
    }

    /**
     * 价格配置修改提交
     * 提交完暂时不修改价格配置主表数据,发起审批流程,等审批通过再修改价格配置主表
     * @param entity
     * @return
     * caizhanghe edit 2024-08-15
     */
    @RequestMapping("addOrUpdateConfig")
    @CacheLock(prefix = "addOrUpdateConfig", expire = 60)
    @Transactional(rollbackFor = Exception.class)
    public RestResponse addOrUpdateConfig(ResSourceConfigureEntity entity) {
        RestResponse response = new RestResponse();
        try {
            //entity.setApprovalState("0");//待审核
            //entity.setRemark("");
            ResSourceConfigureRecordEntity record = null;
            if (StringUtils.isNotBlank(entity.getId())) {
                record = new ResSourceConfigureRecordEntity();
                record.setPrice(entity.getPrice());
                record.setSourceId(entity.getSourceId());
                record.setYearIncrease(entity.getYearIncrease());
                record.setYearNum(entity.getYearNum());
            }
            //boolean b = resSourceConfigureService.saveOrUpdate(entity);//暂时不修改,等到审批通过的时候再修改
            String sourceConfigureId = entity.getId();
            ResSourceConfigureEntity oldConfigure = resSourceConfigureService.getById(sourceConfigureId);//未更新之前的记录
            oldConfigure.setApprovalState("0");//待审核
            resSourceConfigureService.updateById(oldConfigure);//更新价格配置主表为待审核状态
            String userId = UoneSysUser.id();
            String projectId = UoneHeaderUtil.getProjectId();
            String sourceId = entity.getSourceId();
            ResSourceVo resSourceVo = resSourceService.getInfoById(sourceId);
            ResSourceConfigureAuditEntity configureAudit = new ResSourceConfigureAuditEntity();
            configureAudit.setSourceName(resSourceVo.getHouseName());//房源地址
            configureAudit.setSourceConfigureId(sourceConfigureId);
            configureAudit.setSourceId(sourceId);
            configureAudit.setProjectId(projectId);
            configureAudit.setPrice(entity.getPrice());//对外表价
            configureAudit.setLowPrice(entity.getLowPrice());
            configureAudit.setDeposit(entity.getDeposit());
            configureAudit.setYearIncrease(entity.getYearIncrease());
            configureAudit.setYearNum(entity.getYearNum());
            configureAudit.setApprovalState("0");//默认待审核状态
            configureAudit.setRemark("价格配置审批");
            boolean b = resSourceConfigureAuditService.saveOrUpdate(configureAudit);//保存价格配置审批记录表
            //发起价格审批流程
            if(b){
                String configureAuditId = configureAudit.getId();
                iActReDeploymentService.priceAduitStart(configureAuditId,userId,projectId);
                //return RestResponse.success();
                return response.setSuccess(true).setMessage("保存成功");
            } else {
                return response.setSuccess(false).setMessage("保存失败");
            }
            //保存修改記錄
//            if (b && record != null) {
//                resSourceConfigureRecordService.save(record);
//            }
        } catch (Exception e){
            e.printStackTrace();
            return response.setSuccess(false).setMessage("保存失败");
        }
    }

    @RequestMapping("getFloorByProject")
    public RestResponse getFloorByProject(@RequestParam(required = false) String projectId, @RequestParam(required = false) String partitionId) {
        Map<String, Object> map = Maps.newHashMap();
        if (StringUtils.isBlank(projectId)) {
            projectId = UoneHeaderUtil.getProjectId();
        }
        map.put("projectId", projectId);
        if (StringUtils.isNotBlank(partitionId)) {
            map.put("partitionId", partitionId);
        }
        return RestResponse.success().setData(resSourceService.getFloorByProject(map));
    }

    @RequestMapping("statisticsRents")
    public RestResponse statisticsRents(ResSourceSearchVo vo) {
        Map<String,Object> result= resSourceService.statisticsRents(vo);
        List<RptOperateInfoEntity> rpts= rptOperateInfoService.list(new QueryWrapper<RptOperateInfoEntity>().eq("project_id",UoneHeaderUtil.getProjectId()).groupBy("source_type"));
        List<Map<String,String>> types=Lists.newArrayList();
        Map<String,String> all = Maps.newHashMap();
        all.put("name","全部");
        all.put("sourceType","-1");
        types.add(all);
        rpts.forEach(r->{
            Map<String,String> m = Maps.newHashMap();
            m.put("name",SourceTypeEnum.getNameByValue(r.getSourceType()));
            m.put("sourceType",r.getSourceType());
            types.add(m);
        });
        result.put("sourceTypes",types);
        return RestResponse.success().setData(result);
    }

    @RequestMapping("statisticsHouseState")
    public RestResponse statisticsHouseState() {
        return RestResponse.success().setData(resSourceService.statisticsHouseState(UoneHeaderUtil.getProjectId()));
    }

    @UoneLog("导出房源价格模板")
    @RequestMapping(value = "/exportPrice")
    public void exportPrice(HttpServletRequest request, HttpServletResponse response) throws BusinessException {
        Map<String, Object> map = Maps.newHashMap();
        //项目名称
        ResProjectEntity project = resProjectService.getById(UoneHeaderUtil.getProjectId());
        map.put("projectName", project.getName());
        //区域名称
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("project_id", project.getId());
        List<ResPlanPartitionEntity> partitionEntities = resPlanPartitionService.list(queryWrapper);
        String[] sp = new String[partitionEntities.size()];
        int i = 0;
        for (ResPlanPartitionEntity p : partitionEntities) {
            sp[i] = p.getName();
            i++;
        }
        map.put("partitionName", StringUtils.join(sp, ","));

        ExcelRender.me("/excel/import/priceTemplate.xls").beans(map).render(response);
    }

    @PostMapping(value = "/importSource", headers = "content-type=multipart/form-data")
    public RestResponse importSource(@RequestParam("file") MultipartFile multipartFile) {
        InputStream is = null;
        try {
            is = multipartFile.getInputStream();
            List<SourceImportVo> list = ExcelDataUtil.importData(is, SourceImportVo.class);
            int i = 3;
            int j = 1;
            List<ResSourceEntity> sources = new ArrayList<>();
            Set<Object> set = new HashSet<>();
            if (list.size() > 0) {
                ResProjectEntity projectEntity = resProjectService.getOne(new QueryWrapper<ResProjectEntity>().eq("name", list.get(0).getProjectName()));
                if (projectEntity == null) {
                    return RestResponse.failure("项目不存在");
                }
                if(expenseProjectFegin.isOverSourceNum(projectEntity.getId(),list.size())){
                    return RestResponse.failure("导入后房源数量将超过限制数量，无法导入！");
                }
                for (SourceImportVo vo : list) {
                    if (StringUtils.isBlank(vo.getProjectName())) {
                        return RestResponse.failure("第" + i + "行，项目名称不能为空");
                    }
                    if (StringUtils.isBlank(vo.getPartitionName())) {
                        return RestResponse.failure("第" + i + "行，区域名称不能为空");
                    }
                    if (StringUtils.isBlank(vo.getCode())) {
                        return RestResponse.failure("第" + i + "行，房间号不能为空");
                    }
//                        if (StringUtils.isBlank(vo.getFloor())) {
//                            return RestResponse.failure("第" + i + "行，所在楼层不能为空");
//                        }
//                        if (StringUtils.isBlank(vo.getThemeName())) {
//                            return RestResponse.failure("第" + i + "行，主题名称不能为空");
//                        }

//                        if (StringUtils.isBlank(vo.getOrientation())) {
//                            return RestResponse.failure("第" + i + "行，房屋朝向不能为空");
//                        }
                    if (StringUtils.isBlank(vo.getArea())) {
                        return RestResponse.failure("第" + i + "行，房屋面积不能为空");
                    }
//                        if (StringUtils.isBlank(vo.getCheckinMax())) {
//                            return RestResponse.failure("第" + i + "行，入住人上限不能为空");
//                        }
                        if (StringUtils.isBlank(vo.getHouseType())) {
                            return RestResponse.failure("第" + i + "行，出租户型不能为空");
                        }
//                        if (StringUtils.isBlank(vo.getDegree())) {
//                            return RestResponse.failure("第" + i + "行，装修程度不能为空");
//                        }
//                    if (ObjectUtil.isNull(vo.getPrice())) {
//                        return RestResponse.failure("第" + i + "行，对外表价不能为空");
//                    }
//                    if (ObjectUtil.isNull(vo.getLowPrice())) {
//                        return RestResponse.failure("第" + i + "行，底价不能为空");
//                    }
                    if (!ObjectUtil.isNull(vo.getPrice())&&!ObjectUtil.isNull(vo.getLowPrice())) {
                        if (vo.getPrice().compareTo(vo.getLowPrice()) == -1) {
                            return RestResponse.failure("第" + i + "行，对外表价不能低于低价");
                        }
                    }
//                    if (vo.getPrice().compareTo(vo.getLowPrice()) == -1) {
//                        return RestResponse.failure("第" + i + "行，对外表价不能低于低价");
//                    }
                    set.add("{"+vo.getCode()+","+vo.getPartitionName()+"}");
                    if (set.size() != j) {
                        return RestResponse.failure("第" + i + "行，房间号不能重复");
                    }
                    //区域
                    ResPlanPartitionEntity plan = resPlanPartitionService.getOne(new QueryWrapper<ResPlanPartitionEntity>().eq("project_id", projectEntity.getId()).eq("name", vo.getPartitionName()));
                    if(plan == null){
                        return RestResponse.failure("第" + i + "行，楼栋不存在");
                    }
                    //主题
                    // ResThemeEntity theme = resThemeService.getOne(new QueryWrapper<ResThemeEntity>().eq("project_id", projectEntity.getId()).eq("name", vo.getThemeName()));
                    //出租户型
                    ResHouseTypeEntity houseTypeEntity = resHouseTypeService.getOne(new QueryWrapper<ResHouseTypeEntity>().eq("project_id", projectEntity.getId()).eq("name", vo.getHouseType()));
                    ResSourceEntity source = new ResSourceEntity();
                    source.setProjectId(projectEntity.getId());
                    source.setSourceType(plan.getPropertyNature());
                    source.setCode(vo.getCode());
                    source.setPartitionId(plan.getId());
                    //判断房间是否存在
                    if (resSourceService.getResSourceEntity(source) != null) {
                        return RestResponse.failure("第" + i + "行房间号已存在");
                    }
//                    source.setThemeId(theme.getId());
                    if(houseTypeEntity==null){
                        return RestResponse.failure("第" + i + "行，出租户型不存在");
                    }
                    source.setHouseTypeId(houseTypeEntity.getId());
                    source.setRentCode(vo.getRentCode());
                    source.setFloor(Integer.valueOf(vo.getFloor()));
                    //如果有填了入住人上限，再保存
                    if (StringUtils.isNotBlank(vo.getCheckinMax())) {
                        source.setCheckinMax(Integer.valueOf(vo.getCheckinMax()));
                    }
                    source.setIsEffective(BaseConstants.BOOLEAN_OF_TRUE);
                    source.setPublishTarget(BaseConstants.BOOLEAN_OF_FALSE);
                    source.setState(SourceStateEnum.UNRENT.getValue());
                    source.setArea(new BigDecimal(vo.getArea()));
                    source.setOrientation(OrientationEnum.getValueByName(vo.getOrientation()));
                    source.setDegree(DegreeEnum.getValueByName(vo.getDegree()));
                    source.setIsShort(0);
                    source.setAssetAttr(vo.getAssetAttr());
                    source.setAssetCode(vo.getAssetCode());
                    //source.setAssetDisposalPrice(vo.getAssetDisposalPrice());
                    //source.setAssetDisposalTime(vo.getAssetDisposalTime());
                    source.setAssetDisposalWay(vo.getAssetDisposalWay());
                    //source.setAssetEndtime(vo.getAssetEndtime());
                    source.setAssetGainWay(vo.getAssetGainWay());
                    //source.setAssetMarketPrice(vo.getAssetMarketPrice());
                    source.setAssetNature(vo.getAssetNature());
                    source.setAssetOwnership(vo.getAssetOwnership());
                    source.setAssetRateAgency(vo.getAssetRateAgency());
                    //source.setAssetStorePrice(vo.getAssetStorePrice());
                    //source.setAssetStoreTime(vo.getAssetStoreTime());
                    source.setAssetType(vo.getAssetType());
                    source.setAssetUse(vo.getAssetUse());
                    source.setRealNumber(vo.getRealNumber());
                    source.setRemark(vo.getRemark());
                    ResSourceConfigureEntity config = new ResSourceConfigureEntity();
                    if(vo.getPrice()!=null&&vo.getLowPrice()!=null){
                        config.setApprovalState("0");//价格审核状态
                        config.setRemark("");
                    }
                    config.setPrice(vo.getPrice());
                    config.setLowPrice(vo.getLowPrice());
                    if(vo.getYearIncrease()==null) {
                        config.setYearIncrease(BigDecimal.ZERO);
                    }else {
                        config.setYearIncrease(vo.getYearIncrease());
                    }
                    if(vo.getYearNum()==null){
                        config.setYearNum(BigDecimal.ZERO);
                    }else{
                        config.setYearNum(vo.getYearNum());
                    }
                    if(vo.getDeposit()==null) {
                        config.setDeposit(BigDecimal.ZERO);
                    }else {
                        config.setDeposit(vo.getDeposit());
                    }
                    //房屋描述
                    if (StringUtils.isNotBlank(vo.getSummary())) {
                        config.setSummary(vo.getSummary());
                    }
                    //房屋标签
                    if (StringUtils.isNotBlank(vo.getHouseLabel())) {
                        String[] labels = vo.getHouseLabel().split(",");
                        String sb = "";
                        for (String s : labels) {
                            sb += HouseLabelEnum.getValueByName(s) + ",";
                        }
                        config.setHouseLabel(sb.substring(0, sb.length() - 1));
                    }
                    //私有配置
                    if (StringUtils.isNotBlank(vo.getHouseConf())) {
                        String[] labels = vo.getHouseConf().split(",");
                        String sb = "";
                        for (String s : labels) {
                            sb += HouseConfEnum.getValueByName(s) + ",";
                        }
                        config.setHouseConf(sb.substring(0, sb.length() - 1));
                    }
                    //共用配置
                    if (StringUtils.isNotBlank(vo.getPublicConf())) {
                        String[] labels = vo.getPublicConf().split(",");
                        String sb = "";
                        for (String s : labels) {
                            sb += PublicConfEnum.getValueByName(s) + ",";
                        }
                        config.setPublicConf(sb.substring(0, sb.length() - 1));
                    }
                    source.setSourceConfigureEntity(config);
                    sources.add(source);
                    i++;
                    j++;
                }
                resSourceService.saveBatch(sources);
                for (ResSourceEntity r : sources) {
                    ResSourceCheckEntity ck = new ResSourceCheckEntity();
                    ck.setSourceId(r.getId());
                    resSourceCheckService.save(ck);
                    ResSourceConfigureEntity config = r.getSourceConfigureEntity();
                    config.setSourceId(r.getId());
                    config.setApprovalState("1");
                    resSourceConfigureService.save(config);
                }
            }else{
                return RestResponse.failure("导入失败！");
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return RestResponse.success();
    }


    @PostMapping(value = "/importCar", headers = "content-type=multipart/form-data")
    public RestResponse importCar(@RequestParam("file") MultipartFile multipartFile) {
        InputStream is = null;
        try {
            is = multipartFile.getInputStream();
            List<SourceImportVo> list = ExcelDataUtil.importData(is, SourceImportVo.class);
            int i = 3;
            List<ResSourceEntity> sources = new ArrayList<>();
            ResHouseTypeEntity houseType = null;
            String str = "";
            if (list.size() > 0) {
                ResProjectEntity project = resProjectService.getOne(new QueryWrapper<ResProjectEntity>().eq("name", list.get(0).getProjectName()));
                if (!project.getId().equals(UoneHeaderUtil.getProjectId())) {
                    return RestResponse.failure("导入项目必须为当前项目");
                }
                houseType = resHouseTypeService.getOne(new QueryWrapper<ResHouseTypeEntity>().eq("project_id", UoneHeaderUtil.getProjectId()).eq("name", "P"));
            }
            for (SourceImportVo vo : list) {
                String msg = "";
                if (StrUtil.isBlank(vo.getProjectName())) {
                    msg += "第" + i + "行项目不能为空,";
                    str += msg;
                    i++;
                    continue;
                }
                if (StrUtil.isBlank(vo.getPartitionName())) {
                    msg += "第" + i + "行区域编号不能为空,";
                    str += msg;
                    i++;
                    continue;
                }
                if (StrUtil.isBlank(vo.getCode())) {
                    msg += "第" + i + "行车位编号不能为空,";
                    str += msg;
                    i++;
                    continue;
                }
                ResPlanPartitionEntity par = resPlanPartitionService.getOne(new QueryWrapper<ResPlanPartitionEntity>().eq("project_id", UoneHeaderUtil.getProjectId()).eq("name", vo.getPartitionName()));
                if (ObjectUtil.isNull(par)) {
                    msg += "第" + i + "区域不存在,";
                }
                //车位编号校验
                if (ObjectUtil.isNotNull(resSourceService.getOne(new QueryWrapper<ResSourceEntity>().eq("project_id", UoneHeaderUtil.getProjectId()).eq("code", vo.getCode()).eq("source_type", "2")))) {
                    msg += "第" + i + "车位已存在,";
                }

                if (StrUtil.isBlank(msg)) {
                    ResSourceEntity souce = new ResSourceEntity();
                    souce.setProjectId(UoneHeaderUtil.getProjectId());
                    souce.setPartitionId(par.getId());
                    souce.setCode(vo.getCode());
                    souce.setSourceType("2");
                    if (ObjectUtil.isNotNull(houseType)) {
                        souce.setHouseTypeId(houseType.getId());
                    }
                    souce.setIsEffective(BaseConstants.BOOLEAN_OF_TRUE);
                    souce.setState(SourceStateEnum.UNRENT.getValue());
                    souce.setRent(vo.getPrice());
                    sources.add(souce);
                }
                str += msg;
                i++;
            }
            if (StrUtil.isBlank(str)) {
                for (ResSourceEntity source : sources) {
                    resSourceService.save(source);
                    ResSourceConfigureEntity sc = new ResSourceConfigureEntity();
                    sc.setSourceId(source.getId());
                    sc.setPrice(source.getRent());
                    sc.setSummary("导入车位");
                    resSourceConfigureService.save(sc);
                }
            } else {
                return RestResponse.failure(str);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return RestResponse.success();
    }

    @RequestMapping("importPrice")
    public RestResponse importPrice(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return RestResponse.failure("请选择上传文件");
        }
        StringBuilder msg = new StringBuilder();
        String projectId = UoneHeaderUtil.getProjectId();
        try {
            List<SourcePriceExportVo> list = ExcelDataUtil.importData(file.getInputStream(), SourcePriceExportVo.class);
            if (list.size() > 0) {
                Set set = new HashSet();
                for (SourcePriceExportVo vo : list) {
                    set.add(vo.getProjectName());
                }
                if (set.size() != 1) {
                    return RestResponse.failure("导入项目必须为当前选择项目！");
                }
                ResProjectEntity pro = resProjectService.getOne(new QueryWrapper<ResProjectEntity>().eq("name", list.get(0).getProjectName()));
                if (pro == null) {
                    return RestResponse.failure("导入项目必须为当前选择项目！");
                }
                if (!pro.getId().equals(projectId)) {
                    return RestResponse.failure("导入项目必须为当前选择项目！");
                }
                Set<ResSourceConfigureEntity> prices = new HashSet<ResSourceConfigureEntity>();
                int index = 3;
                for (SourcePriceExportVo vo : list) {
                    //验证区域
                    ResPlanPartitionEntity pe = resPlanPartitionService.getOne(new QueryWrapper<ResPlanPartitionEntity>().eq("project_id", projectId).eq("name", vo.getPartitionName()));
                    if (pe == null) {
                        msg.append(String.format("第%s行:区域编号不存在,请修改后重新导入。<br/>", index));
                        index++;
                        continue;
                    }
                    //验证房源
                    ResSourceEntity sourceEntity = resSourceService.getOne(new QueryWrapper<ResSourceEntity>().eq("project_id", projectId).eq("partition_id", pe.getId()).eq("code", vo.getCode()).eq("source_type", pe.getPropertyNature()));
                    if (sourceEntity == null) {
                        msg.append(String.format("第%s行:房屋编号不存在,请修改后重新导入。<br/>", index));
                        index++;
                        continue;
                    }
                    if(SourceStateEnum.BOOKED.getValue().equals(sourceEntity.getState())){
                        msg.append(String.format("第%s行:房源已预定,无法修改价格。<br/>", index));
                        index++;
                        continue;
                    }
                    if(SourceStateEnum.RENT.getValue().equals(sourceEntity.getState())){
                        msg.append(String.format("第%s行:房源已出租,无法修改价格。<br/>", index));
                        index++;
                        continue;
                    }

                    if(StrUtil.isNotBlank(vo.getPrice()) && StrUtil.isNotBlank(vo.getLowPrice())){
                        if (new BigDecimal(vo.getPrice()).compareTo(new BigDecimal(vo.getLowPrice())) == -1) {
                            msg.append(String.format("第%s行:对外表价不能低于低价。<br/>", index));
                            index++;
                            continue;
                        }
                    }
                    ResSourceConfigureEntity p = resSourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", sourceEntity.getId()));
                    if(ObjectUtil.isNotNull(p)){
                        if(StrUtil.isNotBlank(vo.getPrice())&&StrUtil.isBlank(vo.getLowPrice())){
                            if(new BigDecimal(vo.getPrice()).compareTo(p.getLowPrice())==-1){
                                msg.append(String.format("第%s行:对外表价不能低于低价。<br/>", index));
                                index++;
                                continue;
                            }
                        }else if(StrUtil.isBlank(vo.getPrice())&&StrUtil.isNotBlank(vo.getLowPrice())){
                            if(p.getPrice().compareTo(new BigDecimal(vo.getLowPrice()))==-1){
                                msg.append(String.format("第%s行:对外表价不能低于低价。<br/>", index));
                                index++;
                                continue;
                            }
                        }
                    }else{
                        if(StrUtil.isNotBlank(vo.getLowPrice())&&StrUtil.isBlank(vo.getPrice())){
                            msg.append(String.format("第%s行:对外表价不能低于低价。<br/>", index));
                            index++;
                            continue;
                        }

                    }

                    //取消发布(房源)
                    resSourceService.getById(sourceEntity.getId()).setPublishTarget(BaseConstants.BOOLEAN_OF_FALSE).updateById();
                    if (p == null) {
                        p = new ResSourceConfigureEntity();
                    }
                    p.setSourceId(sourceEntity.getId());
                    //设置审核状态为待审核
                    p.setApprovalState("0");
                    p.setRemark("");
                    if(StrUtil.isNotBlank(vo.getYearIncrease())){
                        p.setYearIncrease(new BigDecimal(vo.getYearIncrease()));
                    }
                    if(StrUtil.isNotBlank(vo.getLowPrice())){
                        p.setLowPrice(new BigDecimal(vo.getLowPrice()));
                    }
                    if(StrUtil.isNotBlank(vo.getDeposit())){
                        p.setDeposit(new BigDecimal(vo.getDeposit()));
                    }
                    if(StrUtil.isNotBlank(vo.getPrice())){
                        p.setPrice(new BigDecimal(vo.getPrice()));
                    }
                    p.setApprovalState("1");
                    prices.add(p);
                    index++;
                }
                if(CollectionUtil.isNotEmpty(prices)){
                    resSourceConfigureService.saveOrUpdateBatch(prices);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (StrUtil.isNotBlank(msg.toString())) {
            return RestResponse.failure(msg.toString());
        }
        return RestResponse.success("导入成功");
    }



//        /**
//         * 房源运营-房源列表
//         */
//        @RequestMapping("/resourceOperation/list")
//        public RestResponse listByResourceOperation(Page page, ResSourceSearchVo vo) {
//            IPage iPage = resSourceService.queryListByResourceOperation(page, vo);
//            List<ResSourceVo> list = iPage.getRecords();
//            for (ResSourceVo sourceVo : list) {
//                sourceVo.setSourceTypeName(SourceTypeEnum.getNameByValue(sourceVo.getSourceType()));
//                sourceVo.setFace(OrientationEnum.getNameByValue(sourceVo.getOrientation()));
//            }
//
//            return RestResponse.success().setData(iPage);
//        }

    /**
     * 房源运营-房源列表
     */
    @RequestMapping("/resourceOperation/list")
    public RestResponse listByResourceOperation(Page page, ResSourceSearchVo vo) {
//            sourceStateDaysService.timedTask();
        Map<String,List<ResSourceVo>> map = Maps.newHashMap();
        List<ResSourceVo> sourceVoList = resSourceService.listByResourceOperation(vo);
        for(ResSourceVo source: sourceVoList){
            Integer floor = source.getFloor();
            List<ResSourceVo> sourceList = map.get(floor+"");
            if(sourceList==null){
                sourceList = Lists.newArrayList();
                //floorVo.setFloor(floor+"");
                //floorVo.setLeased("0");
                //floorVo.setSourceTotal("0");
                //floorVo.setAreaTotal("0");
                //floorVo.setPartitionName(source.getPartitionName());
                //floorVo.setLeasedArea("0");
            }
            //int leased = Integer.parseInt(floorVo.getLeased()) + (source.getState().equals("2")?1:0);
            //int sourceTotal = Integer.parseInt(floorVo.getSourceTotal()) +1;
            //BigDecimal areaTotal = new BigDecimal(floorVo.getAreaTotal()).add(source.getArea());
            //BigDecimal leasedArea = new BigDecimal(floorVo.getLeasedArea()).add(source.getState().equals("2")?source.getArea():BigDecimal.ZERO);
            sourceList.add(source);
            //floorVo.setLeased(leased+"");
            //floorVo.setSourceTotal(sourceTotal+"");
            //floorVo.setAreaTotal(areaTotal.toString());
            //floorVo.setLeasedArea(leasedArea.toString());
            map.put(floor+"", sourceList);
        }
        // 将Map中的值转换为List
        //List<PartitionFloorSourceVo> list = CollectionUtil.newArrayList(map.values());
        vo.setPublishTarget("1");
        IPage iPage = resSourceService.queryListByPartitionId(page, vo);
        List<PartitionFloorSourceVo> list = iPage.getRecords();
        for (PartitionFloorSourceVo floorVo : list) {
            List<ResSourceVo> sourceList = (List<ResSourceVo>) map.get(floorVo.getFloor());
            floorVo.setSources(sourceList);
        }


        /*IPage iPage = resSourceService.queryListByPartitionId(page, vo);
        List<PartitionFloorSourceVo> list = iPage.getRecords();
        for (PartitionFloorSourceVo floorVo : list) {
            vo.setFloor(floorVo.getFloor());
            List<ResSourceVo> sourceVoList = resSourceService.listByResourceOperation(vo);
//            for (ResSourceVo sourceVo : sourceVoList) {
//                sourceVo.setSourceTypeName(SourceTypeEnum.getNameByValue(sourceVo.getSourceType()));
//                sourceVo.setFace(OrientationEnum.getNameByValue(sourceVo.getOrientation()));
//            }
            floorVo.setSources(sourceVoList);
        }*/
        return RestResponse.success().setData(iPage);
    }
    /**
     * 房源运营-房源点击查看
     */
    @PostMapping("/resourceOperation/view")
    public RestResponse viewByResourceOperation(@RequestParam("id") String id) throws Exception {
        ResSourceVo sourceVo = resSourceService.getSourceVoByResourceOperation(id);
        ContContractEntity contractEntity = contContractService.getById(sourceVo.getContractId());
        if (ObjectUtil.isNotNull(contractEntity)) {
            sourceVo.setStartDate(contractEntity.getStartDate());
            sourceVo.setEndDate(contractEntity.getEndDate());
            sourceVo.setGd(contractEntity.isGd());
        }
        return RestResponse.success().setData(sourceVo);
    }

    /**
     * 前台--首页--热门项目
     *
     * @return
     */
    @RequestMapping("/getProjectMinPrice")
    @UonePermissions
    public RestResponse getProjectMinPrice() {
        Map<String,Object> map=Maps.newHashMap();
        map.put("cityCode",UoneHeaderUtil.getCityCode());
        List<ResSourceFileVo> list = resSourceService.getProjectMinPrice(map);
        return RestResponse.success().setData(list);
    }

    /*
      project项目发起签约时获取房源列表
     */
    @RequestMapping("/roomList")
    public RestResponse queryRoomList(@RequestParam(value = "id", required = false) String id,@RequestParam(value = "state", required = false) String state) {
        Map<String,Object> map = Maps.newHashMap();
        String isShort="0";
        if (StringUtils.isNotBlank(id)) {
            map.put("partitionId", id);
        } else {
            return RestResponse.success().setData(Lists.newArrayList());
        }
        if (StringUtils.isNotBlank(state)) {
            map.put("state", state);
        }
        map.put("isShort", isShort);
        map.put("publishTarget","1");
        List<ResSourceVo> list = resSourceService.queryRoomList(map);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/queryRoomList")
    public RestResponse queryRoomList(@RequestParam(value = "partitionId", required = false) String partitionId,
                                      @RequestParam(value = "state", required = false) String state,
                                      @RequestParam(value = "contractStates", required = false) String contractStates,
                                      String renterEffect,
                                      @RequestParam(value = "lastMonth", required = false) String lastMonth,
                                      @RequestParam(value = "isUp", required = false,defaultValue = "1") String isUp,
                                      @RequestParam(value = "sourceType", required = false) String sourceType,
                                      @RequestParam(value = "sourceTypes", required = false) String sourceTypes,
                                      @RequestParam(value = "isShort", required = false) String isShort,
                                      @RequestParam(value = "publishTarget", required = false) String publishTarget,
                                      String isChange,
                                      String platform,
                                      String isTalent) {
        Map<String,Object> map = Maps.newHashMap();
        if (StringUtils.isNotBlank(partitionId)) {
            map.put("partitionId", partitionId);
        } else {
            return RestResponse.success().setData(Lists.newArrayList());
        }
        map.put("isUp", isUp);
        if(StrUtil.isNotEmpty(state)){
            map.put("state", state);
        }
        if(StrUtil.isNotEmpty(publishTarget)){
            map.put("publishTarget", publishTarget);
        }
        if(StrUtil.isNotEmpty(contractStates)){
            map.put("contractStates", contractStates);
        }
        if (StringUtils.isNotBlank(renterEffect)) {
            map.put("renterEffect", renterEffect);
        }
        if (StringUtils.isNotBlank(isShort)) {
            map.put("isShort", isShort);
        }
        if(StrUtil.isNotBlank(lastMonth)){
            map.put("lastMonth", lastMonth);
        }
        if(StrUtil.isNotBlank(sourceType)){
            map.put("sourceType", sourceType);
        }
        if(StrUtil.isNotBlank(isTalent)){
            map.put("isTalent", isTalent);
        }
        if(StrUtil.isNotBlank(isChange)){
            map.put("isChange", isChange);
        }
        if(StrUtil.isNotBlank(platform)){
            map.put("platform", platform);
        }
        if(StrUtil.isNotBlank(sourceTypes)){
            String[] sourceTypeArray = sourceTypes.split(",");
            List<String> sourceTypeList = Arrays.asList(sourceTypeArray);
            map.put("sourceTypes", sourceTypeList);
        }
        List<ResSourceVo> list = resSourceService.queryRoomList(map);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/queryRoomList2")
    @UonePermissions
    public RestResponse queryRoomList2(@RequestParam(value = "partitionId", required = false) String partitionId,
                                       @RequestParam(value = "state", required = false) String state,
                                       @RequestParam(value = "contractStates", required = false) String contractStates,
                                       String renterEffect,
                                       @RequestParam(value = "lastMonth", required = false) String lastMonth,
                                       @RequestParam(value = "isUp", required = false,defaultValue = "1") String isUp,
                                       @RequestParam(value = "sourceType", required = false) String sourceType,
                                       @RequestParam(value = "sourceTypes", required = false) String sourceTypes,
                                       @RequestParam(value = "isShort", required = false) String isShort,
                                       String isChange,
                                       String platform,
                                       String isTalent) {
        Map<String,Object> map = Maps.newHashMap();
        if (StringUtils.isNotBlank(partitionId)) {
            map.put("partitionId", partitionId);
        } else {
            return RestResponse.success().setData(Lists.newArrayList());
        }
        map.put("isUp", isUp);
        if(StrUtil.isNotEmpty(state)){
            map.put("state", state);
        }
        if(StrUtil.isNotEmpty(contractStates)){
            map.put("contractStates", contractStates);
        }
        if (StringUtils.isNotBlank(renterEffect)) {
            map.put("renterEffect", renterEffect);
        }
        if (StringUtils.isNotBlank(isShort)) {
            map.put("isShort", isShort);
        }
        if(StrUtil.isNotBlank(lastMonth)){
            map.put("lastMonth", lastMonth);
        }
        if(StrUtil.isNotBlank(sourceType)){
            map.put("sourceType", sourceType);
        }
        if(StrUtil.isNotBlank(isTalent)){
            map.put("isTalent", isTalent);
        }
        if(StrUtil.isNotBlank(isChange)){
            map.put("isChange", isChange);
        }
        if(StrUtil.isNotBlank(platform)){
            map.put("platform", platform);
        }
        if(StrUtil.isNotBlank(sourceTypes)){
            String[] sourceTypeArray = sourceTypes.split(",");
            List<String> sourceTypeList = Arrays.asList(sourceTypeArray);
            map.put("sourceTypes", sourceTypeList);
        }
        List<ResSourceVo> list = resSourceService.queryRoomList(map);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/queryAvailableRoom")
    public RestResponse queryAvailable(@RequestParam(value = "partitionId", required = false) String partitionId,
                                       @RequestParam(value = "state", required = false) String state,
                                       @RequestParam(value = "contractStates", required = false) String contractStates,
                                       String renterEffect,
                                       @RequestParam(value = "lastMonth", required = false) String lastMonth,
                                       @RequestParam(value = "isUp", required = false,defaultValue = "1") String isUp,
                                       @RequestParam(value = "sourceType", required = false) String sourceType,
                                       @RequestParam(value = "sourceTypes", required = false) String sourceTypes,
                                       @RequestParam(value = "isShort", required = false) String isShort,
                                       String isChange,
                                       String platform,
                                       String isTalent) {
        Map<String,Object> map = Maps.newHashMap();
        if (StringUtils.isNotBlank(partitionId)) {
            map.put("partitionId", partitionId);
        } else {
            return RestResponse.success().setData(Lists.newArrayList());
        }
        map.put("isUp", isUp);
        if(StrUtil.isNotEmpty(state)){
            map.put("state", state);
        }
        if(StrUtil.isNotEmpty(contractStates)){
            map.put("contractStates", contractStates);
        }
        if (StringUtils.isNotBlank(renterEffect)) {
            map.put("renterEffect", renterEffect);
        }
        if (StringUtils.isNotBlank(isShort)) {
            map.put("isShort", isShort);
        }
        if(StrUtil.isNotBlank(lastMonth)){
            map.put("lastMonth", lastMonth);
        }
        if(StrUtil.isNotBlank(sourceType)){
            map.put("sourceType", sourceType);
        }
        if(StrUtil.isNotBlank(isTalent)){
            map.put("isTalent", isTalent);
        }
        if(StrUtil.isNotBlank(isChange)){
            map.put("isChange", isChange);
        }
        if(StrUtil.isNotBlank(platform)){
            map.put("platform", platform);
        }
        if(StrUtil.isNotBlank(sourceTypes)){
            String[] sourceTypeArray = sourceTypes.split(",");
            List<String> sourceTypeList = Arrays.asList(sourceTypeArray);
            map.put("sourceTypes", sourceTypeList);
        }
        List<ResSourceVo> list = resSourceService.queryAvailable(map);
        return RestResponse.success().setData(list);
    }

    @UonePermissions
    @RequestMapping("/c/cSourcePage")
    public RestResponse cSourcePage(Page page, ResSourceSearchVo sourceSearchVo) {
        sourceSearchVo.setPublishTarget("1");
        IPage list = resSourceService.cSourcePage(page, sourceSearchVo);
        List<ResSourceVo> res = list.getRecords();
        for (ResSourceVo vo : res) {
            vo.setUrl(FileUtil.getPath(vo.getUrl()));
            vo.setSourceTypeName(SourceTypeEnum.getNameByValue(vo.getSourceType()));
            vo.setOrientation(OrientationEnum.getNameByValue(vo.getOrientation()));
        }
        return RestResponse.success().setData(list);
    }

    @UonePermissions
    @RequestMapping("/c/cSourceDetail")
    public RestResponse cSourceDetail(@RequestParam("sourceId") String sourceId) {
        Map<String, Object> map = Maps.newHashMap();
        //房源信息
        ResSourceVo sourceVo = resSourceService.cSourceDetail(sourceId);
        sourceVo.setFace(OrientationEnum.getNameByValue(sourceVo.getOrientation()));
        sourceVo.setSourceTypeName(SourceTypeEnum.getNameByValue(sourceVo.getSourceType()));
        if (StringUtils.isNotBlank(sourceVo.getWoshiurl())) {
            sourceVo.setWoshiurl(FileUtil.getPath(sourceVo.getWoshiurl()));
        }
        List<DevDeviceEntityVo> devices = resSourceService.cSourceDevice(sourceId);
        List<String> pics = Lists.newArrayList();
        if (StringUtils.isNotBlank(sourceVo.getKetingurl())) {
            pics = Arrays.asList(sourceVo.getKetingurl().split(","));
            for (int i = 0; i < pics.size(); i++) {
                pics.set(i, FileUtil.getPath(pics.get(i)) );
            }
        }
        map.put("sourceVo", sourceVo);
        map.put("devices", devices);
        map.put("pics", pics);
        return RestResponse.success().setData(map);
    }

    @UonePermissions
    @RequestMapping("/c/cGetProjectMinPriceByType")
    public RestResponse cGetProjectMinPriceByType(Page page, @RequestParam(value = "sourceType", required = false) String sourceType, @RequestParam(value = "projectId", required = false) String projectId, @RequestParam(value = "sourceId", required = false) String sourceId, @RequestParam(value = "keyword", required = false) String keyword, @RequestParam(value = "houseTypeId", required = false) String houseTypeId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("sourceType", sourceType);
        if (StringUtils.isNotBlank(projectId)) {
            map.put("projectId", projectId);
        }
        if (StringUtils.isNotBlank(sourceId)) {
            map.put("sourceId", sourceId);
        }
        if (StringUtils.isNotBlank(keyword)) {
            map.put("keyword", keyword);
        }
        if (StringUtils.isNotBlank(houseTypeId)) {
            map.put("houseTypeId", houseTypeId);
        }
        map.put("cityCode", UoneHeaderUtil.getCityCode());
        IPage<ResSourceVo> record = resSourceService.getProjectMinPriceByType(page, map);
        for (ResSourceVo vo : record.getRecords()) {
            vo.setUrl(FileUtil.getPath(vo.getUrl()));
        }
        return RestResponse.success().setData(record);
    }

    @UonePermissions
    @RequestMapping("/c/cGetProjectCarMinPrice")
    public RestResponse cGetProjectCarMinPrice(Page page, @RequestParam(required = false) String keyword) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("keyword", keyword);
        map.put("cityCode", UoneHeaderUtil.getCityCode());
        IPage<ResSourceVo> records = resSourceService.cGetProjectCarMinPrice(page, map);
        for (ResSourceVo vo : records.getRecords()) {
            vo.setUrl(FileUtil.getPath(vo.getUrl()));
        }
        return RestResponse.success().setData(records);
    }

    @UonePermissions
    @RequestMapping("/c/cGetProjectHouseType")
    public RestResponse cGetProjectHouseType(@RequestParam("sourceType") String sourceType,
                                             @RequestParam("projectId") String projectId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("sourceType", sourceType);
        map.put("projectId", projectId);
        List<ResSourceVo> list = resSourceService.getProjectMinPriceByType(map);
        for (ResSourceVo vo : list) {
            vo.setUrl(FileUtil.getPath(vo.getUrl()));
        }
        List<ResSourceVo> housetype = resSourceService.getHouseTypeMinPriceByType(map);
        for (ResSourceVo vo : housetype) {
            vo.setUrl(FileUtil.getPath(vo.getUrl()));
        }
        List<SysFileEntity> pics = sysFileService.list(new QueryWrapper<SysFileEntity>().eq("from_id", projectId).eq("type", "9"));
        map.put("project", list.get(0));
        map.put("pics", pics);
        map.put("housetype", housetype);
        return RestResponse.success().setData(map);
    }

    @UonePermissions
    @RequestMapping("/c/cGetProjectSource")
    public RestResponse cGetProjectSource(@RequestParam("sourceType") String sourceType, @RequestParam("projectId") String projectId, @RequestParam("houseTypeId") String houseTypeId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("sourceType", sourceType);
        map.put("projectId", projectId);
        map.put("houseTypeId", houseTypeId);
        List<ResSourceVo> list = resSourceService.getProjectMinPriceByType(map);
        for (ResSourceVo vo : list) {
            vo.setUrl(FileUtil.getPath(vo.getUrl()));
        }
        map.put("houseTypeId", houseTypeId);
        List<ResSourceVo> source = resSourceService.getSourceByType(map);
        for (ResSourceVo vo : source) {
            vo.setUrl(FileUtil.getPath(vo.getUrl()));
        }
        List<SysFileEntity> pics = sysFileService.list(new QueryWrapper<SysFileEntity>().eq("from_id", CollectionUtil.isEmpty(list)?null:list.get(0).getThemeId()));

        map.put("project", CollectionUtil.isEmpty(list)?null:list.get(0));
        map.put("pics", pics);
        map.put("source", source);
        return RestResponse.success().setData(map);
    }

    @UonePermissions
    @RequestMapping("/c/cGetMapProject")
    public RestResponse cGetMapProject(ResSourceSearchVo sourceSearchVo) {
        List<ResSourceVo> maps=resSourceService.getMapProject(sourceSearchVo);
        for(ResSourceVo s:maps){
            if(StrUtil.isNotBlank(s.getLatitude())&& StrUtil.isNotBlank(s.getLongitude())){
                GPS gps= BaiduMapUtil.bd09_To_Gcj02(Double.valueOf(s.getLatitude()),Double.valueOf(s.getLongitude()));
                s.setLatitude(gps.getLat()+"");
                s.setLongitude(gps.getLon()+"");
            }
        }
        return RestResponse.success().setData(maps);
    }

    @UonePermissions
    @RequestMapping("/c/cGetProjectTimeInfo")
    public RestResponse cGetProjectTimeInfo(@RequestParam("projectId") String projectId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, Object> map = Maps.newHashMap();
        String start = simpleDateFormat.format(new Date());
        String end = simpleDateFormat.format(DateUtils.addYears(new Date(), 10));
        String startTime = "00:00:00";
        String endTime = "23:59:00";
        ResProjectInfoEntity entity = resProjectInfoService.getOne(new QueryWrapper<ResProjectInfoEntity>().eq("project_id", projectId));
        if (entity != null) {
            if (entity.getOrderEndDay() != null) {
                end = simpleDateFormat.format(DateUtils.addDays(new Date(), entity.getOrderEndDay()));
            }

            if (entity.getOrderStartTime() != null) {
                startTime = entity.getOrderStartTime();
            }
            if (entity.getOrderEndTime() != null) {
                endTime = entity.getOrderEndTime();
            }
        }
        Date now = DateUtil.date();
        Date sTime = DateUtil.parse(DateUtil.formatDate(now) + " " + startTime);
        Date eTime = DateUtil.parse(DateUtil.formatDate(now) + " " + endTime);


        if (now.getTime() < sTime.getTime()) {
            now = sTime;
        } else if (now.getTime() > eTime.getTime()) {
            now = eTime;
        }

        map.put("start", start);
        map.put("end", end);
        map.put("startTime", DateUtil.format(sTime, "HH:mm"));
        map.put("endTime", DateUtil.format(eTime, "HH:mm"));
        map.put("nowTime", DateUtil.format(now, "HH:mm"));
        return RestResponse.success().setData(map);
    }

    /**
     * 获取值班人员信息
     *
     * @param projectId
     * @return
     */
    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping("/c/cGetProjectManager")
    public RestResponse cGetProjectManager(@RequestParam("projectId") String projectId) {
        String tel = "";
        ResProjectInfoEntity entity = resProjectInfoService.getOne(new QueryWrapper<ResProjectInfoEntity>().eq("project_id", projectId));
        if (entity != null && entity.getSalesPhone() != null) {
            tel = entity.getSalesPhone();
        }
        return RestResponse.success().setData(tel);
    }

    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping("/c/cGetCarProjectInfo")
    public RestResponse cGetCarProjectInfo(@RequestParam("projectId") String projectId) {
        ResProjectEntity project = resProjectService.getById(projectId);
        ResProjectInfoEntity projectInfo = resProjectInfoService.getOne(new QueryWrapper<ResProjectInfoEntity>().eq("project_id", projectId));
        Map<String, Object> map = Maps.newHashMap();
        map.put("project", project);
        map.put("projectInfo", projectInfo);
        return RestResponse.success().setData(map);
    }

    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping("/c/cGetCarInfo")
    public RestResponse cGetCarInfo(@RequestParam("projectId") String projectId) {
        Map<String, Object> query = new HashMap<>();
        query.put("projectId", projectId);
        query.put("isEffective", BaseConstants.BOOLEAN_OF_TRUE);
        query.put("state", SourceStateEnum.UNRENT.getValue());
        query.put("sourceType", "2");
        List<Map<String, String>> cars = resSourceService.selectSourceList(query);
        return RestResponse.success().setData(cars);
    }

    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping("/c/cGetCarConfig")
    public RestResponse cGetCarConfig(@RequestParam("sourceId") String sourceId) {
        ResSourceConfigureEntity sourceConfigureEntity = resSourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", sourceId));
        return RestResponse.success().setData(sourceConfigureEntity);
    }

    // 获取房源信息-XX公寓
    @UonePermissions
    @RequestMapping("/comun/getSourceInfo")
    public RestResponse getSourceInfoByComun(@RequestParam String id) throws Exception {
        Map<String, Object> map = Maps.newHashMap();
        map.put("id", id);

        return RestResponse.success().setData(resSourceService.getSourceInfoByComun(map));
    }

    // 预订房源-XX公寓
    @UonePermissions
    @RequestMapping(value = "/comun/book", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "book", expire = 5)
    public RestResponse book(HttpServletRequest request, @CacheParam String customerId) throws Exception {
        SaleDemandEntity demandEntity = null;

        log.info("客户ID："+customerId);
        if (StrUtil.isNotBlank(customerId)) {
            demandEntity = saleDemandDao.selectOne(new QueryWrapper<SaleDemandEntity>().eq("customer_id", customerId));
        }
        if (null == demandEntity) {
            demandEntity = new SaleDemandEntity();
        }
        if (BooleanUtil.isTrue(demandEntity.getIsFix())) {
            ResSourceVo sourceVo = resSourceService.getInfoById(demandEntity.getSourceId());
            return RestResponse.failure("您已预定" + sourceVo.getHouseName() + ",请勿重复预定！");
        }

        // 必填项判断
        if (StrUtil.isBlank(request.getParameter("imageCode"))) {
            return RestResponse.failure("图形验证码不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("code"))) {
            return RestResponse.failure("验证码不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("payType"))) {
            return RestResponse.failure("缴费方式不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("checkInDate"))) {
            return RestResponse.failure("租赁起日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("checkOutDate"))) {
            return RestResponse.failure("租赁止日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("costId"))) {
            return RestResponse.failure("费用配置异常");
        }
        if (StrUtil.isBlank(request.getParameter("platform"))) {
            return RestResponse.failure("第三方平台判断参数不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("tempId"))) {
            return RestResponse.failure("合同模板异常");
        }

        // 参数判断
        if (StrUtil.isBlank(request.getParameter("sourceId"))) {
            return RestResponse.failure("参数异常");
        }

        String vildCode = (String) CacheUtil.get(BaseConstants.TEL_SMS_HEADER + request.getParameter("phone"));
        if (!request.getParameter("code").equals(vildCode)) {
            return RestResponse.failure("短信验证码错误或已过期（5分钟内）！").code(406);
        }

        // 判断房源状态是否为未出租
        ResSourceEntity sourceEntity = resSourceService.getById(request.getParameter("sourceId"));

        if (!SourceStateEnum.UNRENT.getValue().equals(sourceEntity.getState())) {
            return RestResponse.failure("该房源已预定或已出租");
        }

        Map<String, Object> resultDataMap = new HashMap<>();
        // 判断当前手机号是否已经成为会员
        RenterEntity renterEntity;

        String phone = request.getParameter("phone");
        log.info("预定手机号：" + phone);
        renterEntity = renterFegin.getByTelAndType(phone, RenterType.COMMON.getValue());

        if (null == renterEntity) {
            // 新增租客用户
            String password = PassWordCreateUtil.createPassWord(6);
            renterEntity = new RenterEntity();
            renterEntity.setName(request.getParameter("name"))
                    .setTel(request.getParameter("phone"))
                    .setIdType(request.getParameter("idType"))
                    .setIdNo(request.getParameter("idNo"))
                    .setType(RenterType.COMMON.getValue())
                    .setUsername(request.getParameter("phone"))
                    .setSalt(AlgorUtil.getSalt())
                    .setIsVerify(BaseConstants.BOOLEAN_OF_FALSE)
                    .setOpenid(request.getParameter("openid"))
                    .setPassword(password); // 随机密码生成

            renterEntity = JSON.parseObject(JSON.toJSONString(renterFegin.add(renterEntity).get("data")), RenterEntity.class);
        }
        log.info("预定人信息：" + JSONUtil.toJsonStr(renterEntity));

        String payType = request.getParameter("payType");
        /*if(PayTypeEnum.ONE_ONE_STU.getValue().equals(payType)){
            payType = PayTypeEnum.ONE_ONE.getValue() ;
        }*/

        // 生成客户需求
        demandEntity.setUserId(renterEntity.getId())
                .setCheckInTime(DateUtil.parse(request.getParameter("checkInDate")))
                .setCheckOutTime(DateUtil.parse(request.getParameter("checkOutDate")))
                .setSourceId(request.getParameter("sourceId"))
                .setIsFix(true)
                .setCostId(request.getParameter("costId"))
                .setTempId(request.getParameter("tempId"))
                .setPayType(payType).insertOrUpdate();

        // 生成定金账单
        ResSourceConfigureEntity sourceConfig = resSourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", request.getParameter("sourceId")));
        ContTempEntity tempEntity = contTempService.getById(request.getParameter("tempId"));
        BilOrderEntity bilOrderEntity = bilOrderAutoService.createDueOrder(sourceConfig, renterEntity.getId(), demandEntity.getPayType(), request.getParameter("platform"),tempEntity,BaseConstants.BOOLEAN_OF_TRUE);

        resultDataMap.put("order", bilOrderEntity);
        resultDataMap.put("renter", renterEntity);

        return RestResponse.success().setData(resultDataMap);
    }

    @RequestMapping(value = "/loginCheck", method = RequestMethod.POST)
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse loginCheck(@RequestParam("sourceId") String sourceId,@RequestParam(required = false) String tempId) throws Exception {
        // 获取租客用户信息
        if (StrUtil.isBlank(UoneSysUser.id())) {
            return RestResponse.failure("未找到对应用户信息，请联系客服");
        }
        log.info("小程序预定链接打开，对应房源id为：" + sourceId + ";租客id为：" + UoneSysUser.id());
        ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
        if (BooleanUtil.isTrue(sourceEntity.getTalent())) {
            QueryWrapper<ContTalentEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("project_id", sourceEntity.getProjectId());
            wrapper.eq("renter_id", UoneSysUser.id());
            ContTalentEntity talentEntity = contTalentService.getOne(wrapper);
            if (ObjectUtil.isNull(talentEntity)) {
                return RestResponse.failure("该房源为人才房，只有该项目的人才才可预定哦~");
            }
        }
        RenterEntity renterEntity = renterFegin.getById(UoneSysUser.id());
        Map<String, Object> map = new HashMap<>();
        ContTempEntity tempEntity = null;
        if(StrUtil.isNotBlank(tempId)){
            tempEntity = contTempService.getById(tempId);
        }
        if(null==tempEntity){
            List<ContTempEntity> tempList = contTempService.getTempleteBySource(sourceEntity);
            tempEntity = tempList.get(0);
        }

        map.put("subsidyprice", tempEntity.getSubsidyPrice());
        map.put("renter", renterEntity);
        return RestResponse.success().setData(map);
    }

    /**
     * 获取人才房列表
     *
     * @return
     */
    @RequestMapping("/ut/talentSourcePage")
    public RestResponse getSourceInfoByComun(Page page, ResSourceSearchVo sourceSearchVo) {
        return RestResponse.success().setData(resSourceService.talentSourcePage(page, sourceSearchVo));
    }

    /**
     * 人才房关联列表
     *
     * @return
     */
    @RequestMapping("/ut/getTalentUnOrRelationSourcePage")
    public RestResponse getTalentSourceRelation(Page page, ResSourceSearchVo sourceSearchVo) {
        return RestResponse.success().setData(resSourceService.talentUnOrRelationSourcePage(page, sourceSearchVo));
    }

    @RequestMapping("/ut/batchTalentSource")
    public RestResponse batchTalentSource(@RequestParam("isTalent") String isTalent, @RequestParam("ids") String ids) {
        resSourceService.batchTalentSource(isTalent, Arrays.asList(ids.split(",")));
        return RestResponse.success();
    }

    @RequestMapping("/ut/contractInfo")
    public RestResponse contractInfo(@RequestParam("contractId") String contractId, @RequestParam("sourceId") String sourceId) {
        return RestResponse.success().setData(contContractService.utContractInfo(contractId, sourceId));
    }

    /**
     * 查询
     *
     * @return
     */
    @RequestMapping("/getUnRentList")
    @UonePermissions(value = LoginType.ANON)
    public RestResponse getUnRentList(String sourceType,String projectId,String renterId,String partitionId,String isTalent) throws Exception {
        RestResponse response = new RestResponse();
        Map<String, Object> filterData = new HashMap<>();
        filterData.put("projectId",projectId);
        filterData.put("partitionId",partitionId);
        //查询该租客是否是该项目对应的人才
        if(StrUtil.isNotBlank(renterId)){
            ContTalentEntity talentEntity = contTalentService.getByRenterIdAndProjectId(renterId, projectId);
            if(ObjectUtil.isNull(talentEntity)){
                filterData.put("isTalent","0");
            }
        }
        if(StrUtil.isNotBlank(isTalent)){
            filterData.put("isTalent",isTalent);
        }
        filterData.put("sourceType",sourceType);
        if(!"2".equals(sourceType)){
            filterData.put("isUp", "1");
        }
        return response.setSuccess(true).setData(resSourceService.getUnRentList(filterData));
    }

    /**
     * 查询
     *
     * @return
     */
    @RequestMapping("/getUnRentList2")
    @UonePermissions(value = LoginType.ANON)
    public RestResponse getUnRentList2(String sourceType,String projectId,String renterId,String partitionId,String isTalent) throws Exception {
        RestResponse response = new RestResponse();
        Map<String, Object> filterData = new HashMap<>();
        filterData.put("projectId",projectId);
        filterData.put("partitionId",partitionId);
        //查询该租客是否是该项目对应的人才
        if(StrUtil.isNotBlank(renterId)){
            ContTalentEntity talentEntity = contTalentService.getByRenterIdAndProjectId(renterId, projectId);
            if(ObjectUtil.isNull(talentEntity)){
                filterData.put("isTalent","0");
            }
        }
        if(StrUtil.isNotBlank(isTalent)){
            filterData.put("isTalent",isTalent);
        }
        filterData.put("sourceType",sourceType);
        if(!"2".equals(sourceType)){
            filterData.put("isUp", "1");
        }
        return response.setSuccess(true).setData(resSourceService.getUnRentList2(filterData));
    }

    @UonePermissions
    @RequestMapping("/getLocation")
    public RestResponse getLocation(String log,String lat){
        String city = BaiduMapUtil.getAdd(log,lat);
        if(StrUtil.isBlank(city)){
            return RestResponse.failure("获取城市信息失败!");
        }
        Map<String,Object> map = Maps.newHashMap();
        SysAreaEntity  area = areaService.getOne(new QueryWrapper<SysAreaEntity>().eq("area_name",city));
        CityEntity cityEntity = sourceDao.getCityByName(area.getAreaName());
        boolean open =ObjectUtil.isNotNull(cityEntity)?true:false;
        boolean project = false;
        if(open){
            List<ResProjectEntity> projectEntities = resProjectService.list(new QueryWrapper<ResProjectEntity>().eq("city_code",cityEntity.getCityCode()).eq("operate_state",BaseConstants.BOOLEAN_OF_FALSE));
            if(CollectionUtil.isNotEmpty(projectEntities)){
                project=true;
            }
        }
        boolean kf =open&&project;
        map.put("current", new HashMap<String, Object>() {{
            put("label",city);
            put("isUserCityOpen",kf);
            put("city",cityEntity);
        }});
        map.put("city",cityEntity);
        return RestResponse.success().setData(map);
    }


    @UonePermissions
    @RequestMapping("/getMapDist")
    public RestResponse getMapDist(){
        SysAreaEntity parent = areaService.getByCode(UoneHeaderUtil.getCityCode());
        List<SysAreaEntity> son = areaService.list(new QueryWrapper<SysAreaEntity>().eq("parent_id",parent.getId()).eq("is_last",BaseConstants.BOOLEAN_OF_TRUE));
        return RestResponse.success().setData(son);
    }

    /**
     * 上线城市
     *
     * @return
     */
    @UonePermissions
    @RequestMapping("/getUpCity")
    public RestResponse getUpCity() {
        List<ResUpCity> citys = projectDao.upCity();
        Map<String, String> num = Maps.newHashMap();
        citys.forEach(city -> {
            String label = FirstCharUtil.first(city.getLabel());
            if (ObjectUtil.isNotNull(num.get(label))) {
                List<Map<String, String>> has = citys.stream().filter(cityfile ->
                        label.equals(cityfile.getLetter())
                ).collect(Collectors.toList()).get(0).getData();
                Map<String, String> map = new HashMap<>();
                map.put("value", city.getValue());
                map.put("label", city.getLabel());
                has.add(map);
                city.setData(has);
            } else {
                List<Map<String, String>> lists = Lists.newArrayList();
                city.setLetter(label);
                num.put("label", city.getLabel());
                Map<String, String> map = new HashMap<>();
                map.put("value", city.getValue());
                map.put("label", city.getLabel());
                lists.add(map);
                city.setData(lists);
            }
        });
        return RestResponse.success().setData(citys);
    }

    @RequestMapping("/getShortSourceList")
    public RestResponse getShortSourceList(@RequestParam String partitionId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("partition_id", partitionId);
        wrapper.eq("is_short", BaseConstants.BOOLEAN_OF_TRUE);
        wrapper.orderByAsc("code");
        return RestResponse.success().setData(resSourceService.list(wrapper));
    }

    @RequestMapping("/getSourcesList")
    public RestResponse getSourcesList(@RequestParam String partitionId) {
        QueryWrapper wrapper = new QueryWrapper();
        if(partitionId==null || "".equals(partitionId)){
            String projectId=UoneHeaderUtil.getProjectId();
            wrapper.eq("project_id", projectId);
        }else{
            wrapper.eq("partition_id", partitionId);
        }
        wrapper.orderByAsc("code","CHAR_LENGTH(code)");
        return RestResponse.success().setData(resSourceService.list(wrapper));
    }

    @RequestMapping("/getRentSourcesList")
    public RestResponse getRentSourcesList(@RequestParam String partitionId) {
        QueryWrapper wrapper = new QueryWrapper();
        if(partitionId==null || "".equals(partitionId)){
            String projectId=UoneHeaderUtil.getProjectId();
            wrapper.eq("project_id", projectId);
        }else{
            wrapper.eq("partition_id", partitionId);
        }
//        wrapper.eq("state",SourceStateEnum.RENT.getValue());
        wrapper.orderByAsc("code");
        return RestResponse.success().setData(resSourceService.list(wrapper));
    }




    @PostMapping("/setIsShort")
    public RestResponse setIsShort(@RequestParam String id, @RequestParam Integer isShort) {
        ResSourceEntity sourceEntity = resSourceService.getById(id);
        if (ObjectUtil.isNull(sourceEntity)) {
            return RestResponse.failure("房源信息有误");
        }
        ResSourceConfigureEntity sourceConfig = resSourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", id));
        sourceConfig.setPrice(null);
        sourceConfig.setDeposit(null);
        sourceConfig.setLowPrice(null);
        sourceConfig.updateById();

        sourceEntity.setIsShort(isShort);
        sourceEntity.setPublishTarget(BaseConstants.BOOLEAN_OF_FALSE);

        sourceEntity.updateById();
        return RestResponse.success();
    }

    @RequestMapping(value = "/book", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "book", expire = 5)
    public RestResponse book(HttpServletRequest request) throws Exception {

        String phone = request.getParameter("phone");
        String customerId = request.getParameter("customerId");
        SaleDemandEntity demandEntity = null;
        RenterEntity renterEntity = null;
        if (StrUtil.isNotBlank(customerId)) {
            demandEntity = saleDemandDao.selectOne(new QueryWrapper<SaleDemandEntity>().eq("customer_id", customerId));
            SaleCustomerEntity customerEntity= customerDao.selectById(customerId);
            renterEntity  = renterFegin.getById(customerEntity.getRenterId());
        }else{
            log.info("预定手机号：" + phone);
            renterEntity = renterFegin.getByTelAndType(phone, RenterType.COMMON.getValue());
        }

        // 判断当前手机号是否已经成为会员
        if(ObjectUtil.isEmpty(renterEntity)){
            return RestResponse.failure("该手机号未注册会员。");
        }
        /*if(BaseConstants.BOOLEAN_OF_FALSE.equals(renterEntity.getIsVerify())){
            return RestResponse.failure("租客未实名认证，请引导租客实名认证之后预定");
        }*/

        // 参数判断
        if (StrUtil.isBlank(request.getParameter("sourceId"))) {
            return RestResponse.failure("参数异常");
        }

        String sourceId = request.getParameter("sourceId");
        ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
        if(!SourceStateEnum.UNRENT.getValue().equals(sourceEntity.getState())){
            return RestResponse.failure("房源已被预定或出租");
        }
        if (BooleanUtil.isTrue(sourceEntity.getTalent())) {
            QueryWrapper<ContTalentEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("project_id", sourceEntity.getProjectId());
            wrapper.eq("renter_id", renterEntity.getId());
            ContTalentEntity talentEntity = contTalentService.getOne(wrapper);
            if (ObjectUtil.isNull(talentEntity)) {
                return RestResponse.failure("该房源为人才房，只有该项目的人才才可预定哦~");
            }
        }

        if (StrUtil.isBlank(request.getParameter("payType"))) {
            return RestResponse.failure("缴费方式不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("shareStartTime"))) {
            return RestResponse.failure("租赁起日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("shareEndTime"))) {
            return RestResponse.failure("租赁止日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("costId"))) {
            return RestResponse.failure("费用配置异常");
        }
        if (StrUtil.isBlank(request.getParameter("templateId"))) {
            return RestResponse.failure("合同模板异常");
        }
        String discount = request.getParameter("discount") ;
        if (StrUtil.isBlank(discount)) {
            discount = "100" ;
        }



        if (null == demandEntity) {
            demandEntity = new SaleDemandEntity();
        }
        if (BooleanUtil.isTrue(demandEntity.getIsFix())) {
            ResSourceVo sourceVo = resSourceService.getInfoById(demandEntity.getSourceId());
            return RestResponse.failure("该租客已预定" + sourceVo.getHouseName() + ",请勿重复预定！");
        }

        if (!SourceStateEnum.UNRENT.getValue().equals(sourceEntity.getState())) {
            return RestResponse.failure("该房源已预定或已出租");
        }

//        QueryWrapper<DemoContractEntity> wrapper = new QueryWrapper<>();
//        DemoContractEntity demoContractEntity = demoContractService.getOne(wrapper.eq("source_id",sourceId).last("limit 0,1"));
//        if(null!=demoContractEntity&&DateUtil.beginOfDay(demoContractEntity.getEndTime()).getTime()<DateUtil.beginOfDay(DateUtil.parse(request.getParameter("shareEndtTime"), "yyyy-MM-dd")).getTime()){
//            return RestResponse.failure("合同截止日大于业主合同截止日，不允许签约");
//        }

        Map<String, Object> resultDataMap = new HashMap<>();
        // TODO 针对学生类型的押一付一月租金不上浮
        String payType = request.getParameter("payType");
//        if(PayTypeEnum.ONE_ONE_STU.getValue().equals(payType)){
//            payType = PayTypeEnum.ONE_ONE.getValue() ;
//        }

        // 生成客户需求
        demandEntity.setUserId(renterEntity.getId())
                .setCheckInTime(DateUtil.parse(request.getParameter("shareStartTime")))
                .setCheckOutTime(DateUtil.parse(request.getParameter("shareEndTime")))
                .setSourceId(request.getParameter("sourceId"))
                .setIsFix(true)
                .setCostId(request.getParameter("costId"))
                .setTempId(request.getParameter("templateId"))
                .setDiscount(new BigDecimal(discount).divide(new BigDecimal(100)))
                .setPayType(payType).insertOrUpdate();

        if(StringUtils.isNotBlank(request.getParameter("due")) && StringUtils.isNotEmpty(request.getParameter("due"))){
            demandEntity.setDeposit(new BigDecimal(request.getParameter("due")));
            demandEntity.insertOrUpdate();
        }
        if(StringUtils.isNotBlank(request.getParameter("price")) && StringUtils.isNotEmpty(request.getParameter("price"))){
            demandEntity.setPrice(new BigDecimal(request.getParameter("price")));
            demandEntity.insertOrUpdate();
        }
        //前端传的数据，deposit是押金，对应表中的字段名是cashPledge。due是定金，对应表字段名deposit。
        if(StringUtils.isNotBlank(request.getParameter("deposit")) && StringUtils.isNotEmpty(request.getParameter("deposit"))){
            demandEntity.setCashPledge(new BigDecimal(request.getParameter("deposit")));
            demandEntity.insertOrUpdate();
        }
        String isDue = StrUtil.isNotBlank(request.getParameter("isDue"))?request.getParameter("isDue"):"0";
        String sourcId = request.getParameter("sourceId");
        // 生成定金账单
        ResSourceConfigureEntity sourceConfig = resSourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id",sourcId));
        BigDecimal deposit = BigDecimal.ZERO;
        if(StrUtil.isNotBlank(request.getParameter("due"))){
            deposit = new BigDecimal(request.getParameter("due"));
        }
        sourceConfig.setDeposit(deposit);  //管家端定金是可编辑的，账单需根据管家输入金额来设定
        ContTempEntity tempEntity = contTempService.getById(request.getParameter("templateId"));
        BilOrderEntity bilOrderEntity = bilOrderAutoService.createDueOrder(sourceConfig, renterEntity.getId(), demandEntity.getPayType(), "0",tempEntity,isDue);

        resultDataMap.put("order", bilOrderEntity);
        resultDataMap.put("renter", renterEntity);

        return RestResponse.success().setData(resultDataMap);
    }

    @RequestMapping(value = "/toIntention", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "toIntention", expire = 5)
    public RestResponse toIntention(HttpServletRequest request) throws Exception {

        String orderId = request.getParameter("orderId");
        String sourceId = request.getParameter("sourceId");
        BilOrderEntity bilOrderEntity = bilOrderService.getById(orderId);
        if(bilOrderEntity == null)
            return RestResponse.failure("没有找到对应的订单信息");
        if(StrUtil.isNotBlank(sourceId)){
            ResSourceEntity newSourceEntity = resSourceService.getById(sourceId);
            if(!SourceStateEnum.UNRENT.getValue().equals(newSourceEntity.getState())){
                return RestResponse.failure("房源已被预定或出租");
            }
        }
        if (StrUtil.isBlank(request.getParameter("shareStartTime"))) {
            return RestResponse.failure("租赁起日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("shareEndTime"))) {
            return RestResponse.failure("租赁止日不能为空");
        }
        if(DateUtil.compare(DateUtil.parse(request.getParameter("shareStartTime")),DateUtil.parse(request.getParameter("shareEndTime")))>0){
            return RestResponse.failure("租赁止日不能小于租赁开始日");
        }
        if (StrUtil.isBlank(request.getParameter("discount"))) {
            return RestResponse.failure("价格策略不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("payType"))) {
            return RestResponse.failure("付款方式不能为空");
        }
        Map<String, Object> resultDataMap = new HashMap<>();
        Map<String,String> paras = Maps.newHashMap();
        paras.put("orderId",orderId);
        paras.put("sourceId",sourceId);
        paras.put("shareStartTime",request.getParameter("shareStartTime"));
        paras.put("shareEndTime",request.getParameter("shareEndTime"));
        paras.put("discount",request.getParameter("discount"));
        paras.put("payType",request.getParameter("payType"));
        resultDataMap = bilOrderService.toIntention(paras);
        resultDataMap.put("order", "");

        return RestResponse.success().setData(resultDataMap);
    }

    @RequestMapping("/selectRoom")
    public RestResponse getRoomList(String id) {
        RestResponse response = new RestResponse();
//        QueryWrapper<ResSourceEntity> wrapper = new QueryWrapper<ResSourceEntity>();
//        wrapper.eq("project_id", id);
        List<ResSourceEntity> entities=resSourceService.getList(id);
        List<RoomSourceVo> voList=new ArrayList<>();
        for(ResSourceEntity entity:entities){ //ResSourceEntity中的房间号"code"与前端字段"roomCode"不一样,将前端改成"code"不利于识别,entity中的字段改成roomCode又会造成原来的代码错误,所以新建实体类RoomSourceVo,用于存放roomCode;
            RoomSourceVo roomSourceVo=new RoomSourceVo();
            roomSourceVo.setSourceId(entity.getId());
            roomSourceVo.setRoomCode(entity.getCode());
            voList.add(roomSourceVo);
        }
        return response.setSuccess(true).setData(voList);
    }

    @RequestMapping("/sourcesInFloor")
    public RestResponse getRoomList(ResSourceSearchVo vo){
        //避免初始数据量太大，默认只显示第二层的房源
        if(StrUtil.isBlank(vo.getFloor())){
            vo.setFloor("2");
        }
        RestResponse response = new RestResponse();
        Map map=new HashedMap();
        map.put("searchVo",vo);
        List<FloorSourceVo> list = sourceManageService.getSourcesInFloor(map);
        return response.setSuccess(true).setData(list);
    }

    @PostMapping("changePropertyState")
    public RestResponse changePropertyState(String sourceId, String state) {
        ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
        sourceEntity.setState(state);
        if(resSourceService.updateById(sourceEntity)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("改变状态失败");
        }
    }

    @PostMapping("getCountByCompanyIds")
    public int getCountByCompanyIds(@RequestParam List<String> companyIds) {
       return resSourceService.getCountByCompanyIds(companyIds);
    }

    @RequestMapping("/getAllSourceList")
    public RestResponse getAllSourceList(){
        Map<String,Object> map = Maps.newHashMap();
        map.put("publishTarget","1");

        List<ResSourceVo> list = resSourceService.getSourceVoList(map);

        return RestResponse.success().setData(list);
    }

}
