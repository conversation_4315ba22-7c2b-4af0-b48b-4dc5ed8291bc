package cn.uone.business.res.service;

import cn.uone.bean.entity.business.res.ResProjectOwnerEntity;
import cn.uone.bean.entity.business.res.vo.ResProjectOwnerVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-15
 */
public interface IResProjectOwnerService extends IService<ResProjectOwnerEntity> {

    IPage<ResProjectOwnerVo> queryProjectOwnerEntity(String name, Page page);

    void insertProjectOwnerEntity(ResProjectOwnerEntity entity);

    void deleteProjectOwnerEntity(String id);

    void updateProjectOwnerEntity(ResProjectOwnerEntity entity);

    ResProjectOwnerEntity queryById(String id);

    List<ResProjectOwnerEntity> queryOwnerList();

    Boolean queryExist(QueryWrapper wrapper);

    String getOwnerId(String id);
}
