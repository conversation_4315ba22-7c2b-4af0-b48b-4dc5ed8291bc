package cn.uone.business.bil.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.bil.BilVerificationTransferEntity;
import cn.uone.bean.entity.business.bil.vo.BilVerificationVo;
import cn.uone.business.bil.dao.BilVerificationTransferDao;
import cn.uone.business.bil.service.IBilVerificationTransferService;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 退款/收款确认表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-10
 */
@Service
public class BilVerificationTransferServiceImpl extends ServiceImpl<BilVerificationTransferDao, BilVerificationTransferEntity> implements IBilVerificationTransferService {



    @Override
    public IPage<BilVerificationVo> findByCondition(Page page, BilVerificationVo bilVerificationVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilVerificationVo = assembleSearchVo(bilVerificationVo);
        DataScope dataScope = getDataScope(bilVerificationVo);
        map.put("searchVo", bilVerificationVo);
        return baseMapper.selectBilVeriByMap(page, map, dataScope);
    }

    /**
     * 根据请求header过滤项目
     *
     * @param bilVerificationVo
     * @return
     */
    private BilVerificationVo assembleSearchVo(BilVerificationVo bilVerificationVo) {
        //后台管理
        if (StrUtil.isNotEmpty(UoneHeaderUtil.getProjectId())) {
            bilVerificationVo.setProjectId(UoneHeaderUtil.getProjectId());
        }
        return bilVerificationVo;
    }

    /**
     * XX公寓 有用户id 不过滤数据权限
     *
     * @param bilVerificationVo
     * @return
     */
    private DataScope getDataScope(BilVerificationVo bilVerificationVo) {
        DataScope dataScope = null;
        if (StrUtil.isEmpty(bilVerificationVo.getUserId())) {
            dataScope = new DataScope(UoneSysUser.id());
            dataScope.setProAlias("s");
            dataScope.setProjectFieldName("project_id");
        }
        return dataScope;
    }
}
