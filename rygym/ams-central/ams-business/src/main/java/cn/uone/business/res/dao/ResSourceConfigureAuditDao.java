package cn.uone.business.res.dao;

import cn.uone.bean.entity.business.bil.PriceStrategyEntity;
import cn.uone.bean.entity.business.res.ResSourceConfigureAuditEntity;
import cn.uone.bean.entity.business.res.ResSourceConfigureEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 * 价格配置审批记录接口
 * <AUTHOR>
 * @since 2024-08-13
 */
public interface ResSourceConfigureAuditDao extends BaseMapper<ResSourceConfigureAuditEntity> {
    IPage<ResSourceConfigureAuditEntity> findByCondition(Page page, @Param("map") Map<String, Object> map);

    IPage<ResSourceConfigureAuditEntity> getConfigureAuditList(Page page, @Param("entity") ResSourceConfigureAuditEntity entity);
}
