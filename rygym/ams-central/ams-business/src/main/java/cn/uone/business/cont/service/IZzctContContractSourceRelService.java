package cn.uone.business.cont.service;

import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.cont.bo.ContContractSourceRelBo;
import cn.uone.bean.entity.business.cont.vo.ContContractSourceRelVo;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * 合同房源关系
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IZzctContContractSourceRelService extends IService<ContContractSourceRelEntity> {

    List<ContContractSourceRelEntity> selectListByCondition(Map<String, Object> map);

    ContContractSourceRelEntity addOrUpdateContMultiSourceRel(ContContractEntity contract, String sourceIds, BigDecimal price, BigDecimal cashPledge, BigDecimal subsidySum, List<ContRentLadderEntity> rentLadders) throws Exception;
}
