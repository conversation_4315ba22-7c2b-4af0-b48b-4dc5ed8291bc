package cn.uone.business.dev.dao;

import cn.uone.bean.entity.business.dev.DevAuthorizationEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 门禁授权表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-03
 */
public interface DevAuthorizationDao extends BaseMapper<DevAuthorizationEntity> {

    IPage queryDevAuthorizationLinkUser(Page page, @Param("map")Map map);

    List queryDevAuthorizationDoor(@Param("map")Map map);
    List queryDoor(@Param("map") Map map);
}
