package cn.uone.business.res.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.source.PriceTypeEnum;
import cn.uone.bean.entity.business.res.ResCostConfigureDetailEntity;
import cn.uone.bean.entity.business.res.ResCostConfigureEntity;
import cn.uone.business.res.service.IResCostConfigureDetailService;
import cn.uone.business.res.service.IResCostConfigureService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 前端控制器
 * </p>costConfigureDsetail
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/costConfigureDsetail")
public class ResCostConfigureDetailController extends BaseController {

    @Autowired
    private IResCostConfigureDetailService resCostConfigureDetailService;

    @Autowired
    private IResCostConfigureService resCostConfigureService;

    @RequestMapping("/queryIPage")
    public RestResponse queryIPage(Page page) {
        String projectid = UoneHeaderUtil.getProjectId();
        ResCostConfigureEntity entity =resCostConfigureService.queryByProjectId(projectid);
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("cost_configure_id",entity.getId());
        IPage<ResCostConfigureDetailEntity> list = resCostConfigureDetailService.queryIPage(page,wrapper);
        return RestResponse.success().setData(list);
    }

    /**
     * 基础配置主页面销售主体table查询
     *
     * @return
     */
    @RequestMapping("/queryList")
    public RestResponse queryList() {
        List<Map<String, Object>> list = PriceTypeEnum.toListNotStatic();
        String projectid = UoneHeaderUtil.getProjectId();
        List<ResCostConfigureDetailEntity> list1 = resCostConfigureDetailService.queryList(projectid);
        for (Map<String, Object> map : list) {
            if (list1.size() > 0) {
                for (ResCostConfigureDetailEntity entity : list1) {
                    if (map.get("value").toString().equals(entity.getCostType())) {
                        map.put("id", entity.getId());
                        map.put("chargeTarget", entity.getChargeTarget());
                        map.put("payTarget", entity.getPayTarget());
                        map.put("costType", entity.getCostType());
                        map.put("payStandard", entity.getPayStandard());
                        map.put("update_date", entity.getUpdateDate());
                    } else {
                        map.put("costType", map.get("value").toString());
                    }
                }
            } else {
                map.put("id", "");
                map.put("chargeTarget", "");
                map.put("payTarget", "");
                map.put("costType", map.get("value").toString());
                map.put("payStandard", "");
                map.put("update_date", "");
            }
        }
        IPage iPage = new Page();
        iPage.setRecords(list);
        return RestResponse.success().setData(iPage);
    }

    @RequestMapping("/updateTarget")
    public RestResponse updateTarget(@RequestParam(value = "id", required = false) String id,
                                     @RequestParam(value = "costType", required = true) String costType,
                                     @RequestParam(value = "payTarget", required = false) String payTarget,
                                     @RequestParam(value = "chargeTarget", required = false) String chargeTarget){
        ResCostConfigureDetailEntity costConfigureDetailEntity = new ResCostConfigureDetailEntity();
        if (StrUtil.isNotBlank(id)) {
            costConfigureDetailEntity = resCostConfigureDetailService.queryById(id);
        } else {
            String projectid = UoneHeaderUtil.getProjectId();
            ResCostConfigureEntity configureEntity = resCostConfigureService.queryBaseByProjectId(projectid);
            costConfigureDetailEntity.setCostConfigureId(configureEntity.getId());
        }
        costConfigureDetailEntity.setCostType(costType);
        if (StrUtil.isNotBlank(payTarget)) {
            costConfigureDetailEntity.setPayTarget(payTarget);
        }
        if (StrUtil.isNotBlank(chargeTarget)) {
            costConfigureDetailEntity.setChargeTarget(chargeTarget);
        }
        resCostConfigureDetailService.saveOrUpdate(costConfigureDetailEntity);
        return RestResponse.success();
    }

    @RequestMapping("/saveDetail")
    public RestResponse saveDetail(ResCostConfigureDetailEntity entity) {
        ResCostConfigureEntity configureEntity = resCostConfigureService.queryByProjectId(UoneHeaderUtil.getProjectId());
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("cost_type", entity.getCostType());
        wrapper.eq("cost_configure_id", configureEntity.getId());
        if (StrUtil.isNotEmpty(entity.getId())) {
            wrapper.ne("id", entity.getId());
        }
        List<ResCostConfigureDetailEntity> list = resCostConfigureDetailService.list(wrapper);
        if (list.size() > 0) {
            return RestResponse.failure("该费用类型已存在");
        }
        entity.setCostConfigureId(configureEntity.getId());
        resCostConfigureDetailService.saveOrUpdate(entity);
        return RestResponse.success();
    }

    @RequestMapping("/del")
    public RestResponse del(@RequestParam(value = "id", required = false) String id) {
        resCostConfigureDetailService.removeById(id);
        return RestResponse.success();
    }

}
