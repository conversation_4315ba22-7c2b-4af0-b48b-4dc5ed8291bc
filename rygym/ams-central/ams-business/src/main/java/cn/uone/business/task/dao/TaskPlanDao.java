package cn.uone.business.task.dao;


import cn.uone.bean.entity.business.task.TaskPlanEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 计划管理表  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
public interface TaskPlanDao extends BaseMapper<TaskPlanEntity> {


    /**
     * 根据周期获取任务列表
     * @param executionCycle
     * @param executorId
     * @return
     */
   List<TaskPlanEntity> getTaskByCycle(@Param("executionCycle")String executionCycle,@Param("executorId")String executorId,@Param("taskType")String taskType);
}
