package cn.uone.business.cont.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ApiTypeEnum;
import cn.uone.application.enumerate.IdTypeEnum;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.*;
import cn.uone.application.enumerate.source.DegreeEnum;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.*;
import cn.uone.bean.entity.business.cont.vo.ContContractVo;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResProjectInfoEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.tpi.contractRegistration.ContractRegistrationVo;
import cn.uone.bean.entity.tpi.record.RecordConfigVo;
import cn.uone.bean.entity.tpi.record.RecordPo;
import cn.uone.business.biz.dao.BizReleaseDao;
import cn.uone.business.cont.dao.*;
import cn.uone.business.cont.service.IContCheckInUserService;
import cn.uone.business.cont.service.impl.ContContractInfoServiceImpl;
import cn.uone.business.res.dao.ResSourceDao;
import cn.uone.business.res.service.IResProjectInfoService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.sys.dao.SysFileDao;
import cn.uone.business.util.ContractUtil;
import cn.uone.cache.util.CacheUtil;
import cn.uone.fegin.crm.IExpenseConfigFegin;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.tpi.IContractRegFegin;
import cn.uone.fegin.tpi.IRecordFegin;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class ContractTask {

    private static final Logger log = LoggerFactory.getLogger(ContContractInfoServiceImpl.class);

    @Resource
    private ContContractSourceRelDao contractSourceRelDao;

    @Resource
    private SysFileDao sysFileDao;

    @Resource
    private ContContractDao contractDao;

    @Autowired
    IResProjectService resProjectService;
    @Autowired
    IExpenseConfigFegin expenseConfigFegin;
    @Autowired
    private IContractRegFegin contractRegFegin;
    @Resource
    private ContractFilingParametersDao parametersDao;
    @Resource
    private ContFrameContractDao frameContractDao;
    @Resource
    private IRenterFegin renterFegin;

    public RestResponse record(String contractId, Boolean isV) throws Exception {
        ContContractVo contractVo =  contractDao.getContractInfoById(contractId);
        ContractRegistrationVo vo = new ContractRegistrationVo();
        /*获取合同备案接口TOKEN参数*/
        RecordConfigVo configVo = getConfig(contractVo.getProjectId());
        if(configVo == null){
            return RestResponse.failure("未获取到合同备案TOKEN参数");
        }
        vo.setConfigVo(configVo);
        //获取合同附件时判断个人或者企业
        List<SysFileEntity> contractFile ;
        if("0".equals(contractVo.getIsOrganize())){
            //查询个人合同附件
            contractFile = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractId).in("type", SysFileTypeEnum.CONTRACT.getValue(),SysFileTypeEnum.SUB_CONTRACT.getValue()));
        }else {
            //查询企业合同附件
            contractFile = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractVo.getFrameContractId()).in("type", SysFileTypeEnum.CONTRACT.getValue(),SysFileTypeEnum.SUB_CONTRACT.getValue()));
        }

        List<Object> contractList = new ArrayList<>();
        for (SysFileEntity f : contractFile) {
            String url = f.getPath();
            configVo.setUrl(url);
            RestResponse contFileRes = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
            if(contFileRes.getSuccess()){
                JSONObject jsonObject = JSONUtil.parseObj(contFileRes.get("data"));
                contractList.add(jsonObject);
            }
        }
        vo.setFjBctk(JSONUtil.toJsonStr(contractList));//合同附件


        //获取备案参数
        ContractFilingParametersEntity parameters =  parametersDao.selectOne(new QueryWrapper<ContractFilingParametersEntity>().eq("project_id", contractVo.getProjectId()).eq("partition_id",contractVo.getPartitionId()));
        if(parameters == null){
            parameters =  parametersDao.selectOne(new QueryWrapper<ContractFilingParametersEntity>().eq("project_id", contractVo.getProjectId()));
        }
        //id
        vo.setId(contractVo.getId());
        //业务类型 首次
        vo.setYwlx("contract_filing_new");
        // 设置申请人姓名
        vo.setSqrxm(parameters.getSqrxm());
        // 设置申请人手机号
        vo.setSqrsjh(parameters.getSqrsjh());
        // 设置申请人证件类型
        vo.setSqrzjlx(parameters.getSqrzjlx());
        // 设置申请人证件号
        vo.setSqrzjh(parameters.getSqrzjh());
        // 设置合同编号---不是必填
        vo.setHtbh(contractVo.getContractCode());
        // 原合同备案号(首次不传)
//        vo.setYhtbabh("");
        // 设置是否由房地产经纪机构待办（假设从ContContractEntity获取对应值，有对应get方法）
        vo.setSfjjdb("0");//0否 1 是
        // 根据是否由房地产经纪机构待办的值判断相关字段是否必填并赋值
        if ("1".equals(vo.getSfjjdb())) {
            // 房地产经纪机构名称
            vo.setJjjgmc("房地产经纪机构名称");
            // 房地产经纪机构统一信用代码
            vo.setJjjgxydm("房地产经纪机构统一信用代码");
        }
        // 设置租赁期限开始日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        vo.setZlqxks(sdf.format(contractVo.getStartDate()));
        // 设置租赁期限结束日期
        vo.setZlqxjs(sdf.format(contractVo.getEndDate()));
        // 设置合同签订日期（同样的格式化操作及赋值，需ContContractEntity有对应获取方法）
        vo.setHtqdrq(sdf.format(contractVo.getSignDate()));
        // 设置房屋交付日期（不必填）
//        vo.setFwjfrq();
        // 设置是否转租
        vo.setSfzz("0");//0否 1 是
//        // 设置前置合同租赁开始日期（不必填）
//        vo.setQzhtkssj(null);
//        // 设置前置合同租赁结束日期（不必填）
//        vo.setQzhtjzsj(null);
//        // 设置租赁用途（不必填）
//        vo.setZlyt("不必填");
        // 设置居住面积（不必填）
//        vo.setJuzhumianji();
        // 设置使用面积（不必填）
//        vo.setSymj();
        // 设置出租面积
        vo.setCzmj(contractVo.getRoomArea().doubleValue());
        // 设置押付方式（  1 押一付三 2押一付二 3押一付一 4押二付一 5年付不押 6半年付不押 7面议 99其他 8 押二付二）
        if(PayTypeEnum.ONE_ONE.getValue().equals(contractVo.getPayType())){
            vo.setYffs("3");
            // 设置支付方式（1按月付 2按季付 3半年付 4按年付）
//            vo.setZffs("1");
        }else if(PayTypeEnum.ONE_TWO.getValue().equals(contractVo.getPayType())){
            vo.setYffs("2");
            // 设置支付方式（1按月付 2按季付 3半年付 4按年付）
//            vo.setZffs("2");
        }else if(PayTypeEnum.ONE_THREE.getValue().equals(contractVo.getPayType())){
            vo.setYffs("1");
            // 设置支付方式（1按月付 2按季付 3半年付 4按年付）
//            vo.setZffs("2");
        }else if(PayTypeEnum.TWO_ONE.getValue().equals(contractVo.getPayType())){
            vo.setYffs("4");
            // 设置支付方式（1按月付 2按季付 3半年付 4按年付）
//            vo.setZffs("1");
        }else if(PayTypeEnum.TWO_THREE.getValue().equals(contractVo.getPayType())){
            vo.setYffs("99");
            // 设置支付方式（1按月付 2按季付 3半年付 4按年付）
//            vo.setZffs("2");
        }else if(PayTypeEnum.ONE_SIX.getValue().equals(contractVo.getPayType())){
            vo.setYffs("6");
            // 设置支付方式（1按月付 2按季付 3半年付 4按年付）
            vo.setZffs("3");
        }else if(PayTypeEnum.ONE_TWELVE.getValue().equals(contractVo.getPayType())){
            vo.setYffs("5");
            // 设置支付方式（1按月付 2按季付 3半年付 4按年付）
            vo.setZffs("4");
        }

        // 设置押金金额
        //vo.setYjje(contractVo.getCashPledge().doubleValue());//应滨海需求，暂时不传押金金额过去 caizhanghe edit 2025-03-20
        // 设置是否使用租金贷款（不必填）
//        vo.setSfzjdk(contractEntity.getSfzjdk());
        // 设置月租金金额
        vo.setZjje(contractVo.getPrice().doubleValue());//t_cont_rent_ladder
        // 设置押金交付日期（不必填）
//        vo.setYjjfrq();

        // 设置首次租金交付日期（不必填）
//        vo.setSczjjfrq();

        // 设置房屋套数
        vo.setFwts(1);
        // 设置承租人是否为保障对象（不必填）
//        vo.setSfbzdxpz();

        // 设置合同备注（首次不必填）
//        vo.setHtbz();
        // 设置注销日期（首次不必填）
//        vo.setZxrq();
        //设置注销原因（首次不必填）
//        vo.setZxyy();

        // 设置所属项目名称
        vo.setSsxmmc(parameters.getSsxmmc());

        // 设置项目所属单位统一社会信用代码
        vo.setTyshxydm(parameters.getTyshxydm());

        // 权属信息数组相关赋值
        List<ContractRegistrationVo.CovenantOwner> covenantOwnerList = new ArrayList<>();
        ContractRegistrationVo.CovenantOwner owner = new ContractRegistrationVo.CovenantOwner();
        owner.setQszjlx(parameters.getQszjlx());
        owner.setQszjbh(parameters.getQszjbh());
        String partitionName = contractVo.getPartitionName();//楼栋号
        if(parameters.getProjectId().equals("634db6a33ac66818762160b88a065547")){//弘堍公寓
            owner.setBaBzdz(parameters.getBaBzdz()+contractVo.getCode()+"室");
        }else if(parameters.getProjectId().equals("6a3f808c9d8cd92b8fbff464b179e923")){//轨道滨海项目
            if(partitionName.equals("3号楼")){//3号楼
                owner.setBaBzdz(parameters.getBaBzdz()+"5号"+contractVo.getCode()+"室");
            } else if(partitionName.equals("2号楼")){//2号楼
                owner.setBaBzdz(parameters.getBaBzdz()+"2号"+contractVo.getCode()+"室");
            }
        }else{
            owner.setBaBzdz(parameters.getBaBzdz()+contractVo.getPartitionName()+contractVo.getCode()+"室");
        }
        owner.setBaXzqh(parameters.getBaXzqh());
        owner.setBaZj(parameters.getBaZj());
        owner.setBaCj(parameters.getBaCj());
        owner.setFwxz(parameters.getFwxz());
        owner.setFwyt(parameters.getFwyt());
        owner.setWqS(contractVo.getRoom());
        owner.setWqTing(contractVo.getHall());
        owner.setWqW(contractVo.getToilet());
        owner.setWqC(contractVo.getKitchen());
        owner.setZlfs(parameters.getZlfs());
//            owner.setBw(parameters.getBw());//出租部位
        //装修程度（1精装修 3粗装修） t_res_source degree
        if(DegreeEnum.ORDINARYDECORATION.getValue().equals(contractVo.getDegree())){
            owner.setZxcd("3");//粗装修
        }else {
            owner.setZxcd("1");//精装修
        }
        owner.setJzmj(contractVo.getRoomArea().doubleValue());//房间面积（使用面积 居住面积 建筑面积）

        //产权人数组
        List<ContractRegistrationVo.CovenantOwner.Cqr> cqrList = new ArrayList<>();
        ContractRegistrationVo.CovenantOwner.Cqr cqr = new ContractRegistrationVo.CovenantOwner.Cqr();
        cqr.setCqrxm(parameters.getCqrxm());
        cqr.setCqrzjlx(parameters.getCqrzjlx());
        cqr.setCqrzjhm(parameters.getCqrzjhm());
        List<Object> cqrFileList = new ArrayList<>();
        //产权证
        configVo.setUrl(parameters.getCqzsmj());
        RestResponse cqrFileRes = contractRegFegin.sendFile(configVo);  //TODO 上传附件并获取到返回的数据
        if(cqrFileRes.getSuccess()){
            JSONObject jsonObject = JSONUtil.parseObj(cqrFileRes.get("data"));
            cqrFileList.add(jsonObject);
        }
        cqr.setCqzsmj(JSONUtil.toJsonStr(cqrFileList));//产权人扫描件
        cqrList.add(cqr);
        owner.setCqrList(cqrList.toArray(new ContractRegistrationVo.CovenantOwner.Cqr[0]));


        covenantOwnerList.add(owner);
        vo.setCovenantOwnerList(covenantOwnerList.toArray(new ContractRegistrationVo.CovenantOwner[0]));

        // 出租方信息数组相关赋值
        List<ContractRegistrationVo.CovenantHirer> covenantHirerList = new ArrayList<>();
        ContractRegistrationVo.CovenantHirer hirer = new ContractRegistrationVo.CovenantHirer();
        hirer.setHirerType(parameters.getHirerType());
        hirer.setHirerName(parameters.getHirerName());
        hirer.setHirerPhone(parameters.getHirerPhone());
        hirer.setHirerCardType(parameters.getHirerCardType());
        hirer.setHirerCardNum(parameters.getHirerCardNum());
        List<Object> hirerCarList = new ArrayList<>();
        configVo.setUrl(parameters.getHirerCardFile());
        RestResponse hirerCarFileRes = contractRegFegin.sendFile(configVo);  //TODO 上传附件并获取到返回的数据
        if(hirerCarFileRes.getSuccess()){
            JSONObject jsonObject = JSONUtil.parseObj(hirerCarFileRes.get("data"));
            hirerCarList.add(jsonObject);
        }
        hirer.setHirerCardFile(JSONUtil.toJsonStr(hirerCarList));//出租方附件

        hirer.setHirerAgentName(parameters.getHirerAgentName());
        hirer.setHirerAgentPhone(parameters.getHirerAgentPhone());
        hirer.setHirerAgentCardType(parameters.getHirerAgentCardType());
        hirer.setHirerAgentCardNum(parameters.getHirerAgentCardNum());
        List<Object> agentList = new ArrayList<>();
        configVo.setUrl(parameters.getHirerAgentCardFile());
        RestResponse agentFileRes = contractRegFegin.sendFile(configVo);  //TODO 上传附件并获取到返回的数据
        if(agentFileRes.getSuccess()){
            JSONObject jsonObject = JSONUtil.parseObj(agentFileRes.get("data"));
            agentList.add(jsonObject);
        }
        hirer.setHirerAgentCardFile(JSONUtil.toJsonStr(agentList));//出租方代理人身份证明附件
        covenantHirerList.add(hirer);
        vo.setCovenantHirerList(covenantHirerList.toArray(new ContractRegistrationVo.CovenantHirer[0]));

        //其他附件-授权书
        List<Object> qtList = new ArrayList<>();
        configVo.setUrl(parameters.getHirerAgentQtFile());
        RestResponse qtResponse = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
        if(qtResponse.getSuccess()){
            JSONObject jsonObject = JSONUtil.parseObj(qtResponse.get("data"));
            qtList.add(jsonObject);
        }
        vo.setFjQt(JSONUtil.toJsonStr(qtList));//其他附件



        // 承租方信息数组相关赋值
        List<ContractRegistrationVo.CovenantLessee> covenantLesseeList = new ArrayList<>();
        ContractRegistrationVo.CovenantLessee lessee = new ContractRegistrationVo.CovenantLessee();
        //承租方类型
        if("0".equals(contractVo.getIsOrganize())){
            lessee.setLesseeType("personal");// personal 个人备案
            lessee.setLesseeName(contractVo.getName());
            lessee.setLesseeCardType(getCardType(contractVo.getIdType()));//证件类型
            lessee.setLesseePhone(contractVo.getTel());
            lessee.setLesseeCardNum(contractVo.getIdNo());
            List<String> fileTypes = Lists.newArrayList();
            fileTypes.add(SysFileTypeEnum.ID_CARD.getValue());
            fileTypes.add(SysFileTypeEnum.BUSINESS_LICENSE.getValue());
            List<SysFileEntity> files = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractId).in("type", fileTypes));
            List<Object> stringList = new ArrayList<>();
            for (SysFileEntity f : files) {
                String url = f.getPath();
                configVo.setUrl(url);
                RestResponse response = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
                if(response.getSuccess()){
                    JSONObject jsonObject = JSONUtil.parseObj(response.get("data"));
                    stringList.add(jsonObject);
                }
            }
            lessee.setLesseeCardFile(JSONUtil.toJsonStr(stringList));
        }else {
            lessee.setLesseeType("enterprise");//enterprise 企业备案
            /*承租方 start*/
            ContFrameContractEntity frame = frameContractDao.selectById(contractVo.getFrameContractId());
            RenterEntity renter = renterFegin.getById(frame.getSignerId());
            lessee.setLesseeName(renter.getName());
            lessee.setLesseeCardType("2019");//证件类型
            lessee.setLesseePhone(renter.getTel());
            lessee.setLesseeCardNum(renter.getIdNo());
            List<String> fileTypes = Lists.newArrayList();
            fileTypes.add(SysFileTypeEnum.ID_CARD.getValue());
            fileTypes.add(SysFileTypeEnum.BUSINESS_LICENSE.getValue());
            List<SysFileEntity> files = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractVo.getFrameContractId()).in("type", fileTypes));
            List<Object> stringList = new ArrayList<>();
            for (SysFileEntity f : files) {
                String url = f.getPath();
                configVo.setUrl(url);
                RestResponse response = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
                if(response.getSuccess()){
                    JSONObject jsonObject = JSONUtil.parseObj(response.get("data"));
                    stringList.add(jsonObject);
                }
            }
            lessee.setLesseeCardFile(JSONUtil.toJsonStr(stringList));
            /*承租方 end*/
            /*承租方代理人 start*/
            lessee.setLesseeAgentName(contractVo.getName());// 承租方代理人姓名（承租方类型为企业备案时必填）
            lessee.setLesseeAgentPhone(contractVo.getTel());// 承租方代理人手机号（承租方类型为企业备案时必填）
            lessee.setLesseeAgentCardType(getCardType(contractVo.getIdType()));// 承租方代理人证件类型（详见字典3.4人员证件类型，承租方类型为企业备案时必填）
            lessee.setLesseeAgentCardNum(contractVo.getIdNo());      // 承租方代理人证件号码（承租方类型为企业备案时必填）
            List<String> dlrTypes = Lists.newArrayList();
            dlrTypes.add(SysFileTypeEnum.ID_CARD.getValue());
            dlrTypes.add(SysFileTypeEnum.BUSINESS_LICENSE.getValue());
            List<SysFileEntity> dlrFiles = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractId).in("type", fileTypes));
            List<Object> dlrList = new ArrayList<>();
            for (SysFileEntity f : dlrFiles) {
                String url = f.getPath();
                configVo.setUrl(url);
                RestResponse response = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
                if(response.getSuccess()){
                    JSONObject jsonObject = JSONUtil.parseObj(response.get("data"));
                    dlrList.add(jsonObject);
                }
            }
            lessee.setLesseeAgentCardFile(JSONUtil.toJsonStr(stringList));// 承租方代理人身份证明附件，文件上传返回的对象数组转字符串（承租方类型为企业备案时必填）
            /*承租方代理人 end*/
        }

        covenantLesseeList.add(lessee);
        vo.setCovenantLesseeList(covenantLesseeList.toArray(new ContractRegistrationVo.CovenantLessee[0]));
        //存储状态及record_id 到 t_cont_contract_source_rel 表
        String status = RecordState.LOADING.getValue();
        String message = null;
        ContContractSourceRelEntity res = contractSourceRelDao.selectOne(new QueryWrapper<ContContractSourceRelEntity>().eq("contract_id", contractVo.getId()).eq("source_id", contractVo.getSourceId()));
        RestResponse pushRes = contractRegFegin.pushContractReg(vo);
        if(!pushRes.getSuccess()){
            status = RecordState.FAIL.getValue();
            message = pushRes.getMessage();
            res.setRecordErrorMsg(message);
        }
        res.setRecordId(parameters.getRecordIdPrefix()+contractVo.getId());
        res.setRecordState(status);
        res.updateById();
        return pushRes;
    }

    private String getCardType(String idType){
        String cardType = "2000";
        if(IdTypeEnum.IDENTITY_CARD.getValue().equals(idType)){
            cardType = "2000";// 居民身份证
        }else if(IdTypeEnum.BUSINESS_LICENSE.getValue().equals(idType)){
            cardType ="2019";// 营业执照
        }else if(IdTypeEnum.TEMPORARY_IDENTITY_CARD.getValue().equals(idType)){
            cardType ="2007";// 临时居民身份证
        }else if(IdTypeEnum.RESIDENCE_BOOKLET.getValue().equals(idType)){
            cardType ="2001";// 户口簿（未成年人）
        }else if(IdTypeEnum.PASSPORT.getValue().equals(idType)){
            cardType ="2002";// 护照
        }else if(IdTypeEnum.CERTIFICATE_OF_OFFICERS.getValue().equals(idType)){
            cardType ="2003";// 军官证
        }else if(IdTypeEnum.CERTIFICATE_OF_ARMED_POLICE.getValue().equals(idType)){
            cardType ="2009";// 武警警官证
        }else if(IdTypeEnum.CERTIFICATE_OF_SOLDIERS.getValue().equals(idType)){
            cardType ="2004";// 士兵证
        }else if(IdTypeEnum.STUDENT_CARD.getValue().equals(idType)){
            cardType ="2015";// 学员证（军人）2015为 其他个人证件
        }else if(IdTypeEnum.FOREIGNER_RESIDENCE_PERMIT.getValue().equals(idType)){
            cardType ="2008";// 外国人永久居留证
        }else if(IdTypeEnum.FOREIGNER_EXIT_AND_ENTRY_PERMITS.getValue().equals(idType)){
            cardType ="2015";// 外国人出入境证  2015为 其他个人证件
        }else if(IdTypeEnum.H_MAINLAND_TRAVEL_PERMIT.getValue().equals(idType)){
            cardType ="2005";// 港澳居民来往内地通行证
        }else if(IdTypeEnum.T_MAINLAND_TRAVEL_PERMIT.getValue().equals(idType)){
            cardType ="2006";// 台湾居民来往大陆通行证
        }
        return cardType;
    }

    /**获取合同备案接口TOKEN参数*/
    private RecordConfigVo getConfig(String projectId){
        ResProjectEntity project = resProjectService.getById(projectId);
        JSONObject config = expenseConfigFegin.getExpenseConfigByCode(project.getCode(), ApiTypeEnum.CONTRACT_RECORD.getValue());
        RecordConfigVo configVo = JSONUtil.toBean(config,RecordConfigVo.class);
        return configVo;
    }


    public RestResponse outRecord(String contractId,Boolean isV) throws Exception {
        ContContractVo contractVo =  contractDao.getContractInfoById(contractId);
        ContContractSourceRelEntity res = contractSourceRelDao.selectOne(new QueryWrapper<ContContractSourceRelEntity>().eq("contract_id", contractVo.getId()).eq("source_id", contractVo.getSourceId()));
        ContractRegistrationVo vo = new ContractRegistrationVo();
        /*获取合同备案接口TOKEN参数*/
        RecordConfigVo configVo =  getConfig(contractVo.getProjectId());
        if(configVo == null){
            return RestResponse.failure("未获取到合同备案TOKEN参数");
        }
        vo.setConfigVo(configVo);
        //获取合同附件时判断个人或者企业
        List<SysFileEntity> contractFile ;
        if("0".equals(contractVo.getIsOrganize())){
            //查询个人合同附件
            contractFile = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractId).in("type", SysFileTypeEnum.CONTRACT.getValue(),SysFileTypeEnum.SUB_CONTRACT.getValue()));
        }else {
            //查询企业合同附件
            contractFile = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractVo.getFrameContractId()).in("type", SysFileTypeEnum.CONTRACT.getValue(),SysFileTypeEnum.SUB_CONTRACT.getValue()));
        }

        List<Object> contractList = new ArrayList<>();
        for (SysFileEntity f : contractFile) {
            String url = f.getPath();
            configVo.setUrl(url);
            RestResponse contFileRes = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
            if(contFileRes.getSuccess()){
                JSONObject jsonObject = JSONUtil.parseObj(contFileRes.get("data"));
                contractList.add(jsonObject);
            }

        }
        vo.setFjBctk(JSONUtil.toJsonStr(contractList));//合同附件
        //查询其他附件
//        List<String> fileTypes = Lists.newArrayList();
//        fileTypes.add(SysFileTypeEnum.ID_CARD.getValue());
//        fileTypes.add(SysFileTypeEnum.BUSINESS_LICENSE.getValue());
//        List<SysFileEntity> files = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractId).in("type", fileTypes));
//        List<String> stringList = new ArrayList<>();
//        for (SysFileEntity f : files) {
//            RestResponse response = contractRegFegin.sendFile(f.getPath()); //上传附件并获取到返回的数据
//            stringList.add(response.get("data").toString());
//        }
//        vo.setFjQt(JSON.toJSONString(stringList));//其他附件

        //获取备案参数
        ContractFilingParametersEntity parameters =  parametersDao.selectOne(new QueryWrapper<ContractFilingParametersEntity>().eq("project_id", contractVo.getProjectId()));

        //id
        vo.setId(contractVo.getId());
        //业务类型 注销
        vo.setYwlx("contract_filing_cancel");
        // 设置申请人姓名
        vo.setSqrxm(parameters.getSqrxm());
        // 设置申请人手机号
        vo.setSqrsjh(parameters.getSqrsjh());
        // 设置申请人证件类型
        vo.setSqrzjlx(parameters.getSqrzjlx());
        // 设置申请人证件号
        vo.setSqrzjh(parameters.getSqrzjh());
        // 设置合同编号---不是必填
        vo.setHtbh(contractVo.getContractCode());
        // 原合同备案号
        vo.setYhtbabh(res.getRecordCode());
        // 设置是否由房地产经纪机构待办（假设从ContContractEntity获取对应值，有对应get方法）
        vo.setSfjjdb("0");//0否 1 是
        // 根据是否由房地产经纪机构待办的值判断相关字段是否必填并赋值
        if ("1".equals(vo.getSfjjdb())) {
            // 房地产经纪机构名称
            vo.setJjjgmc("房地产经纪机构名称");
            // 房地产经纪机构统一信用代码
            vo.setJjjgxydm("房地产经纪机构统一信用代码");
        }


        //设置合同备注
        vo.setHtbz("注销");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 设置注销日期
        vo.setZxrq(sdf.format(new Date()));
        //设置注销原因（首次不必填）
        vo.setZxyy("退租");

        // 设置所属项目名称
        vo.setSsxmmc(parameters.getSsxmmc());

        // 设置项目所属单位统一社会信用代码
        vo.setTyshxydm(parameters.getTyshxydm());

        // 权属信息数组相关赋值
//        List<ContractRegistrationVo.CovenantOwner> covenantOwnerList = new ArrayList<>();
//        ContractRegistrationVo.CovenantOwner owner = new ContractRegistrationVo.CovenantOwner();
//        owner.setQszjlx(parameters.getQszjlx());
//        owner.setQszjbh(parameters.getQszjbh());
//        owner.setBaBzdz(parameters.getBaBzdz());
//        owner.setBaXzqh(parameters.getBaXzqh());
//        owner.setBaZj(parameters.getBaZj());
//        owner.setBaCj(parameters.getBaCj());
//        owner.setFwxz(parameters.getFwxz());
//        owner.setFwyt(parameters.getFwyt());
//        owner.setWqS(contractVo.getRoom());
//        owner.setWqTing(contractVo.getHall());
//        owner.setWqW(contractVo.getToilet());
//        owner.setWqC(contractVo.getKitchen());
//        owner.setZlfs(parameters.getZlfs());
////            owner.setBw(parameters.getBw());//出租部位
//        //装修程度（1精装修 3粗装修） t_res_source degree
//        if(DegreeEnum.ORDINARYDECORATION.getValue().equals(contractVo.getDegree())){
//            owner.setZxcd("3");//粗装修
//        }else {
//            owner.setZxcd("1");//精装修
//        }
//        owner.setJzmj(contractVo.getRoomArea().doubleValue());//房间面积（使用面积 居住面积 建筑面积）
//
//        //产权人数组
//        List<ContractRegistrationVo.CovenantOwner.Cqr> cqrList = new ArrayList<>();
//        ContractRegistrationVo.CovenantOwner.Cqr cqr = new ContractRegistrationVo.CovenantOwner.Cqr();
//        cqr.setCqrxm(parameters.getCqrxm());
//        cqr.setCqrzjlx(parameters.getCqrzjlx());
//        cqr.setCqrzjhm(parameters.getCqrzjhm());
//        List<Object> cqrFileList = new ArrayList<>();
//        RestResponse cqrFileRes = contractRegFegin.sendFile("C:\\Users\\<USER>\\Desktop\\zfba\\资产公司营业执照.pdf");  //TODO 上传附件并获取到返回的数据
//        if(cqrFileRes.getSuccess()){
//            JSONObject jsonObject = JSONUtil.parseObj(cqrFileRes.get("data"));
//            cqrFileList.add(jsonObject);
//        }
//        cqr.setCqzsmj(JSONUtil.toJsonStr(cqrFileList));//产权人扫描件
//        cqrList.add(cqr);
//        owner.setCqrList(cqrList.toArray(new ContractRegistrationVo.CovenantOwner.Cqr[0]));
//
//
//        covenantOwnerList.add(owner);
//        vo.setCovenantOwnerList(covenantOwnerList.toArray(new ContractRegistrationVo.CovenantOwner[0]));

        // 出租方信息数组相关赋值
        List<ContractRegistrationVo.CovenantHirer> covenantHirerList = new ArrayList<>();
        ContractRegistrationVo.CovenantHirer hirer = new ContractRegistrationVo.CovenantHirer();
        hirer.setHirerType(parameters.getHirerType());
        hirer.setHirerName(parameters.getHirerName());
        hirer.setHirerPhone(parameters.getHirerPhone());
        hirer.setHirerCardType(parameters.getHirerCardType());
        hirer.setHirerCardNum(parameters.getHirerCardNum());
        List<Object> hirerCarList = new ArrayList<>();
        configVo.setUrl(parameters.getHirerCardFile());
        RestResponse hirerCarFileRes = contractRegFegin.sendFile(configVo);  //TODO 上传附件并获取到返回的数据
        if(hirerCarFileRes.getSuccess()){
            JSONObject jsonObject = JSONUtil.parseObj(hirerCarFileRes.get("data"));
            hirerCarList.add(jsonObject);
        }
        hirer.setHirerCardFile(JSONUtil.toJsonStr(hirerCarList));//出租方附件
        hirer.setHirerAgentName(parameters.getHirerAgentName());
        hirer.setHirerAgentPhone(parameters.getHirerAgentPhone());
        hirer.setHirerAgentCardType(parameters.getHirerAgentCardType());
        hirer.setHirerAgentCardNum(parameters.getHirerAgentCardNum());
        List<Object> agentList = new ArrayList<>();
        configVo.setUrl(parameters.getHirerAgentCardFile());
        RestResponse agentFileRes = contractRegFegin.sendFile(configVo);  //TODO 上传附件并获取到返回的数据
        if(agentFileRes.getSuccess()){
            JSONObject jsonObject = JSONUtil.parseObj(agentFileRes.get("data"));
            agentList.add(jsonObject);
        }
        hirer.setHirerAgentCardFile(JSONUtil.toJsonStr(agentList));//出租方代理人身份证明附件
        covenantHirerList.add(hirer);
        vo.setCovenantHirerList(covenantHirerList.toArray(new ContractRegistrationVo.CovenantHirer[0]));

        //其他附件-授权书
        List<Object> qtList = new ArrayList<>();
        configVo.setUrl(parameters.getHirerAgentQtFile());
        RestResponse qtResponse = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
        if(qtResponse.getSuccess()){
            JSONObject jsonObject = JSONUtil.parseObj(qtResponse.get("data"));
            qtList.add(jsonObject);
        }
        vo.setFjQt(JSONUtil.toJsonStr(qtList));//其他附件

        // 承租方信息数组相关赋值
        List<ContractRegistrationVo.CovenantLessee> covenantLesseeList = new ArrayList<>();
        ContractRegistrationVo.CovenantLessee lessee = new ContractRegistrationVo.CovenantLessee();
        //承租方类型
        if("0".equals(contractVo.getIsOrganize())){
            lessee.setLesseeType("personal");// personal 个人备案
            lessee.setLesseeName(contractVo.getName());
            lessee.setLesseeCardType(getCardType(contractVo.getIdType()));//证件类型
            lessee.setLesseePhone(contractVo.getTel());
            lessee.setLesseeCardNum(contractVo.getIdNo());
            List<String> fileTypes = Lists.newArrayList();
            fileTypes.add(SysFileTypeEnum.ID_CARD.getValue());
            fileTypes.add(SysFileTypeEnum.BUSINESS_LICENSE.getValue());
            List<SysFileEntity> files = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractId).in("type", fileTypes));
            List<Object> stringList = new ArrayList<>();
            for (SysFileEntity f : files) {
                String url = f.getPath();
                configVo.setUrl(url);
                RestResponse response = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
                if(response.getSuccess()){
                    JSONObject jsonObject = JSONUtil.parseObj(response.get("data"));
                    stringList.add(jsonObject);
                }
            }
            lessee.setLesseeCardFile(JSONUtil.toJsonStr(stringList));
        }else {
            lessee.setLesseeType("enterprise");//enterprise 企业备案
            /*承租方 start*/
            ContFrameContractEntity frame = frameContractDao.selectById(contractVo.getFrameContractId());
            RenterEntity renter = renterFegin.getById(frame.getSignerId());
            lessee.setLesseeName(renter.getName());
            lessee.setLesseeCardType("2019");//证件类型
            lessee.setLesseePhone(renter.getTel());
            lessee.setLesseeCardNum(renter.getIdNo());
            List<String> fileTypes = Lists.newArrayList();
            fileTypes.add(SysFileTypeEnum.ID_CARD.getValue());
            fileTypes.add(SysFileTypeEnum.BUSINESS_LICENSE.getValue());
            List<SysFileEntity> files = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractVo.getFrameContractId()).in("type", fileTypes));
            List<Object> stringList = new ArrayList<>();
            for (SysFileEntity f : files) {
                String url = f.getPath();
                configVo.setUrl(url);
                RestResponse response = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
                if(response.getSuccess()){
                    JSONObject jsonObject = JSONUtil.parseObj(response.get("data"));
                    stringList.add(jsonObject);
                }
            }
            lessee.setLesseeCardFile(JSONUtil.toJsonStr(stringList));
            /*承租方 end*/
            /*承租方代理人 start*/
            lessee.setLesseeAgentName(contractVo.getName());// 承租方代理人姓名（承租方类型为企业备案时必填）
            lessee.setLesseeAgentPhone(contractVo.getTel());// 承租方代理人手机号（承租方类型为企业备案时必填）
            lessee.setLesseeAgentCardType(getCardType(contractVo.getIdType()));// 承租方代理人证件类型（详见字典3.4人员证件类型，承租方类型为企业备案时必填）
            lessee.setLesseeAgentCardNum(contractVo.getIdNo());      // 承租方代理人证件号码（承租方类型为企业备案时必填）
            List<String> dlrTypes = Lists.newArrayList();
            dlrTypes.add(SysFileTypeEnum.ID_CARD.getValue());
            dlrTypes.add(SysFileTypeEnum.BUSINESS_LICENSE.getValue());
            List<SysFileEntity> dlrFiles = sysFileDao.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", contractId).in("type", fileTypes));
            List<Object> dlrList = new ArrayList<>();
            for (SysFileEntity f : dlrFiles) {
                String url = f.getPath();
                configVo.setUrl(url);
                RestResponse response = contractRegFegin.sendFile(configVo); //上传附件并获取到返回的数据
                if(response.getSuccess()){
                    JSONObject jsonObject = JSONUtil.parseObj(response.get("data"));
                    dlrList.add(jsonObject);
                }
            }
            lessee.setLesseeAgentCardFile(JSONUtil.toJsonStr(stringList));// 承租方代理人身份证明附件，文件上传返回的对象数组转字符串（承租方类型为企业备案时必填）
            /*承租方代理人 end*/
        }
        covenantLesseeList.add(lessee);
        vo.setCovenantLesseeList(covenantLesseeList.toArray(new ContractRegistrationVo.CovenantLessee[0]));

        //存储状态及record_id 到 t_cont_contract_source_rel 表
        String status = RecordState.LOGOUTING.getValue();
        String message = null;
        RestResponse pushRes = contractRegFegin.pushContractReg(vo);
        if(!pushRes.getSuccess()){
            status = RecordState.LOGOUTFAIL.getValue();
            message = pushRes.getMessage();
            res.setRecordErrorMsg(message);
        }
        res.setRecordState(status);
        res.setRecordId(parameters.getRecordIdPrefix()+contractVo.getId());
        res.setRecordState(RecordState.LOGOUTING.getValue());
        res.updateById();

        return pushRes;
    }


    private void vild(String[] vileArray,RecordPo po) throws Exception {
        Field[] field = po.getClass().getDeclaredFields(); //获取实体类的所有属性，返回Field数组
        for (int j = 0; j < field.length; j++) { //遍历所有属性
            String name = field[j].getName(); //获取属性的名字
            if (ArrayUtil.contains(vileArray, name)) {
                Method m = po.getClass().getMethod("get" + name);
                Object value = m.invoke(po); //利用反射原理，调用getter方法获取属性值
                if (value == null) {
                    log.error(name+"为空");
                    throw new BusinessException("请确保必填项非空再提交！");
                }
            }
        }

    }

    /**
     * 备案
     *
     * @param contractId
     */
    @Async
    public void putOnRecord(String contractId) throws Exception {
        record(contractId, false);
    }

    /**
     * 注销备案
     *
     * @param contractId
     */
    @Async
    public void outOnRecord(String contractId) throws Exception {
        outRecord(contractId,false);
    }
}
