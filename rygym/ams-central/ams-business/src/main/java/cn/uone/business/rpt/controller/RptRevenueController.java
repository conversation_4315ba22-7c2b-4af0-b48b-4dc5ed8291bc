package cn.uone.business.rpt.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.rpt.RptRevenueEntity;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueDataVo;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueInfoVo;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueSearchVo;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueVo;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.rpt.service.IRptRevenueService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.ExcelDataUtil;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.SafeCompute;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 营收确认明显表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-16
 */
@RestController
@RequestMapping("/report/revenue")
public class RptRevenueController extends BaseController {
    @Autowired
    private IRptRevenueService revenueService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IResProjectParaService resProjectParaService;

    @RequestMapping(value = "/page", method = RequestMethod.GET)
    public RestResponse page(Page page, RptRevenueSearchVo vo) {
        RestResponse response = new RestResponse();
        if (ObjectUtil.isNotNull(vo.getEndCreateDate())) {
            vo.setEndCreateDate(DateUtil.endOfDay(vo.getEndCreateDate()));
        }
        if (ObjectUtil.isNotNull(vo.getEndContractBegin())) {
            vo.setEndContractBegin(DateUtil.endOfDay(vo.getEndContractBegin()));
        }
        if (ObjectUtil.isNotNull(vo.getEndContractFinish())) {
            vo.setEndContractFinish(DateUtil.endOfDay(vo.getEndContractFinish()));
        }
        vo.setProjectId(UoneHeaderUtil.getProjectId());
        IPage<String> contractPage = revenueService.selectContractIdsByMap(page, vo);
        vo.setContractIds(contractPage.getRecords());

        List<RptRevenueVo> records =  revenueService.findByCondition(vo);
        Page p=page.setRecords(records);
        vo.setContractIds(null);
        records.add(revenueService.selectRevenueTotalByMap(vo));

        return response.setSuccess(true).setData(p);
    }

    @UoneLog("营收确认明细表数据导入")
    @RequestMapping("/importData")
    @CacheLock(prefix = "importData", expire = 60)
    public RestResponse importData(@RequestParam("file") MultipartFile file) throws Exception {
        if(file.isEmpty()){
            return RestResponse.failure("请选择上传文件");
        }
        List<RptRevenueDataVo> data = ExcelDataUtil.importData(file.getInputStream(), RptRevenueDataVo.class);
        String ret= revenueService.importData(data);
        return RestResponse.success().setData(ret);
    }

    @UoneLog("营收确认明细表模板导出")
    @RequestMapping(value="/exportTemp")
    public void exportTemp(HttpServletResponse response, @RequestParam(value = "type",required = false) String type) throws BusinessException {
        Map<String,Object> bean= Maps.newHashMap();
        bean.put("types","是,否");
        bean.put("orderItemTypes","租金,综合服务费");
        ExcelRender.me("/excel/import/rptRevenueTemp.xls" ).beans(bean).render(response);
    }

    @RequestMapping("/export")
    public void export(HttpServletResponse response, RptRevenueSearchVo vo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        vo.setProjectId(UoneHeaderUtil.getProjectId());
        List<RptRevenueVo> list = revenueService.findByCondition(vo);
        list.add(revenueService.selectRevenueTotalByMap(vo));
        for (RptRevenueVo revenueVo : list) {
            revenueVo.setOrderItemType(OrderItemTypeEnum.getNameByValue(revenueVo.getOrderItemType()));
            revenueVo.setSum(revenueVo.getSum());
            revenueVo.setTaxSum(revenueVo.getTaxSum());
            revenueVo.setAll1AndTaxSum(revenueVo.getAll1AndTaxSum());
        }
        beans.put("revenues", list);
        ExcelRender.me("/excel/export/rptRevenue.xlsx").beans(beans).render(response);
    }

    @RequestMapping("/getRevenueDetails")
    public RestResponse getRevenueDetails(String contractId, String orderItemType) {
        RestResponse response = new RestResponse();
        List<RptRevenueInfoVo> list = new ArrayList();
        QueryWrapper<RptRevenueEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("contract_id", contractId);
        queryWrapper.eq("order_item_type", orderItemType);
        List<RptRevenueEntity> revenueList = revenueService.list(queryWrapper);
        for (RptRevenueEntity revenue : revenueList) {
            ResSourceEntity source = resSourceService.getById(revenue.getSourceId());
            String taxes = resProjectParaService.getByCode(ProjectParaEnum.RENT_TAXES.getValue(), source.getProjectId());
            BigDecimal tax = BigDecimal.ZERO;
            if(StrUtil.isNotBlank(taxes)){
                tax = new BigDecimal(taxes);
            }
            RptRevenueInfoVo info = new RptRevenueInfoVo();
            String year = revenue.getReportYear();
            if (ObjectUtil.isNotNull(revenue.getJan())) {
                BigDecimal payment = revenue.getJan();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-01", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getFeb())) {
                BigDecimal payment = revenue.getFeb();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-02", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getMar())) {
                BigDecimal payment = revenue.getMar();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-03", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getApr())) {
                BigDecimal payment = revenue.getApr();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-04", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getMay())) {
                BigDecimal payment = revenue.getMay();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-05", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getJun())) {
                BigDecimal payment = revenue.getJun();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-06", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getJul())) {
                BigDecimal payment = revenue.getJul();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-07", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getAug())) {
                BigDecimal payment = revenue.getAug();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-08", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getSept())) {
                BigDecimal payment = revenue.getSept();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-09", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getOct())) {
                BigDecimal payment = revenue.getOct();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-10", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getNov())) {
                BigDecimal payment = revenue.getNov();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-11", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
            if (ObjectUtil.isNotNull(revenue.getDece())) {
                BigDecimal payment = revenue.getDece();
                BigDecimal payment1AndTax = payment.divide(BigDecimal.ONE.add(tax), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal paymentTax = payment.subtract(payment1AndTax);
                info = assembleInfo(year + "-12", payment, paymentTax, payment1AndTax, revenue.getType());
                list.add(info);
            }
        }
        list = mergeByYearMonth(list);
        list.sort((x, y) -> StrUtil.compare(x.getYearMonth(), y.getYearMonth(), true));
        return response.setSuccess(true).setData(list);
    }

    @RequestMapping(value = "/getOrderType", method = RequestMethod.GET)
    public RestResponse getOrderType(){
        List<RptRevenueEntity> orderType = revenueService.getOrderType();
        return RestResponse.success().setData(orderType);
    }

    private RptRevenueInfoVo assembleInfo(String yearMonth, BigDecimal payment, BigDecimal tax, BigDecimal payment1AndTax, String type) {
        RptRevenueInfoVo info = new RptRevenueInfoVo();
        if (type.equals("1")) {
            info.setYearMonth(yearMonth);
            info.setPayment(payment);
            info.setTax(tax);
            info.setPayment1AndTax(payment1AndTax);
        } else {
            info.setYearMonth(yearMonth);
            info.setDeductPayment(payment);
            info.setDeductTax(tax);
            info.setDeduct1AndTax(payment1AndTax);
        }
        return info;
    }

    private List mergeByYearMonth(List<RptRevenueInfoVo> list) {
        List<RptRevenueInfoVo> result = new ArrayList();
        HashMap<String, RptRevenueInfoVo> map = new HashMap();
        for (RptRevenueInfoVo vo : list) {
            RptRevenueInfoVo item = map.get(vo.getYearMonth());
            if (ObjectUtil.isNotNull(item)) {
                if (ObjectUtil.isNotNull(vo.getPayment())) {
                    item.setPayment(vo.getPayment());
                    item.setTax(vo.getTax());
                    item.setPayment1AndTax(vo.getPayment1AndTax());

                    item.setPaymentTotal(SafeCompute.add(vo.getPayment(), item.getPaymentTotal()));
                    item.setTaxTotal(SafeCompute.add(vo.getTax(), item.getTaxTotal()));
                    item.setTotal(SafeCompute.add(vo.getPayment1AndTax(), item.getTotal()));
                }
                if (ObjectUtil.isNotNull(vo.getDeductPayment())) {
                    item.setDeductPayment(vo.getDeductPayment());
                    item.setDeductTax(vo.getDeductTax());
                    item.setDeduct1AndTax(vo.getDeduct1AndTax());

                    item.setPaymentTotal(SafeCompute.add(vo.getDeductPayment(), item.getPaymentTotal()));
                    item.setTaxTotal(SafeCompute.add(vo.getDeductTax(), item.getTaxTotal()));
                    item.setTotal(SafeCompute.add(vo.getDeduct1AndTax(), item.getTotal()));
                }
            } else {
                item = vo;
                item.setPaymentTotal(SafeCompute.add(vo.getDeductPayment(), vo.getPayment()));
                item.setTaxTotal(SafeCompute.add(vo.getDeductTax(), vo.getTax()));
                item.setTotal(SafeCompute.add(vo.getDeduct1AndTax(), vo.getPayment1AndTax()));
                map.put(vo.getYearMonth(), vo);
                result.add(item);
            }
        }
        return result;
    }

}
