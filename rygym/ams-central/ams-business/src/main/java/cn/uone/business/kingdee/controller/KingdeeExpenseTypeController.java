package cn.uone.business.kingdee.controller;


import cn.uone.business.kingdee.service.IKingdeeExpenseTypeService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-10
 */
@RestController
@RequestMapping("/kingdee-expense-type-entity")
public class KingdeeExpenseTypeController extends BaseController {

    @Autowired
    IKingdeeExpenseTypeService kingdeeExpenseTypeService;

    @RequestMapping("/getExpenseTypeList")
    public RestResponse getExpenseTypeList(@RequestParam(value = "id",required = false) String id) {
        RestResponse response = new RestResponse();
        Map<String,Object> map = Maps.newHashMap();
        map.put("type",id);
        return response.setSuccess(true).setData(kingdeeExpenseTypeService.getList(map));
    }
}
