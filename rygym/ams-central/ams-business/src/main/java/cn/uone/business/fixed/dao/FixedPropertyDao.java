package cn.uone.business.fixed.dao;

import cn.uone.bean.entity.business.fixed.FixedPropertyEntity;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 资产信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
public interface FixedPropertyDao extends BaseMapper<FixedPropertyEntity> {

    IPage<FixedPropertyEntity> getPageByParams(Page page, DataScope scope, Map<String, Object> map);

    List<FixedPropertyEntity> getPageByParams(DataScope scope, Map<String, Object> map);

    FixedPropertyEntity getIdByRfidCode(String code);
}
