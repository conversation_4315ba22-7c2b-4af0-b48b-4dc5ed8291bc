package cn.uone.business.apro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.ApprovalTemplateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalProjectParaEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalProjectParaService;
import cn.uone.business.apro.service.Operation;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.util.wechat.ApprovalStateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 退押金
 */
@Service
public class ChangeRentertion implements Operation {

    @Resource
    private IApprovalCommitService approvalCommitService ;
    @Resource
    private IBizReleaseService releaseService ;
    @Resource
    private IApprovalProjectParaService projectParaService;


    @Override
    public ApprovalCommitEntity apply(Expression expression) {
        BizReleaseEntity entity=releaseService.getById(expression.getCodeId());
        ApprovalCommitEntity  commitEntity=null;
        if(ObjectUtil.isNotNull(entity)){
            commitEntity= approvalCommitService.getCommitEntity(expression.getCodeId(), ApprovalTypeEnum.CHANGERENTER.getValue());
            Map<String,Object> info=approvalCommitService.getDepositInfo(entity.getContractId());
            ApprovalProjectParaEntity temp=projectParaService.get((String) info.get("project_id"),ApprovalTemplateEnum.CHANGEROOM.getType(),true);
            if(commitEntity==null){
                commitEntity=new ApprovalCommitEntity();
                ApprovalStateUtil.initCommit(commitEntity,expression.getCodeId());
                commitEntity.setSigner(entity.getRenterId());
                commitEntity.setTemplateid(temp.getApprovalTempId());
                commitEntity.setType(ApprovalTypeEnum.CHANGERENTER.getValue());
                commitEntity.setTableName(ApprovalTypeEnum.CHANGERENTER.getTable());
                commitEntity.setTitle(ApprovalTypeEnum.CHANGERENTER.getName());
                commitEntity.setTitle1("房源地址:"+info.get("source_name"));
                commitEntity.setTitle2("签约方:"+ info.get("name"));
                String memo=StrUtil.isBlank(entity.getChangeRoomApprovalReason())?" ":entity.getChangeRoomApprovalReason();
                commitEntity.setTitle3("备注内容:"+ memo);
                approvalCommitService.save(commitEntity);
            }else{
                String memo=StrUtil.isBlank(entity.getChangeRoomApprovalReason())?" ":entity.getChangeRoomApprovalReason();
                approvalCommitService.resetInitCommit(commitEntity,"房源地址:"+info.get("source_name"),"签约方:"+ info.get("name"),"备注内容:"+memo,null);
                approvalCommitService.updateById(commitEntity);
            }
        }
        return commitEntity;
    }

}
