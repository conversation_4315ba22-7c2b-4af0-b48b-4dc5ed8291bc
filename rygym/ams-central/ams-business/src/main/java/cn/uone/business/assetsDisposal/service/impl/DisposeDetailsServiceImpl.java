package cn.uone.business.assetsDisposal.service.impl;

import cn.uone.bean.entity.business.assetsDisposal.DisposeDetailsEntity;
import cn.uone.business.assetsDisposal.dao.DisposeDetailsDao;
import cn.uone.business.assetsDisposal.service.IDisposeDetailsService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 处置详情记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@Service
public class DisposeDetailsServiceImpl extends ServiceImpl<DisposeDetailsDao, DisposeDetailsEntity> implements IDisposeDetailsService {

    @Override
    public Integer delByDisposalId(String disposalId) {
        return baseMapper.delByDisposalId(disposalId);
    }

    @Override
    public IPage<DisposeDetailsEntity> getByType(Page page, String type) {
        return baseMapper.getByType(page,type);
    }


}
