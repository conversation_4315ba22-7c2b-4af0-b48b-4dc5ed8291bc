package cn.uone.business.base.service;

import cn.uone.bean.entity.business.base.BaseCarEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 服务类
 * 车辆信息
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IBaseCarService extends IService<BaseCarEntity> {

    /**
     * 新增车辆信息
     *
     * @param carEntity
     * @return
     * @throws Exception
     */
    int addCar(BaseCarEntity carEntity) throws Exception;

    /**
     * 修改车辆信息
     *
     * @param baseCarEntity
     * @return
     * @throws Exception
     */
    int updateBaseCarService(BaseCarEntity baseCarEntity) throws Exception;

    /**
     * 删除车辆信息
     *
     * @param id
     * @return
     * @throws Exception
     */
    int deleteBaseCarService(String id) throws Exception;

    BaseCarEntity getByContractSourceId(String id);
}
