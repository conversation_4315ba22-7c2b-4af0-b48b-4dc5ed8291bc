package cn.uone.business.equ.service;

import cn.uone.bean.entity.business.equ.EquDeviceEntity;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 设备表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-19
 */
public interface IEquDeviceService extends IService<EquDeviceEntity> {
    Page<EquDeviceEntity> page(Page page, EquDeviceEntity entity);
    boolean saveOrUpdate(EquDeviceEntity entity, List<MultipartFile> files,List<MultipartFile> image);
}
