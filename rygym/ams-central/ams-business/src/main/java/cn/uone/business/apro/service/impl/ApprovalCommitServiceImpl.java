package cn.uone.business.apro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.ApprovalTemplateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.application.enumerate.contract.ReleaseChangeRoomStatEnum;
import cn.uone.application.enumerate.contract.ReleaseRefundStatEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.source.PatrolStateEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalDetailEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.business.apro.vo.ApprovalCommitVo;
import cn.uone.bean.entity.business.apro.vo.ApprovalSearchVo;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.res.ResPatrolEntity;
import cn.uone.bean.entity.business.res.ResSourceConfigureEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.business.Guomi.service.IGuomiService;
import cn.uone.business.apro.dao.ApprovalCommitDao;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalDetailService;
import cn.uone.business.apro.service.Operation;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.biz.service.IBizSettleService;
import cn.uone.business.cont.service.IContParService;
import cn.uone.business.res.service.IResPatrolService;
import cn.uone.business.res.service.IResSourceConfigureService;
import cn.uone.business.util.OperatorFactory;
import cn.uone.fegin.tpi.IQyWechatFegin;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.CodeUtil;
import cn.uone.util.wechat.ApprovalStateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 提交审批流程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-09
 */
@Service
public class ApprovalCommitServiceImpl extends ServiceImpl<ApprovalCommitDao, ApprovalCommitEntity> implements  IApprovalCommitService {
    @Autowired
    private IQyWechatFegin qyWechatFegin;
    @Autowired
    private IResPatrolService patrolService;
    @Autowired
    @Lazy
    private IBizSettleService settleService;
    @Autowired
    @Lazy
    private IBilOrderService orderService;
    @Autowired
    private IApprovalDetailService approvalDetailService;
    @Autowired
    @Lazy
    private IGuomiService guomiService;
    @Autowired
    private IResSourceConfigureService configureService;
    @Autowired
    private IContParService contParService;

    @Override
    public IPage<ApprovalCommitVo> commitByMe(Page page, ApprovalSearchVo vo) {
        return baseMapper.commitByme(page,vo);
    }

    @Override
    public IPage<ApprovalCommitVo> mytodo(Page page,ApprovalSearchVo vo) {
        return baseMapper.mytodo(page,vo);
    }

    @Override
    public IPage<ApprovalCommitVo> toSubmit(Page page, ApprovalSearchVo vo) {
        return baseMapper.toSubmit(page,vo);
    }

    @Override
    public IPage<ApprovalCommitVo> toApproval(Page page,ApprovalSearchVo vo) {
        return baseMapper.toApproval(page,vo);
    }

    @Override
    public IPage<ApprovalCommitVo> myjoin(Page page,ApprovalSearchVo vo) {
        return baseMapper.myjoin(page,vo);
    }

    @Override
    public ApprovalCommitVo getDesignContractByid(String id) {
        return baseMapper.getDesignContractByid(id);
    }

    @Override//
    public ApprovalCommitEntity addOrUpdateComit(Expression expression) {
        Operation targetOperation = OperatorFactory
                .getOperation(expression.getType())
                .orElseThrow(() -> new IllegalArgumentException("Invalid Operator"));
        ApprovalCommitEntity sub= targetOperation.apply(expression);
       saveApprovalRel(sub.getId(),sub.getCodeId());
        return sub;
    }

    @Override
    public ApprovalCommitEntity commit(String commitId,boolean reset) {
        ApprovalCommitEntity entity=baseMapper.selectById(commitId);
        if(reset){
            entity.setOldCode(entity.getCode());
            entity.setCode(CodeUtil.generateUuid(true));
            baseMapper.updateById(entity);
        }else{
            entity.setCreateDate(new Date());
            entity.setStatus(ApprovalStateEnum.APPROVAL.getValue());
            baseMapper.updateById(entity);
        }
        changeStatus(entity,reset);
        return entity;
    }

    @Override
    public void changeStatus(ApprovalCommitEntity commitEntity,boolean reset){
        new Thread() {
            public void run() {
                try {
                    Thread.sleep(60000);
                    //查询提交审批单号是否存在
                    JSONObject object=qyWechatFegin.getopenapprovaldata(commitEntity.getCode());
                    //待提交--审批中
                    if(ObjectUtil.isNull(object)) {
                        if(reset){
                            commitEntity.setCode(commitEntity.getOldCode());
                        }
                        commitEntity.setStatus(ApprovalStateEnum.TOBESUBMIT.getValue());
                        commitEntity.updateById();
                        //账单、优惠券审批状态改变
                        if(ApprovalStateUtil.type(commitEntity.getType())){
                            baseMapper.upApprovalState(commitEntity.getTableName(),getKey(commitEntity.getType()),ApprovalStateEnum.TOBESUBMIT.getValue(),commitEntity.getCodeId());
                        }
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        }.start();
    }



    @Override
    public ApprovalCommitEntity getBycommitId(String id) {
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        ApprovalCommitEntity commitEntity=baseMapper.selectById(id);
        if(commitEntity!=null){
            commitEntity.setStatusText1(ApprovalStateEnum.getNameByValue(commitEntity.getStatus()));
            commitEntity.setTime(simpleDateFormat.format(commitEntity.getCreateDate()));
            if(commitEntity.getApplyTime()!=null){
                commitEntity.setSqTime(simpleDateFormat.format(commitEntity.getApplyTime()));
            }else{
                commitEntity.setSqTime(simpleDateFormat.format(commitEntity.getCreateDate()));
            }
            if(commitEntity.getPayTime()!=null){
                commitEntity.setPayDate(simpleDateFormat.format(commitEntity.getPayTime()));
            }
        }
        return commitEntity;
    }

    @Override
    public ApprovalCommitVo getAlreadyPayAll(Map<String, Object> map) {
        return baseMapper.getAlreadyPayAll(map);
    }

    @Override
    public void upApprovalState(String table,String key, String status, String id) {
        baseMapper.upApprovalState(table,key,status,id);
    }

    @Override
    public UserEntity getQyUserInfo(String qywechat) {
        return baseMapper.getQyUserInfo(qywechat);
    }


    @Override
    public Map<String, Object> getDepositInfo(String contractId) {
        return baseMapper.getDepositInfo(contractId);
    }

    @Override
    public ApprovalCommitEntity getCommitEntity(String codeid,String type){
        return baseMapper.selectOne(new QueryWrapper<ApprovalCommitEntity>().eq("code_id",codeid).eq("type",type));
    }

    @Override
    public void resetInitCommit(ApprovalCommitEntity commitEntity,String title1,String title2,String title3,String state){
        commitEntity.setUserid(UoneSysUser.id());
        if(ObjectUtil.isNull(state)){
            commitEntity.setOldCode(CodeUtil.generateUuid(true));
        }
        commitEntity.setTitle1(title1);
        commitEntity.setTitle2(title2);
        commitEntity.setTitle3(title3);
        commitEntity.setApplyTime(new Date());
        commitEntity.setStatus(ObjectUtil.isNull(state)?ApprovalStateEnum.TOBESUBMIT.getValue():state);
        if(ApprovalStateUtil.type(commitEntity.getType())){
            baseMapper.upApprovalState(commitEntity.getTableName(),getKey(commitEntity.getType()),ApprovalStateEnum.TOBESUBMIT.getValue(),commitEntity.getCodeId());
        }
        approvalDetailService.remove(new QueryWrapper<ApprovalDetailEntity>().eq("code",commitEntity.getCode()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateApproval(String code, String status) throws Exception {
        ApprovalCommitEntity entity = baseMapper.selectOne(new QueryWrapper<ApprovalCommitEntity>().eq("code", code));
        if (entity != null) {
            if(ApprovalStateEnum.REJECT.getValue().equals(status)){

                if(ApprovalTypeEnum.REIMBURSEMENT.getValue().equals(entity.getType())){
                    entity.setOldCode(CodeUtil.generateUuid(true));
                }else{
                    entity.setOldCode(null);//多次提交oldCode不清单号重复
                }
            }
            //撤销能重新提交
            if(ApprovalStateEnum.CANCEL.getValue().equals(status)){
                entity.setOldCode(entity.getCode());
            }
            entity.setStatus(status);
            entity.updateById();
            //账单、优惠券审批状态改变
            if(ApprovalStateUtil.type(entity.getType())){
                for (String string : entity.getCodeId().split(",")) {
                    baseMapper.upApprovalState(entity.getTableName(),getKey(entity.getType()),status,string);
                }
            }
            //退押金
            if(ApprovalTypeEnum.RETURNDEPOSIT.getValue().equals(entity.getType())){
                if(status.equals(ApprovalStateEnum.COMPLETE.getValue())){
                    settleService.callBack(entity.getCodeId(), ReleaseRefundStatEnum.YES.getValue());
                }else if(status.equals(ApprovalStateEnum.REJECT.getValue())){
                    settleService.callBack(entity.getCodeId(), ReleaseRefundStatEnum.NO.getValue());
                }else if(status.equals(ApprovalStateEnum.CANCEL.getValue())){
                    settleService.callBack(entity.getCodeId(), ReleaseRefundStatEnum.NO.getValue());
                }
            }
            //三级报修且审批通过
            if(ApprovalTypeEnum.PATROL.getValue().equals(entity.getType())){
                ResPatrolEntity patrolEntity=patrolService.getById(entity.getCodeId());
                if(ApprovalStateEnum.COMPLETE.getValue().equals(status)){
                    patrolEntity.setManagerId(patrolEntity.getChargeUser());
                    patrolEntity.setState(PatrolStateEnum.FOLLOWUP.getValue());
                    patrolEntity.updateById();
                }else if(ApprovalStateEnum.REJECT.getValue().equals(status)){
                    patrolEntity.setManagerId(patrolEntity.getChargeUser());
                    patrolEntity.updateById();
                }
            }

            //免换房费
            if(ApprovalTypeEnum.CHANGEROOM.getValue().equals(entity.getType()) || ApprovalTypeEnum.CHANGERENTER.getValue().equals(entity.getType())){
                if(status.equals(ApprovalStateEnum.COMPLETE.getValue())){
                    settleService.roomChangeCallBack(entity.getCodeId(),ReleaseChangeRoomStatEnum.NO.getValue());
                }else if(status.equals(ApprovalStateEnum.REJECT.getValue())){
                    settleService.roomChangeCallBack(entity.getCodeId(),ReleaseChangeRoomStatEnum.YES.getValue());
                }

            }
            //取消账单
            if(ApprovalTypeEnum.CANCELORDER.getValue().equals(entity.getType()) && ApprovalStateEnum.COMPLETE.getValue().equals(status)){
                String ids = entity.getCodeId();
                Boolean flag = true;
                for (String id : ids.split(",")) {
                    BilOrderEntity bil = orderService.getById(id);
                    if (!OrderTypeEnum.DEPOSIT.getValue().equals(bil.getOrderType()) && contParService.orderSynToCCB(bil.getContractId())) {
                        if (!guomiService.queryGuomiOrder(bil)) {
                            flag = false;
                            break;
                        }
                    }
                }
                if (flag) {
                    orderService.cancelDone(ids, PayStateEnum.CANCEL.getValue(), entity.getSummary());
                }
            }

            //房源改价
            if(ApprovalTypeEnum.SOURCEPRICE.getValue().equals(entity.getType()) && ApprovalStateEnum.COMPLETE.getValue().equals(status)){
                ResSourceConfigureEntity  sourceConfigureEntity = configureService.getById(entity.getCodeId());
                sourceConfigureEntity.setPrice(entity.getPrice());
                sourceConfigureEntity.setDeposit(entity.getDeposit());
                if(entity.getTemplateid().equals(ApprovalTemplateEnum.SOURCEPRICELD.getValue())){
                    sourceConfigureEntity.setLowPrice(entity.getPrice());
                }
                sourceConfigureEntity.updateById();
            }

            //修改尾期账单
            if(ApprovalTypeEnum.FIXLASTORDER.getValue().equals(entity.getType()) && ApprovalStateEnum.COMPLETE.getValue().equals(status)){
                orderService.fixOrderDone(entity.getCodeId());
            }
        }
    }

    public String getKey(String type){
       return ApprovalTypeEnum.getKeyByType(type);
    }

    @Override
    public ApprovalCommitEntity getByFun(String codeid, String type) {
        return baseMapper.getByFun(codeid,type);
    }

    @Override
    public Map<String, Object> getOrderCode(List codeId) {
        return baseMapper.getOrderCode(codeId);
    }

    @Override
    public String getOrderCodeIds(List types) {
        List<String> codeIds = baseMapper.getOrderCodeIds(types);
        StringBuilder sb = new StringBuilder();
        for (String codeId : codeIds) {
            sb.append(codeId).append(",");
        }
        return sb.length() > 1 ? sb.toString().substring(0, sb.toString().length() - 1) : "";
    }

    @Override
    public void saveApprovalRel(String approvalId, String codeId) {
        for (String id : codeId.split(",")) {
            baseMapper.saveApprovalRel(approvalId,id);
        }
    }

    @Override
    public void deleteRel() {
        baseMapper.deleteRel();
    }
}
