package cn.uone.business.onlineBid.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.bean.entity.business.onlineBid.BidAfficheEntity;
import cn.uone.bean.entity.business.onlineBid.FailureAfficheEntity;
import cn.uone.bean.entity.business.onlineBid.WinAfficheEntity;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.business.onlineBid.service.IBidAfficheService;
import cn.uone.business.onlineBid.service.IFailureAfficheService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 流标公告管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@RestController
@RequestMapping("/onlineBid/failureAffiche")
public class FailureAfficheController extends BaseController {

    @Autowired
    IFailureAfficheService  service;

    @Autowired
    private IBidAfficheService bidAfficheService;

    @Autowired
    IResProjectParaService resProjectParaService;


    @GetMapping("/page")
    @UonePermissions
    public RestResponse page(Page<FailureAfficheEntity> page, FailureAfficheEntity entity){
        QueryWrapper<FailureAfficheEntity> wrapper = new QueryWrapper<>();
        if(StrUtil.isNotBlank(entity.getKeyword())){
            wrapper.like("property_info","%"+entity.getKeyword()+"%");
        }
        if(StrUtil.isNotBlank(entity.getStatus())){
            wrapper.like("status","%"+entity.getStatus()+"%");
        }
        if(StrUtil.isNotBlank(entity.getTitle())){
            wrapper.like("title","%"+entity.getTitle()+"%");
        }
//        wrapper.eq("type","3");
        String projectId = UoneHeaderUtil.getProjectId();
        if(StrUtil.isNotBlank(projectId)){
            wrapper.eq("project_id",projectId);
        }
        wrapper.orderByDesc("create_date");
        IPage<FailureAfficheEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }
    @GetMapping("/getById")
    @UonePermissions
    public RestResponse getById(String id){
        FailureAfficheEntity entity = service.getById(id);
        return RestResponse.success().setData(entity);
    }

    @PostMapping("/save")
    public RestResponse save(FailureAfficheEntity entity){
        entity.setType("3");
        entity.setStatus("1");
        entity.setPublisher(UoneSysUser.loginName());
        //取项目参数判断是否需要审核发布
        ResProjectParaEntity resProjectParaEntity = resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.BID_IS_AUDIT.getValue(), UoneHeaderUtil.getProjectId());
        String isAudit = resProjectParaEntity.getParamValue();
        if("0".equals(isAudit)){
            entity.setStatus("4");//已发布
        }
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }

    @PostMapping("/edit")
    public RestResponse edit(FailureAfficheEntity entity){
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    @PostMapping("/audit")
    public RestResponse audit(FailureAfficheEntity entity){
        BidAfficheEntity afficheEntity = bidAfficheService.getById(entity.getBidId());
        if("2".equals(entity.getStatus())){
            afficheEntity.setStatus("8");
        }else {
            afficheEntity.setStatus(entity.getBidStatus());
        }
        afficheEntity.updateById();
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    /*
 下拉菜单获取项目
*/
    @RequestMapping("/selectProject")
    public RestResponse selectCompany() {
        List<Map<String,Object>> data=bidAfficheService.selectProject();
        return RestResponse.success().setData(data);
    }

    /*
     下拉菜单获取楼栋
   */
    @RequestMapping("/selectPartition")
    public RestResponse selectPartition(String projectId) {
        List<Map<String,Object>> data=bidAfficheService.selectPartition(projectId);
        return RestResponse.success().setData(data);
    }

    /*
     下拉菜单获取房源
   */
    @RequestMapping("/selectSource")
    public RestResponse selectSource(String partitionId) {
        List<Map<String,Object>> data=bidAfficheService.selectSource(partitionId);
        return RestResponse.success().setData(data);
    }


}
