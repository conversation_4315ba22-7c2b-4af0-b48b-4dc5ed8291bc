package cn.uone.business.rpt.service;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
public interface IRptBusinessBoardService {

    Map<String,Object> getTotalSourceData(Map<String,Object> map);
    Map<String,Object> getSignNumData(Map<String,Object> map);
    Map<String,Object> getPayNumData(Map<String,Object> map);
    Map<String,Object> getTotalPaymentData(Map<String,Object> map);
    List<Map<String,Object>> getPaymentListData(Map<String,Object> map);
}
