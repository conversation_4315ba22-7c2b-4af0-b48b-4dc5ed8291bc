package cn.uone.business.fixed.service;

import cn.uone.bean.entity.business.fixed.AssetInventoryEntity;
import cn.uone.bean.entity.business.fixed.vo.AssetInventoryVo;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 * 固定资产盘点主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
public interface IAssetInventoryService extends IService<AssetInventoryEntity> {

    IPage<AssetInventoryEntity> page(Page page, AssetInventoryEntity entity);

    IPage<AssetInventoryVo> pageList(Page page, Map<String, Object> map);

    Object keywordList(Page page, String keyword, String flag);

    boolean saveInventory(AssetInventoryVo assetInventoryVo) throws Exception;

    boolean updateAssetInventory(String inventoryId);

    boolean updateInventory(AssetInventoryVo assetInventoryVo);

    AssetInventoryVo getInventoryById(String inventoryId);
}
