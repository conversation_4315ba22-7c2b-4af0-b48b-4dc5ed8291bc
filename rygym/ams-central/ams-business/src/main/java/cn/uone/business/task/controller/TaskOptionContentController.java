package cn.uone.business.task.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.task.*;
import cn.uone.business.task.service.ITaskOptionContentService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *
 * 选项内容表  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@RestController
@RequestMapping("/task/optionContent")
public class TaskOptionContentController extends BaseController {

    @Autowired
    private ITaskOptionContentService service;

    @GetMapping("/page")
    public RestResponse page(Page<TaskOptionContentEntity> page, TaskOptionContentEntity entity){
        QueryWrapper<TaskOptionContentEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc("create_date");
        if(StrUtil.isNotBlank(entity.getTypeName())){
            wrapper.like("type_name","%"+entity.getTypeName()+"%");
        }
        if(StrUtil.isNotBlank(entity.getOptionTypeId())){
            wrapper.like("option_type_id","%"+entity.getOptionTypeId()+"%");
        }
        IPage<TaskOptionContentEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/save")
    public RestResponse save(TaskOptionContentEntity entity){
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }


}
