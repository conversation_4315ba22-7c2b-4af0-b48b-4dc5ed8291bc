package cn.uone.business.fixed.service;

import cn.uone.bean.entity.business.fixed.FixedMaintenanceTeamEntity;
import cn.uone.bean.entity.business.fixed.FixedRfidEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <p>
 * 维保班组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
public interface IFixedMaintenanceTeamService extends IService<FixedMaintenanceTeamEntity> {
    IPage<FixedMaintenanceTeamEntity> page(Page page, FixedMaintenanceTeamEntity entity);
}
