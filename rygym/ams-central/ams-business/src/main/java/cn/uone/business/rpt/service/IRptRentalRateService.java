package cn.uone.business.rpt.service;

import cn.uone.bean.entity.business.report.vo.UnPayVo;
import cn.uone.bean.parameter.UnPayPo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
public interface IRptRentalRateService {

    Map<String,Object> getTotalSourceData(Map<String,Object> map);
    List<Map<String,Object>> getSourceDataByYear(Map<String,Object> map);
    List<Map<String,Object>> getRentDataByYear(Map<String,Object> map);
    List<Map<String,Object>> getSynthesizeDataByYear(Map<String,Object> map);
}
