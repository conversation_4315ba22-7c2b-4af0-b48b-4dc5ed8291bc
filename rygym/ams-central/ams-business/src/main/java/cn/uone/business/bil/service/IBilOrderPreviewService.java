package cn.uone.business.bil.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.uone.application.enumerate.contract.ReleaseTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemPreviewEntity;
import cn.uone.bean.entity.business.bil.BilOrderPreviewEntity;
import cn.uone.bean.entity.business.bil.PriceStrategyEntity;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractInfoEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 租金主账单预览表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
public interface IBilOrderPreviewService extends IService<BilOrderPreviewEntity> {
    List<BilOrderPreviewEntity> saveRentOrderForStaff(String contractId);
    List<BilOrderPreviewEntity> createRentOrderForStaff(List<BilOrderPreviewEntity> orderList);
    BilOrderPreviewEntity saveOrderPreview(String orderType,BigDecimal payment,Date orderStartDate,String contractId,String projectId,String sourceId,String payerId, boolean isFist,BilOrderItemPreviewEntity item);
    BilOrderPreviewEntity saveOrderPreview(String orderType,BigDecimal payment,Date orderStartDate,String contractId,String projectId,String sourceId,String payerId, boolean isFist,List<BilOrderItemPreviewEntity> itemList);
    BilOrderPreviewEntity createOrderPreview(String orderType,BigDecimal payment,Date orderStartDate,String contractId,String projectId,String sourceId,String payerId, boolean isFist,BilOrderItemPreviewEntity item);
    BilOrderPreviewEntity createOrderPreview(String orderType,BigDecimal payment,Date orderStartDate,String contractId,String projectId,String sourceId,String payerId, boolean isFist,List<BilOrderItemPreviewEntity> itemList);
    List<BilOrderPreviewEntity> saveRentOrderForStrategy(String contractId,String payType,long betweenMonths) throws Exception;
    List<BilOrderPreviewEntity> createRentOrderForStrategy(List<BilOrderPreviewEntity> orderList,List<PriceStrategyEntity> priceStrategyList) throws Exception;
    BilOrderPreviewEntity createDepositOrderForStrategy(BilOrderPreviewEntity depositPreview,String payType,List<BilOrderPreviewEntity> orderList,List<PriceStrategyEntity> priceStrategyList,boolean isSave,long betweenMonths);
    List<BilOrderPreviewEntity> saveRentOrderPreview(ContContractEntity contract) throws Exception;
    List<BilOrderPreviewEntity> createRentOrderPreview(Date startDate,Date endDate,String payType,BigDecimal price,String projectId,String sourceId,String platform) throws Exception;
}
