package cn.uone.business.flow.service;

import cn.uone.bean.entity.business.flow.SysDeployFormEntity;
import cn.uone.bean.entity.business.flow.SysFormEntity;
import cn.uone.business.flow.domain.SysDeployForm;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 流程实例关联表单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
public interface ISysDeployFormService extends IService<SysDeployFormEntity> {
    /**
     * 查询流程挂着的表单
     * @param deployId
     * @return
     */
    SysFormEntity selectSysDeployFormByDeployId(String deployId);

    /**
     * 新增流程实例关联表单
     *
     * @param sysDeployForm 流程实例关联表单
     * @return 结果
     */
    boolean insertSysDeployForm(SysDeployFormEntity sysDeployForm);
}
