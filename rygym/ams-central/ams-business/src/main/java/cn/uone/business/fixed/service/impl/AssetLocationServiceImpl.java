package cn.uone.business.fixed.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.fixed.AssetLocationEntity;
import cn.uone.business.fixed.dao.AssetLocationDao;
import cn.uone.business.fixed.service.IAssetLocationService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 固定资产位置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@Service
public class AssetLocationServiceImpl extends ServiceImpl<AssetLocationDao, AssetLocationEntity> implements IAssetLocationService {

    @Autowired
    AssetLocationDao assetLocationDao;

    @Override
    public IPage<AssetLocationEntity> page(Page page, AssetLocationEntity entity) {
        QueryWrapper<AssetLocationEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getLocationCode())) {
            queryWrapper.like("t_fixed_asset_location.location_code", entity.getLocationCode());
        }
        if (ObjectUtil.isNotEmpty(entity.getLocationName())) {
            queryWrapper.like("t_fixed_asset_location.location_name", entity.getLocationName());
        }
        queryWrapper.orderByDesc("t_fixed_asset_location.create_date");
        IPage iPage =  baseMapper.selectPage(page, queryWrapper);
        return iPage;
    }

    @Override
    public List<AssetLocationEntity> selectList(AssetLocationEntity entity) {
        QueryWrapper<AssetLocationEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getLocationCode())) {
            queryWrapper.like("t_fixed_asset_location.location_code", entity.getLocationCode());
        }
        if (ObjectUtil.isNotEmpty(entity.getLocationName())) {
            queryWrapper.like("t_fixed_asset_location.location_name", entity.getLocationName());
        }
        queryWrapper.orderByDesc("t_fixed_asset_location.create_date");
        List list =  baseMapper.selectList(queryWrapper);
        return list;
    }
}
