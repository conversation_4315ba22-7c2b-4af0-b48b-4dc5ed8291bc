package cn.uone.business.rpt.dao;

import cn.uone.bean.entity.business.rpt.vo.RptReVo;
import cn.uone.bean.entity.business.rpt.vo.RptReceivable;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueSearchVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 应收实收
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-16
 */
public interface RptReceivableDao extends BaseMapper<RptReceivable> {
    /**
     * 查询应收
     * @param page
     * @param vo
     * @return
     */
    List<RptReceivable> selectReceivable(Page page,@Param("searchVo")  RptRevenueSearchVo vo);

//    /**
//     * 查询实收
//     * @param page
//     * @param vo
//     * @return
//     */
//    List<RptReceivable> selectNetReceivable(Page page,@Param("searchVo")  RptRevenueSearchVo vo);

    /**
     * 获取 网络费 与 物业费
     */
    BigDecimal getCost(@Param("contractId") String contractId, @Param("costType") Integer costType);

    List<RptReVo> getReceipts(@Param("contractId") String contractId,@Param("year") String  year );


    List<RptReceivable> selectReceivable(@Param("searchVo")  RptRevenueSearchVo vo);
}
