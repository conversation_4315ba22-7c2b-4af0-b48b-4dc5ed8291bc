package cn.uone.business.res.service;

import cn.uone.bean.entity.business.res.ResProjectCompanyEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 项目公司表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
public interface IResProjectCompanyService extends IService<ResProjectCompanyEntity> {
    ResProjectCompanyEntity getCompanyBySourceId(String sourceId);
}
