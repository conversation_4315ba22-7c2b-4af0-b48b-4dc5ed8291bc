package cn.uone.business.cont.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.EducationEnum;
import cn.uone.application.enumerate.contract.IntentionHouseTypeEnum;
import cn.uone.application.enumerate.contract.JobTitleEnum;
import cn.uone.application.enumerate.contract.MarryEnum;
import cn.uone.bean.entity.business.cont.ContTalentCheckEntity;
import cn.uone.bean.entity.business.cont.vo.ContTalentCheckVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.business.cont.service.IContTalentCheckService;
import cn.uone.business.sys.service.ISysAreaService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.ExcelRender;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/cont/talentcheck")
public class ContTalentCheckController extends BaseController {

    @Autowired
    private IContTalentCheckService talentCheckService;

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private ISysMsgTemplateFegin sysMsgTemplateFegin;

    @Autowired
    private ISysAreaService sysAreaService;
    @Autowired
    private MinioUtil minioUtil;

    @UonePermissions(LoginType.CUSTOM)
    @RequestMapping("/apply")
    public RestResponse apply(ContTalentCheckEntity entity,String[] keepImgIds){
        String id = UoneSysUser.id();
        entity.setRentId(id);
        List<SysFileEntity> files = Lists.newArrayList();

        QueryWrapper wrapper = new QueryWrapper();
        String tel = entity.getTel();
        wrapper.eq("tel", tel);
        if(StrUtil.isNotBlank(entity.getId())){
            wrapper.ne("id", id);
        }
        wrapper.eq("state", "0");
        List<ContTalentCheckEntity> list = talentCheckService.list(wrapper);
        if(!CollectionUtils.isEmpty(list)){
            return RestResponse.failure("你所提交的用户已经在审核中，不能重复提交哦~");
        }
        wrapper.eq("state", "1");
        wrapper.eq("result", "1");
        list = talentCheckService.list(wrapper);
        if(!CollectionUtils.isEmpty(list)){
            return RestResponse.failure("你所提交的用户已经通过审核，不能重复提交哦");
        }
        //删除文件
        if (null != keepImgIds && keepImgIds.length > 0) {
            files = sysFileService.list(new QueryWrapper<SysFileEntity>().eq("from_id", entity.getId()).notIn("id",keepImgIds));
        }
        if (files != null) {
            for (SysFileEntity file : files) {
                //FileUtil.delete(file.getUrl());
                minioUtil.delete(file.getUrl());
                sysFileService.removeById(file.getId());
            }
        }
        entity.setApplyDate(new Date());
        entity.setState("0");
        entity.setResult("");
        entity.setReason("");
        boolean b = talentCheckService.saveOrUpdate(entity);
        if(b){
            return RestResponse.success().setData(entity);
        }else{
            return RestResponse.failure("申请失败");
        }
    }

    @UonePermissions(LoginType.CUSTOM)
    @RequestMapping("/delete")
    @Transactional
    public RestResponse delete(String id){
        boolean b = talentCheckService.removeById(id);
        //图片文件等的删除
        sysFileService.delFileByFromId(id);
        if(b){
            return RestResponse.success("删除成功");
        }else{
            return RestResponse.failure("删除失败");
        }
    }


    /**
     * 人才审核查询
     *
     * @param page
     * @param name
     * @param tel
     * @param state
     * @return
     */
    @RequestMapping("/queryPages")
    @UonePermissions(LoginType.USER)
    public RestResponse queryPages(Page page, @RequestParam(value = "name", required = false) String name,
                                   @RequestParam(value = "tel", required = false) String tel,
                                   @RequestParam(value = "state", required = false) String state,
                                   @RequestParam(value = "result", required = false) String result) {
        QueryWrapper wrapper = new QueryWrapper();
        if (StrUtil.isNotBlank(name)) {
            wrapper.like("name", name);
        }
        if (StrUtil.isNotBlank(tel)) {
            wrapper.like("tel", tel);
        }
        if (StrUtil.isNotBlank(state)) {
            wrapper.eq("state", state);
        }
        if(StrUtil.isNotBlank(result)){
            wrapper.eq("result", result);
        }
        wrapper.orderByDesc("apply_date");
        IPage<ContTalentCheckEntity> list = talentCheckService.page(page, wrapper);
        return RestResponse.success().setData(list);
    }

    /**
     * 人才审核查询
     *
     * @param page
     * @param state
     * @return
     */
    @RequestMapping("/queryPage")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse queryPage(Page page, @RequestParam(value = "state", required = false) String state,
                                   @RequestParam(value = "result", required = false) String result) {
        QueryWrapper<ContTalentCheckEntity> wrapper = new QueryWrapper<>();
        String id = UoneSysUser.id();
//        if(StrUtil.isNotBlank(id)){
            wrapper.eq("rent_id", id);
//        }
        if (StrUtil.isNotBlank(state)) {
            wrapper.eq("state", state);
        }
        if(StrUtil.isNotBlank(result)){
            wrapper.eq("result", result);
        }
        wrapper.orderByDesc("apply_date");
        IPage<ContTalentCheckEntity> list = talentCheckService.page(page, wrapper);
        return RestResponse.success().setData(list);
    }

    @UonePermissions(LoginType.ANON)
    @RequestMapping("/selectById")
    public RestResponse selectById(String id) {
        ContTalentCheckVo vo = talentCheckService.selectWithId(id);
        //如果学历字段为4（其他），那么页面需要显示otherText
        if (StrUtil.isNotBlank(vo.getEducation()) && vo.getEducation().split("_").length > 0) {
            if ("4".equals(vo.getEducation().split("_")[0])) {
                vo.setOtherText(vo.getEducation().split("_")[1]);
                vo.setEducation("4");
            }
        }
        //学历字段
//        vo.setEducation(EducationEnum.getNameByValue(vo.getEducation()));
//        vo.setMarriage(MarryEnum.getNameByValue(vo.getMarriage()));
//        vo.setTitle(JobTitleEnum.getNameByValue(vo.getTel()));

        // 查询身份证图片
        List<SysFileEntity> idCardImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.ID_CARD.getValue()));
        vo.setIdCardImgs(idCardImgs);
        // 查询婚育证明图片
        List<SysFileEntity> marryImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.MARRY.getValue()));
        vo.setMarryImgs(marryImgs);
        // 查询劳动合同图片
        List<SysFileEntity> laborImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.LABOR.getValue()));
        vo.setLaborImgs(laborImgs);
        // 查询社保证明图片
        List<SysFileEntity> socialImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.SOCIAL.getValue()));
        vo.setSocialImgs(socialImgs);
        // 查询户口本图片
        List<SysFileEntity> householdImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.HOUSEHOLD.getValue()));
        vo.setHouseholdImgs(householdImgs);
        // 查询学历证书图片
        List<SysFileEntity> eduImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.EDU.getValue()));
        vo.setEduImgs(eduImgs);
        // 查询无住房证明图片
        List<SysFileEntity> nohouseImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.NOHOUSE.getValue()));
        vo.setNoHouseImgs(nohouseImgs);
        //人才公寓意向登记表
        List<SysFileEntity> attentionImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.ATTENTION_FILE.getValue()));
        vo.setAttentionImgs(attentionImgs);
        return RestResponse.success().setData(vo);
    }

    @RequestMapping("/auditSuccess")
    public RestResponse auditSuccess(String id) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("id", id);
        ContTalentCheckEntity entity = talentCheckService.getOne(wrapper);
        entity.setState("1");//已审核
        entity.setResult("1");//通过
        talentCheckService.saveOrUpdate(entity);
        //对应的租客设置成人才

        return RestResponse.success();
    }

    /**
     * todo 该方法暂未被使用
     * @param id
     * @param reason
     * @param send
     * @param tel
     * @return
     * @throws Exception
     */
    @RequestMapping("/auditFail")
    public RestResponse auditFail(String id, String reason, String send, String tel) throws Exception {
        ContTalentCheckEntity entity = new ContTalentCheckEntity();
        entity.setId(id);
        entity.setState("1");//已审核
        entity.setResult("0");//未通过
        entity.setReason(reason);//不通过原因
        talentCheckService.saveOrUpdate(entity);
        if ("0".equals(send)) {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("mobile", tel);
            params.put("name", "XX公寓公众号");
            params.put("template_code", "198388"); // 模板code
            //该方法暂未被使用，先注释掉发送短信功能，使用短信需要结合是否saas收费模式
            //sysMsgTemplateFegin.send(params);
        }
        return RestResponse.success();
    }

    @RequestMapping("/export")
    public void export(HttpServletResponse response, String ids,
                       @RequestParam(value = "name", required = false) String name,
                       @RequestParam(value = "tel", required = false) String tel,
                       @RequestParam(value = "state", required = false) String state,
                       @RequestParam(value = "result", required = false) String result) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();

        Map<String, Object> query = Maps.newHashMap();
        if(StrUtil.isNotBlank(ids)){
            String[] split = ids.split(",");
            List<String> idList = new ArrayList<>(Arrays.asList(split));
            query.put("ids", idList);
        }
        query.put("name", name);
        query.put("tel", tel);
        query.put("state", state);
        query.put("result", result);
        List<ContTalentCheckVo>  list = talentCheckService.query(query);
        for (ContTalentCheckVo contTalentCheckVo : list) {

            //如果学历字段为4（其他），那么页面需要显示otherText
            String edu = "";
            if (StrUtil.isNotBlank(contTalentCheckVo.getEducation())) {
                String[] strings = contTalentCheckVo.getEducation().split("_");
                for (int i = 0; i < strings.length; i++) {
                    if(i==0){
                        edu = EducationEnum.getNameByValue(strings[0]);
                    }
                    if(i==1){
                        edu +="-" + strings[1];
                    }
                }
            }
            contTalentCheckVo.setSex("1".equals(contTalentCheckVo.getSex()) ? "男" : "女");
            contTalentCheckVo.setProvinceId(sysAreaService.getById(contTalentCheckVo.getProvinceId()).getAreaName());
            contTalentCheckVo.setCityId(sysAreaService.getById(contTalentCheckVo.getCityId()).getAreaName());
            if(ObjectUtil.isNotNull(sysAreaService.getById(contTalentCheckVo.getDistrictId()))){
                contTalentCheckVo.setDistrictId(sysAreaService.getById(contTalentCheckVo.getDistrictId()).getAreaName());
            }
            contTalentCheckVo.setMarriage(MarryEnum.getNameByValue(contTalentCheckVo.getMarriage()));
            contTalentCheckVo.setEducation(edu);
            contTalentCheckVo.setTitle(JobTitleEnum.getNameByValue(contTalentCheckVo.getTitle()));
            contTalentCheckVo.setHouseType(IntentionHouseTypeEnum.getNameByValue(contTalentCheckVo.getHouseType()));
            if("0".equals(contTalentCheckVo.getState())){
                contTalentCheckVo.setResult("待审核");
            }else{
                contTalentCheckVo.setResult("0".equals(contTalentCheckVo.getResult()) ? "不通过" : "通过");
            }
        }
        beans.put("talentList", list);
        ExcelRender.me("/excel/export/auditTalents.xlsx").beans(beans).render(response);
    }

}
