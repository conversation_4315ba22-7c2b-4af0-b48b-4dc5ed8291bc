package cn.uone.business.res.controller;


import cn.uone.business.res.service.IResSourceConfigureRecordService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-11
 */
@RestController
@RequestMapping("/configure/record")
public class ResSourceConfigureRecordController extends BaseController {

    @Autowired
    private IResSourceConfigureRecordService resSourceConfigureRecordService;

    @RequestMapping("pageList")
    public RestResponse pageList(Page page, @RequestParam("sourceId")String sourceId) {
        return RestResponse.success().setData(resSourceConfigureRecordService.pageList(page,sourceId));
    }
}
