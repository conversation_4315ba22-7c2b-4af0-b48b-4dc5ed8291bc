package cn.uone.business.common.controller;

import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.application.enumerate.source.SourceTypeEnum;
import cn.uone.bean.entity.business.report.vo.DepositStatisticsVo;
import cn.uone.bean.entity.business.report.vo.PutAccountVo;
import cn.uone.bean.entity.business.res.vo.ResSourceSearchVo;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.rpt.RptContAccountEntity;
import cn.uone.bean.entity.business.rpt.RptOperateInfoEntity;
import cn.uone.bean.entity.business.rpt.RptRentAccountEntity;
import cn.uone.bean.entity.business.sale.vo.CustomerVo;
import cn.uone.bean.parameter.*;
import cn.uone.business.cont.dao.ContContractInfoDao;
import cn.uone.business.cont.service.IContContractInfoService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.rpt.dao.ReportDepositDao;
import cn.uone.business.rpt.dao.RptOperateInfoDao;
import cn.uone.business.rpt.service.IReportPutAccountService;
import cn.uone.business.rpt.service.IRptContAccountService;
import cn.uone.business.rpt.service.IRptRentAccountService;
import cn.uone.business.sale.service.ISaleCustomerService;
import cn.uone.business.sys.dao.SysFileDao;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.ExcelRender;
import cn.uone.web.base.BusinessException;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Controller
@Slf4j
public class ExportController {

    @Autowired
    ISaleCustomerService customerService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IReportPutAccountService putAccountService;

    @Autowired
    private IRptRentAccountService rentAccountService;

    @Autowired
    private IRptContAccountService contAccountService;

    @Resource
    private ReportDepositDao reportDepositDao;

    @Resource
    private ContContractInfoDao contractInfoDao;

    @Resource
    private SysFileDao sysFileDao;

    @Resource
    private RptOperateInfoDao operateInfoDao;

    @Autowired
    private IContContractInfoService contContractInfoService;

    @RequestMapping(value = {"/admin/custom/export"}, method = RequestMethod.POST)
    public void export(HttpServletResponse response, CustomerSearchPo customer) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        List<CustomerVo> list = customerService.selectPageByMap(customer);
        beans.put("customers", list);
        ExcelRender.me("/excel/export/saleCustomer.xlsx").beans(beans).render(response);
    }

    @RequestMapping(value = {"/admin/report/putAccount/export"}, method = RequestMethod.POST)
    public void putAccountExport(HttpServletResponse response, PutAccountPo param) throws BusinessException {
        List<PutAccountVo> list = putAccountService.export(param);
        Map<String, Object> beans = Maps.newHashMap();
        beans.put("vo", list);
        ExcelRender.me("/excel/export/putAccount.xlsx").beans(beans).render(response);
    }

    @RequestMapping(value = {"/admin/report/rentAccount/export"}, method = RequestMethod.POST)
    public void rentAccountExport(HttpServletResponse response, PutAccountPo param) throws BusinessException {

        List<RptRentAccountEntity> list =  rentAccountService.export(param);
        Map<String, Object> beans = Maps.newHashMap();
        beans.put("vo", list);
        ExcelRender.me("/excel/export/rentAccount.xlsx").beans(beans).render(response);
    }

    @RequestMapping(value = {"/admin/report/contAccount/export"}, method = RequestMethod.POST)
    public void contAccountExport(HttpServletResponse response, PutAccountPo param) throws BusinessException {

        List<RptContAccountEntity> list =  contAccountService.export(param);
        Map<String, Object> beans = Maps.newHashMap();
        beans.put("vo", list);
        ExcelRender.me("/excel/export/contAccount.xlsx").beans(beans).render(response);
    }

    @RequestMapping(value = {"/admin/report/deposit/export"}, method = RequestMethod.POST)
    public void depositExport(HttpServletResponse response, ReportDepositPo param) throws BusinessException {
        DataScope scope = DataScope.newDataScope(UoneSysUser.id());
        scope.setProAlias("cs");
        scope.setParAlias("cs");
        List<DepositStatisticsVo> list = reportDepositDao.selectPage(scope,param);
        Map<String, Object> beans = Maps.newHashMap();
        beans.put("vo", list);
        ExcelRender.me("/excel/export/deposit.xlsx").beans(beans).render(response);
    }

    @RequestMapping("/admin/source/export")
    public void sourceReportExport(HttpServletResponse response, ResSourceSearchVo sourceSearchVo) throws BusinessException {
       Map<String, Object> beans = Maps.newHashMap();
        sourceSearchVo.setProjectId(UoneHeaderUtil.getProjectId());
       List<ResSourceVo> list=resSourceService.report(sourceSearchVo);
       for(ResSourceVo vo:list){
           vo.setSourceType(SourceTypeEnum.getNameByValue(vo.getSourceType()));
           vo.setState(SourceStateEnum.getNameByValue(vo.getState()));
       }
       beans.put("sources", list);
        ExcelRender.me("/excel/export/source.xlsx").beans(beans).render(response);
    }

    @RequestMapping(value = {"/admin/record/export2"}, method = RequestMethod.POST)
    public void rentAccountExport(HttpServletResponse response, RecordManagePo po) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        po.setProjectId(UoneHeaderUtil.getProjectId());
        List<RecordManageVo> list=contContractInfoService.selectRecord(po);
        for(RecordManageVo vo:list){
            if(BaseConstants.BOOLEAN_OF_FALSE.equals(vo.getIsOrganize())){
                vo.setIsOrganize("个人");
            }else{
                vo.setIsOrganize("机构");
            }
        }
        beans.put("vo", list);
        ExcelRender.me("/excel/export/record2.xlsx").beans(beans).render(response);
    }

    @RequestMapping(value = {"/admin/report/operate/export"}, method = RequestMethod.POST)
    public void operateInfoExport(HttpServletResponse response,String sourceType,  @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate) throws BusinessException {
        QueryWrapper<RptOperateInfoEntity> wrapper= new QueryWrapper<RptOperateInfoEntity>().eq("source_type",sourceType).eq("project_id",UoneHeaderUtil.getProjectId());
        if(StrUtil.isNotBlank(startDate)){
            wrapper.ge("date(report_date)",startDate);
        }
        if(StrUtil.isNotBlank(endDate)){
            wrapper.le("date(report_date)",endDate);
        }
        Map<String, Object> beans = Maps.newHashMap();
        List<RptOperateInfoEntity> list= operateInfoDao.selectList(wrapper);
        beans.put("vo", list);
        ExcelRender.me("/excel/export/operateInfo.xlsx").beans(beans).render(response);
    }

}
