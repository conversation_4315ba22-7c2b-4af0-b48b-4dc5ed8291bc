package cn.uone.business.rpt.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface RptBusinessBoardDao {

    Map<String,Object> getTotalSourceData(@Param("map") Map<String,Object> map);
    Map<String,Object> getSignNumData(@Param("map") Map<String,Object> map);
    Map<String,Object> getPayNumData(@Param("map") Map<String,Object> map);
    Map<String,Object> getTotalPaymentData(@Param("map") Map<String,Object> map);
    List<Map<String,Object>> getPaymentListData(@Param("map") Map<String,Object> map);
}
