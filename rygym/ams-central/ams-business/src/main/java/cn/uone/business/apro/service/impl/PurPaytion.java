package cn.uone.business.apro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.application.enumerate.ApprovalTemplateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalProjectParaEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.business.pur.PurPurchaseEntity;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalProjectParaService;
import cn.uone.business.apro.service.Operation;
import cn.uone.business.pur.service.IPurPurchaseService;
import cn.uone.util.wechat.ApprovalStateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PurPaytion implements Operation {

    @Resource
    private IPurPurchaseService purPurchaseService;
    @Resource
    private IApprovalCommitService approvalCommitService;
    @Resource
    private IApprovalProjectParaService projectParaService;

    @Override
    public ApprovalCommitEntity apply(Expression expression) {
        PurPurchaseEntity entity=purPurchaseService.getById(expression.getCodeId());
        ApprovalProjectParaEntity temp=projectParaService.get(entity.getProjectId(),ApprovalTemplateEnum.ORDERPAY.getType(),true);
        ApprovalCommitEntity commitEntity=null;
        if(ObjectUtil.isNotNull(entity)){
            commitEntity=new ApprovalCommitEntity();
            ApprovalStateUtil.initCommit(commitEntity,expression.getCodeId());
            commitEntity.setPrice(expression.getPrice());
            commitEntity.setApprovalNum(entity.getCode());
            commitEntity.setTemplateid(temp.getApprovalTempId());
            commitEntity.setType(ApprovalTypeEnum.ORDERPAY.getValue());
            commitEntity.setTableName(ApprovalTypeEnum.ORDERPAY.getTable());
            commitEntity.setTitle(ApprovalTypeEnum.ORDERPAY.getName());
            commitEntity.setTitle1("采购订单编号:"+entity.getCode());
            commitEntity.setTitle2("合计金额:" + entity.getPrice() + "元");
            commitEntity.setTitle3("本次申请付款金额:" + expression.getPrice() + "元");
            approvalCommitService.save(commitEntity);
        }
        return commitEntity;
    }


}
