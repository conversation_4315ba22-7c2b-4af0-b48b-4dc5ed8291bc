package cn.uone.business.rpt.controller;


import cn.uone.bean.entity.business.res.vo.ResSourceSearchVo;
import cn.uone.business.cont.dao.ContCheckInUserDao;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 房源报表
 */
@RestController
@RequestMapping("/report/source")
public class ReportSourceController extends BaseController {

    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private ContCheckInUserDao checkInUserDao;

    @GetMapping("/report")
    public RestResponse report(Page page, ResSourceSearchVo sourceSearchVo) {
        sourceSearchVo.setProjectId(UoneHeaderUtil.getProjectId());
        return RestResponse.success().setData(resSourceService.report(page, sourceSearchVo));
    }

    @RequestMapping("/getSourceInfo")
    public RestResponse getSourceInfo(@RequestParam String sourceId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("sourceId", sourceId);
        return RestResponse.success().setData(resSourceService.pageList(map));
    }

    @RequestMapping("/getSourceChein")
    public RestResponse getSourceChein(Page page,@RequestParam String sourceId) {
        return RestResponse.success().setData(checkInUserDao.getSourceChein(page,sourceId));
    }

    @RequestMapping("/sourceAllContract")
    public RestResponse sourceAllContract(Page page,@RequestParam String sourceId) {
        return RestResponse.success().setData(checkInUserDao.allContract(page,sourceId));
    }


}
