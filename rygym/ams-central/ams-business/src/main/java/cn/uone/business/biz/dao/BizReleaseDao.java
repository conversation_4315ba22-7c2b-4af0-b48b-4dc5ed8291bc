package cn.uone.business.biz.dao;

import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.biz.vo.BizReleaseVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-03
 */
@Repository
public interface BizReleaseDao extends BaseMapper<BizReleaseEntity> {

    IPage<BizReleaseVo> selectBizReleaseByMap(Page page, @Param("map") Map<String, Object> map, DataScope dataScope);

    List<BizReleaseEntity> queryInSettle(@Param("map")Map<String, Object> par);

    IPage<BizReleaseVo> selectCheckHouseByMap(Page page,@Param("map") Map<String,Object> map, DataScope dataScope);
}
