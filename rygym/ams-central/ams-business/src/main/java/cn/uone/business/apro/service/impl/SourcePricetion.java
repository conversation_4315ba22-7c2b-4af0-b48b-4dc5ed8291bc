package cn.uone.business.apro.service.impl;

import cn.uone.application.enumerate.ApprovalTemplateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalProjectParaEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.business.res.ResSourceConfigureEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalProjectParaService;
import cn.uone.business.apro.service.Operation;
import cn.uone.business.res.service.IResSourceConfigureService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.util.wechat.ApprovalStateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *  房源改价
 */
@Service
public class SourcePricetion implements Operation {

    @Resource
    private IResSourceService sourceService ;
    @Resource
    private IApprovalCommitService approvalCommitService ;
    @Resource
    private IResSourceConfigureService configureService;
    @Resource
    private IApprovalProjectParaService projectParaService;

    @Override
    public ApprovalCommitEntity apply(Expression expression) {
        ResSourceConfigureEntity entity=configureService.getById(expression.getCodeId());
        ResSourceVo source = sourceService.cSourceDetail(entity.getSourceId());
        ApprovalCommitEntity commitEntity=new ApprovalCommitEntity();
        ApprovalStateUtil.initCommit(commitEntity,expression.getCodeId());
        commitEntity.setSummary(expression.getMemo());
        //期望表价
        commitEntity.setPrice(expression.getPrice());
        commitEntity.setDeposit(expression.getDeposit());
        //当前表价、定金
        commitEntity.setNowPrice(entity.getPrice());
        commitEntity.setNowDeposit(entity.getDeposit());
        commitEntity.setNowLowPrice(entity.getLowPrice());
        ApprovalProjectParaEntity temp =null;
        if(expression.getPrice().compareTo(entity.getLowPrice())<0){
            temp = projectParaService.get(source.getProjectId(),ApprovalTemplateEnum.SOURCEPRICELD.getType(),true);
        }else if(expression.getPrice().compareTo(entity.getPrice())>0){
            temp = projectParaService.get(source.getProjectId(),ApprovalTemplateEnum.SOURCEPRICEHY.getType(),true);
        }else{
            temp = projectParaService.get(source.getProjectId(),ApprovalTemplateEnum.SOURCEPRICELY.getType(),true);
        }
        commitEntity.setTemplateid(temp.getApprovalTempId());
        commitEntity.setType(ApprovalTypeEnum.SOURCEPRICE.getValue());
        commitEntity.setTableName(ApprovalTypeEnum.SOURCEPRICE.getTable());
        commitEntity.setTitle(ApprovalTypeEnum.SOURCEPRICE.getName());
        commitEntity.setTitle1("房源地址:"+ source.getSourceName());
        commitEntity.setTitle2("备注内容:"+ expression.getMemo());
        commitEntity.setTitle3(" ");
        approvalCommitService.save(commitEntity);
        return commitEntity;
    }


}
