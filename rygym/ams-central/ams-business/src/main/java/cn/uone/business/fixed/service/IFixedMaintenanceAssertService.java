package cn.uone.business.fixed.service;

import cn.uone.bean.entity.business.fixed.FixedMaintenanceAssertEntity;
import cn.uone.bean.entity.business.fixed.vo.FixedMaintenanceAssertVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 维保耗材表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
public interface IFixedMaintenanceAssertService extends IService<FixedMaintenanceAssertEntity> {
    IPage<FixedMaintenanceAssertVo> page(Page page, Map<String,Object> map);
    void deleteByTaskId(String taskId);
}
