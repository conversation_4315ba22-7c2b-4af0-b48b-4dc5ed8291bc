package cn.uone.business.fixed.dao;

import cn.uone.bean.entity.business.fixed.FixedRfidBatchEntity;
import cn.uone.bean.entity.business.fixed.FixedStorageEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 固定资产入库表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
public interface FixedStorageDao extends BaseMapper<FixedStorageEntity> {


    IPage<FixedStorageEntity> getListByPage(Page page, FixedStorageEntity entity);

    FixedStorageEntity getById(String id);

}
