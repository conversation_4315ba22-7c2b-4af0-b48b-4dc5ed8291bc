package cn.uone.business.res.controller;



import cn.uone.bean.entity.business.res.ResHouseTypeHotEntity;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeHotVo;
import cn.uone.business.res.service.IResHouseTypeHotService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */

@RestController
@RequestMapping("/house-type-hot")
public class ResHouseTypeHotController extends BaseController{


    @Autowired
    private IResHouseTypeHotService houseTypeHotService;


    /*
      热门户型添加或修改
     */
    @RequestMapping("/saveOrUpdate")
    public RestResponse saveOrUpdate(ResHouseTypeHotEntity hotEntity){
        if(hotEntity.insertOrUpdate()){
            return RestResponse.success().setData("操作成功");
        }
        return RestResponse.failure("失败");
    }

    /*
       热门户型删除
    */
    @RequestMapping("/deleteHot")
    public RestResponse deleteHot(@RequestParam(value = "id", required = true) String id){
        ResHouseTypeHotEntity entity = houseTypeHotService.getById(id);
        if (entity == null) {
            return RestResponse.failure("该项目不存在");
        }
        if(entity.deleteById()){
            return RestResponse.success().setData("操作成功");
        };
        return RestResponse.failure("失败");
    }

    /*
     *热门户型列表
     */
    @RequestMapping("/hotTypePage")
    public RestResponse hotTypePage(Page page) {
        Map<String,Object> map = new HashMap<>();
        map.put("projectId", UoneHeaderUtil.getProjectId());
        IPage<ResHouseTypeHotVo> pages = houseTypeHotService.hotTypePage(page,map);
        return RestResponse.success().setData(pages);
    }

    /*
     *热门户型展示
     */
    @UonePermissions
    @RequestMapping("/hotTypes")
    public RestResponse hotTypes(@RequestParam(required = false) String projectId) {
        Map<String,Object> map=new HashMap<>();
        map.put("projectId",projectId);
        List<ResHouseTypeHotVo> hotTypes=houseTypeHotService.hotTypes(map);
        return RestResponse.success().setData(hotTypes);
    }

    @RequestMapping("/toState")
    public RestResponse toState(@RequestParam(value = "id", required = true) String id) {
        ResHouseTypeHotEntity entity = houseTypeHotService.getById(id);
        QueryWrapper<ResHouseTypeHotEntity> wrapper=new QueryWrapper<>();
        wrapper.eq("state","1");
        if (entity == null) {
            return RestResponse.failure("该项目不存在");
        }
        /*if(houseTypeHotService.count(wrapper)==6){
            return RestResponse.failure("展示数已达到6条，无法再增加");
        }*/
        entity.setState("1");
        houseTypeHotService.saveOrUpdate(entity);
        return RestResponse.success();
    }

    @RequestMapping(value = "/cancelState", method = RequestMethod.POST)
    public RestResponse cancelState(@RequestParam(value = "id", required = true) String id) {
        ResHouseTypeHotEntity entity = houseTypeHotService.getById(id);
        if (entity == null) {
            return RestResponse.failure("该项目不存在");
        }
        entity.setState("0");
        houseTypeHotService.saveOrUpdate(entity);
        return RestResponse.success();
    }
}
