package cn.uone.business.dev.dao;

import cn.uone.bean.entity.business.dev.DevSupplierEntity;
import cn.uone.bean.entity.business.dev.vo.DevSupplierEntityVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface DevSupplierDao extends BaseMapper<DevSupplierEntity> {

    IPage<DevSupplierEntityVo> queryDevSupplierPage(Page page, @Param("map") Map map);
}
