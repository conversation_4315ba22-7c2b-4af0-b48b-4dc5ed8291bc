package cn.uone.business.cont.task;

import cn.uone.bean.entity.business.cont.ContContractEntity;
import org.springframework.context.ApplicationEvent;

public class SaveRentOrderTransactionEvent extends ApplicationEvent {

    private ContContractEntity contract;

    private String platform;

    private String createRentType;

    private boolean isManualAdd;//是否合同录入


    public SaveRentOrderTransactionEvent(Object source, ContContractEntity contract, String platform,String createRentType,boolean isManualAdd) {
        super(source);
        this.contract = contract;
        this.platform = platform;
        this.createRentType = createRentType;
        this.isManualAdd = isManualAdd;
    }

    public ContContractEntity getContract() {
        return contract;
    }

    public String getPlatform() {
        return platform;
    }

    public String getCreateRentType() {
        return createRentType;
    }

    public boolean getIsManualAdd() {
        return isManualAdd;
    }
}
