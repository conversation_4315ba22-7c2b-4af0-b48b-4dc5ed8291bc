package cn.uone.business.cosmic.dao;

import cn.uone.bean.entity.business.cosmic.CosmicLogEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 金蝶接口日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface CosmicLogDao extends BaseMapper<CosmicLogEntity> {


    Integer getRetryFrequency(String dataId,String type);

    CosmicLogEntity getLastOne(String dataId,String type);

    List<CosmicLogEntity> getByIds(List dataIds, String type);
}
