package cn.uone.business.apro.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalDetailEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.business.apro.dao.ApprovalDetailDao;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalDetailService;
import cn.uone.util.wechat.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 * 审批节点信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-09
 */
@Service
public class ApprovalDetailServiceImpl extends ServiceImpl<ApprovalDetailDao, ApprovalDetailEntity> implements IApprovalDetailService {

    @Autowired
    @Lazy
    private IApprovalCommitService approvalCommitService;

    @Override
    public List<ApprovalDetailEntity> getApprovalInfo(ApprovalCommitEntity entity,String id, String type) {
        if (ObjectUtil.isNull(entity) || StrUtil.isBlank(entity.getId())) {
            entity = approvalCommitService.getByFun(id,type);
        }
            List<ApprovalDetailEntity> approval= baseMapper.selectList(new QueryWrapper<ApprovalDetailEntity>().eq("code",entity.getCode()).orderByAsc("sort"));
        for(ApprovalDetailEntity a:approval){
            a.setStatusText(ApprovalStateEnum.getNameByValue(a.getStatus()));
        }
        return approval;
    }

    @Override
    public BilOrderVo approvalOrderConfirmInfo(String orderid) {
        return baseMapper.approvalOrderConfirmInfo(orderid);
    }

    @Override
    public void saveDetail(ApprovalInfo approvalInfo) {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ApprovalNodes ApprovalNodes = approvalInfo.getApprovalNodes();
        baseMapper.delete(new QueryWrapper<ApprovalDetailEntity>().eq("code", approvalInfo.getThirdNo()));
        List<ApprovalNode> no = ApprovalNodes.getApprovalNode();
        LinkedList<ApprovalDetailEntity> linkedList= Lists.newLinkedList();
        for(ApprovalNode node:no){
            Items items = node.getItems();
            List<Item> item=items.getItem();
            //转审状态（子节点）
            for(Item it:item){
                if(ApprovalStateEnum.CANCEL.getValue().equals(it.getItemStatus())){
                    continue;
                }
                ApprovalDetailEntity entity = new ApprovalDetailEntity();
                //会签（所有成员统一节点）
                entity.setName(it.getItemName());
                if(it.getItemOpTime()!=0){
                    Date d = new Date(it.getItemOpTime()*1000);
                    entity.setOptime(sd.format(d));
                }
                entity.setImage(it.getItemImage());
                entity.setUserid(it.getItemUserId());
                entity.setParty(it.getItemParty());
                entity.setCode(approvalInfo.getThirdNo());
                entity.setSpeech(it.getItemSpeech());
                entity.setStatus(it.getItemStatus());
                linkedList.add(entity);
            }
            //1-审批中；2-已同意；3-已驳回；
            if(ApprovalStateEnum.APPROVAL.getValue().equals(node.getNodeStatus()) || ApprovalStateEnum.REJECT.getValue().equals(node.getNodeStatus())){
                break;
            }
        }
        if(CollectionUtil.isNotEmpty(linkedList)){
            for(int i=0;i<linkedList.size();i++){
                baseMapper.insert(linkedList.get(i).setSort((i+1)+""));
            }
        }
    }
}
