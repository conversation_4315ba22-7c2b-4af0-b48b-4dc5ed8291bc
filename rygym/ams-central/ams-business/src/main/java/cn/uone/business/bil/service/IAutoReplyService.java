package cn.uone.business.bil.service;

import cn.uone.bean.entity.business.bil.AutoReplyEntity;
import cn.uone.bean.entity.business.bil.vo.AutoReplyVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
public interface IAutoReplyService extends IService<AutoReplyEntity> {

    AutoReplyEntity getEntityByKeyWord(String keyword);

    IPage<AutoReplyEntity> findByCondition(Page page, AutoReplyVo autoReplyVo);
}
