package cn.uone.business.fixed.controller;


import cn.uone.bean.entity.business.fixed.FixedMaintenanceTeamEntity;
import cn.uone.business.fixed.service.IFixedMaintenanceTeamService;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;

import java.util.List;

/**
 * <p>
 * 维保班组表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@RestController
@RequestMapping("/fixed/fixed-maintenance-team-entity")
public class FixedMaintenanceTeamController extends BaseController {
    @Autowired
    IFixedMaintenanceTeamService fixedMaintenanceTeamService;
    /**
     * 获取信息
     *
     * @param fixedMaintenanceTeamEntity
     * @return 维保班组列表 分页
     */
    @GetMapping("/page")
    public RestResponse page(Page page, FixedMaintenanceTeamEntity fixedMaintenanceTeamEntity) {
        IPage<FixedMaintenanceTeamEntity> iPage = fixedMaintenanceTeamService.page(page, fixedMaintenanceTeamEntity);

        return RestResponse.success().setData(iPage);
    }

    /**
     * 获取信息
     *
     * @param id 主键
     * @return 维保班组信息
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        FixedMaintenanceTeamEntity info = fixedMaintenanceTeamService.getById(id);
        return RestResponse.success().setData(info);
    }

    /**
     * 新增
     *
     * @param fixedMaintenanceTeamEntity 参数
     * @return
     */
    @PostMapping("/save")
    public RestResponse save(FixedMaintenanceTeamEntity fixedMaintenanceTeamEntity) {
        if(fixedMaintenanceTeamService.save(fixedMaintenanceTeamEntity)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    /**
     * 修改
     *
     * @param fixedMaintenanceTeamEntity 参数
     * @return
     */
    @PostMapping("/edit")
    public RestResponse edit(FixedMaintenanceTeamEntity fixedMaintenanceTeamEntity) {
        if(fixedMaintenanceTeamService.updateById(fixedMaintenanceTeamEntity)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        if(fixedMaintenanceTeamService.removeByIds(ids)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 获取信息
     *
     * @return 班组信息列表
     */
    @PostMapping("/getList")
    public RestResponse getList() {
        return RestResponse.success().setData(fixedMaintenanceTeamService.list());
    }
}
