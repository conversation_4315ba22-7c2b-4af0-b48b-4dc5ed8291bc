package cn.uone.business.dev.controller;


import cn.uone.bean.entity.business.dev.DevSupplierClassEntity;
import cn.uone.bean.entity.business.dev.DevSupplierClassRelEntity;
import cn.uone.business.dev.service.IDevSupplierClassRelService;
import cn.uone.business.dev.service.IDevSupplierClassService;
import cn.uone.business.dev.service.IDevSupplierService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Api(value="供应商类型服务",tags={"供应商类型操作"})
@RestController
@RequestMapping("/dev-supplier-class-entity")
public class DevSupplierClassController extends BaseController {

    @Autowired
    private IDevSupplierClassService devSupplierClassService;
    @Autowired
    private IDevSupplierClassRelService devSupplierClassRelService;
    @Autowired
    private IDevSupplierService devSupplierService;

    @RequestMapping("/addOrUpdateDevSupplierClass")
    public RestResponse addOrUpdateDevSupplierClass(@RequestBody DevSupplierClassEntity devSupplierClassEntity){
        if (devSupplierClassEntity==null){
            return RestResponse.failure("请求对象不能为空");
        }
        if (StringUtils.isBlank(devSupplierClassEntity.getParentId())){
            return RestResponse.failure("请选择所属系统");
        }
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("name",devSupplierClassEntity.getName());
        DevSupplierClassEntity checkObj=devSupplierClassService.getOne(queryWrapper);
        if (StringUtils.isBlank(devSupplierClassEntity.getId())){//添加
            if (checkObj!=null){
                return RestResponse.failure("该类别名称已存在");
            }
        }else{//修改
            if (checkObj!=null && !checkObj.getId().equals(devSupplierClassEntity.getId())){
                return RestResponse.failure("该类别名称已存在");
            }
        }
        devSupplierClassService.saveOrUpdate(devSupplierClassEntity);
        return  RestResponse.success("提交成功");
    }

    @RequestMapping("/queryDevSupplierClass")
    @RequiresPermissions("supplierLibrary:show")
    public RestResponse queryDevSupplierClass(@RequestParam(value = "type",required = false)String type,@RequestParam(value = "id",required = false)String id){//type 标志查询方式:空查询全表，1查询父类
        QueryWrapper<DevSupplierClassEntity> queryWrapper=new QueryWrapper();
        if (StringUtils.isNotBlank(type)){
            if (type.equals("1")){//查询父菜单
                queryWrapper.and(i-> i.isNull("parent_id").or().eq("parent_id",""));
            }else if (type.equals("2")){//查询父带子的菜单
                if (StringUtils.isNotBlank(id)){
                    queryWrapper.and(i->i.eq("id",id).or().eq("parent_id",id));
                }
            }else if (type.equals("3")){//查询子菜单
                if (StringUtils.isNotBlank(id)) {
                    queryWrapper.eq("parent_id", id);
                } else {
                    queryWrapper.isNotNull("parent_id");
                }
            }
        }
       List<DevSupplierClassEntity> list=devSupplierClassService.list(queryWrapper);
       return RestResponse.success().setData(list);
    }

    @RequestMapping("/delDevSupplierClass")
    public RestResponse delDevSupplierClass(@RequestParam(value = "id",required = false)String id){
        Map map=Maps.newHashMap();
        map.put("supplier_class_id",id);
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("supplier_class_id",id);
        List<DevSupplierClassRelEntity> list=devSupplierClassRelService.list(queryWrapper);
        List tmpList=new ArrayList();
        if (CollectionUtils.isNotEmpty(list)){
            for (int i=0;i<list.size();i++){
                tmpList.add(list.get(i).getSupplierId());
            }
            int result=devSupplierService.querySupplierUse(tmpList);
            if (result!=0){
                return RestResponse.failure("该分类下的供应商有被使用不能删除");
            }
        }

        devSupplierClassRelService.removeByMap(map);//向删除供应商类型关系表
        if (CollectionUtils.isNotEmpty(tmpList)){
            devSupplierService.removeByIds(tmpList);
        }
        devSupplierClassService.removeById(id);
        return RestResponse.success("删除成功");
    }
}
