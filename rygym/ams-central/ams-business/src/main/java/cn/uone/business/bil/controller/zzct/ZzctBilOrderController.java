package cn.uone.business.bil.controller.zzct;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.*;
import cn.uone.application.enumerate.order.*;
import cn.uone.bean.entity.business.bil.*;
import cn.uone.bean.entity.business.bil.vo.*;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.business.bil.service.*;
import cn.uone.business.cont.service.*;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.fegin.crm.*;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.util.AddressMergerUtil;
import cn.uone.util.MinioUtil;
import cn.uone.util.PdfUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * 从漳州城投工程迁移过来
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 * caizhanghe edit 2024-05-29
 */
@RestController
@RequestMapping("/zzct/bil/order")
@Slf4j
public class ZzctBilOrderController extends BaseController {

    @Autowired
    private IBilOrderService bilOrderService;

    @Autowired
    private IBilOrderItemService bilOrderItemService;

    @Autowired
    private IContContractSourceRelService contContractSourceRelService;

    @Autowired
    private IResSourceService resSourceService;

    @Autowired
    private IRenterFegin renterFegin;

    @Autowired
    private ISysFileService sysFileService;

    @Resource
    private IResProjectParaService projectParaService;

    @Autowired
    private ISysPushMsgService sysPushMsgService;

    @Autowired
    private IResProjectParaService resProjectParaService;

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    private IZzctSysMsgTemplateFegin zzctSysMsgTemplateFegin;

    @Autowired
    private IZzctBilOrderService zzctBilOrderService;

    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    private ISysCompanyFegin sysCompanyFegin;
    @Autowired
    private PdfUtil pdfUtil;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    private AddressMergerUtil addressMergerUtil;

    /**
     * 分页查询
     * 漳州城投项目账单管理页面统计增加实付金额统计
     * caizhanghe edit 2024-07-03
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndCreateDate())) {
            bilOrderSearchVo.setEndCreateDate(DateUtil.endOfDay(bilOrderSearchVo.getEndCreateDate()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayTime())) {
            bilOrderSearchVo.setEndPayTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPushTime())) {
            bilOrderSearchVo.setEndPushTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPushTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayableTime())) {
            bilOrderSearchVo.setEndPayableTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayableTime()));
        }
        bilOrderSearchVo.setCarKeyWord(true);
        bilOrderSearchVo.setIsZzct("zzct");
        //BigDecimal total=bilOrderService.getTotal(bilOrderSearchVo);
        //BigDecimal total=bilOrderService.countPayment(bilOrderSearchVo);
        //total = total == null?BigDecimal.ZERO:total;
        //获取账单金额统计
        BilOrderStatisticsVo bilOrderStatisticsVo = zzctBilOrderService.getStatisticsVoByMap(bilOrderSearchVo);
        IPage<BilOrderVo> data=bilOrderService.findByCondition(page, bilOrderSearchVo);
        List<BilOrderVo> dataList = data.getRecords();
        Map<String,Object> map = Maps.newHashMap();
        for(BilOrderVo bilOrderVo:dataList){
            String sourceIds = bilOrderVo.getSourceIds();
            if(StringUtils.isNotBlank(sourceIds)){//不为空时进入组装
                map.put("sourceIds",sourceIds);
                ResSourceVo resSourceVo = resSourceService.getMultiSourceName(map);//获取多房源的房源坐落
                bilOrderVo.setAddress(resSourceVo.getSourceName());
                bilOrderVo.setPartitionName(resSourceVo.getPartitionName());
                bilOrderVo.setArea(resSourceVo.getArea());
            }
        }
        ObjectMapper objectMapper = new ObjectMapper();
        String bilOrderStatisticsStr = "";
        try {
            bilOrderStatisticsStr = objectMapper.writeValueAsString(bilOrderStatisticsVo);//将对象转json字符串,方便传到前端
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return response.setSuccess(true).setData(data).setMessage(bilOrderStatisticsStr);//前端页面显示总金额，试过和setAny()中，layui识别不到，只能放在message中便于传递
        //return response.setSuccess(true).setData(data).setMessage(total.toPlainString());//前端页面显示总金额，试过和setAny()中，layui识别不到，只能放在message中便于传递
    }

    /**
     * 催付
     */
    @RequestMapping("/urge")
    public RestResponse urge(String id,String type) {
        RestResponse response = new RestResponse();
        try {
            BilOrderEntity entity = bilOrderService.getById(id);
            RenterEntity renter = renterFegin.getById(entity.getPayerId());
            Map<String,Object> maps = Maps.newHashMap();
            maps.put("id",entity.getSourceId());
            ResSourceVo resEntity = resSourceService.getSourceInfoByComun(maps);
//            if(StringUtils.isBlank(renter.getOpenid())){
//                return response.setSuccess(false).setMessage("该租客未绑定公众号");
//            }else{
                sendUrgeMsg(renter,entity.getPayment().toString() ,resEntity,type);
//            }
            // 简单邮件的发送
            //MailUtil e = new MailUtil ();
            //e.SendSimpleEmail("账单催付通知书","尊敬的承租户:"+renter.getName()+"您好！您本期账单已经生成，请及时前往小程序-》个人中心—》我的账单模块完成账单支付，以免造成您的信用问题。感谢支持","<EMAIL>");
            renter.setTime(renter.getTime() + 1 );
            renter.setUrgeTime(new Date());
            renterFegin.update(renter);
            //保存账单表的催付次数
            Integer count = entity.getDemandPaymentCount()==null?0:entity.getDemandPaymentCount();
            entity.setDemandPaymentCount(count+1);
            bilOrderService.updateById(entity);
            response.setSuccess(true).setMessage("发送成功！");
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage("催付信息发送失败;"+e.getMessage());
        }
        return response;
    }

    private void sendUrgeMsg(RenterEntity renter ,String payment ,ResSourceVo vo,String type) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("mobile", renter.getTel());
        params.put("name",renter.getName());
        params.put("project",vo.getProjectName());
        params.put("house",vo.getPartitionName()+vo.getCode());
        params.put("price",payment);
        if("0".equals(type) || StrUtil.isBlank(type)){
            params.put("template_code", "198888");
        }else if("3".equals(type)){
            params.put("template_code", "198887");
        }else if("5".equals(type)){
            params.put("template_code", "198886");
        }else if("10".equals(type)){
            params.put("template_code", "198885");
        }
        zzctSysMsgTemplateFegin.send(params);
        params.put("title","账单缴费提醒");
        params.put("renterId",renter.getId());
        sysPushMsgService.pushRenterMsg(params);
    }

    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping(value = "/downBill", method = RequestMethod.POST)
    public RestResponse downBill(@RequestParam("orderid") String orderid,@RequestParam("ordercode") String ordercode,@RequestParam("payment") double payment,@RequestParam("sourcename") String sourcename,@RequestParam("payer") String payer){
        SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", orderid).eq("type", SysFileTypeEnum.DEPOSIT_BILL.getValue()));
        String url = "";
        if(sysFileEntity != null){
            url = sysFileEntity.getPath();
            return RestResponse.success().setAny("url",url);
        }
        BilOrderEntity order = bilOrderService.getById(orderid);
        ResSourceEntity source = resSourceService.getById(order.getSourceId());
        ResProjectParaEntity resProjectParaEntity = projectParaService.getByCodeAndProjectId("BILL_CODE",source.getProjectId());

        // 根据项目code判断是否添加前缀
        ResProjectEntity projectEntity = resProjectService.getById(source.getProjectId());
        String projectCode = projectEntity.getCode();
        String billPrefix = "";
        if ("公房".equals(projectCode)) {
            billPrefix = "ZGGF";
        }

        String billcodeStr = resProjectParaEntity.getParamValue();
        String prexYM = billcodeStr.substring(0,6);
        String yearMonth = DateUtil.format(new Date(),"yyyyMM");
        int billcode = Integer.valueOf(billcodeStr.substring(8));
        if(yearMonth.equals(prexYM)){
            billcode = billcode + 1;
        }else{
            billcode = 1;
        }
        // 生成基础发票编号（不含前缀）
        String baseBillCode = yearMonth+StrUtil.padPre(billcode+"",3,"0");

        // 保存基础编号到参数表（不含前缀）
        resProjectParaEntity.setParamValue(baseBillCode);
        projectParaService.saveOrUpdate(resProjectParaEntity);

        // 生成最终发票编号（如果需要则添加前缀）
        billcodeStr = baseBillCode;
        if (StrUtil.isNotBlank(billPrefix)) {
            billcodeStr = billPrefix + baseBillCode;
        }
        //获取项目水印地址
        ResProjectParaEntity projectWatermark = projectParaService.getByCodeAndProjectId(ProjectParaEnum.PDF_WATERMARK.getValue(),source.getProjectId());
        String watermarkImage = projectWatermark==null?"":projectWatermark.getParamValue();
        Map<String,Object> beans = Maps.newHashMap();
        beans.put("watermarkImage", watermarkImage);
        beans.put("username", payer);
        Date payTime = order.getPayTime();
        beans.put("year", DateUtil.year(payTime));
        beans.put("month", DateUtil.month(payTime));
        beans.put("day", DateUtil.dayOfMonth(payTime));
        beans.put("price", new BigDecimal(payment).setScale(2, BigDecimal.ROUND_HALF_UP));
        beans.put("amountCN", Convert.digitToChinese(payment));
        ResSourceVo sourceVo = resSourceService.getInfoById(order.getSourceId());
        String drawer = resProjectParaService.getByCodeAndProjectId("DRAWER",sourceVo.getProjectId()).getParamValue();
        beans.put("drawer",drawer);
        if("10".equals(order.getOrderType())){
            beans.put("note", "该意向金在签订正式租赁合同之后将自动结转为房屋押金，押金不足部分将另行缴纳。");
            beans.put("project","意向金");
        }else if("5".equals(order.getOrderType())){
            ContContractSourceRelEntity csr = contContractSourceRelService.getListByContractId(order.getContractId()).get(0);
            Map<String,Object> paras = Maps.newHashMap();
            paras.put("sourceIds",csr.getSourceId());
            ResSourceVo resSourceVo = resSourceService.getMultiSourceName(paras);
            String locations = addressMergerUtil.mergerAddress(resSourceVo.getSourceName());
            beans.put("note", sourcename);
            beans.put("project",locations);
        }else if("30".equals(order.getOrderType())){
            beans.put("note", sourcename);
            beans.put("project","能耗金");
        }
        beans.put("billcode", billcodeStr);

        //获取项目信息
        String projectId=UoneHeaderUtil.getProjectId();
        ResProjectEntity project = resProjectService.getById(projectId);
        SysCompanyEntity companyEntity = sysCompanyFegin.getById(project.getCompanyId());
        beans.put("companyName",companyEntity.getName());
        beans.put("sealPic",sysFileService.getByFromIdAndType(project.getCompanyId(),"seal").getPath());
        // 设置PDF页面大小为A5
        beans.put("pageSize", "A5");

        try {
            String html = ResourceUtil.readUtf8Str("excel/export/bill_zzct.html");
            String pdfName = ordercode+"pj.pdf";
            url = pdfUtil.pdf(html,beans,pdfName);

            SysFileEntity pdfEntity = new SysFileEntity();
            pdfEntity.setFromId(orderid)
                    .setName(pdfName)
                    .setType(SysFileTypeEnum.DEPOSIT_BILL.getValue())
                    .setUrl(url)
                    .insert();
            url = pdfEntity.getPath();

            order.setBillsCode(billcodeStr);
            order.setBillsState("2");
            bilOrderService.updateById(order);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        return RestResponse.success().setAny("url",url);
    }

    /**
     * 导出
     *
     * @param response
     * @param bilOrderSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/export")
    @UoneLog("账单导出")
    public void export(HttpServletResponse response, BilOrderSearchVo bilOrderSearchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        List<BilOrderVo> list = bilOrderService.bilExport(bilOrderSearchVo);
        for (BilOrderVo vo : list) {
            if(vo.getOrderType().equals(OrderTypeEnum.YAJIN.getValue())){
                vo.setOrderType("履约保证金");
            }else{
                vo.setOrderType(OrderTypeEnum.getNameByValue(vo.getOrderType()));
            }
            vo.setPayState(PayStateEnum.getNameByValue(vo.getPayState()));
            vo.setInvoiceState(InvoiceStateEnum.getNameByValue(vo.getInvoiceState()));
            vo.setPayWay(PayWayEnum.getNameByValue(vo.getPayWay()));
            if("0".equals(vo.getPlatform())){
                vo.setPlatform("社会化合同");
            }else if("3".equals(vo.getPlatform())){
                vo.setPlatform("员工合同");
            }
            if(ObjectUtil.isNotNull(vo.getPayment())){
                vo.setReceivable(vo.getPayment());
                if(ObjectUtil.isNotNull(vo.getDiscountAmount())){
                    vo.setReceivable(vo.getPayment().subtract(vo.getDiscountAmount()));
                }
            }
            List<BilOrderItemEntity> detail=bilOrderItemService.list(new QueryWrapper<BilOrderItemEntity>().eq("order_id",vo.getId()));
            for(BilOrderItemEntity d:detail){
                d.setOrderItemType(OrderItemTypeEnum.getNameByValue(d.getOrderItemType()));
            }
            vo.setDetail(detail);
            vo.setAddress(vo.getPartitionName()+vo.getAddress());
        }
        beans.put("orders", list);
        ExcelRender.me("/excel/export/bilOrder.xlsx").beans(beans).render(response);
    }

    /**
     * 导出
     *
     * @param response
     * @param bilOrderSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/exportBill")
    @UoneLog("票据管理-导出票据")
    public void exportBill(HttpServletResponse response, BilOrderSearchVo bilOrderSearchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        bilOrderSearchVo.setBillInfo("1");
        List<BilOrderVo> list = bilOrderService.bilExport(bilOrderSearchVo);
        for (BilOrderVo vo : list) {
            if(OrderTypeEnum.YAJIN.getValue().equals(vo.getOrderType())){
                vo.setBillType("履约保证金");
            }else if(OrderTypeEnum.DEPOSIT.getValue().equals(vo.getOrderType())){
                vo.setBillType("意向金");
                if(StrUtil.isNotBlank(vo.getContractId()) && "6".equals(vo.getCState())){
                    vo.setChanges("已转成押金");
                    ContContractEntity contContractEntity = contContractService.getById(vo.getContractId());
                    vo.setStartTimeStr(DateUtil.format(contContractEntity.getSignDate(), "yyyy-MM-dd"));
                }
                else
                    vo.setChanges("否");
            } else
                vo.setBillType("无效票据");
            vo.setBillName("电子票据");
            if(PayStateEnum.PAYCONFIR.getValue().equals(vo.getPayState())){
                vo.setPushType("已开票");
            } else if(PayStateEnum.NOPAY.getValue().equals(vo.getPayState())){
                vo.setPushType("待支付");
            } else
                vo.setPushType("无效票据");
            vo.setAddress(vo.getPartitionName()+vo.getAddress());
        }
        beans.put("orders", list);
        ExcelRender.me("/excel/export/billInfo.xlsx").beans(beans).render(response);
    }

}
