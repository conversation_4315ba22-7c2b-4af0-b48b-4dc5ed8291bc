package cn.uone.business.rpt.controller;


import cn.uone.bean.entity.business.report.vo.ConfirmReleaseVo;
import cn.uone.bean.parameter.ConfirmReleasePo;
import cn.uone.business.report.service.IReportReleaseOrderService;
import cn.uone.business.rpt.service.IReportConfirmReleaseService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 确认退款明细表
 */
@RestController
@RequestMapping("/report/confirmRelease")
public class ReportConfirmReleaseController extends BaseController {

    @Autowired
    private IReportConfirmReleaseService confirmReleaseService;

    @Autowired
    private IReportReleaseOrderService reportReleaseOrderService;

    @RequestMapping(value = "/page", method = RequestMethod.GET)
    public RestResponse page(Page page, ConfirmReleasePo param) {
        param.setProjectId(UoneHeaderUtil.getProjectId());
        return RestResponse.success().setData(reportReleaseOrderService.reportPage(page, param));
    }

    @RequestMapping("/export")
    public void sourceReportExport(HttpServletResponse response, ConfirmReleasePo param) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        param.setProjectId(UoneHeaderUtil.getProjectId());
        List<ConfirmReleaseVo> list= reportReleaseOrderService.report(param);
        beans.put("confirm", list);
        ExcelRender.me("/excel/export/confirmRelease.xlsx").beans(beans).render(response);
    }


}
