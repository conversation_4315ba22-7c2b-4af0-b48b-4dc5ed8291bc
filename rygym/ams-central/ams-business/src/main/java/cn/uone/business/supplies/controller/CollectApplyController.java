package cn.uone.business.supplies.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.supplies.*;
import cn.uone.business.supplies.service.ICategoryService;
import cn.uone.business.supplies.service.ICollectApplyService;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 领用申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@RestController
@RequestMapping("/supplies/collectApply")
public class CollectApplyController extends BaseController {

    @Autowired
    private ICollectApplyService service;
    @Autowired
    private ICategoryService categoryService;
    @Autowired
    private IUserFegin userFegin;



    @GetMapping("/page")
    public RestResponse page(Page<CollectApplyEntity> page, CollectApplyEntity entity){

        IPage<CollectApplyEntity> p = service.getListByPage(page,entity);
        return RestResponse.success().setData(p);
    }


    //审核列表
    @GetMapping("/collectAudit")
    public RestResponse collectAudit(Page<CollectApplyEntity> page, CollectApplyEntity entity){
        QueryWrapper<CollectApplyEntity> wrapper = new QueryWrapper<>();

        wrapper.eq("auditor_id",UoneSysUser.id());
        wrapper.orderByDesc("apply_time");
        wrapper.in("state","0","1");
        IPage<CollectApplyEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/save")
    public RestResponse save(CollectApplyEntity entity){
        if(!StrUtil.isNotBlank(entity.getAuditor())){
            entity.setApplicant(UoneSysUser.nickName());
            entity.setApplicantId(UoneSysUser.id());
            entity.setMobile(UoneSysUser.tel());
        }
        CategoryEntity categoryEntity = categoryService.getById(entity.getMaterialCategoryId());
        if(entity.getQuantity()>categoryEntity.getNowQuantity()){
            return RestResponse.failure("库存不足,当前库存量："+categoryEntity.getNowQuantity());
        }
        entity = audit(entity);
        entity.setApplyTime(new Date());
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    @PostMapping("/edit")
    public RestResponse edit(CollectApplyEntity entity){
        CategoryEntity categoryEntity = categoryService.getById(entity.getMaterialCategoryId());
        if(entity.getQuantity()>categoryEntity.getNowQuantity()){
            return RestResponse.failure("库存不足,当前库存量："+categoryEntity.getNowQuantity());
        }
        CollectApplyEntity collectApplyEntity = service.getById(entity.getId());
        String state = collectApplyEntity.getState();
        if(!"0".equals(state)){
            return RestResponse.failure("修改失败！审核状态已改变，请刷新");
        }
        entity = audit(entity);
        entity.setApplyTime(new Date());
        entity.insertOrUpdate();
        return RestResponse.success();
    }
    @PostMapping("/pass")
    public RestResponse pass(CollectApplyEntity collectApplyEntity){
        CategoryEntity categoryEntity = categoryService.getById(collectApplyEntity.getMaterialCategoryId());
        Integer nowQuantity =  categoryEntity.getNowQuantity();
        if(nowQuantity<collectApplyEntity.getQuantity()){
            return RestResponse.failure("库存不足");
        }
        String grade = userFegin.getGradeByUserId(collectApplyEntity.getAuditorId());
        Map<String,Object> map = userFegin.getAuditor(collectApplyEntity.getAuditorId(),grade);
        if(map!=null){
            //更新当前审核人
            collectApplyEntity.setAuditor(map.get("realName")==null?null:map.get("realName").toString());
            collectApplyEntity.setAuditorId( map.get("id")==null?null:map.get("id").toString());
        }
        collectApplyEntity.setFlowNode(grade);
        collectApplyEntity.setState("1");
        if("1".equals(grade)){
            collectApplyEntity.setDeptLeadData(new Date());
            collectApplyEntity.setDeptResult("审核通过");
            collectApplyEntity.insertOrUpdate();
        }else if("2".equals(grade)){
            collectApplyEntity.setBranchedLeadData(new Date());
            collectApplyEntity.setBranchedResult("审核通过");
            collectApplyEntity.insertOrUpdate();
        }else if("3".equals(grade)){
            collectApplyEntity.setAuditor("流程结束");
            collectApplyEntity.setFlowNode("4");
            collectApplyEntity.setGeneralManagerData(new Date());
            collectApplyEntity.setState("2");
            collectApplyEntity.setGeneralResult("审核通过");

            //生成出库表
            boolean insert = ware(collectApplyEntity);
            if(insert){
                collectApplyEntity.insertOrUpdate();
            }else{
                return RestResponse.failure("操作失败");
            }
        }else {
            //当没有上级部门时自己就是审核人
            collectApplyEntity.setAuditor("流程结束");
            collectApplyEntity.setAuditorId(collectApplyEntity.getAuditorId());
            collectApplyEntity.setFlowNode("4");
            collectApplyEntity.setGeneralManagerData(new Date());
            collectApplyEntity.setState("2");
            //生成入库表
            boolean insert = ware(collectApplyEntity);
            if(insert){
                collectApplyEntity.insertOrUpdate();
            }else{
                return RestResponse.failure("操作失败");
            }
        }
        return RestResponse.success();
    }
    @PostMapping("/fail")
    public RestResponse fail(CollectApplyEntity entity){
        String grade = userFegin.getGradeByUserId(entity.getAuditorId());
        if("1".equals(grade)){
            entity.setDeptLeadData(new Date());
            entity.setDeptResult("审核不通过");
        }else if("2".equals(grade)){
            entity.setBranchedLeadData(new Date());
            entity.setBranchedResult("审核不通过");
        }else if("3".equals(grade)){
            entity.setGeneralManagerData(new Date());
            entity.setGeneralResult("审核不通过");
        }
        entity.setAuditor("流程结束");
        entity.setFlowNode("4");
        entity.setState("3");
        entity.setAuditTime(new Date());
        entity.insertOrUpdate();
        return RestResponse.success();
    }
//

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        CollectApplyEntity collectApplyEntity = new CollectApplyEntity();
        for (String id:ids) {
            collectApplyEntity = service.getById(id);

        String state = collectApplyEntity.getState();
        if(!"0".equals(state)){
            return RestResponse.failure("删除失败！审核状态已改变，请刷新");
        }
        }
        service.removeByIds(ids);
        return RestResponse.success();
    }

    public CollectApplyEntity audit(CollectApplyEntity entity){
        String grade = userFegin.getGradeByUserId(entity.getApplicantId());
        Map<String,Object> map = userFegin.getAuditor(entity.getApplicantId(),grade);
        if(map==null){
            map = new HashMap<>();
            //无上级审核人则取本身
            map.put("realName",entity.getApplicant());
            map.put("id",entity.getApplicantId());
        }
        entity.setState("0");
        if("3".equals(grade)){
            entity.setGeneralManagerData(new Date());
            entity.setGeneralResult("发起申请");
            entity.setGeneralManager(entity.getApplicant());
            entity.setGeneralManagerId(entity.getApplicantId());
            entity.setAuditor("流程结束");
            entity.setFlowNode("4");
            entity.setState("2");
            //生成入库表
            ware(entity);
            return entity;
        }
        entity.setFlowNode(grade);
        //当前审核人
        String nowName = map.get("realName")==null?null:map.get("realName").toString();
        String nowId = map.get("id")==null?null:map.get("id").toString();
        entity.setAuditor(nowName);
        entity.setAuditorId(nowId);
        //下一个审核人
        String nextGrade = userFegin.getGradeByUserId(nowId);
        Map<String,Object> nextMap = userFegin.getAuditor(nowId,nextGrade);
        String nextName = "";
        String nextId = "";
        if(nextMap!=null){
            nextName = nextMap.get("realName")==null?null:nextMap.get("realName").toString();
            nextId = nextMap.get("id")==null?null:nextMap.get("id").toString();
        }
        //记录各个节点审核人
        if("0".equals(grade)){
            //部门主管
            entity.setDeptLead(nowName);
            entity.setDeptLeadId(nowId);
            //分管领导
            entity.setBranchedLead(nextName);
            entity.setBranchedLeadId(nextId);
            //总经理
            String generalGrade = userFegin.getGradeByUserId(nextId);
            if(!ObjectUtil.isEmpty(generalGrade)){
                Map<String,Object> generalMap = userFegin.getAuditor(nextId,generalGrade);
                entity.setGeneralManager(generalMap.get("realName")==null?null:generalMap.get("realName").toString());
                entity.setGeneralManagerId(generalMap.get("id")==null?null:generalMap.get("id").toString());
            }else {
                entity.setGeneralManager(entity.getApplicant());
                entity.setGeneralManagerId(entity.getApplicantId());
            }
        }else if("1".equals(grade)){
            entity.setDeptLead(entity.getApplicant());
            entity.setDeptLeadId(entity.getApplicantId());
            entity.setDeptLeadData(new Date());
            entity.setDeptResult("发起申请");
            //分管领导
            entity.setBranchedLead(nowName);
            entity.setBranchedLeadId(nowId);
            //总经理
            entity.setGeneralManager(nextName);
            entity.setGeneralManagerId(nextId);
        }else if("2".equals(grade)){
            entity.setBranchedLeadData(new Date());
            entity.setBranchedResult("发起申请");
            entity.setBranchedLead(entity.getApplicant());
            entity.setBranchedLeadId(entity.getApplicantId());
            //总经理
            entity.setGeneralManager(nowName);
            entity.setGeneralManagerId(nowId);
        }
        return entity;
    }
    public boolean ware(CollectApplyEntity entity) {
        CheckoutEntity checkoutEntity = new CheckoutEntity();
        checkoutEntity.setDeliveryId(entity.getId());
        checkoutEntity.setItemName(entity.getItemName());
        checkoutEntity.setQuantity(entity.getQuantity());
        checkoutEntity.setMaterialCategoryId(entity.getMaterialCategoryId());
        checkoutEntity.setType("1");
        checkoutEntity.setState("1");
        return checkoutEntity.insertOrUpdate();
    }

}
