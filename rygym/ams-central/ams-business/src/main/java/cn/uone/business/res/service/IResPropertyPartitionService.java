package cn.uone.business.res.service;

import cn.uone.bean.entity.business.res.ResPropertyPartitionEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IResPropertyPartitionService extends IService<ResPropertyPartitionEntity> {

    IPage<ResPropertyPartitionEntity> queryPropertyPartition(ResPropertyPartitionEntity entity);

    ResPropertyPartitionEntity queryById(String id);

    void insert(ResPropertyPartitionEntity entity);

    void updatePropertyPartition(ResPropertyPartitionEntity entity);

    void deletePropertyPartition(QueryWrapper<ResPropertyPartitionEntity> wrapper);

    Boolean propertyPartitionExist(QueryWrapper wrapper);

    List<Map<String, Object>> listWithOwner(String id);
}
