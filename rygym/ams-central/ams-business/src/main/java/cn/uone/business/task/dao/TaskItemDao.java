package cn.uone.business.task.dao;


import cn.uone.bean.entity.business.task.TaskItemEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 任务项管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
public interface TaskItemDao extends BaseMapper<TaskItemEntity> {



    IPage<TaskItemEntity> selectPageByPlanId(Page page, @Param("planId") String planId,@Param("taskType") String taskType);

    IPage<TaskItemEntity> selectSublistByPlanId(Page page, @Param("planId") String planId);

    List<TaskItemEntity>  getByPlanId(@Param("planId") String planId);

}
