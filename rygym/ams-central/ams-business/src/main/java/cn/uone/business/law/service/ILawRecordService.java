package cn.uone.business.law.service;

import cn.uone.bean.entity.business.law.LawRecordEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
public interface ILawRecordService extends IService<LawRecordEntity> {
    IPage<LawRecordEntity> queryPage(Page page, LawRecordEntity lawRecord);
}
