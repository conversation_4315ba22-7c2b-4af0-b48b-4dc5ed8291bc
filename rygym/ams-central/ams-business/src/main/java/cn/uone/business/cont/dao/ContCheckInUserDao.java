package cn.uone.business.cont.dao;

import cn.uone.bean.entity.business.cont.ContCheckInUserEntity;
import cn.uone.bean.entity.business.cont.vo.ContCheckInUserVo;
import cn.uone.bean.entity.business.cont.vo.CostShareVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface ContCheckInUserDao extends BaseMapper<ContCheckInUserEntity> {

    /**
     * @return
     * <AUTHOR>
     * @date 2018-12-29 16:43
     * @Param:
     */
    List<ContCheckInUserVo> selectCheckInUserListByMap(@Param("map") Map<String, Object> map) throws Exception;

    IPage<ContCheckInUserVo> selectPageByMap(Page page, DataScope scope, @Param("map")Map<String, Object> map);

    List<ContCheckInUserEntity> getByContractSourceId(@Param("contractSourceId") String contractSourceId,@Param("isApplyCard") String isApplyCard);

    List<ContCheckInUserEntity> getListByMap(@Param("map")Map<String, Object> map);

    void batchUpdate(@Param("map")Map<String, Object> map);

    List<CostShareVo> getShareByDateAndDeviceId(@Param("deviceId") String deviceId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取企业 已入住,还未提交搬离申请的数据
     * @param contractSourceId
     * @return
     */
    ContCheckInUserEntity getUnApplyRemove(@Param("contractSourceId") String contractSourceId);

    IPage<Map<String, String>> getSourceChein(Page page, @Param("sourceId")String sourceId);

    IPage<Map<String, String>> allContract(Page page, @Param("sourceId")String sourceId);

    List<ContCheckInUserEntity> getCheckInUser(@Param("map")Map<String, Object> map);

    List<ContCheckInUserEntity> getByContractSourceIdIn(@Param("map") Map<String, Object> map);

    IPage<ContCheckInUserEntity> getByContractSourceIdIn(Page page,@Param("map") Map<String, Object> map);

    /**
     * 逻辑删除（更改状态为99）
     * @param ids
     * @return
     */
    int changeStateByIds(List<String> ids);

}
