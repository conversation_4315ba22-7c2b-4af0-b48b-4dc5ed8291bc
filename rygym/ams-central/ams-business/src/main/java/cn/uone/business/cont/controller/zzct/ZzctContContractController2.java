package cn.uone.business.cont.controller.zzct;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.RenterType;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.ContractStateEnum;
import cn.uone.application.enumerate.contract.ContractTypeEnum;
import cn.uone.application.enumerate.contract.PayTypeEnum;
import cn.uone.application.enumerate.contract.PlatformEnum;
import cn.uone.application.enumerate.contract.SignTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.source.SourceSignEnum;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.application.enumerate.source.SourceTypeEnum;
import cn.uone.bean.entity.business.base.BaseCarEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.cont.ContCheckInHouseEntity;
import cn.uone.bean.entity.business.cont.ContCheckInUserEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractInfoEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.cont.vo.AlterPriceVo;
import cn.uone.bean.entity.business.cont.vo.ContBatchImportVo;
import cn.uone.bean.entity.business.cont.vo.ContCheckInUserVo;
import cn.uone.bean.entity.business.demo.DemoContractEntity;
import cn.uone.bean.entity.business.res.ResCostConfigureEntity;
import cn.uone.bean.entity.business.res.ResPlanPartitionEntity;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResProjectInfoEntity;
import cn.uone.bean.entity.business.res.ResSourceConfigureEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.Guomi.service.IGuomiService;
import cn.uone.business.base.service.IBaseCarService;
import cn.uone.business.base.service.IBaseEnterpriseService;
import cn.uone.business.bil.service.IBilOrderAutoService;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.bil.service.IZzctBilOrderService;
import cn.uone.business.bil.service.impl.BilOrderServiceImpl;
import cn.uone.business.bil.task.FixedOrderAutoTask;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.biz.service.IBizSettleService;
import cn.uone.business.bpm.service.IBpmWorkflowService;
import cn.uone.business.cont.controller.ContContractController2;
import cn.uone.business.cont.dao.ContContractSourceRelDao;
import cn.uone.business.cont.service.IContCheckInHouseService;
import cn.uone.business.cont.service.IContCheckInUserService;
import cn.uone.business.cont.service.IContContractInfoService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.cont.service.IContContractTempletService;
import cn.uone.business.cont.service.IContParService;
import cn.uone.business.cont.service.IContRentLadderService;
import cn.uone.business.cont.service.IContTempService;
import cn.uone.business.cont.service.IZzctContCheckInHouseService;
import cn.uone.business.cont.service.IZzctContContractService;
import cn.uone.business.cont.service.IZzctContContractSourceRelService;
import cn.uone.business.cont.service.IZzctContRentLadderService;
import cn.uone.business.demo.service.IDemoContractService;
import cn.uone.business.dev.service.IDevDeviceLogService;
import cn.uone.business.flow.service.IActReDeploymentService;
import cn.uone.business.kingdee.service.impl.KingdeeApiServiceImpl;
import cn.uone.business.res.dao.ResSourceDao;
import cn.uone.business.res.service.IResCostConfigureService;
import cn.uone.business.res.service.IResPlanPartitionService;
import cn.uone.business.res.service.IResProjectInfoService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceConfigureService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.business.util.ContractUtil;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.crm.ISysParaFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.fegin.crm.IZzctSysMsgTemplateFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.CacheParam;
import cn.uone.web.util.ExcelDataUtil;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.UoneHeaderUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 适用于漳州城投项目
 * <p>
 * 前端控制器sign
 * 合同
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 * caizhanghe edit 2024-05-21
 */
@Api(value = "合同服务", tags = {"合同新增、修改等接口"})
@RestController
@RequestMapping("/zzct/cont/contract2")
public class ZzctContContractController2 extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ContContractController2.class);

    @Autowired
    private ISysFileService fileService;
    @Autowired
    private IResSourceService sourceService;
    @Autowired
    private IContContractService contractService;
    @Autowired
    private IBilOrderService orderService;
    @Autowired
    private IContRentLadderService rentLadderService;
    @Autowired
    private IContContractInfoService contractInfoService;
    @Autowired
    private IBaseCarService carService;
    @Resource
    private IRenterFegin renterFegin;
    @Autowired
    private IResCostConfigureService configureService;
    @Autowired
    private IContContractSourceRelService contractSourceRelService;
    @Autowired
    private IResProjectInfoService projectInfoService;
    @Autowired
    private IBilOrderAutoService bilOrderAutoService;
    @Autowired
    private IContCheckInUserService checkInUserService;
    @Autowired
    private IContCheckInUserService contCheckInUserService;
    @Autowired
    private IContCheckInHouseService contCheckInHouseService;
    @Autowired
    private IContContractSourceRelService contContractSourceRelService;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    private ISysPushMsgService pushMsgService;
    @Autowired
    private IBaseEnterpriseService enterpriseService;
    @Autowired
    private ISysParaFegin sysParaFegin;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private IBizReleaseService releaseService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IBilOrderItemService bilOrderItemService;
    @Autowired
    private IGuomiService guomiService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    private IResProjectParaService resProjectParaService;
    @Autowired
    private IContTempService contTempService;
    @Autowired
    private IContContractTempletService contContractTempletService;
    @Autowired
    private IContParService contParService;
    @Resource
    private ContContractSourceRelDao contContractSourceRelDao;
    @Autowired
    private IBizReleaseService bizReleaseService;
    @Autowired
    private FixedOrderAutoTask fixedOrderAutoTask;

    @Autowired
    private IDemoContractService demoContractService;
    @Autowired
    private IBpmWorkflowService bpmWorkflowService;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private IUserFegin userFegin;
    @Resource
    private IResProjectParaService projectParaService;
    @Autowired
    private ResSourceDao resSourceDao;
    @Autowired
    private KingdeeApiServiceImpl kingdeeApiService;
    @Autowired
    private IDevDeviceLogService deviceLogService;
    @Autowired
    private IContCheckInHouseService checkInHouseService;
    @Autowired
    public IBizSettleService bizSettleService;
    @Autowired
    private IResPlanPartitionService resPlanPartitionService;

    @Resource
    private IActReDeploymentService iActReDeploymentService;

    @Autowired
    private IResSourceConfigureService resSourceConfigureService;

    @Autowired
    private IContRentLadderService contRentLadderService;

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    ISysMsgTemplateFegin sysMsgTemplateFegin;

    @Autowired
    private IZzctContContractSourceRelService zzctContContractSourceRelService;

    @Autowired
    private IZzctContRentLadderService zzctContRentLadderService;

    @Autowired
    private IZzctContContractService zzctContractService;

    @Autowired
    IZzctSysMsgTemplateFegin zzctSysMsgTemplateFegin;

    @Autowired
    private IZzctBilOrderService zzctBilOrderService;

    @Autowired
    private IZzctContContractService zzctContContractService;

    @Autowired
    private IZzctContCheckInHouseService zzctCheckInHouseService;
    @Autowired
    private BilOrderServiceImpl bilOrderServiceImpl;


    /**
     * 合同录入 待审核状态
     * @param files
     * @param idFiles
     * @param xszFiles
     * @param jszFiles
     * @param tzrIDCardFiles
     * @param tzrTemporaryFiles
     * @param request
     * @param contractCode
     * @param isRecord
     * @return
     * @throws Exception
     */
    @PostMapping("/addContractUnEffect")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "addContractUnEffect", expire = 30)
    public RestResponse addContractUnEffect(@RequestParam(required = false) List<MultipartFile> files,
                                    @RequestParam(required = false) List<MultipartFile> idFiles,
                                    @RequestParam(required = false) List<MultipartFile> xszFiles,
                                    @RequestParam(required = false) List<MultipartFile> jszFiles,
                                    @RequestParam(required = false) List<MultipartFile> tzrIDCardFiles,
                                    @RequestParam(required = false) List<MultipartFile> tzrTemporaryFiles,
                                    HttpServletRequest request,
                                    @CacheParam String contractCode,
                                    @RequestParam(required = false) String isRecord) throws Exception {
        // 必填项判断
        if (StrUtil.isBlank(request.getParameter("sourceId"))) {
            return RestResponse.failure("请选择房源");
        }
        if (StrUtil.isBlank(request.getParameter("tel"))) {
            return RestResponse.failure("手机号码不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("name"))) {
            return RestResponse.failure("租客姓名不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("idType"))) {
            return RestResponse.failure("身份证件类型不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("idNo"))) {
            return RestResponse.failure("身份证件号码不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("paperCode"))) {
            return RestResponse.failure("纸质合同编号不能为空");
        }
        if (StrUtil.isBlank(contractCode)) {
            return RestResponse.failure("合同编号不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("costConfigureId"))) {
            return RestResponse.failure("费用信息不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("payType"))) {
            return RestResponse.failure("缴费方式不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("startDate"))) {
            return RestResponse.failure("租凭起日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("endDate"))) {
            return RestResponse.failure("租凭止日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("tempId"))) {
            return RestResponse.failure("合同模板不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("signDate"))) {
            return RestResponse.failure("签约日期不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("leaseholdArea"))) {
            return RestResponse.failure("计租面积不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("taxPoint"))) {
            return RestResponse.failure("税点不能为空");
        }
        String sourceIds = request.getParameter("sourceId");
        QueryWrapper query = new QueryWrapper();
        query.in("id",sourceIds.split(","));
        List<ResSourceEntity> sourceList = sourceService.list(query);
        for(ResSourceEntity source: sourceList){
            if (!BaseConstants.BOOLEAN_OF_TRUE.equals(source.getPublishTarget()) && (SourceTypeEnum.HOUSE.getValue().equals(source.getSourceType()))) {
                return RestResponse.failure(source.getCode() + "房源未发布");
            }
            if(!SourceStateEnum.UNRENT.getValue().equals(source.getState())){
                return RestResponse.failure("房源已被预定或出租");
            }
            if (SourceSignEnum.NOTRENT.getValue().equals(source.getSourceSign()) || SourceSignEnum.RESERVE.getValue().equals(source.getSourceSign())) {
                return RestResponse.failure(source.getCode() + "房源" + SourceSignEnum.getNameByValue(source.getSourceSign()) + ",不能签约");
            }
            if (SourceTypeEnum.HOUSE.getName().equals(request.getParameter("sourceType"))
                    && PlatformEnum.YW.getValue().equals(request.getParameter("platform"))) {
                boolean isSyncCCB = contTempService.isSyncCCB(request.getParameter("tempId"));
                //判断，当同步参数和房源已上架再同时同步
                if (isSyncCCB && !BaseConstants.BOOLEAN_OF_TRUE.equals(source.getCcbPublishTarget())) {
                    return RestResponse.failure("房源还未上架，请先上架再进行操作");
                }
            }
            QueryWrapper<DemoContractEntity> wrapper = new QueryWrapper<>();
            DemoContractEntity demoContractEntity = demoContractService.getOne(wrapper.eq("source_id",source.getId()).last("limit 0,1"));
            if(null!=demoContractEntity&&DateUtil.beginOfDay(demoContractEntity.getEndTime()).getTime()<DateUtil.beginOfDay(DateUtil.parse(request.getParameter("endDate"), "yyyy-MM-dd")).getTime()){
                return RestResponse.failure("合同截止日大于业主合同截止日，不允许签约");
            }
        }
        String contractType = sourceList.size()>1?ContractTypeEnum.MULTISOURCE.getValue():ContractTypeEnum.APARTMENT.getValue();
        // 商业/公区
        List<ContRentLadderEntity> rentLadderEntityList = null;
        ContTempEntity searchVo=new ContTempEntity()
                .setType("1")
                .setSubjectType("0");
        List<ContTempEntity> temps=contTempService.queryByParam(searchVo);
        ContTempEntity temp = temps.get(0);
        String tempId = temp.getId();
        ResCostConfigureEntity costConfigure = configureService.queryByTemplateId(tempId).get(0);
        //ContTempEntity temp = contTempService.getById(request.getParameter("tempId"));
        if (null == temp) {
            return RestResponse.failure("未配置合同模版！");
        }
        if (null == costConfigure) {
            return RestResponse.failure("未配置费用信息！");
        }
        // 判断租赁周期是否符合项目租赁周期最小值
        ResProjectInfoEntity projectInfoEntity = projectInfoService.getOne(new QueryWrapper<ResProjectInfoEntity>().eq("project_id", UoneHeaderUtil.getProjectId())); // 获取项目信息
        if (null != projectInfoEntity && null != projectInfoEntity.getMinRentPeriod()) {
            if (ContractUtil.period(DateUtil.parse(request.getParameter("startDate"), "yyyy-MM-dd"), DateUtil.parse(request.getParameter("endDate"), "yyyy-MM-dd"), projectInfoEntity.getMinRentPeriod())) {
                return RestResponse.failure("租赁周期小于项目租赁周期最小值");
            }
        }
        String tel = request.getParameter("tel");
        RenterEntity renter = renterFegin.getByTelAndType(tel, RenterType.COMMON.getValue());
        // 判断租客用户是否存在,不存在则新增租客用户
        renter = contractService.judgeRenter(tel, renter, request.getParameter("name"), request.getParameter("idType"), request.getParameter("idNo"));
        String payType = request.getParameter("payType");
        /*if(PayTypeEnum.ONE_ONE_STU.getValue().equals(payType)){
            payType = PayTypeEnum.ONE_ONE.getValue() ;
        }*/
        BigDecimal leaseholdArea = null;//计租面积
        BigDecimal taxPoint = null;//税点
        if (StrUtil.isNotBlank(request.getParameter("leaseholdArea"))) {
            leaseholdArea = new BigDecimal(request.getParameter("leaseholdArea"));
        }
        if (StrUtil.isNotBlank(request.getParameter("taxPoint"))) {
            taxPoint = new BigDecimal(request.getParameter("taxPoint"));
        }
        Date freeStartDate = null;//免租起日
        Date freeEndDate = null;//免租止日
        if(StrUtil.isNotBlank(request.getParameter("freeStartDate"))){
            freeStartDate = DateUtil.parse(request.getParameter("freeStartDate"), "yyyy-MM-dd");
        }
        if(StrUtil.isNotBlank(request.getParameter("freeEndDate"))){
            freeEndDate = DateUtil.parse(request.getParameter("freeEndDate"), "yyyy-MM-dd");
        }
        BigDecimal yearIncrease = null;
        BigDecimal yearNum = null;
        if(StrUtil.isNotBlank(request.getParameter("yearIncrease"))){
            yearIncrease = new BigDecimal(request.getParameter("yearIncrease"));
        }
        if(StrUtil.isNotBlank(request.getParameter("yearNum"))){
            yearNum = new BigDecimal(request.getParameter("yearNum"));
        }
        BigDecimal price = null;//合同承租价
        if (StrUtil.isNotBlank(request.getParameter("price"))) {
            price = new BigDecimal(request.getParameter("price"));
        }
        String remark = "";//合同备注
        if (StrUtil.isNotBlank(request.getParameter("remark"))) {
            remark = request.getParameter("remark");
        }
        // 新增合同 用纸质合同编号存入系统合同编号字段
        ContContractEntity contract = new ContContractEntity();
        contract.setContractType(contractType)
                .setContractTempletId(temp.getId())
                .setContractCode(request.getParameter("paperCode"))
                .setSignerId(renter.getId())
                .setStartDate(DateUtil.beginOfDay(DateUtil.parse(request.getParameter("startDate"), "yyyy-MM-dd")))
                .setEndDate(DateUtil.endOfDay(DateUtil.parse(request.getParameter("endDate"), "yyyy-MM-dd")))
                .setSignType(SignTypeEnum.ON_LINE.getValue().equals(request.getParameter("signType")) ? SignTypeEnum.ON_LINE.getValue() : SignTypeEnum.OFF_LINE.getValue()) // 线下签约
                .setPayType(payType)
                .setCostConfigureId(costConfigure.getId())
                .setPaperCode(request.getParameter("paperCode"))
                .setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE)
                .setSignDate(DateUtil.parse(request.getParameter("signDate"), "yyyy-MM-dd"))
                .setLeaseholdArea(leaseholdArea)
                .setTaxPoint(taxPoint)
                .setFreeStartDate(freeStartDate)
                .setFreeEndDate(freeEndDate)
                .setTotalPrice(price)
                .setYearIncrease(yearIncrease)
                .setYearNum(yearNum)
                .setRemark(remark)
                .setOriginalRent(price);//保存原始租金
        contract.setState(ContractStateEnum.STATUS_REVIEW.getValue());//默认待审核状态
        /*long diffDay = DateUtil.betweenDay(contract.getStartDate(), new Date(), false);
        if (diffDay >= 0) {
            contract.setState(ContractStateEnum.STATUS_TAKE_EFFECT.getValue());
        } else {
            contract.setState(ContractStateEnum.STATUS_NO_IN_TIME.getValue());
        }*/
        contract.setManager(UoneSysUser.id());
        //如果有传值，说明是公寓合同且可能为第三方合同
        if (StrUtil.isNotBlank(request.getParameter("platform"))) {
            contract.setPlatform(request.getParameter("platform"));
            if (!PlatformEnum.YW.getValue().equals(request.getParameter("platform"))) {
                contract.setPlatformCode(request.getParameter("paperCode"))
                        .setPaperCode(null);
            }
        }
        // 判断是否重复生成合同
        if (contractService.getOne(new QueryWrapper<ContContractEntity>().eq("contract_code", contract.getContractCode())) != null) {
            return RestResponse.failure("该纸质合同编号已经存在,请检查！");
        }
        contractService.save(contract);
        // 新增合同信息
        ContContractInfoEntity contractInfo = contractInfoService.handleContractInfo(new ContContractInfoEntity(), contract.getId(), null, renter, temp, sourceList.get(0).getProjectId());
        contractInfoService.saveOrUpdate(contractInfo);
        // 新增合同房源关系
        BigDecimal deposit = null;//合同押金
        if (StrUtil.isNotBlank(request.getParameter("deposit"))) {
            deposit = new BigDecimal(request.getParameter("deposit"));
        }
        //ContContractSourceRelEntity contractSourceRelEntity = contractSourceRelService.addContContractSourceRel(contract, source, price, deposit, temp.getSubsidyPrice(), rentLadderEntityList);
        ContContractSourceRelEntity contractSourceRelEntity = zzctContContractSourceRelService.addOrUpdateContMultiSourceRel(contract, sourceIds, price, deposit, temp.getSubsidyPrice(), rentLadderEntityList);
        int idnum = 0;
        int tenum = 0;
        if (StrUtil.isNotEmpty(request.getParameter("contCheckInUserList"))) {
            List<ContCheckInUserVo> checkInUserList = JSONArray.parseArray(request.getParameter("contCheckInUserList"), ContCheckInUserVo.class);
            // 判断同住人用户是否存在,不存在则新增同住人用户
            contractService.saveCheckInUser(tzrIDCardFiles, tzrTemporaryFiles, checkInUserList, sourceIds, tel, contractSourceRelEntity, idnum, tenum, ApprovalStateEnum.APPROVAL.getValue());
        }
        // 车位
        if (ContractTypeEnum.CARPORT.getValue().equals(contractType)) {
            // 新增车辆信息
            BaseCarEntity carEntity = new BaseCarEntity();
            carEntity.setNum(request.getParameter("num"))
                    .setOwner(request.getParameter("owner"))
                    .setUser(request.getParameter("user"))
                    .setContractSourceId(contractSourceRelEntity.getId());

            carService.addCar(carEntity);

            fileService.uploadFiles(idFiles, contract.getId(), "上传身份证照片异常", SysFileTypeEnum.ID_CARD);
            fileService.uploadFiles(idFiles, contract.getId(), "上传驾驶证证照片异常", SysFileTypeEnum.DRIVING_LICENCE);
            fileService.uploadFiles(idFiles, contract.getId(), "上传照片异常", SysFileTypeEnum.VEHICLE_LICENSE);
        }
        fileService.uploadFiles(idFiles, contract.getId(), "上传身份证照片异常", SysFileTypeEnum.ID_CARD);
        boolean createPdf = false;
        //签字/未签字pdf 商业合同详情页面
        if (createPdf && ContractTypeEnum.BUSINESS.getValue().equals(contract.getContractType()) && SignTypeEnum.OFF_LINE.getValue().equals(contract.getSignType())) {
            List<SysFileEntity> contractFiles = fileService.getListByFromIdAndType(contract.getId(), SysFileTypeEnum.CONTRACT);
            if (!CollectionUtils.isEmpty(contractFiles)) {
                for (SysFileEntity file : contractFiles) {
                    file.setType(SysFileTypeEnum.UNSIGNED_CONTRACT.getValue());
                }
                fileService.updateBatchById(contractFiles);
            }
        }
        // 判断是否存在附件，是则新增合同;
        if (StrUtil.isNotBlank(request.getParameter("fileName"))) {
            SysFileEntity entity = new SysFileEntity();
            entity.setFromId(contract.getId());
            entity.setType(SysFileTypeEnum.SUB_CONTRACT.getValue());
            entity.setName(request.getParameter("contractFile"));
            entity.setUrl(request.getParameter("fileName"));
            fileService.save(entity);
        }
        //修改房源状态为已出租
        String[] sourceIdArr = sourceIds.split(",");
        for(String sourceId:sourceIdArr){
            ResSourceEntity source = sourceService.getById(sourceId);
            source.setState(SourceStateEnum.RENT.getValue()).updateById();
        }
        /*if (ContractTypeEnum.BUSINESS.getValue().equals(contract.getContractType()) && SignTypeEnum.ON_LINE.getValue().equals(contract.getSignType())) {
            contract.setState(ContractStateEnum.STATUS_SIGNING.getValue()).updateById();
            for(ResSourceEntity source: sourceList){
                source.setState(SourceStateEnum.BOOKED.getValue()).updateById();
            }
        } else {
            //contContractService.auditContract(contract, source, renter, BaseConstants.BOOLEAN_OF_TRUE.equals(isRecord));
            contContractService.auditMultiSourceContract(contract,sourceIds,renter);
            //直接办理入住
            checkInHouseService.directChenkIn(contract.getId());
        }*/
        return RestResponse.success("保存成功");
    }

    /**
     * 合同修改 待审核状态
     * @param files
     * @param idFiles
     * @param xszFiles
     * @param jszFiles
     * @param tzrIDCardFiles
     * @param tzrTemporaryFiles
     * @param request
     * @param contractCode
     * @param isRecord
     * @return
     * @throws Exception
     */
    @PostMapping("/updateContractUnEffect")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "updateContractUnEffect", expire = 30)
    public RestResponse updateContractUnEffect(@RequestParam(required = false) List<MultipartFile> files,
                                            @RequestParam(required = false) List<MultipartFile> idFiles,
                                            @RequestParam(required = false) List<MultipartFile> xszFiles,
                                            @RequestParam(required = false) List<MultipartFile> jszFiles,
                                            @RequestParam(required = false) List<MultipartFile> tzrIDCardFiles,
                                            @RequestParam(required = false) List<MultipartFile> tzrTemporaryFiles,
                                            HttpServletRequest request,
                                            @CacheParam String contractCode,
                                            @RequestParam(required = false) String isRecord,
                                            @RequestParam(required = false) String deleteIdCardIds) throws Exception {

        String contractId = request.getParameter("contractId");//合同id
        // 必填项判断
        if (StrUtil.isBlank(request.getParameter("sourceId"))) {
            return RestResponse.failure("请选择房源");
        }
        if (StrUtil.isBlank(request.getParameter("tel"))) {
            return RestResponse.failure("手机号码不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("name"))) {
            return RestResponse.failure("租客姓名不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("idType"))) {
            return RestResponse.failure("身份证件类型不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("idNo"))) {
            return RestResponse.failure("身份证件号码不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("paperCode"))) {
            return RestResponse.failure("纸质合同编号不能为空");
        }
        if (StrUtil.isBlank(contractCode)) {
            return RestResponse.failure("合同编号不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("costConfigureId"))) {
            return RestResponse.failure("费用信息不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("payType"))) {
            return RestResponse.failure("缴费方式不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("startDate"))) {
            return RestResponse.failure("租凭起日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("endDate"))) {
            return RestResponse.failure("租凭止日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("tempId"))) {
            return RestResponse.failure("合同模板不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("signDate"))) {
            return RestResponse.failure("签约日期不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("leaseholdArea"))) {
            return RestResponse.failure("计租面积不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("taxPoint"))) {
            return RestResponse.failure("税点不能为空");
        }
        String sourceIds = request.getParameter("sourceId");
        QueryWrapper query = new QueryWrapper();
        query.in("id",sourceIds.split(","));
        List<ResSourceEntity> sourceList = sourceService.list(query);
        for(ResSourceEntity source: sourceList){
            if (!BaseConstants.BOOLEAN_OF_TRUE.equals(source.getPublishTarget()) && (SourceTypeEnum.HOUSE.getValue().equals(source.getSourceType()))) {
                return RestResponse.failure(source.getCode() + "房源未发布");
            }
            //修改时,不做该项的检查
            /*if(!SourceStateEnum.UNRENT.getValue().equals(source.getState())){
                return RestResponse.failure("房源已被预定或出租");
            }*/
            if (SourceSignEnum.NOTRENT.getValue().equals(source.getSourceSign()) || SourceSignEnum.RESERVE.getValue().equals(source.getSourceSign())) {
                return RestResponse.failure(source.getCode() + "房源" + SourceSignEnum.getNameByValue(source.getSourceSign()) + ",不能签约");
            }
            if (SourceTypeEnum.HOUSE.getName().equals(request.getParameter("sourceType"))
                    && PlatformEnum.YW.getValue().equals(request.getParameter("platform"))) {
                boolean isSyncCCB = contTempService.isSyncCCB(request.getParameter("tempId"));
                //判断，当同步参数和房源已上架再同时同步
                if (isSyncCCB && !BaseConstants.BOOLEAN_OF_TRUE.equals(source.getCcbPublishTarget())) {
                    return RestResponse.failure("房源还未上架，请先上架再进行操作");
                }
            }
            QueryWrapper<DemoContractEntity> wrapper = new QueryWrapper<>();
            DemoContractEntity demoContractEntity = demoContractService.getOne(wrapper.eq("source_id",source.getId()).last("limit 0,1"));
            if(null!=demoContractEntity&&DateUtil.beginOfDay(demoContractEntity.getEndTime()).getTime()<DateUtil.beginOfDay(DateUtil.parse(request.getParameter("endDate"), "yyyy-MM-dd")).getTime()){
                return RestResponse.failure("合同截止日大于业主合同截止日，不允许签约");
            }
        }
        String contractType = sourceList.size()>1?ContractTypeEnum.MULTISOURCE.getValue():ContractTypeEnum.APARTMENT.getValue();
        // 商业/公区
        List<ContRentLadderEntity> rentLadderEntityList = null;
       /* if (SourceTypeEnum.BUSINESS.getName().equals(request.getParameter("sourceType"))) {
            if ("1".equals(request.getParameter("isRentLadder"))) {
                if (StrUtil.isBlank(request.getParameter("rentData"))) {
                    return RestResponse.failure("租金列表不能为空");
                }
                rentLadderEntityList = JSON.parseArray(request.getParameter("rentData"), ContRentLadderEntity.class);
            }
            contractType = ContractTypeEnum.BUSINESS.getValue();
        }*/
        ContTempEntity searchVo=new ContTempEntity()
                .setType("1")
                .setSubjectType("0");
        List<ContTempEntity> temps=contTempService.queryByParam(searchVo);
        ContTempEntity temp = temps.get(0);
        String tempId = temp.getId();
        ResCostConfigureEntity costConfigure = configureService.queryByTemplateId(tempId).get(0);
        //ContTempEntity temp = contTempService.getById(request.getParameter("tempId"));
        if (null == temp) {
            return RestResponse.failure("未配置合同模版！");
        }
        if (null == costConfigure) {
            return RestResponse.failure("未配置费用信息！");
        }
        // 判断租赁周期是否符合项目租赁周期最小值
        ResProjectInfoEntity projectInfoEntity = projectInfoService.getOne(new QueryWrapper<ResProjectInfoEntity>().eq("project_id", UoneHeaderUtil.getProjectId())); // 获取项目信息
        if (null != projectInfoEntity && null != projectInfoEntity.getMinRentPeriod()) {
            if (ContractUtil.period(DateUtil.parse(request.getParameter("startDate"), "yyyy-MM-dd"), DateUtil.parse(request.getParameter("endDate"), "yyyy-MM-dd"), projectInfoEntity.getMinRentPeriod())) {
                return RestResponse.failure("租赁周期小于项目租赁周期最小值");
            }
        }
        String tel = request.getParameter("tel");
        RenterEntity renter = renterFegin.getByTelAndType(tel, RenterType.COMMON.getValue());
        // 判断租客用户是否存在,不存在则新增租客用户
        renter = contractService.judgeRenter(tel, renter, request.getParameter("name"), request.getParameter("idType"), request.getParameter("idNo"));
        String payType = request.getParameter("payType");
        /*if(PayTypeEnum.ONE_ONE_STU.getValue().equals(payType)){
            payType = PayTypeEnum.ONE_ONE.getValue() ;
        }*/
        BigDecimal leaseholdArea = null;//计租面积
        BigDecimal taxPoint = null;//税点
        if (StrUtil.isNotBlank(request.getParameter("leaseholdArea"))) {
            leaseholdArea = new BigDecimal(request.getParameter("leaseholdArea"));
        }
        if (StrUtil.isNotBlank(request.getParameter("taxPoint"))) {
            taxPoint = new BigDecimal(request.getParameter("taxPoint"));
        }
        Date freeStartDate = null;//免租起日
        Date freeEndDate = null;//免租止日
        if(StrUtil.isNotBlank(request.getParameter("freeStartDate"))){
            freeStartDate = DateUtil.parse(request.getParameter("freeStartDate"), "yyyy-MM-dd");
        }
        if(StrUtil.isNotBlank(request.getParameter("freeEndDate"))){
            freeEndDate = DateUtil.parse(request.getParameter("freeEndDate"), "yyyy-MM-dd");
        }
        BigDecimal yearIncrease = null;
        BigDecimal yearNum = null;
        if(StrUtil.isNotBlank(request.getParameter("yearIncrease"))){
            yearIncrease = new BigDecimal(request.getParameter("yearIncrease"));
        }
        if(StrUtil.isNotBlank(request.getParameter("yearNum"))){
            yearNum = new BigDecimal(request.getParameter("yearNum"));
        }
        BigDecimal price = null;//合同承租价
        if (StrUtil.isNotBlank(request.getParameter("price"))) {
            price = new BigDecimal(request.getParameter("price"));
        }
        String remark = "";//合同备注
        if (StrUtil.isNotBlank(request.getParameter("remark"))) {
            remark = request.getParameter("remark");
        }
        // 修改合同 用纸质合同编号存入系统合同编号字段
        ContContractEntity contract = contractService.getById(contractId);
        contract.setContractType(contractType)
                .setContractTempletId(temp.getId())
                .setContractCode(request.getParameter("paperCode"))
                .setSignerId(renter.getId())
                .setStartDate(DateUtil.beginOfDay(DateUtil.parse(request.getParameter("startDate"), "yyyy-MM-dd")))
                .setEndDate(DateUtil.endOfDay(DateUtil.parse(request.getParameter("endDate"), "yyyy-MM-dd")))
                .setSignType(SignTypeEnum.ON_LINE.getValue().equals(request.getParameter("signType")) ? SignTypeEnum.ON_LINE.getValue() : SignTypeEnum.OFF_LINE.getValue()) // 线下签约
                .setPayType(payType)
                .setCostConfigureId(costConfigure.getId())
                .setPaperCode(request.getParameter("paperCode"))
                .setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE)
                .setSignDate(DateUtil.parse(request.getParameter("signDate"), "yyyy-MM-dd"))
                .setLeaseholdArea(leaseholdArea)
                .setTaxPoint(taxPoint)
                .setFreeStartDate(freeStartDate)
                .setFreeEndDate(freeEndDate)
                .setTotalPrice(price)
                .setYearIncrease(yearIncrease)
                .setRemark(remark)
                .setYearNum(yearNum);
        contract.setState(ContractStateEnum.STATUS_REVIEW.getValue());//合同状态默认待审核
        contract.setManager(UoneSysUser.id());
        //如果有传值，说明是公寓合同且可能为第三方合同
        if (StrUtil.isNotBlank(request.getParameter("platform"))) {
            contract.setPlatform(request.getParameter("platform"));
            if (!PlatformEnum.YW.getValue().equals(request.getParameter("platform"))) {
                contract.setPlatformCode(request.getParameter("paperCode"))
                        .setPaperCode(null);
            }
        }
        ContContractEntity contractOther =  contractService.getOne(new QueryWrapper<ContContractEntity>().eq("contract_code", contract.getContractCode()));
        // 判断是否重复生成合同,前面已经把系统合同编号设置为纸质合同编号,两种合同编号是一样的
        if (contractOther !=null && !contractId.equals(contractOther.getId())) {//合同id如果不一致,说明不是同一条记录,则提示重复
            return RestResponse.failure("该纸质合同编号已经存在,请检查！");
        }
        contractService.saveOrUpdate(contract);
        // 修改合同信息
        ContContractInfoEntity contractInfo = contractInfoService.getByContractId(contractId);//获取合同信息
        contractInfo = contractInfoService.handleContractInfo(contractInfo, contract.getId(), null, renter, temp, sourceList.get(0).getProjectId());
        contractInfoService.saveOrUpdate(contractInfo);
        // 新增合同房源关系
        BigDecimal deposit = null;//合同押金
        if (StrUtil.isNotBlank(request.getParameter("deposit"))) {
            deposit = new BigDecimal(request.getParameter("deposit"));
        }
        //ContContractSourceRelEntity contractSourceRelEntity = contractSourceRelService.addContContractSourceRel(contract, source, price, deposit, temp.getSubsidyPrice(), rentLadderEntityList);
        ContContractSourceRelEntity contractSourceRelEntity = zzctContContractSourceRelService.addOrUpdateContMultiSourceRel(contract, sourceIds, price, deposit, temp.getSubsidyPrice(), rentLadderEntityList);
        int idnum = 0;
        int tenum = 0;
        if (StrUtil.isNotEmpty(request.getParameter("contCheckInUserList"))) {
            List<ContCheckInUserVo> checkInUserList = JSONArray.parseArray(request.getParameter("contCheckInUserList"), ContCheckInUserVo.class);
            // 判断同住人用户是否存在,不存在则新增同住人用户
            contractService.saveCheckInUser(tzrIDCardFiles, tzrTemporaryFiles, checkInUserList, sourceIds, tel, contractSourceRelEntity, idnum, tenum, ApprovalStateEnum.APPROVAL.getValue());
        }
        // 车位
        if (ContractTypeEnum.CARPORT.getValue().equals(contractType)) {
            // 新增车辆信息
            BaseCarEntity carEntity = new BaseCarEntity();
            carEntity.setNum(request.getParameter("num"))
                    .setOwner(request.getParameter("owner"))
                    .setUser(request.getParameter("user"))
                    .setContractSourceId(contractSourceRelEntity.getId());

            carService.addCar(carEntity);

            fileService.uploadFiles(idFiles, contract.getId(), "上传身份证照片异常", SysFileTypeEnum.ID_CARD);
            fileService.uploadFiles(idFiles, contract.getId(), "上传驾驶证证照片异常", SysFileTypeEnum.DRIVING_LICENCE);
            fileService.uploadFiles(idFiles, contract.getId(), "上传照片异常", SysFileTypeEnum.VEHICLE_LICENSE);
        }
        if(StringUtils.isNotBlank(deleteIdCardIds)){//如果有删除身份证图片,则先删除
            String[] deleteIdCardIdsArr = deleteIdCardIds.split(",");
            for(String delId:deleteIdCardIdsArr){
                SysFileEntity idCardFile = fileService.getById(delId);
                fileService.delFile(idCardFile);
            }
        }
        fileService.uploadFiles(idFiles, contract.getId(), "上传身份证照片异常", SysFileTypeEnum.ID_CARD);
        boolean createPdf = false;
        //签字/未签字pdf 商业合同详情页面
        if (createPdf && ContractTypeEnum.BUSINESS.getValue().equals(contract.getContractType()) && SignTypeEnum.OFF_LINE.getValue().equals(contract.getSignType())) {
            List<SysFileEntity> contractFiles = fileService.getListByFromIdAndType(contract.getId(), SysFileTypeEnum.CONTRACT);
            if (!CollectionUtils.isEmpty(contractFiles)) {
                for (SysFileEntity file : contractFiles) {
                    file.setType(SysFileTypeEnum.UNSIGNED_CONTRACT.getValue());
                }
                fileService.updateBatchById(contractFiles);
            }
        }
        // 判断是否存在附件，是则新增附件记录;
        if (StrUtil.isNotBlank(request.getParameter("fileName"))) {
            String contractFile = request.getParameter("contractFile");
            List<SysFileEntity> oldSysFileList = fileService.getListByFromIdAndType(contractId,SysFileTypeEnum.SUB_CONTRACT);
            String oldFileName = "";//上次上传的合同的名称
            SysFileEntity oldSysFile = null;
            if(oldSysFileList.size()>0){
                oldSysFile = oldSysFileList.get(0);
                oldFileName = oldSysFile.getName();
            }
            if(!contractFile.equals(oldFileName)){//如果文件名不一样，说明是新增合同上传,删除旧的，上传新的,否则不做修改
                if(oldSysFile != null){
                    fileService.delFile(oldSysFile);//先删除旧的
                }
                SysFileEntity entity = new SysFileEntity();
                entity.setFromId(contract.getId());
                entity.setType(SysFileTypeEnum.SUB_CONTRACT.getValue());
                entity.setName(contractFile);
                entity.setUrl(request.getParameter("fileName"));
                fileService.saveOrUpdate(entity);
            }
        }
        return RestResponse.success("修改成功");
    }

    /**
     * 合同批量审核
     * 待审核状态审核通过以后变成已生效状态
     * @param contractIds
     * @return
     * @throws Exception
     */
    @RequestMapping("/takeContractEffect")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "takeContractEffect", expire = 60)
    public RestResponse takeContractEffect(String contractIds) throws Exception {
        String[] contractIdsArr = contractIds.split(",");
        for(String contractId:contractIdsArr){//遍历每一份合同,审核通过
            //String contractId = request.getParameter("contractId");//合同id
            ContContractEntity contract = contContractService.getById(contractId);
            String contractState = contract.getState();
            if(!ContractStateEnum.STATUS_REVIEW.getValue().equals(contractState)){//判断合同状态只能为待审核状态
                return RestResponse.failure(contract.getContractCode() + ",合同状态为非待审核状态，请检查！");
            }
            List<ContContractSourceRelEntity> relList = contractSourceRelService.findByContractId(contractId);
            ContContractSourceRelEntity rel = relList.get(0);
            String sourceIds = rel.getSourceId();
            //String sourceIds = request.getParameter("sourceId");
            QueryWrapper query = new QueryWrapper();
            query.in("id",sourceIds.split(","));
            List<ResSourceEntity> sourceList = sourceService.list(query);
            String sourceCodes = "";
            for(ResSourceEntity source: sourceList){
                if (!BaseConstants.BOOLEAN_OF_TRUE.equals(source.getPublishTarget())) {
                    return RestResponse.failure(source.getCode() + "房源未发布");
                }
                //审核时不做该项的检查
                /*if(SourceStateEnum.RENT.getValue().equals(source.getState())){
                    return RestResponse.failure("房源已出租");
                }*/
                if (SourceSignEnum.NOTRENT.getValue().equals(source.getSourceSign()) || SourceSignEnum.RESERVE.getValue().equals(source.getSourceSign())) {
                    return RestResponse.failure(source.getCode() + "房源" + SourceSignEnum.getNameByValue(source.getSourceSign()) + ",不能签约");
                }
                sourceCodes = (StrUtil.isBlank(sourceCodes)?"":sourceCodes+",")+source.getCode();
            }
            String contractType = sourceList.size()>1?ContractTypeEnum.MULTISOURCE.getValue():ContractTypeEnum.APARTMENT.getValue();
            // 商业/公区
            List<ContRentLadderEntity> rentLadderEntityList = null;
            String tempId = contract.getContractTempletId();
            ContTempEntity temp = contTempService.getById(tempId);
            if (null == temp) {
                return RestResponse.failure("未配置合同模版！");
            }
            ResCostConfigureEntity costConfigure = configureService.queryByTemplateId(tempId).get(0);
            if (null == costConfigure) {
                return RestResponse.failure("未配置费用信息！");
            }
            String signerId = contract.getSignerId();
            RenterEntity renter = renterFegin.getById(signerId);
            String tel = renter.getTel();
            //合同修改
            long diffDay = DateUtil.betweenDay(contract.getStartDate(), new Date(), false);
            if (diffDay >= 0) {
                contract.setState(ContractStateEnum.STATUS_TAKE_EFFECT.getValue());
            } else {
                contract.setState(ContractStateEnum.STATUS_NO_IN_TIME.getValue());
            }
            contract.setManager(UoneSysUser.id());
            contractService.saveOrUpdate(contract);
            BigDecimal price = contract.getTotalPrice();//房屋承租价
            boolean resAddLander = zzctContRentLadderService.addMultiContRentLadder(contract,rel,price,rentLadderEntityList);//保存租金阶梯关系表记录
            int idnum = 0;
            int tenum = 0;
            /*if (StrUtil.isNotEmpty(request.getParameter("contCheckInUserList"))) {
                List<ContCheckInUserVo> checkInUserList = JSONArray.parseArray(request.getParameter("contCheckInUserList"), ContCheckInUserVo.class);
                // 判断同住人用户是否存在,不存在则新增同住人用户
                contractService.saveCheckInUser(tzrIDCardFiles, tzrTemporaryFiles, checkInUserList, sourceIds, tel, rel, idnum, tenum, ApprovalStateEnum.APPROVAL.getValue());
            }*/
            boolean createPdf = false;
            //签字/未签字pdf 商业合同详情页面
            if (createPdf && ContractTypeEnum.BUSINESS.getValue().equals(contract.getContractType()) && SignTypeEnum.OFF_LINE.getValue().equals(contract.getSignType())) {
                List<SysFileEntity> contractFiles = fileService.getListByFromIdAndType(contract.getId(), SysFileTypeEnum.CONTRACT);
                if (!CollectionUtils.isEmpty(contractFiles)) {
                    for (SysFileEntity file : contractFiles) {
                        file.setType(SysFileTypeEnum.UNSIGNED_CONTRACT.getValue());
                    }
                    fileService.updateBatchById(contractFiles);
                }
            }
            if (ContractTypeEnum.BUSINESS.getValue().equals(contract.getContractType()) && SignTypeEnum.ON_LINE.getValue().equals(contract.getSignType())) {
                contract.setState(ContractStateEnum.STATUS_SIGNING.getValue()).updateById();
                for(ResSourceEntity source: sourceList){
                    source.setState(SourceStateEnum.BOOKED.getValue()).updateById();
                }
            } else {
                //contContractService.auditContract(contract, source, renter, BaseConstants.BOOLEAN_OF_TRUE.equals(isRecord));
                zzctContractService.auditMultiSourceContract(contract,sourceIds,renter);
                //直接办理入住
                zzctCheckInHouseService.directChenkIn(contract.getId());
            }
            //保存合同总金额(不含押金)，改成在生成租金账单的时候累加汇总保存
            //contract.setTotalAmount(bilOrderService.getTotalAmount(contractId));
            contractService.saveOrUpdate(contract);

            try {
                ResProjectEntity project=resProjectService.queryById(sourceList.get(0).getProjectId());
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("mobile", renter.getTel());
                params.put("name",renter.getName() );
                params.put("project",project.getName());
                params.put("house",sourceCodes);
                params.put("template_code", "198884");
                // modify by linderen on 20210714 修改通知方式为公众号通知 start
                //zzctSysMsgTemplateFegin.send(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return RestResponse.success("签约成功");
    }

    /**
     * 合同撤回
     */
    @RequestMapping("/revocation")
    @Transactional
    public RestResponse revocation(String contractId) {

        if(StrUtil.isBlank(contractId)){
            return RestResponse.failure("未获取到合同Id");
        }
        ContContractEntity contract = contContractService.getById(contractId);
        if(contract==null){
            return RestResponse.failure("未获取到合同");
        }
        /*查询是否存在已支付的账单，存在则不能撤回*/
        QueryWrapper<BilOrderEntity> isPayQuery = new QueryWrapper();
        isPayQuery.eq("contract_id", contractId);
        isPayQuery.eq("pay_state",PayStateEnum.PAYCONFIR.getValue());
        isPayQuery.ne("order_type", OrderTypeEnum.DEPOSIT.getValue());
        isPayQuery.ne("order_type", OrderTypeEnum.DEPOSITREFUND.getValue());
        List<BilOrderEntity> orderPays = orderService.list(isPayQuery);
        if(orderPays!=null && orderPays.size()>0){
            return RestResponse.failure("存在已支付的账单，不能撤回");
        }

        List<ContContractSourceRelEntity> relList = contractSourceRelService.findByContractId(contractId);
        ContContractSourceRelEntity rel = relList.get(0);
        /* 删除租金阶梯关系表记录  */
        QueryWrapper<ContRentLadderEntity> ladderQueryWrapper = new QueryWrapper();
        ladderQueryWrapper.eq("contract_source_id", rel.getId());
        zzctContRentLadderService.remove(ladderQueryWrapper);
        // 删除除了意向金外的所有账单
        QueryWrapper<BilOrderEntity> orderWrapper = new QueryWrapper();
        orderWrapper.eq("contract_id", contractId);
        orderWrapper.ne("order_type", OrderTypeEnum.DEPOSIT.getValue());
        orderWrapper.ne("order_type", OrderTypeEnum.DEPOSITREFUND.getValue());
        List<BilOrderEntity> orderEntities = orderService.list(orderWrapper);
        //删除账单子表
        for (BilOrderEntity order:orderEntities) {
            QueryWrapper<BilOrderItemEntity> orderItemEntityQueryWrapper = new QueryWrapper();
            orderItemEntityQueryWrapper.eq("order_id", order.getId());
            bilOrderItemService.remove(orderItemEntityQueryWrapper);
        }
        //删除账单主表
        orderService.remove(orderWrapper);

        //当前入住状态改为未入住
        String[] sourceIds = rel.getSourceId().split(",");
        for (String sourceId :sourceIds) {
            ResSourceVo source = resSourceService.getInfoById(sourceId);
            source.setCheckIn(false);
            resSourceService.updateById(source);
        }

        //删除入住表信息
        QueryWrapper<ContCheckInHouseEntity> houseEntityQueryWrapper = new QueryWrapper();
        houseEntityQueryWrapper.eq("contract_source_id", rel.getId());
        contCheckInHouseService.remove(houseEntityQueryWrapper);
        QueryWrapper<ContCheckInUserEntity> userEntityQueryWrapper = new QueryWrapper();
        userEntityQueryWrapper.eq("contract_source_id", contractId);
        contCheckInUserService.remove(userEntityQueryWrapper);

        //合同状态修改为未审核
        contract.setState(ContractStateEnum.STATUS_REVIEW.getValue());
        contract.setManager("");
        contractService.updateById(contract);

        return RestResponse.success("撤回成功");
    }

    /**
     * 合同批量删除
     * 只能删除待审核状态的合同
     * @param contractIds
     * @return
     * @throws Exception
     */
    @RequestMapping("/delContracts")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "delContracts", expire = 60)
    public RestResponse delContracts(String contractIds) throws Exception {
        String[] contractIdsArr = contractIds.split(",");
        for(String contractId:contractIdsArr){//遍历每一份合同,删除相关表记录
            //String contractId = request.getParameter("contractId");//合同id
            ContContractEntity contract = contContractService.getById(contractId);
            String contractState = contract.getState();
            if(!ContractStateEnum.STATUS_REVIEW.getValue().equals(contractState)){//判断合同状态只能为待审核状态
                return RestResponse.failure(contract.getContractCode() + ",合同状态为非待审核状态，请检查！");
            }
            boolean delFiles = fileService.delFileByFromId(contractId);//删除合同附件、身份证
            List<ContContractSourceRelEntity> relList = contractSourceRelService.findByContractId(contractId);
            ContContractSourceRelEntity rel = relList.get(0);
            String sourceIds = rel.getSourceId();
            contractSourceRelService.removeById(rel.getId());//删除房源合同关系记录
            //String sourceIds = request.getParameter("sourceId");
            QueryWrapper query = new QueryWrapper();
            query.in("id",sourceIds.split(","));
            List<ResSourceEntity> sourceList = sourceService.list(query);
            for(ResSourceEntity source: sourceList){
                source.setState(SourceStateEnum.UNRENT.getValue());//修改房源状态为未出租
                sourceService.saveOrUpdate(source);
            }
            ContContractInfoEntity contractInfo = contractInfoService.getByContractId(contractId);
            if(contractInfo != null){
                contractInfoService.removeById(contractInfo.getId());//删除合同信息表
            }
            contractService.removeById(contractId);//删除合同表
        }
        return RestResponse.success("删除成功");
    }

    /**
     * 批量终止合同
     * 只能终止已生效状态的合同
     * @param contractIds
     * @return
     * @throws Exception
     */
    @RequestMapping("/batchEndContract")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "batchEndContract", expire = 60)
    public RestResponse batchEndContract(@RequestParam("contractIds") String contractIds,@RequestParam("stopTime")String stopTime,
                                         @RequestParam("remark")String remark,@RequestParam(value = "payment",required = false) BigDecimal payment) throws Exception {
        RestResponse response = new RestResponse();
        try {
            String[] contractIdsArr = contractIds.split(",");
            for(String contractId:contractIdsArr){//遍历每一份合同,暂时只修改合同状态和房源状态
                //String contractId = request.getParameter("contractId");//合同id
                ContContractEntity contract = contContractService.getById(contractId);
                String contractState = contract.getState();
                if(!ContractStateEnum.STATUS_TAKE_EFFECT.getValue().equals(contractState)){//判断合同状态只能为已生效状态
                    return RestResponse.failure(contract.getContractCode() + ",合同状态为非已生效状态，请检查！");
                }
                if(DateUtil.compare(new Date(),contract.getEndDate()) == -1){
                    contract.setState(ContractStateEnum.STATUS_EARLY_STOP.getValue());//提前终止
                    if(payment != null){
                        //修改退租当期账单的金额
                        Map<String,Object> map = Maps.newHashMap();
                        map.put("contractId",contractId);
                        map.put("orderType",OrderTypeEnum.RENT.getValue());
                        map.put("stopTime",stopTime);
                        BilOrderEntity stopOrder = orderService.getOrderByMap(map);
                        //BilOrderItemEntity lastItem = bilOrderItemService.getItemsByOrderId(stopOrder.getId()).get(0);
                        map.put("orderId",stopOrder.getId());
                        map.put("isAfter","0");
                        List<BilOrderItemEntity> itemList0 = bilOrderItemService.getOrderItemsByMap(map);
                        BigDecimal payment0 = BigDecimal.ZERO;
                        for(BilOrderItemEntity item:itemList0){
                            payment0 = payment0.add(item.getPayment());
                        }
                        map.put("isAfter","1");
                        List<BilOrderItemEntity> itemList1 = bilOrderItemService.getOrderItemsByMap(map);
                        if(itemList1 != null && itemList1.size() > 0){
                            BilOrderItemEntity lastItem = itemList1.get(0);
                            lastItem.setPayment(payment.subtract(payment0));
                            bilOrderItemService.updateById(lastItem);
                            itemList1.remove(0);
                            for(int i=1;i<itemList1.size();i++){
                                bilOrderItemService.removeById(itemList1.get(i).getId());
                            }
                        }
                        stopOrder.setPayablePayment(payment);
                        stopOrder.setPush(true);
                        orderService.updateById(stopOrder);
                    }
                    //账单处理：取消原来未推送的账单
                    bilOrderService.cancelUnpushOrdersByContractId(contract.getId());
                }else{
                    contract.setState(ContractStateEnum.STATUS_TERMINATION.getValue());//合同状态修改为已退租状态
                }
                contract.setStopTime(DateUtil.parseDateTime(stopTime));
                String oldRemark = contract.getRemark();
                String newRemark = remark;
                if(StringUtils.isNotBlank(oldRemark)){
                    newRemark = oldRemark + newRemark;
                }
                contract.setRemark(newRemark);
                contContractService.saveOrUpdate(contract);
                List<ContContractSourceRelEntity> relList = contractSourceRelService.findByContractId(contractId);
                ContContractSourceRelEntity rel = relList.get(0);
                String sourceIds = rel.getSourceId();
                QueryWrapper query = new QueryWrapper();
                query.in("id",sourceIds.split(","));
                List<ResSourceEntity> sourceList = sourceService.list(query);
                for(ResSourceEntity source: sourceList){
                    source.setState(SourceStateEnum.UNRENT.getValue());//修改房源状态为未出租状态
                    sourceService.saveOrUpdate(source);
                }
            }
            response.setSuccess(true).setMessage("提交成功！");
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 获取合同相关信息
     * 租客信息、合同附件、身份证
     *
     * @param sourceId
     * @return
     */
    @RequestMapping("/getRenterAndFile")
    @UonePermissions
    public RestResponse getRenterAndFile(@RequestParam("contractId") String contractId, @RequestParam(required = false) String sourceId) throws Exception {
        Map<String, Object> resultDataMap = new HashMap<>();
        ContContractEntity contract = contractService.getById(contractId);
        ContContractSourceRelEntity rel = null;
        List<ContContractSourceRelEntity> relList = contractSourceRelService.getListByContractId(contractId);
        if(relList.size()>0){
            rel = relList.get(0);
        }
        resultDataMap.put("rel", rel);
        //签约人信息
        RenterEntity renter = renterFegin.getById(contract.getSignerId());
        resultDataMap.put("renter", renter);
        //组装文件map
        return RestResponse.success().setData(assembleFileMap(contractId, resultDataMap,true));
    }


    private Map<String, Object> assembleFileMap(String id, Map<String, Object> resultDataMap,boolean isAddCheckinIdCard) {
        //合同pdf
        List<SysFileEntity> contractFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.CONTRACT);
        resultDataMap.put("contractFiles", contractFiles);
        //未签字合同pdf
        List<SysFileEntity> unsignedContractFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.UNSIGNED_CONTRACT);
        resultDataMap.put("unsignedContractFiles", unsignedContractFiles);
        //补充协议
        List<SysFileEntity> subjoinContractFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.SUBJOIN_CONTRACT);
        //退房确认书
        List<SysFileEntity> stopContractFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.EARLY_AGREEMENT);
        subjoinContractFiles.addAll(stopContractFiles);
        resultDataMap.put("subjoinContractFiles", subjoinContractFiles);
        //身份证
        List<SysFileEntity> idCardFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.ID_CARD);
        if(isAddCheckinIdCard){
            List<ContContractSourceRelEntity> rels = contContractSourceRelService.getListByContractId(id);
            for (ContContractSourceRelEntity rel : rels) {
                List<ContCheckInUserEntity> checkInUsers = contCheckInUserService.getByContractSourceId(rel.getId());
                for (ContCheckInUserEntity checkInUser : checkInUsers) {
                    List<SysFileEntity> checkinIdcardfFiles = fileService.getListByFromIdAndType(checkInUser.getId(), SysFileTypeEnum.CHECKIN_ID_CARD);
                    if (!checkinIdcardfFiles.isEmpty()) {
                        idCardFiles.addAll(checkinIdcardfFiles);
                    }
                }
            }
        }

        resultDataMap.put("idCardFiles", idCardFiles);
        //暂住证
        List<SysFileEntity> temporaryResidenceFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.TEMPORARY_RESIDENCE);
        resultDataMap.put("temporaryResidenceFiles", temporaryResidenceFiles);
        //营业执照
        List<SysFileEntity> businessLicenseFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.BUSINESS_LICENSE);
        resultDataMap.put("businessLicenseFiles", businessLicenseFiles);
        //行驶证
        List<SysFileEntity> vehicleLicenseFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.VEHICLE_LICENSE);
        resultDataMap.put("vehicleLicenseFiles", vehicleLicenseFiles);
        //驾驶证
        List<SysFileEntity> drivingLicenceFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.DRIVING_LICENCE);
        resultDataMap.put("drivingLicenceFiles", drivingLicenceFiles);
        //手动上传合同
        List<SysFileEntity> subContractFiles = fileService.getListByFromIdAndType(id, SysFileTypeEnum.SUB_CONTRACT);
        resultDataMap.put("subContractFiles", subContractFiles);
        return resultDataMap;
    }

    /**
     * 预定第三方平台合同直接生成接口
     *
     * @param sourceId
     * @param platformCode
     * @return
     * @throws Exception
     */
    @RequestMapping("/generateContract")
    //@UonePermissions
    @Transactional
    public RestResponse generateContract(String sourceId, String platformCode, String orderId, String tempId, String platform,String startDate,String endDate, AlterPriceVo alterPriceVo) throws Exception {
        if (StrUtil.isBlank(sourceId)) {
            return RestResponse.failure("sourceId不允许为空！");
        }

        if (StrUtil.isBlank(orderId)) {
            return RestResponse.failure("orderId不允许为空！");
        }
        BilOrderEntity order = bilOrderService.getById(orderId);
        ResSourceVo source = resSourceService.getInfoById(sourceId);
        RenterEntity renter = renterFegin.getById(order.getPayerId());
        if (ObjectUtil.isNull(renter)) {
            return RestResponse.failure("order对应的支付人为空！");
        }
        //获取模板
        //因为车位租客小程序签约时就生成合同了，所以只能先沿用原来的tempId为搜索的
        if (StrUtil.isEmpty(tempId)) {
            ContTempEntity temp = contTempService.matcherContractTemplet(source, renter, "sign");
            tempId = temp.getId();
        }
        //根据合同模板判断是否同步到建信，是的话判断合同编号(第三方也判断合同编号,条件为platform是1)
        //7.9 去掉判断CCB的合同编号

//        if (StrUtil.isBlank(platformCode) && (BaseConstants.BOOLEAN_OF_TRUE.equals(platform))) {
//            return RestResponse.failure("platformCode不允许为空！");
//        }

        //1.修改房源状态 变成已预订
        source.setState(SourceStateEnum.BOOKED.getValue());
        resSourceService.updateById(source);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
        Date sDate = simpleDateFormat.parse(startDate);
        Date eDate = simpleDateFormat.parse(endDate);
        //直接生成生效合同
        ContContractEntity contract = contractService.generateContract(renter, source, tempId, sDate, eDate,null,alterPriceVo);

        //如果是第三方合同，就无视是否建信，直接设置合同为第三方
//        if (BaseConstants.BOOLEAN_OF_TRUE.equals(platform)) {
//            contract.setPlatform(BaseConstants.BOOLEAN_OF_TRUE);
//        }
//        contract.setPlatformCode(platformCode);
        contract.setManager(UoneSysUser.id());
        contract.updateById();
        //更新定金账单对应的合同id
        order.setContractId(contract.getId());
        bilOrderService.updateById(order);
        zzctContContractService.remind(renter.getTel(),renter.getName(),renter.getId());
        return RestResponse.success();
    }

    @RequestMapping(value = "/export")
    public void export(HttpServletRequest request, HttpServletResponse response) throws BusinessException {
        Map<String, Object> map = Maps.newHashMap();
        ExcelRender.me("/excel/import/contractTemplate_zzct.xlsx").beans(map).render(response);
    }

    /**
     * 批量导入合同
     * @param multipartFile
     * @return
     * @throws Exception
     */
    @PostMapping(value="/importContract",headers = "content-type=multipart/form-data")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "importContract", expire = 30)
    public RestResponse importContract(@RequestParam("file") MultipartFile multipartFile) throws Exception {

        InputStream is = null;
        try {
            is = multipartFile.getInputStream();
            List<ContBatchImportVo> list = ExcelDataUtil.importData(is, ContBatchImportVo.class);
            int i = 3;
            int j = 1;
            Set<Object> set = new HashSet<>();
            if (list.size() > 0) {
                ResProjectEntity projectEntity = resProjectService.getOne(new QueryWrapper<ResProjectEntity>().eq("name", list.get(0).getProjectName()));
                if (projectEntity == null) {
                    return RestResponse.failure("项目不存在");
                }
                for (ContBatchImportVo vo : list) {
                    vo.setCostConfigureStr("数字化资产租赁收费标准");

                    if (StringUtils.isBlank(vo.getProjectName())) {
                        return RestResponse.failure("第" + i + "行，项目名称不能为空");
                    }
                    if (StringUtils.isBlank(vo.getPartitionName())) {
                        return RestResponse.failure("第" + i + "行，区域名称不能为空");
                    }
                    if (StringUtils.isBlank(vo.getCode())) {
                        return RestResponse.failure("第" + i + "行，房间号不能为空");
                    }
                    if (StringUtils.isBlank(vo.getTel())) {
                        return RestResponse.failure("第" + i + "行，手机号码不能为空");
                    }
                    if (!isPhoneNum(vo.getTel())) {
                        return RestResponse.failure("第" + i + "行，手机号码格式错误");
                    }
                    if (StringUtils.isBlank(vo.getName())) {
                        return RestResponse.failure("第" + i + "行，租客姓名不能为空");
                    }
                    //--
                    if (vo.getTaxPint()==null) {
                        return RestResponse.failure("第" + i + "行，税点不能为空");
                    }
                    if (vo.getLeaseholdArea()==null) {
                        return RestResponse.failure("第" + i + "行，计租面积不能为空");
                    }
                    /*if (vo.getRentFreePeriod() ==null) {
                        return RestResponse.failure("第" + i + "行，免租期不能为空");
                    }*/
                    //--
//                    if (StringUtils.isBlank(vo.getIdTypeStr())) {
//                        return RestResponse.failure("第" + i + "行，证件类型不能为空");
//                    }
                    if (StringUtils.isBlank(vo.getIdNo())) {
                        return RestResponse.failure("第" + i + "行，证件号码不能为空");
                    }
                    if (StringUtils.isBlank(vo.getPaperCode())) {
                        return RestResponse.failure("第" + i + "行，纸质合同编号不能为空");
                    }
                    if (StringUtils.isBlank(vo.getStartDate())) {
                        return RestResponse.failure("第" + i + "行，租赁起日不能为空");
                    }
                    if (StringUtils.isBlank(vo.getEndDate())) {
                        return RestResponse.failure("第" + i + "行，租赁止日不能为空");
                    }
                    if (StringUtils.isBlank(vo.getPrice())) {
                        return RestResponse.failure("第" + i + "行，房源租金不能为空");
                    }
                    if (StringUtils.isBlank(vo.getDeposit())) {
                        return RestResponse.failure("第" + i + "行，房源押金不能为空");
                    }
//                    if (StringUtils.isBlank(vo.getTempStr())) {
//                        return RestResponse.failure("第" + i + "行，合同模板不能为空");
//                    }
                    if (StringUtils.isBlank(vo.getCostConfigureStr())) {
//                        return RestResponse.failure("第" + i + "行，费用信息不能为空");
                    }
                    if (StringUtils.isBlank(vo.getPayTypeStr())) {
                        return RestResponse.failure("第" + i + "行，缴费方式不能为空");
                    }
                    if (StringUtils.isBlank(vo.getSignDate())) {
                        return RestResponse.failure("第" + i + "行，签约日期不能为空");
                    }
                    set.add("{"+vo.getCode()+","+vo.getPartitionName()+"}");
                    if (set.size() != j) {
                        return RestResponse.failure("第" + i + "行，房间号不能重复");
                    }
//                    String idType = IdTypeEnum.getValueByName(vo.getIdTypeStr());
//                    if (StringUtils.isBlank(idType)) {
//                        return RestResponse.failure("第" + i + "行，请填写正确的证件类型");
//                    }
                    //String contractCode = resProjectService.getContractCodeByProjectId(UoneHeaderUtil.getProjectId());
                    //区域
                    ResPlanPartitionEntity plan = resPlanPartitionService.getOne(new QueryWrapper<ResPlanPartitionEntity>().eq("project_id", projectEntity.getId()).eq("name", vo.getPartitionName()));
                    ResSourceEntity sourceParam = new ResSourceEntity();
                    sourceParam.setProjectId(projectEntity.getId());
                    sourceParam.setSourceType(plan.getPropertyNature());
                    sourceParam.setCode(vo.getCode());
                    sourceParam.setPartitionId(plan.getId());
                    ResSourceEntity source = resSourceService.getResSourceEntity(sourceParam);
                    //判断房间是否存在
                    if (source == null) {
                        return RestResponse.failure("第" + i + "行房间号不存在");
                    }
                    //合同模版/费用信息
                    ContTempEntity searchVo=new ContTempEntity()
                            .setType("1")
                            .setSubjectType("0");
                    List<ContTempEntity> temps=contTempService.queryByParam(searchVo);
                    ContTempEntity temp = temps.get(0);
                    String tempId = temp.getId();
                    ResCostConfigureEntity costConfigure = configureService.queryByTemplateId(tempId).get(0);
                    if (null == temp) {
                        return RestResponse.failure("第" + i +"未配置合同模版！");
                    }
                    if (null == costConfigure) {
                        return RestResponse.failure("第" + i +"未配置费用信息！");
                    }

                    String payType = PayTypeEnum.getValueByName(vo.getPayTypeStr());
                    if (StringUtils.isBlank(payType)) {
                        return RestResponse.failure("第" + i + "行，请填写正确的缴费方式");
                    }
                    String sourceId = source.getId();
                    ResSourceConfigureEntity sourceConfigure = resSourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", sourceId));
                    if(sourceConfigure == null || (sourceConfigure != null && sourceConfigure.getPrice() ==null)){
                        return RestResponse.failure("第" + i + "行房源租金价格未配置");
                    }

                    if (!BaseConstants.BOOLEAN_OF_TRUE.equals(source.getPublishTarget())) {
                        return RestResponse.failure("第" + i + "行房源未发布");
                    }

                    if(!SourceStateEnum.UNRENT.getValue().equals(source.getState())){
                        return RestResponse.failure("第" + i + "行房源已被预定或出租");
                    }

                    if (SourceSignEnum.NOTRENT.getValue().equals(source.getSourceSign()) || SourceSignEnum.RESERVE.getValue().equals(source.getSourceSign())) {
                        return RestResponse.failure("第" + i + "行房源" + SourceSignEnum.getNameByValue(source.getSourceSign()) + ",不能签约");
                    }

                    String contractType = ContractTypeEnum.APARTMENT.getValue();

                    // 商业/公区
                    List<ContRentLadderEntity> rentLadderEntityList = null;


                    // 判断租赁周期是否符合项目租赁周期最小值
                    ResProjectInfoEntity projectInfoEntity = projectInfoService.getOne(new QueryWrapper<ResProjectInfoEntity>().eq("project_id", UoneHeaderUtil.getProjectId())); // 获取项目信息
                    if (null != projectInfoEntity && null != projectInfoEntity.getMinRentPeriod()) {
                        if (ContractUtil.period(DateUtil.parse(vo.getStartDate(), "yyyy-MM-dd"), DateUtil.parse(vo.getEndDate(), "yyyy-MM-dd"), projectInfoEntity.getMinRentPeriod())) {
                            return RestResponse.failure("租赁周期小于项目租赁周期最小值");
                        }
                    }
                    Date startDate = DateUtil.parse(vo.getStartDate(), "yyyy-MM-dd");
                    Date endDate = DateUtil.parse(vo.getEndDate(), "yyyy-MM-dd");
                    if(StringUtils.isNotBlank(vo.getFreeStartDate())){
                        Date freeStartDate = DateUtil.parse(vo.getFreeStartDate(), "yyyy-MM-dd");
                        if(startDate.compareTo(freeStartDate)>0 || freeStartDate.compareTo(endDate)>0){
                            return RestResponse.failure("第" + i + "行，免租起日要在合同租赁起止范围内");
                        }
                    }
                    if(StringUtils.isNotBlank(vo.getFreeEndDate())){
                        Date freeEndDate = DateUtil.parse(vo.getFreeEndDate(), "yyyy-MM-dd");
                        if(startDate.compareTo(freeEndDate)>0 || freeEndDate.compareTo(endDate)>0){
                            return RestResponse.failure("第" + i + "行，免租止日要在合同租赁起止范围内");
                        }
                    }
                    BigDecimal yearNum = null;//增幅间隔年数
                    BigDecimal yearIncrease = null;//年涨幅
                    if(StringUtils.isNotBlank(vo.getYearNum())){
                        yearNum = new BigDecimal(vo.getYearNum());
                    }
                    if(StringUtils.isNotBlank(vo.getYearIncrease())){
                        yearIncrease = new BigDecimal(vo.getYearIncrease());
                    }
                    String tel = vo.getTel();
                    RenterEntity renter = renterFegin.getByTelAndType(tel, RenterType.COMMON.getValue());
                    // 判断租客用户是否存在,不存在则新增租客用户
                    renter = contractService.judgeRenter(tel, renter, vo.getName(), "1", vo.getIdNo());
                    BigDecimal price = new BigDecimal(vo.getPrice());//合同承租价
                    // 新增合同 纸质合同编号保存到原来的系统合同编号中
                    ContContractEntity contract = new ContContractEntity();
                    contract.setContractType(contractType)
                            .setContractCode(vo.getPaperCode())
                            .setSignerId(renter.getId())
                            .setStartDate(DateUtil.beginOfDay(DateUtil.parse(vo.getStartDate(), "yyyy-MM-dd")))
                            .setEndDate(DateUtil.endOfDay(DateUtil.parse(vo.getEndDate(), "yyyy-MM-dd")))
                            .setSignType(SignTypeEnum.OFF_LINE.getValue()) // 线下签约
                            .setPayType(payType)
                            .setContractTempletId(temp.getId())
                            .setCostConfigureId(costConfigure.getId())
                            .setPaperCode(vo.getPaperCode())
                            .setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE)
                            .setSignDate(DateUtil.parse(vo.getSignDate(), "yyyy-MM-dd"))
                            .setFirstParty(vo.getFirstParty())//甲方
                            .setRentalArea(vo.getRentalArea())//租赁面积
                            .setLeaseUse(vo.getLeaseUse())//租赁用途
                            .setElectricityDeposit(BigDecimal.ZERO)//厂房保证金
                            .setPlantDeposit(BigDecimal.ZERO)//用电保证金
                            .setOtherLease(vo.getOtherLease())//其他租赁物
                            .setRemark(vo.getRemark())//备注
                            .setOriginalRent(price)//保存原始租金
                            //.setRentDate(vo.getRentDate())//交租日期
                            .setLeaseholdArea(vo.getLeaseholdArea())//计租面积
                            //.setRentFreePeriod(vo.getRentFreePeriod())//免租期
                            //.setFreeEndDate(DateUtil.parse(vo.getFreeEndDate(), "yyyy-MM-dd"))//免租止日
                            .setTaxPoint(vo.getTaxPint());//税点
                            if(StringUtils.isNotBlank(vo.getFreeStartDate())){
                                contract.setFreeStartDate(DateUtil.parse(vo.getFreeStartDate(), "yyyy-MM-dd"));//免租起日
                            }
                            if(StringUtils.isNotBlank(vo.getFreeEndDate())){
                                contract.setFreeEndDate(DateUtil.parse(vo.getFreeEndDate(), "yyyy-MM-dd"));//免租止日
                            }
                            contract.setTotalPrice(price);//合同承租价
                            contract.setYearNum(yearNum);//增幅间隔年数
                            contract.setYearIncrease(yearIncrease);//年涨幅
                            contract.setState(ContractStateEnum.STATUS_REVIEW.getValue());//默认待审核状态
//                    long diffDay = DateUtil.betweenDay(contract.getStartDate(), new Date(), false);
//                    if (diffDay >= 0) {
//                        contract.setState(ContractStateEnum.STATUS_TAKE_EFFECT.getValue());
//                    } else {
//                        contract.setState(ContractStateEnum.STATUS_NO_IN_TIME.getValue());
//                    }
                    contract.setManager(UoneSysUser.id());

                    // 判断是否重复生成合同 用的其实是纸质合同编号去检查重复
                    if (contractService.getOne(new QueryWrapper<ContContractEntity>().eq("contract_code", contract.getContractCode())) != null) {
                        return RestResponse.failure("该纸质合同编号已经存在,请检查！");
                    }

                    contractService.save(contract);

                    // 新增合同信息
                    ContContractInfoEntity contractInfo = contractInfoService.handleContractInfo(new ContContractInfoEntity(), contract.getId(), null, renter, temp, source.getProjectId());
                    contractInfoService.save(contractInfo);

                    // 新增合同房源关系
                    BigDecimal deposit = new BigDecimal(vo.getDeposit());//合同押金
                    ContContractSourceRelEntity contractSourceRelEntity = contractSourceRelService.addContContractSourceRel(contract, source, price, deposit, temp.getSubsidyPrice(), rentLadderEntityList);

                    // 生成PDF 暂时不生成pdf caizhanghe edit 20231026
//                    contTempService.createSignPdf(contract.getId());

//                    contContractService.auditContract(contract, source, renter, BaseConstants.BOOLEAN_OF_TRUE.equals(false));
                    //直接办理入住
//                    checkInHouseService.directChenkIn(contract.getId());
                    //修改房源状态为已出租
                    source.setState(SourceStateEnum.RENT.getValue()).updateById();
                    i++;
                    j++;
                }
            }else{
                return RestResponse.failure("导入失败！");
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return RestResponse.success();
    }

    public  boolean isPhoneNum(String phoneNum) {
        String regex = "^1[3-9][0-9]\\d{8}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNum);
        return matcher.matches();
    }

}
