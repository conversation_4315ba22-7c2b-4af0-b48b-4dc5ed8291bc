package cn.uone.business.bil.service;

import cn.uone.bean.entity.business.bil.BilInterfaceMsgEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-25
 */
public interface IBilInterfaceMsgService extends IService<BilInterfaceMsgEntity> {

    void addInterfaceMsg(String requestMsg, String responseMsg, String sign, String code, String note);

    BilInterfaceMsgEntity findBySign(String sign);
}
