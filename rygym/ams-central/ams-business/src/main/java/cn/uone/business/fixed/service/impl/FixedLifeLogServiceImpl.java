package cn.uone.business.fixed.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.fixed.FixedLifeLogEntity;
import cn.uone.business.fixed.dao.FixedLifeLogDao;
import cn.uone.business.fixed.service.IFixedLifeLogService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 生命周期日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Service
public class FixedLifeLogServiceImpl extends ServiceImpl<FixedLifeLogDao, FixedLifeLogEntity> implements IFixedLifeLogService {

    //分页查询
    @Override
    public IPage<FixedLifeLogEntity> page(Page page, FixedLifeLogEntity entity) {
        QueryWrapper<FixedLifeLogEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getSourceId())) {
            queryWrapper.eq("t_fixed_life_log.source_id", entity.getSourceId());
        }
        if (ObjectUtil.isNotEmpty(entity.getType())) {
            queryWrapper.eq("t_fixed_life_log.type", entity.getType());
        }
        if (ObjectUtil.isNotEmpty(entity.getName())) {
            queryWrapper.eq("t_fixed_life_log.name", entity.getName());
        }
        queryWrapper.orderByDesc("t_fixed_life_log.create_date");
        IPage iPage =  baseMapper.selectPage(page, queryWrapper);
        return iPage;
    }
    //列表查询
    @Override
    public List<FixedLifeLogEntity> list(FixedLifeLogEntity entity) {
        QueryWrapper<FixedLifeLogEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getSourceId())) {
            queryWrapper.eq("t_fixed_life_log.source_id", entity.getSourceId());
        }
        if (ObjectUtil.isNotEmpty(entity.getType())) {
            queryWrapper.eq("t_fixed_life_log.type", entity.getType());
        }
        if (ObjectUtil.isNotEmpty(entity.getName())) {
            queryWrapper.eq("t_fixed_life_log.name", entity.getName());
        }
        queryWrapper.orderByDesc("t_fixed_life_log.create_date");
        List<FixedLifeLogEntity> list =  baseMapper.selectList( queryWrapper);
        return list;
    }
}
