package cn.uone.business.face.service.impl;

import cn.uone.bean.entity.business.face.FaceAuditEntity;
import cn.uone.business.face.dao.FaceAuditDao;
import cn.uone.business.face.service.IFaceAuditService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人脸注册表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Service
public class FaceAuditServiceImpl extends ServiceImpl<FaceAuditDao, FaceAuditEntity> implements IFaceAuditService {

    @Override
    public List<Map<String, Object>> selectFaceAuditList(Map<String, Object> map) {
        return baseMapper.selectFaceAuditList(map);
    }
}
