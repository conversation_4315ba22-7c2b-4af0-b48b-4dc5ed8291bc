package cn.uone.business.rpt.service;

import cn.uone.bean.entity.business.report.vo.UnPayVo;
import cn.uone.bean.parameter.UnPayPo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
public interface IReportUnpayService {

    /**
     * 分页查询 未收的租金
     * @param page
     * @param param
     * @return
     */
    IPage<UnPayVo> selectPages(Page page, UnPayPo param);

    /**
     * 根据条件导出 未收的租金
     * @param param
     * @return
     */
    List<UnPayVo> export(UnPayPo param);

    BigDecimal getTotalUnpay(UnPayPo param);
}
