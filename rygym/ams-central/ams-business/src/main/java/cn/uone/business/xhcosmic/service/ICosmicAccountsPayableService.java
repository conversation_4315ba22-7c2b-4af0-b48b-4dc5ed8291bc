package cn.uone.business.xhcosmic.service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import cn.uone.bean.entity.business.xhcosmic.CosmicAccountsPayableEntity;
import cn.uone.bean.entity.business.xhcosmic.vo.AccountsPayableSearchVo;
import cn.uone.web.base.RestResponse;

/**
 * <p>
 * 金蝶(星瀚)系统财务应付单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
public interface ICosmicAccountsPayableService extends IService<CosmicAccountsPayableEntity> {

    void saveOrUpdateBatchWithItems(Collection<CosmicAccountsPayableEntity> entities);


    /**
     * 获取可以推送到金蝶的应付单票据信息并保存到数据库
     */
    List<CosmicAccountsPayableEntity> listCanPushCosmicAccountsPayable(Date startTime, Date endTime, List<String> ids);

    /**
     * 推送到金蝶
     *
     * @param entities
     * @return
     */
    RestResponse push(Collection<CosmicAccountsPayableEntity> entities);

    /**
     * 列表
     *
     * @param page     分页数据
     * @param searchVo 搜索字段
     * @return
     */
    IPage<CosmicAccountsPayableEntity> queryPage(Page<CosmicAccountsPayableEntity> page, AccountsPayableSearchVo searchVo);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    CosmicAccountsPayableEntity getInfo(String id);

    /**
     * 根据id进行推送
     *
     * @param ids
     * @return
     */
    RestResponse push(List<String> ids);
}
