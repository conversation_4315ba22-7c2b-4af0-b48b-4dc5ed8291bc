package cn.uone.business.pbls.controller;


import cn.uone.bean.entity.business.pbls.ParkingSpaceEntity;
import cn.uone.business.pbls.service.IParkingApplyService;
import cn.uone.business.pbls.service.IParkingSpaceService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@RestController
@RequestMapping("/parkingSpace")
public class ParkingSpaceController extends BaseController {

    @Autowired
    private IParkingSpaceService parkingSpaceService ;
    @Autowired
    private IParkingApplyService applyService ;

    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, ParkingSpaceEntity parkingSpaceEntity) {
        RestResponse response = new RestResponse();
        /**
         * 相关参数配置在该处处理
         */
        return response.setSuccess(true).setData(parkingSpaceService.findByCondition(page, parkingSpaceEntity));
    }

    @RequestMapping(value = "addParkSpace", method = RequestMethod.POST)
    @CacheLock(prefix = "addParkSpace", expire = 5)
    @UoneLog("停车位信息录入")
    public RestResponse add(ParkingSpaceEntity parkingSpaceEntity) throws Exception {
        parkingSpaceEntity.setProjectId(UoneHeaderUtil.getProjectId());
        parkingSpaceService.save(parkingSpaceEntity);
        return RestResponse.success();
    }

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        for (String id:ids) {
            int number = applyService.selectBySpaceId(id);
            if(number>0){
                return RestResponse.failure("已存在车辆信息的停车区域无法删除");
            }
        }
        parkingSpaceService.removeByIds(ids);
        return RestResponse.success();
    }

    @PostMapping("/edit")
    public RestResponse edit(ParkingSpaceEntity entity){
        entity.updateById();
        return RestResponse.success();
    }

    @PostMapping("/getPriceById")
    public RestResponse edit(String id){
        ParkingSpaceEntity entity = parkingSpaceService.getById(id);
        return RestResponse.success().setData(entity.getPrice());
    }
}
