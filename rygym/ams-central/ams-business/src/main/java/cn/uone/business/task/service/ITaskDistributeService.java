package cn.uone.business.task.service;


import cn.uone.bean.entity.business.task.TaskDistributeEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 任务派发表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-27
 */
public interface ITaskDistributeService extends IService<TaskDistributeEntity> {


    Integer delDistributeByPlanId(String planId);

    List<String> getExecutorIds(String planId);

    IPage<TaskDistributeEntity> getExecutorByPlanId(Page page, String planId);

}
