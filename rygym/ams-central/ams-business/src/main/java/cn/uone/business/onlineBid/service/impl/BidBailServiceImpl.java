package cn.uone.business.onlineBid.service.impl;

import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.afforest.VegetationEntity;
import cn.uone.bean.entity.business.onlineBid.BidBailEntity;
import cn.uone.business.onlineBid.dao.BidBailDao;
import cn.uone.business.onlineBid.service.IBidBailService;
import cn.uone.business.sys.service.ISysFileService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class BidBailServiceImpl extends ServiceImpl<BidBailDao, BidBailEntity> implements IBidBailService {

    @Autowired
    private ISysFileService fileService;


    @Override
    public boolean saveOrUpdate(BidBailEntity entity,List<MultipartFile> image,SysFileTypeEnum sysFileTypeEnum) {
        this.saveOrUpdate(entity);
        if(image != null &&  image.size() > 0){
            fileService.delFileByFromIdAndType(entity.getId(), sysFileTypeEnum);
            fileService.saveFiles(image, entity.getId(), sysFileTypeEnum.getValue());
        }
        return false;
    }

    @Override
    public String getIdByApplyId(String applyId) {
        return baseMapper.getIdByApplyId(applyId);
    }


}
