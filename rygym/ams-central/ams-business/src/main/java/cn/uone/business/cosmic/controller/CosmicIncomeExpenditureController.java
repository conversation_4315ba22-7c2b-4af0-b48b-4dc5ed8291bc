package cn.uone.business.cosmic.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.afforest.VegetationEntity;
import cn.uone.bean.entity.business.cosmic.CosmicIncomeExpenditureEntity;
import cn.uone.bean.entity.business.supplies.BorrowEntity;
import cn.uone.bean.entity.business.supplies.CategoryEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.business.cosmic.service.ICosmicIncomeExpenditureService;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@RestController
@RequestMapping("/cosmic/incomeExpenditure")
public class CosmicIncomeExpenditureController extends BaseController {

    @Autowired
    private ICosmicIncomeExpenditureService service;

    @GetMapping("/page")
    public RestResponse page(Page<CosmicIncomeExpenditureEntity> page, CosmicIncomeExpenditureEntity entity){
        QueryWrapper<CosmicIncomeExpenditureEntity> wrapper = new QueryWrapper<>();
        if(StrUtil.isNotBlank(entity.getTypeName())){
            wrapper.like("typeName","%"+entity.getTypeName()+"%");
        }
        IPage<CosmicIncomeExpenditureEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/save")
    public RestResponse save(CosmicIncomeExpenditureEntity entity){
        boolean c = check("",entity.getOrderType(),entity.getType());
        if(c){
            return RestResponse.failure("同类别的账单类型不能重复！！！");
        }
        entity.insertOrUpdate();
        return RestResponse.success();
    }
    @PostMapping("/edit")
    public RestResponse edit(CosmicIncomeExpenditureEntity entity){
        boolean c =  check(entity.getId(),entity.getOrderType(),entity.getType());
        if(c){
           return RestResponse.failure("同类别的账单类型不能重复！！！");
        }
        entity.updateById();
        return RestResponse.success();
    }

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }

    public boolean  check(String id,String orderType,String type){
        QueryWrapper<CosmicIncomeExpenditureEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("order_type",orderType);
        wrapper.eq("type",type);
        if(StrUtil.isNotBlank(id)){
            wrapper.ne("id",id);
        }
        List<CosmicIncomeExpenditureEntity> p = service.list(wrapper);
        if(p==null||p.size()==0){
            return false;
        }else{
            return true;
        }

    }


    /**获取金蝶数据*/
    @RequestMapping("/getByKingdee")
    public RestResponse getByKingdee() {
        List<Map<String,String>> data=new ArrayList<>();
        Map<String,String> m=new HashMap<>();
        m.put("value", "F01029");
        m.put("name","职工薪酬1");
        m.put("attribute","4");
        m.put("paymentNumber","6666");
        data.add(m);
        Map<String,String> m2=new HashMap<>();
        m2.put("value", "F01039");
        m2.put("name","职工薪酬2");
        m2.put("attribute","4");
        m2.put("paymentNumber","8888");
        data.add(m2);
        return RestResponse.success().setData(data);
    }

}
