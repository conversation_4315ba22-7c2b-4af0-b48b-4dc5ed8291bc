package cn.uone.business.rpt.service;

import cn.uone.bean.entity.business.report.vo.ConfirmAccountVo;
import cn.uone.bean.parameter.ConfirmAccountPo;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface IReportConfirmAccountService  extends IService<ConfirmAccountVo> {

    IPage<ConfirmAccountVo> findConfirmByCondition(Page page, ConfirmAccountPo confirmAccountPo);

    List<ConfirmAccountVo> findConfirmByCondition(ConfirmAccountPo confirmAccountPo);

    BigDecimal getArriveTotal(ConfirmAccountPo confirmAccountPo);

    IPage<ConfirmAccountVo> findCcbCondition(Page page, ConfirmAccountPo confirmAccountPo);

    List<ConfirmAccountVo> findCcbCondition(ConfirmAccountPo confirmAccountPo);

    BigDecimal getCcbTotal(ConfirmAccountPo confirmAccountPo);

    IPage<ConfirmAccountVo> findUnionPayCondition(Page page, ConfirmAccountPo confirmAccountPo);

    List<ConfirmAccountVo> findUnionPayCondition(ConfirmAccountPo confirmAccountPo);

    BigDecimal getUnionPayTotal(ConfirmAccountPo confirmAccountPo);

    RestResponse confirmAccount(List<String> orderIds, String transferId, Date arriveTime,String bankSerialCode);

    Date getLatelyPayTime(String merchantId, String transferTime, String transferPayment, String projectId);
}
