package cn.uone.business.pbls.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.order.PayWayEnum;
import cn.uone.bean.entity.business.bil.AccountBalanceEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.pbls.ParkingRecordEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.tpi.Cotto.CottoCarVo;
import cn.uone.business.bil.service.IAccountBalanceService;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.pbls.dao.ParkingRecordDao;
import cn.uone.business.pbls.service.IParkingRecordService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.tpi.ICottoFegin;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-24
 */
@Service
public class ParkingRecordServiceImpl extends ServiceImpl<ParkingRecordDao, ParkingRecordEntity> implements IParkingRecordService {


    @Autowired
    private ICottoFegin cottoFegin ;
    @Autowired
    private IAccountBalanceService accountBalanceService;
//    @Autowired
//    private IWxPayFegin wxPayFegin;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IBilOrderItemService bilOrderItemService;

    @Override
    public Map<String,Object> toPayParking(String applyId, Date startTime, Date endTime, BigDecimal allRent,int month,String type){
        //设置开始时间00:00:00
        startTime = getZeroHour(startTime);
        //设置结束时间时间23:59:59
        endTime = getEndHour(endTime);
        Map<String,Object> maps = Maps.newHashMap();
        // 1、获取 t_parking_record 表记录
        ParkingRecordEntity parkingRecordEntity = getOne(new QueryWrapper<ParkingRecordEntity>().eq("apply_id", applyId));

        // 判断是否后台审核，后台审核通过直接更新状态和同步科拓信息，其他为续费逻辑
        if("审核通过缴费".equals(type)){
            parkingRecordEntity.setStatus("1");
            CottoCarVo cottoCarVo = createCottoCarVo(month,allRent.intValue(),startTime,endTime,parkingRecordEntity.getCardId());
            // 3、调用科拓接口，根据返回状态判断后续操作
            JSONObject json = cottoFegin.payCarCardFee(cottoCarVo);
            if("0".equals(json.get("resCode"))){
                maps.put("success","缴费成功");
                maps.put("code",1);
            }else{
                maps.put("error",json.get("resMsg"));
                maps.put("code",-1);
            }
            parkingRecordEntity.insertOrUpdate();
            return maps;
        }

        //续费逻辑处理
        parkingRecordEntity.setStatus("4");
        parkingRecordEntity.insertOrUpdate();

        QueryWrapper query = new QueryWrapper();
        query.eq("order_type", OrderTypeEnum.PARK.getValue());
        query.eq("contract_id",applyId);
        query.eq("pay_state", PayStateEnum.NOPAY.getValue());
        BilOrderEntity order = bilOrderService.getOne(query);
        if(order != null){
            bilOrderItemService.removeByCondition(new BilOrderItemEntity().setOrderId(order.getId()));
            bilOrderService.removeById(order.getId());
        }
        order = toCreateBilEntity(startTime,endTime,allRent,parkingRecordEntity.getUserId(),parkingRecordEntity.getNumberplate(),applyId);
        if("微信续费".equals(type)){
            order.setPayWay(PayWayEnum.WECHAT.getValue());
            RenterEntity renter = null;
            try {
                renter = renterFegin.getById(parkingRecordEntity.getUserId());
            } catch (Exception e) {
                e.printStackTrace();
                maps.put("error","租客不存在");
                maps.put("code",-1);
                return maps;
            }
//            RestResponse payInfo = wxPayFegin.prepayWithRequestPayment(order.getCode(),parkingRecordEntity.getNumberplate()+"停车续费",order.getPayment(),renter.getOpenid());
//            maps.put("code",2);
//            maps.put("success","待续费");
//            maps.put("payInfo",payInfo);
        }else{
            if(parkingRecordEntity.getEndTime().compareTo(new Date()) < 0) {
                parkingRecordEntity.setStartTime(startTime);
            }
            parkingRecordEntity.setEndTime(endTime);
            parkingRecordEntity.setStatus("1");
            order.setPayWay(PayWayEnum.OFFLINE.getValue());
            order.setPayState(PayStateEnum.PAYCONFIR.getValue());
            if(type.equals("钱包续费")){
                order.setPayWay(PayWayEnum.BALANCEPAY.getValue());
                // 5、新增 t_bil_order表记录
                AccountBalanceEntity accountBalanceEntity = accountBalanceService.getOne(new QueryWrapper<AccountBalanceEntity>().eq("signer_id", UoneSysUser.id()).eq("order_type", "555"));
                BigDecimal balance = accountBalanceEntity.getBalance();
                accountBalanceEntity.setBalance(balance.subtract(allRent));
                accountBalanceEntity.insertOrUpdate();
            }else if(type.equals("线下续费")){
                order.setPayWay(PayWayEnum.OFFLINE.getValue());
                // 5、新增 t_bil_order表记录
                //toCreateBilEntity(startTime,endTime,allRent,parkingRecordEntity.getUserId(),parkingRecordEntity.getNumberplate(),applyId);
            }
            // 2、组装调用科拓固定车充值接口vo
            CottoCarVo cottoCarVo = createCottoCarVo(month,allRent.intValue(),startTime,endTime,parkingRecordEntity.getCardId());
            // 3、调用科拓接口，根据返回状态判断后续操作
            JSONObject json = cottoFegin.payCarCardFee(cottoCarVo);
            if("0".equals(json.get("resCode"))){
                maps.put("success","缴费成功");
                maps.put("code",1);
            }else{
                maps.put("error",json.get("resMsg"));
                maps.put("code",-1);
            }
            parkingRecordEntity.insertOrUpdate();
        }
        bilOrderService.updateById(order);
        return maps ;
    }






    @Override
    public int getByParkSpaceId(String parkSpaceId) {
        return baseMapper.getByParkSpaceId(parkSpaceId);
    }

    private BilOrderEntity toCreateBilEntity(Date startTime, Date endTime, BigDecimal allRent , String userId, String numberPlate, String applyId){
        BilOrderEntity orderEntity  = new BilOrderEntity();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");//设置日期格式
        String code = "TC"+ LocalDateTime.now().format(fmt);
        orderEntity.setCode(code);
        orderEntity.setPayablePayment(allRent);//应付金额
        orderEntity.setActualPayment(allRent);//支付金额
        orderEntity.setPayment(allRent);//账单金额
        orderEntity.setPayState("10");//支付状态
        orderEntity.setPayTime(new Date());//当前时间
        orderEntity.setOrderType("448");//账单类型
        orderEntity.setPush(true);
        orderEntity.setPayerId(userId);//付款方
        orderEntity.setPush(true);//是否推送
        orderEntity.setSourceId(numberPlate);//车牌号存入房源号
        orderEntity.setContractId(applyId);//申请表id存入合同id
        orderEntity.insert();
        BilOrderItemEntity item = new BilOrderItemEntity();
        item.setOrderId(orderEntity.getId());//主表ID
        item.setStartTime(startTime);//开始时间
        item.setEndTime(endTime);//结束时间
        item.setPayment(allRent);//金额
        item.setOrderItemType("448");//类型
        item.insert();
        return orderEntity;
    }

    @Override
    public CottoCarVo createCottoCarVo(int chargeNum, int amount, Date startTime , Date endTime, int cardId){
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");//设置日期格式
        String code = "TC"+ LocalDateTime.now().format(fmt);
        CottoCarVo cottoCarVo = new CottoCarVo() ;
        cottoCarVo.setUserName(StrUtil.isBlank(UoneSysUser.loginName())?"车主":UoneSysUser.loginName());
        cottoCarVo.setUserId(0);
        cottoCarVo.setPayChannel(1011);
        cottoCarVo.setCardId(cardId);
        cottoCarVo.setChargeMethod(1);
        cottoCarVo.setChargeNumber(chargeNum);
        cottoCarVo.setAmount(amount);
        cottoCarVo.setFreeNumber(0);
        cottoCarVo.setValidFrom(getTimeStamp(startTime));
        cottoCarVo.setValidTo(getTimeStamp(DateUtil.endOfDay(endTime)));
        cottoCarVo.setCreateTime(getTimeStamp(DateUtil.date()));
        cottoCarVo.setOrderNo(code);
        return cottoCarVo ;
    }

    public static String getTimeStamp(Date date) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    public Date getZeroHour(Date date){
        // 创建calendar对象
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        // 将小时设置为0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟设置为0
        calendar.set(Calendar.MINUTE, 0);
        // 将秒设置为0
        calendar.set(Calendar.SECOND, 0);
        // 将毫秒设置为0
        calendar.set(Calendar.MILLISECOND, 0);
        //获取修改后的日期
        Date previousDate=calendar.getTime();
        return previousDate;
    }

    public Date getEndHour(Date date){
        // 创建calendar对象
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        // 将小时设置为23
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        // 将分钟设置为59
        calendar.set(Calendar.MINUTE, 59);
        // 将秒设置为59
        calendar.set(Calendar.SECOND, 59);
        // 将毫秒设置为0
        calendar.set(Calendar.MILLISECOND, 0);
        //获取修改后的日期
        Date previousDate=calendar.getTime();
        return previousDate;
    }
}
