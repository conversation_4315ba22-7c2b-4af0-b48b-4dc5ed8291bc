package cn.uone.business.base.dao;

import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface BaseEnterpriseDao extends BaseMapper<BaseEnterpriseEntity> {

    // 查询机构
    List<BaseEnterpriseEntity> selectByList(@Param("map") Map<String, Object> map);

    // 查询机构（分页）
    IPage<BaseEnterpriseEntity> selectByIPage(Page<BaseEnterpriseEntity> page, @Param("map") Map<String, Object> map);

    List<Map<String, Object>> selectCompanys();
}
