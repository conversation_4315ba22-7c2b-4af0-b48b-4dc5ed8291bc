package cn.uone.business.bil.controller;


import cn.uone.bean.entity.business.bil.BilInterfaceMsgEntity;
import cn.uone.business.bil.service.IBilInterfaceMsgService;
import cn.uone.fegin.bus.IBilInterfaceMsgFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-25
 */
@RestController
@RequestMapping("/bil/interfaceMsg")
public class BilInterfaceMsgController extends BaseController implements IBilInterfaceMsgFegin {

    @Autowired
    private IBilInterfaceMsgService bilInterfaceMsgService;

    /**
     * 查询
     *
     * @return
     */
    @Override
    @RequestMapping("/addInterfaceMsg")
    @UonePermissions
    public void addInterfaceMsg(@RequestBody BilInterfaceMsgEntity entity) {
        bilInterfaceMsgService.addInterfaceMsg(entity.getRequestMsg(), entity.getResponseMsg(),
                entity.getSign(), entity.getCode(), entity.getNote());
    }
}
