package cn.uone.business.fixed.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.fixed.FixedMaintenancePlanEntity;
import cn.uone.bean.entity.business.fixed.FixedMaintenanceTaskEntity;
import cn.uone.bean.entity.business.fixed.vo.FixedMaintenanceTaskVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.business.fixed.service.IFixedMaintenancePlanService;
import cn.uone.business.fixed.service.IFixedMaintenanceTaskService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 维保任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@RestController
@RequestMapping("/fixed/fixed-maintenance-task-entity")
public class FixedMaintenanceTaskController extends BaseController {
    @Autowired
    IFixedMaintenanceTaskService fixedMaintenanceTaskService;
    @Autowired
    IFixedMaintenancePlanService fixedMaintenancePlanService;
    @Autowired
    ISysFileService sysFileService;
    /**
     * 获取信息
     *
     * @param fixedMaintenanceTaskVo
     * @return 维保方案列表 分页
     */
    @GetMapping("/page")
    public RestResponse page(Page page, FixedMaintenanceTaskVo fixedMaintenanceTaskVo) {
        IPage<FixedMaintenanceTaskVo> iPage = fixedMaintenanceTaskService.page(page, fixedMaintenanceTaskVo);
        return RestResponse.success().setData(iPage);
    }

    /**
     * 获取信息
     *
     * @param id 主键
     * @return 维保方案信息
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        FixedMaintenanceTaskEntity info = fixedMaintenanceTaskService.getById(id);
        return RestResponse.success().setData(info);
    }

    /**
     * 新增
     *
     * @param fixedMaintenanceTaskEntity 参数
     * @return
     */
    @PostMapping("/save")
    public RestResponse save(FixedMaintenanceTaskEntity fixedMaintenanceTaskEntity) {
        if(fixedMaintenanceTaskService.save(fixedMaintenanceTaskEntity)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    /**
     * 修改
     *
     * @param fixedMaintenanceTaskEntity 参数
     * @return
     */
    @PostMapping("/edit")
    public RestResponse edit(FixedMaintenanceTaskEntity fixedMaintenanceTaskEntity) {
        if(fixedMaintenanceTaskService.updateById(fixedMaintenanceTaskEntity)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 执行任务
     *
     * @param id 参数
     * @return
     */
    @PostMapping("/startTask")
    public RestResponse startTask(@Param(value = "id")String id) {
        FixedMaintenanceTaskEntity fixedMaintenanceTaskEntity = fixedMaintenanceTaskService.getById(id);
        fixedMaintenanceTaskEntity.setState("1");
        fixedMaintenanceTaskEntity.setStartTime(new Date());
        if(fixedMaintenanceTaskService.updateById(fixedMaintenanceTaskEntity)){
            FixedMaintenancePlanEntity plan = fixedMaintenancePlanService.getById(fixedMaintenanceTaskEntity.getPlanId());
            if("0".equals(plan.getPlanState())){
                plan.setPlanState("1");
            }
            fixedMaintenancePlanService.updateById(plan);
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 执行任务
     *
     * @param id 参数
     * @return
     */
    @PostMapping("/endTask")
    public RestResponse endTask(@Param(value = "id")String id) {
        FixedMaintenanceTaskEntity fixedMaintenanceTaskEntity = fixedMaintenanceTaskService.getById(id);
        fixedMaintenanceTaskEntity.setState("2");
        fixedMaintenanceTaskEntity.setEndTime(new Date());
        long offsetSecond = DateUtil.between(fixedMaintenanceTaskEntity.getStartTime(),
                fixedMaintenanceTaskEntity.getEndTime(), DateUnit.SECOND,true);
        double hours = (double) offsetSecond / 3600;
        hours = Math.round(hours * 10) / 10.0;
        fixedMaintenanceTaskEntity.setRealHours(Double.toString(hours));
        if(fixedMaintenanceTaskService.updateById(fixedMaintenanceTaskEntity)){
            QueryWrapper query = new QueryWrapper();
            query.eq("plan_id",fixedMaintenanceTaskEntity.getPlanId());
            query.eq("state","1");
            List<FixedMaintenanceTaskEntity> list = fixedMaintenanceTaskService.list(query);
            if(list == null || list.size()==0){
                FixedMaintenancePlanEntity plan = fixedMaintenancePlanService.getById(fixedMaintenanceTaskEntity.getPlanId());
                plan.setPlanState("2");
                fixedMaintenancePlanService.updateById(plan);
            }
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        if(fixedMaintenanceTaskService.removeByIds(ids)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    @GetMapping("/getFiles")
    public RestResponse getFiles(@Param(value = "id")String id) {
        List<SysFileEntity> files = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.MAINTENANCE_TASK);
        return RestResponse.success().setData(files);
    }
}
