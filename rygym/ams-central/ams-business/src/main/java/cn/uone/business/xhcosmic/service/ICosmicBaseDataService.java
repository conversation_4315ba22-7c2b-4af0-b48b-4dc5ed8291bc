package cn.uone.business.xhcosmic.service;

import cn.uone.bean.entity.business.xhcosmic.CosmicBaseDataEntity;
import cn.uone.bean.entity.tpi.cosmic.BaseQueryPageVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 金蝶财务系统基础资料表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
public interface ICosmicBaseDataService extends IService<CosmicBaseDataEntity> {

    List<CosmicBaseDataEntity> insertBatch(List<CosmicBaseDataEntity> entities);

    CosmicBaseDataEntity getByTypeAndNumber(String type, String number);

    /**
     * 在数据库没有查到的时候去查金蝶基础资料(保存并返回想要的编码.类型编码)
     *
     * @param type        我们定义的类型 CosmicBaseDataTypeEnum
     * @param number      编码
     * @param groupNumber 分类编码
     * @return 存到数据库后的实体类集合
     */
    List<CosmicBaseDataEntity> listFromCosmicApi(String name, String type, String number, String groupNumber, String parentNumber,
                                                 String modifyTime, String orgId, String orgGuId,
                                                 String attribute, boolean isDept, Integer fisAccounting,
                                                 Collection<String> numbers,Collection<String> names);


    List<CosmicBaseDataEntity> listByTypeAndGroupNumber(String type, String groupNumber);

    List<CosmicBaseDataEntity> listByTypeAndNumbers(String type, Collection<String> numbers);

    /**
     * 按照类型和names查找
     * @param type
     * @param names
     * @return
     */
    List<CosmicBaseDataEntity> listByTypeAndName(String type, Collection<String> names);

    default List<CosmicBaseDataEntity> getByType(String type) {
        return listByTypeAndGroupNumber(type, null);
    }

    /**
     * 批量保存 基础资料 结算方式
     *
     * @param vo 金蝶返回的分页格式
     */
    <T> List<CosmicBaseDataEntity> insertBatchForSettleType(BaseQueryPageVo<T> vo);

    /**
     * 批量保存 基础资料-收支项目
     *
     * @param ts
     */
    <T> List<CosmicBaseDataEntity> insertBatchForExpenseItem(List<T> ts);


    /**
     * 批量保存 基础资料-税率
     *
     * @param vo 金蝶返回的分页方式
     */
    <T> List<CosmicBaseDataEntity> insertBatchForTaxRate(BaseQueryPageVo<T> vo);

    <T> List<CosmicBaseDataEntity> insertBatchForBank(BaseQueryPageVo<T> vo);


    /**
     * 批量保存 基础资料-项目
     *
     * @param vo 金蝶返回的分页方式
     */
    <T> List<CosmicBaseDataEntity> insertBatchForProject(BaseQueryPageVo<T> vo);

    /**
     * 批量保存 基础资料-行政组织
     *
     * @param vo 金蝶返回的分页方式
     */
    <T> List<CosmicBaseDataEntity> insertBatchForAdminOrg(BaseQueryPageVo<T> vo);

    /**
     * 批量保存 基础资料-收款类型
     *
     * @param vo
     * @param <T>
     * @return
     */
    <T> List<CosmicBaseDataEntity> insertBatchForPaymentBillType(BaseQueryPageVo<T> vo);
}
