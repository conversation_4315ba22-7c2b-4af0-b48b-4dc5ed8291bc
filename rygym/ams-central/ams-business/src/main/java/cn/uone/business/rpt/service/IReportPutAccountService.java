package cn.uone.business.rpt.service;

import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.bean.entity.business.report.ReportPutAccountEntity;
import cn.uone.bean.entity.business.report.vo.PutAccountVo;
import cn.uone.bean.parameter.PutAccountPo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 收费台账 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
public interface IReportPutAccountService extends IService<ReportPutAccountEntity> {

    /***
     * 生成报表数据
     * @param date
     */
    void generate(Date date);


    IPage<PutAccountVo> selectPages(Page page, PutAccountPo param);


    List<PutAccountVo> export(PutAccountPo param);

    IPage<BilOrderVo> selectOrderPage(Page page, BilOrderSearchVo search);


}
