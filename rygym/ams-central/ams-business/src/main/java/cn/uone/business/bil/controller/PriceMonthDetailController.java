package cn.uone.business.bil.controller;


import cn.uone.bean.entity.business.bil.PriceMonthDetailEntity;
import cn.uone.bean.entity.business.bil.PriceStrategyEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.business.bil.service.IPriceMonthDetailService;
import cn.uone.business.bil.service.IPriceStrategyService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *  价格策略指定月份明细
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@RestController
@RequestMapping("/price-month-detail")
public class PriceMonthDetailController extends BaseController {

    @Autowired
    private IPriceStrategyService priceStrategyService;

    @Autowired
    IContContractService contractService;

    @Autowired
    private IPriceMonthDetailService priceMonthDetailService;

    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, PriceMonthDetailEntity entity) {
        RestResponse response = new RestResponse();
        IPage<PriceMonthDetailEntity> pageList = priceMonthDetailService.findByCondition(page, entity);
        return response.setSuccess(true).setData(pageList);
    }

    /**
     * 查询
     *
     * @return
     */
    @RequestMapping("/getList")
    public RestResponse getList(String priceStrategyId) {
        RestResponse response = new RestResponse();
        List<PriceMonthDetailEntity> list = priceMonthDetailService.getList(priceStrategyId);
        return response.setSuccess(true).setData(list);
    }

}
