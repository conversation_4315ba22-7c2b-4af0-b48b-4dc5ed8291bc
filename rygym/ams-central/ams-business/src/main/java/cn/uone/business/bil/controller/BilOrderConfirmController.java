package cn.uone.business.bil.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.bean.entity.business.bil.BilOrderConfirmEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilTransferEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.kingdee.KingdeeReceiptEntity;
import cn.uone.business.bil.service.IBilOrderConfirmService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.bil.service.IBilTransferService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.kingdee.service.IKingdeeApiService;
import cn.uone.business.kingdee.service.IKingdeeReceiptItemService;
import cn.uone.business.kingdee.service.IKingdeeReceiptService;
import cn.uone.business.res.service.IResProjectCompanyService;
import cn.uone.fegin.tpi.IXyKingdeeFegin;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 退款/收款确认表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@RestController
@RequestMapping("/bil/order/confirm")
public class BilOrderConfirmController extends BaseController {

    @Autowired
    private IBilOrderConfirmService service;
    @Autowired
    private IBilOrderService orderService;
    @Autowired
    private IResProjectCompanyService companyService;
    @Autowired
    private IKingdeeApiService kingdeeApiService;
    @Autowired
    private IContContractService contractService;
    @Autowired
    private IKingdeeReceiptService kingdeeReceiptService;
    @Autowired
    private IKingdeeReceiptItemService kingdeeReceiptItemService;
    @Autowired
    private IXyKingdeeFegin xyKingdeeFegin;
    @Autowired
    private IBilTransferService bilTransferService;

    @GetMapping("/page")
    @UoneLog("线下支付确认-数据加载/筛选")
    public RestResponse page(Page<BilOrderConfirmEntity> page, BilOrderConfirmEntity p) {
        p.setProjectId(UoneHeaderUtil.getProjectId());
        StringBuilder confirmIds =new StringBuilder();
        //根据合同编号，找到对应的order confirm的id
        if(StrUtil.isNotBlank(p.getContractCode())){
            //首先，用contractCoce找出orderId
            String contractId=contractService.getOne(new QueryWrapper<ContContractEntity>().eq("contract_code",p.getContractCode())).getId();
            List<BilOrderEntity> orderList=orderService.getByContractId(contractId);
            //取得所有orderConfirm的orderId，再遍历以上bilOrderEntity list中的id，判断是否包含，如有包含关系，将confirm id装入string中，
            List<BilOrderConfirmEntity> confirmList=service.getIdList();

            for(BilOrderConfirmEntity entity:confirmList){
                for(BilOrderEntity orderEntity:orderList){
                    String orderId=orderEntity.getId();
                    if(entity.getOrderId().contains(orderId)){
                        confirmIds.append(entity.getId());
                        System.out.println(entity.getId());
                        break;
                    }
                }
            }
        }
        IPage pa=service.page(page,p,confirmIds.toString());
        return RestResponse.success().setData(pa);

    }

    @GetMapping("/orderList")
    @UoneLog("线下支付确认-查看订单列表")
    public RestResponse orderList(String id){
        BilOrderConfirmEntity confirmEntity =  service.getById(id);
        List<BilOrderEntity> orders = orderService.getByIds(Arrays.asList(confirmEntity.getOrderId().split(",")));
        return RestResponse.success().setData(orders);
    }

    @PostMapping("/confirm")
    @UoneLog("线下支付确认-确认支付/退款")
    public RestResponse confirm(String id, @RequestParam(required = false) String companyId) {
        RestResponse res = new RestResponse();
        try{
            res = orderService.confirm(id,companyId);
        }catch (Exception e){
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
       return res;
    }

    @PostMapping("/del")
    @UoneLog("线下支付确认-删除申请")
    @Transactional
    public RestResponse del(String id){
        BilOrderConfirmEntity confirmEntity = service.getById(id);
        QueryWrapper query = new QueryWrapper();
        query.eq("rel_id",id);
        KingdeeReceiptEntity receiptEntity = kingdeeReceiptService.getOne(query);
        if(receiptEntity != null){
            boolean isExist = xyKingdeeFegin.isExist(receiptEntity.getId());
            if(isExist){
                return RestResponse.failure("财务系统存在该笔收款单，不能删除！");
            }
            Map map = Maps.newHashMap();
            map.put("receipt_id",receiptEntity.getId());
            kingdeeReceiptItemService.removeByMap(map);
            kingdeeReceiptService.removeById(receiptEntity.getId());
        }
        List<BilOrderEntity> orders = orderService.getByIds(Arrays.asList(confirmEntity.getOrderId().split(",")));
        for (BilOrderEntity order:orders) {
//            order.setActualPayment(null);
//
//            order.setPayTime(null);
//            order.setRemark(null);
//            order.setPayWay(null);
//            order.setTradeCode(null);
//            order.setApprovalState(null);
//
             //set方法传null时，update没效果，改成调update语句来设置空值
              order.setPayState(PayStateEnum.NOPAY.getValue());
             try {
                 order.updateById();
              int a=orderService.setPartNull(order.getId());
                 System.out.println(a);
                 BilTransferEntity bilTransfer = bilTransferService.getOne(new QueryWrapper<BilTransferEntity>().eq("order_id",order.getId()));
                 if(bilTransfer != null){//删除时将转账记录也删除 caizhanghe edit 2024-04-15
                     bilTransferService.removeById(bilTransfer.getId());
                 }
             }catch (Exception e){
                return RestResponse.failure("删除失败");
             }
        }
        if(service.removeById(id)){
            return RestResponse.success("删除成功");
        }
        return RestResponse.failure("删除失败");
    }
}
