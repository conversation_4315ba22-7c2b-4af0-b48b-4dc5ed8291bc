package cn.uone.business.rpt.dao;

import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.rpt.RptRentAccountEntity;
import cn.uone.bean.parameter.PutAccountPo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-28
 */
public interface RptRentAccountDao extends BaseMapper<RptRentAccountEntity> {

    IPage<RptRentAccountEntity> page(Page page, @Param("po") PutAccountPo po);

    List<RptRentAccountEntity> page(@Param("po") PutAccountPo po);

    List<ContContractEntity> selectContByRent(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    void delByMonth(@Param("month")String month);

}
