package cn.uone.business.equ.service.impl;

import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.equ.EquDeviceEntity;
import cn.uone.business.equ.dao.EquDeviceDao;
import cn.uone.business.equ.service.IEquDeviceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-19
 */
@Service
public class EquDeviceServiceImpl extends ServiceImpl<EquDeviceDao, EquDeviceEntity> implements IEquDeviceService {

    @Autowired
    private ISysFileService fileService;

    @Override
    public Page<EquDeviceEntity> page(Page page, EquDeviceEntity entity) {
        entity.setProjectId(UoneHeaderUtil.getProjectId());
        DataScope dataScope = DataScope.newDataScope(UoneSysUser.id());
        dataScope.setProAlias("t");
        return baseMapper.list(page, dataScope ,entity);
    }

    @Override
    public boolean saveOrUpdate(EquDeviceEntity entity, List<MultipartFile> files,List<MultipartFile> image) {
        this.saveOrUpdate(entity);
        if(files != null && files.size()>0){
            fileService.delFileByFromIdAndType(entity.getId(), SysFileTypeEnum.EQU_DEVICE_FILE);
            fileService.saveFiles(files, entity.getId(), SysFileTypeEnum.EQU_DEVICE_FILE.getValue());
        }
        if(image != null &&  image.size() > 0){
            fileService.delFileByFromIdAndType(entity.getId(), SysFileTypeEnum.FACILITY_PICTURE);
            fileService.saveFiles(image, entity.getId(), SysFileTypeEnum.FACILITY_PICTURE.getValue());
        }
        return true;
    }
}
