package cn.uone.business.bil.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.order.*;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalDetailEntity;
import cn.uone.bean.entity.business.bil.*;
import cn.uone.bean.entity.business.bil.vo.BilInvestSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilInvestVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractInfoEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.cont.vo.ContContractVo;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.res.vo.SelectVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.apro.dao.ApprovalCommitDao;
import cn.uone.business.apro.service.IApprovalDetailService;
import cn.uone.business.bil.service.*;
import cn.uone.business.cont.service.IContContractInfoService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.cont.service.IContRentLadderService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.ExcelRender;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-15
 */
@RestController
@RequestMapping("/bil/invest")
@Slf4j
public class BilInvestController extends BaseController {

    @Autowired
    private IBilInvestService bilInvestService;

    @Autowired
    private IContContractService contContractService;

    @Autowired
    private IResProjectService resProjectService;

    @Autowired
    private IContContractSourceRelService contContractSourceRelService;
    @Autowired
    private IApprovalDetailService approvalDetailService;
    @Autowired
    private IResSourceService resSourceService;
    @Resource
    private ApprovalCommitDao approvalCommitDao;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private IContContractInfoService contractInfoService;
    @Autowired
    private IContRentLadderService contRentLadderService;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IBilOrderPayInfoService bilOrderPayInfoService;
    @Autowired
    private IUserFegin userFegin;
    @Autowired
    private IBilOrderItemService bilOrderItemService;
    @Autowired
    private IBilDiscountLogService bilDiscountLogService;
    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, BilInvestSearchVo bilInvestSearchVo) throws Exception {
        RestResponse response = new RestResponse();
        if (ObjectUtil.isNotNull(bilInvestSearchVo.getEndPayTime())) {
            bilInvestSearchVo.setEndPayTime(DateUtil.endOfDay(bilInvestSearchVo.getEndPayTime()));
        }
        if (ObjectUtil.isNotNull(bilInvestSearchVo.getEndCreateDate())) {
            bilInvestSearchVo.setEndCreateDate(DateUtil.endOfDay(bilInvestSearchVo.getEndCreateDate()));
        }
        bilInvestSearchVo.setCarKeyWord(true);
        IPage<BilInvestVo> billInvestPage = bilInvestService.findBackendByCondition(page, bilInvestSearchVo);
        List<BilInvestVo> billInvestList = billInvestPage.getRecords();//为了解决后台查询很慢报错的问题,支付者信息采用单独查询设置
        if(billInvestList.size() > 0){
            for(BilInvestVo billInvestVo:billInvestList){
                String payerId =  billInvestVo.getPayerId();
                if(payerId != null && !"".equals(payerId)){//判断不为空
                    RenterEntity renter = renterFegin.getById(payerId);
                    if(renter != null){
                        billInvestVo.setPayer(renter.getName());
                        billInvestVo.setTime(renter.getTime());
                    }
                }
            }
        }
        return response.setSuccess(true).setData(billInvestPage);
    }

    /**
     * 分页查询余额扣费水电账单列表
     *
     * @return
     */
    @RequestMapping("/getSdOrderPage")
    public RestResponse getSdOrderPage(Page page) throws Exception {
        String payerId = UoneSysUser.id();
        IPage<Map<String, Object>> sdOrderPage = bilOrderService.getSdOrderPage(page,payerId);
        return RestResponse.success().setData(sdOrderPage);
    }

    /**
     * 备注
     */
    @RequestMapping("/addRemark")
    public RestResponse addRemark(String id, String remark) {
        RestResponse response = new RestResponse();
        try {
            BilInvestEntity entity = bilInvestService.getById(id);
            entity.setRemark(remark);
            bilInvestService.updateById(entity);
            response.setSuccess(true).setMessage("操作成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    @ApiOperation(value = "新增预充值信息")
    @PostMapping("/toAddInvest")
    public RestResponse toAddInvest(BilInvestEntity entity) throws Exception{
        if(StrUtil.isNotBlank(entity.getPayTimeStr())){
            entity.setPayTime(DateUtil.parseDateTime(entity.getPayTimeStr()));
        }
        ContContractEntity contract = contContractService.getValidContractBySourceId(entity.getSourceId());
        if(null==contract){
            throw new BusinessException("不存在有效合同，无法完成充值！");
        }
        entity.setContractId(contract.getId());
        entity.setPayerId(contract.getSignerId());
        entity.setPayablePayment(entity.getPayment());
        entity.setCode(resProjectService.getOrderCodeBySourceId(entity.getSourceId(),"O","invest"));
        entity.insertOrUpdate();
        return RestResponse.success();
    }


    @ApiOperation(value = "wx新增预充值信息")
    @PostMapping("/wxAdd")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse wxAdd(BilInvestEntity entity,String discountLogId) throws Exception {
        //ContContractEntity contractEntity = contContractService.getById(entity.getContractId());
        BilDiscountLogEntity discountLogEntity = new BilDiscountLogEntity();

        //查询该优惠券是否已被关联过其他订单
        discountLogEntity = bilDiscountLogService.getById(discountLogId);
        if(!"".equals(discountLogId)){
            if (discountLogEntity.getOrderId() !=null){
                return RestResponse.success().setSuccess(false).setMessage("该优惠券已被其他订单使用,请重新选择!");
            }
        }
        ContContractEntity contractEntity = contContractService.getContractBySourceId(entity.getSourceId());
        entity.setPayerId(UoneSysUser.id());
        entity.setPayablePayment(entity.getPayment());
        entity.setCode(resProjectService.getOrderCodeBySourceId(entity.getSourceId(),"O","invest"));
        entity.setContractId(contractEntity.getId());
        entity.setIsPush("1");//TODO 暂时写死
        bilInvestService.save(entity);
        //entity.insert();
        discountLogEntity = bilDiscountLogService.getById(discountLogId);
        if(ObjectUtil.isNotNull(discountLogEntity)){
            discountLogEntity.setOrderId(entity.getId());
            bilDiscountLogService.updateById(discountLogEntity);
        }
        return RestResponse.success().setData(entity.getId());
    }

    /**
     * 导出
     *
     * @param response
     * @param bilInvestSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/export")
    public void export(HttpServletResponse response, BilInvestSearchVo bilInvestSearchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        List<BilInvestVo> list = bilInvestService.bilExport(bilInvestSearchVo);
        for (BilInvestVo vo : list) {
            vo.setOrderType(OrderTypeEnum.getNameByValue(vo.getOrderType()));
            vo.setPayState(PayStateEnum.getNameByValue(vo.getPayState()));
            vo.setInvoiceState(InvoiceStateEnum.getNameByValue(vo.getInvoiceState()));
            if(InvoiceStateEnum.INVOICED.getValue().equals(vo.getInvoiceState())){
                vo.setInvoiceState("已开票");
            }else
                vo.setInvoiceState("未开票");
            vo.setPayWay(PayWayEnum.getNameByValue(vo.getPayWay()));
            vo.setFeeCharge(vo.getPayment().multiply(new BigDecimal("0.003")).setScale(2,BigDecimal.ROUND_HALF_UP));
            vo.setAddress(vo.getPartitionName()+vo.getAddress());
        }
        beans.put("orders", list);
        ExcelRender.me("/excel/export/bilInvest.xlsx").beans(beans).render(response);
    }


    /**
     * 获取水电账单信息
     *
     * @param id
     * @return
     */@RequestMapping("/getWaterInfo")
    public RestResponse getWaterInfo(String id) {
        RestResponse response = new RestResponse();
        try {
            Map<String, Object> resultDataMap = new HashMap<>();
            //账单信息
            BilInvestEntity invest = bilInvestService.getById(id);
            resultDataMap.put("order", invest);
            //支付人信息
            RenterEntity renter = renterFegin.getById(invest.getPayerId());
            resultDataMap.put("renter", renter);
//            //优惠金额
//            BilDiscountEntity discount = bilDiscountLogService.getDiscountByOrder(order);
//            if (ObjectUtil.isNotNull(discount) && PayStateEnum.PAYCONFIR.getValue().equals(order.getPayState())) {
//                resultDataMap.put("discountAmount", discount.getDiscountAmount());
//            }
            //房源信息
            ResSourceVo source = resSourceService.getInfoById(invest.getSourceId());
            resultDataMap.put("source", source);
            if (ObjectUtil.isNull(source)) {
                response.setSuccess(false).setMessage("找不到该账单房源信息！");
                return response;
            }
            //合同信息
            String contractId = invest.getContractId();
            ContContractEntity contract = contContractService.getById(contractId);
            if (ObjectUtil.isNull(contract)) {
                contract = new ContContractEntity();
            }
            resultDataMap.put("contract", contract);
            //合同房源关系表
            ContContractSourceRelEntity contractSourceRel = contContractSourceRelService.getByContractIdAndSourceId(contract.getId(), source.getId());
            if (ObjectUtil.isNull(contractSourceRel)) {
                contractSourceRel = new ContContractSourceRelEntity();
            }
            resultDataMap.put("contractSourceRel", contractSourceRel);
            //阶梯表
            ContRentLadderEntity ladder = contRentLadderService.getRentLadderByContSourceIdAndCreatDate(contractSourceRel.getId());
            if (ObjectUtil.isNull(ladder)) {
                ladder = new ContRentLadderEntity();
            }
            resultDataMap.put("ladder", ladder);
            //合同信息
            ContContractInfoEntity contractInfo = contractInfoService.getByContractId(contractId);
            contractInfo = ObjectUtil.isNull(contractInfo) ? new ContContractInfoEntity() : contractInfo;
            resultDataMap.put("contractInfo", contractInfo);


            //审批信息
            if(StringUtils.isNotBlank(invest.getApprovalState())){
                //获取当前流程审批
                List<ApprovalCommitEntity>  commitEntitys= approvalCommitDao.getAllAppro(invest.getId(),null);
                commitEntitys.forEach(c->{
                    List<ApprovalDetailEntity> lists=approvalDetailService.getApprovalInfo(c,null,null);
                    c.setDetails(lists);
                    List<SysFileEntity> files = sysFileService.getListByFromIdAndType(c.getId(), SysFileTypeEnum.PAY_PIC);
                    c.setFiles(files);
                    BilOrderSearchVo bilOrderSearchVo = new BilOrderSearchVo();
                    bilOrderSearchVo.setIds(Arrays.asList(c.getCodeId().split(",")));
                    List<BilOrderVo> orderList = bilOrderService.findByCondition(bilOrderSearchVo);
                    c.setOrderList(orderList);
                });
                resultDataMap.put("approval", commitEntitys);
                //获取支付信息
                List<SysFileEntity> allFiles = Lists.newArrayList();
                QueryWrapper<BilOrderPayInfoEntity> queryWrapper = new QueryWrapper();
                queryWrapper.eq("order_id", invest.getId());
                List<BilOrderPayInfoEntity> payInfos = bilOrderPayInfoService.list(queryWrapper);
                for (BilOrderPayInfoEntity payInfo : payInfos) {
                    payInfo.setApplyUser(userFegin.getUserById(payInfo.getApplyUser()).getName());
                    List<SysFileEntity> files = sysFileService.getListByFromIdAndType(payInfo.getApprovalId(), SysFileTypeEnum.PAY_PIC);
                    payInfo.setFiles(files);
                    allFiles.addAll(files);
                }
                resultDataMap.put("payInfos", payInfos);
                resultDataMap.put("files", allFiles);
            }
            response.setSuccess(true).setData(resultDataMap);
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }


    /**
     * 查询
     *
     * @return
     */
    @RequestMapping("/getListByUser")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse getListByUser(Page page, BilInvestSearchVo bilInvestSearchVo) {
        RestResponse response = new RestResponse();
        String userId = UoneSysUser.id();
        bilInvestSearchVo.setUserId(userId);
        return response.setSuccess(true).setData(bilInvestService.findByCondition(page, bilInvestSearchVo));
    }

    @RequestMapping("/custom/projects")
    @UonePermissions(value = LoginType.CUSTOM)
    @ApiOperation("我的项目")
    public RestResponse projects() throws Exception{
        Map map= Maps.newHashMap();
        map.put("signerId",UoneSysUser.id());
        List<ContContractVo> list=contContractService.getContractBySignerId(map);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/custom/codes")
    @ApiOperation("我的房间")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse codes(@RequestParam(value = "contractId" ,required = false)String contractId){
        List<SelectVo> list=contContractService.getSourceIdList(UoneSysUser.id());
        return RestResponse.success().setData(list);
    }

    /**
     * 数据审核 财务使用功能
     */
    @RequestMapping("/toShenHe")
    @UoneLog("数据审核财务使用功能")
    public RestResponse toShenHe (String id) {
        RestResponse response = new RestResponse();
        try {
            BilInvestEntity bilInvestEntity = bilInvestService.getById(id);
            if("20".equals(bilInvestEntity.getPayState())){
                response.setSuccess(false).setMessage("已确认支付的账单不用继续审核！");
                return response;
            }
            return bilInvestService.toShenHe(id);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 获取账单信息
     *
     * @param id
     * @return
     */
    @RequestMapping("/getInvestOrderInfo")
    @UonePermissions
    public RestResponse getInvestOrderInfo(String id) {
        RestResponse response = new RestResponse();
        try {
            Map<String, Object> resultDataMap = new HashMap<>();
            //账单信息
            BilInvestEntity order = bilInvestService.getById(id);

            //优惠信息
            BilInvestVo bilInvestVo = new BilInvestVo();
            BeanUtils.copyProperties(order,bilInvestVo);
            List<BilInvestVo> discountPay = bilInvestService.getDiscountPayment(id);

            if (discountPay.size()>0){
                for (BilInvestVo vo : discountPay){
                    int state =0;//表示是否找到对应的优惠金额  0 未找到 1 找到
                    if (vo.getId().equals(bilInvestVo.getId())){
                        bilInvestVo.setDiscountPayment(vo.getDiscountPayment());
                        state =1;
                    }
                    if (state ==0){
                        bilInvestVo.setDiscountPayment("0");
                    }
                }
            }else {
                bilInvestVo.setDiscountPayment("0");
            }

            resultDataMap.put("order", bilInvestVo);

            //房源信息
            ResSourceVo source = resSourceService.getInfoById(order.getSourceId());
            resultDataMap.put("source", source);
            if (ObjectUtil.isNull(source)) {
                response.setSuccess(false).setMessage("找不到该账单房源信息！");
                return response;
            }
            response.setSuccess(true).setData(resultDataMap);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    @RequestMapping("/toChangeBalance")
    @UonePermissions
    public void toChangeBalance() throws Exception {
        bilInvestService.toChangeBalance();
    }

    @RequestMapping("/toCreateAccount")
    @UonePermissions
    public void toCreateAccount() throws Exception {
        bilInvestService.toCreateAccount();
    }
}
