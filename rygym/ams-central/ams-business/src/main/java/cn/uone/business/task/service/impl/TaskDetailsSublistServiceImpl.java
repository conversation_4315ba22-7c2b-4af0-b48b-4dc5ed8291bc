package cn.uone.business.task.service.impl;

import cn.uone.bean.entity.business.task.TaskDetailsSublistEntity;
import cn.uone.business.task.dao.TaskDetailsSublistDao;
import cn.uone.business.task.service.ITaskDetailsSublistService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@Service
public class TaskDetailsSublistServiceImpl extends ServiceImpl<TaskDetailsSublistDao, TaskDetailsSublistEntity> implements ITaskDetailsSublistService {

    @Autowired
    TaskDetailsSublistDao taskDetailsSublistDao;

    @Override
    public List<TaskDetailsSublistEntity> getByDetailsId(String detailsId) {
        return taskDetailsSublistDao.getByDetailsId(detailsId);
    }
}
