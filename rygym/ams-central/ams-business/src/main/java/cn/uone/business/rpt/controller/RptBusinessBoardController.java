package cn.uone.business.rpt.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.business.rpt.service.IRptBusinessBoardService;
import cn.uone.business.rpt.service.IRptRentalRateService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-28
 */
@RestController
@RequestMapping("/report/businessBoard")
public class RptBusinessBoardController extends BaseController {

    @Autowired
    private IRptBusinessBoardService rptBusinessBoardService;


    @RequestMapping(value = "/getBoardData", method = RequestMethod.GET)
    public RestResponse getBoardData(@RequestParam(required = false) String searchDate) {
        String projectId = UoneHeaderUtil.getProjectId();
        Map<String, Object> paras = new HashMap<String, Object>();
        paras.put("projectId", projectId);
        paras.put("searchDate", StrUtil.isBlank(searchDate)? DateUtil.formatDate(new Date()):searchDate);
        Map<String, Object> totalSourceData = rptBusinessBoardService.getTotalSourceData(paras);
        //可出租数
        BigDecimal sourceNum = new BigDecimal(totalSourceData.get("sourceNum").toString());
        //已出租数
        BigDecimal rentedNum = new BigDecimal(totalSourceData.get("rentedNum").toString());
        //可出租面积
        //BigDecimal sourceArea = new BigDecimal(totalSourceData.get("sourceArea").toString());
        //已出租面积
        //BigDecimal rentedArea = new BigDecimal(totalSourceData.get("rentedArea").toString());
        //本日、本月、本年签约数
        Map<String, Object> signNumData = rptBusinessBoardService.getSignNumData(paras);
        BigDecimal dayCount = new BigDecimal(signNumData.get("dayCount").toString());
        BigDecimal monthCount = new BigDecimal(signNumData.get("monthCount").toString());
        BigDecimal yearCount = new BigDecimal(signNumData.get("yearCount").toString());
        //本日、本月、本年出租率
        BigDecimal dayRateByNum = dayCount.divide(sourceNum, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        BigDecimal monthRateByNum = monthCount.divide(sourceNum, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        BigDecimal yearRateByNum = yearCount.divide(sourceNum, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        //本日、本月、本年应收缴数
        Map<String, Object> payNumData = rptBusinessBoardService.getPayNumData(paras);
        BigDecimal monthPayment = new BigDecimal(payNumData.get("monthPayment").toString());
        BigDecimal yearPayment = new BigDecimal(payNumData.get("yearPayment").toString());
        //本日、本月、本年已收缴金额
        BigDecimal dayPaidPayment = new BigDecimal(payNumData.get("dayPaidPayment").toString());
        BigDecimal monthPaidPayment = new BigDecimal(payNumData.get("monthPaidPayment").toString());
        BigDecimal yearPaidPayment = new BigDecimal(payNumData.get("yearPaidPayment").toString());
        //本日、本月、本年收缴率
        BigDecimal dayRateByPayid = dayPaidPayment.divide(monthPayment,2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        BigDecimal monthRateByPayid = monthPaidPayment.divide(monthPayment,2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        BigDecimal yearRateByPayid = yearPaidPayment.divide(yearPayment,2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        //本日、本月、本年面积
        BigDecimal monthArea = new BigDecimal(payNumData.get("monthArea").toString());
        BigDecimal yearArea = new BigDecimal(payNumData.get("yearArea").toString());
        //平均租金
        BigDecimal dayAvgPrice = dayPaidPayment.divide(monthArea,2, BigDecimal.ROUND_HALF_UP);
        BigDecimal monthAvgPrice = monthPaidPayment.divide(monthArea,2, BigDecimal.ROUND_HALF_UP);
        BigDecimal yearAvgPrice = yearPaidPayment.divide(yearArea,2, BigDecimal.ROUND_HALF_UP);
        //组装出租率数据
        Map<String,Object> rentRateDate1 = Maps.newHashMap();
        rentRateDate1.put("title","本日");
        rentRateDate1.put("rentRate",dayRateByNum);
        rentRateDate1.put("signNum",dayCount);
        rentRateDate1.put("paidRate",dayRateByPayid);
        rentRateDate1.put("avgPrice",dayAvgPrice);
        Map<String,Object> rentRateDate2 = Maps.newHashMap();
        rentRateDate2.put("title","本月");
        rentRateDate2.put("rentRate",monthRateByNum);
        rentRateDate2.put("signNum",monthCount);
        rentRateDate2.put("paidRate",monthRateByPayid);
        rentRateDate2.put("avgPrice",monthAvgPrice);
        Map<String,Object> rentRateDate3 = Maps.newHashMap();
        rentRateDate3.put("title","本年");
        rentRateDate3.put("rentRate",yearRateByNum);
        rentRateDate3.put("signNum",yearCount);
        rentRateDate3.put("paidRate",yearRateByPayid);
        rentRateDate3.put("avgPrice",yearAvgPrice);
        List<Map<String,Object>> rentRateDateList = Lists.newArrayList();
        rentRateDateList.add(rentRateDate1);
        rentRateDateList.add(rentRateDate2);
        rentRateDateList.add(rentRateDate3);

        //组装租金收入数据
        List<Map<String,Object>> rentIncomeList = Lists.newArrayList();
        String orderType = OrderTypeEnum.RENT.getValue();
        String orderItemType = OrderItemTypeEnum.RENT.getValue();
        rentIncomeList = getIncomeList(rentIncomeList,projectId,searchDate,orderType,orderItemType,"2","本月（合同口径）");
        rentIncomeList = getIncomeList(rentIncomeList,projectId,searchDate,orderType,orderItemType,"4","本年（合同口径）");
        rentIncomeList = getIncomeList(rentIncomeList,projectId,searchDate,orderType,orderItemType,"1","本月（权责口径）");
        rentIncomeList = getIncomeList(rentIncomeList,projectId,searchDate,orderType,orderItemType,"3","本年（权责口径）");
        rentIncomeList = getIncomeList(rentIncomeList,projectId,searchDate,orderType,orderItemType,"6","以前年度（合同口径）");
        rentIncomeList = getIncomeList(rentIncomeList,projectId,searchDate,orderType,orderItemType,"5","以前年度（权责口径）");

        //组装物管费收入数据
        List<Map<String,Object>> wuguanIncomeList = Lists.newArrayList();
        orderType = OrderTypeEnum.SYNTHESIZE_BASEFEE.getValue();
        orderItemType = OrderItemTypeEnum.SYNTHESIZE_BASEFEE.getValue();
        wuguanIncomeList = getIncomeList(wuguanIncomeList,projectId,searchDate,orderType,orderItemType,"2","本月（合同口径）");
        wuguanIncomeList = getIncomeList(wuguanIncomeList,projectId,searchDate,orderType,orderItemType,"4","本年（合同口径）");
        wuguanIncomeList = getIncomeList(wuguanIncomeList,projectId,searchDate,orderType,orderItemType,"1","本月（权责口径）");
        wuguanIncomeList = getIncomeList(wuguanIncomeList,projectId,searchDate,orderType,orderItemType,"3","本年（权责口径）");
        wuguanIncomeList = getIncomeList(wuguanIncomeList,projectId,searchDate,orderType,orderItemType,"6","以前年度（合同口径）");
        wuguanIncomeList = getIncomeList(wuguanIncomeList,projectId,searchDate,orderType,orderItemType,"5","以前年度（权责口径）");

        //todo 多经收入

        //数据输出
        Map<String,Object> data = Maps.newHashMap();
        data.put("sourceNum",sourceNum);//可出租房间
        data.put("rentedNum",rentedNum);//已出租房间
        //data.put("sourceArea",sourceArea);//可出租面积
        //data.put("rentedArea",rentedArea);//已出租面积
        data.put("rentRateDateList",rentRateDateList);
        data.put("rentIncomeList",rentIncomeList);
        data.put("wuguanIncomeList",wuguanIncomeList);
        return RestResponse.success().setData(data);
    }


    private List<Map<String,Object>> getIncomeList(List<Map<String,Object>> incomeList,String projectId,String searchDate,String orderType,String orderItemType,String timeType,String title){
        Map<String, Object> paras = new HashMap<String, Object>();
        paras.put("projectId", projectId);
        paras.put("searchDate", StrUtil.isBlank(searchDate)? DateUtil.formatDate(new Date()):searchDate);
        paras.put("orderType", orderType);
        paras.put("orderItemType", orderItemType);
        paras.put("timeType", timeType);
        Map<String, Object> totalRentPaymentData = rptBusinessBoardService.getTotalPaymentData(paras);
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal payment = BigDecimal.ZERO;
        BigDecimal incomeRate = BigDecimal.ZERO;
        if(totalRentPaymentData != null){
            total = new BigDecimal(totalRentPaymentData.get("total").toString()).divide(new BigDecimal("10000"),2,BigDecimal.ROUND_HALF_UP);
            payment = new BigDecimal(totalRentPaymentData.get("payment").toString()).divide(new BigDecimal("10000"),2,BigDecimal.ROUND_HALF_UP);
            incomeRate = (total.subtract(payment)).divide(total,2,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        }
        Map<String,Object> rentIncome = Maps.newHashMap();
        rentIncome.put("title",title);
        rentIncome.put("total",total);
        rentIncome.put("payment",payment);
        rentIncome.put("incomeRate",incomeRate);
        incomeList.add(rentIncome);
        return incomeList;
    }



}
