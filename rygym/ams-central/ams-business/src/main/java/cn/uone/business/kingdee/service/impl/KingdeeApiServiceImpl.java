package cn.uone.business.kingdee.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.contract.PayerTypeEnum;
import cn.uone.application.enumerate.kingdee.BosTypeEnum;
import cn.uone.application.enumerate.kingdee.PaymentTypeEnum;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderItemVo;
import cn.uone.bean.entity.business.biz.BizAccountEntity;
import cn.uone.bean.entity.business.kingdee.*;
import cn.uone.bean.entity.business.kingdee.vo.*;
import cn.uone.bean.entity.business.res.ResProjectCompanyEntity;
import cn.uone.bean.entity.business.rpt.RptRevenueEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.bil.dao.BilOrderDao;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.biz.service.IBizAccountService;
import cn.uone.business.bpm.service.IBpmWorkflowService;
import cn.uone.business.kingdee.service.*;
import cn.uone.business.res.dao.ResSourceDao;
import cn.uone.business.res.service.IResProjectCompanyService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.rpt.service.IReportInvoiceService;
import cn.uone.business.rpt.service.IRptRevenueService;
import cn.uone.business.util.DateUtils;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.fegin.tpi.IXyKingdeeFegin;
import cn.uone.util.CodeUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Service
public class KingdeeApiServiceImpl implements IKingdeeApiService {
    @Autowired
    IKingdeePaymentService kingdeePaymentService;
    @Autowired
    IKingdeeTransferService kingdeeTransferService;
    @Autowired
    IKingdeePaymentItemService kingdeePaymentItemService;
    @Autowired
    IKingdeeTransferItemService kingdeeTransferItemService;
    @Autowired
    IKingdeeExpenseTypeService kingdeeExpenseTypeService;
    @Autowired
    IKingdeeCustomerService kingdeeCustomerService;
    @Autowired
    IKingdeeReceivableService kingdeeReceivableService;
    @Autowired
    IKingdeeReceivableItemService kingdeeReceivableItemService;
    @Autowired
    IKingdeeReceivableRelService kingdeeReceivableRelService;
    @Autowired
    IKingdeeAmortizeService kingdeeAmortizeService;
    @Autowired
    IKingdeeAmortizeItemService kingdeeAmortizeItemService;
    @Autowired
    IKingdeeAmortizeRelService kingdeeAmortizeRelService;
    @Autowired
    IKingdeeReceiptService kingdeeReceiptService;
    @Autowired
    IKingdeeReceiptItemService kingdeeReceiptItemService;
    @Autowired
    IKingdeeInvoiceService kingdeeInvoiceService;
    @Autowired
    IKingdeeInvoiceItemService kingdeeInvoiceItemService;
    @Autowired
    IBizAccountService bizAccountService;
    @Autowired
    IBpmWorkflowService bpmWorkflowService;
    @Autowired
    IBilOrderItemService bilOrderItemService;
    @Autowired
    IResProjectParaService resProjectParaService;
    @Autowired
    IReportInvoiceService reportInvoiceService;
    @Autowired
    IRptRevenueService revenueService;
    @Autowired
    ResSourceDao resSourceDao;
    @Autowired
    IXyKingdeeFegin xyKingdeeFegin;
    @Autowired
    BilOrderDao bilOrderDao;
    @Autowired
    IResProjectCompanyService resProjectCompanyService;
    @Autowired
    IRenterFegin renterFegin;
    @Autowired
    IUserFegin userFegin;

    /**
     * 收款单-跑批生成
     * @param entryDate
     */
    @Transactional
    public void createReceiptBillByJob(String entryDate){
        List<KingdeeReceiptEntity> list = Lists.newArrayList();
        List<KingdeeReceiptItemEntity> itemList = Lists.newArrayList();
        //获取银联划付列表
        List<KingdeeReceiptVo> unionList = kingdeeReceiptService.getUnionTransfersByArriveTime(entryDate);
        List<KingdeeReceiptVo> confirmList = kingdeeReceiptService.getOrderConfirmByApplyTime(entryDate);
        unionList.addAll(confirmList);
        for(KingdeeReceiptVo vo: unionList){
            String receiptId = IdUtil.simpleUUID();
            KingdeeReceiptEntity entity = vo;
            entity.setId(receiptId);
            list.add(entity);
            kingdeeReceiptService.save(entity);
            for(KingdeeReceiptItemEntity item: vo.getEntrys()){
                BilOrderItemVo orderItemVo = bilOrderItemService.getItemVoWithoutItemInfoByOrderItemId(item.getOrderItemId());
                String contractor = this.getCustomerStr(orderItemVo);
                String sourceId = item.getSourceId();
                KingdeeSourceVo sourceVo = resSourceDao.getKingdeeSource(sourceId);
                String projectId = sourceVo.getProjectId();
                String partitionId = sourceVo.getPartitionId();
                String czType = OrderItemTypeEnum.getNameByValue(item.getOrderItemType());
                String customer = kingdeeCustomerService.getCustomerCode(projectId,partitionId,item.getOrderItemType());
                String itemId = IdUtil.simpleUUID();
                item.setId(itemId);
                item.setReceiptId(receiptId);
                item.setCustomer(customer);
                item.setProduct(sourceVo.getProduct());
                item.setProject(sourceVo.getProject());
                item.setCzType(czType);
                item.setRoom(sourceVo.getCode());
                item.setContractor(contractor);
                itemList.add(item);
            }
        }
        if(list.size()>0){
            kingdeeReceiptService.saveBatch(list,list.size());
        }
        if(itemList.size()>0){
            kingdeeReceiptItemService.saveBatch(itemList,itemList.size());
        }

    }

    /**
     * 收款单-确认收款时生成
     * @param id
     * @param type
     */
    @Transactional
    public void createReceiptBill(String id,String type) throws Exception {
        KingdeeReceiptVo vo = new KingdeeReceiptVo();
        List<KingdeeReceiptItemEntity> itemList = Lists.newArrayList();
        //type==1?银联划付：线下支付确认
        if("1".equals(type)){
            vo = kingdeeReceiptService.getUnionTransferById(id);
        }else{
            vo = kingdeeReceiptService.getOrderConfirmById(id);
        }
        if(vo == null){
            throw new Exception("已存在与该笔匹配的收款单，请检查数据的正确性");
        }
        KingdeeReceiptEntity entity = vo;
        kingdeeReceiptService.save(entity);
        String receiptId = entity.getId();
        for(KingdeeReceiptItemEntity item: vo.getEntrys()){
            BilOrderItemVo orderItemVo = bilOrderItemService.getItemVoWithoutItemInfoByOrderItemId(item.getOrderItemId());
            String contractor = this.getCustomerStr(orderItemVo);
            String sourceId = item.getSourceId();
            KingdeeSourceVo sourceVo = resSourceDao.getKingdeeSource(sourceId);
            String projectId = sourceVo.getProjectId();
            String partitionId = sourceVo.getPartitionId();
            String czType = OrderItemTypeEnum.getNameByValue(item.getOrderItemType());
            String customer = kingdeeCustomerService.getCustomerCode(projectId,partitionId,item.getOrderItemType());
            String itemId = IdUtil.simpleUUID();
            item.setId(itemId);
            item.setReceiptId(receiptId);
            item.setCustomer(customer);
            item.setProduct(sourceVo.getProduct());
            item.setProject(sourceVo.getProject());
            item.setCzType(czType);
            item.setRoom(sourceVo.getCode());
            item.setContractor(contractor);
            itemList.add(item);
        }
        if(itemList.size()>0){
            kingdeeReceiptItemService.saveBatch(itemList,itemList.size());
        }
    }

    /**
     * 退款单拆分付款单和转款单-批量
     * @param orderId
     * @param sourceId
     * @param renterId
     * @throws Exception
     */
    @Transactional
    public void createPaymentAndTransferBill(String orderId,String sourceId,String renterId) throws Exception {
        BilOrderEntity orderEntity = bilOrderDao.selectById(orderId);
        //退款账单明细按金额降序排列,过滤金额为0的明细
        List<BilOrderItemVo> orderItemList = bilOrderItemService.getItemVosByOrderId(orderId);
        //取最后一条明细为转出记录
        int outI = orderItemList.size() - 1;
        BigDecimal outPayment = orderItemList.get(outI).getPayment();
        //如果最后一条明细金额大于0，则该账单不是退款账单，抛出异常
        if(outPayment.compareTo(BigDecimal.ZERO) == 1){
            throw new Exception("账单明细不符合退款账单的明细");
        }
        BigDecimal paymentAmount = BigDecimal.ZERO;
        BigDecimal transferAmount = BigDecimal.ZERO;
        String outType = orderItemList.get(outI).getOrderItemType();
        BigDecimal intoPayment = BigDecimal.ZERO;
        List<KingdeePaymentItemEntity> paymentItemList = Lists.newArrayList();
        List<KingdeeTransferItemEntity> transferItemList = Lists.newArrayList();
        String paymentId = CodeUtil.generateUuid(true);
        String transferId = CodeUtil.generateUuid(true);
        KingdeeSourceVo sourceVo = resSourceDao.getKingdeeSource(sourceId);
        String projectId = sourceVo.getProjectId();
        String partitionId = sourceVo.getPartitionId();
        String roomNumber = sourceVo.getCode();
        //遍历退款单明细
        for(int i=0;i<orderItemList.size();i++){
            if(i > outI){
                break;
            }
            String customerStr = this.getCustomerStr(orderItemList.get(i));
            //当前遍历记录是转出记录时，如果转出金额不为0，则转出金额为付款金额，生成该笔付款明细
            if(i == outI){
                if(outPayment.compareTo(BigDecimal.ZERO) == -1){
                    paymentAmount = paymentAmount.add(outPayment.abs());
                    paymentItemList.add(createPaymentItem(paymentId,outType,outPayment.abs(),projectId,partitionId,roomNumber,customerStr));
                }else if(outPayment.compareTo(BigDecimal.ZERO) == 1){
                    throw new Exception("账单明细不符合退款账单的明细");
                }
                break;
            }
            intoPayment = orderItemList.get(i).getPayment();
            String intoType = orderItemList.get(i).getOrderItemType();
            //过滤金额为0的情况
            if(intoPayment.compareTo(BigDecimal.ZERO) == 0){
                continue;
            }
            //明细均为负数时，转入金额为付款金额
            if(intoPayment.compareTo(BigDecimal.ZERO) == -1){
                paymentAmount = paymentAmount.add(intoPayment.abs());
                paymentItemList.add(createPaymentItem(paymentId,intoType,intoPayment.abs(),projectId,partitionId,roomNumber,customerStr));
                continue;

            }
            //当前转入大于等于当前转出的情况，转出金额为转款金额
            while(intoPayment.compareTo(outPayment.abs()) != -1){
                //过滤金额为0的情况
                if(outPayment.compareTo(BigDecimal.ZERO) == 0){
                    outI = outI - 1;
                    outPayment = orderItemList.get(outI).getPayment();
                    outType = orderItemList.get(outI).getOrderItemType();
                    if(i == outI){
                        break;
                    }
                    if(outPayment.compareTo(BigDecimal.ZERO) == 1){
                        throw new Exception("账单明细不符合退款账单的明细");
                    }
                    continue;
                }
                transferAmount = transferAmount.add(outPayment.abs());
                transferItemList.add(createTransferItem(transferId,sourceVo,customerStr,outType,intoType,outPayment.abs()));
                intoPayment = intoPayment.add(outPayment);
                outI = outI - 1;
                outPayment = orderItemList.get(outI).getPayment();
                outType = orderItemList.get(outI).getOrderItemType();
                if(i == outI){
                    break;
                }
                if(outPayment.compareTo(BigDecimal.ZERO) == 1){
                    throw new Exception("账单明细不符合退款账单的明细");
                }
            }
            //当前遍历记录是转出记录时，如果转出金额不为0，则转出金额为付款金额，生成该笔付款明细
            if(i == outI){
                if(intoPayment.compareTo(BigDecimal.ZERO) == 1){
                    throw new Exception("账单明细不符合退款账单的明细");
                }
                break;
            }
            //当前转入小于转出的情况，转入金额为转款金额
            if(intoPayment.compareTo(outPayment.abs()) == -1){
                transferAmount = transferAmount.add(intoPayment);
                transferItemList.add(createTransferItem(transferId,sourceVo,customerStr,outType,intoType,intoPayment.abs()));
                outPayment = intoPayment.add(outPayment);
                continue;
            }
        }
        /*BpmWorkflowEntity workflow = bpmWorkflowService.getByRelId(orderId);
        Date paymentDate = new Date();
        if(workflow == null){
            throw new Exception("该退款单丢失审批记录");
        }else{
            paymentDate = workflow.getAuditTime();
        }*/
        if(paymentItemList.size() > 0){
            BizAccountEntity account = bizAccountService.getByRenterId(renterId);
            RenterEntity renter = renterFegin.getById(renterId);
            if(account == null){
                throw new Exception(StrUtil.format("租客【renterId：{}】的退款银行账户不存在",renterId));
            }
            System.out.println("BizAccountEntity==="+account);
            String companyId=resProjectCompanyService.getOne(new QueryWrapper<ResProjectCompanyEntity>().eq("project_id",projectId)).getId();
            KingdeePaymentEntity entity = createPayment(paymentId,orderId,companyId,paymentAmount,sourceVo,account);
            kingdeePaymentService.insert(entity);
            kingdeePaymentItemService.saveBatch(paymentItemList,paymentItemList.size());
        }
        if(transferItemList.size() > 0){
            String period = DateUtil.format(new Date(), "yyyyMM");
            KingdeeTransferEntity entity = createTransfer(transferId,orderId,period,sourceVo);
            kingdeeTransferService.insert(entity);
            kingdeeTransferItemService.saveBatch(transferItemList,transferItemList.size());
        }
    }

    /**
     * 退款单拆分付款单和转款单,用于提交审批时获得付款明细表和转款明细表
     * @param orderId
     * @param sourceId
     * @param renterId
     * @throws Exception
     */
    @Transactional
    public Map getPaymentAndTransferBill(String orderId,String sourceId,String renterId,Date payTime) throws Exception {
        BilOrderEntity orderEntity=bilOrderDao.selectById(orderId);
        KingdeePaymentEntity payment=new KingdeePaymentEntity();
        KingdeeTransferEntity transfer=new KingdeeTransferEntity();
        //退款账单明细按金额降序排列,过滤金额为0的明细
        List<BilOrderItemVo> orderItemList = bilOrderItemService.getItemVosByOrderId(orderId);

        //取最后一条明细为转出记录
        int outI = orderItemList.size() - 1;
        BigDecimal outPayment = orderItemList.get(outI).getPayment();
        //如果最后一条明细金额大于0，则该账单不是退款账单，抛出异常
        if(outPayment.compareTo(BigDecimal.ZERO) == 1){
            throw new Exception("账单明细不符合退款账单的明细");
        }
        BigDecimal paymentAmount = BigDecimal.ZERO;
        BigDecimal transferAmount = BigDecimal.ZERO;
        String outType = orderItemList.get(outI).getOrderItemType();
        BigDecimal intoPayment = BigDecimal.ZERO;
        List<KingdeePaymentItemEntity> paymentItemList = Lists.newArrayList();
        List<KingdeeTransferItemEntity> transferItemList = Lists.newArrayList();
        String paymentId = CodeUtil.generateUuid(true);
        String transferId = CodeUtil.generateUuid(true);
        KingdeeSourceVo sourceVo = resSourceDao.getKingdeeSource(sourceId);
        String projectId = sourceVo.getProjectId();
        String partitionId = sourceVo.getPartitionId();
        String roomNumber = sourceVo.getCode();
        //遍历退款单明细
        for(int i=0;i<orderItemList.size();i++){
            if(i > outI){
                break;
            }
            String customerStr = this.getCustomerStr(orderItemList.get(i));
            intoPayment = orderItemList.get(i).getPayment();
            String intoType = orderItemList.get(i).getOrderItemType();
            //过滤金额为0的情况
            if(intoPayment.compareTo(BigDecimal.ZERO) == 0){
                continue;
            }
            //明细均为负数时，转入金额为付款金额
            if(intoPayment.compareTo(BigDecimal.ZERO) == -1){
                paymentAmount = paymentAmount.add(intoPayment.abs());
                paymentItemList.add(createPaymentItem(paymentId,intoType,intoPayment.abs(),projectId,partitionId,roomNumber,customerStr));
                continue;
            }
            //当前转入大于等于当前转出的情况，转出金额为转款金额
            while(intoPayment.compareTo(outPayment.abs()) != -1){
                //过滤金额为0的情况
                if(outPayment.compareTo(BigDecimal.ZERO) == 0){
                    outI = outI - 1;
                    outPayment = orderItemList.get(outI).getPayment();
                    outType = orderItemList.get(outI).getOrderItemType();
                    if(i == outI){
                        break;
                    }
                    if(outPayment.compareTo(BigDecimal.ZERO) == 1){
                        throw new Exception("账单明细不符合退款账单的明细");
                    }
                    continue;
                }
                transferAmount = transferAmount.add(outPayment.abs());
                transferItemList.add(createTransferItem(transferId,sourceVo,customerStr,outType,intoType,outPayment.abs()));
                intoPayment = intoPayment.add(outPayment);
                outI = outI - 1;
                outPayment = orderItemList.get(outI).getPayment();
                outType = orderItemList.get(outI).getOrderItemType();
                if(i == outI){
                    break;
                }
                if(outPayment.compareTo(BigDecimal.ZERO) == 1){
                    throw new Exception("账单明细不符合退款账单的明细");
                }
            }
            //当前遍历记录是转出记录时，如果转出金额不为0，则转出金额为付款金额，生成该笔付款明细
            if(i == outI){
                if(outPayment.compareTo(BigDecimal.ZERO) == -1){
                    paymentAmount = paymentAmount.add(outPayment.abs());
                    paymentItemList.add(createPaymentItem(paymentId,outType,outPayment.abs(),projectId,partitionId,roomNumber,customerStr));
                }else if(outPayment.compareTo(BigDecimal.ZERO) == 1){
                    throw new Exception("账单明细不符合退款账单的明细");
                }
                break;
            }
            //当前转入小于转出的情况，转入金额为转款金额
            if(intoPayment.compareTo(outPayment.abs()) == -1){
                transferAmount = transferAmount.add(intoPayment);
                transferItemList.add(createTransferItem(transferId,sourceVo,customerStr,outType,intoType,intoPayment.abs()));
                outPayment = intoPayment.add(outPayment);
                continue;
            }
        }
        if(paymentItemList.size() > 0){
            BizAccountEntity account = bizAccountService.getOne(new QueryWrapper<BizAccountEntity>().eq("renter_id",renterId));
            if(account == null){
                throw new Exception(StrUtil.format("租客【renterId：{}】的退款银行账户不存在",renterId));
            }
            //Date paymentDate=payTime;
            payment = createPayment(paymentId,orderId,orderEntity.getPayUnit(),paymentAmount,sourceVo,account);
        }
        if(transferItemList.size() > 0){
            String period ="";
            transfer = createTransfer(transferId,orderId,period,sourceVo);

        }
        Map PTmap=new HashMap();
        PTmap.put("payment",payment);
        PTmap.put("transfer",transfer);
        PTmap.put("paymentItemList",paymentItemList);
        PTmap.put("transferItemList",transferItemList);
        return PTmap;
    }




    /**
     * 按月生成应收单
     * @param yearMonth
     */
    @Transactional
    public void createReceivableBill(String yearMonth,String projectId){
        //获取待生成应收单的账单明细，目前获取的是租金、固耗、能耗账单,排除已生成的
        List<Map<String,Object>> orderItemList = bilOrderItemService.getItemsByMonth(yearMonth,projectId);
        //map用于存应收单分组
        Map<String,String> map = Maps.newHashMap();
        //itemMap用于存应收单客户段分组，应收单按项目段分组
        Map<String,Map<String,KingdeeReceivableItemEntity>> itemMap = Maps.newHashMap();
        List<KingdeeReceivableEntity> list = Lists.newArrayList();
        List<KingdeeReceivableItemEntity> itemList = Lists.newArrayList();
        List<KingdeeReceivableRelEntity> relList = Lists.newArrayList();
        for(Map<String,Object> orderItem:orderItemList){
            String orderItemId = (String) orderItem.get("id");
            String sourceId = (String) orderItem.get("source_id");
            String orderType = (String) orderItem.get("order_item_type");
            BigDecimal payment = (BigDecimal) orderItem.get("payment");
            KingdeeSourceVo sourceVo = resSourceDao.getKingdeeSource(sourceId);
            String project = sourceVo.getProject();
            //String projectId = sourceVo.getProjectId();
            String partitionId = sourceVo.getPartitionId();
            String product = sourceVo.getProduct();
            String period = yearMonth;
            String receivableId = map.get(project);//获取应收单
            if(StrUtil.isBlank(receivableId)){//如果应收单不存在，则新建应收单
                receivableId = CodeUtil.generateUuid(true);
                KingdeeReceivableEntity receivable = createReceivable(receivableId,period,sourceVo);
                map.put(project,receivableId);
                list.add(receivable);
            }
            //获取应收单的客户段分组
            Map<String,KingdeeReceivableItemEntity> customerMap = itemMap.get(receivableId);
            if(MapUtil.isEmpty(customerMap)){
                customerMap = Maps.newHashMap();
            }
            //获取客户段对应的应收单明细
            String paymentType = kingdeeExpenseTypeService.getCodeByType("1",orderType);
            String czType = OrderItemTypeEnum.getNameByValue(orderType);
            String customer = kingdeeCustomerService.getCustomerCode(projectId,partitionId,orderType);
            KingdeeReceivableItemEntity item = customerMap.get(customer);
            if(item == null){//不存在，则新建
                String taxCode = ProjectParaEnum.getTaxCodeByOrderType(orderType);
                BigDecimal taxRate = new BigDecimal(resProjectParaService.getByCode(taxCode,projectId));
                item = createReceivableItem(receivableId,product,customer,paymentType,czType,payment,taxRate);
            }else{//存在，则将账单明细累加进入
                payment = payment.add(item.getAmount());
                String itemId = item.getId();
                item = createReceivableItem(receivableId,product,customer,paymentType,czType,payment,item.getTaxRate());
                item.setId(itemId);
            }
            KingdeeReceivableRelEntity rel = createReceivableRel(item.getId(),orderItemId);
            relList.add(rel);
            customerMap.put(customer,item);
            itemMap.put(receivableId,customerMap);
        }
        if(list.size() > 0){
            kingdeeReceivableService.saveBatch(list,list.size());
        }
        if(MapUtil.isNotEmpty(itemMap) && itemMap.values().size()>0){
            for(Map<String,KingdeeReceivableItemEntity> imap:itemMap.values()){
                itemList.addAll(imap.values());
            }
        }
        if(itemList.size() > 0){
            kingdeeReceivableItemService.saveBatch(itemList,itemList.size());
        }
        if(relList.size() > 0){
            kingdeeReceivableRelService.saveBatch(relList,relList.size());
        }
    }

    /**
     * 按月生成摊销单
     * @param yearMonth
     */
    @Transactional
    public void createAmortizeBill(String yearMonth,String projectId){
        String[] createDate = yearMonth.replaceAll(" ", "").split("-");
        //获取待生成摊销单的摊销记录
        List<String> monthBetween = new ArrayList<>();
        monthBetween = DateUtils.getMonthBetween(createDate[0], createDate[1]);
        //map用于存摊销单分组
        Map<String,String> map = Maps.newHashMap();
        //itemMap用于存客户段分组，摊销单按项目段分组
        Map<String,Map<String,KingdeeAmortizeItemEntity>> itemMap = Maps.newHashMap();
        List<KingdeeAmortizeEntity> list = Lists.newArrayList();
        List<KingdeeAmortizeItemEntity> itemList = Lists.newArrayList();
        List<KingdeeAmortizeRelEntity> relList = Lists.newArrayList();
        for(int i = 0;i < monthBetween.size(); i++){
            String dateStr = monthBetween.get(i);
            Date date = DateUtil.parse(dateStr,"yyyyMM");
            int year = DateUtil.year(date);
            int month = DateUtil.month(date) + 1;
            List<RptRevenueEntity> revenueList = revenueService.selectRevenueForKingdee(year+"",projectId);
            for(RptRevenueEntity revenue: revenueList){
            String revenueId = revenue.getId();
            String sourceId = revenue.getSourceId();
            String orderType = revenue.getOrderItemType();
            BigDecimal payment = getRevenueByMonth(revenue,month);
            KingdeeSourceVo sourceVo = resSourceDao.getKingdeeSource(sourceId);
            String project = sourceVo.getProject();
            String partitionId = sourceVo.getPartitionId();
            String product = sourceVo.getProduct();
            String period = dateStr;
            String amortizeId = map.get(project+dateStr);//获取摊销单
            if(StrUtil.isBlank(amortizeId)){//如果摊销单不存在，则新建摊销单
                amortizeId = CodeUtil.generateUuid(true);
                KingdeeAmortizeEntity amortize = createAmortize(amortizeId,period,sourceVo);
                map.put(project+dateStr,amortizeId);
                list.add(amortize);
            }
            //获取摊销单的客户段分组
            Map<String,KingdeeAmortizeItemEntity> customerMap = itemMap.get(amortizeId);
            if(MapUtil.isEmpty(customerMap)){
                customerMap = Maps.newHashMap();
            }
            //获取客户段对应的应收单明细
            String czType = OrderItemTypeEnum.getNameByValue(orderType);
            String customer = kingdeeCustomerService.getCustomerCode(projectId,partitionId,orderType);
            KingdeeAmortizeItemEntity item = customerMap.get(customer);
            String itemId = CodeUtil.generateUuid(true);
            if(item != null){//存在，则将摊销明细累加进入
                itemId = item.getId();
                payment = payment.add(item.getAmount());
            }
            String paymentType = kingdeeExpenseTypeService.getCodeByType("1",orderType);
            item = createAmortizeItem(amortizeId,period,product,customer,paymentType,czType,payment);
            item.setId(itemId);
            KingdeeAmortizeRelEntity rel = createAmortizeRel(item.getId(),revenueId);
            relList.add(rel);
            customerMap.put(customer,item);
            itemMap.put(amortizeId,customerMap);
          }
        }
        if(list.size() > 0){
            kingdeeAmortizeService.saveBatch(list,list.size());
        }
        if(MapUtil.isNotEmpty(itemMap) && itemMap.values().size()>0){
            for(Map<String,KingdeeAmortizeItemEntity> imap:itemMap.values()){
                itemList.addAll(imap.values());
            }
        }
        if(itemList.size() > 0){
            kingdeeAmortizeItemService.saveBatch(itemList,itemList.size());
        }
        if(relList.size() > 0){
            kingdeeAmortizeRelService.saveBatch(relList,relList.size());
        }
    }

    /**
     * 单笔生成开票信息
     * @param startDate
     * @param endDate
     */
    @Transactional
    public void createInvoiceBill(Date startDate, Date endDate){
        //获取开始时间至结束时间内的发票列表
        Map<String,Object> map = Maps.newHashMap();
        map.put("startDate",startDate);
        map.put("endDate",endDate);
        List<Map<String,Object>> list = reportInvoiceService.getTaxListForKingdee(map);
        for(Map<String,Object> obj: list){
            //String sourceId = obj.get("");
            //KingdeeInvoiceEntity = createInvoice();
        }

    }



    //===付款单和转款单相关=========================================================================================
    /**
     * 获取账单明细列表
     * @param orderId
     * @return
     */
    private List<BilOrderItemEntity> getItemsByOrderId(String orderId) {
        QueryWrapper<BilOrderItemEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("order_id", orderId);
        queryWrapper.ne("payment",0);
        queryWrapper.orderByDesc("payment");
        return bilOrderItemService.list(queryWrapper);
    }

    /**
     * 创建付款单
     * @param id
     * @param orderId
     * @param amount
     * @param sourceVo
     * @param account
     * @return
     */
    private KingdeePaymentEntity createPayment(String id,String orderId,String companyId, BigDecimal amount,KingdeeSourceVo sourceVo, BizAccountEntity account){
        ResProjectCompanyEntity companyEntity = resProjectCompanyService.getById(companyId);
        KingdeePaymentEntity entity = new KingdeePaymentEntity();
        entity.setId(id);
        entity.setNumber(CodeUtil.codeCreate("KD"));
        entity.setCompany(companyEntity.getCode());   //
        entity.setAccount(companyEntity.getCard());
        entity.setPaymentBank(companyEntity.getBankCode());
        entity.setType(PaymentTypeEnum.PAY.getValue());
        //entity.setPaymentDate(paymentDate);
        entity.setSubject("100101");
        entity.setAmount(amount);
        entity.setPayableAmount(amount);
        entity.setCollectionAccount(account.getCode());
        entity.setReceivingBank(account.getBankCode());
        entity.setCustomer(account.getName());
        entity.setProduct(sourceVo.getProduct());
        entity.setProject(sourceVo.getProject());
        entity.setProjectId(sourceVo.getProjectId());
        entity.setOrderId(orderId);
        return entity;
    }

    /**
     * 创建付款单明细
     * @param paymentId
     * @param orderType
     * @param amount
     * @param projectId
     * @param partitionId
     * @return
     */
    private KingdeePaymentItemEntity createPaymentItem(String paymentId,String orderType,BigDecimal amount,String projectId,String partitionId,String roomNumber,String customerStr){
        String czType = OrderItemTypeEnum.getNameByValue(orderType);
        String expenseType = kingdeeExpenseTypeService.getCodeByType("2",orderType);
        String customer = kingdeeCustomerService.getCustomerCode(projectId,partitionId,orderType);
        KingdeePaymentItemEntity item = new KingdeePaymentItemEntity();
        item.setId(CodeUtil.generateUuid(true));
        item.setPaymentId(paymentId);
        item.setExpenseType(expenseType);
        item.setAmount(amount);
        item.setCustomer(customer);
        item.setCustomerStr(customerStr);
        item.setRoomNumber(roomNumber);
        item.setCzType(czType);
        return item;
    }

    /**
     * 获取签约人姓名，企业：根据选择的付款方传企业名称/员工姓名
     * @param vo
     * @return
     */
    private String getCustomerStr(BilOrderItemVo vo){
        String isOrganize = vo.getIsOrganize();
        String rentPayPayer = vo.getRentPayPayer();
        String lifePayPayer = vo.getLifePayPayer();
        String fixLifePayPayer = vo.getFixLifePayPayer();
        String staffName = vo.getStaffName();
        String organizeName = vo.getOrganizeName();
        String orderType = vo.getOrderType();
        if("0".equals(isOrganize)
                || (OrderTypeEnum.RENT.getValue().equals(orderType) && PayerTypeEnum.STAFF.getValue().equals(rentPayPayer))
                ||(OrderTypeEnum.ENERGY.getValue().equals(orderType) && PayerTypeEnum.STAFF.getValue().equals(lifePayPayer))
                ||(OrderTypeEnum.FIXED.getValue().equals(orderType) && PayerTypeEnum.STAFF.getValue().equals(fixLifePayPayer))){
            return staffName;
        }
        return organizeName;
    }

    /**
     * 创建转款单
     * @param id
     * @param orderId
     * @param period
     * @param sourceVo
     * @return
     */
    public KingdeeTransferEntity createTransfer(String id,String orderId,String period,KingdeeSourceVo sourceVo){
        KingdeeTransferEntity entity = new KingdeeTransferEntity();
        entity.setId(id);
        entity.setNumber(CodeUtil.codeCreate("KD"));
        entity.setCompany(sourceVo.getCompany());
        entity.setPeriod(period);
        entity.setProject(sourceVo.getProject());
        entity.setProjectId(sourceVo.getProjectId());
        entity.setOrderId(orderId);
        return entity;
    }

    /**
     * 创建转款明细
     * @param transferId
     * @param sourceVo
     * @param outType
     * @param intoType
     * @param amount
     * @return
     */
    public KingdeeTransferItemEntity createTransferItem(String transferId,KingdeeSourceVo sourceVo,String customerStr,String outType, String intoType,BigDecimal amount){
        String projectId = sourceVo.getProjectId();
        String partitionId = sourceVo.getPartitionId();
        String czTransferOut = OrderItemTypeEnum.getNameByValue(outType);
        String czIncome = OrderItemTypeEnum.getNameByValue(intoType);
        String outExpenseType = kingdeeExpenseTypeService.getCodeByType("2",outType);
        String intoExpenseType = kingdeeExpenseTypeService.getCodeByType("1",intoType);
        KingdeeTransferItemEntity item = new KingdeeTransferItemEntity();
        item.setId(CodeUtil.generateUuid(true));
        item.setTransferId(transferId);
        item.setCustomer(kingdeeCustomerService.getCustomerCode(projectId,partitionId,outType));
        item.setProduct(sourceVo.getProduct());
        item.setProject(sourceVo.getProject());
        item.setOutType(outExpenseType);
        item.setOutAmount(amount);
        item.setIntoType(intoExpenseType);
        item.setIntoAmount(amount);
        item.setIntoCustomer(kingdeeCustomerService.getCustomerCode(projectId,partitionId,intoType));
        item.setIntoProduct(sourceVo.getProduct());
        item.setIntoProject(sourceVo.getProject());
        item.setIntoTime(DateUtil.date());
        item.setCustomerStr(customerStr);
        item.setRoomNumber(sourceVo.getCode());
        item.setCzTransferOut(czTransferOut);
        item.setCzIncome(czIncome);
        return item;
    }

    //===应收单相关=====================================================================================================
    /**
     * 创建应收单实例
     * @param id
     * @param sourceVo
     * @param period
     * @return
     */
    private KingdeeReceivableEntity createReceivable(String id,String period,KingdeeSourceVo sourceVo){
        KingdeeReceivableEntity entity = new KingdeeReceivableEntity();
        entity.setId(id);
        entity.setNumber(CodeUtil.codeCreate("KD"));
        entity.setCompany(sourceVo.getCompany());
        entity.setPeriod(period);
        entity.setProject(sourceVo.getProject());
        entity.setProjectId(sourceVo.getProjectId());
        return entity;
    }

    /**
     * 创建应收明细实例
     * @param receivableId
     * @param product
     * @param customer
     * @param amount
     * @param taxRate
     * @return
     */
    private KingdeeReceivableItemEntity createReceivableItem(String receivableId,String product,String customer,String paymentType,String czType,BigDecimal amount,BigDecimal taxRate){
        BigDecimal noTaxAmount = amount.divide(BigDecimal.ONE.add(taxRate),2,BigDecimal.ROUND_HALF_UP);
        BigDecimal taxAmount = amount.subtract(noTaxAmount);
        KingdeeReceivableItemEntity entity = new KingdeeReceivableItemEntity();
        entity.setId(CodeUtil.generateUuid(true));
        entity.setReceivableId(receivableId);
        entity.setPaymentType(paymentType);
        entity.setCzType(czType);
        entity.setAmount(amount);
        entity.setTaxRate(taxRate);
        entity.setTaxAmount(taxAmount);
        entity.setNoTaxAmount(noTaxAmount);
        entity.setCustomer(customer);
        entity.setProduct(product);
        return entity;
    }

    /**
     * 创建应收明细与账单明细的关联
     * @param receivableItemId
     * @param orderItemId
     * @return
     */
    private KingdeeReceivableRelEntity createReceivableRel(String receivableItemId,String orderItemId){
        KingdeeReceivableRelEntity entity = new KingdeeReceivableRelEntity();
        entity.setId(CodeUtil.generateUuid(true));
        entity.setReceivableItemId(receivableItemId);
        entity.setOrderItemId(orderItemId);
        return entity;
    }

    //===收入摊销相关===============================================================================================
    private KingdeeAmortizeEntity createAmortize(String id,String dyPeriod,KingdeeSourceVo sourceVo){
        KingdeeAmortizeEntity entity = new KingdeeAmortizeEntity();
        entity.setId(id);
        entity.setNumber(CodeUtil.codeCreate("KD"));
        entity.setCompany(sourceVo.getCompany());
        entity.setDyPeriod(dyPeriod);
        entity.setProject(sourceVo.getProject());
        entity.setProjectId(sourceVo.getProjectId());
        return entity;
    }

    private KingdeeAmortizeItemEntity createAmortizeItem(String amortizeId,String period,String product,String customer,String paymentType,String czType,BigDecimal amount){
        KingdeeAmortizeItemEntity entity = new KingdeeAmortizeItemEntity();
        //entity.setId(CodeUtil.generateUuid(true));
        entity.setAmortizeId(amortizeId);
        entity.setPaymentType(paymentType);
        entity.setCzType(czType);
        entity.setPeriod(period);
        entity.setAmount(amount);
        entity.setCustomer(customer);
        entity.setProduct(product);
        return entity;
    }

    private KingdeeAmortizeRelEntity createAmortizeRel(String amortizeItemId,String revenueId){
        KingdeeAmortizeRelEntity entity = new KingdeeAmortizeRelEntity();
        entity.setId(CodeUtil.generateUuid(true));
        entity.setAmortizeItemId(amortizeItemId);
        entity.setRevenueId(revenueId);
        return entity;
    }

    private BigDecimal getRevenueByMonth(RptRevenueEntity revenue, int month){
        BigDecimal payment = BigDecimal.ZERO;
        switch (month){
            case 1:
                payment = revenue.getJan();
                break;
            case 2:
                payment = revenue.getFeb();
                break;
            case 3:
                payment = revenue.getMar();
                break;
            case 4:
                payment = revenue.getApr();
                break;
            case 5:
                payment = revenue.getMay();
                break;
            case 6:
                payment = revenue.getJun();
                break;
            case 7:
                payment = revenue.getJul();
                break;
            case 8:
                payment = revenue.getAug();
                break;
            case 9:
                payment = revenue.getSept();
                break;
            case 10:
                payment = revenue.getOct();
                break;
            case 11:
                payment = revenue.getNov();
                break;
            case 12:
                payment = revenue.getDece();
                break;
        }
        return payment==null?BigDecimal.ZERO:payment;
    }

    //===开票信息相关=====================================================================================================
    private KingdeeInvoiceEntity createInvoice(String startPeriod,String endPeriod,KingdeeSourceVo sourceVo){
        KingdeeInvoiceEntity entity = new KingdeeInvoiceEntity();
        entity.setNumber(CodeUtil.codeCreate("KD"));
        entity.setCompany(sourceVo.getCompany());
        entity.setStartPeriod(startPeriod);
        entity.setEndPeriod(endPeriod);
        entity.setProject(sourceVo.getProject());
        entity.setProjectId(sourceVo.getProjectId());
        return entity;
    }

    private KingdeeInvoiceItemEntity createInvoiceItem(String invoiceId,String customer,String issuingOffice,String startPeriod,String endPeriod,BigDecimal amount,BigDecimal taxAmount,String reportInviceId){
        KingdeeInvoiceItemEntity entity = new KingdeeInvoiceItemEntity();
        entity.setInvoiceId(invoiceId);
        entity.setPaymentType(PaymentTypeEnum.COLLECT.getValue());
        entity.setCustomer(customer);
        entity.setIssuingOffice(issuingOffice);
        entity.setStartPeriod(startPeriod);
        entity.setEndPeriod(endPeriod);
        entity.setAmount(amount);
        entity.setTaxAmount(taxAmount);
        entity.setReportInvoiceId(reportInviceId);
        return entity;
    }

    //===推送接口Start===============================================================================================
    public JSONObject AmortizePush(String id){
        KingdeeAmortizeVo entity = kingdeeAmortizeService.selectVoById(id);
        entity.setPushPeriod(DateUtil.format(new Date(),"yyyyMM"));
        cn.hutool.json.JSONObject data = JSONUtil.parseObj(entity);
        JSONObject json = new JSONObject();
        json.put("bosType", BosTypeEnum.AMORTIZE.getValue());
        json.put("company", entity.getCompany());
        json.put("id", id);
        json.put("number", entity.getNumber());
        json.put("data", data);
        JSONObject res = xyKingdeeFegin.kingdeePush(JSONUtil.toJsonStr(json),entity.getKingdeeId());
        if("S".equals(res.getString("result"))){
            JSONObject resData = res.getJSONObject("data");
            entity.setKingdeeId(resData.getString("kingdeeId"));
            kingdeeAmortizeService.updateById(entity);
        }
        return res;
    }

    /**
     * 删除摊销单数据
     * @param id
     * @return
     */
    @Override
    public JSONObject AmortizeDelete(String id) {
        JSONObject jsonObject = new JSONObject();
        //判断当前摊销数据在金蝶是否存在
        KingdeeAmortizeEntity amortizeEntity = kingdeeAmortizeService.getById(id);
        boolean exist = xyKingdeeFegin.isExist(amortizeEntity.getKingdeeId());
        if(exist){
            jsonObject.put("result","FAIL");
            jsonObject.put("msg","当前摊销数据在金蝶已存在，不允许删除");
            return jsonObject;
        }
        //删除摊销数据以及子表、关联关系的数据
        kingdeeAmortizeService.removeById(id);
        List<String> amortizeItemIds = kingdeeAmortizeItemService.getKingDeeAmortizeItemList(id).stream().map(item -> item.getId()).collect(Collectors.toList());

        QueryWrapper<KingdeeAmortizeItemEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("amortize_id",id);
        kingdeeAmortizeItemService.remove(queryWrapper);

        for(String item:amortizeItemIds){
//            QueryWrapper<KingdeeAmortizeRelEntity> queryWrapperRel = new QueryWrapper();
//            queryWrapper.eq("amortize_item_id",item);
            kingdeeAmortizeRelService.remove(Wrappers.<KingdeeAmortizeRelEntity>query().lambda().eq(KingdeeAmortizeRelEntity::getAmortizeItemId,item));
        }
        jsonObject.put("result","S");
        return jsonObject;
    }
    //===推送接口End===============================================================================================
    public JSONObject PaymentPush(String id){
        KingdeePaymentVo entity = kingdeePaymentService.selectVoById(id);
        cn.hutool.json.JSONObject data = JSONUtil.parseObj(entity);
        JSONObject json = new JSONObject();
        json.put("bosType", BosTypeEnum.PAYMENT.getValue());
        json.put("company", entity.getCompany());
        json.put("id", id);
        json.put("number", entity.getNumber());
        json.put("data", data);
        JSONObject res = xyKingdeeFegin.kingdeePush(JSONUtil.toJsonStr(json),entity.getKingdeeId());
        if("S".equals(res.getString("result"))){
            JSONObject resData = res.getJSONObject("data");
            entity.setKingdeeId(resData.getString("kingdeeId"));
            kingdeePaymentService.updateById(entity);
        }
        return res;
    }

    public JSONObject TransferPush(String id){
        KingdeeTransferVo entity = kingdeeTransferService.selectVoById(id);
        cn.hutool.json.JSONObject data = JSONUtil.parseObj(entity);
        JSONObject json = new JSONObject();
        json.put("bosType", BosTypeEnum.TRANSFER.getValue());
        json.put("company", entity.getCompany());
        json.put("id", id);
        json.put("number", entity.getNumber());
        json.put("data", data);
        JSONObject res = xyKingdeeFegin.kingdeePush(JSONUtil.toJsonStr(json),entity.getKingdeeId());
        if("S".equals(res.getString("result"))){
            JSONObject resData = res.getJSONObject("data");
            entity.setKingdeeId(resData.getString("kingdeeId"));
            kingdeeTransferService.updateById(entity);
        }
        return res;
    }

    public JSONObject ReceivablePush(String id){
        KingdeeReceivableVo entity = kingdeeReceivableService.selectVoById(id);
        cn.hutool.json.JSONObject data = JSONUtil.parseObj(entity);
        JSONObject json = new JSONObject();
        json.put("bosType", BosTypeEnum.RECEIVABLE.getValue());
        json.put("company", entity.getCompany());
        json.put("id", id);
        json.put("number", entity.getNumber());
        json.put("data", data);
        JSONObject res = xyKingdeeFegin.kingdeePush(JSONUtil.toJsonStr(json),entity.getKingdeeId());
        if("S".equals(res.getString("result"))){
            JSONObject resData = res.getJSONObject("data");
            entity.setKingdeeId(resData.getString("kingdeeId"));
            kingdeeReceivableService.updateById(entity);
        }
        return res;
    }
}
