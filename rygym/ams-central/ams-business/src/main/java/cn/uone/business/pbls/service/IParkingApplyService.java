package cn.uone.business.pbls.service;

import cn.uone.bean.entity.business.pbls.ParkingApplyEntity;
import cn.uone.bean.entity.business.pbls.ParkingRecordEntity;
import cn.uone.bean.entity.business.pbls.vo.ParkingApplyVo;
import cn.uone.bean.entity.business.pbls.vo.ParkingSearchVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
public interface IParkingApplyService extends IService<ParkingApplyEntity> {

    IPage<ParkingApplyVo> findByCondition(Page page, ParkingSearchVo parkingSearchVo);

    IPage<ParkingApplyVo> selectParkingRecordByMap(Page page, ParkingSearchVo parkingSearchVo);

    Map<String,Object> auditApply(ParkingApplyEntity parkingApplyEntity);

    Map<String,Object> editParkInfo(ParkingApplyEntity old, ParkingApplyEntity entity, ParkingRecordEntity recordEntity) throws Exception;

    ParkingApplyVo getApplyVo(ParkingSearchVo parkingSearchVo);

    /**
     * 本年的（每月）车位申请数量
     */
    Map<String,Object> getQuantityForMonth();

    int selectBySpaceId(String spaceId);

    int selectByLicense(String license);

    int selectByUserId(String userId);

    int countByStatus();

    ParkingApplyEntity getByLicense(String license);
}
