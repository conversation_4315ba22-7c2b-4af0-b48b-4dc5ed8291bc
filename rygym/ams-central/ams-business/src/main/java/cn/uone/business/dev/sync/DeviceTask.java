package cn.uone.business.dev.sync;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.dev.DevClassEntity;
import cn.uone.bean.entity.business.dev.DevDeviceEntity;
import cn.uone.bean.entity.business.dev.DevSupplierEntity;
import cn.uone.bean.entity.business.res.*;
import cn.uone.bean.entity.business.res.vo.ResSourceDeviceRelationExportVo;
import cn.uone.bean.entity.job.vo.AsyncResultVo;
import cn.uone.business.dev.service.IDevClassService;
import cn.uone.business.dev.service.IDevDeviceService;
import cn.uone.business.dev.service.IDevSupplierService;
import cn.uone.business.res.service.*;
import cn.uone.util.CodeUtil;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Component
public class DeviceTask {

    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IResSourceDeviceRelService resSourceDeviceRelService;
    @Autowired
    private IDevClassService devClassService;
    @Autowired
    private IDevSupplierService devSupplierService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    private IResPlanPartitionService resPlanPartitionService;
    @Autowired
    private IDevDeviceService devDeviceService;
    @Autowired
    private IResHouseTypeService resHouseTypeService;

    @Async
    public CompletableFuture<AsyncResultVo<List<DevDeviceEntity>>> impNoAi(ResSourceDeviceRelationExportVo vo, int i,boolean pub,String cityCode) {
        String msg = judge("", i, vo, false);
        List<String> sourceIds = Lists.newArrayList();
        String sourceId="";
        AsyncResultVo<List<DevDeviceEntity>> results= new AsyncResultVo();

        if (StrUtil.isNotBlank(msg)) {
            results.setMsg(msg);
            return CompletableFuture.completedFuture(results);
        }

        ResProjectEntity projectEntity = getProject(vo.getProjectName());
        //区域
        ResPlanPartitionEntity planPartitionEntity = getPartition(projectEntity.getId(), vo.getPartitionName());
        if (planPartitionEntity == null) {
            msg += "非智能设备:第" + i + "行区域不存在,";
        }

        DevSupplierEntity supplierEntity=devSupplierService.getOne(new QueryWrapper<DevSupplierEntity>().eq("name", vo.getSupplier()));
        if(ObjectUtil.isNull(supplierEntity)){
            //msg+="智能设备:第"+i+"行供应商不存在,";
            supplierEntity = new DevSupplierEntity();
            supplierEntity.setName(vo.getSupplier());
            devSupplierService.save(supplierEntity);
        }
        DevClassEntity classEntity=devClassService.getOne(new QueryWrapper<DevClassEntity>().eq("name",vo.getDeviceName()));
        if(ObjectUtil.isNull(classEntity)){
            //msg+="智能设备:第"+i+"行设备名称不存在,";
            classEntity = new DevClassEntity();
            classEntity.setName(vo.getDeviceName());
            devClassService.save(classEntity);
        }

       /* DevSupplierEntity supplierEntity = devSupplierService.getOne(new QueryWrapper<DevSupplierEntity>().eq("name", vo.getSupplier()));
        if (ObjectUtil.isNull(supplierEntity)) {
            msg += "非智能设备:第" + i + "行供应商不存在,";
        }
        DevClassEntity classEntity = devClassService.getOne(new QueryWrapper<DevClassEntity>().eq("name", vo.getDeviceName()));
        if (ObjectUtil.isNull(classEntity)) {
            msg += "非智能设备:第" + i + "行设备名称不存在,";
        }*/
        List<ResSourceEntity> sourceList = Lists.newArrayList();
        if(pub){
            if(!"房源".equals(vo.getCodeType())){
                msg += "共用设备仅支持导入类型为房源的记录";
            }else if (StrUtil.isNotBlank(vo.getCode())) {
                String[] sources = vo.getCode().split(",");
                for (String s : sources) {
                    ResSourceEntity sourceEntity = getSource(projectEntity.getId(), planPartitionEntity.getId(), s);
                    if (sourceEntity == null) {
                        msg += "非智能设备:第" + i + "行房间号不存在,";
                    }
                    if (StrUtil.isBlank(msg)) {
                        sourceIds.add(sourceEntity.getId());
                        sourceList.add(sourceEntity);
                    }
                }
            }
        }else{
            if("户型".equals(vo.getCodeType())){
                QueryWrapper query = new QueryWrapper();
                query.eq("project_id",projectEntity.getId());
                query.eq("name",vo.getCode());
                ResHouseTypeEntity houseType = resHouseTypeService.getOne(query);
                if(houseType != null){
                    query = new QueryWrapper();
                    query.eq("project_id",projectEntity.getId());
                    query.eq("partition_id",planPartitionEntity.getId());
                    query.eq("house_type_id",houseType.getId());
                    sourceList = resSourceService.list(query);
                }
            }else{
                ResSourceEntity sourceEntity = getSource(projectEntity.getId(), planPartitionEntity.getId(), vo.getCode());
                if (sourceEntity == null) {
                    msg += "非智能设备:第" + i + "行房间号不存在,";
                }else{
                    //sourceId=sourceEntity.getId();
                    sourceList.add(sourceEntity);
                }
            }
        }
        if (StrUtil.isBlank(msg)) {
            List<DevDeviceEntity> list = Lists.newArrayList();
            for (ResSourceEntity source: sourceList) {
                DevDeviceEntity devDeviceEntity = new DevDeviceEntity();
                devDeviceEntity.setSupplierId(supplierEntity.getId());
                devDeviceEntity.setClassId(classEntity.getParentId());
                devDeviceEntity.setTypeId(classEntity.getId());
                //devDeviceEntity.setCode(devClassService.createCode("", classEntity.getParentId(),cityCode));
                devDeviceEntity.setCode(CodeUtil.codeCreate("RKSB-"));
                if(StrUtil.isNotBlank(vo.getNum())){//轨道项目设备数量可能为空,进行判断，caizhanghe edit 2025-03-03
                    devDeviceEntity.setNum(Integer.valueOf(vo.getNum()));
                }
                devDeviceEntity.setProjectId(projectEntity.getId());
                devDeviceEntity.setPrice(new BigDecimal(vo.getPrice()));
                devDeviceEntity.setDamage(new BigDecimal(vo.getDamage()));
                if(pub){
                    devDeviceEntity.setSourceIds(sourceIds);
                }else{
                    devDeviceEntity.setSourceId(source.getId());
                }
                list.add(devDeviceEntity);
                results.setObj(list);
            }
        } else {
            results.setMsg(msg);
            return CompletableFuture.completedFuture(results);
        }

        return CompletableFuture.completedFuture(results);
    }


    @Async
    public CompletableFuture<AsyncResultVo<List<DevDeviceEntity>>> impPAi(ResSourceDeviceRelationExportVo vo,int i){
        String msg = judge("", i, vo, true);
        AsyncResultVo<List<DevDeviceEntity>> result = new AsyncResultVo<>();
        if(StrUtil.isNotBlank(msg)){
            result.setMsg(msg);
            return CompletableFuture.completedFuture(result);
        }
        ResProjectEntity projectEntity=getProject(vo.getProjectName());
        //区域
        ResPlanPartitionEntity planPartitionEntity = getPartition(projectEntity.getId(), vo.getPartitionName());
        if (planPartitionEntity == null) {
            msg += "智能设备:第" + i + "行区域不存在,";
        }

        DevSupplierEntity supplierEntity = devSupplierService.getOne(new QueryWrapper<DevSupplierEntity>().eq("name", vo.getSupplier()));
        if(ObjectUtil.isNull(supplierEntity)){
            //msg+="智能设备:第"+i+"行供应商不存在,";
            supplierEntity = new DevSupplierEntity();
            supplierEntity.setName(vo.getSupplier());
            devSupplierService.save(supplierEntity);
        }
        DevClassEntity classEntity = devClassService.getOne(new QueryWrapper<DevClassEntity>().eq("name", vo.getDeviceName()));
        if(ObjectUtil.isNull(classEntity)){
            //msg+="智能设备:第"+i+"行设备名称不存在,";
            classEntity = new DevClassEntity();
            classEntity.setName(vo.getDeviceName());
            devClassService.save(classEntity);
        }
        //房间号
        if (StrUtil.isNotBlank(vo.getCode())) {
            String[] sources = vo.getCode().split(",");
            List<String> sourceIds = Lists.newArrayList();
            DevDeviceEntity device = null;
            for (String s : sources) {
                ResSourceEntity sourceEntity = getSource(projectEntity.getId(), planPartitionEntity.getId(), s);
                if (sourceEntity == null) {
                    msg += "智能设备:第" + i + "行房间号不存在,";
                    break;
                }
                if (StrUtil.isBlank(msg)) {
                    sourceIds.add(sourceEntity.getId());
                }
                device = devDeviceService.getByCode(vo.getDeviceCode());
                if (ObjectUtil.isNotNull(device)) {
                    ResSourceDeviceRelEntity rel = resSourceDeviceRelService.getOne(new QueryWrapper<ResSourceDeviceRelEntity>().eq("device_id", device.getId()).eq("is_public", false));
                    if (ObjectUtil.isNotNull(rel)) {
                        msg += "智能设备:第" + i + "行设备编号已关联,";
                    } else {
                        ResSourceDeviceRelEntity r = resSourceDeviceRelService.getOne(new QueryWrapper<ResSourceDeviceRelEntity>().eq("device_id", device.getId()).eq("is_public", true).eq("source_id", sourceEntity.getId()));
                        if (ObjectUtil.isNotNull(r)) {
                            sourceIds.remove(sourceEntity.getId());
                        }
                    }
                }
            }
            if (StrUtil.isBlank(msg)) {
                DevDeviceEntity devDeviceEntity = null;
                if (ObjectUtil.isNotNull(device)) {
                    devDeviceEntity = device;
                    devDeviceEntity.setPrice(new BigDecimal(vo.getPrice()));
                    devDeviceEntity.setDamage(new BigDecimal(vo.getDamage()));
                    devDeviceEntity.setSourceIds(sourceIds);
                } else {
                    devDeviceEntity = new DevDeviceEntity();
                    devDeviceEntity.setCode(vo.getDeviceCode());
                    devDeviceEntity.setProjectId(projectEntity.getId());
                    devDeviceEntity.setNum(1);
                    devDeviceEntity.setPrice(new BigDecimal(vo.getPrice()));
                    devDeviceEntity.setDamage(new BigDecimal(vo.getDamage()));
                    devDeviceEntity.setSourceIds(sourceIds);
                }
                if(supplierEntity != null)
                    devDeviceEntity.setSupplierId(supplierEntity.getId());
                if(classEntity != null){
                    devDeviceEntity.setClassId(classEntity.getParentId());
                    devDeviceEntity.setTypeId(classEntity.getId());
                }
                List<DevDeviceEntity> list = Lists.newArrayList();
                list.add(devDeviceEntity);
                result.setObj(list);
            }else{
                result.setMsg(msg);
            }
        }
        return CompletableFuture.completedFuture(result);
   }


    @Async
    public CompletableFuture<AsyncResultVo<List<DevDeviceEntity>>> impAi(ResSourceDeviceRelationExportVo vo,int i){
        String msg=judge("",i,vo,true);
        AsyncResultVo<List<DevDeviceEntity>> result = new AsyncResultVo<>();
        if(StrUtil.isNotBlank(msg)){
            result.setMsg(msg);
            return CompletableFuture.completedFuture(result);
        }
        ResProjectEntity projectEntity=getProject(vo.getProjectName());
        //区域
        ResPlanPartitionEntity planPartitionEntity=getPartition(projectEntity.getId(),vo.getPartitionName());
        if(planPartitionEntity==null){
            msg+="智能设备:第"+i+"行区域不存在,";
            result.setMsg(msg);
            return CompletableFuture.completedFuture(result);
        }
        //房间号
        ResSourceEntity sourceEntity=getSource(projectEntity.getId(),planPartitionEntity.getId(),vo.getCode());
        if(sourceEntity==null){
            msg+="智能设备:第"+i+"行房间号不存在,";
            result.setMsg(msg);
            return CompletableFuture.completedFuture(result);
        }

        DevSupplierEntity supplierEntity=devSupplierService.getOne(new QueryWrapper<DevSupplierEntity>().eq("name", vo.getSupplier()));
        if(ObjectUtil.isNull(supplierEntity)){
            //msg+="智能设备:第"+i+"行供应商不存在,";
            supplierEntity = new DevSupplierEntity();
            supplierEntity.setName(vo.getSupplier());
            devSupplierService.save(supplierEntity);
        }
        DevClassEntity classEntity=devClassService.getOne(new QueryWrapper<DevClassEntity>().eq("name",vo.getDeviceName()));
        if(ObjectUtil.isNull(classEntity)){
            //msg+="智能设备:第"+i+"行设备名称不存在,";
            classEntity = new DevClassEntity();
            classEntity.setName(vo.getDeviceName());
            devClassService.save(classEntity);
        }
        DevDeviceEntity device=devDeviceService.getByCode(vo.getDeviceCode());
        boolean flag=true;
        if(ObjectUtil.isNotNull(device)){
            List<ResSourceDeviceRelEntity> rels= resSourceDeviceRelService.list(new QueryWrapper<ResSourceDeviceRelEntity>().eq("device_id",device.getId()));
            if(rels.size()>0){
                ResSourceDeviceRelEntity rel= resSourceDeviceRelService.getOne(new QueryWrapper<ResSourceDeviceRelEntity>().eq("device_id",device.getId()).eq("is_public",false).eq("source_id",sourceEntity.getId()));
                if(ObjectUtil.isNull(rel)){
                    msg+="智能设备:第"+i+"行设备编号已关联,";
                }else{
                    flag=false;
                }
            }
        }
        if(StrUtil.isBlank(msg)){
            DevDeviceEntity devDeviceEntity=null;
            if(flag){
                if(ObjectUtil.isNotNull(device)){
                    devDeviceEntity=device;
                    devDeviceEntity.setPrice(new BigDecimal(vo.getPrice()));
                    devDeviceEntity.setDamage(new BigDecimal(vo.getDamage()));
                    devDeviceEntity.setSourceId(sourceEntity.getId());
                }else{
                    devDeviceEntity=new DevDeviceEntity();
                    devDeviceEntity.setCode(vo.getDeviceCode());
                    devDeviceEntity.setProjectId(projectEntity.getId());
                    devDeviceEntity.setNum(1);
                    devDeviceEntity.setPrice(new BigDecimal(vo.getPrice()));
                    devDeviceEntity.setDamage(new BigDecimal(vo.getDamage()));
                    devDeviceEntity.setSourceId(sourceEntity.getId());
                }
            }else{
                devDeviceEntity=device;
                devDeviceEntity.setPrice(new BigDecimal(vo.getPrice()));
                devDeviceEntity.setDamage(new BigDecimal(vo.getDamage()));
            }
            if(supplierEntity != null)
                devDeviceEntity.setSupplierId(supplierEntity.getId());
            if(classEntity != null){
                devDeviceEntity.setClassId(classEntity.getParentId());
                devDeviceEntity.setTypeId(classEntity.getId());
            }
            List<DevDeviceEntity> list = Lists.newArrayList();
            list.add(devDeviceEntity);
            result.setObj(list);
        }else {
            result.setMsg(msg);
        }
        return CompletableFuture.completedFuture(result);
    }


    public String judge(String str,int i,ResSourceDeviceRelationExportVo vo,boolean isAi){

        String title = "非智能设备:";
        if(isAi){
            title = "智能设备:";
        }

        if(StrUtil.isBlank(vo.getProjectName())){
            str+= title+"第"+i+"行项目名称不能为空,";
        }
        if(StrUtil.isBlank(vo.getPartitionName())){
            str+=  title+"第"+i+"行区域名称不能为空,";
        }
        if(StrUtil.isBlank(vo.getCode())){
            str+=  title+"第"+i+"行房间号/户型号不能为空,";
        }
        if(!isAi && StrUtil.isBlank(vo.getCodeType())){
            str+=  title+"第"+i+"行导入类型不能为空,";
        }
        if(StrUtil.isBlank(vo.getDeviceName())){
            str+=  title+"第"+i+"行设备名称不能为空,";
        }
        if(StrUtil.isBlank(vo.getSupplier())){
            str+=  title+"第"+i+"行供应商不能为空,";
        }
        if(StrUtil.isBlank(vo.getPrice())){
            str+=  title+"第"+i+"行采购单价不能为空,";
        }
        if(StrUtil.isBlank(vo.getDamage())){
            str+=  title+"第"+i+"行赔偿价不能为空,";
        }
        boolean ifContain = vo.getProjectName().contains("爱萌公寓");//轨道项目设备导入暂不校验数量 caizhanghe 2025-03-03
        if(!ifContain){
            if(StrUtil.isBlank(vo.getNum())&&!isAi){
                str+= title+"第"+i+"行设备数量不能为空,";
            }
        }
        return str;
    }

    private ResProjectEntity getProject(String projectName){
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("name",projectName);
        return resProjectService.getOne(queryWrapper);
    }
    private ResPlanPartitionEntity getPartition(String projectId,String partitionName){
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("name",partitionName);
        queryWrapper.eq("project_id",projectId);
        return resPlanPartitionService.getOne(queryWrapper);
    }
    private ResSourceEntity getSource(String projectId,String partitionId,String code){
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("project_id",projectId);
        queryWrapper.eq("partition_id",partitionId);
        queryWrapper.eq("code",code);
        return resSourceService.getOne(queryWrapper);
    }
}
