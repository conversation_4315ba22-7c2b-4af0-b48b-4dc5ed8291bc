package cn.uone.business.demo.service;

import cn.uone.bean.entity.business.demo.DemoFileEntity;
import cn.uone.business.demo.vo.FileVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-12
 */
public interface IDemoFileService extends IService<DemoFileEntity> {

    IPage<FileVo> queryPage(Page page, String contractCode);
}
