package cn.uone.business.rpt.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.contract.InvoiceTypeEnum;
import cn.uone.application.enumerate.order.InvoiceStateEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.invoice.InvoiceAccountEntity;
import cn.uone.bean.entity.business.invoice.InvoiceAccountOrdertypeRelEntity;
import cn.uone.bean.entity.business.report.InvoiceEntity;
import cn.uone.bean.entity.business.report.vo.InvoiceVo;
import cn.uone.bean.entity.business.res.ResProjectInfoEntity;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.parameter.InvoicePo;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.invoice.service.IInvoiceAccountOrdertypeRelService;
import cn.uone.business.invoice.service.IInvoiceAccountService;
import cn.uone.business.res.service.IResProjectInfoService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.rpt.dao.ReportInvoiceDao;
import cn.uone.business.rpt.service.IReportInvoiceService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.tpi.IBaiwangFegin;
import cn.uone.fegin.tpi.INuonuoFegin;
import cn.uone.fegin.tpi.INuonuoSdkFegin;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.util.DateParseException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.MDC;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import cn.uone.bean.entity.business.bil.vo.BatchInvoiceResultVo;

/**
 * <p>
 * 开票管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
@Service
@Slf4j
public class ReportInvoiceServiceImpl  extends ServiceImpl<ReportInvoiceDao, InvoiceEntity>  implements IReportInvoiceService {
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private INuonuoFegin nuonuoFegin;
    @Autowired
    private IBilOrderItemService orderItemService;
    @Autowired
    private IResProjectInfoService projectInfoService ;
    @Autowired
    private IResSourceService sourceService ;
    @Autowired
    @Lazy
    private IBilOrderService orderService ;
    @Autowired
    private IResProjectParaService projectParaService;
    @Autowired
    private INuonuoSdkFegin nuonuoSdkFegin;
    @Autowired
    private IBaiwangFegin baiwangFegin;
    @Autowired
    private IInvoiceAccountOrdertypeRelService invoiceAccountOrdertypeRelService;
    @Autowired
    private IInvoiceAccountService invoiceAccountService;
    @Autowired
    private RedissonClient redissonClient;
    
    @Autowired
    private ReportInvoiceServiceImpl self;

    @Override
    public IPage<InvoiceVo> selectPages(Page page, InvoicePo param) {
        return baseMapper.queryList(page,param);
    }

    @Override
    public List<InvoiceVo> getList(InvoicePo param) {
        return baseMapper.queryList(param);
    }

    @Override
    public List<InvoiceVo> export(InvoicePo param) {
        return baseMapper.queryList(param);
    }

    @Override
    public String getLastSerialNoByUserId(String userId) {
        return baseMapper.getLastSerialNoByUserId(userId);
    }

    @Override
    public List<Map<String, Object>> getTaxListForKingdee(Map<String, Object> map) {
        return baseMapper.getTaxListForKingdee(map);
    }

    @Override
    public List<InvoiceEntity> getInvoicing() {
        return baseMapper.getInvoicing();
    }

    @Override
    public void makeInvoice(BilOrderEntity order) throws Exception {
        // 状态检查：确保只有未开票状态的订单才能进行开票
        if (!InvoiceStateEnum.UNBILLED.getValue().equals(order.getInvoiceState())) {
            String currentStatus = InvoiceStateEnum.getNameByValue(order.getInvoiceState());
            throw new Exception("订单[" + order.getCode() + "]当前状态为[" + currentStatus + "]，不允许重复开票");
        }
        
        // 修改订单状态为"开票中"，这是一个原子操作
        BilOrderEntity updateStatusOrder = new BilOrderEntity();
        updateStatusOrder.setId(order.getId());
        updateStatusOrder.setInvoiceState(InvoiceStateEnum.INVOICING.getValue());
        boolean updateSuccess = orderService.updateById(updateStatusOrder);
        if (!updateSuccess) {
            throw new Exception("更新订单状态失败，可能是订单已被修改");
        }
        
        InvoiceVo invoiceVo=new InvoiceVo();
        // TODO 获取发票的备注信息以及门店信息
        Map<String,String> invoiceMap = orderItemService.getInvoiceInfo(order.getId());
        invoiceVo.setLocation(invoiceMap.get("source_name"));
        invoiceVo.setRentStart(invoiceMap.get("startTime"));
        invoiceVo.setRentEnd(invoiceMap.get("endTime"));
        //买方信息
        RenterEntity renter=renterFegin.getById(order.getPayerId());
        invoiceVo.setIsOrganize("0");//个人
        invoiceVo.setTaxName(renter.getName());//买方抬头
        invoiceVo.setPhone(renter.getTel());
        invoiceVo.setEmail(renter.getEmail());
        invoiceVo.setAddress(invoiceMap.get("address")+invoiceMap.get("source_name"));
        //账单信息
        // TODO 获取开票税率、税号信息
        ResSourceEntity sourceEntity = sourceService.getById(order.getSourceId());
        String taxInfo = invoiceVo.getRentStart()+"至"+invoiceVo.getRentEnd() + OrderTypeEnum.getNameByValue(order.getOrderType());
        invoiceVo.setOrderCode(order.getCode());
        invoiceVo.setTaxInfo(taxInfo);
        ResProjectParaEntity paraEntity = projectParaService.getByCodeAndProjectId(ProjectParaEnum.RENT_TAXES.getValue(),sourceEntity.getProjectId());
        BigDecimal taxIncludedAmount = order.getActualPayment();
        BigDecimal taxRate = new BigDecimal(paraEntity.getParamValue());
        //BigDecimal taxExcludedAmount = taxIncludedAmount.divide(BigDecimal.ONE.add(taxRate),2,BigDecimal.ROUND_HALF_UP);
        //BigDecimal tax = taxIncludedAmount.subtract(taxExcludedAmount);

        //invoiceVo.setTax(tax);
        invoiceVo.setTaxRate(taxRate);
        //invoiceVo.setTaxfreeamt(taxExcludedAmount);
        invoiceVo.setTaxamt(taxIncludedAmount);

        RestResponse invoiceRes = nuonuoSdkFegin.createInvoice(invoiceVo);
        InvoiceEntity invoice=new InvoiceEntity();
        BeanUtils.copyProperties(invoiceVo,invoice);
        
        // 根据开票结果更新订单状态
        BilOrderEntity resultOrder = new BilOrderEntity();
        resultOrder.setId(order.getId());
        
        if(invoiceRes.getSuccess()){
            invoice.setRequestCode((String) invoiceRes.get("invoiceSerialNum"));
            invoice.setTaxDate(new Date());
            invoice.setOrderId(order.getId()); // 存放订单id数据
            invoice.insertOrUpdate();
            resultOrder.setInvoicePayment(order.getActualPayment());
            resultOrder.setInvoiceType(InvoiceTypeEnum.PERSONAL.getValue());
            // 保持开票中状态，稍后通过定时任务更新为已开票
            List<BilOrderItemEntity> entityList=orderItemService.getItemsByOrderId(order.getId());
            for(BilOrderItemEntity entity:entityList){
                entity.setInvoiceId(invoice.getId());
                entity.insertOrUpdate();
            }
            String regex = "【[^】]+】";
            String remark = order.getRemark() != null ? order.getRemark().replaceAll(regex, "") : "";
            resultOrder.setRemark(remark);
        }else{
            // 开票失败，回滚状态为未开票
            resultOrder.setInvoiceState(InvoiceStateEnum.INVOICEFAILD.getValue());
            String errorMsg = "【开票失败：" + invoiceRes.getMessage() + "】";
            resultOrder.setRemark(order.getRemark() != null ? order.getRemark() + errorMsg : errorMsg);
        }
        // 更新订单状态
        resultOrder.insertOrUpdate();
    }

    /**
     * 开票申请
     * @param order
     * @throws Exception
     */
    @Override
    public void appleInvoice(BilOrderEntity order) throws Exception {
        InvoiceEntity invoice=new InvoiceEntity();
        RenterEntity renter=renterFegin.getById(order.getPayerId());
        invoice.setTaxPayment(order.getPayablePayment());
        invoice.setTaxName(renter.getName());
        invoice.setTaxDate(new Date());
        invoice.setOrderId(order.getId()); // 存放订单id数据
        invoice.insertOrUpdate();
        order.setInvoiceState(InvoiceStateEnum.APPLE.getValue());
        order.insertOrUpdate();
    }

    @Override
    public String againPushInvoice(BilOrderEntity order) throws Exception {
        InvoiceEntity invoice = getOne(new QueryWrapper<InvoiceEntity>().eq("order_id",order.getId()));
        String msg = "";
        // 重新向诺诺那边推送发票信息
        InvoiceVo invoiceVo=new InvoiceVo();
        invoiceVo.setOrderCode(order.getCode());
        RenterEntity renter = renterFegin.getById(order.getPayerId());
        invoiceVo.setPhone(renter.getTel());
        invoiceVo.setIsOrganize("0");
        invoiceVo.setTaxPayment(order.getActualPayment());
        invoiceVo.setTaxName(renter.getName());
        ResSourceEntity sourceEntity = sourceService.getById(order.getSourceId());
        ResProjectInfoEntity projectInfoEntity = projectInfoService.getProjectInfo(sourceEntity.getProjectId());
        invoiceVo.setTaxStr(projectInfoEntity.getTax().toPlainString());
        invoiceVo.setTaxInfo(projectInfoEntity.getTaxInfo());
        invoiceVo.setTaxNum(projectInfoEntity.getTaxNum());
        //        ResProjectParaEntity paraEntity = projectParaService.getByCodeAndProjectId("BILL_DEPART",sourceEntity.getProjectId());
//        invoiceVo.setBillDepart(paraEntity.getParamValue());
        // TODO 获取发票的备注信息以及门店信息
        Map<String,String> invoiceMap = orderItemService.getInvoiceInfo(order.getId());
        invoiceVo.setLocation(invoiceMap.get("source_name"));
        invoiceVo.setRentStart(invoiceMap.get("startTime"));
        invoiceVo.setRentEnd(invoiceMap.get("endTime"));
        JSONObject json = nuonuoFegin.createInvoice(invoiceVo);
        if("0000".equals(json.get("status"))){
            invoice.setRequestCode(json.get("fpqqlsh").toString());//
            invoice.setTaxDate(new Date());
            invoice.insertOrUpdate();
            order.setInvoiceState(InvoiceStateEnum.INVOICING.getValue());
            order.insertOrUpdate();
            msg = "重新推送成功";
        }else{
            if("9999".equals(json.get("status"))){
                msg = "参数错误";
            }else if("9009".equals(json.get("status"))){
                msg = "系统错误";
            }else if("4000".equals(json.get("status"))){
                msg = "检查是否加密";
            }else if("400".equals(json.get("status"))){
                msg = "参数值为密文";
            }
        }
        return msg ;
    }

    @Override
    public List<String> queryTaxCodeList(Map<String, Object> query) {
        return baseMapper.queryTaxCodeList(query);
    }

    /**
     * 定时任务 每天晚上查询系统中发票状态为开票中的订单信息 修改开票状态
     */
    @Override
    public void queryInvoiceState(){
        List<InvoiceEntity> invoiceList= getInvoicing();
        JSONObject infoObject = null ;
        JSONArray listArray = null ;
        BilOrderEntity orderEntity = null ;
        for(InvoiceEntity entity:invoiceList) {
            infoObject = nuonuoFegin.queryInvoice(entity.getRequestCode());
            listArray =  (JSONArray) infoObject.get("list");
            orderEntity = orderService.getById(entity.getOrderId());
            if(null!=listArray){
                for (int i = 0; i < listArray.size(); i++) {
                    JSONObject object = (JSONObject) listArray.get(i);
                    if ("2".equals(object.get("c_status")) || "21".equals(object.get("c_status"))) {
                        entity.setTaxCode(object.get("c_fpdm").toString());
                        entity.setTaxNumber(object.get("c_fphm").toString());
                        entity.setPdfUrl(object.get("c_url").toString());
                        entity.setTaxAmount(object.getBigDecimal("c_hjse"));
                        entity.insertOrUpdate();
                        orderEntity.setInvoiceState(InvoiceStateEnum.INVOICED.getValue());
                        orderEntity.insertOrUpdate();
                    } else if("20".equals(object.get("c_status"))){

                    } else {
                        entity.setResultMsg(object.getStr("c_resultmsg"));
                        orderEntity.setInvoiceState(InvoiceStateEnum.INVOICEFAILD.getValue());
                        entity.insertOrUpdate();
                        orderEntity.insertOrUpdate();
                    }

                }
            } else {
                entity.setResultMsg("发票不存在");
                entity.insertOrUpdate();
                orderEntity.setInvoiceState(InvoiceStateEnum.INVOICEFAILD.getValue());
                orderEntity.insertOrUpdate();
            }
        }
    }

    /**
     * 定时任务 每天晚上查询系统中发票状态为开票中的订单信息 修改开票状态
     */
    @Override
    public void queryBaiwangInvoiceState() throws DateParseException {
        List<InvoiceEntity> invoiceList= getInvoicing();
        for(InvoiceEntity entity:invoiceList) {
            BilOrderEntity orderEntity = orderService.getById(entity.getOrderId());
            ResSourceVo source = sourceService.getInfoById(orderEntity.getSourceId());
            String projectId = source.getProjectId();
            QueryWrapper query = new QueryWrapper();
            query.eq("project_id",projectId);
            query.eq("order_type",orderEntity.getOrderType());
            InvoiceAccountOrdertypeRelEntity rel = invoiceAccountOrdertypeRelService.getOne(query);
            if(rel != null){
                InvoiceAccountEntity invoiceAccount = invoiceAccountService.getById(rel.getAccountId());
                RestResponse res = baiwangFegin.baiwangQuery("SAAS",invoiceAccount.getInvoiceTaxNo(),orderEntity.getCode());
                if (res.getSuccess()) {
                    JSONArray array = JSONUtil.parseArray(res.get("data"));
                    JSONObject json = array.getJSONObject(0);
                    String serialNo = json.getStr("serialNo");//请求流水号
                    String orderNo = json.getStr("orderNo");//开票单号
                    String invoiceTime = json.getStr("invoiceTime");//开票时间(yyyy-MM-dd HH:mm:ss)
                    String buyerName = json.getStr("buyerName");
                    String buyerTaxNo = json.getStr("buyerTaxNo");
                    String invoiceCode =  json.getStr("invoiceCode");//发票代码
                    String invoiceNo =  json.getStr("invoiceNo");//发票号码
                    String digitInvoiceNo =  json.getStr("digitInvoiceNo");//数电发票号码
                    String status =  json.getStr("status");//开票状态:00-开票中,01-开票完成,02-开票失败,03-发票已作废,04-发票作废中
                    String statusUpdateTime = json.getStr("statusUpdateTime");//发票状态更新时间
                    String statusMessage = json.getStr("statusMessage");//发票状态描述
                    String errorMessage = json.getStr("errorMessage");//错误信息
                    String pdfUrl = json.getStr("pdfUrl");//pdf地址
                    if("01".equals(status)){
                        entity.setPdfUrl(pdfUrl);
                        entity.setTaxCode(invoiceCode);
                        entity.setTaxNumber(digitInvoiceNo);
                        entity.setTaxDate(DateUtil.parseDateTime(invoiceTime));
                        orderEntity.setInvoiceState(InvoiceStateEnum.INVOICED.getValue());//修改订单中的开票状态为已开票
                    }else{
                        entity.setResultMsg(errorMessage);
                        String invoiceState = "02".equals(status)?InvoiceStateEnum.INVOICEFAILD.getValue():InvoiceStateEnum.INVOICING.getValue();
                        orderEntity.setInvoiceState(invoiceState);
                    }
                    entity.insertOrUpdate();;
                    orderEntity.setInvoiceType(InvoiceTypeEnum.PERSONAL.getValue());//默认开票类型是个人开票
                    orderEntity.setInvoicePayment(orderEntity.getActualPayment());//开票金额默认为实付金额
                    orderEntity.insertOrUpdate();
                }
            }
        }
    }

    @Override
    public IPage<InvoiceVo> getInvoicePages(Page page, InvoicePo param) {
        return baseMapper.queryList(page,param);
    }

    @Override
    public void queryInvoice(String id){
        InvoiceEntity entity = getById(id);
        JSONObject infoObject = null ;
        JSONArray listArray = null ;
        BilOrderEntity orderEntity = null ;
        infoObject = nuonuoFegin.queryInvoice(entity.getRequestCode());
        listArray =  (JSONArray) infoObject.get("list");
        orderEntity = orderService.getById(entity.getOrderId());
        if(null!=listArray){
            for (int i = 0; i < listArray.size(); i++) {
                JSONObject object = (JSONObject) listArray.get(i);
                if ("2".equals(object.get("c_status")) || "21".equals(object.get("c_status"))) {
                    entity.setTaxCode(object.get("c_fpdm").toString());
                    entity.setTaxNumber(object.get("c_fphm").toString());
                    entity.setPdfUrl(object.get("c_url").toString());
                    entity.setTaxAmount(object.getBigDecimal("c_hjse"));
                    entity.insertOrUpdate();
                    orderEntity.setInvoiceState(InvoiceStateEnum.INVOICED.getValue());
                    orderEntity.insertOrUpdate();
                } else if("20".equals(object.get("c_status"))){

                } else {
                    entity.setResultMsg(object.getStr("c_resultmsg"));
                    orderEntity.setInvoiceState(InvoiceStateEnum.INVOICEFAILD.getValue());
                    entity.insertOrUpdate();
                    orderEntity.insertOrUpdate();
                }

            }
        } else {
            entity.setResultMsg("发票不存在");
            entity.insertOrUpdate();
            orderEntity.setInvoiceState(InvoiceStateEnum.INVOICEFAILD.getValue());
            orderEntity.insertOrUpdate();
        }
    }

    
    /**
     * 在新事务中执行单笔开票
     * 使用REQUIRES_NEW传播行为确保每个订单的开票独立事务
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void makeInvoiceInNewTransaction(BilOrderEntity order) throws Exception {
        makeInvoice(order);
    }

    @Override
    public BatchInvoiceResultVo batchMakeInvoice(List<String> orderIds) {
        // 初始化结果对象
        BatchInvoiceResultVo result = BatchInvoiceResultVo.initialize();
        
        // 初始化批处理请求ID，用于日志关联
        String batchRequestId = UUID.randomUUID().toString().replace("-", "");
        MDC.put("batchRequestId", batchRequestId);
        
        log.info("开始批量开票处理，共 {} 个订单，批次ID: {}", orderIds.size(), batchRequestId);
        
        if (orderIds == null || orderIds.isEmpty()) {
            log.warn("批量开票参数为空，批次ID: {}", batchRequestId);
            return result;
        }
        
        // 遍历处理每个订单
        for (String orderId : orderIds) {
            // 为每个订单设置请求ID，便于跟踪
            String orderRequestId = UUID.randomUUID().toString().replace("-", "");
            MDC.put("requestId", orderRequestId);
            MDC.put("orderId", orderId);
            
            log.info("开始处理订单开票: {}, 请求ID: {}, 批次ID: {}", orderId, orderRequestId, batchRequestId);
            
            // 获取分布式锁
            RLock lock = redissonClient.getLock("invoice:order:" + orderId);
            
            try {
                // 尝试获取锁，最多等待10秒
                if (lock.tryLock(10, TimeUnit.SECONDS)) {
                    try {
                        // 在独立事务中处理单个订单开票
                        self.processSingleInvoiceInNewTransaction(orderId, result);
                        log.info("订单开票处理成功: {}, 请求ID: {}, 批次ID: {}", orderId, orderRequestId, batchRequestId);
                    } catch (Exception e) {
                        // 记录失败信息
                        String errorMsg = e.getMessage() != null ? e.getMessage() : "未知错误";
                        result.addFailure(orderId, errorMsg);
                        log.error("订单开票处理失败: {}, 原因: {}, 请求ID: {}, 批次ID: {}", 
                                orderId, errorMsg, orderRequestId, batchRequestId, e);
                    }
                } else {
                    // 获取锁超时
                    result.addFailure(orderId, "获取处理锁超时，请稍后重试");
                    log.warn("订单开票获取锁超时: {}, 请求ID: {}, 批次ID: {}", orderId, orderRequestId, batchRequestId);
                }
            } catch (InterruptedException e) {
                // 获取锁被中断
                result.addFailure(orderId, "获取处理锁被中断，请稍后重试");
                log.error("订单开票获取锁被中断: {}, 请求ID: {}, 批次ID: {}", orderId, orderRequestId, batchRequestId, e);
                Thread.currentThread().interrupt(); // 重置中断状态
            } finally {
                // 释放锁
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                    log.debug("释放订单锁: {}, 请求ID: {}, 批次ID: {}", orderId, orderRequestId, batchRequestId);
                }
                
                // 清理MDC
                MDC.remove("requestId");
                MDC.remove("orderId");
            }
        }
        
        // 记录批处理完成日志
        log.info("批量开票处理完成，总共 {} 个订单，成功 {} 个，失败 {} 个，批次ID: {}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailureCount(), batchRequestId);
        
        // 清理批处理MDC
        MDC.remove("batchRequestId");
        
        return result;
    }
    
    /**
     * 在新事务中处理单个订单开票
     * 此方法需要由self引用调用，以确保事务独立性
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void processSingleInvoiceInNewTransaction(String orderId, BatchInvoiceResultVo result) throws Exception {
        // 获取订单信息
        BilOrderEntity order = orderService.getById(orderId);
        
        if (order == null) {
            throw new Exception("订单不存在: " + orderId);
        }
        
        // 调用现有的单笔开票逻辑
        makeInvoice(order);
        
        // 记录成功
        result.addSuccess(orderId);
    }
}
