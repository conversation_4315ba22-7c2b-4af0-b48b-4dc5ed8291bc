package cn.uone.business.onlineBid.dao;

import cn.uone.bean.entity.business.equ.EquDeviceEntity;
import cn.uone.bean.entity.business.onlineBid.BidApplyEntity;
import cn.uone.bean.entity.business.supplies.CollectApplyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 报名表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
public interface BidApplyDao extends BaseMapper<BidApplyEntity> {

    IPage<BidApplyEntity> getListByPage(Page page, BidApplyEntity entity);

    Integer getApplyStatus(@Param("applicantId")String applicantId,@Param("bidId")String bidId);
    /**报名列表**/
    IPage<BidApplyEntity> getListByMy(Page page, BidApplyEntity entity);
}
