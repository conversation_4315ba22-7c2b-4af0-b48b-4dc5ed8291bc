package cn.uone.business.fixed.dao;

import cn.uone.bean.entity.business.fixed.FixedMaintenanceTeamEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 维保班组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
public interface FixedMaintenanceTeamDao extends BaseMapper<FixedMaintenanceTeamEntity> {
    IPage<FixedMaintenanceTeamEntity> queryByPage(Page page, @Param("map") Map<String,Object> map);
}
