package cn.uone.business.urge.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.urge.UrgePayRecordEntity;
import cn.uone.business.afforest.service.IAreaService;
import cn.uone.business.urge.service.IUrgePayRecordService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@RestController
@RequestMapping("/urgePay/record")
public class UrgePayRecordController extends BaseController {

    @Autowired
    private IUrgePayRecordService service;

    @GetMapping("/page")
    public RestResponse page(Page<UrgePayRecordEntity> page, UrgePayRecordEntity entity){
        QueryWrapper<UrgePayRecordEntity> wrapper = new QueryWrapper<>();

//        wrapper.eq("project_id",UoneHeaderUtil.getProjectId());
        if(StrUtil.isNotBlank(entity.getOperator())){
            wrapper.like("operator","%"+entity.getOperator()+"%");
        }
        if(StrUtil.isNotBlank(entity.getRenterName())){
            wrapper.like("renter_name","%"+entity.getRenterName()+"%");
        }
        if(StrUtil.isNotBlank(entity.getSourceName())){
            wrapper.like("source_name","%"+entity.getSourceName()+"%");
        }
        IPage<UrgePayRecordEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

}
