package cn.uone.business.base.controller;


import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import cn.uone.business.base.service.IBaseEnterpriseService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */

@Api(value = "企业服务", tags = "企业新增、修改等接口")
@RestController
@RequestMapping("/base/enterprise")
public class BaseEnterpriseController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(BaseEnterpriseController.class);

    @Autowired
    private IBaseEnterpriseService enterpriseService;

    /**
     * 新增企业
     *
     * @param baseEnterpriseEntity
     * @return
     * @throws Exception
     */
//    @UoneLog("新增企业")
//    @PostMapping("add")
//    public RestResponse add(@RequestBody BaseEnterpriseEntity baseEnterpriseEntity) throws Exception {
//        // 必填项判断; 新增企业;
//
//
//
//        // 新增企业
//        baseEnterpriseService.addBaseEnterprise(baseEnterpriseEntity);
//
//        return RestResponse.success("新增成功");
//    }

    /**
     * 修改企业
     *
     * @param baseEnterpriseEntity
     * @return
     * @throws Exception
     */
//    @UoneLog("修改企业")
//    @PostMapping("update")
//    public RestResponse update(@RequestBody BaseEnterpriseEntity baseEnterpriseEntity) throws Exception {
//        // 必填项判断; 参数判断; 修改企业;
//
//
//
//        // 参数判断
//        if (StrUtil.isBlank(baseEnterpriseEntity.getId())) {
//            return RestResponse.failure("参数异常");
//        }
//
//        // 修改企业
//        baseEnterpriseService.updateBaseEnterprise(baseEnterpriseEntity);
//
//        return RestResponse.success("修改成功");
//    }

    /**
     * 删除企业
     *
     * @param id
     * @return
     * @throws Exception
     */
//    @UoneLog("删除企业")
//    @PostMapping("delete")
//    public RestResponse delete(@RequestParam("id") String id) throws Exception {
//        // 参数判断; 删除企业;
//
//        // 参数判断
//        if (StrUtil.isBlank(id)) {
//            return RestResponse.failure("参数异常");
//        }
//
//        // 删除企业
//        baseEnterpriseService.deleteBaseEnterprise(id);
//
//        return RestResponse.success("删除成功");
//    }

    /**
     * 查看企业
     *
     * @param id
     * @return
     * @throws Exception
     */
//    @UoneLog("查看企业")
//    @PostMapping("view")
//    public RestResponse view(@RequestParam("id") String id) throws Exception {
//        // 参数判断; 查看企业;
//
//        // 参数判断;
//        if (StrUtil.isBlank(id)) {
//            return RestResponse.failure("参数异常");
//        }
//
//        // 查看企业
//        BaseEnterpriseEntity baseEnterpriseEntity = baseEnterpriseService.getBaseEnterprise(id);
//
//        return new RestResponse().setSuccess(true).setMessage("查看成功").setData(baseEnterpriseEntity);
//    }

    /**
     * 查询机构（分页）
     *
     * @param request
     * @return
     * @throws Exception
     */
    @GetMapping("/list")
    public RestResponse list(HttpServletRequest request) throws Exception {
        Page<BaseEnterpriseEntity> page = new Page<>();
        page.setCurrent(Long.parseLong(request.getParameter("current")));
        page.setSize(Long.parseLong(request.getParameter("size")));

        Map<String, Object> map = Maps.newHashMap();
        map.put("name", request.getParameter("name"));

        IPage<BaseEnterpriseEntity> iPage = enterpriseService.selectByIPage(page, map);

        return RestResponse.success("查询成功").setData(iPage);
    }
}
