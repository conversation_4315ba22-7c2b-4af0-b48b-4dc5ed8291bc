package cn.uone.business.cont.service;

import cn.uone.application.enumerate.contract.AnnexTypeEnum;
import cn.uone.application.enumerate.contract.TempCodeEnum;
import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import cn.uone.bean.entity.business.cont.ContFrameContractEntity;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.cont.ContTempParamEntity;
import cn.uone.bean.entity.business.cont.ContTempRichEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itextpdf.text.DocumentException;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
public interface IContTempService extends IService<ContTempEntity> {

    Page<ContTempEntity> page(Page page, ContTempEntity e);

    /**
     * 根据房源获取合同模板
     *
     * @return
     */
    boolean saveOrUpdate(ContTempEntity e, ContTempRichEntity rich, List<ContTempParamEntity> plist,List<String> costIds) throws BusinessException, IOException, DocumentException, Exception;

    ContTempRichEntity getRichByTempId(String tempId);

    List<String> getCostIdsByTempId(String tempId);

    String getTempById(String tempId,String costId) throws BusinessException;

    String getTempAnnexById(String sourceId, AnnexTypeEnum annexEnum);

    List<ContTempParamEntity> getParamByTempId(String tempId);

    ContTempParamEntity getParamByTempIdAndCode(String tempId, TempCodeEnum codeEnum);

    boolean delete(String id);
    /**
     * 根据房源获取合同模板
     *
     * @param operate
     * @return
     */
    ContTempEntity matcherContractTemplet(ResSourceEntity source, RenterEntity renter, String operate);

    /**
     * 根据项目id，合同模板类型，客户类型获取合同模板
     *
     * @return
     */
    List<ContTempEntity> queryByParam(ContTempEntity searchVo);

    /**
     * 根据房源获取合同模板(个人)
     *
     * @return
     */
    List<ContTempEntity> getTempleteBySource(ResSourceEntity source);

    /**
     * 根据房源获取退房合同模板(个人)
     *
     * @return
     */
    ContTempEntity getCheckOutTempletBySource(ResSourceEntity source);

    /**
     * 是否人才
     *
     * @param source
     * @param renter
     * @return
     */
    boolean isTalent(ResSourceEntity source, RenterEntity renter);

    /**
     * 生成退房确认书
     *
     * @param id
     */
    void generateCheckOutPdf(String id) throws Exception;

    /**
     * 获取退房确认书的内容
     *
     * @param id
     */
    String generateCheckOutContent(String id) throws Exception;

    String buildHtml(ContTempEntity template,List<ContTempParamEntity> tempPrams,ContTempRichEntity rich,boolean view) throws BusinessException;


    String getTempHtml(String contractId, ContTempEntity e,Map<TempCodeEnum,String> map, ContTempRichEntity rich,String costId) throws BusinessException;

    String getFrameTempHtml(BaseEnterpriseEntity enterprise, ContTempEntity e, Map<TempCodeEnum, String> map, ContTempRichEntity rich) throws BusinessException;
    String getFrameTempHtml2(BaseEnterpriseEntity enterprise, ContTempEntity e, Map<TempCodeEnum, String> map, ContTempRichEntity rich) throws Exception;
    /**
     * 生成签约合同pdf
     * @param contractId
     * @return
     * @throws Exception
     */
    SysFileEntity createSignPdf(String contractId) throws Exception;
    SysFileEntity createSignPdf(String contractId,String contractName) throws Exception;

    SysFileEntity createFramePdf(ContFrameContractEntity contractEntity, BaseEnterpriseEntity enterprise) throws Exception;


    Boolean isSyncCCB(String tempId);

    void audit(String guId,String auditResult) throws Exception;

    boolean createFrameTemp(String projectId) throws Exception;

    SysFileEntity createCheckoutPdf(String contractId) throws Exception;

    /**
     * 生成交付确认pdf
     * @param contractId
     * @return
     * @throws Exception
     */
    SysFileEntity deliveryAffirmPdf(String contractId,String contractName)throws Exception;

    /**
     * 生成授权书pdf：公积金抵扣授权书
     * @param contractId
     * @param contractName
     * @param subjectType
     * @param fileType
     * @return
     * @throws Exception
     */
    SysFileEntity pfpoaPdf(String fromId,String contractId,String contractName,String subjectType,String fileType,String projectId) throws Exception;

    String remindPdf(String orderId, BigDecimal unPaidRent, BigDecimal weiyujin) throws Exception;

}
