package cn.uone.business.kingdee.dao;

import cn.uone.bean.entity.business.kingdee.KingdeeReceiptItemEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptItemVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Repository
public interface KingdeeReceiptItemDao extends BaseMapper<KingdeeReceiptItemEntity> {
    IPage<KingdeeReceiptItemVo> getVoListByReceiptId(Page page, @Param("receiptId") String receiptId);
    List<KingdeeReceiptItemEntity> getListByReceiptId(@Param("receiptId") String receiptId);
    List<KingdeeReceiptItemEntity> getListForCreateByNumber(@Param("number") String number);
    List<KingdeeReceiptItemEntity> getListForCreateByOrderId(@Param("orderId") String orderId);
    IPage<KingdeeReceiptItemVo> getVoList(Page page, @Param("map") Map<String, Object> map);
    List<KingdeeReceiptItemVo> getVoList(@Param("map") Map<String, Object> map);
}
