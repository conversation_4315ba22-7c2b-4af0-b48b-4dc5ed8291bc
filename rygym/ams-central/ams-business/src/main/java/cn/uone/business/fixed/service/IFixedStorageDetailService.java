package cn.uone.business.fixed.service;

import cn.uone.bean.entity.business.fixed.FixedPropertyEntity;
import cn.uone.bean.entity.business.fixed.FixedStorageDetailEntity;
import cn.uone.bean.entity.business.fixed.vo.StorageStatisticsVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 固定资产入库详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
public interface IFixedStorageDetailService extends IService<FixedStorageDetailEntity> {

    IPage<FixedPropertyEntity> getListByPage(Page page, FixedStorageDetailEntity entity);

    int checkByCode(String id,String code);

    int updateByCode(String state,String id,String code,String storageBy);

    StorageStatisticsVo getQuantity(String storageId);

    List<FixedPropertyEntity> getPropertyById(String storageId);
}
