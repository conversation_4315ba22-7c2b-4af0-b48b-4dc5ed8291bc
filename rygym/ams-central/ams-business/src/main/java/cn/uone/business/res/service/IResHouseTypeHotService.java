package cn.uone.business.res.service;

import cn.uone.bean.entity.business.res.ResCollectEntity;
import cn.uone.bean.entity.business.res.ResHouseTypeEntity;
import cn.uone.bean.entity.business.res.ResHouseTypeHotEntity;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeHotVo;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeSearchVo;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IResHouseTypeHotService extends IService<ResHouseTypeHotEntity> {

    IPage<ResHouseTypeHotVo> hotTypePage(Page page,Map<String,Object> map);

    List<ResHouseTypeHotVo> hotTypes(Map<String,Object> map);

    List<ResHouseTypeHotVo> collectShow(ResCollectEntity searchVo);
}
