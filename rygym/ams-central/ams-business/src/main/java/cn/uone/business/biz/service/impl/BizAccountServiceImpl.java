package cn.uone.business.biz.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.base.BaseBankEntity;
import cn.uone.bean.entity.business.biz.BizAccountEntity;
import cn.uone.bean.entity.business.invoice.vo.InvoiceBuyerVo;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.base.service.IBaseBankService;
import cn.uone.business.biz.dao.BizAccountDao;
import cn.uone.business.biz.service.IBizAccountService;
import cn.uone.business.res.dao.ResProjectParaDao;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.impl.ResProjectParaServiceImpl;
import cn.uone.business.sys.service.ISysBranchCodeService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.PdfUtil;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-04
 */
@Service
public class BizAccountServiceImpl extends ServiceImpl<BizAccountDao, BizAccountEntity> implements IBizAccountService {

    @Resource
    private IRenterFegin iRenterFegin;

    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private PdfUtil pdfUtil;

    @Override
    @Transactional
    public void addOrUpdateAccount(BizAccountEntity account,MultipartFile file) throws Exception {
        boolean createPdf = true;
        String renterId = UoneSysUser.id();
        if(StrUtil.isBlank(renterId)){
            throw new BusinessException("获取不到租客信息");
        }
        account.setRenterId(renterId);
        RenterEntity renter = iRenterFegin.getById(renterId);
        //String branchId = account.getBranchId();
        //SysBranchCodeEntity branch = sysBranchCodeService.getById(branchId);
        //account.setBankBranch(branch.getFullName());
        if(StrUtil.isBlank(account.getId())){
            //新增的时候, 同名不需要生成pdf
            if(account.getName().equalsIgnoreCase(renter.getName())){
                createPdf = false;
            }
        }else{
            //修改时候
            if(isAccountInfoChange(account)){
                //账号修改过
                //删除旧的pdf
                sysFileService.delFileByFromId(account.getId());
                //判断是否得重新生成
                if(account.getName().equalsIgnoreCase(renter.getName())){
                    createPdf = false;
                }
            }else{
                createPdf = false;
            }
        }

        account.setIsDel(BaseConstants.BOOLEAN_OF_FALSE);
        if(createPdf){
            if(ObjectUtil.isNull(file)){
                throw new BusinessException("开户名跟账号姓名不一致，租客签名不能为空");
            }
        }
        this.saveOrUpdate(account);
        if(createPdf){
            SysFileEntity signFile = sysFileService.saveImg(file, account.getId(), SysFileTypeEnum.ACCOUNT_GRANT_SIGN.getValue(), "sign_name");
            String pdfName = "租客退款授权书.pdf"; // PDF名称
            String html = "<p style=\"text-align: center;\"><span style=\"font-size: 20px;\"><strong><span style=\"font-family: 宋体, SimSun;\">租客退款授权书</span></strong></span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">本人授权以下账户为指定收款账号:&nbsp;</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">开户名:{account.name!}</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">开户行:{account.bankName!}-{account.bankBranch!}</span></p><p><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">账号:{account.code!}</span></p><p style=\"text-align: right;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">授权人:!signName!</span></p><p style=\"text-align: right;\"><span style=\"font-family: 宋体, SimSun; font-size: 14px;\">{signDate!}</span></p>";
            html = html.replace("!signName!", "<img src=\"" + signFile.getPath() + "\" style=\"width:200px;\" />");
            HashMap<String, Object> map = new HashMap<>();
            //String bankId = account.getBranchId();
            //SysBranchCodeEntity branchEntity = sysBranchCodeService.getById(bankId);
            account.setBankName(account.getBankBranch());
            map.put("account",account);
            String date = DateUtil.format(new Date(), "yyyy-MM-dd");
            map.put("signDate",date);
            String url = pdfUtil.pdf(html, map, pdfName);
            SysFileEntity grantPdf = new SysFileEntity();

            grantPdf.setUrl(url);
            grantPdf.setName("租客退款授权书");
            grantPdf.setType(SysFileTypeEnum.ACCOUNT_GRANT.getValue());
            grantPdf.setFromId(account.getId());
            sysFileService.save(grantPdf);
        }
    }

    private boolean isAccountInfoChange(BizAccountEntity newAccount){
        String id = newAccount.getId();
        BizAccountEntity oldAccount = this.getById(id);
        if(newAccount.getName().equalsIgnoreCase(oldAccount.getName())
                && newAccount.getCode().equals(oldAccount.getCode())
                && newAccount.getBranchId().equals(oldAccount.getBranchId())
                && newAccount.getProvinceId().equals(oldAccount.getProvinceId())){
            return true;
        }
        return false;
    }

    @Override
    public List<BizAccountEntity> queryList(Map<String, Object> map) {
        return baseMapper.queryList(map);
    }

    @Override
    public BizAccountEntity detail(String id){
        BizAccountEntity account = getById(id);
        /*SysBranchCodeEntity branch = sysBranchCodeService.getById(account.getBranchId());
        if(ObjectUtil.isNotNull(branch)){
            account.setBankBranch(branch.getFullName());
        }
        BaseBankEntity bank = baseBankService.getById(account.getBankId());
        if(ObjectUtil.isNotNull(bank)){
            account.setBankName(bank.getName());
        }*/
        List<SysFileEntity> files = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.ACCOUNT_GRANT);
        if(!CollectionUtils.isEmpty(files)){
            account.setPdf(files.get(0).getPath());
        }
        return account;
    }

    @Override
    public void delete(String id) {
        BizAccountEntity account = this.getById(id);
        account.setIsDel(BaseConstants.BOOLEAN_OF_TRUE);
        this.saveOrUpdate(account);
    }

    @Override
    public BizAccountEntity getByRenterId(String renterId) {
        QueryWrapper query = new QueryWrapper();
        query.eq("renter_id",renterId);
        return baseMapper.selectOne(query);
    }

    @Override
    public boolean addBankCard(BaseBankEntity baseBankEntity, String bankCard,String name,String accountId) {
        BizAccountEntity bae = new BizAccountEntity();
        if(accountId !=null){
            bae = baseMapper.selectById(accountId);
        }
        BizAccountEntity ba = new BizAccountEntity();
        String renterId = UoneSysUser.id();
        if (bae ==null){
            ba.setName(name);
            ba.setRenterId(renterId);
            ba.setBankBranch(baseBankEntity.getName());
            ba.setBankId(baseBankEntity.getId());
            ba.setBankCode(baseBankEntity.getCode());
            ba.setCode(bankCard);
            return ba.insert();
        }else{
            bae.setBankId(baseBankEntity.getId());
            bae.setBankBranch(baseBankEntity.getName());
            bae.setBankCode(baseBankEntity.getCode());
            bae.setCode(bankCard);
            return bae.insertOrUpdate();
        }

    }

    @Override
    public List<BizAccountEntity> getBankCard(String userName) {
        String renterId = UoneSysUser.id();
        Map<String, Object> map = new HashMap<>();
        map.put("isDel",BaseConstants.BOOLEAN_OF_FALSE);
        map.put("renterId",renterId);
        map.put("userName",userName);
        return baseMapper.getBankCard(map);
    }

    /**
     * 根据购方信息同步银行账号信息
     * @param invoiceBuyerVo
     * @throws Exception
     */
    @Override
    public void updateAccountByBuyer(InvoiceBuyerVo invoiceBuyerVo) throws Exception {
        RenterEntity renter = iRenterFegin.getByTel(invoiceBuyerVo.getBuyerTelephone());
        if(renter == null){
            return;
        }
        boolean isUpdate = false;
        if(StrUtil.isBlank(renter.getName())){
            renter.setName(invoiceBuyerVo.getBuyerName());
            isUpdate = true;
        }
        if(StrUtil.isBlank(renter.getIdNo())){
            renter.setIdNo(invoiceBuyerVo.getBuyerTaxNo());
            isUpdate = true;
        }
        if(StrUtil.isBlank(renter.getAddress())){
            renter.setAddress(invoiceBuyerVo.getBuyerAddress());
            isUpdate = true;
        }
        if(isUpdate){
            iRenterFegin.update(renter);
        }
        BizAccountEntity bizAccount = this.getByRenterId(renter.getId());
        if(bizAccount == null){
            bizAccount = new BizAccountEntity();
        }
        bizAccount.setRenterId(renter.getId());
        bizAccount.setName(invoiceBuyerVo.getBuyerName());
        bizAccount.setCode(invoiceBuyerVo.getBuyerBankNumber());
        bizAccount.setBankBranch(invoiceBuyerVo.getBuyerBankName());
        bizAccount.setBankCode(invoiceBuyerVo.getBuyerBankNumber());
        this.saveOrUpdate(bizAccount);
    }


}
