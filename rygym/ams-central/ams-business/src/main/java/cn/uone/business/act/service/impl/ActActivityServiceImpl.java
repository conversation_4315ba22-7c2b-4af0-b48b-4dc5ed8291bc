package cn.uone.business.act.service.impl;

import cn.uone.bean.entity.business.act.ActActivityEntity;
import cn.uone.bean.entity.business.act.ActSearchVo;
import cn.uone.bean.entity.business.act.ActVo;
import cn.uone.business.act.dao.ActActivityDao;
import cn.uone.business.act.service.IActActivityService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 活动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@Service
public class ActActivityServiceImpl extends ServiceImpl<ActActivityDao, ActActivityEntity> implements IActActivityService {
    @Override
    public IPage<ActVo> listPage(Page page, ActSearchVo vo) {
        return baseMapper.listPage(page,vo);
    }
}
