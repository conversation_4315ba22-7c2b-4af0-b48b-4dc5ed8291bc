package cn.uone.business.demo.service.impl;

import cn.uone.business.demo.dao.DemoBillPeriodDao;
import cn.uone.business.demo.dao.DemoContractDao;
import org.apache.commons.lang.time.DurationFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;


public class ToolBoxService{
    @Autowired
    DemoContractDao demoContractDao;
    @Autowired
    DemoBillPeriodDao billPeriodDao;

    /*
       生成order_code或contract_code
     */
    public String generateCode(String maxCode,String initial){
         String now=getNowString();  //将日期转化为指定格式
         System.out.println(maxCode);
         int a;
         if(maxCode==null){
             a=1;
         }else{a= Integer.parseInt(maxCode.substring(13))+1;}  //新数据的code是在上条数据上+1，所以找出最大值，string转成int类型后得到最后几位数字+1
         String num=String.valueOf(a);
         StringBuffer sb=new StringBuffer();
         for(int i = 0; i < 5-num.length(); i++) {
             sb.append("0");
         }
         num=sb.toString()+num;
         System.out.println(num);
         String code=initial+now+num; //拼接成最终需要的contract_code
         System.out.println(code);
         return code;
     }

     /*
        将当前时间值转化成String类型
      */
     public String getNowString(){
         Date date=new Date(); //获得当前日期
         SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");  //指定日期格式
         String now=formatter.format(date);  //将日期转化为指定格式
         return now;
     }

    /*
       获取每笔账单的开始时间,以合同日期为基点,加上每一期账单周期月数
     */
    public Date getStartDate(Date startTime,int months){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar startDate = Calendar.getInstance();
        startDate.setTime(startTime);
        startDate.add(Calendar.MONTH, months);
        Date start = startDate.getTime();
        return start;
    }

    /*
       获取每笔账单的结束时间
     */
    public Date getEndDate(Date startTime,int months){
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar startDate = Calendar.getInstance();
        startDate.setTime(startTime);
        startDate.add(Calendar.DAY_OF_MONTH,-1); //在原日期的月份上加一,得到的结束日期会跟开始时间一样,所以要减一.比如5月11日,月分加一,变成6月11日,但实际一个月的周期结束日期应该是是6月10日.
        startDate.add(Calendar.MONTH, months);
        Date end = startDate.getTime();
        return end;
    }

    /*
    获取两个日期之间的月份差
     */
    public int getMonths(Date startDate, Date endDate){
//        Calendar calendarS = Calendar.getInstance();
//        Calendar calendarE = Calendar.getInstance();
//        calendarS.setTime(startDate);
//        calendarE.setTime(endDate);
//        int start=calendarS.get(Calendar.MONTH)+1 ;
//        int end=calendarE.get(Calendar.MONTH)+1;
//        int months=(end-start)+1;
        String month= DurationFormatUtils.formatPeriod(new Date(String.valueOf(startDate)).getTime(), new Date(String.valueOf(endDate)).getTime(), "M");
        int months=Integer.parseInt(month)+1;
        System.out.println(months);
        return months;
    }
}
