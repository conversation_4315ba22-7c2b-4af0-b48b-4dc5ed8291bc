package cn.uone.business.dev.dao;

import cn.uone.bean.entity.business.dev.DevDeviceEntity;
import cn.uone.bean.entity.business.dev.vo.DevDeviceEntitySerchVo;
import cn.uone.bean.entity.business.dev.vo.DevDeviceEntityVo;
import cn.uone.bean.entity.business.res.vo.ResSourceDeviceInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface DevDeviceDao extends BaseMapper<DevDeviceEntity> {

    List<DevDeviceEntityVo> queryDevListBySourceId(String sourceId);

    List<DevDeviceEntityVo> queryDevByNameAndSourid(@Param("map")Map map);

    List<DevDeviceEntityVo> getAiDevices(@Param("map") Map map);

    @Select("select sd.device_id,s.source_name,ds.name as supplier,sd.source_id sourceId from " +
            "t_res_source_device_rel sd " +
            " join t_cont_contract_source_rel cs on sd.source_id=cs.source_id" +
            " join (select t.id,t.renter_id,t.contract_source_id,t.is_checkin,t.name from t_cont_check_in_user t where t.is_checkin = '1' group by t.renter_id,t.contract_source_id) cu on cu.contract_source_id=cs.id" +
            " join v_res_source s on cs.source_id = s.id" +
            " join t_dev_device dd on sd.device_id=dd.id" +
            " join t_dev_supplier ds on dd.supplier_id=ds.id" +
            " join t_cont_contract cc on cc.id=cs.contract_id" +
            " where cu.renter_id=#{renterId} and dd.type_id=#{typeId} and cc.state in('6','8','14','13')  and cu.is_checkin='1' GROUP BY sd.id ORDER BY s.source_name")
    List<ResSourceDeviceInfo> getId(@Param("renterId") String renterId, @Param("typeId")String typeId);

    /**
     * 仅仅用于云丁水电表 通过code查询对应的
     * @param code
     * @return
     */
    @Select("select * from t_dev_device t where t.suplier_id = '1c6cdbfc4fe547738d6282dcc53c8cbe' and t.class_id = '27f26ef0b5d8768ce4b662e434a7a993' and t.code =#{code}")
    DevDeviceEntity getByCode(@Param("code") String code );

    Map<String, Object> countDevsByCityCode(@Param("cityCode") String cityCode);

    IPage<DevDeviceEntityVo> getDevicesByParams(Page page, @Param("map") DevDeviceEntitySerchVo serchVo);

    List<DevDeviceEntityVo> getDevicesByParams( @Param("map") DevDeviceEntitySerchVo serchVo);
}
