package cn.uone.business.bil.dao;

import cn.uone.bean.entity.business.bil.PriceStrategyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
public interface PriceStrategyDao extends BaseMapper<PriceStrategyEntity> {

    IPage<PriceStrategyEntity> findByCondition(Page page, @Param("map")Map<String, Object> map);

    List<PriceStrategyEntity> getList(@Param("map")Map<String, Object> map);

    IPage<Map<String, Object>> selectPageByAudit( Page page,@Param("map")Map<String, Object> map);
}
