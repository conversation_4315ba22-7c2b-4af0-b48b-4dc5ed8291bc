package cn.uone.business.task.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.task.TaskExecutorEntity;
import cn.uone.business.task.service.ITaskExecutorService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 执行者管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-25
 */
@RestController
@RequestMapping("/task/executor")
public class TaskExecutorController extends BaseController {

    @Autowired
    private ITaskExecutorService service;

    @GetMapping("/page")
    public RestResponse page(Page<TaskExecutorEntity> page, TaskExecutorEntity entity){
        QueryWrapper<TaskExecutorEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("create_date");
        if(StrUtil.isNotBlank(entity.getRealName())){
            wrapper.like("real_name","%"+entity.getRealName()+"%");
        }
        if(StrUtil.isNotBlank(entity.getPhone())){
            wrapper.like("phone","%"+entity.getPhone()+"%");
        }
        if(StrUtil.isNotBlank(entity.getUserType())){
            wrapper.like("user_type","%"+entity.getUserType()+"%");
        }
        if(StrUtil.isNotBlank(entity.getWorkingState())){
            wrapper.like("working_state","%"+entity.getWorkingState()+"%");
        }
        IPage<TaskExecutorEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/save")
    public RestResponse save(TaskExecutorEntity entity){
        entity.insertOrUpdate();
        return RestResponse.success();
    }



    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }

}
