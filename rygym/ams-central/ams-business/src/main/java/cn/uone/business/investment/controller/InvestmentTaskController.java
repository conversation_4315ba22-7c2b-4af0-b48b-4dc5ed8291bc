package cn.uone.business.investment.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.investment.InvestmentTaskEntity;
import cn.uone.bean.entity.business.investment.vo.InvestmentTaskVo;
import cn.uone.business.investment.service.IInvestmentTaskService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.util.FileUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 招商任务 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@RestController
@RequestMapping("/investment/investment-task-entity")
public class InvestmentTaskController extends BaseController {
    @Resource
    private IInvestmentTaskService iInvestmentTaskService;


    /**
     * 根据各种参数获取招商任务列表
     * @param investmentTaskVo 招商任务实体类
     * @return 招商任务列表 分页
     * author caizhanghe 2023-07-26
     */
    @GetMapping("/getPageByParams")
    public RestResponse getPageByParams(Page page, InvestmentTaskVo investmentTaskVo) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("taskCode",investmentTaskVo.getTaskCode());
        map.put("taskName",investmentTaskVo.getTaskName());
        map.put("taskType",investmentTaskVo.getTaskType());
        map.put("taskSource",investmentTaskVo.getTaskSource());
        map.put("taskState",investmentTaskVo.getTaskState());
        map.put("taskPriority",investmentTaskVo.getTaskPriority());
        //map.put("purchaseDateStart",purchaseDateStart);
        //map.put("purchaseDateEnd",purchaseDateEnd);
        IPage<InvestmentTaskVo> iPage = iInvestmentTaskService.getPageByParams(page,map);
        return RestResponse.success().setData(iPage);
    }


    /**
     * 获取信息
     *
     * @param id 主键
     * @return 招商任务列表
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        InvestmentTaskEntity info = iInvestmentTaskService.getById(id);
        info.setAttachmentPath(FileUtil.getPath(info.getTaskAttachment()));
        return RestResponse.success().setData(info);
    }

    /**
     * 新增和修改
     *
     * @param investmentTask 参数
     * @return 招商任务列表
     */
    @RequestMapping("/addOrUpdate")
    public RestResponse addOrUpdate(@ModelAttribute InvestmentTaskEntity investmentTask,
                             @RequestParam(required = false) List<MultipartFile> annexFiles) {
        /*if(ObjectUtil.isEmpty(investmentTask.getPropertySta())){
            investmentTask.setPropertySta("正常");
        }*/
        if(iInvestmentTaskService.addOrUpdate(investmentTask,annexFiles)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 修改
     *
     * @param investmentTask 参数
     * @return 招商任务列表
     */
    @PostMapping("/edit")
    public RestResponse edit(InvestmentTaskEntity investmentTask) {
        if(iInvestmentTaskService.updateById(investmentTask)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return 招商任务列表
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        if(iInvestmentTaskService.removeByIds(ids)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 获取招商任务列表
     * @param taskName 招商任务名称
     * @return 招商任务列表 不分页
     * author caizhanghe 2023-08-02
     */
    @RequestMapping("/getTaskList")
    public RestResponse getTaskList(String taskName) {
        Map<String,Object> m = Maps.newHashMap();
        m.put("taskName",taskName);
        //map.put("purchaseDateStart",purchaseDateStart);
        //map.put("purchaseDateEnd",purchaseDateEnd);
        List<InvestmentTaskVo> taskList = iInvestmentTaskService.getTaskList(m);
        List<Map<String,String>> data = new ArrayList<>();
        for(InvestmentTaskVo taskVo:taskList){
            Map<String,String> map = new HashMap<>();
            map.put("name",taskVo.getTaskName());
            map.put("value",taskVo.getId());
            data.add(map);
        }
        return RestResponse.success().setData(data);
    }

}
