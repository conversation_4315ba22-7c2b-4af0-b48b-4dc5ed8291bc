package cn.uone.business.investment.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.investment.InvestmentNewsEntity;
import cn.uone.bean.entity.business.investment.InvestmentReleaseEntity;
import cn.uone.business.investment.service.IInvestmentNewsService;
import cn.uone.business.investment.service.IInvestmentReleaseService;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 新闻资讯 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/investment/news")
public class InvestmentNewsController extends BaseController {

    @Autowired
    private IInvestmentNewsService service;

    @GetMapping("/page")
    public RestResponse page(Page<InvestmentNewsEntity> page, InvestmentNewsEntity entity){
        QueryWrapper<InvestmentNewsEntity> wrapper = new QueryWrapper<>();

//        wrapper.eq("project_id",UoneHeaderUtil.getProjectId());
        if(StrUtil.isNotBlank(entity.getTitle())){
            wrapper.like("title","%"+entity.getTitle()+"%");
        }
        if(StrUtil.isNotBlank(entity.getStatus())){
            wrapper.eq("status",entity.getStatus());
        }
        wrapper.orderByDesc("create_date");
        IPage<InvestmentNewsEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/save")
    public RestResponse save(InvestmentNewsEntity entity){
        entity.insertOrUpdate();
        return RestResponse.success();
    }


    @PostMapping("/edit")
    public RestResponse edit(InvestmentNewsEntity entity){
        entity.updateById();
        return RestResponse.success();
    }

    @PostMapping("/publish")
    public RestResponse publish(InvestmentNewsEntity entity){
        entity.setReleaseTime(new Date());
        entity.setStatus("2");
        entity.updateById();
        return RestResponse.success();
    }

    /*取消发布*/
    @PostMapping("/cancel")
    public RestResponse cancel(InvestmentNewsEntity entity){
        entity.setStatus("1");
        entity.updateById();
        return RestResponse.success();
    }

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }
}
