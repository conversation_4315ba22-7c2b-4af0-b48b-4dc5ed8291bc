package cn.uone.business.common.controller;

import cn.uone.bean.entity.business.bil.EquipmentWarnEntity;
import cn.uone.bean.entity.business.dev.DevDeviceEntity;
import cn.uone.business.bil.service.IEquipmentWarnService;
import cn.uone.business.dev.service.IDevDeviceService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/common/yunding")
public class YunDingCotroller extends BaseController {

    @Autowired
    private IEquipmentWarnService equipmentWarnService ;

    @Autowired
    private IDevDeviceService devDeviceService ;

    @RequestMapping(value = "/getNotice" , method = {RequestMethod.GET,RequestMethod.POST})
    @UonePermissions
    public void getNotice(HttpServletRequest request , HttpServletResponse response){
        response.setContentType("text/html;charset=utf-8");

        String event = request.getParameter("event");
        String timestamp = request.getParameter("time");
        String uuid = request.getParameter("uuid");
        String homeId = request.getParameter("home_id");
        String sign = request.getParameter("sign");
        System.out.println(event);
        System.out.println(timestamp);
        System.out.println(uuid);
        System.out.println(homeId);
        System.out.println(timestamp);
        DevDeviceEntity devDeviceEntity = devDeviceService.getByCode(uuid);
        //  如果设备不存在 则直接退出
        if(null == devDeviceEntity){
            RestResponse.success().setData("暂无设备");
        }else{
            EquipmentWarnEntity equipmentWarnEntity = equipmentWarnService.getEntityByUUid(uuid);
            if(null==equipmentWarnEntity){
                equipmentWarnEntity = new EquipmentWarnEntity();
            }
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateString = formatter.format(timestamp);
            Date occurTime = new Date();
            try{
                occurTime = formatter.parse(dateString);
            }catch (Exception e){
                e.printStackTrace();
            }
            String classType = devDeviceEntity.getClassId();
            String type ="";
            if("6feedee3ae91d6335854565d4c2a869e".equals(classType)){ //智能门锁
                type = "1";
            }else if("b7623119ceab40e61cd69dbb9e4ee1e8".equals(classType)){ // 智能水表
                type = "3";
            }else if("c76d9768ab2f11333f06b8d0e1606f01".equals(classType)){ // 智能电表
                type = "2";
            }
            equipmentWarnEntity.setDeviceNo(uuid);
            equipmentWarnEntity.setType(type);
            equipmentWarnEntity.setTypes("1");
            // 根据事件类型做相应操作
            if("lockOfflineAlarm".equals(event)){ // 事件 离线
                equipmentWarnEntity.setState("1");
                equipmentWarnEntity.setStatus("1");
                equipmentWarnEntity.setOccurTime(occurTime);
            }else if("batteryAlarm".equals(event)){ // 低电量 事件
                equipmentWarnEntity.setState("1");
                equipmentWarnEntity.setStatus("2");
                equipmentWarnEntity.setOccurTime(occurTime);
            }else if("clearLockOfflineAlarm".equals(event)){  // 离线解除 事件
                equipmentWarnEntity.setState("2");
            }else if("clearBatteryAlarm".equals(event)){ // 低电量解除
                equipmentWarnEntity.setState("2");
            }
            equipmentWarnEntity.insertOrUpdate();
            RestResponse.success().setData("感谢通知");
        }
    }
}
