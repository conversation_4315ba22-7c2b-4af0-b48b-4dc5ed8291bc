package cn.uone.business.util.prop;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * @ClassName projectSignatureProp
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/23 18:45
 * @Version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "projectsignature")
public class projectSignatureProp {
    public static List<Map<String, String>> list;   //static 才能拿配置值

    public static List<Map<String, String>> getList() {
        return list;
    }

    public void setList(List<Map<String, String>> list) {
        projectSignatureProp.list = list;
    }
}
