package cn.uone.business.pbls.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.pbls.RenovationApplyEntity;
import cn.uone.bean.entity.business.pbls.vo.RenovationApplyVo;
import cn.uone.bean.entity.business.pbls.vo.RenovationSearchVo;
import cn.uone.business.pbls.dao.RenovationApplyDao;
import cn.uone.business.pbls.service.IRenovationApplyService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
@Service
public class RenovationApplyServiceImpl extends ServiceImpl<RenovationApplyDao, RenovationApplyEntity> implements IRenovationApplyService {

    @Autowired
    private ISysFileService fileService;

    @Override
    public IPage<RenovationApplyVo> findByCondition(Page page, RenovationSearchVo renovationSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        DataScope dataScope = getDataScope(renovationSearchVo);
        map.put("searchVo", renovationSearchVo);
        return baseMapper.selectRenovationApplyByMap(page, map, dataScope);
    }

    @Override
    public boolean saveOrUpdate(RenovationApplyEntity entity, List<MultipartFile> image) {
        this.saveOrUpdate(entity);
        if(image != null &&  image.size() > 0){
            fileService.delFileByFromIdAndType(entity.getId(), SysFileTypeEnum.FINISH_PICTURE);
            fileService.saveFiles(image, entity.getId(), SysFileTypeEnum.FINISH_PICTURE.getValue());
        }
        return false;
    }

    /**
     * XX公寓 有用户id 不过滤数据权限
     *
     * @param renovationSearchVo
     * @return
     */
    private DataScope getDataScope(RenovationSearchVo renovationSearchVo) {
        DataScope dataScope = null;
        if (StrUtil.isEmpty(renovationSearchVo.getUserId())) {
            dataScope = new DataScope(UoneSysUser.id());
            dataScope.setProAlias("s");
            dataScope.setProjectFieldName("project_id");
        }
        return dataScope;
    }


}
