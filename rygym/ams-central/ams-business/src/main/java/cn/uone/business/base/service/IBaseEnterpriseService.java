package cn.uone.business.base.service;

import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * 机构
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IBaseEnterpriseService extends IService<BaseEnterpriseEntity> {

    /**
     * 新增机构
     *
     * @param enterpriseEntity
     * @return
     * @throws Exception
     */
    int addEnterprise(BaseEnterpriseEntity enterpriseEntity) throws Exception;


    /**
     * 新增机构
     *
     * @param enterprise
     * @return
     * @throws Exception
     */
    BaseEnterpriseEntity saveOrUpdateEnterprise(BaseEnterpriseEntity enterprise) throws Exception;


    int updateEnterprise(BaseEnterpriseEntity enterpriseEntity) throws Exception;

    /**
     * 查询机构
     *
     * @param map
     * @return
     * @throws Exception
     */
    List<BaseEnterpriseEntity> selectByList(Map<String, Object> map) throws Exception;

    /**
     * 查询机构（分页）
     *
     * @param page
     * @param map
     * @return
     * @throws Exception
     */
    IPage<BaseEnterpriseEntity> selectByIPage(Page<BaseEnterpriseEntity> page, Map<String, Object> map) throws Exception;

    BaseEnterpriseEntity getByRenterId(String id);

    List<Map<String, Object>> selectCompanys();
}
