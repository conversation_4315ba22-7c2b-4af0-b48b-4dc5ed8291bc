package cn.uone.business.bpm.service.impl;

import cn.uone.bean.entity.business.bpm.BpmWorkflowEntity;
import cn.uone.bean.entity.business.bpm.vo.BpmWorkflowSearchVo;
import cn.uone.bean.entity.business.bpm.vo.BpmWorkflowVo;
import cn.uone.business.bpm.dao.BpmWorkflowDao;
import cn.uone.business.bpm.service.IBpmWorkflowService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
@Service
@Slf4j
public class BpmWorkflowServiceImpl extends ServiceImpl<BpmWorkflowDao, BpmWorkflowEntity> implements IBpmWorkflowService {

    @Override
    public IPage<BpmWorkflowVo> findByCondition(Page page, BpmWorkflowSearchVo bpmWorkflowSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        //bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        //DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bpmWorkflowSearchVo);
        return baseMapper.selectBpmWorkflowByMap(page, map);
    }

    @Override
    public List<BpmWorkflowEntity> getListByRelId(String relId) {
        QueryWrapper query = new QueryWrapper();
        query.eq("rel_id",relId);
        return baseMapper.selectList(query);
    }

    public BpmWorkflowEntity getByRelId(String relId) {
        QueryWrapper query = new QueryWrapper();
        query.eq("rel_id",relId);
        query.orderByDesc("create_date");
        return baseMapper.selectOne(query);
    }

    @Override
    public BpmWorkflowEntity getByGuid(String guid) {
        QueryWrapper query = new QueryWrapper();
        query.eq("guid",guid);
        return baseMapper.selectOne(query);
    }
}
