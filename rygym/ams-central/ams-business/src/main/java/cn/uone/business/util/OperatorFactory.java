package cn.uone.business.util;

import cn.uone.business.apro.service.Operation;
import cn.uone.business.apro.service.impl.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
public class OperatorFactory {
    static Map<String, Operation> operationMap = new HashMap<>();

    public OperatorFactory(Reimbursemettion reimbursemettion, DesignContracttion d, ContractPaytion c, ProcurementBudgettion p, PurPaytion pp, OrderConfirmPaytion op, OrderRefundtion or, Coupontion cc, ProcurementPaytion pp2, Patroltion patroltion, ReturnDeposittion rd, ChangeRoomtion cr, ChangeRentertion crt, OrderCanceltion ocl,SourcePricetion sp,FixLastOrdertion flo) {
        operationMap.put("1", reimbursemettion);
        operationMap.put("2", d);
        operationMap.put("3", c);
        operationMap.put("4", p);
        operationMap.put("5",pp);
        operationMap.put("6", op);
        operationMap.put("7", or);
        operationMap.put("8",cc);
        operationMap.put("9",pp2);
        operationMap.put("11",patroltion);
        operationMap.put("12", rd);
        operationMap.put("13", cr);
        operationMap.put("14", crt);
        operationMap.put("15", ocl);
        operationMap.put("16",sp);
        operationMap.put("17",flo);
    }

    public static Optional<Operation> getOperation(String operator) {
        return Optional.ofNullable(operationMap.get(operator));

    }
}
