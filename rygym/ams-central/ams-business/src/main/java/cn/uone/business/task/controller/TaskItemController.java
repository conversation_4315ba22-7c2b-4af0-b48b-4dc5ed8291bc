package cn.uone.business.task.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.task.TaskItemEntity;
import cn.uone.bean.entity.business.task.TaskOptionTypeEntity;
import cn.uone.business.task.service.ITaskItemService;
import cn.uone.business.task.service.ITaskOptionTypeService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 任务项管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@RestController
@RequestMapping("/task/item")
public class TaskItemController extends BaseController {

    @Autowired
    private ITaskItemService service;
    @Autowired
    private ITaskOptionTypeService taskOptionTypeService;

    @GetMapping("/page")
    public RestResponse page(Page<TaskItemEntity> page, TaskItemEntity entity){
        QueryWrapper<TaskItemEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("task_type","create_date");
        if(StrUtil.isNotBlank(entity.getTaskName())){
            wrapper.like("task_name","%"+entity.getTaskName()+"%");
        }
        if(StrUtil.isNotBlank(entity.getTaskType())){
            wrapper.like("task_type","%"+entity.getTaskType()+"%");
        }
        IPage<TaskItemEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/save")
    public RestResponse save(TaskItemEntity entity){
        entity.insertOrUpdate();
        return RestResponse.success();
    }



    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }

    //获取选项
    @RequestMapping("/optionType")
    public RestResponse optionType(String optionType) {
        RestResponse response=new RestResponse();
        QueryWrapper<TaskOptionTypeEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("option_type",optionType);
        List<TaskOptionTypeEntity> taskOptionTypeEntities=taskOptionTypeService.list(wrapper);
        List<Map<String,String>> data=new ArrayList<>();
        for(TaskOptionTypeEntity taskOptionTypeEntity:taskOptionTypeEntities){
            Map<String,String> map=new HashMap<>();
            map.put("name",taskOptionTypeEntity.getTypeName());
            map.put("value",taskOptionTypeEntity.getId());
            data.add(map);
        }
        return response.setSuccess(true).setData(data);
    }

}
