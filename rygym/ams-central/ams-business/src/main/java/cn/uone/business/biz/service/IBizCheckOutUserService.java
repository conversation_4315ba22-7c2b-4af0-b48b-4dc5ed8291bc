package cn.uone.business.biz.service;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.biz.BizCheckOutUserEntity;
import cn.uone.bean.entity.business.biz.vo.BizCheckOutUserVo;
import cn.uone.bean.parameter.CheckOutUserPo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-20
 */
public interface IBizCheckOutUserService extends IService<BizCheckOutUserEntity> {

    IPage<BizCheckOutUserVo> selectPage(Page page, CheckOutUserPo checkOut);

    void cancelMoveaway(String id) throws Exception;

    void sureMoveaway(String id) throws Exception;

    Map<String,Object> getLifeOrderByMoveaway(String id) throws Exception;

    void sureMoveaway(String id, BilOrderEntity fixOrder, BilOrderEntity energyOrder) throws Exception;
}
