package cn.uone.business.tpi.controller;


import cn.hutool.core.lang.Console;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.enumerate.ApiTypeEnum;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.order.PayWayEnum;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.face.FaceLockEntity;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.bean.entity.tpi.icbcPay.IcbcPayConfigVo;
import cn.uone.bean.entity.tpi.icbcPay.IcbcPayVo;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.face.service.IFaceLockService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.fegin.crm.IExpenseConfigFegin;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysCompanyFegin;
import cn.uone.fegin.tpi.IIcbcPayFegin;
import cn.uone.fegin.tpi.ITrudianFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icbc.api.utils.IcbcEncrypt;
import com.icbc.api.utils.IcbcSignature;
import com.icbc.api.utils.WebUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 契约锁接口控制类
 */
@RestController
@RequestMapping("/tpi/trudian")
public class TrudianController extends BaseController {

    @Autowired
    ITrudianFegin trudianFegin;
    @Autowired
    ISysCompanyFegin sysCompanyFegin;
    @Autowired
    IResProjectService resProjectService;
    @Autowired
    IFaceLockService faceLockService;

    @RequestMapping("/qrcode")
    @UonePermissions
    public RestResponse qrcode(String expenseProjectId){
        List<String> localIdList = new ArrayList<>();
        List<SysCompanyEntity> companyList = sysCompanyFegin.getByTopId(expenseProjectId);
        for(SysCompanyEntity company : companyList){
            QueryWrapper queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("company_id", company.getId());
            List<ResProjectEntity> projectList = resProjectService.list(queryWrapper);
            for(ResProjectEntity project : projectList){
                QueryWrapper queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.eq("project_id", project.getId());
                List<FaceLockEntity> faceLockList = faceLockService.list(queryWrapper1);
                for(FaceLockEntity faceLock : faceLockList){
                    localIdList.add(faceLock.getLocalId());
                }
            }
        }
        return trudianFegin.qrcode(expenseProjectId,StrUtil.join(",", localIdList));
    }



}
