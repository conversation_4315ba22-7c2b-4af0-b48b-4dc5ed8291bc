package cn.uone.business.common.service.impl;

import cn.uone.application.constant.TransferConfig;
import cn.uone.application.vo.MiniUnionPayItemVo;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.business.bil.service.IBilInterfaceMsgService;
import cn.uone.business.common.controller.CommonUnionPayController;
import cn.uone.business.common.service.IUnionMiniPayService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.util.unionPay.UnionPayUtil;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.SafeCompute;
import com.google.common.collect.Lists;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Created by xmlin on 2019-01-12.
 */
@Service
public class UnionMiniPayServiceImpl implements IUnionMiniPayService {

    private static final Logger log = LoggerFactory.getLogger(CommonUnionPayController.class);

    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IBilInterfaceMsgService bilInterfaceMsgService;

    /*
     * 微信：
     *    测试环境：http://***********:29015/v1/netpay/wx/unified-order
     *    生产环境：https://api-mop.chinaums.com/v1/netpay/wx/unified-order
     * 支付宝：
     *   测试环境：http://***********:29015/v1/netpay/trade/create
     *   生产环境：https://api-mop.chinaums.com/v1/netpay/trade/create
     *
     *  appid   10037e6f6823b20801682b6a5e5a0006
     *  appkey  1c4e3b16066244ae9b236a09e5b312e8
     *  mid：898201612345678
     *  tid：88880001
     * */
    static String authorization;

    @Override
    @Transactional
    public RestResponse miniUnionPay(BilOrderEntity order, SysCompanyEntity companyEntity, String openid, boolean isCz) throws BusinessException{
        RestResponse response = new RestResponse();
        JSONObject json = getMiniUnionJson(order,companyEntity,openid,isCz);
        String resp = null;
        try {
            String url = TransferConfig.wxUrl;
            resp = send(url, json.toString());
            System.out.println("返回结果:\n" + resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject respJson = JSONObject.fromObject(resp);
        if (respJson.containsKey("targetStatus")) {
            String status = respJson.getString("targetStatus");
            bilInterfaceMsgService.addInterfaceMsg(json.toString(), respJson.toString(), null, order.getCode(), "发起银联分账支付");
            if ("SUCCESS|SUCCESS".equals(status)) {
                String miniPayRequest = respJson.getString("miniPayRequest");
                response.setData(miniPayRequest);
                response.setSuccess(true);
                response.code(200);
                return response;
            } else {
                return response.setMessage("查询银联分账支付结果状态:" + status).setSuccess(false);
            }
        } else {
            log.info("银联返回消息：" + respJson);
            return response.setMessage("银联返回消息：" + respJson.getString("errMsg")).setSuccess(false);
        }
    }


    public List<MiniUnionPayItemVo> getSubOrders(String subMit, BigDecimal subPayment) {
        List<MiniUnionPayItemVo> unionpayItemVos = Lists.newArrayList();
        MiniUnionPayItemVo subVo = new MiniUnionPayItemVo();
        subVo.setMid(subMit);
        subVo.setTotalAmount(getAmount(subPayment));
        subVo.setMerOrderId("31WG"+ UnionPayUtil.genMerOrderId(TransferConfig.msgId));
        unionpayItemVos.add(subVo);
        return unionpayItemVos;
    }

    //BigDecimal转Unionpay string
    private String getAmount(BigDecimal payment) {
        return SafeCompute.multiply(payment, new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).toString();
    }

    private JSONObject getMiniUnionJson(BilOrderEntity order, SysCompanyEntity companyEntity, String openid, boolean isCz) {
        BigDecimal payment = order.getPayment();
        BigDecimal splitProportion = companyEntity.getSplitProportion();
        BigDecimal subPayment = payment.multiply(splitProportion).setScale(2,BigDecimal.ROUND_HALF_UP);
        String subMit = companyEntity.getMerchantId();
        List<MiniUnionPayItemVo> subOrders = getSubOrders(subMit,subPayment);
        String totalAmount = getAmount(payment);
        String platformAmount = getAmount(payment.subtract(subPayment));
        ResSourceVo vo = resSourceService.getInfoById(order.getSourceId());
        String orderDesc = vo.getHouseName() + "-" + order.getCode();
        /* post参数,格式:JSON */
        JSONObject json = new JSONObject();
        //json.put("msgId", "001");   // 消息Id,原样返回
        json.put("requestTimestamp", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));    // 报文请求时间
        json.put("merOrderId", "31WG"+order.getCode()); // 商户订单号
        //json.put("srcReserve", "请求系统预留字段"); // 请求系统预留字段
        json.put("mid", companyEntity.getBankCode()); // 商户号
        json.put("tid", companyEntity.getCode());    // 终端号
        //json.put("instMid", "MINIDEFAULT"); // 业务类型
        // 准备商品信息
        /*List<Goods> goodsList = new ArrayList<Goods>();
        goodsList.add(new Goods("0001", "充值0.01元", 1L, 100L, "Auto", "充值0.01元"));
        goodsList.add(new Goods("0002", "Goods Name", 2L, 200L, "Auto", "goods body"));
        json.put("goodsList", goodsList);*/
        json.put("attachedData",order.getCode()); //商户附加数据
        //json.put("expireTime", DateFormatUtils.format(new Date().getTime() + 60 * 60 * 1000, "yyyy-MM-dd HH:mm:ss"));  // 订单过期时间,这里设置为一个小时
        //json.put("goodsTag", "商品标记");   // 商品标记
        json.put("goodsTradeNo", getMerchantOrderId());    // 商品交易单号，跟goods字段二选一
        json.put("orderDesc",orderDesc);  // 账单描述
        //json.put("originalAmount", 10);     // 订单原始金额
        //json.put("productId", "001");   // 商品ID
        json.put("totalAmount", totalAmount);      // 支付总金额
        /* 分账部分 */
        json.put("divisionFlag", true); // 分账标记
        //json.put("asynDivisionFlag", false); // 异步分账标记
        json.put("platformAmount", platformAmount); // 平台商户分账金额
        /* subOrders,子订单信息 */
        //ArrayList<JSON> subOrders = new ArrayList<>();
        //JSONObject jsonObject = new JSONObject();
        //jsonObject.put("mid", "988460101800204");
        //jsonObject.put("totalAmount", 1);
        //subOrders.add(jsonObject);
        json.put("subOrders", subOrders);
        String notifyUrl = TransferConfig.wxNotifyUrl;
        if(isCz){
            notifyUrl = TransferConfig.wxNotifyUrlCz;
        }
        json.put("notifyUrl", notifyUrl);  // 支付结果通知地址
        //json.put("returnUrl", wxNetpayProp.getReturnUrl());  // 网页跳转地址
        json.put("showUrl", TransferConfig.returnUrl);    // 订单展示页面
        //json.put("secureTransaction", "false"); // 担保交易标识
        json.put("subAppId", TransferConfig.WXAPPID);   // 微信子商户appId
        json.put("subOpenId", openid);   // 用户子标识，微信必传
        //json.put("userId", "111");  // 用户子标识，支付宝必传
        json.put("tradeType", "MINI");  // 交易类型
        //json.put("limitCreditCard", "false");   // 是否需要限制信用卡支付
        return json;
    }

    /**
     * 发送请求
     *
     * @param url    eg:http://***********:29015/v1/netpay/trade/create
     * @return
     * @throws Exception
     */
    public static String send(String url, String entity) throws Exception {
        authorization = getOpenBodySig(TransferConfig.APPID, TransferConfig.APPKEY, entity);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Authorization", authorization);
        StringEntity se = new StringEntity(entity, "UTF-8");
        se.setContentType("application/json");
        httpPost.setEntity(se);
        CloseableHttpResponse response = httpClient.execute(httpPost);
        HttpEntity entity1 = response.getEntity();
        String resStr = null;
        if (entity1 != null) {
            resStr = EntityUtils.toString(entity1, "UTF-8");
        }
        httpClient.close();
        response.close();
        return resStr;
    }

    /**
     * open-body-sig方式获取到Authorization 的值
     *
     * @param appId  f0ec96ad2c3848b5b810e7aadf369e2f
     * @param appKey 775481e2556e4564985f5439a5e6a277
     * @param body   json字符串 String body = "{\"merchantCode\":\"123456789900081\",\"terminalCode\":\"00810001\",\"merchantOrderId\":\"20123333644493200\",\"transactionAmount\":\"1\",\"merchantRemark\":\"测试\",\"payMode\":\"CODE_SCAN\",\"payCode\":\"285668667587422761\",\"transactionCurrencyCode\":\"156\"}";
     * @return
     * @throws Exception
     */
    public static String getOpenBodySig(String appId, String appKey, String body) throws Exception {
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());   // eg:20190227113148
        String nonce = UUID.randomUUID().toString().replace("-", ""); // eg:be46cd581c9f46ecbd71b9858311ea12
        String localSignatureStr = null;   // Signature
        InputStream is = null;
        try {
            byte[] data = body.getBytes("UTF-8");
            System.out.println("data:\n" + body);
            is = new ByteArrayInputStream(data);
            String bodyDigest = testSHA256(is); // eg:d60bc3aedeb853e2a11c0c096baaf19954dd9b752e48dea8e919e5fb29a42a8d
            System.out.println("bodyDigest:\n" + bodyDigest);
            String str1_C = appId + timestamp + nonce + bodyDigest; // eg:f0ec96ad2c3848b5b810e7aadf369e2f + 20190227113148 + be46cd581c9f46ecbd71b9858311ea12 + d60bc3aedeb853e2a11c0c096baaf19954dd9b752e48dea8e919e5fb29a42a8d

            System.out.println("str1_C:" + str1_C);

            //System.out.println("appKey_D:\n" + appKey);

            byte[] localSignature = hmacSHA256(str1_C.getBytes(), appKey.getBytes());

            localSignatureStr = Base64.encodeBase64String(localSignature);
            System.out.println("Authorization:\n" + "OPEN-BODY-SIG AppId=" + "\"" + appId + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + localSignatureStr + "\"\n");
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if(is != null){
                is.close();
            }
        }
        return ("OPEN-BODY-SIG AppId=" + "\"" + appId + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + localSignatureStr + "\"");
    }

    /**
     * 进行加密
     *
     * @param is
     * @return 加密后的结果
     */
    private static String testSHA256(InputStream is) {
        try {
            //System.out.println(is.hashCode());
            return DigestUtils.sha256Hex(is);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if(is != null){
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    /**
     * @param data
     * @param key
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static byte[] hmacSHA256(byte[] data, byte[] key) throws NoSuchAlgorithmException, InvalidKeyException {
        String algorithm = "HmacSHA256";
        Mac mac = Mac.getInstance(algorithm);
        mac.init(new SecretKeySpec(key, algorithm));
        return mac.doFinal(data);
    }

    /**
     * 获取到订单号
     *
     * @return 订单号
     */
    public static String getMerchantOrderId() {
        return DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
    }


}
