package cn.uone.business.bil.dao;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface BilRefundConfirmDao extends BaseMapper<BilOrderEntity> {

    IPage<BilOrderVo> selectBilOrderByMap(Page page, @Param("map") Map<String, Object> map, DataScope dataScope);

}
