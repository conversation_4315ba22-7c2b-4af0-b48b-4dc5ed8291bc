package cn.uone.business.kingdee.service;

import cn.uone.bean.entity.business.kingdee.KingdeeReceiptItemEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptItemVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
public interface IKingdeeReceiptItemService extends IService<KingdeeReceiptItemEntity> {
    IPage<KingdeeReceiptItemVo> getVoListByReceiptId(Page page, String receiptId);
    IPage<KingdeeReceiptItemVo> getVoPage(Page page, Map<String, Object> map);
    List<KingdeeReceiptItemVo> getVoList(Map<String, Object> map);
}
