package cn.uone.business.kingdee.controller;


import cn.uone.business.kingdee.service.IKingdeeInvoiceItemService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@RestController
@RequestMapping("/kingdee-invoice-item-entity")
public class KingdeeInvoiceItemController extends BaseController {

    @Autowired
    private IKingdeeInvoiceItemService kingdeeInvoiceItemService ;

    /**
     * 列表
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, @RequestParam("invoiceId") String invoiceId) {
        RestResponse response = new RestResponse();
        QueryWrapper query = new QueryWrapper();
        query.eq("invoice_id",invoiceId);
        query.orderByAsc("create_date");
        return response.setSuccess(true).setData(kingdeeInvoiceItemService.page(page,query));
    }
}
