package cn.uone.business.kingdee.service.impl;

import cn.uone.bean.entity.business.kingdee.KingdeeReceiptItemEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptItemVo;
import cn.uone.business.kingdee.dao.KingdeeReceiptItemDao;
import cn.uone.business.kingdee.service.IKingdeeReceiptItemService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Service
public class KingdeeReceiptItemServiceImpl extends ServiceImpl<KingdeeReceiptItemDao, KingdeeReceiptItemEntity> implements IKingdeeReceiptItemService {

    @Override
    public IPage<KingdeeReceiptItemVo> getVoListByReceiptId(Page page, String receiptId) {
        return baseMapper.getVoListByReceiptId(page,receiptId);
    }

    @Override
    public IPage<KingdeeReceiptItemVo> getVoPage(Page page, Map<String, Object> map) {
        return baseMapper.getVoList(page,map);
    }

    @Override
    public List<KingdeeReceiptItemVo> getVoList(Map<String, Object> map) {
        return baseMapper.getVoList(map);
    }
}
