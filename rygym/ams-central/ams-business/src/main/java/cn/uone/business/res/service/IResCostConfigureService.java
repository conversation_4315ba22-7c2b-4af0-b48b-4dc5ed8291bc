package cn.uone.business.res.service;

import cn.uone.application.enumerate.source.PriceTypeEnum;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.res.ResCostConfigureEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResCostConfigureVo;
import cn.uone.bean.entity.business.res.vo.ResCostVo;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IResCostConfigureService extends IService<ResCostConfigureEntity> {

    ResCostConfigureEntity queryByProjectId(String projectId);

    ResCostConfigureEntity queryBaseByProjectId(String projectId);

    /**
     * 项目费用配置详细信息
     *
     * @param map
     * @return
     */
    List<ResCostConfigureVo> queryCostAll(Map<String, Object> map);

    List<ResCostConfigureVo> queryByCostConfigureId(String costConfigureId);

    IPage<ResCostConfigureEntity> queryIPage(Page page, QueryWrapper wrapper);


    List<ResCostVo> selectCostListByIdAndTypeAndSourceType(String id, PriceTypeEnum type, BigDecimal lim)throws BusinessException;

    ResCostVo selectCostByIdAndTypeAndSourceType(String id, PriceTypeEnum type) throws BusinessException;

    ResCostVo selectCostByIdAndTypeAndSourceType(String id, PriceTypeEnum type, BigDecimal lim) throws BusinessException;

    List<ResCostConfigureEntity> queryByTemplate(List<ContTempEntity> contractTempletList);

    List<ResCostConfigureEntity> queryByTemplateId(String templateId);

    String getShowCost(String id);

    BigDecimal getFee(String id,PriceTypeEnum type,ResSourceEntity source,BigDecimal num) throws Exception;

    List<String> getShowCost(String id, ResSourceEntity source) throws BusinessException;
}
