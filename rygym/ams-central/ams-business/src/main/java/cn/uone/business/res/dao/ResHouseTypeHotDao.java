package cn.uone.business.res.dao;

import cn.uone.bean.entity.business.res.ResCollectEntity;
import cn.uone.bean.entity.business.res.ResHouseTypeEntity;
import cn.uone.bean.entity.business.res.ResHouseTypeHotEntity;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeHotVo;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeSearchVo;
import cn.uone.bean.entity.business.res.vo.ResHouseTypeVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface ResHouseTypeHotDao extends BaseMapper<ResHouseTypeHotEntity> {

    IPage<ResHouseTypeHotVo> hotTypePage(Page page,@Param("map") Map<String,Object> map);
    List<ResHouseTypeHotVo> hotShowList(@Param("map") Map<String,Object> map);

    List<ResHouseTypeHotVo> collectShowTwo(@Param("searchVo") ResCollectEntity resCollectEntity);
}
