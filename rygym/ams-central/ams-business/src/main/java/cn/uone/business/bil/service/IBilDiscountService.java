package cn.uone.business.bil.service;

import cn.uone.bean.entity.business.bil.BilDiscountEntity;
import cn.uone.bean.entity.business.bil.vo.BilDiscountSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IBilDiscountService extends IService<BilDiscountEntity> {

    List<BilDiscountEntity> findByCondition(String isFinish, String keyWord,String approvalState,String projectId,String isUsed,String userId);

    IPage<BilDiscountEntity> findByCondition(Page page, String discountType, String key, String isFinish, String isUsed, String approvalState);

    IPage<BilDiscountEntity> findByConditionAndIds(Page page, String discountType, String key, String isFinish, String isUsed, String approvalState, Set<String> ids);

    String getMaxCode(String code);

    IPage<BilDiscountSearchVo> selectBilDiscountTotal(Page page, Map<String, Object> map);

    List<BilDiscountSearchVo> selectBilDiscountTotal(Map<String, Object> map);

    IPage<BilOrderVo> getOrderList(Page page, BilOrderSearchVo bilOrderSearchVo);
}
