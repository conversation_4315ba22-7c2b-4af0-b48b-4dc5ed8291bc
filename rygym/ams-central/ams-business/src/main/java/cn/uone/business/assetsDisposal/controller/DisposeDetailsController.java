package cn.uone.business.assetsDisposal.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.assetsDisposal.DisposalEntity;
import cn.uone.bean.entity.business.assetsDisposal.DisposeDetailsEntity;
import cn.uone.business.assetsDisposal.service.IDisposalService;
import cn.uone.business.assetsDisposal.service.IDisposeDetailsService;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import cn.uone.web.base.BaseController;

/**
 * <p>
 * 处置详情记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@RestController
@RequestMapping("/assetsDisposal/disposeDetails")
public class DisposeDetailsController extends BaseController {


    @Autowired
    private IDisposeDetailsService service;
    @Autowired
    private IDisposalService disposalService;

    @GetMapping("/page")
    public RestResponse page(Page<DisposeDetailsEntity> page, DisposeDetailsEntity entity){
        QueryWrapper<DisposeDetailsEntity> wrapper = new QueryWrapper<>();
        if(StrUtil.isNotBlank(entity.getDisposalId())){
            wrapper.eq("disposal_id",entity.getDisposalId());
        }
        if(StrUtil.isNotBlank(entity.getType())){
            wrapper.eq("type",entity.getType());
        }
        if(StrUtil.isNotBlank(entity.getPropertyInfo())){
            wrapper.like("property_info","%"+entity.getPropertyInfo()+"%");
        }
        wrapper.orderByDesc("create_date");
        IPage<DisposeDetailsEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/save")
    public RestResponse save(DisposeDetailsEntity entity){
        entity.insertOrUpdate();
        DisposalEntity disposalEntity = disposalService.getById(entity.getDisposalId());
        if("4".equals(entity.getType())){
            disposalEntity.setDisposalState("0");
        }else{
            disposalEntity.setDisposalState(entity.getType());
        }
        disposalEntity.updateById();
        return RestResponse.success();
    }
    @GetMapping("/getListByType")
    public RestResponse getListByType(Page<DisposeDetailsEntity> page, DisposeDetailsEntity entity){
        IPage<DisposeDetailsEntity> p = service.getByType(page,entity.getType());
        return RestResponse.success().setData(p);
    }

}
