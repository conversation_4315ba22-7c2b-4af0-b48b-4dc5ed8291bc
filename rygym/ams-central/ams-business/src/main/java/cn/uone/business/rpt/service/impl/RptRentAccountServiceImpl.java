package cn.uone.business.rpt.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.rpt.RptRentAccountEntity;
import cn.uone.bean.entity.job.vo.AsyncResultVo;
import cn.uone.bean.parameter.PutAccountPo;
import cn.uone.business.rpt.dao.RptContAccountDao;
import cn.uone.business.rpt.dao.RptRentAccountDao;
import cn.uone.business.rpt.service.IRptRentAccountService;
import cn.uone.business.rpt.task.RentAccountTask;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-28
 */
@Service
public class RptRentAccountServiceImpl extends ServiceImpl<RptRentAccountDao, RptRentAccountEntity> implements IRptRentAccountService {

    @Autowired
    private RentAccountTask task;

    @Resource
    private RptContAccountDao contAccountDao;


    @Override
    public IPage<RptRentAccountEntity> selectPages(Page page, PutAccountPo param) {
        return baseMapper.page(page, param);
    }

    @Override
    public List<RptRentAccountEntity> export(PutAccountPo param) {
        return baseMapper.page(param);
    }

    @Override
    public void generate(Date date) throws ExecutionException, InterruptedException, BusinessException {
        date = DateUtil.offsetDay(date,-1);//前一天
        Date startDate = DateUtil.beginOfMonth(date);
        String month = DateUtil.format(date, "yyyy-MM");
        List<ContContractEntity> contracts =  contAccountDao.selectCont(month);

        List<CompletableFuture<AsyncResultVo<RptRentAccountEntity>>> cfs = Lists.newArrayList();

        StringBuilder msg = new StringBuilder();
        for (ContContractEntity contract:contracts){
            List<ContContractSourceRelEntity> csList = contract.getContractSource();
            if (CollUtil.isNotEmpty(csList) && csList.size() > 0) {
                for (ContContractSourceRelEntity cs : csList) {
                    CompletableFuture<AsyncResultVo<RptRentAccountEntity>> result = task.getAccountByContract(contract,cs,startDate);
                    cfs.add(result);
                }
            }
        }

        CompletableFuture.allOf(cfs.toArray(new CompletableFuture[cfs.size()])).join();
        List<RptRentAccountEntity> entitys = Lists.newArrayList();
        for (CompletableFuture<AsyncResultVo<RptRentAccountEntity>> cf : cfs) {
            AsyncResultVo<RptRentAccountEntity> rpt = cf.get();
            if (StrUtil.isNotBlank(rpt.getMsg())) {
                msg.append(rpt.getMsg()).append("<br/>");
                continue;
            }
            if (ObjectUtil.isNotNull(rpt.getObj())) {
                RptRentAccountEntity entity = rpt.getObj();
                entitys.add(entity);
            }
        }

        makeData(month,entitys);

        if(StrUtil.isNotBlank(msg)){
            throw new BusinessException(msg.toString());
        }
    }

    @Transactional
    void makeData(String month, List<RptRentAccountEntity> entitys){
        //删除该月数据
        baseMapper.delByMonth(month);
        for (RptRentAccountEntity entity:entitys) {
            entity.insert();
        }
    }

}
