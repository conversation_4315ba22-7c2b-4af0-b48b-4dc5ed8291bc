package cn.uone.business.cont.dao;

import cn.uone.bean.entity.business.cont.ContAppointmentEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-27
 */
public interface ContAppointmentDao extends BaseMapper<ContAppointmentEntity> {

    IPage<ContAppointmentEntity> listPage(Page page, @Param("search")String search);
}
