package cn.uone.business.cont.dao;

import cn.uone.bean.entity.business.cont.ContContractInfoEntity;
import cn.uone.bean.entity.tpi.record.RecordPo;
import cn.uone.bean.parameter.RecordManagePo;
import cn.uone.bean.parameter.RecordManageVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface ContContractInfoDao extends BaseMapper<ContContractInfoEntity> {

    List<RecordPo> getRecordInfo(@Param("contractId") String contractId,@Param("contractSourceId") String contractSourceId);

    List<RecordPo> getRecordInfoBy2(@Param("contState") String contState, @Param("xu") String xu, @Param("startDate")Date startDate,@Param("projectId") String projectId);

    IPage<RecordManageVo> selectRecord(Page page, @Param("p") RecordManagePo p);

    List<RecordManageVo> selectRecord(@Param("p")RecordManagePo p);

    ContContractInfoEntity selectInfoByContractId(@Param("contractId") String contractId);

}
