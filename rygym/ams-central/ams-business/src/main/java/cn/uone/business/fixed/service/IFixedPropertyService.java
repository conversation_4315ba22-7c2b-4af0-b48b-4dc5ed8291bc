package cn.uone.business.fixed.service;

import cn.uone.bean.entity.business.fixed.FixedPropertyEntity;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 资产信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
public interface IFixedPropertyService extends IService<FixedPropertyEntity> {
    IPage<FixedPropertyEntity> page(Page page, FixedPropertyEntity entity);

    List<HashMap<String,String>> keywordList(Page page,String keyword,String flag);

    RestResponse complete(List<String> ids);

    IPage<FixedPropertyEntity> getPageByParams(Page page, Map<String, Object> map);

    List<FixedPropertyEntity> getPropertyList(Map<String, Object> map);

    FixedPropertyEntity getIdByRfidCode(String code);
}
