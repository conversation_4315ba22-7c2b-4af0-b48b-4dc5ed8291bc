package cn.uone.business.sys.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.sale.vo.BannerFileVo;
import cn.uone.bean.entity.business.sys.SysBannerEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.business.video.VideoManagementEntity;
import cn.uone.business.sys.service.ISysBannerService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/banner")
public class SysBannerController extends BaseController {

    @Autowired
    private ISysBannerService sysBannerService;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private MinioUtil minioUtil;

    @RequestMapping("/getPromotionalVideo")
    @UonePermissions
    public RestResponse getPromotionalVideo(String projectId) {
        SysBannerEntity entity  = sysBannerService.getPromotionalVideo(projectId);
        String videoPath ="";
        String url = "";
        if(entity!=null){
            List<SysFileEntity> file = sysFileService.getListByFromIdAndType(entity.getId(), SysFileTypeEnum.getEnumByValue("74"));
            videoPath = file.get(0).getPath();
            url = file.get(0).getUrl();
        }
        return RestResponse.success().setData(videoPath).setAny("url",url);
    }

    @RequestMapping("/imageToVideo")
    @UonePermissions
    public RestResponse imageToVideo(String id) {
            List<SysFileEntity> file = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.getEnumByValue("74"));
            String videoPath = file.get(0).getPath();
        return RestResponse.success().setData(videoPath);
    }

    @RequestMapping("/queryPage")
    @RequiresPermissions("bannerImg:show")
    public RestResponse queryPage(@RequestParam(value = "name", required = false) String name,
                                  Page page) {
        IPage<BannerFileVo> list = sysBannerService.queryPage(page, name);
        return RestResponse.success().setData(page);
    }

    @RequestMapping("/queryList")
    @UonePermissions
    public RestResponse queryList(@RequestParam(value = "name", required = false) String name) {
        List<BannerFileVo> list = sysBannerService.queryList(name);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/bannerListByApp")
    @UonePermissions
    public RestResponse bannerListByApp(String projectId) {
        List<BannerFileVo> list = sysBannerService.bannerListByApp(projectId);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/saveBanner")
    public RestResponse saveBanner(@RequestParam(value = "file",required = false) MultipartFile file,
                                   @RequestParam(value = "videoFile",required = false) MultipartFile videoFile,
                                   @ModelAttribute SysBannerEntity entity) {
        entity.setProjectId(UoneHeaderUtil.getProjectId());
        //如果是编辑热门项目，需要先判断改名字是否已经存在
        if (StrUtil.isNotEmpty(entity.getId())) {
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.ne("id", entity.getId());
            wrapper.eq("name", entity.getName());
            List<SysBannerEntity> list = sysBannerService.list(wrapper);
            if (list.size() > 0) {
                return RestResponse.failure("该项目已在列表页，可直接编辑");
            }
        }
        //修改banner，如果为热门项目也把热门项目对应的名字改了
        if (StrUtil.isNotEmpty(entity.getId())) {
            SysBannerEntity entity1 = sysBannerService.getById(entity.getId());
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("name", entity1.getName());
            List<SysBannerEntity> list = sysBannerService.list(wrapper);
            for (SysBannerEntity bannerEntity : list) {
                bannerEntity.setName(entity.getName());
                sysBannerService.saveOrUpdate(bannerEntity);
            }
        }
        entity.setCityCode("350200");
        sysBannerService.saveOrUpdate(entity);
        if(file!=null){
            if(StrUtil.isNotBlank(entity.getOldImageId())){
                sysFileService.delFileByFromIdAndType(entity.getId(), SysFileTypeEnum.BANNER_PIC);
            }
            //String url= FileUtil.save(file);
            String url = minioUtil.save(file);
            SysFileEntity fileEntity=new SysFileEntity();
            fileEntity.setUrl(url);
            fileEntity.setType(SysFileTypeEnum.BANNER_PIC.getValue());
            fileEntity.setFromId(entity.getId());
            sysFileService.save(fileEntity);
        }
        if(videoFile!=null){
            if(StrUtil.isNotBlank(entity.getOldVideoId())){
                sysFileService.delFileByFromIdAndType(entity.getId(), SysFileTypeEnum.BANNER_VIDEO);
            }
            //String url= FileUtil.save(videoFile);
            String url = minioUtil.save(videoFile);
            SysFileEntity fileEntity=new SysFileEntity();
            fileEntity.setUrl(url);
            fileEntity.setType(SysFileTypeEnum.BANNER_VIDEO.getValue());
            fileEntity.setFromId(entity.getId());
            sysFileService.save(fileEntity);
        }
        return RestResponse.success().setData(entity);
    }
//    @RequestMapping("/saveBanner")
//    public RestResponse saveBanner(SysBannerEntity entity) {
//        //如果是编辑热门项目，需要先判断改名字是否已经存在
//        if (StrUtil.isNotEmpty(entity.getId())) {
//            QueryWrapper wrapper = new QueryWrapper();
//            wrapper.ne("id", entity.getId());
//            wrapper.eq("name", entity.getName());
//            List<SysBannerEntity> list = sysBannerService.list(wrapper);
//            if (list.size() > 0) {
//                return RestResponse.failure("该项目已在列表页，可直接编辑");
//            }
//        }
//        //修改banner，如果为热门项目也把热门项目对应的名字改了
//        if (StrUtil.isNotEmpty(entity.getId())) {
//            SysBannerEntity entity1 = sysBannerService.getById(entity.getId());
//            QueryWrapper wrapper = new QueryWrapper();
//            wrapper.eq("name", entity1.getName());
//            List<SysBannerEntity> list = sysBannerService.list(wrapper);
//            for (SysBannerEntity bannerEntity : list) {
//                bannerEntity.setName(entity.getName());
//                sysBannerService.saveOrUpdate(bannerEntity);
//            }
//        }
//        entity.setCityCode("350200");
//        sysBannerService.saveOrUpdate(entity);
//        return RestResponse.success().setData(entity);
//    }

    @RequestMapping("/delBanner")
    public RestResponse delBanner(@RequestParam(value = "id", required = true) String id) {
        sysBannerService.removeById(id);
        return RestResponse.success();
    }

    @RequestMapping(value = "/cancelState", method = RequestMethod.POST)
    public RestResponse cancelState(@RequestParam(value = "id", required = true) String id) {
        SysBannerEntity entity = sysBannerService.getById(id);
        if (entity == null) {
            return RestResponse.failure("该banner不存在");
        }
        entity.setState("0");
        sysBannerService.saveOrUpdate(entity);
        return RestResponse.success();
    }

    @RequestMapping("/toState")
    public RestResponse toState(@RequestParam(value = "id", required = true) String id) {
        SysBannerEntity entity = sysBannerService.getById(id);
        if (entity == null) {
            return RestResponse.failure("该banner不存在");
        }
        entity.setState("1");
        sysBannerService.saveOrUpdate(entity);
        return RestResponse.success();
    }

    @RequestMapping("/queryExist")
    public RestResponse queryExist(@RequestParam(value = "name", required = true) String name,
                                   @RequestParam(value = "id", required = false) String id) {
        QueryWrapper wrapper = new QueryWrapper<>();
        wrapper.eq("name", name);
        if (StrUtil.isNotBlank(id)) {
            wrapper.ne("id", id);
        }
        SysBannerEntity entity = sysBannerService.getOne(wrapper);
        if (entity == null) {
            return RestResponse.success();
        } else {
            return RestResponse.failure("该banner已经存在");
        }
    }

    @RequestMapping("/queryUpdate")
    public RestResponse queryUpdate(@RequestParam(value = "id", required = true) String id) {
        SysBannerEntity entity = sysBannerService.getById(id);
        if (entity == null) {
            return RestResponse.failure("该banner不存在");
        }
        return RestResponse.success().setData(entity);
    }

}
