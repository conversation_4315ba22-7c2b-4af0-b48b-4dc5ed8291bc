package cn.uone.business.bil.service;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface IBilRefundConfirmService extends IService<BilOrderEntity> {

    IPage<BilOrderVo> findByCondition(Page page, BilOrderSearchVo bilOrderSearchVo);

}
