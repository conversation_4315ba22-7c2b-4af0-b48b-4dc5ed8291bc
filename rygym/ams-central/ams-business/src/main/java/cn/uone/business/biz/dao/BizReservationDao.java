package cn.uone.business.biz.dao;

import cn.uone.bean.entity.business.biz.BizReservationEntity;
import cn.uone.bean.entity.business.biz.vo.BizReservationEntityVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface BizReservationDao extends BaseMapper<BizReservationEntity> {

    /**
     * 预约入住获取列表
     *
     * @return
     * <AUTHOR>
     * @date 2019-01-02 15:57
     * @Param:
     */

    IPage<BizReservationEntityVo> selectPageByMap(Page page, DataScope scope, @Param("state") String state);
}
