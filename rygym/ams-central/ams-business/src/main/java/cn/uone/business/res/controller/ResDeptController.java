package cn.uone.business.res.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.res.ResDeptEntity;
import cn.uone.bean.entity.business.res.ResPostEntity;
import cn.uone.business.res.service.IResDeptService;
import cn.uone.business.res.service.IResPostService;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 部门表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-30
 */
@RestController
@RequestMapping("/res/dept")
public class ResDeptController extends BaseController {

    @Autowired
    private IResDeptService service;
    @Autowired
    private IUserFegin userFegin;

    @Value("${eas.url}")
    private String easUrl;

    @GetMapping("/page")
    public RestResponse page(Page<ResDeptEntity> page, ResDeptEntity entity){
        QueryWrapper<ResDeptEntity> wrapper = new QueryWrapper<>();

//        wrapper.eq("project_id",UoneHeaderUtil.getProjectId());
        if(StrUtil.isNotBlank(entity.getDeptName())){
            wrapper.like("dept_name","%"+entity.getDeptName()+"%");
        }
        if(StrUtil.isNotBlank(entity.getStatus())){
            wrapper.like("status","%"+entity.getStatus()+"%");
        }
        IPage<ResDeptEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

//    @GetMapping("/listBySourceId")
//    public RestResponse listBySourceId(String sourceId){
//        QueryWrapper<ResProjectCompanyEntity> wrapper = new QueryWrapper<>();
//        wrapper.exists("select 1 from t_res_source s where s.project_id = t_res_project_company.project_id and s.id = '"+sourceId+"'");
//        List<ResProjectCompanyEntity> list =  service.list(wrapper);
//        list.forEach(item->{
//            item.setValue(item.getId());
//        });
//        return RestResponse.success().setData(list);
//    }

    @PostMapping("/save")
    public RestResponse save(ResDeptEntity entity){
        entity.setStatus("0");
        entity.insertOrUpdate();
        return RestResponse.success();
    }
//
    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }

//    @RequestMapping("/getCompanyList")
//    public RestResponse getByProjectId() {
//        QueryWrapper query = new QueryWrapper<>();
//        //query.eq("project_id", UoneHeaderUtil.getProjectId());
//        return RestResponse.success().setData(service.list(query));
//    }
//
//    /*
//       下拉菜单获取公司名（name）及id(value)
//     */
//    @RequestMapping("/selectCompany")
//    public RestResponse selectCompany() {
//        RestResponse response=new RestResponse();
//        List<ResProjectCompanyEntity> companyEntities=service.list();
//        List<Map<String,String>> companyList=new ArrayList<>();
//        for(ResProjectCompanyEntity companyEntity:companyEntities){
//            Map<String,String> map=new HashMap<>();
//            map.put("name",companyEntity.getName());
//            map.put("value",companyEntity.getId());
//            companyList.add(map);
//        }
//        return response.setSuccess(true).setData(companyList);
//    }
//
//    @RequestMapping("/getEasBank")
//    @UonePermissions
//    public RestResponse getEasBank(@RequestParam("city") String city,@RequestParam(value="bankName",required = false) String bankName) {
//        JSONArray arr = new JSONArray();
//        if(StrUtil.isBlank(city)){
//            return RestResponse.success().setData(arr);
//        }
//        Map<String,Object> paras = Maps.newHashMap();
//        paras.put("city",city);
//        paras.put("bankName",bankName);
//        String json = HttpUtil.post(easUrl+"/getEasBank",paras);
//        JSONObject obj = JSONUtil.parseObj(json);
//        arr = obj.getJSONArray("data");
//        return RestResponse.success().setData(arr);
//    }
//
//    @RequestMapping("/getCompanyBank")
//    @UonePermissions
//    public RestResponse getCompanyBank(@RequestParam("id") String id) {
//        JSONArray arr = new JSONArray();
//        if(StrUtil.isBlank(id)){
//            return RestResponse.success().setData(arr);
//        }
//        Map<String,Object> paras = Maps.newHashMap();
//        paras.put("company",id);
//        String json = HttpUtil.post(easUrl+"/getCompanyBank",paras);
//        JSONObject obj = JSONUtil.parseObj(json);
//        arr = obj.getJSONArray("data");
//        return RestResponse.success().setData(arr);
//    }

}
