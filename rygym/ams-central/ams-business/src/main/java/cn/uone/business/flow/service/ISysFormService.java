package cn.uone.business.flow.service;

import cn.uone.bean.entity.business.flow.SysFormEntity;
import cn.uone.bean.entity.business.flow.SysListenerEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 流程表单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
public interface ISysFormService extends IService<SysFormEntity> {
    IPage<SysFormEntity> page(Page page, SysFormEntity entity);

    List<SysFormEntity> list(SysFormEntity entity);

    Map<String,Object> formSet(Map<String,Object> map, Map<String,Object> mapPram);
}
