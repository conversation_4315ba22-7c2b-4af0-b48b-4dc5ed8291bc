package cn.uone.business.afforest.service.impl;


import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.afforest.VegetationEntity;
import cn.uone.business.afforest.dao.VegetationDao;
import cn.uone.business.afforest.service.IVegetationService;
import cn.uone.business.sys.service.ISysFileService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 植被管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Service
public class VegetationServiceImpl extends ServiceImpl<VegetationDao, VegetationEntity> implements IVegetationService {

    @Autowired
    private ISysFileService fileService;

    @Override
    public boolean saveOrUpdate(VegetationEntity entity, List<MultipartFile> files, List<MultipartFile> image) {
        this.saveOrUpdate(entity);
        if(files != null &&  files.size() > 0){
            fileService.delFileByFromIdAndType(entity.getId(), SysFileTypeEnum.VEGETATION_FILE);
            fileService.saveFiles(files, entity.getId(), SysFileTypeEnum.VEGETATION_FILE.getValue());

        }
        if(image != null &&  image.size() > 0){
            fileService.delFileByFromIdAndType(entity.getId(), SysFileTypeEnum.VEGETATION_PICTURE);
            fileService.saveFiles(image, entity.getId(), SysFileTypeEnum.VEGETATION_PICTURE.getValue());
        }
        return false;
    }
}
