package cn.uone.business.rpt.controller;


import cn.hutool.core.date.DateUtil;
import cn.uone.bean.parameter.PutAccountPo;
import cn.uone.business.rpt.service.IRptContAccountService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.ExecutionException;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-07
 */
@RestController
@RequestMapping("/report/contAccount")
public class RptContAccountController extends BaseController {

    @Autowired
    private IRptContAccountService contAccountService;


    @RequestMapping(value = "/page", method = RequestMethod.GET)
    public RestResponse page(Page page, PutAccountPo param) {
        return RestResponse.success().setData(contAccountService.selectPages(page, param));
    }


    @RequestMapping(value = "/generate", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse generate(String date) throws InterruptedException, ExecutionException, BusinessException {
        contAccountService.generate(DateUtil.endOfMonth(DateUtil.parse(date,"yyyy-MM")));
        return RestResponse.success();
    }

}
