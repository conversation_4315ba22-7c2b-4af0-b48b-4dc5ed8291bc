package cn.uone.business.bil.service;

import cn.uone.application.enumerate.DataFromEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.vo.BilCountVo;
import cn.uone.bean.entity.business.bil.vo.BilEmpOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.bean.entity.business.bil.vo.BilOverdueVo;
import cn.uone.bean.entity.business.bil.vo.DailyOrderVo;
import cn.uone.bean.entity.business.bil.vo.OrderConfirmPayVo;
import cn.uone.bean.entity.business.bil.vo.OrderImportVo;
import cn.uone.bean.entity.business.bil.vo.ParkOrderVo;
import cn.uone.bean.entity.business.biz.vo.SettleVo;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContFrameContractEntity;
import cn.uone.bean.entity.business.invoice.vo.InvoiceBuyerVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IBilOrderService extends IService<BilOrderEntity> {
    /**
     * 根据合同id和账单类型获取账单列表
     * @param contractId
     * @param orderType
     * @return
     */
    List<BilOrderVo> getOrderList(String contractId,String orderType);

    List<BilOrderVo> getOrderList(Map<String,Object> map);

    BilOrderVo getOrderById(String orderId);

    /**
     * 根据合同id获取押金账单
     * @param contractId
     * @return
     */
    BilOrderEntity getYajinOrder(String contractId);






   //====以前的接口================================================================================================
    List<BilOrderVo> findByCondition(BilOrderSearchVo bilOrderSearchVo);

    List<BilOrderVo> queryList(BilOrderSearchVo bilOrderSearchVo);

    Integer countByCondition(BilOrderSearchVo bilOrderSearchVo);

    IPage<BilOrderVo> findByCondition(Page page, BilOrderSearchVo bilOrderSearchVo);

    BigDecimal countPayment(BilOrderSearchVo bilOrderSearchVo);

    BigDecimal countEmpOrderByCondition(BilEmpOrderSearchVo bilEmpOrderSearchVo);

    IPage<BilOrderVo> findEmpOrderByCondition(Page page, BilEmpOrderSearchVo bilEmpOrderSearchVo);

    RestResponse batchUpdate(List<String> ids, String discard, String remark);

    List<HashMap> selectRefundInfoByOrderId(String id);

    RestResponse confirmPay(List<OrderConfirmPayVo> vos, String remark, Date payTime, String payWay, String tradeCode,String approvalId) throws Exception;

    RestResponse confirmRefund(String id, String remark, BigDecimal payment, Date refundTime);

    List<BilOrderEntity> getByIds(List<String> ids);

    BilOrderEntity getByCode(String code);

    BilOrderEntity getByCodeAndState(String code,String payState);

    BilOrderEntity getByCondition(BilOrderEntity condition);

    List<BilOrderEntity> getByContractId(String contractId);

    List<BilOrderVo> getBillAndItemByContractId(String contractId);

    List<BilOrderEntity> findOrdersByMergeCode(String mergeCode);

    List<BilOrderEntity> getOrderListByCode(String orderCode,String payState);

    /**
     * 保存账单
     *
     * @param orderTypeEnum
     * @param isFirst
     * @param payment
     * @param sourceId
     * @param contract
     * @param payerId
     * @return
     */
    BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                             ContContractEntity contract, String payerId, DataFromEnum dataFromEnum);

    /**
     * 保存账单
     *
     * @param orderTypeEnum
     * @param isFirst
     * @param payment
     * @param sourceId
     * @param contract
     * @param payerId
     * @return
     */
    BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                             ContContractEntity contract, String payerId, Date orderStartDate, DataFromEnum dataFromEnum);
    /**
     * 保存账单
     *
     * @param orderTypeEnum
     * @param isFirst
     * @param payment
     * @param sourceId
     * @param contract
     * @param payerId
     * @return
     */
    BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                             ContContractEntity contract, String payerId, String remark, DataFromEnum dataFromEnum);

    /**
     * 构建账单（仅用于生成合同pdf的费用清单）
     * @param orderTypeEnum
     * @param payment
     * @return
     */
    BilOrderEntity buildOrder(OrderTypeEnum orderTypeEnum, BigDecimal payment);

    BigDecimal addItem(List<BilOrderItemEntity> items, BilOrderItemEntity item);

    /**
     * 根据合同处理账单开票类型
     *
     * @param order
     * @param cont
     * @return
     */
    BilOrderEntity handleOrder(BilOrderEntity order, ContContractEntity cont);

    /**
     * 支付成功处理
     *
     * @param orderCode
     * @param sign
     * @param payway
     * @param params
     * @param notify
     * @throws Exception
     */
    void tradeSuccess(String orderCode, String sign, String payway, String params, String notify) throws Exception;

    /**
     * 获取付款方
     *
     * @param cont
     * @param cs
     * @param type 支付方类型（租金/生活费用）
     * @return
     */
    String getPayerId(ContContractEntity cont, ContContractSourceRelEntity cs, String type);

    /**
     * 账单通知
     *
     * @param payerId
     */
    void sendMessage(String payerId,String projectId);

    /**
     * 获取需要推送账单
     *
     * @return
     */
    List<BilOrderEntity> getNeedPushOrder();

    /**
     * 获取需要同步ccb账单
     *
     * @return
     */
    List<BilOrderEntity> getNeedSyncOrder();

    BilOrderEntity findByImportVo(String contractId, String orderType, String orderItemType, String startTime, String endTime);

    BilOrderEntity findByImportVo1(String id, String orderType, String changeOrderItemType, String startTime, String endTime);

    /**
     * 取消七天前的定金账单
     */
    void cancelDueOrder();

    /**
     * 获取已支付的押金,水电周转金
     */
    Map<String, Object> getDepositAndTurnover(String contractId, String sourceId,String releaseType,Date checkoutDate);

    /**
     * 获取合同下所有的已支付的押金,水电周转金
     */
    Map<String, Object> getDepositAndTurnover(String contractId);

    /**
     * 获取 已退款的 押金,水电周转金
     *
     * @param contractId
     * @return
     */
    Map<String, Object> getRefundDepositAndTurnover(String contractId, String sourceId);

    /**
     * 获取合同下已退款的押金,水电周转金
     */
    Map<String, Object> getRefundDepositAndTurnover(String sourceId);

    List<BilOrderEntity> getLastOrder(String contractId, String sourceId);

    String getReleaseOrderCode(String contractId, String sourceId);

    /**
     * 根据条件获取需要取消的账单
     *
     * @param map
     */
    List<BilOrderEntity> getCancelOrder(Map<String, Object> map);

    Map<String, Object> showLastOrder(String contractId, String sourceId) throws Exception;

    void cancelDone(String ids, String status, String memo) throws Exception;

    boolean lastOrderIsCreated(String contractId);

    String importRoomLivingFee(List<OrderImportVo> order, String importMonth) throws Exception;

    /**
     * 账单导出
     *
     * @param bilOrderSearchVo
     * @return
     */
    List<BilOrderVo> bilExport(BilOrderSearchVo bilOrderSearchVo);

    List<BilOrderVo> finaceExport(BilOrderSearchVo bilOrderSearchVo);

    /**
     * 根据合同id  contractId
     * 房源id sourceId
     * 判断时间 checkoutDate
     * 获取本期租金，下期租金
     *
     * 注意！！！  子账单中，只查询租金   不查询优惠金额等
     * @param map
     * @return
     */
    Map<String,BilOrderEntity> getCurrentAndNextRentOrder(Map<String, Object> map);

    BilOrderEntity getCurrentRentOrder(Map<String, Object> map);

    BilOrderEntity getCurrentLiveOrder(Map<String, Object> map);

    BilOrderEntity getCurrentLiveOrderPb(Map<String, Object> map);

    BilOrderEntity getCurrentSubsidyOrder(Map<String, Object> map);

    boolean orderIsAllPayed(String contractId,String sourceId);

    /**
     * 水电周转金退款
     * @param contractId
     * @param contractId
     */
    void releaseWaterAndEleTurnover(String contractId,String sourceId) throws Exception;

    Map<String, BilOrderEntity> getCurrentAndNextFixedOrder(Map<String, Object> map);

    /**
     * 修改尾期账单金额
     * @param ids
     */
    void fixOrderDone(String ids);

    /**
     * 判断 账单是否处于审批状态中
     * @param contractId
     * @param sourceId
     * @return
     */
    Map<String, String> isApproval(String contractId, String sourceId);

    void update1(String id);
    int setPartNull(String id);

    void handleByOrder(BilOrderEntity order) throws Exception;

    RestResponse confirm(String id, String companyId) throws Exception;

    List<BilOrderVo> getListByVo(BilOrderSearchVo bilOrderSearchVo);

    List<BilOrderVo> getPartListByVo(BilOrderSearchVo bilOrderSearchVo);

    BilOrderEntity addOrgOrder(BigDecimal total, ContFrameContractEntity frameContract, SettleVo vo);

    BilCountVo getStatistics(Map<String,String> map);

    BigDecimal getTotal(BilOrderSearchVo bilOrderSearchVo);

    BilCountVo getMonthData(Date date);

    BilCountVo getMonthData(Map<String, Object> maps);

    Map<String, Object> countUnPayNum(Map<String, Object> search);

    Map<String, Object> toIntention(Map<String, String> paras) throws Exception;

    SysFileEntity createIntentionPdf(BilOrderEntity bill) throws Exception;

    BilOrderEntity getCurrentReserve(Map<String,Object> maps );

    BilCountVo getReserveCount(Map<String, String> para);

    IPage<DailyOrderVo> findDailyByCondition(Page page, BilOrderSearchVo bilOrderSearchVo);

    IPage<DailyOrderVo> findsummaryByCondition(Page page, BilOrderSearchVo bilOrderSearchVo);

    List<BilOrderVo> getBillPdf(BilOrderSearchVo bilOrderSearchVo);

    List<DailyOrderVo> dailyExport(BilOrderSearchVo bilOrderSearchVo);

    List<BilOrderEntity> getOverdueOrder(String renterId);

    List<BilOrderEntity> getLastLifeFee();

    String importRoomLivingFeeN(List<OrderImportVo> order, String monthStart ,String monthEnd)  throws Exception;

    List<BilOverdueVo> getOverdueBil();

    List<BilOrderVo> hydropowerExport(BilOrderSearchVo bilOrderSearchVo);

    List<Map<String, Object>> countPayAndClear(String cityCode);

    Map<String, Object> getOverdueFee(String projectId);

    IPage<Map<String, Object>> selectPageByIntent(Page page, Map<String, Object> map);

    /**
     * 取消未推送的账单
     * @param contractId
     */
    void cancelUnpushOrdersByContractId(String contractId);

    List<Map<String,Object>> getLastPaidOrderList(String contractId,String checkoutDate);

    RestResponse previewInvoice(InvoiceBuyerVo invoiceBuyerVo);
    /**
     * 百旺开票
     * @param invoiceBuyerVo
     * @return
     */
    RestResponse baiwangInvoice(InvoiceBuyerVo invoiceBuyerVo);

    /**
     * 百旺红冲
     * @param invoiceIds
     * @param remark
     * @return
     */
    RestResponse baiwangRedInvoice(String invoiceIds,String remark);


     IPage<ParkOrderVo> getByPark(Page page,BilOrderSearchVo bilOrderSearchVo);


     String getPayStateByContractId(String contractId);


     List<ParkOrderVo> getByParkOrder(BilOrderSearchVo bilOrderSearchVo);

    /**
     * 获取待退款账单明细列表
     * @param map
     * @return
     */
    IPage<Map<String,Object>> getToRefundOrderList(Page page,Map<String, Object> map);

    List<Map<String,Object>> countRentOrders(String year);

    BilOrderEntity getOrderByMap(Map<String, Object> map);

    /**
     * 获取余额扣费水电账单
     * @param page
     * @param payerId
     * @return
     */
    IPage<Map<String,Object>> getSdOrderPage(Page page,String payerId);

    /**
     * 获取水电账单
     * @param contractId
     * @param orderType
     * @return
     */
    List<Map<String, Object>> getSdOrderList(String contractId,String orderType);
}
