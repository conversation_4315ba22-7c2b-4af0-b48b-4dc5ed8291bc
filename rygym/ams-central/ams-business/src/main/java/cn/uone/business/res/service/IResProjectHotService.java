package cn.uone.business.res.service;

import cn.uone.bean.entity.business.res.ResCollectEntity;
import cn.uone.bean.entity.business.res.ResProjectHotEntity;
import cn.uone.bean.entity.business.res.vo.ResProjectHotVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-16
 */
public interface IResProjectHotService extends IService<ResProjectHotEntity> {

    IPage<ResProjectHotVo> queryPage(String name, Page page);

    List<ResProjectHotVo> queryList();

    List<ResProjectHotVo> showList();

    List<ResProjectHotVo> collectShowOne(ResCollectEntity resCollectEntity);
}
