package cn.uone.business.kingdee.dao;

import cn.uone.bean.entity.business.kingdee.KingdeePaymentItemEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeePaymentItemVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Repository
public interface KingdeePaymentItemDao extends BaseMapper<KingdeePaymentItemEntity> {
    IPage<KingdeePaymentItemVo> getVoListByPaymentId(Page page, @Param("paymentId") String paymentId);
    List<KingdeePaymentItemEntity> getListByPaymentId(@Param("paymentId") String paymentId);
    IPage<KingdeePaymentItemVo> getVoList(Page page, @Param("map") Map<String, Object> map);
}
