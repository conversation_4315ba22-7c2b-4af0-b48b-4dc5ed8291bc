package cn.uone.business.supplies.service.impl;


import cn.uone.bean.entity.business.supplies.CollectApplyEntity;
import cn.uone.business.supplies.dao.CollectApplyDao;
import cn.uone.business.supplies.service.ICollectApplyService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-11
 */
@Service
public class CollectApplyServiceImpl extends ServiceImpl<CollectApplyDao, CollectApplyEntity> implements ICollectApplyService {

    @Autowired
    CollectApplyDao collectApplyDao;

    @Override
    public IPage<CollectApplyEntity> getListByPage(Page page, CollectApplyEntity entity) {
        return collectApplyDao.getListByPage(page,entity);
    }
}
