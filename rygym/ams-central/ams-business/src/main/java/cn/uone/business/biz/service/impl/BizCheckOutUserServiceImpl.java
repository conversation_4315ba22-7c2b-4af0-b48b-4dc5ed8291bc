package cn.uone.business.biz.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.contract.ContractStateEnum;
import cn.uone.application.enumerate.contract.InspectStateEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.biz.BizCheckOutUserEntity;
import cn.uone.bean.entity.business.biz.BizInspectEntity;
import cn.uone.bean.entity.business.biz.vo.BizCheckOutUserVo;
import cn.uone.bean.entity.business.cont.ContCheckInHouseEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.job.vo.AsyncResultVo;
import cn.uone.bean.parameter.CheckOutUserPo;
import cn.uone.business.bil.task.LiveOrderAutoTask;
import cn.uone.business.biz.dao.BizCheckOutUserDao;
import cn.uone.business.biz.service.IBizCheckOutUserService;
import cn.uone.business.biz.service.IBizInspectService;
import cn.uone.business.cont.service.*;
import cn.uone.business.dev.service.IDevDeviceService;
import cn.uone.business.res.service.IResCostConfigureService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.res.service.impl.ResProjectServiceImpl;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-20
 */
@Service
public class BizCheckOutUserServiceImpl extends ServiceImpl<BizCheckOutUserDao, BizCheckOutUserEntity> implements IBizCheckOutUserService {

    @Autowired
    @Lazy
    private IContCheckInHouseService checkInHouseService;
    @Autowired
    private IContCheckInUserService contCheckInUserService;
    @Autowired
    private IBizInspectService inspectService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IContContractSourceRelService contContractSourceRelService;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    private LiveOrderAutoTask orderAutoTask;
    @Autowired
    private IResCostConfigureService configureService;
    @Autowired
    private IResProjectParaService projectParaService;
    @Autowired
    private IDevDeviceService devDeviceService;
    @Autowired
    private IResProjectService resProjectService;


    @Override
    public IPage<BizCheckOutUserVo> selectPage(Page page, CheckOutUserPo checkOut) {
        DataScope scope = DataScope.newDataScope(UoneSysUser.id());
        scope.setProAlias("s");
        scope.setProjectFieldName("project_id");
        return baseMapper.queryList(page, scope, checkOut);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelMoveaway(String id) throws Exception {
        BizCheckOutUserEntity checkOutUser = this.getById(id);
        String contractSourceId = checkOutUser.getContractSourceId();
        ContContractSourceRelEntity cs = contContractSourceRelService.getById(contractSourceId);
        String contractId = cs.getContractId();
        ContContractEntity cont = contContractService.getById(contractId);
        String state = cont.getState();
        if(ContractStateEnum.STATUS_CHECKOUT.getValue().equals(state)){
            throw new Exception("该合同为退租状态，不能取消搬离");
        }
        String inspectId = checkOutUser.getInspectId();
        BizInspectEntity inspect = inspectService.getById(inspectId);
        inspect.setState(InspectStateEnum.CANCEL.getValue());
        inspectService.updateById(inspect);
        //入住人更改为已入住状态
        Map<String, Object> map = new HashMap<>();
        map.put("contractSourceId",checkOutUser.getContractSourceId());
        map.put("newType","20");
        map.put("newState","3");
        map.put("renterId",checkOutUser.getRenterId());
        contCheckInUserService.batchUpdate(map);
    }

    @Override
    @Transactional
    public void sureMoveaway(String id) throws Exception {
        BizCheckOutUserEntity checkOutUserEntity = this.getById(id);
        String inspectId = checkOutUserEntity.getInspectId();
        BizInspectEntity inspect = inspectService.getById(inspectId);
        inspect.setState(InspectStateEnum.COMPLETE.getValue());
        inspectService.updateById(inspect);
        //将入住状态改为未入住
        String contractSourceId = checkOutUserEntity.getContractSourceId();//房源关联id
        ContContractSourceRelEntity contContractSourceRel = contContractSourceRelService.getById(contractSourceId);
        ContContractEntity cont = contContractService.getById(contContractSourceRel.getContractId());
        String sourceId = contContractSourceRel.getSourceId();
        ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
        sourceEntity.setCheckIn(false);
        resSourceService.updateById(sourceEntity);
        ContCheckInHouseEntity checkInHouse = checkInHouseService.getByContractSourceId(contractSourceId);
        //数据迁移， 这张表数据可能为空
        if(ObjectUtil.isNull(checkInHouse)){
            checkInHouse = new ContCheckInHouseEntity();
            checkInHouse.setRenterId(checkOutUserEntity.getRenterId());
            checkInHouse.setContractSourceId(contractSourceId);
            checkInHouse.setExamine("2");
            checkInHouse.insert();
        }
        checkInHouse.setExamine("2");
        checkInHouse.updateById();
        //生成固耗账单
        CompletableFuture<AsyncResultVo<BilOrderEntity>> result = orderAutoTask.createLifeOrder(cont,contContractSourceRel, DateUtil.beginOfMonth(DateUtil.date()),DateUtil.beginOfDay(DateUtil.date()),false,true,false);
        createCheckOutOrder(result);
        //生成能耗账单
        CompletableFuture<AsyncResultVo<BilOrderEntity>> result2 =orderAutoTask.createLifeOrder(cont,contContractSourceRel,DateUtil.offsetMonth(DateUtil.beginOfMonth(DateUtil.date()),1),DateUtil.beginOfDay(DateUtil.date()),false,false,false);
        createCheckOutOrder(result2);
        //入住人搬离
        contCheckInUserService.remove(contractSourceId);
    }

    private void createCheckOutOrder(CompletableFuture<AsyncResultVo<BilOrderEntity>> result) throws ExecutionException, InterruptedException, BusinessException {
        if (StrUtil.isNotBlank(result.get().getMsg())) {
            throw new BusinessException(result.get().getMsg());
        }
        if (ObjectUtil.isNotNull(result.get().getObj()) && ObjectUtil.isNotNull(result.get().getObj().getItems()) && result.get().getObj().getItems().size() > 0) {
            BilOrderEntity order = result.get().getObj();
            order.setCode(resProjectService.getOrderCodeBySourceId(order.getSourceId(),"WY","order"));
            order.insert();
            for (BilOrderItemEntity item : order.getItems()) {
                item.setOrderId(order.getId());
                item.insert();
            }
        }
    }

    @Override
    public Map<String,Object> getLifeOrderByMoveaway(String id) throws Exception {
        Map<String,Object> resultDataMap = Maps.newHashMap();
        BizCheckOutUserEntity checkOutUserEntity = this.getById(id);
        //将入住状态改为未入住
        String contractSourceId = checkOutUserEntity.getContractSourceId();//房源关联id
        ContContractSourceRelEntity contContractSourceRel = contContractSourceRelService.getById(contractSourceId);
        String contractId = contContractSourceRel.getContractId();
        ContContractEntity cont = contContractService.getById(contractId);
        resultDataMap.put("contract",cont);

        String sourceId = contContractSourceRel.getSourceId();
        //判断是否有智能水电设备
        Map<String, Boolean> havingDevice = devDeviceService.havingDevice(sourceId);
        resultDataMap.putAll(havingDevice);

        //房源信息
        ResSourceVo source = resSourceService.getInfoById(sourceId);
        if (ObjectUtil.isNull(source)) {
            source = new ResSourceVo();
        }
        resultDataMap.put("source", source);

        List<String> showCost = configureService.getShowCost(cont.getCostConfigureId(),source);
        resultDataMap.put("showCost",showCost);
        //生成固耗账单
        CompletableFuture<AsyncResultVo<BilOrderEntity>> result = orderAutoTask.buildLifeOrder(cont,contContractSourceRel, DateUtil.beginOfMonth(DateUtil.date()),DateUtil.beginOfDay(DateUtil.date()),false,true,false);
        BilOrderEntity fixOrder = result.get().getObj();
        resultDataMap.put("fixOrder",fixOrder);
        //生成能耗账单
        CompletableFuture<AsyncResultVo<BilOrderEntity>> result2 =orderAutoTask.buildLifeOrder(cont,contContractSourceRel,DateUtil.offsetMonth(DateUtil.beginOfMonth(DateUtil.date()),1),DateUtil.beginOfDay(DateUtil.date()),false,false,false);
        BilOrderEntity energyOrder = result2.get().getObj();
        resultDataMap.put("energyOrder",energyOrder);
        return resultDataMap;
    }

    @Override
    @Transactional
    public void sureMoveaway(String id,BilOrderEntity fixOrder,BilOrderEntity energyOrder) throws Exception {
        BizCheckOutUserEntity checkOutUserEntity = this.getById(id);
        String inspectId = checkOutUserEntity.getInspectId();
        BizInspectEntity inspect = inspectService.getById(inspectId);
        inspect.setState(InspectStateEnum.COMPLETE.getValue());
        inspectService.updateById(inspect);
        //将入住状态改为未入住
        String contractSourceId = checkOutUserEntity.getContractSourceId();//房源关联id
        ContContractSourceRelEntity contContractSourceRel = contContractSourceRelService.getById(contractSourceId);
        ContContractEntity cont = contContractService.getById(contContractSourceRel.getContractId());
        String sourceId = contContractSourceRel.getSourceId();
        ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
        sourceEntity.setCheckIn(false);
        resSourceService.updateById(sourceEntity);
        ContCheckInHouseEntity checkInHouse = checkInHouseService.getByContractSourceId(contractSourceId);
        //数据迁移， 这张表数据可能为空
        if(ObjectUtil.isNull(checkInHouse)){
            checkInHouse = new ContCheckInHouseEntity();
            checkInHouse.setRenterId(checkOutUserEntity.getRenterId());
            checkInHouse.setContractSourceId(contractSourceId);
            checkInHouse.setExamine("2");
            checkInHouse.insert();
        }
        checkInHouse.setExamine("2");
        checkInHouse.updateById();

        Date createTime = new Date();
        fixOrder.setPayableTime(createTime);
        energyOrder.setPayableTime(createTime);

        AsyncResultVo<BilOrderEntity> result = new AsyncResultVo<>();
        result.setObj(fixOrder);
        //完成搬离时,暂不生成固耗或者能耗账单 caizhanghe edit 20240106
        if(!fixOrder.getOrderType().equals(OrderTypeEnum.FIXED.getValue()) && !fixOrder.getOrderType().equals(OrderTypeEnum.ENERGY.getValue())){
            createCheckOutOrder(CompletableFuture.completedFuture(result));
        }
        AsyncResultVo<BilOrderEntity> result2 = new AsyncResultVo<>();
        result2.setObj(energyOrder);
        //完成搬离时,暂不生成固耗或者能耗账单 caizhanghe edit 20240106
        if(!fixOrder.getOrderType().equals(OrderTypeEnum.FIXED.getValue()) && !fixOrder.getOrderType().equals(OrderTypeEnum.ENERGY.getValue())){
            createCheckOutOrder(CompletableFuture.completedFuture(result2));
        }
        //入住人搬离
        contCheckInUserService.remove(contractSourceId);
    }
}
