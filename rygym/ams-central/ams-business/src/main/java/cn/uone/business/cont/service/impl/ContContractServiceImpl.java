package cn.uone.business.cont.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.*;
import cn.uone.application.enumerate.contract.*;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.source.PriceTypeEnum;
import cn.uone.application.enumerate.source.SourceSignEnum;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.application.enumerate.source.SourceTypeEnum;
import cn.uone.bean.entity.business.base.BaseCarEntity;
import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.*;
import cn.uone.bean.entity.business.cont.bo.ContContractSourceRelBo;
import cn.uone.bean.entity.business.cont.dto.ContractDTO;
import cn.uone.bean.entity.business.cont.vo.*;
import cn.uone.bean.entity.business.demo.DemoContractEntity;
import cn.uone.bean.entity.business.res.*;
import cn.uone.bean.entity.business.res.vo.ResCostConfigureVo;
import cn.uone.bean.entity.business.res.vo.ResSourceDeviceRelVo;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.res.vo.SelectVo;
import cn.uone.bean.entity.business.sale.SaleDemandEntity;
import cn.uone.bean.entity.business.sys.SysAreaEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.business.sys.vo.RenterVo;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.guomi.cont.GuomiContractBodyVo;
import cn.uone.bean.entity.guomi.cont.GuomiContractFeeVo;
import cn.uone.bean.entity.tpi.fadada.ReqExtsignAuto;
import cn.uone.bean.entity.tpi.fadada.ReqUploadDocs;
import cn.uone.bean.parameter.SignContractVo;
import cn.uone.business.Guomi.service.IGuomiService;
import cn.uone.business.base.service.IBaseCarService;
import cn.uone.business.base.service.IBaseEnterpriseService;
import cn.uone.business.bil.service.IAccountBalanceService;
import cn.uone.business.bil.service.IBilOrderAutoService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.biz.service.IBizSettleService;
import cn.uone.business.cont.dao.ContContractDao;
import cn.uone.business.cont.service.*;
import cn.uone.business.cont.task.AuditContractTransactionEvent;
import cn.uone.business.cont.task.SignAfterTransactionEvent;
import cn.uone.business.demo.service.IDemoContractService;
import cn.uone.business.dev.service.IDevDeviceService;
import cn.uone.business.flow.domain.dto.FlowTaskDto;
import cn.uone.business.flow.domain.vo.FlowTaskVo;
import cn.uone.business.flow.service.IFlowTaskService;
import cn.uone.business.res.service.*;
import cn.uone.business.res.service.impl.ResProjectInfoServiceImpl;
import cn.uone.business.sale.dao.SaleDemandDao;
import cn.uone.business.sale.service.ISaleCustomerService;
import cn.uone.business.sys.service.ISysAreaService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.business.util.ContractUtil;
import cn.uone.business.util.ZipUtils;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.tpi.IFadadaFegin;
import cn.uone.fegin.tpi.IFileFeign;
import cn.uone.fegin.tpi.IWechatFegin;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.*;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.DateTimeUtil;
import cn.uone.web.util.PassWordCreateUtil;
import cn.uone.web.util.SafeCompute;
import cn.uone.web.util.UoneHeaderUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang.StringUtils;
import org.flowable.task.api.Task;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <p>
 * 服务实现类
 * 合同
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Service
public class ContContractServiceImpl extends ServiceImpl<ContContractDao, ContContractEntity> implements IContContractService {

    private final static Logger log = LoggerFactory.getLogger(ContContractServiceImpl.class);
    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private ISysMsgTemplateFegin sysMsgTemplateFegin;
    @Autowired
    @Lazy
    private IContTempService contTempService;
    @Autowired
    @Lazy
    private IBilOrderService bilOrderService;
    @Autowired
    private IContContractSourceRelService contractSourceRelService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    private IResCostConfigureService costConfigureService;
    @Autowired
    private IResSourceDeviceRelService sourceDeviceRelService;
    @Autowired
    private IContRentLadderService rentLadderService;
    @Autowired
    private IResSourceConfigureService resSourceConfigureService;
    @Autowired
    private ISaleCustomerService saleCustomerService;
    @Autowired
    private IContContractInfoService contractInfoService;
    @Autowired
    private IBaseEnterpriseService enterpriseService;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private ISysPushMsgService sysPushMsgService;
    @Resource
    private IRenterFegin renterFegin;
    @Autowired
    private IResProjectInfoService resProjectInfoService;
    @Autowired
    private IBaseCarService baseCarService;
    @Autowired
    private IContRentLadderService contRentLadderService;
    @Autowired
    private IContTempletCostRelService contTempletCostRelService;
    @Autowired
    @Lazy
    private IBizReleaseService bizReleaseService;
    @Value("${project.uoneTianDi.id}")
    private String uoneTianDiId;
    @Autowired
    private ISysAreaService sysAreaService;
    @Autowired
    private IResProjectParaService resProjectParaService;
    @Autowired
    private IContCheckInUserService checkInUserService;
    @Autowired
    @Lazy
    private IContCheckInHouseService checkInHouseService;
    @Autowired
    private IContParService contParService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    @Lazy
    private IBilOrderAutoService bilOrderAutoService;
    @Autowired
    private IGuomiService guomiService;
    @Autowired
    private IDemoContractService demoContractService;
    @Resource
    private IFadadaFegin fadadaFegin;
    @Autowired
    private IResSourceService sourceService;
    @Autowired
    @Lazy
    private IBizSettleService bizSettleService;

    @Lazy
    @Autowired
    private IFlowTaskService flowTaskService;

    @Resource
    private IWechatFegin wechatFegin ;
    @Autowired
    private IFileFeign fileFegin;

    @Resource
    private SaleDemandDao saleDemandDao;

    @Autowired
    private IDevDeviceService deviceService;

    @Autowired
    private IAccountBalanceService accountBalanceService;
    @Autowired
    private MinioUtil minioUtil;
    @Autowired
    private PdfUtil pdfUtil;
    @Autowired
    private IContContractFileRelService contContractFileRelService;
    @Autowired
    private IContContractPriceRelService contContractPriceRelService;


    @Override
    public void synImg(String id){
        //查找合同对应的营业执照,并更新到租客信息下
        if(StrUtil.isNotBlank(id)){
            ContContractEntity contract = this.getById(id);
            if(ObjectUtil.isNotNull(contract)){
                List<SysFileEntity> files = sysFileService.getListByFromIdAndType(id,SysFileTypeEnum.BUSINESS_LICENSE);
                if(!org.apache.shiro.util.CollectionUtils.isEmpty(files)){
                    ArrayList<SysFileEntity> fileEntities = new ArrayList<>();
                    for (SysFileEntity file : files) {
                        SysFileEntity f = new SysFileEntity();
                        f.setUrl(file.getUrl());
                        f.setFromId(contract.getSignerId());
                        f.setName(file.getName());
                        f.setType(SysFileTypeEnum.BUSINESS_LICENSE.getValue());
                        fileEntities.add(f);
                    }
                    if(!CollectionUtils.isEmpty(fileEntities)){
                        SysFileEntity sysFileEntity = new SysFileEntity();
                        sysFileEntity.setType(SysFileTypeEnum.BUSINESS_LICENSE.getValue());
                        sysFileEntity.setFromId(contract.getSignerId());
                        sysFileService.delFile(sysFileEntity);
                        sysFileService.saveOrUpdateBatch(fileEntities);
                    }
                }
            }
        }
    }

    @Override
    public List<ContContractVo> queryList(Map<String, Object> map) {
        return baseMapper.queryList(map);
    }


    /**
     * 参数判断;
     * 查看合同;
     */
    @Override
    public ContContractVo getContractByCheckOut(Map<String, Object> map) throws Exception {
        // 参数判断;
        if (StringUtils.isBlank(map.get("id").toString())) {
            throw new Exception("参数异常");
        }

        // 查看合同;
        ContContractVo contractVo;

        try {
            contractVo = baseMapper.getContractByCheckOut(map);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new Exception("查看合同发生异常");
        }

        if (contractVo == null) {
            throw new Exception("查看合同失败");
        }

        // 查询房源信息
        Map<String, Object> map2 = Maps.newHashMap();
        map2.put("contractId", map.get("id"));

        List<ContContractSourceRelVo> contractSourceRelVoList = contractSourceRelService.selectByVo(map2);

        if (contractSourceRelVoList == null || contractSourceRelVoList.size() < 1) {
            throw new Exception("查询房源信息失败");
        }

        contractVo.setContractSourceRelVoList(contractSourceRelVoList);

        return contractVo;
    }

    /**
     * 1.参数判断;
     * 2.更新合同;
     */
    @Transactional
    @Override
    public int updateContContract(ContContractEntity contContractEntity) throws Exception {
        // 1.参数判断;
        if (StrUtil.isBlank(contContractEntity.getId())) {
            throw new Exception("参数异常");
        }

        // 2.更新合同;
        int i;

        try {
            i = baseMapper.updateById(contContractEntity);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new Exception("更新合同发生异常");
        }

        if (i < 1) {
            throw new Exception("更新合同失败");
        }

        return i;
    }


    /**
     * 参数判断;
     * 查询是否存在已关联的合同;
     */
    @Override
    public Boolean existContContractByContractTempId(String contractTempletId) throws Exception {
        // 参数判断;
        if (StrUtil.isBlank(contractTempletId)) {
            throw new BusinessException("参数异常");
        }

        // 查询是否存在已关联的合同;
        QueryWrapper<ContContractEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("contract_templet_id", contractTempletId);
        List<ContContractEntity> list = baseMapper.selectList(wrapper);
        if (list.size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * 查询合同
     */
    @Override
    public IPage<ContContractVo> selectContContract(Page<ContContractEntity> page, Map<String, Object> map) throws Exception {
        // 查询合同
        DataScope dataScope = new DataScope(UoneSysUser.id());
        dataScope.setProAlias("C");
        dataScope.setProjectFieldName("project_id");
        return baseMapper.queryContractList(page, dataScope, map);
    }

    @Override
    public IPage<ContContractVo> releaseList(Page<ContContractEntity> page, Map<String, Object> map) {
        // 查询合同
        DataScope dataScope = new DataScope(UoneSysUser.id());
        dataScope.setProAlias("C");
        dataScope.setProjectFieldName("project_id");
        return baseMapper.releaseList(page,dataScope,map);
    }

    @Override
    public List<ContContractVo> releaseWaitRenterConfirmList(Map<String, Object> map) {
        return baseMapper.releaseWaitRenterConfirmList(map);
    }

    /**
     * 查询合同
     */
    @Override
    public IPage<ContContractVo> selectContContractByUser(Page<ContContractEntity> page, Map<String, Object> map) throws Exception {
        // 查询合同
        return baseMapper.queryContractPage(page, map);
    }




    /**
     * 查询合同
     */
    @Override
    public List<ContContractVo> selectContContract(Map<String, Object> map) throws Exception {
        // 查询合同
        DataScope dataScope = new DataScope(UoneSysUser.id());
        dataScope.setProAlias("C");
        dataScope.setProjectFieldName("project_id");
        return baseMapper.queryContractList(dataScope, map);
    }

    /**
     * 同住人导出
     */
    @Override
    public List<ContContractVo> getRoomMateList(Map<String, Object> map) throws Exception {
        // 查询合同
        DataScope dataScope = new DataScope(UoneSysUser.id());
        dataScope.setProAlias("C");
        dataScope.setProjectFieldName("project_id");
        return baseMapper.getRoomMateList(dataScope, map);
    }

    /**
     * 查询合同
     */
    @Override
    public List<ContContractVo> exportContContract(Map<String, Object> map) throws Exception {
        // 查询合同
        DataScope dataScope = new DataScope(UoneSysUser.id());
        dataScope.setProAlias("C");
        dataScope.setProjectFieldName("project_id");
        return baseMapper.exportContractList(dataScope, map);
    }

    /**
     * 查询房源合同导出使用
     */
    @Override
    public List<ContContractVo> exportSourceContract(Map<String, Object> map) throws Exception {
        // 查询合同
        DataScope dataScope = new DataScope(UoneSysUser.id());
        dataScope.setProAlias("C");
        dataScope.setProjectFieldName("project_id");
        return baseMapper.exportSourceContract(dataScope, map);
    }


    /**
     * 参数判断;
     * 根据签约用户id查询合同;
     */
    @Override
    public List<ContContractVo> getContContractBySignerId(Map<String, Object> map) throws Exception {
        // 参数判断;
        if (StrUtil.isBlank(map.get("signerId").toString())) {
            throw new Exception("参数异常");
        }

        // 根据签约用户id查询合同
        List<ContContractVo> list;

        try {
            list = baseMapper.selectBySignerId(map);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new Exception("根据签约用户id查询合同发生异常");
        }

        return list;
    }


    /**
     * 参数判断;
     * 根据签约用户id查询合同;
     */
    @Override
    public List<ContContractVo> getContractBySignerId(Map<String, Object> map) throws Exception {
        // 参数判断;
        if (StrUtil.isBlank(map.get("signerId").toString())) {
            throw new Exception("参数异常");
        }
        // 根据签约用户id查询合同
        List<ContContractVo> list;
        try {
            list = baseMapper.getBySignerId(map);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new Exception("根据签约用户id查询合同发生异常");
        }
        return list;
    }



    /**
     * 查询机构合同（分页、项目权限）
     */
    @Override
    public IPage<ContContractVo> selectByOrganize(Page<ContContractEntity> page, Map<String, Object> map) throws Exception {
        // 查询合同
        IPage<ContContractVo> iPage;

        DataScope dataScope = new DataScope(UoneSysUser.id());
        dataScope.setProAlias("E");
        dataScope.setProjectFieldName("id");

        try {
            iPage = baseMapper.selectByOrganize(page, dataScope, map);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new Exception("查询机构合同发生异常");
        }

        return iPage;
    }


    /**
     * 参数判断;
     * 赋值;
     * 新增机构合同;
     */
    @Transactional
    @Override
    public void saveOrUpdateContract(ContContractEntity contContractEntity) throws Exception {
        // 参数判断
        if (StrUtil.isBlank(contContractEntity.getContractCode())) {
            throw new Exception("合同编号不能为空");
        } else if (StrUtil.isBlank(contContractEntity.getContractTempletId())) {
            throw new Exception("合同模板不能为空");
        } else if (null == contContractEntity.getStartDate()) {
            throw new Exception("合同起始日不能为空");
        } else if (null == contContractEntity.getEndDate()) {
            throw new Exception("合同到期日不能为空");
        } else if (null == contContractEntity.getSignDate()) {
            throw new Exception("签约日期不能为空");
        } else if (StrUtil.isBlank(contContractEntity.getPaperCode())) {
            throw new Exception("纸质合同号不能为空");
        } else if (StrUtil.isBlank(contContractEntity.getPayType())) {
            throw new Exception("缴费方式不能为空");
        } else if (StrUtil.isBlank(contContractEntity.getRentPayPayer())) {
            throw new Exception("租金支付方不能为空");
        } else if (StrUtil.isBlank(contContractEntity.getLifePayPayer())) {
            throw new Exception("生活费用支付方不能为空");
        } else if (StrUtil.isBlank(contContractEntity.getCostConfigureId())) {
            throw new Exception("费用信息不能为空");
        } else if (StrUtil.isBlank(contContractEntity.getInvoiceType())) {
            throw new Exception("发票类型不能为空");
        }

        // 赋值
        contContractEntity.setSignType(SignTypeEnum.OFF_LINE.getValue()) // 线下签约
                .setState(ContractStateEnum.STATUS_REVIEW.getValue()) // 未生效
                .setIsOrganize(BaseConstants.BOOLEAN_OF_TRUE); // 是否机构
        this.saveOrUpdate(contContractEntity);
    }

    /**
     * 合同通过审核
     *
     * @param contract
     * @param source
     * @param renter
     * @throws Exception
     */
    @Transactional
    public void auditContract(ContContractEntity contract, ResSourceEntity source, RenterEntity renter, boolean isRecord) throws Exception {
        log.info("合同签约已通过审核");
        ContContractSourceRelEntity rel = contractSourceRelService.getByContractIdAndSourceId(contract.getId(), source.getId());
        //1.修改合同状态
        //判断时间，如果时间是当前之后就是未起租状态,正数
        contract.setState(ContractStateEnum.STATUS_TAKE_EFFECT.getValue()).updateById();
        long diffDay = ContractUtil.isBeforeToday(contract.getStartDate());
        if (diffDay <= 0) {
          if(contract.setState(ContractStateEnum.STATUS_TAKE_EFFECT.getValue()).updateById()){
              log.info("合同状态修改为 6生效");
          }else{
              log.info("合同状态修改为6生效，失败");
          }
        } else {
            if(contract.setState(ContractStateEnum.STATUS_NO_IN_TIME.getValue()).updateById()){
                log.info("合同状态修改为 5未启租");
            }else{
                log.info("合同状态修改为5未启租,失败");
            }
        }

        //2.修改房源状态
        source.setState(SourceStateEnum.RENT.getValue()).updateById();
        //3.添加入住交房&添加入住人&备案
        isRecord = false;
        if (contract.getIsOrganize().equals(BaseConstants.BOOLEAN_OF_FALSE) && ContractTypeEnum.APARTMENT.getValue().equals(contract.getContractType())) {
            //公寓平台没有入住办理，所以直接
            checkInHouseService.saveCheckInHouse(rel.getId(), contract.getSignerId(),BaseConstants.BOOLEAN_OF_FALSE);
            checkInUserService.saveCheckInUser(rel.getId(), renter, CheckInUserTypeEnum.CHECKIN, ApprovalStateEnum.COMPLETE, DataFromEnum.AUTO,null);
            //门锁授权
            try {
                deviceService.authorization(contract,source.getId(),contract.getSignerId());
            } catch (Exception e) {
                log.error("门禁授权失败");
                e.printStackTrace();
            }
            if (isRecord) {
                applicationEventPublisher.publishEvent(new SignAfterTransactionEvent(this, contract.getId(), RecordTypeEnum.ADD));
            }
        } else {
            if (isRecord) {
                applicationEventPublisher.publishEvent(new SignAfterTransactionEvent(this, contract.getId(), RecordTypeEnum.ADD));
            }
        }
        contParService.handerConPar(contract.getId());

        //6.反写销售客户信息
        saleCustomerService.signed(contract.getId());
        if (contract.getIsOrganize().equals(BaseConstants.BOOLEAN_OF_FALSE)){
            if (ContractTypeEnum.APARTMENT.getValue().equals(contract.getContractType())) {
                if (BaseConstants.BOOLEAN_OF_FALSE.equals(contract.getPlatform())) {
                    //9.短信通知
                    auditMsg(renter.getTel(), renter.getName());
                }
            } else {
                //9.短信通知
                auditMsg(renter.getTel(), renter.getName());
            }
        }
        BizReleaseEntity release = bizReleaseService.getByNewContractId(contract.getId());
        if(ObjectUtil.isNotNull(release) && ReleaseTypeEnum.RELET.getValue().equals(release.getType())){//续租时，更新旧合同状态为已退租
            String oldContractId = release.getContractId();
            if(StringUtils.isNotBlank(oldContractId)){
                ContContractEntity oldContract = getById(oldContractId);
                oldContract.setState(ContractStateEnum.STATUS_TERMINATION.getValue()).updateById();
                //oldContract.updateById();
                //this.updateById(oldContract);
            }
        }
        String createRentType = "1";//租金生成规则 1自然月 2合同月 默认是自然月
        ResProjectParaEntity projectPara = resProjectParaService.getByCodeAndProjectId("CREATE_RENT_TYPE", source.getProjectId());//查询租金生成类型 自然月或者合同月
        if(projectPara != null){
            createRentType = projectPara.getParamValue();
        }
        //7.公寓且XX平台 或 非公寓 创建租金账单
        //if ((ContractTypeEnum.APARTMENT.getValue().equals(contract.getContractType()))
               // || !ContractTypeEnum.APARTMENT.getValue().equals(contract.getContractType())) {
            //4.水电周转金以及押金子账单（废弃）
            //bilOrderAutoService.createDepositOrder(contract);
            //5.生成账单
            applicationEventPublisher.publishEvent(new AuditContractTransactionEvent(this, contract,createRentType));
        // }
    }


    private String getMonths(Date beginDate, Date endDate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");//格式化为年月
        SimpleDateFormat mdf = new SimpleDateFormat("M");//格式化为月
        String result = "";
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        min.setTime(sdf.parse(sdf.format(beginDate)));
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
        max.setTime(sdf.parse(sdf.format(endDate)));
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
        Calendar curr = min;
        List<String> resList = new ArrayList<>();
        while (curr.before(max)) {
            if (!resList.contains(mdf.format(curr.getTime()))) {
                resList.add(mdf.format(curr.getTime()));
            }
            curr.add(Calendar.MONTH, 1);
        }
        for (String content : resList) {
            result = result + content + ",";
        }
        result = result.substring(0, result.length() - 1);
        return result;
    }

    public void auditMsg(String tel, String name) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("name", name);
        params.put("mobile", tel);
        params.put("template_code", "97223"); // 模板code
        // modify by linderen on 20210714 修改通知方式为公众号通知 start
        //sysMsgTemplateFegin.send(params);
        RenterEntity renter = renterFegin.getByTelAndType(tel, RenterType.COMMON.getValue());
//        if(renter.getOpenid()!=null && renter.getOpenid()!="") {
//            wechatFegin.sendMsgByTempWithOpenid(renter.getOpenid(), "XX公寓", "账单提醒",
//                    "尊敬的" + name + "，您的租赁合同已审核通过，请及时支付账单哦");
//        }
        //modify by linderen on 20210714 修改通知方式为公众号通知 end

    }

    private void syncContractGuomi(ContContractEntity contract, ResSourceEntity source, RenterEntity renter, ContContractSourceRelEntity rel) throws Exception {
        GuomiContractBodyVo vo = new GuomiContractBodyVo();
        vo.setOutContractId(contract.getContractCode());
        ResProjectEntity projectEntity = resProjectService.getById(source.getProjectId());
        vo.setRoomId(source.getCcbSourceId());
        vo.setProjectId(projectEntity.getCcbProjectId());
        vo.setCustName(renter.getName());
        vo.setCustSex(renter.getSex());
        vo.setCustPhone(renter.getTel());
        vo.setCustCardType(IdTypeEnum.getGuomiIdTypeEnum(renter.getIdType()));
        vo.setCustCardNo(renter.getIdNo());
        vo.setBeginDate(DateUtil.format(contract.getStartDate(), "yyyy-MM-dd"));
        vo.setEndDate(DateUtil.format(contract.getEndDate(), "yyyy-MM-dd"));
        vo.setSignDate(DateUtil.format(contract.getSignDate(), "yyyy-MM-dd HH:mm:ss"));

        Set set = Sets.newHashSet();
        String result = getMonths(contract.getStartDate(), contract.getEndDate());
        //result = "1,2,3,4,5,6,7,8,9,10,11,12";
        GuomiContractFeeVo feeVo = new GuomiContractFeeVo();
        feeVo.setFeeName("押金");
        feeVo.setFeeType("1");
        feeVo.setAmount(rel.getCashPledge());
        feeVo.setFeePayInterval("0");

        Calendar cal = Calendar.getInstance();
        cal.setTime(contract.getSignDate());
        //因为计算出来少一个月，所以加上
        cal.add(Calendar.MONTH, 1);
        int month = cal.get(Calendar.MONTH);
        feeVo.setPayMonth(String.valueOf(month));
        set.add(feeVo);

        feeVo = new GuomiContractFeeVo();
        feeVo.setFeeName("租金");
        feeVo.setFeeType("0");
        ContRentLadderEntity rentLadderEntity = rentLadderService.getRentLadderByContSourceId(rel.getId());
        BigDecimal price = getPrice(contract, renter, source.getId());
        feeVo.setAmount(price);
        feeVo.setFeePayInterval(PayTypeEnum.getCcbPayIntervalValue(contract.getPayType()));
        feeVo.setPayMonth(result);
        set.add(feeVo);

        List<Map<String, Object>> costList = PriceTypeEnum.toList();
        for (Map<String, Object> map : costList) {
            feeVo = new GuomiContractFeeVo();
            feeVo.setFeeName(map.get("name").toString());
            feeVo.setFeeType("0");
            feeVo.setFeePayInterval("1");
            feeVo.setPayMonth(result);
            if (PriceTypeEnum.ELECTRIC.getName().equals(map.get("name").toString())
                    || PriceTypeEnum.WATER.getName().equals(map.get("name").toString())) {
                feeVo.setAmount(new BigDecimal("0.00"));
                set.add(feeVo);
            } else {
                //因为建信那边长度为20，不能超过7个金，因此把特殊履约保证金改为特殊保证金
                // if (PriceTypeEnum.SEPECIAL_MARGINFEE.getName().equals(map.get("name").toString())) {
                //feeVo.setFeeName("特殊保证金");
                //}
                //feeVo.setAmount(resCostLadderService.queryByTypeAndCostId(contract.getCostConfigureId(), map.get("value").toString()));
                //set.add(feeVo);
            }
        }
        vo.setFees(set);

        String contId = guomiService.addGuomiContract(vo);
        if (StrUtil.isNotEmpty(contId)) {
            contract.setPlatformCode(contId);
            contract.setSyncState(ContractSyncEnum.SYNC_SUCCESSS.getValue());
            contract.updateById();
        } else {
            contract.setSyncState(ContractSyncEnum.SYNC_NO.getValue());
            contract.updateById();
            //手动回滚
            //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new BusinessException("合同同步到建信平台失败，请联系技术人员");
        }
    }

    /**
     * 机构合同-公寓 PDF生成
     */
    private String orgApartContractPDF(Map<String, Object> data,Map<String, Object> map) throws Exception {
        // 机构合同-公寓
        ContContractEntity contractEntity = (ContContractEntity) data.get("contractEntity"); // 合同
        // 获取数据
        ContContractTempletEntity contractTempletEntity = (ContContractTempletEntity) data.get("contractTempletEntity"); // 合同模板
        List<ContContractSourceRelBo> contractSourceRelBoList = (List<ContContractSourceRelBo>) data.get("contractSourceRelBoList"); // 合同房源关系
        List<ResCostConfigureVo> costConfigureVoList = (List<ResCostConfigureVo>) data.get("costConfigureVoList"); // 费用配置信息
        ResProjectEntity projectEntity = (ResProjectEntity) data.get("projectEntity"); // 项目
        String province = (String) data.get("province");
        String city =  (String) data.get("city");
        String distric = (String)data.get("distric");


        // 付款周期
        String payMode = "";

        if (PayTypeEnum.ONE_ONE.getValue().equals(contractEntity.getPayType()) || PayTypeEnum.TWO_ONE.getValue().equals(contractEntity.getPayType())) {
            payMode = "<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                    + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                    + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>"
                    + "每月&nbsp; &nbsp;□每季度&nbsp; &nbsp;□每半年&nbsp; &nbsp;□每年";
        }
        if (PayTypeEnum.ONE_THREE.getValue().equals(contractEntity.getPayType())) {
            payMode = "□每月&nbsp; &nbsp;"
                    + "<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                    + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                    + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>"
                    + "每季度&nbsp; &nbsp;□每半年&nbsp; &nbsp;□每年";
        }
        if (PayTypeEnum.ONE_SIX.getValue().equals(contractEntity.getPayType())) {
            payMode = "□每月&nbsp; &nbsp;□每季度&nbsp; &nbsp;"
                    + "<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                    + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                    + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>"
                    + "每半年&nbsp; &nbsp;□每年";
        }
        if (PayTypeEnum.ONE_TWELVE.getValue().equals(contractEntity.getPayType())) {
            payMode = "□每月&nbsp; &nbsp;□每季度&nbsp; &nbsp;□每半年&nbsp; &nbsp;"
                    + "<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                    + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                    + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>"
                    + "每年";
        }

        // 拼接html
        StringBuilder roomListBuilder = new StringBuilder().append("<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%\"><tr class=\"firstRow\" style=\"background:rgb(242, 242, 242);\"><td>房屋坐落</td><td>面积(平方米)</td><td>户型图</td></tr>");
        StringBuilder roomFeeListBuilder = new StringBuilder().append("<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%\"><tr class=\"firstRow\" style=\"background:rgb(242, 242, 242);\">" +
                "<td>房屋坐落</td><td>履约保证金(元)</td><td>租金(元/月)</td><td>物业费(元/每平米*月)</td><td>公维金(元/每平米*月)</td><td>宽带费(元/月)</td><td>公摊水费</td><td>公摊电费</td><td>其他(元/月)</td></tr>");
        StringBuilder deviceListBuilder = new StringBuilder();

        StringBuilder signDetail = new StringBuilder().append("" +
                "<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%;color:black;text-align:center;\">" +
                "<tr class=\"firstRow\" style=\"background:rgb(242, 242, 242);\">" +
                    "<td>序号</td>" +
                    "<td>房间号</td>" +
                    "<td>面积(m²)</td>" +
                    "<td>履约保证金(元)</td>" +
                    "<td>月租金(元)</td>" +
                    "<td>网络费(元)</td>" +
                    "<td>水费标准(元/度)</td>" +
                    "<td>电费标准(元/度)</td>" +
                "</tr>");

        StringBuilder signDetailNS = new StringBuilder().append("" +
                "<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%;color:black;text-align:center;\">" +
                "<tr class=\"firstRow\" style=\"background:rgb(242, 242, 242);\">" +
                "<td>序号</td>" +
                "<td>房间号</td>" +
                "<td>面积(m²)</td>" +
                "<td>履约保证金(元)</td>" +
                "<td>月租金(元)</td>" +
                "<td>综合服务费(元)</td>" +
                "<td>水费标准(元/月)</td>" +
                "<td>电费标准(元/度)</td>" +
                "</tr>");
        // 合计
        BigDecimal prices = BigDecimal.ZERO; // 租金
        BigDecimal cashPledges = BigDecimal.ZERO; // 押金
        BigDecimal propertyFees = BigDecimal.ZERO; // 物业费
        BigDecimal maintenanceCharges = BigDecimal.ZERO; // 公维金
        BigDecimal networkCharges = BigDecimal.ZERO; // 宽带费
        BigDecimal serviceCharges = BigDecimal.ZERO; // 综合服务费
        BigDecimal areas = BigDecimal.ZERO; // 面积
        BigDecimal waterCharge = BigDecimal.ZERO; // 水费
        BigDecimal electricCharge = BigDecimal.ZERO; // 电费
        BigDecimal waterCharges = BigDecimal.ZERO; // 水费
        BigDecimal electricCharges = BigDecimal.ZERO; // 电费
        int index = 0;
        for (ContContractSourceRelBo contractSourceRelBo : contractSourceRelBoList) {
            index++;
            // 地址
            String location = province + city + distric + projectEntity.getAddress() + contractSourceRelBo.getPartitionName()+"号" + contractSourceRelBo.getCode()+"单元";
            // 租金
            BigDecimal price = contractSourceRelBo.getPrice();
            // 押金
            BigDecimal cashPledge = contractSourceRelBo.getCashPledge();
            // 物业费
            BigDecimal propertyFee = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.PROPERTY_BASEFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取公寓物业基本服务费
            ).collect(Collectors.toList()).get(0).getPrice();
            // 公维金
            BigDecimal maintenanceCharge = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.PROPERTY_PUBLICFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取公寓房屋公维金
            ).collect(Collectors.toList()).get(0).getPrice();
            // 宽带费
            BigDecimal networkCharge = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.NETWORK_FEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取网络费
            ).collect(Collectors.toList()).get(0).getPrice();

            BigDecimal serviceCharge = propertyFee;//综合服务费


            BigDecimal area = contractSourceRelBo.getArea();

            //查找房屋户型图
            String imgcxt = "";
            List<SysFileEntity> file = sysFileService.getListByFromIdAndType(contractSourceRelBo.getHouseTypeId(), SysFileTypeEnum.HOUSEFLOOR);
            if (!CollectionUtils.isEmpty(file)) {
                SysFileEntity fileAttachment = file.get(0);
                imgcxt = "<img width=\"180\" height=\"120\" src=\""  + fileAttachment.getPath() + "\" title=\"户型图\" />";
            }
            // 获取该房源设备信息
            List<ResSourceDeviceRelVo> sourceDeviceRelVoList = sourceDeviceRelService.getDevice(contractSourceRelBo.getSourceId(), null);

            // 房源设备表格
            deviceListBuilder.append("<br/><p>")
                    .append(contractSourceRelBo.getSourceName())
                    .append("</p><table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%\"><tr class=\"firstRow\" style=\"background:rgb(242, 242, 242);\"><td>区域</td><td>名称</td><td>数量</td><td colspan=\"2\">价值</td></tr>");

            for (ResSourceDeviceRelVo vo : sourceDeviceRelVoList) {
                deviceListBuilder.append("<tr><td></td><td>")
                        .append(vo.getName()).append("</td><td>1</td><td>")
                        .append(vo.getDamage()).append("</td><td>元/件</td></tr>");
            }

            deviceListBuilder.append("</table>");


            //楼层号
            Integer flo = contractSourceRelBoList.get(0).getFloor();
            try {
                // 水费
                List<ResCostConfigureVo> waterFeeList;
                if(flo != null){
                    BigDecimal floor = new BigDecimal(flo.intValue());
                    waterFeeList = costConfigureVoList.stream().filter(costConfigureVo ->
                                    PriceTypeEnum.WATER.getValue().equals(costConfigureVo.getCostType()) &&
                                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) && costConfigureVo.getLowerLimit().compareTo(floor)<=0
                                            && (costConfigureVo.getTopLimit() == null || costConfigureVo.getTopLimit().compareTo(floor)>=0)
                            // 获取水费
                    ).collect(Collectors.toList());
                    if(waterFeeList.size() > 1){
                        waterFeeList = waterFeeList.stream().filter(costConfigureVo ->
                                costConfigureVo.getTopLimit() != null
                        ).collect(Collectors.toList());
                    }
                }else{
                    waterFeeList = costConfigureVoList.stream().filter(costConfigureVo ->
                            PriceTypeEnum.WATER.getValue().equals(costConfigureVo.getCostType()) &&
                                    SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取水费
                    ).collect(Collectors.toList());
                }
                if(waterFeeList.size() > 1){
                    throw new BusinessException("生成合同PDF异常：水费配置错误");
                }
                waterCharge = contractSourceRelBo.getProjectName().equals("一住公寓")?new BigDecimal(30): waterFeeList.get(0).getPrice();
            } catch (Exception e) {
                throw new BusinessException("生成合同PDF异常：水费配置错误");
            }

            try {
                // 电费
                List<ResCostConfigureVo> elecFeeList;
                if(flo != null){
                    BigDecimal floor = new BigDecimal(flo.intValue());
                    elecFeeList = costConfigureVoList.stream().filter(costConfigureVo ->
                                    PriceTypeEnum.ELECTRIC.getValue().equals(costConfigureVo.getCostType()) &&
                                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) && costConfigureVo.getLowerLimit().compareTo(floor)<=0
                                            && (costConfigureVo.getTopLimit() == null || costConfigureVo.getTopLimit().compareTo(floor)>=0)
                            // 获取水费
                    ).collect(Collectors.toList());
                    if(elecFeeList.size() > 1){
                        elecFeeList = elecFeeList.stream().filter(costConfigureVo ->
                                costConfigureVo.getTopLimit() != null
                        ).collect(Collectors.toList());
                    }
                }else{
                    elecFeeList = costConfigureVoList.stream().filter(costConfigureVo ->
                            PriceTypeEnum.ELECTRIC.getValue().equals(costConfigureVo.getCostType()) &&
                                    SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取电费
                    ).collect(Collectors.toList());
                }
                if(elecFeeList.size() > 1){
                    throw new BusinessException("生成合同PDF异常：电费配置错误");
                }
                electricCharge = elecFeeList.get(0).getPrice();
            } catch (Exception e) {
                throw new BusinessException("生成合同PDF异常：电费配置错误");
            }


            // 房源费用
            roomListBuilder.append("<tr><td>").append(location).append("</td><td>").append(contractSourceRelBo.getArea()).append("</td><td>").append(imgcxt)
                    .append("</td></tr>");
            roomFeeListBuilder.append("<tr><td>").append(contractSourceRelBo.getCode()).append("</td><td>").append(cashPledge).append("</td><td>").append(price)
                    .append("</td><td>").append(propertyFee).append("</td><td>").append(maintenanceCharge).append("</td><td>").append(networkCharge)
                    .append("</td><td colspan=\"2\">按物业公用部分的总费用分摊</td><td>").append(BigDecimal.ZERO).append("</td></tr>");
            signDetail.append("<tr>" +
                        "<td>").append(index).append("</td>" + //序号
                        "<td>").append(location).append("</td>" + //房间号
                        "<td>").append(area).append("</td>" + //面积
                        "<td>").append(cashPledge).append("</td>" + //履约保证金
                        "<td>").append(price).append("</td>" + //月租金
                        "<td>").append(networkCharge).append("</td>" + //网络费
                    "<td>").append(waterCharge).append("</td>" + //网络费
                    "<td>").append(electricCharge).append("</td>" + //网络费
                        "</tr>");

            signDetailNS.append("<tr>" +
                    "<td>").append(index).append("</td>" + //序号
                    "<td>").append(location).append("</td>" + //房间号
                    "<td>").append(area).append("</td>" + //面积
                    "<td>").append(cashPledge).append("</td>" + //履约保证金
                    "<td>").append(price).append("</td>" + //月租金
                    "<td>").append(serviceCharge).append("</td>" + //综合服务费
                    "<td>").append(waterCharge).append("</td>" + //网络费
                    "<td>").append(electricCharge).append("</td>" + //网络费
                    "</tr>");

            // 合计
            prices = prices.add(price); // 租金
            cashPledges = cashPledges.add(cashPledge); // 押金
            propertyFees = propertyFees.add(propertyFee); // 物业费
            maintenanceCharges = maintenanceCharges.add(maintenanceCharge); // 公维金
            networkCharges = networkCharges.add(networkCharge); // 宽带费
            serviceCharges = serviceCharges.add(serviceCharge);
            waterCharges  = waterCharges.add(waterCharge);
            electricCharges =electricCharges.add(electricCharge);
            areas = SafeCompute.add(areas,area);
        }

        roomListBuilder.append("</table>");
        roomFeeListBuilder.append("<tr><td>合计</td><td>").append(cashPledges).append("</td><td>").append(prices).append("</td><td>")
                .append(propertyFees).append("</td><td>").append(maintenanceCharges).append("</td><td>").append(networkCharges)
                .append("</td><td colspan=\"2\">按物业公用部分的总费用分摊</td><td>").append(BigDecimal.ZERO).append("</td></tr>");
        roomFeeListBuilder.append("<tr><td>付款周期</td><td>————</td><td>每季度</td><td>每月</td><td>每月</td><td>每月</td><td>每月</td><td>每月</td><td>每月</td></tr></table>");

        signDetail.append("<tr>" +
                "<td colspan=\"2\">合计</td>" +
                "<td>").append(areas).append("</td>" + //面积
                "<td>").append(cashPledges).append("</td>" + //履约保证金
                "<td>").append(prices).append("</td>" + //月租金
                "<td>").append(networkCharges).append("</td>" + //综合服务费
                "<td>").append(waterCharges).append("</td>" + //综合服务费
                "<td>").append(electricCharges).append("</td>" + //综合服务费
                "</tr>");
        signDetailNS.append("<tr>" +
                "<td colspan=\"2\">合计</td>" +
                "<td>").append(areas).append("</td>" + //面积
                "<td>").append(cashPledges).append("</td>" + //履约保证金
                "<td>").append(prices).append("</td>" + //月租金
                "<td>").append(serviceCharges).append("</td>" + //综合服务费
                "<td>").append(waterCharges).append("</td>" + //综合服务费
                "<td>").append(electricCharges).append("</td>" + //综合服务费
                "</tr>");
        signDetail.append("<tr>" +
                "<td colspan=\"2\">付款周期</td>" +
                "<td>").append("--").append("</td>" +
                "<td>").append("--").append("</td>" +
                "<td>").append(payMode).append("</td>" + //月租金
                "<td>").append("每月").append("</td>" + //综合服务费
                "<td>").append("--").append("</td>" +
                "<td>").append("--").append("</td>" +
                "</tr>");
        signDetailNS.append("<tr>" +
                "<td colspan=\"2\">付款周期</td>" +
                "<td>").append("--").append("</td>" +
                "<td>").append("--").append("</td>" +
                "<td>").append(payMode).append("</td>" + //月租金
                "<td>").append("每月").append("</td>" + //综合服务费
                "<td>").append("--").append("</td>" +
                "<td>").append("--").append("</td>" +
                "</tr>");
        signDetail.append("<tr>" +
                "<td colspan=\"2\">租期</td>" +
                "<td colspan=\"6\">").append("自"+DateUtil.format(contractEntity.getStartDate(),"yyyy年MM月dd日")+"至"+DateUtil.format(contractEntity.getEndDate(),"yyyy年MM月dd日")).append("</td>" +
                "</tr>");
        signDetailNS.append("<tr>" +
                "<td colspan=\"2\">租期</td>" +
                "<td colspan=\"6\">").append("自"+DateUtil.format(contractEntity.getStartDate(),"yyyy年MM月dd日")+"至"+DateUtil.format(contractEntity.getEndDate(),"yyyy年MM月dd日")).append("</td>" +
                "</tr>");
        /*signDetail.append("<tr>" +
                "<td colspan=\"2\">水费标准</td>" +
                "<td>").append("3.90元/吨").append("</td>" +
                "<td colspan=\"2\">电费标准</td>" +
                "<td>").append("0.95元/度").append("</td>" +
                "</tr>");*/

        signDetail.append("<tr>" +
                "<td colspan=\"2\">水电费付款周期</td>" +
                "<td colspan=\"6\">").append("每月").append("</td>" +
                "</tr>");
        signDetail.append("</table>");
        signDetailNS.append("<tr>" +
                "<td colspan=\"2\">水电费付款周期</td>" +
                "<td colspan=\"6\">").append("每月").append("</td>" +
                "</tr>");
        signDetailNS.append("</table>");


        List<SysFileEntity> businessLicenses = sysFileService.getFiles(new SysFileEntity().setFromId(contractEntity.getId()).setType(SysFileTypeEnum.BUSINESS_LICENSE.getValue()));

        String businessLicenseHtml = "";

        for (SysFileEntity file : businessLicenses) {
            businessLicenseHtml += "<img src=\"" + file.getPath() + "\" width=\"300\" height=\"200\" />";
        }

        String htmlStr = contractTempletEntity.getContent();
        htmlStr = htmlStr.replace("!roomList!", roomListBuilder.toString());
        htmlStr = htmlStr.replace("!deviceList!", deviceListBuilder.toString());
        htmlStr = htmlStr.replace("!roomFeeList!", roomFeeListBuilder.toString());
        htmlStr = htmlStr.replace("!signDetail!", signDetail.toString());
        htmlStr = htmlStr.replace("!signDetailNS!", signDetailNS.toString());
        htmlStr = htmlStr.replace("!payMode!", payMode);
        //机构营业执照（或组织机构代码证） (目前数据为空)
        htmlStr = htmlStr.replace("!busLicense!", "<div>" + businessLicenseHtml + "</div>");

        return htmlStr;
    }

    /**
     * 机构合同-车位 PDF生成
     */
    private String orgCarContractPDF(Map<String, Object> data,Map<String, Object> map) throws Exception {
        // 获取数据
        ContContractTempletEntity contractTempletEntity = (ContContractTempletEntity) data.get("contractTempletEntity"); // 合同模板
        List<ContContractSourceRelBo> contractSourceRelBoList = (List<ContContractSourceRelBo>) data.get("contractSourceRelBoList"); // 合同房源关系
        List<ResCostConfigureVo> costConfigureVoList = (List<ResCostConfigureVo>) data.get("costConfigureVoList"); // 费用配置信息

        // 合计
        BigDecimal prices = BigDecimal.ZERO; // 租金
        BigDecimal cashPledges = BigDecimal.ZERO; // 押金
        BigDecimal propertyFees = BigDecimal.ZERO; // 物业费
        BigDecimal maintenanceCharges = BigDecimal.ZERO; // 公维金

        for (ContContractSourceRelBo contractSourceRelBo : contractSourceRelBoList) {
            // 地址
            //String location = projectEntity.getAddress() + contractSourceRelBo.getPartitionName() + contractSourceRelBo.getCode();
            // 租金
            BigDecimal price = contractSourceRelBo.getPrice();
            // 押金
            BigDecimal cashPledge = contractSourceRelBo.getCashPledge();
            // 物业费
            BigDecimal propertyFee = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.PROPERTY_BASEFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            "2".equals(costConfigureVo.getSourceType()) // 获取公寓物业基本服务费
            ).collect(Collectors.toList()).get(0).getPrice();
            // 公维金
            BigDecimal maintenanceCharge = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.PROPERTY_PUBLICFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            "2".equals(costConfigureVo.getSourceType()) // 获取公寓房屋公维金
            ).collect(Collectors.toList()).get(0).getPrice();

            // 合计
            prices = SafeCompute.add(prices,price);
            cashPledges = SafeCompute.add(cashPledges,cashPledge); // 押金
            propertyFees = SafeCompute.add(propertyFees,propertyFee); // 物业费
            maintenanceCharges = SafeCompute.add(maintenanceCharges,maintenanceCharge); // 公维金
        }
        //展示物业费的时候要加上公维金
        propertyFees = propertyFees.add(maintenanceCharges);
        String htmlStr = contractTempletEntity.getContent();

        String customerTypeHtml = "□个人 &nbsp; &nbsp;<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\"" +
                "                + width=\"12\" height=\"12\" border=\"0\" vspace=\"0\"" +
                "                + style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"/>非个人";
        htmlStr = htmlStr.replace("!customerTypeHtml!", customerTypeHtml);

        // 发票
        String isNeedInvoicHtml = "□需要发票 &nbsp; &nbsp;□不需要发票";
        htmlStr = htmlStr.replace("!isNeedInvoicHtml!", isNeedInvoicHtml);

        String carMsg = "以租客车位交接表为准";
        map.put("carUser", carMsg);
        map.put("carBelonger", carMsg);
        map.put("carNum", carMsg);

        map.put("prices",prices);
        map.put("cashPledges",cashPledges);
        map.put("propertyFees",propertyFees);
        map.put("maintenanceCharges",maintenanceCharges);
        return htmlStr;
    }

    /**
     * 机构合同-商业 PDF生成
     */
    private String orgBusinessContractPDF(Map<String, Object> data,Map<String, Object> map) throws Exception {
        // 获取数据
        ContContractTempletEntity contractTempletEntity = (ContContractTempletEntity) data.get("contractTempletEntity"); // 合同模板
        List<ContContractSourceRelBo> contractSourceRelBoList = (List<ContContractSourceRelBo>) data.get("contractSourceRelBoList"); // 合同房源关系
        ContContractEntity contractEntity = (ContContractEntity) data.get("contractEntity"); // 合同
        String province = (String) data.get("province");
        String city =  (String) data.get("city");
        String distric = (String)data.get("distric");

        // 拼接html
        String htmlStr = contractTempletEntity.getContent();

        //房源列表
        StringBuilder roomBuilder = new StringBuilder();
        StringBuilder roomRentBuilder = new StringBuilder();
        StringBuilder roomListBuilder = new StringBuilder();
        BigDecimal[] priceArray = null;
        String[] dateArray = null;

        roomBuilder = roomBuilder.append(
                "<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%\"><tr><td style=\"width:50%\">房屋坐落</td><td>面积(平方米)</td></tr>");

        roomListBuilder = roomListBuilder.append(
                "<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%\"><tr><td style=\"width:50%\">房屋坐落</td><td>面积(平方米)</td><td>户型图</td></tr>");
//        商业合同只能关联一个房源 --- 如果租金有多条, 同一个房源 会有多条 ContContractSourceRel, 只取一条进行拼接合同模板
//        for (ContContractSourceRelBo cr : contractSourceRelBoList) {
//
//        }
        ContContractSourceRelBo cr = contractSourceRelBoList.get(0);
        BigDecimal area = cr.getArea();
        String location = province + city + distric + cr.getSourceName();
        roomBuilder = roomBuilder.append("<tr><td>").append(location).append("</td><td>").append(area).append("</td></tr>");

        //查找房屋户型图
        String imgcxt = "";
        List<SysFileEntity> file = sysFileService.getListByFromIdAndType(cr.getHouseTypeId(), SysFileTypeEnum.HOUSEFLOOR);

        if (!CollectionUtils.isEmpty(file)) {
            SysFileEntity fileAttachment = file.get(0);
            imgcxt = "<img width=\"180\" height=\"120\" src=\""  + fileAttachment.getPath() + "\" title=\"户型图\" />";
        }
        roomListBuilder = roomListBuilder.append("<tr><td>").append(location).append("</td><td>").append(area).append("</td><td>").append(imgcxt)
                .append("</td></tr>");

        roomRentBuilder.append("房源：").append(cr.getSourceName()).append("\n");
        roomRentBuilder.append(
                "<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%;text-align:center\"><tr><td>时间段</td><td>租金(元/月)</td></tr>");
        List<ContRentLadderVo> contRentLadderVos = contRentLadderService.selectList(cr.getId());
        if (priceArray == null && dateArray == null && contRentLadderVos.size() > 0) {
            priceArray = new BigDecimal[contRentLadderVos.size()];
            dateArray = new String[contRentLadderVos.size()];
        }
        for (int i = 0; i < contRentLadderVos.size(); i++) {
            String sDate = DateTimeUtil.formatDateTimetoString(contRentLadderVos.get(i).getStartDate(),DateTimeUtil.FMT_yyyyMMdd);
            String eDate =  DateTimeUtil.formatDateTimetoString(contRentLadderVos.get(i).getEndDate(),DateTimeUtil.FMT_yyyyMMdd);
            BigDecimal rent = contRentLadderVos.get(i).getPrice();
            roomRentBuilder.append("<tr><td>");
            roomRentBuilder.append(sDate);
            roomRentBuilder.append("~");
            roomRentBuilder.append(eDate);
            roomRentBuilder.append("</td><td>");
            roomRentBuilder.append(rent);
            roomRentBuilder.append("</td></tr>");
            priceArray[i] = (priceArray[i] == null ? BigDecimal.ZERO : priceArray[i]).add(rent);
            dateArray[i] = dateArray[i] == null ? (sDate + "~" + eDate) : dateArray[i];
        }
        roomRentBuilder.append("</table>");
        roomRentBuilder.append("\n");
        roomBuilder = roomBuilder.append("</table>");
        roomRentBuilder.append("合计：").append("\n");
        roomRentBuilder.append("<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%;text-align:center\"><tr><td>时间段</td><td>租金(元/月)</td></tr>");
        for (int i = 0; i < priceArray.length; i++) {
            roomRentBuilder.append("<tr><td>");
            roomRentBuilder.append(dateArray[i]);
            roomRentBuilder.append("</td><td>");
            roomRentBuilder.append(priceArray[i]);
            roomRentBuilder.append("</td></tr>");
        }
        roomRentBuilder.append("</table>");
        roomRentBuilder.append("\n");
        roomListBuilder = roomListBuilder.append("</table>");

        // 营业执照与身份证图片
        List<SysFileEntity> idCards = sysFileService.getFiles(new SysFileEntity().setFromId(contractEntity.getId()).setType(SysFileTypeEnum.ID_CARD.getValue()));
        List<SysFileEntity> businessLicenses = sysFileService.getFiles(new SysFileEntity().setFromId(contractEntity.getId()).setType(SysFileTypeEnum.BUSINESS_LICENSE.getValue()));
        this.synImg(contractEntity.getId());

        String signingzHtml = "";
        String signinyyzzHtml = "";

        for (SysFileEntity f : idCards) {
            signingzHtml += "<img src=\"" + f.getPath() + "\" width=\"300\" height=\"200\" />";
        }
        for (SysFileEntity f : businessLicenses) {
            signinyyzzHtml += "<img src=\"" + f.getPath() + "\" width=\"300\" height=\"200\" />";
        }


        String isNeedInvoicHtml = "□需要发票 &nbsp; &nbsp;□不需要发票";
        htmlStr = htmlStr.replace("!isNeedInvoicHtml!", isNeedInvoicHtml);

        htmlStr = htmlStr.replace("!rooms!", roomBuilder.toString());
        htmlStr = htmlStr.replace("!roomfeelist!", roomRentBuilder.toString());
        htmlStr = htmlStr.replace("!roomlist!", roomListBuilder.toString());
        htmlStr = htmlStr.replace("!signingzHtml!",signingzHtml);
        htmlStr = htmlStr.replace("!signinyyzzHtml!",signinyyzzHtml);
        htmlStr = htmlStr.replace("!ownerSignName!", "");
        return htmlStr;
    }

    /**
     * 签约时,生成pdf的共有字段 返回
     * @param data
     * @return
     */
    private Map<String,Object> getCommonData(Map<String, Object> data) {
        Map<String, Object> map = Maps.newHashMap();

        ContContractEntity contractEntity = (ContContractEntity) data.get("contractEntity"); // 合同
        // 合同信息
        ContContractInfoEntity contractInfoEntity = (ContContractInfoEntity) data.get("contractInfoEntity"); // 合同信息
        Boolean single = true;
        if(BaseConstants.BOOLEAN_OF_TRUE.equals(contractEntity.getIsOrganize())){
            single = false;
        }

        // 租凭起日,年月日
        map.put("sYear", String.format("%tY", contractEntity.getStartDate()));
        map.put("sMonth", String.format("%tm", contractEntity.getStartDate()));
        map.put("sDay", String.format("%td", contractEntity.getStartDate()));
        // 租凭止日,年月日
        map.put("eYear", String.format("%tY", contractEntity.getEndDate()));
        map.put("eMonth", String.format("%tm", contractEntity.getEndDate()));
        map.put("eDay", String.format("%td", contractEntity.getEndDate()));
        map.put("term", getMonthMargin(contractEntity.getStartDate(), contractEntity.getEndDate())); // 期限
        map.put("year", getYearMargin(contractEntity.getStartDate(),contractEntity.getEndDate()));//年
        map.put("renter", contractInfoEntity);
        if(!single){
            // 机构
            BaseEnterpriseEntity enterpriseEntity = enterpriseService.getOne(new QueryWrapper<BaseEnterpriseEntity>().eq("renter_id", contractEntity.getSignerId()));
            map.put("org", enterpriseEntity);  // 机构信息
        }
        // 签约时间
        map.put("signDate", DateTimeUtil.formatDateTimetoString(contractEntity.getSignDate(),DateTimeUtil.FMT_yyyyMMdd));
        // 合同信息
        map.put("contract", contractEntity);
        String payType = contractEntity.getPayType();
        map.put("payTypeStr",PayTypeEnum.getNameByValue(payType));
        String paytype = "";
        if(PayTypeEnum.ONE_ONE.getValue().equals(payType) || PayTypeEnum.TWO_ONE.getValue().equals(payType)){
            paytype = "每月支付一次";
        }
        if(PayTypeEnum.ONE_TWO.getValue().equals(payType)){
            paytype = "每2个月支付一次";
        }
        if(PayTypeEnum.ONE_THREE.getValue().equals(payType)|| PayTypeEnum.TWO_THREE.getValue().equals(payType)){
            paytype = "每3个月支付一次";
        }
        if(PayTypeEnum.ONE_SIX.getValue().equals(payType)){
            paytype = "每6个月支付一次";
        }
        if(PayTypeEnum.ONE_TWELVE.getValue().equals(payType)){
            paytype = "每12个月支付一次";
        }
        map.put("paytype",paytype);

        return map;
    }


    @Override
    public ContractCountVo countContractNum() {
        DataScope dataScope = DataScope.newDataScope(UoneSysUser.id());
        dataScope.setProAlias("s");
        dataScope.setParAlias("s");
        return baseMapper.countContractNum(dataScope);
    }

    @Override
    public ContractCountVo countContractNum(Map<String,String> map) {
        DataScope dataScope = DataScope.newDataScope(UoneSysUser.id());
        dataScope.setProAlias("ts");
        dataScope.setParAlias("ts");
        map.put("manager",UoneSysUser.id());
        ContractCountVo vo = baseMapper.getStatistics(dataScope,map);
        double avgRent = baseMapper.getAvgRent((String)map.get("projectId"));
        Integer recordNum = baseMapper.getRecordNum((String)map.get("projectId"));
        vo.setAvgRent(avgRent);
        vo.setRecordNum(recordNum);
        return vo ;
    }

    @Override
    public List<String> getInReviewContByHomePage(String projectId) {
        return baseMapper.inReviewContByHomePage(projectId);
    }

    @Override
    public List<String> getExpireByHomePage(String projectId) {
        return baseMapper.getExpireByHomePage(projectId);
    }

    // 查看合同
    @Override
    public ContContractVo selectContractById(String id) throws Exception {
        // 参数判断;
        if (StringUtils.isBlank(id)) {
            throw new Exception("参数异常");
        }
        ContContractVo contractVo = baseMapper.selectContractById(id);
        //商业押金展示不计算
        if (!ContractTypeEnum.BUSINESS.getValue().equals(contractVo.getContractType())) {
            if (PayTypeEnum.TWO_THREE.getValue().equals(contractVo.getPayType()) || PayTypeEnum.TWO_ONE.getValue().equals(contractVo.getPayType())) {
                contractVo.setCashPledge(SafeCompute.div(contractVo.getCashPledge(), new BigDecimal(2)));
            }
        }
        List<SysFileEntity> contractPDFs = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.CONTRACT); // 自动生成的合同pdf
        List<SysFileEntity> subContractPDFs = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.SUB_CONTRACT); // 手动上传的合同pdf
        if (ObjectUtil.isNotNull(subContractPDFs)) {
            contractPDFs.addAll(subContractPDFs);
        }
        contractVo.setPdfFiles(contractPDFs);

        ContRentLadderEntity ladder = rentLadderService.getRentLadderByContSourceId(contractVo.getContractSourceId());
        contractVo.setPrice(ladder.getPrice());

        // 查询租金信息
        contractVo.setRentLadderVoList(rentLadderService.selectList(contractVo.getContractSourceId()));

        List<SysFileEntity> subjoinPDFs = sysFileService.getListByFromIdAndType(contractVo.getId(), SysFileTypeEnum.SUBJOIN_CONTRACT);
        List<SysFileEntity> earlyPDFs = sysFileService.getListByFromIdAndType(contractVo.getId(), SysFileTypeEnum.EARLY_AGREEMENT);
        if (ObjectUtil.isNotNull(earlyPDFs)) {
            subjoinPDFs.addAll(earlyPDFs);
        }

        contractVo.setSubjoinPDFs(subjoinPDFs);

        // 查询身份证图片
        List<SysFileEntity> idCardImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.ID_CARD.getValue()));
        contractVo.setIdCardImgs(idCardImgs);
        // 查询婚姻证件图片
        List<SysFileEntity> marriageImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.MARRIAGE.getValue()));
        contractVo.setIdCardImgs(idCardImgs);
        // 查询行驶证图片
        List<SysFileEntity> vehicleLicenseImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.VEHICLE_LICENSE.getValue()));
        contractVo.setVehicleLicenseImgs(vehicleLicenseImgs);
        // 查询驾驶证图片
        List<SysFileEntity> drivingLicenceImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.DRIVING_LICENCE.getValue()));
        contractVo.setDrivingLicenceImgs(drivingLicenceImgs);
        // 查询营业执照(商业)
        List<SysFileEntity> businessLicenseImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.BUSINESS_LICENSE.getValue()));
        contractVo.setBusinessLicenceImgs(businessLicenseImgs);
        // 查询暂住证图片(公寓)
        List<SysFileEntity> temporaryResidenceImgs = sysFileService.getFiles(new SysFileEntity().setFromId(id).setType(SysFileTypeEnum.TEMPORARY_RESIDENCE.getValue()));
        contractVo.setTemporaryResidenceImgs(temporaryResidenceImgs);

        // 查询签约人信息
        RenterEntity renterEntity = renterFegin.getById(contractVo.getSignerId());
        contractVo.setRenterEntity(renterEntity);

        ContContractInfoEntity contractInfo = contractInfoService.getByContractId(id);
        contractVo.setContContractInfoEntity(contractInfo);
        // 查询机构信息
        if (BaseConstants.BOOLEAN_OF_TRUE.equals(contractVo.getIsOrganize())) {
            BaseEnterpriseEntity enterpriseEntity = enterpriseService.getOne(new QueryWrapper<BaseEnterpriseEntity>().eq("renter_id", contractVo.getSignerId()));
            contractVo.setEnterpriseEntity(enterpriseEntity);
        }

        //车位合同获取车位信息
        if (ContractTypeEnum.CARPORT.getValue().equals(contractVo.getContractType())) {
            // 查询车辆信息
            BaseCarEntity carEntity = baseCarService.getByContractSourceId(contractVo.getContractSourceId());
            contractVo.setCarEntity(carEntity);
        }
        if (contractVo == null) {
            throw new Exception("查看合同失败");
        }

        return contractVo;
    }

    /**
     * @param vo
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContContractEntity signCar(SignContractVo vo) throws Exception {
        //获取租客信息
        String rentId = vo.getRenterId();
        RenterEntity renter = renterFegin.getById(rentId);
        ResSourceVo source = sourceService.getInfoById(vo.getSourceId());
        ContTempEntity temp = contTempService.matcherContractTemplet(source, renter, "sign");
        if (ObjectUtil.isNull(temp)) {
            throw new BusinessException("未关联合同模板");
        }
        List<ContTempletCostRelEntity> templetCostRels = contTempletCostRelService.queryListByTempletId(temp.getId());
        if (templetCostRels.size() != 1) {
            throw new Exception("该项目对应的车位合同模板未正确配置费用配置！");
        }
        if(StrUtil.isNotBlank(vo.getContractId())){
            ContContractSourceRelEntity oldRel = contractSourceRelService.getByContractIdAndSourceId(vo.getContractId(), source.getId());
            QueryWrapper<BaseCarEntity> delete = new QueryWrapper<>();
            delete.eq("contract_source_id",oldRel.getId());
            baseCarService.remove(delete);
        }
        //1.生成预定信息
        SaleDemandEntity demand = new SaleDemandEntity();
        demand.setSourceId(source.getId());
        demand.setUserId(renter.getId());
        demand.setPayType(vo.getPayType());
        demand.setCostId(templetCostRels.get(0).getCostId());
        demand.insert();
        //2.生成合同
        deleteContract(vo.getContractId());
        ContContractEntity contract = generateContract(renter, source, null, vo.getStartDate(), DateUtil.endOfDay(vo.getEndDate()),vo.getContractId());
        ContContractSourceRelEntity rel = contractSourceRelService.getByContractIdAndSourceId(contract.getId(), source.getId());
        //3.保存车位信息
        BaseCarEntity car = new BaseCarEntity();
        car.setContractSourceId(rel.getId()).setNum(vo.getNum()).setOwner(vo.getOwner()).setUser(vo.getUser());
        baseCarService.save(car);
        //4.保存
        contract.setIsOrganize("0");
        contract.setRemark(vo.getSummary());
        contract.setState(ContractStateEnum.STATUS_SIGNING.getValue());
        contract.setSignDate(new Date());
        saveOrUpdate(contract);
        //5.修改房源状态
        source.setState(SourceStateEnum.BOOKED.getValue()).updateById();
        return contract;
    }

    @Override
    public void deleteContract(String contractId) {
        if (StrUtil.isNotBlank(contractId)) {
            ContContractEntity contract = getById(contractId);
            //删除合同信息表
            contractInfoService.remove(new QueryWrapper<ContContractInfoEntity>().eq("contract_id", contractId));
            //删除合同房源关系表阶梯
            QueryWrapper<ContContractSourceRelEntity> query = new QueryWrapper<>();
            query.eq("contract_id", contract.getId());
            ContContractSourceRelEntity rel = contractSourceRelService.getOne(query);
            if (ObjectUtil.isNotNull(rel)) {
                //是否有阶梯表,一起删除了
                QueryWrapper<ContRentLadderEntity> delete = new QueryWrapper<>();
                delete.eq("contract_source_id", rel.getId());
                contRentLadderService.remove(delete);
                contractSourceRelService.removeById(rel.getId());

                QueryWrapper<BaseCarEntity> deleteCar = new QueryWrapper<>();
                deleteCar.eq("contract_source_id", rel.getId());
                baseCarService.remove(deleteCar);
            }
        }
    }

    @Override
    public ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate,String contractId,AlterPriceVo alterPriceVo) throws Exception {
        //获取模板
        //因为车位租客小程序签约时就生成合同了，所以只能先沿用原来的tempId为搜索的
        ContTempEntity temp = new ContTempEntity();
        if (StrUtil.isEmpty(tempId)) {
            temp = contTempService.matcherContractTemplet(source, renter, "sign");
        } else {
            temp = contTempService.getById(tempId);
        }
        if (ObjectUtil.isNull(temp)) {
            throw new BusinessException("未关联合同模板！");
        }
        //获取预定信息
        SaleDemandEntity demand = saleCustomerService.getDemandBySourceIdAndUserId(source.getId(), renter.getId());
        if (ObjectUtil.isNull(demand)) {
            throw new BusinessException("未找到预定信息！");
        }


        startDate = ObjectUtil.isNotNull(startDate) ? startDate : demand.getCheckInTime();
        endDate = ObjectUtil.isNotNull(endDate) ? endDate : demand.getCheckOutTime();

        QueryWrapper<DemoContractEntity> wrapper = new QueryWrapper<>();
        DemoContractEntity demoContractEntity = demoContractService.getOne(wrapper.eq("source_id",source.getId()).last("limit 0,1"));
        if(null!=demoContractEntity&&DateUtil.beginOfDay(demoContractEntity.getEndTime()).getTime()<DateUtil.beginOfDay(endDate).getTime()){
            throw new BusinessException("合同截止日不能大于主合同截止日");
        }

        ContContractEntity contract = new ContContractEntity();
        String contractCode = resProjectService.getContractCodeByProjectId(source.getProjectId());
        contract.setContractCode(contractCode);
        contract.setContractTempletId(temp.getId());
        contract.setSignerId(renter.getId());
        contract.setPayType(demand.getPayType());
        contract.setState(ContractStateEnum.STATUS_SIGNING.getValue());
        contract.setContractType(ContractTypeEnum.getEnumBySourceType(source.getSourceType()).getValue());
        contract.setSignType(SignTypeEnum.ON_LINE.getValue());
        contract.setStartDate(DateUtil.beginOfDay(startDate));
        contract.setEndDate(DateUtil.endOfDay(endDate));
        contract.setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE);
        contract.setCostConfigureId(demand.getCostId());
        contract.setPlatform(PlatformEnum.YW.getValue());
        BigDecimal price=null;
        BigDecimal cashPledge=null;
        if(ObjectUtil.isNotNull(alterPriceVo)){
            price=alterPriceVo.getPrice();
            cashPledge=alterPriceVo.getDeposit();
            contract.setTotalPrice(price);
        }
//        if(StrUtil.isNotBlank(UoneSysUser.id())&&LoginType.USER.equals(UoneSysUser.loginType())){
        if(StrUtil.isNotBlank(UoneSysUser.id())){
            contract.setManager(UoneSysUser.id());
        }
        if(StrUtil.isNotBlank(contractId)){
            contract.setId(contractId);
            //删除记录
            this.deleteContract(contractId);
        }
        if (CustomerTypeEnum.ENTERPRISE.getValue().equals(temp.getCustomerType())) {
            contract.setIsOrganize(BaseConstants.BOOLEAN_OF_TRUE);
        } else {
            contract.setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE);
        }

        saveOrUpdate(contract);
        //2.保存合同房源关系表阶梯
        contractSourceRelService.addContContractSourceRel(contract, source, price, cashPledge, temp.getSubsidyPrice(), null);
        //3.保存合同信息
        ContContractInfoEntity contractInfo = contractInfoService.handleContractInfo(new ContContractInfoEntity(), contract.getId(), demand, renter, temp, source.getProjectId());
        contractInfoService.save(contractInfo);
        //4.保存客户合同关系表
        if (StrUtil.isNotBlank(demand.getCustomerId())) {
            saleCustomerService.reserve(demand.getCustomerId(), contract.getId(), source.getHouseName());
        }
        return contract;
    }

    @Override
    public ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate,Date freeStartDate ,Date freeEndDate,String contractId,AlterPriceVo alterPriceVo) throws Exception {
        //获取模板
        //因为车位租客小程序签约时就生成合同了，所以只能先沿用原来的tempId为搜索的
        ContTempEntity temp = new ContTempEntity();
        if (StrUtil.isEmpty(tempId)) {
            temp = contTempService.matcherContractTemplet(source, renter, "sign");
        } else {
            temp = contTempService.getById(tempId);
        }
        if (ObjectUtil.isNull(temp)) {
            throw new BusinessException("未关联合同模板！");
        }
        //获取预定信息
        SaleDemandEntity demand = saleCustomerService.getDemandBySourceIdAndUserId(source.getId(), renter.getId());
        if (ObjectUtil.isNull(demand)) {
            throw new BusinessException("未找到预定信息！");
        }


        startDate = ObjectUtil.isNotNull(startDate) ? startDate : demand.getCheckInTime();
        endDate = ObjectUtil.isNotNull(endDate) ? endDate : demand.getCheckOutTime();

        QueryWrapper<DemoContractEntity> wrapper = new QueryWrapper<>();
        DemoContractEntity demoContractEntity = demoContractService.getOne(wrapper.eq("source_id",source.getId()).last("limit 0,1"));
        if(null!=demoContractEntity&&DateUtil.beginOfDay(demoContractEntity.getEndTime()).getTime()<DateUtil.beginOfDay(endDate).getTime()){
            throw new BusinessException("合同截止日不能大于主合同截止日");
        }

        ContContractEntity contract = new ContContractEntity();
        String contractCode = resProjectService.getContractCodeByProjectId(source.getProjectId());
        String paperCode = generatePaperCode(source.getProjectId());
        contract.setContractCode(contractCode);
        contract.setPaperCode(paperCode);
        contract.setContractTempletId(temp.getId());
        contract.setSignerId(renter.getId());
        contract.setPayType(demand.getPayType());
        contract.setState(ContractStateEnum.STATUS_SIGNING.getValue());
        contract.setContractType(ContractTypeEnum.getEnumBySourceType(source.getSourceType()).getValue());
        contract.setSignType(SignTypeEnum.ON_LINE.getValue());
        contract.setStartDate(DateUtil.beginOfDay(startDate));
        contract.setEndDate(DateUtil.endOfDay(endDate));
        contract.setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE);
        contract.setCostConfigureId(demand.getCostId());
        contract.setPlatform(PlatformEnum.YW.getValue());
        contract.setFreeStartDate(freeStartDate);
        contract.setFreeEndDate(freeEndDate);
        BigDecimal price=null;
        BigDecimal cashPledge=null;
        if(ObjectUtil.isNotNull(alterPriceVo)){
            price=alterPriceVo.getPrice();
            cashPledge=alterPriceVo.getDeposit();
            contract.setTotalPrice(price);
        }
//        if(StrUtil.isNotBlank(UoneSysUser.id())&&LoginType.USER.equals(UoneSysUser.loginType())){
        if(StrUtil.isNotBlank(UoneSysUser.id())){
            contract.setManager(UoneSysUser.id());
        }
        if(StrUtil.isNotBlank(contractId)){
            contract.setId(contractId);
            //删除记录
            this.deleteContract(contractId);
        }
        if (CustomerTypeEnum.ENTERPRISE.getValue().equals(temp.getCustomerType())) {
            contract.setIsOrganize(BaseConstants.BOOLEAN_OF_TRUE);
        } else {
            contract.setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE);
        }

        saveOrUpdate(contract);
        //2.保存合同房源关系表阶梯
        contractSourceRelService.addContContractSourceRel(contract, source, price, cashPledge, temp.getSubsidyPrice(), null);
        //3.保存合同信息
        ContContractInfoEntity contractInfo = contractInfoService.handleContractInfo(new ContContractInfoEntity(), contract.getId(), demand, renter, temp, source.getProjectId());
        contractInfoService.save(contractInfo);
        //4.保存客户合同关系表
        if (StrUtil.isNotBlank(demand.getCustomerId())) {
            saleCustomerService.reserve(demand.getCustomerId(), contract.getId(), source.getHouseName());
        }
        return contract;
    }

    @Override
    public ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate) throws Exception {
        return this.generateContract(renter,source, tempId, startDate, endDate,null,null);
    }

    @Override
    public ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate, String contractId) throws Exception {
        return this.generateContract(renter,source, tempId, startDate, endDate,contractId,null);
    }

    /**
     * 获取数据
     *
     * @param contractId 合同id
     * @return
     * @throws Exception
     */
    private Map<String, Object> getData(String contractId) throws Exception {
        Map<String, Object> data = Maps.newHashMap();
        // 合同
        ContContractEntity contractEntity = baseMapper.selectById(contractId);
        Boolean single = true;
        if(BaseConstants.BOOLEAN_OF_TRUE.equals(contractEntity.getIsOrganize())){
            single = false;
        }
        // 合同信息
        ContContractInfoEntity contractInfoEntity = contractInfoService.getOne(new QueryWrapper<ContContractInfoEntity>().eq("contract_id", contractEntity.getId()));

        //签约人信息
        RenterEntity renter = renterFegin.getById(contractEntity.getSignerId());
        // 合同房源关系
        List<ContContractSourceRelBo> contractSourceRelBoList = contractSourceRelService.selectByContractId(contractId);

        // 合同模板
        ContTempEntity tempEntity = contTempService.getOne(new QueryWrapper<ContTempEntity>().eq("id", contractEntity.getContractTempletId()));

        // 费用配置
        List<ResCostConfigureVo> costConfigureVoList = costConfigureService.queryCostAll(new HashMap() {{
            put("id", contractEntity.getCostConfigureId());
        }});

        // 机构
        BaseEnterpriseEntity enterpriseEntity = enterpriseService.getOne(new QueryWrapper<BaseEnterpriseEntity>().eq("renter_id", contractEntity.getSignerId()));
        // 项目
        ResProjectEntity projectEntity = resProjectService.getById(contractSourceRelBoList.get(0).getProjectId());

        if (null == contractEntity) {
            throw new Exception("获取合同异常");
        }
        if (null == contractInfoEntity) {
            throw new Exception("获取合同信息异常");
        }
        if (null == contractSourceRelBoList || contractSourceRelBoList.size() < 1) {
            throw new Exception("获取合同房源关系异常");
        }
        if (null == tempEntity) {
            throw new Exception("获取合同模板异常");
        }
        if (null == costConfigureVoList || costConfigureVoList.size() < 1) {
            throw new Exception("获取费用配置异常");
        }
        if (null == enterpriseEntity && !single) {
            throw new Exception("获取机构信息异常");
        }
        if (null == projectEntity) {
            throw new Exception("获取项目异常");
        }

        data.put("contractTempletEntity", tempEntity);
        data.put("contractEntity", contractEntity);
        data.put("contractInfoEntity", contractInfoEntity);
        data.put("renterEntity", renter);
        data.put("contractSourceRelBoList", contractSourceRelBoList);
        data.put("enterpriseEntity", enterpriseEntity);
        data.put("costConfigureVoList", costConfigureVoList);
        data.put("projectEntity", projectEntity);

        String provinceId = projectEntity.getProvinceId();
        String cityId = projectEntity.getCityId();
        String districtId = projectEntity.getDistrictId();
        SysAreaEntity province = sysAreaService.getById(provinceId);
        SysAreaEntity city = sysAreaService.getById(cityId);
        SysAreaEntity distric = sysAreaService.getById(districtId);
        data.put("province",province.getAreaName());
        data.put("city",city.getAreaName());
        data.put("distric",distric.getAreaName());

        return data;
    }

    /**
     *  3号签约的,  次月2号就能算一个月  与DateUtil.betweenMonth 有所区别!!
     *              不足一个月,则月份为 0
     * 比较两个日期相差的月数
     */
    private long getMonthMargin(Date start, Date ent){
        Calendar c = Calendar.getInstance();
        c.setTime(ent);
        c.add(Calendar.DAY_OF_MONTH, 1);// 结束日期 + 1天
        return DateUtil.betweenMonth(start, c.getTime(), false);
    }

    /**
     *  3号签约的,  来年2号就能算一年  与DateUtil.betweenYear 有所区别!!
     *              不足一年,则为 0
     * 比较两个日期相差的年数
     */
    private long getYearMargin(Date start, Date ent){
        Calendar c = Calendar.getInstance();
        c.setTime(ent);
        c.add(Calendar.DAY_OF_MONTH, 1);// 结束日期 + 1天
        return DateUtil.betweenYear(start, c.getTime(), false);
    }

    /**
     * 获取三位数
     */
    private int getNum(String param) {
        return baseMapper.getNum(param);
    }

    // 查询合同分页-XX公寓  非短租合同
    @Override
    public IPage<ContContractVo> selectPageByComun(Page page, String renterId,Map map) {
        return baseMapper.selectPageByComun(page, renterId,map);
    }

    @Override
    public IPage<ContContractVo> selectPageByIntention(Page page, String renterId,Map map) {
        return baseMapper.selectPageByIntention(page, renterId,map);
    }

    @Override
    public IPage<ContContractVo> getNameAndValue(Page page,String renterId) {
        return baseMapper.getNameAndValue(page,renterId);
    }

    // 查询合同分页-企业端
    @Override
    public IPage<ContContractEntity> selectPageByPass(Page page, ContContractVo contractEntity) throws Exception {

        IPage<ContContractEntity> iPage;

        try {
            iPage = baseMapper.selectPageByPass(page, contractEntity, UoneSysUser.id());
        } catch (Exception e) {
            log.error(e.getMessage());

            throw new Exception("查询合同分页-企业端异常");
        }

        return iPage;
    }

    @Override
    public ContContractEntity getContractBySourceId(String sourceId) {
        return baseMapper.getContractBySourceId(sourceId);
    }

    @Override
    public ContContractEntity getValidContractBySourceId(String sourceId) {
        return baseMapper.getValidContractBySourceId(sourceId);
    }

    @Override
    public ContContractEntity getUnValidContractBySourceId(String sourceId) {
        return baseMapper.getUnValidContractBySourceId(sourceId);
    }

    @Override
    public List<ContContractVo> getServicePhoneByRenterId(String renterId) {
        return baseMapper.getServicePhoneByRenterId(renterId);
    }

    /**
     * @return
     * <AUTHOR>
     * @date 2019-01-14 14:22
     * @Param:
     */
    @Override
    public IPage<Map<String, Object>> selectPageBySign(Map<String, Object> map, Page page) {
        DataScope scope = DataScope.newDataScope(UoneSysUser.id());
        scope.setProAlias("s");
        scope.setProjectFieldName("project_id");
        return baseMapper.selectPageBySign(page,scope, map);
    }

    @Override
    public Map<String, Object> utContractInfo(String contractId, String sourceId) {
        return baseMapper.utContractInfo(contractId, sourceId);
    }



    // 签约合同 pdf生成
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysFileEntity contractPDF(String contractId) throws Exception {
        // 数据准备
        Map<String, Object> data = getData(contractId);
        ContContractEntity contractEntity = (ContContractEntity) data.get("contractEntity"); // 合同
        contractEntity.setSignDate(DateUtil.date());
        data.put("contractEntity", contractEntity);
        Map<String, Object> map = getCommonData(data);

        Boolean single = true;
        if(BaseConstants.BOOLEAN_OF_TRUE.equals(contractEntity.getIsOrganize())){
            single = false;
        }
        String htmlStr = "";
        if(single){
            //个人合同
            if (ContractTypeEnum.APARTMENT.getValue().equals(contractEntity.getContractType())) {
                // 个人-公寓
                htmlStr = singleApartContractPDF(data, map);
            }
            if (ContractTypeEnum.BUSINESS.getValue().equals(contractEntity.getContractType())) {
                // 个人-商业
                htmlStr = singleBusinessContractPDF(data, map);
            }
            if (ContractTypeEnum.CARPORT.getValue().equals(contractEntity.getContractType())) {
                // 个人-车位
                htmlStr = singleCarContractPDF(data, map);
            }
        }else{
            //企业合同
            if (ContractTypeEnum.APARTMENT.getValue().equals(contractEntity.getContractType())) {
                // 企业-公寓
                htmlStr = orgApartContractPDF(data,map);
            }
            if (ContractTypeEnum.BUSINESS.getValue().equals(contractEntity.getContractType())) {
                // 企业-商业
                htmlStr = orgBusinessContractPDF(data, map);
            }
            if (ContractTypeEnum.CARPORT.getValue().equals(contractEntity.getContractType())) {
                // 企业-车位
                htmlStr = orgCarContractPDF(data, map);
            }
        }

        ResProjectEntity projectEntity = (ResProjectEntity) data.get("projectEntity"); // 项目
        //生成paperCode判断
        if (uoneTianDiId.equals(projectEntity.getId()) && ContractTypeEnum.APARTMENT.getValue().equals(contractEntity.getContractType())) { // 判断项目名称是否为UONE天地
            // 合同
            contractEntity = baseMapper.selectById(contractId);
            contractEntity.setContractCode(contractEntity.getPaperCode());
            map.put("contract", contractEntity);
        }

        String pdfName = contractEntity.getContractCode() + ".pdf"; // PDF名称
        SysFileEntity entity = null;
        // pdf生成
        try {

            //删除旧有合同
            SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", contractId).eq("type", SysFileTypeEnum.CONTRACT.getValue()));
            if (null != sysFileEntity) {
                //FileUtil.delete(sysFileEntity.getUrl());
                minioUtil.delete(sysFileEntity.getUrl());
                sysFileEntity.deleteById();
            }
            //获取项目水印地址
            ResProjectParaEntity projectWatermark = resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.PDF_WATERMARK.getValue(),projectEntity.getId());
            String watermarkImage = projectWatermark==null?"":projectWatermark.getParamValue();
            map.put("watermarkImage", watermarkImage);
            map.put("startPage",2);
            String url = pdfUtil.pdf(htmlStr, map, pdfName);
            if(StrUtil.isBlank(url)){
                throw new Exception("pdf生成异常");
            }
            System.out.println("http://192.168.0.108:8080/"+url);
            entity = new SysFileEntity();
            entity.setFromId(contractEntity.getId())
                    .setName(pdfName)
                    .setType(SysFileTypeEnum.CONTRACT.getValue())
                    .setUrl(url)
                    .insert();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("pdf生成异常");
        }
        return entity;
    }

    // 个人-公寓
    private String singleApartContractPDF(Map<String, Object> data, Map<String, Object> map) throws Exception {
        ContContractTempletEntity contractTempletEntity = (ContContractTempletEntity) data.get("contractTempletEntity"); // 合同模板
        ContContractEntity contractEntity = (ContContractEntity) data.get("contractEntity"); // 合同
        ContContractInfoEntity contractInfoEntity = (ContContractInfoEntity) data.get("contractInfoEntity"); // 合同信息
        List<ContContractSourceRelBo> contractSourceRelBoList = (List<ContContractSourceRelBo>) data.get("contractSourceRelBoList"); // 合同房源关系
        ContContractSourceRelBo contractSourceRelBo = contractSourceRelBoList.get(0);
        List<ResCostConfigureVo> costConfigureVoList = (List<ResCostConfigureVo>) data.get("costConfigureVoList"); // 费用配置信息
        ResProjectEntity projectEntity = (ResProjectEntity) data.get("projectEntity"); // 项目

        //生成paperCode判断
        if (uoneTianDiId.equals(projectEntity.getId())) { // 判断项目名称是否为UONE天地，是则强制修改纸质合同号
            String preCode = "雅园*公寓" + contractEntity.getContractCode().substring(9, 15);
            String maxPaperCode = this.getMaxPaperCode(preCode);
            if(StrUtil.isBlank(maxPaperCode)){
                maxPaperCode = "000";
            }
            String code = CodeUtil.formatString(maxPaperCode);
            String paperCode = preCode + code;
            contractEntity.setPaperCode(paperCode);
            baseMapper.updateById(contractEntity);
        }
        String houseTypeImg = null;
        List<SysFileEntity> houseTypeImgList = sysFileService.getListByFromIdAndType(contractSourceRelBo.getHouseTypeId(), null);
        if (!houseTypeImgList.isEmpty()) {
            houseTypeImg = houseTypeImgList.get(0).getPath();
        }
        String ownerSignImg = null;
        String signImg = null;
        List<SysFileEntity> ownerSignImgList = sysFileService.getListByFromIdAndType(contractEntity.getId(), SysFileTypeEnum.CHECKIN_CUSTOMER_SIGN);
        if (!ownerSignImgList.isEmpty()) {
            ownerSignImg = ownerSignImgList.get(0).getPath();
        }
        List<SysFileEntity> signImgList = sysFileService.getListByFromIdAndType(contractEntity.getId(), SysFileTypeEnum.CHECKIN_MANAGER_SIGN);
        if (!signImgList.isEmpty()) {
            signImg = signImgList.get(0).getPath();
        }

        // 付款周期
        String payMode = "";

        if (PayTypeEnum.ONE_ONE.getValue().equals(contractEntity.getPayType()) || PayTypeEnum.TWO_ONE.getValue().equals(contractEntity.getPayType())) {
            payMode = "<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                    + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                    + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>"
                    + "每月&nbsp; &nbsp;□每季&nbsp; &nbsp;□半年&nbsp; &nbsp;□一年";
        }
        if (PayTypeEnum.ONE_THREE.getValue().equals(contractEntity.getPayType()) || PayTypeEnum.TWO_THREE.getValue().equals(contractEntity.getPayType())) {
            payMode = "□每月&nbsp; &nbsp;"
                    + "<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                    + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                    + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>"
                    + "每季&nbsp; &nbsp;□半年&nbsp; &nbsp;□一年";
        }
        if (PayTypeEnum.ONE_SIX.getValue().equals(contractEntity.getPayType())) {
            payMode = "□每月&nbsp; &nbsp;□每季&nbsp; &nbsp;"
                    + "<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                    + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                    + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>"
                    + "半年&nbsp; &nbsp;□一年";
        }
        if (PayTypeEnum.ONE_TWELVE.getValue().equals(contractEntity.getPayType())) {
            payMode = "□每月&nbsp; &nbsp;□每季&nbsp; &nbsp;□半年&nbsp; &nbsp;"
                    + "<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                    + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                    + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>"
                    + "一年";
        }
        BigDecimal propertyFee = BigDecimal.ZERO;//物业服务费
        BigDecimal maintenanceCharge = BigDecimal.ZERO;//公维金
        BigDecimal networkCharge = BigDecimal.ZERO;//网络费
        BigDecimal publicWaterCharge = BigDecimal.ZERO;//公摊水费
        BigDecimal publicElectricCharge = BigDecimal.ZERO;//公摊电费
        BigDecimal waterCharge = BigDecimal.ZERO;//水费
        BigDecimal electricCharge = BigDecimal.ZERO;//电费
        BigDecimal garbageCharge = BigDecimal.ZERO;//垃圾转运费,清理费
        BigDecimal specMarCharge = BigDecimal.ZERO;//特殊履约保证金
        BigDecimal specSerCharge = BigDecimal.ZERO;//特殊管理费


        BigDecimal totalProperty = BigDecimal.ZERO;//物业服务费总额( 计算按房间,按面积)
        BigDecimal totalMaintenance =  BigDecimal.ZERO;//公维金总额( 计算按房间,按面积)
        BigDecimal totalNetwork = BigDecimal.ZERO;//网络费总额( 计算按房间,按面积)
        BigDecimal totalPublicWater = BigDecimal.ZERO;//公摊水费总额( 计算按房间,按面积)
        BigDecimal totalPublicElectric = BigDecimal.ZERO;//公摊电费总额( 计算按房间,按面积)
        BigDecimal totalGarbage = BigDecimal.ZERO;//垃圾清理费( 计算按房间,按面积)
        BigDecimal totalSpecMar = BigDecimal.ZERO;//特殊履约保证金( 计算按房间,按面积)
        BigDecimal totalSpecSer = BigDecimal.ZERO;//特殊管理费( 计算按房间,按面积)


        BigDecimal houseArea = contractSourceRelBo.getArea();

        try {
            // 物业费
            ResCostConfigureVo resCostConfigureVo = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.PROPERTY_BASEFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取公寓物业基本服务费
            ).collect(Collectors.toList()).get(0);
            propertyFee = resCostConfigureVo.getPrice();
            if("1".equals(resCostConfigureVo.getBillingCode())){
                //按面积
                totalProperty = SafeCompute.multiply(propertyFee,houseArea).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                //按房间
                totalProperty = propertyFee;
            }
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：物业基本费未配置");
        }

        try {
            // 垃圾清运费
            ResCostConfigureVo resCostConfigureVo = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.PROPERTY_GARBAGEFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取公寓物业基本服务费
            ).collect(Collectors.toList()).get(0);
            garbageCharge = resCostConfigureVo.getPrice();
            if("1".equals(resCostConfigureVo.getBillingCode())){
                //按面积
                totalGarbage = SafeCompute.multiply(garbageCharge,houseArea).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                //按房间
                totalGarbage = garbageCharge;
            }
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：垃圾清运费未配置");
        }

        try {
            // 公维金
            ResCostConfigureVo resCostConfigureVo = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.PROPERTY_PUBLICFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取公寓房屋公维金
            ).collect(Collectors.toList()).get(0);
            maintenanceCharge = resCostConfigureVo.getPrice();
            if("1".equals(resCostConfigureVo.getBillingCode())){
                //按面积
                totalMaintenance = SafeCompute.multiply(maintenanceCharge,houseArea).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                //按房间
                totalMaintenance = maintenanceCharge;
            }
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：房屋公维金未配置");
        }
        try {
            // 宽带费
            ResCostConfigureVo resCostConfigureVo = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.NETWORK_FEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取网络费
            ).collect(Collectors.toList()).get(0);
            networkCharge = resCostConfigureVo.getPrice();
            if("1".equals(resCostConfigureVo.getBillingCode())){
                //按面积
                totalNetwork = SafeCompute.multiply(networkCharge,houseArea).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                //按房间
                totalNetwork = networkCharge;
            }
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：网络费未配置");
        }
        try {
            // 公摊水费
            ResCostConfigureVo resCostConfigureVo = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.PROPERTY_WATERFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取公寓房屋公摊水费
            ).collect(Collectors.toList()).get(0);
            publicWaterCharge = resCostConfigureVo.getPrice();
            if("1".equals(resCostConfigureVo.getBillingCode())){
                //按面积
                totalPublicWater = SafeCompute.multiply(publicWaterCharge,houseArea).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                //按房间
                totalPublicWater = publicWaterCharge;
            }
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：房屋公摊水费未配置");
        }
        try {
            // 公摊电费
            ResCostConfigureVo resCostConfigureVo = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.PROPERTY_POWERFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取公寓房屋公摊电费
            ).collect(Collectors.toList()).get(0);
            publicElectricCharge = resCostConfigureVo.getPrice();
            if("1".equals(resCostConfigureVo.getBillingCode())){
                //按面积
                totalPublicElectric = SafeCompute.multiply(publicElectricCharge,houseArea).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                //按房间
                totalPublicElectric = publicElectricCharge;
            }
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：房屋公摊电费未配置");
        }
        try {
            // 特殊履约保证金
            ResCostConfigureVo resCostConfigureVo = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.SEPECIAL_MARGINFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取公寓房屋公摊电费
            ).collect(Collectors.toList()).get(0);
            specMarCharge = resCostConfigureVo.getPrice();
            if("1".equals(resCostConfigureVo.getBillingCode())){
                //按面积
                totalSpecMar = SafeCompute.multiply(specMarCharge,houseArea).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                //按房间
                totalSpecMar = specMarCharge;
            }
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：特殊履约保证金未配置");
        }
        try {
            // 特殊管理费
            ResCostConfigureVo resCostConfigureVo = costConfigureVoList.stream().filter(costConfigureVo ->
                    PriceTypeEnum.SEPECIAL_SERVICEFEE.getValue().equals(costConfigureVo.getCostType()) &&
                            SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取公寓房屋公摊电费
            ).collect(Collectors.toList()).get(0);
            specSerCharge = resCostConfigureVo.getPrice();
            if("1".equals(resCostConfigureVo.getBillingCode())){
                //按面积
                totalSpecSer = SafeCompute.multiply(specSerCharge,houseArea).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else {
                //按房间
                totalSpecSer = specSerCharge;
            }
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：特殊管理费未配置");
        }

        //楼层号
        Integer flo = contractSourceRelBoList.get(0).getFloor();
        try {
            // 水费
            List<ResCostConfigureVo> waterFeeList;
            if(flo != null){
                BigDecimal floor = new BigDecimal(flo.intValue());
                waterFeeList = costConfigureVoList.stream().filter(costConfigureVo ->
                                PriceTypeEnum.WATER.getValue().equals(costConfigureVo.getCostType()) &&
                                        SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) && costConfigureVo.getLowerLimit().compareTo(floor)<=0
                                        && (costConfigureVo.getTopLimit() == null || costConfigureVo.getTopLimit().compareTo(floor)>=0)
                        // 获取水费
                ).collect(Collectors.toList());
                if(waterFeeList.size() > 1){
                    waterFeeList = waterFeeList.stream().filter(costConfigureVo ->
                            costConfigureVo.getTopLimit() != null
                    ).collect(Collectors.toList());
                }
            }else{
                waterFeeList = costConfigureVoList.stream().filter(costConfigureVo ->
                        PriceTypeEnum.WATER.getValue().equals(costConfigureVo.getCostType()) &&
                                SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取水费
                ).collect(Collectors.toList());
            }
            if(waterFeeList.size() > 1){
                throw new BusinessException("生成合同PDF异常：水费配置错误");
            }
            waterCharge = waterFeeList.get(0).getPrice();
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：水费配置错误");
        }

        try {
            // 电费
            List<ResCostConfigureVo> elecFeeList;
            if(flo != null){
                BigDecimal floor = new BigDecimal(flo.intValue());
                elecFeeList = costConfigureVoList.stream().filter(costConfigureVo ->
                                PriceTypeEnum.ELECTRIC.getValue().equals(costConfigureVo.getCostType()) &&
                                        SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) && costConfigureVo.getLowerLimit().compareTo(floor)<=0
                                        && (costConfigureVo.getTopLimit() == null || costConfigureVo.getTopLimit().compareTo(floor)>=0)
                        // 获取水费
                ).collect(Collectors.toList());
                if(elecFeeList.size() > 1){
                    elecFeeList = elecFeeList.stream().filter(costConfigureVo ->
                            costConfigureVo.getTopLimit() != null
                    ).collect(Collectors.toList());
                }
            }else{
                elecFeeList = costConfigureVoList.stream().filter(costConfigureVo ->
                        PriceTypeEnum.ELECTRIC.getValue().equals(costConfigureVo.getCostType()) &&
                                SourceTypeEnum.HOUSE.getValue().equals(costConfigureVo.getSourceType()) // 获取电费
                ).collect(Collectors.toList());
            }
            if(elecFeeList.size() > 1){
                throw new BusinessException("生成合同PDF异常：电费配置错误");
            }
            electricCharge = elecFeeList.get(0).getPrice();
        } catch (Exception e) {
            throw new BusinessException("生成合同PDF异常：电费配置错误");
        }


        // 发票
        String isNeedInvoicHtml = "□需要发票 &nbsp; &nbsp;<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>不需要发票";
        // 入住人表格
        String renterTable = "<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;\" width=\"520\"><tr class=\"firstRow\" style=\"background:rgb(242, 242, 242);\"><td style=\"padding:5px 7px;\">序号</td><td style=\"padding:5px 7px;\">姓名</td><td style=\"padding:5px 7px;\">身份证号码</td><td style=\"padding:5px 7px;\">电话号码</td></tr>"
                + "<tr><td style=\"padding:5px 7px;\">1</td><td style=\"padding:5px 7px;\">"
                + contractInfoEntity.getName() + "</td><td style=\"padding:5px 7px;\">"
                + contractInfoEntity.getIdNo() + "</td><td style=\"padding:5px 7px;\">"
                + contractInfoEntity.getTel() + "</td></tr></table>";
        // 设备清单
        StringBuilder deviceTable = new StringBuilder().append("<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;\" width=\"520\">" +
                "<tr class=\"firstRow\" style=\"background:rgb(242, 242, 242);\">" +
                "<td style=\"padding:5px 7px;\">区域</td><td style=\"padding:5px 7px;\">名称</td>" +
                "<td style=\"padding:5px 7px;\">数量</td><td colspan=\"2\" style=\"padding:5px 7px;\">价值</td></tr>");


        // 获取该房源设备信息
        List<ResSourceDeviceRelVo> sourceDeviceRelVoList = sourceDeviceRelService.getDevice(contractSourceRelBo.getSourceId(), null);


        for (ResSourceDeviceRelVo vo : sourceDeviceRelVoList) {
            String area = vo.getIsPublic() ? "公区" : "室内";
            deviceTable.append("<tr><td>" + area + "</td><td>")
                    .append(vo.getName()).append("</td><td>1</td><td>")
                    .append(vo.getDamage()).append("</td><td>元/件</td></tr>");
        }
        deviceTable.append("</table>");

        String htmlStr = contractTempletEntity.getContent();
        htmlStr = htmlStr.replace("!payMode!", payMode);
        htmlStr = htmlStr.replace("!isNeedInvoicHtml!", isNeedInvoicHtml);
        htmlStr = htmlStr.replace("!renterTable!", renterTable);
        htmlStr = htmlStr.replace("!deviceTable!", deviceTable.toString());

        BigDecimal originPrice = SafeCompute.add(contractSourceRelBo.getPrice(),contractInfoEntity.getSubsidySum());


        // 数据封装
        map.put("partitionName", contractSourceRelBo.getPartitionName()); // 楼栋
        map.put("code", contractSourceRelBo.getCode()); // 房间号
        map.put("area", contractSourceRelBo.getArea()); // 建筑面积
        map.put("houseTypeImg", houseTypeImg); // 房屋户型图
        map.put("ownerSignImg", ownerSignImg); // 管家签名
        map.put("signImg", signImg); // 租客签名
        map.put("cashPledge", contractSourceRelBo.getCashPledge()); // 押金
        map.put("price", contractSourceRelBo.getPrice()); // 租金
        map.put("originPrice",originPrice);
        map.put("payMode", payMode); // 付款周期
        map.put("propertyFee", propertyFee); // 物业费
        map.put("maintenanceCharge", maintenanceCharge); // 公维金
        map.put("networkCharge", networkCharge); // 宽带费
        map.put("publicWaterCharge", publicWaterCharge); // 公摊水费
        map.put("publicElectricCharge", publicElectricCharge); // 公摊电费
        map.put("waterCharge", waterCharge); // 水费
        map.put("electricCharge", electricCharge); // 电费
        map.put("totalProperty", totalProperty);
        map.put("totalMaintenance", totalMaintenance);
        map.put("totalGarbage", totalGarbage);
        map.put("totalNetwork", totalNetwork);
        map.put("totalPublicWater", totalPublicWater);
        map.put("totalPublicElectric", totalPublicElectric);
        map.put("totalSpecMar", totalSpecMar);
        map.put("totalSpecSer", totalSpecSer);
        return htmlStr;
    }

    // 个人-商业
    private String singleBusinessContractPDF(Map<String, Object> data, Map<String, Object> map) throws Exception {
        ContContractTempletEntity contractTempletEntity = (ContContractTempletEntity) data.get("contractTempletEntity"); // 合同模板
        ContContractEntity contractEntity = (ContContractEntity) data.get("contractEntity"); // 合同
        List<ContContractSourceRelBo> contractSourceRelBoList = (List<ContContractSourceRelBo>) data.get("contractSourceRelBoList"); // 合同房源关系
        ContContractSourceRelBo contractSourceRelBo = contractSourceRelBoList.get(0);
        ResProjectEntity projectEntity = (ResProjectEntity) data.get("projectEntity"); // 项目
        String province = (String) data.get("province");
        String city =  (String) data.get("city");
        String distric = (String)data.get("distric");

        // 房源列表
        String rooms = "<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%\"><tr><td style=\"width:50%\">房屋坐落</td><td>面积(平方米)</td></tr>" +
                "<tr><td>" + province + city + distric +  projectEntity.getAddress() + contractSourceRelBo.getPartitionName() + "楼" + contractSourceRelBo.getCode() + "室</td><td>" + contractSourceRelBo.getArea() + "</td></tr></table>";

        // 租金列表
        StringBuilder priceList = new StringBuilder().append("房源：").append(contractSourceRelBo.getSourceName()).append("\n")
                .append("<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%;text-align:center\"><tr><td>时间段</td><td>租金(元/月)</td></tr>");

        for (ContContractSourceRelBo bo : contractSourceRelBoList) {
            priceList.append("<tr><td>").append(DateUtil.format(bo.getStartDate(), "yyyy-MM-dd")).append("~").append(DateUtil.format(bo.getEndDate(), "yyyy-MM-dd")).append("</td><td>")
                    .append(bo.getPrice()).append("</td></tr>");
        }

        priceList.append("</table>").append("\n").append("合计：").append("\n")
                .append("<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%;text-align:center\"><tr><td>时间段</td><td>租金(元/月)</td></tr>");

        for (ContContractSourceRelBo bo : contractSourceRelBoList) {
            priceList.append("<tr><td>").append(DateUtil.format(bo.getStartDate(), "yyyy-MM-dd")).append("~").append(DateUtil.format(bo.getEndDate(), "yyyy-MM-dd")).append("</td><td>")
                    .append(bo.getPrice()).append("</td></tr>");
        }

        priceList.append("</table>").append("\n");

        String houseTypeImg = "";
        List<SysFileEntity> houseTypeImgList = sysFileService.getListByFromIdAndType(contractSourceRelBo.getHouseTypeId(), null);
        if (!houseTypeImgList.isEmpty()) {
            houseTypeImg = houseTypeImgList.get(0).getPath();
        }

        // 房屋平面图
        String roomImg = "<table border=\"1\" bordercolor=\"#000000\" style=\"border-collapse:collapse;width:100%\"><tr><td style=\"width:50%\">房屋坐落</td><td>面积(平方米)</td><td>户型图</td></tr>"
                + "<tr><td>" + projectEntity.getAddress() + contractSourceRelBo.getPartitionName() + "楼" + contractSourceRelBo.getCode() + "室</td><td>" + contractSourceRelBo.getArea()
                + "</td><td><img width=\"180\" height=\"120\" src=\""+houseTypeImg+"\" title=\"户型图\" /></td></tr></table>";

        // 营业执照与身份证图片
        List<SysFileEntity> idCards = sysFileService.getFiles(new SysFileEntity().setFromId(contractEntity.getId()).setType(SysFileTypeEnum.ID_CARD.getValue()));
        List<SysFileEntity> businessLicenses = sysFileService.getFiles(new SysFileEntity().setFromId(contractEntity.getId()).setType(SysFileTypeEnum.BUSINESS_LICENSE.getValue()));
        this.synImg(contractEntity.getId());


        String idCardHtml = "";
        String businessLicenseHtml = "";

        for (SysFileEntity file : idCards) {
            idCardHtml += "<img src=\"" + file.getPath() + "\" width=\"300\" height=\"200\" />";
        }
        for (SysFileEntity file : businessLicenses) {
            businessLicenseHtml += "<img src=\"" + file.getPath() + "\" width=\"300\" height=\"200\" />";
        }
        String htmlStr = contractTempletEntity.getContent();

        // 发票
        String isNeedInvoicHtml = "□需要发票 &nbsp; &nbsp;<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>不需要发票";

        //个人商业不需要签字,走法大大
        htmlStr = htmlStr.replace("!ownerSignName!", "");
        htmlStr = htmlStr.replace("!signName!", "");
        htmlStr = htmlStr.replace("!rooms!", rooms);
        htmlStr = htmlStr.replace("!roomfeelist!", priceList.toString());
        htmlStr = htmlStr.replace("!roomlist!", roomImg);
        htmlStr = htmlStr.replace("!signingzHtml!", "<div>" + idCardHtml + "</div>");
        htmlStr = htmlStr.replace("!signinyyzzHtml!", "<div>" + businessLicenseHtml + "</div>");
        htmlStr = htmlStr.replace("!isNeedInvoicHtml!", isNeedInvoicHtml);
        return htmlStr;
    }


    public String singleCarContractPDF(Map<String, Object> data, Map<String, Object> map) throws Exception {
        // 合同
        ContContractEntity contractEntity = (ContContractEntity) data.get("contractEntity");
        // 合同房源关系
        List<ContContractSourceRelBo> contractSourceRelBoList = (List<ContContractSourceRelBo>) data.get("contractSourceRelBoList");
        // 合同模板
        ContContractTempletEntity contractTempletEntity = (ContContractTempletEntity) data.get("contractTempletEntity");
        // 费用配置
        List<ResCostConfigureVo> costConfigureVoList = (List<ResCostConfigureVo>) data.get("costConfigureVoList");
        //车辆信息
        BaseCarEntity car = baseCarService.getOne(new QueryWrapper<BaseCarEntity>().eq("contract_source_id",contractSourceRelBoList.get(0).getId()));
        //租金阶梯
        List<ContRentLadderEntity> rentLadder = contRentLadderService.list(new QueryWrapper<ContRentLadderEntity>().eq("contract_source_id",contractSourceRelBoList.get(0).getId()));
        String htmlstr = contractTempletEntity.getContent();

        // 合同子表信息：水起数，电起数，公共电起数，付费方式，押金，租金，折扣，出租类型
        String paytype = contractEntity.getPayType();// 付费方式
        String caruser = car.getUser();// 车辆使用人
        String carbelonger = car.getOwner();// 车辆所有人
        BigDecimal price = rentLadder.get(0).getPrice();// 租金
        BigDecimal deposit = contractSourceRelBoList.get(0).getCashPledge();// 押金
        // 物业费=物业基本+物业公维金
        BigDecimal propertyFee = new BigDecimal(0);
        List<ResCostConfigureVo> propertyList = costConfigureVoList.stream().filter(costConfigureVo ->
                PriceTypeEnum.PROPERTY_BASEFEE.getValue().equals(costConfigureVo.getCostType()) &&
                        "2".equals(costConfigureVo.getSourceType()) // 获取车位物业基本服务费
        ).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(propertyList)){
            propertyFee = propertyList.get(0).getPrice();
        }
        // 公维金
        BigDecimal maintenanceCharge = new BigDecimal(0);
        List<ResCostConfigureVo> maintenanceChargeList = costConfigureVoList.stream().filter(costConfigureVo ->
                PriceTypeEnum.PROPERTY_PUBLICFEE.getValue().equals(costConfigureVo.getCostType()) &&
                        "2".equals(costConfigureVo.getSourceType()) // 获取车位房屋公维金
        ).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(maintenanceChargeList)){
            maintenanceCharge = maintenanceChargeList.get(0).getPrice();
        }

        BigDecimal wuyeFee = propertyFee.add(maintenanceCharge);
        map.put("wuyeFee", wuyeFee);
        map.put("carUser", caruser);
        map.put("carBelonger", carbelonger);
        map.put("deposit", deposit);
        map.put("carNum", car.getNum());
        map.put("price", price);
        // 根据缴费方式来算几个月付一次款
        if ("1".equals(paytype) || "3".equals(paytype)) {// 押一付一，押二付一
            map.put("num", "1");
        } else if ("2".equals(paytype)) {// 押一付三，押二付三
            map.put("num", "3");
        } else {
            map.put("num", "");
        }

        String customerTypeHtml = "<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                    + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                    + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"/>"
                    + "个人 &nbsp; &nbsp;□非个人";
        htmlstr = htmlstr.replace("!customerTypeHtml!", customerTypeHtml);
        // 签约人信息：
        RenterEntity user = renterFegin.getById(contractEntity.getSignerId());
        map.put("person", user);
        // 身份证图片
        String idCardBody = "";
        SysFileEntity entity = new SysFileEntity();
        entity.setFromId(contractEntity.getId());
        entity.setType(SysFileTypeEnum.ID_CARD.getValue());
        List<SysFileEntity> idCardList = sysFileService.getFiles(entity);
        if (idCardList.size() > 0) {
            for (SysFileEntity list : idCardList) {
                String path = list.getPath();
                idCardBody += "<img src=\"" + path + "\" width=\"300\" height=\"300\" />";
            }
        }
        //行驶证图片
        String xs_tbody = "";
        entity.setType(SysFileTypeEnum.VEHICLE_LICENSE.getValue());
        List<SysFileEntity> xsCardList = sysFileService.getFiles(entity);
        if (xsCardList.size() > 0) {
            for (SysFileEntity list : xsCardList) {
                String path = list.getPath();
                xs_tbody += "<img src=\"" + path + "\" width=\"300\" height=\"300\"/>";
            }
        }
        //驾驶证图片
        String js_tbody = "";
        entity.setType(SysFileTypeEnum.DRIVING_LICENCE.getValue());
        List<SysFileEntity> jsCardList = sysFileService.getFiles(entity);
        if (jsCardList.size() > 0) {
            for (SysFileEntity list : jsCardList) {
                String path = list.getPath();
                js_tbody += "<img src=\"" + path + "\" width=\"300\" height=\"300\"/>";
            }
        }

        // 发票
        String isNeedInvoicHtml = "□需要发票 &nbsp; &nbsp;<img src=\"http://120.76.246.233/image/IMG_a9999a.png\" title=\"IMG_a9999a.png\" alt=\"IMG_a9999a.png\" "
                + "width=\"12\" height=\"12\" border=\"0\" vspace=\"0\" "
                + "style=\"font-family: 宋体; font-size: 14px; white-space: normal; line-height: 21px; letter-spacing: 0.0377953px; width: 12px; height: 12px;\"></img>不需要发票";
        htmlstr = htmlstr.replace("!isNeedInvoicHtml!", isNeedInvoicHtml);

        String signingzHtml = "<div>" + idCardBody + "</div>";
        htmlstr = htmlstr.replace("!signingzHtml!", signingzHtml);
        String signingxsHtml = "<div>" + xs_tbody + "</div>";
        htmlstr = htmlstr.replace("!signingxsHtml!", signingxsHtml);
        String signingjsHtml = "<div>" + js_tbody + "</div>";
        htmlstr = htmlstr.replace("!signingjsHtml!", signingjsHtml);
        //个人车位不需要签字,走法大大
        htmlstr = htmlstr.replace("!signName!", "");
        htmlstr = htmlstr.replace("!ownerSignName!", "");
        return htmlstr;
    }

    @Override
    public void createPetPDF(ContContractEntity contractEntity) throws Exception {
        //合同信息表
        ContContractInfoEntity contractInfoEntity = contractInfoService.getByContractId(contractEntity.getId());
        //合同房源关系
        ContContractSourceRelEntity contractSourceRelEntity = contractSourceRelService.getOne(new QueryWrapper<ContContractSourceRelEntity>().eq("contract_id", contractEntity.getId()));
        //房源
        ResSourceEntity sourceEntity = sourceService.getById(contractSourceRelEntity.getSourceId());
        //项目信息
        ResProjectInfoEntity projectInfoEntity = resProjectInfoService.getProjectInfo(sourceEntity.getProjectId());
        //宠物协议内容
        String str = projectInfoEntity.getPetAgreement() == null? "": projectInfoEntity.getPetAgreement();
        String[] htmlStr = str.split("\n");
        str = "";
        for (String content : htmlStr) {
            str += "<p style=\"font-family: 宋体;\">" + content + "</p>";
        }
        str += "<p style=\"font-family: 宋体;\">签名:</p>";
        str += "!signName!";
        //乙方签字
        SysFileEntity entity = new SysFileEntity();
        entity.setFromId(contractEntity.getId());
        entity.setType(SysFileTypeEnum.PET_AGREEMENT_SIGN.getValue());
        List<SysFileEntity> filesList = sysFileService.getFiles(entity);
        if (filesList.size() > 0) {
            for (SysFileEntity listFile : filesList) {
                String path = listFile.getPath();
                str = str.replace("!signName!", "<img src=\"" + path + "\" style=\"width:200px;\" />");
            }
        } else {
            str = str.replace("!signName!", "");
        }
        //pdf生成
        String pdfName = contractEntity.getContractCode() + "_宠物协议.pdf"; // PDF名称
        try {
            //获取项目水印地址
            Map<String,Object> map = Maps.newHashMap();
            ResProjectParaEntity projectWatermark = resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.PDF_WATERMARK.getValue(),sourceEntity.getProjectId());
            String watermarkImage = projectWatermark==null?"":projectWatermark.getParamValue();
            map.put("watermarkImage", watermarkImage);
            String url = pdfUtil.pdf(str, map, pdfName);
            SysFileEntity pdfEntity = new SysFileEntity();
            pdfEntity.setFromId(contractEntity.getId())
                    .setName(pdfName)
                    .setType(SysFileTypeEnum.PET_AGREEMENT.getValue())
                    .setUrl(url)
                    .insert();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("pdf生成异常");
        }

    }

    @Override
    public void contractExpireRemind(Map<String, Object> map) {
        List<ContContractVo> list = queryList(map);
        if(!CollectionUtils.isEmpty(list)){
            //发送短信提醒
            list.stream().forEach(item -> {
                String name = item.getName();
                String endDateStr  = DateTimeUtil.formatDateTimetoString(item.getEndDate(), DateTimeUtil.FMT_yyyyMMdd);
                String renterId = item.getRenterId();
                try {
                    RenterEntity renter = renterFegin.getById(renterId);
                    if(ObjectUtil.isNotNull(renter) ){
                        //提醒运营官
                        Map<String, String> par = new HashMap<String, String>();
                        if(BaseConstants.BOOLEAN_OF_TRUE.equals(item.getIsOrganize())){
                            par.put("org", item.getIsOrganize());
                        }
                        par.put("renterName", renter.getName());
                        par.put("date", endDateStr);
                        par.put("sourceId", item.getSourceId());
                        sysPushMsgService.createPushMsg(MsgTypeEnum.HETONGJIJIANGDAOQI.getValue(), null, par);

                        //提醒租客
                        if(StrUtil.isNotBlank(renter.getTel())){
                            Map<String, Object> params = new HashMap<String, Object>();
                            params.put("name",name);
                            params.put("buildNameF","公寓平台");
                            params.put("date", endDateStr);
                            params.put("buildNameT","公寓平台");
                            params.put("org", item.getIsOrganize());
                            // modify by linderen on 20210714 修改通知方式为公众号通知 start
                            sysMsgTemplateFegin.sendByProjectId(item.getProjectId(),"176910",renter.getTel(), JSONUtil.toJsonStr(params));
                            //wechatFegin.sendMsgByTempWithOpenid(renter.getOpenid(),"XX公寓", "到期提醒",
                            //        name+"您好，您在XX公寓的合同将于"+endDateStr+"到期，如有续租意向，" +
                            //                "请及时联系XX公寓运营人员办理续租手续，感谢您的支持！");
                            //modify by linderen on 20210714 修改通知方式为公众号通知 end

                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
    }

    @Override
    @Transactional
    public void cancelContract(String contractId) {
        ContContractEntity contract = this.getById(contractId);
        contract.setState(ContractStateEnum.STATUS_CANCEL.getValue());
        contract.setCancelRemark("用户进行取消操作");
        this.updateById(contract);
        //如果有文件, 则进行删除
        //房源状态更改为未出租
        sourceService.terminationSource(contractId);
    }

    @Override
    public List<ContContractEntity> getRentContractList() {
        return baseMapper.getRentContractList();
    }


    @Override
    public IPage<ContContractVo> getContractListBySourceId(Page page, String sourceId,Date date) {
        return baseMapper.getContractListBySourceId(page, sourceId,date);
    }

    @Override
    public ContContractEntity getContractByReleaseId(String releaseId) {
        BizReleaseEntity release = bizReleaseService.getById(releaseId);
        String contractId = release.getContractId();
        return this.getById(contractId);
    }

    @Override
    public String getMaxPaperCode(String preCode) {
        String maxPaperCode = baseMapper.getMaxPaperCode(preCode);
        return maxPaperCode;
    }

    @Override
    public File downLoadFile(String zipPath,List<ContContractVo> list) {
        File file = new File(zipPath);
        if (file.exists()) {
            file.delete();
        }
        byte[] buffer = new byte[10240];
        FileOutputStream fos = null;
        ZipOutputStream zos = null;
        try {
            if(!CollectionUtils.isEmpty(list)){
                fos = new FileOutputStream(zipPath);
                zos = new ZipOutputStream(fos);
                for (ContContractVo item : list) {
                    String filePath = item.getContractCode() +"-"+ item.getSourceName();
                    File dic = new File(filePath);
                    //查找合同下所有的pdf
                    String id = item.getId();
                    List<SysFileEntity> allFiles = new ArrayList<>();
                    //合同pdf
                    List<SysFileEntity> contractFiles = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.CONTRACT);
                    allFiles.addAll(contractFiles);
                    //未签字合同pdf
                    List<SysFileEntity> unsignedContractFiles = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.UNSIGNED_CONTRACT);
                    allFiles.addAll(unsignedContractFiles);
                    //补充协议
                    List<SysFileEntity> subjoinContractFiles = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.SUBJOIN_CONTRACT);
                    allFiles.addAll(subjoinContractFiles);
                    //手动上传合同
                    List<SysFileEntity> subContractFiles = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.SUB_CONTRACT);
                    allFiles.addAll(subContractFiles);

                    //退房确认书
                    List<SysFileEntity> stopContractFiles = sysFileService.getListByFromIdAndType(id, SysFileTypeEnum.EARLY_AGREEMENT);
                    allFiles.addAll(stopContractFiles);

                    Map<String, Integer> map = new HashMap<>();
                    for (int i = 0; i < allFiles.size(); i++) {
                        SysFileEntity fileEntity = allFiles.get(i);
                        String url = fileEntity.getPath();
                        InputStream input = ZipUtils.getFileInputStreamUrl(url);
                        if(null != input) {
                            String fileName = fileEntity.getName();
                            if(StrUtil.isBlank(fileName)){
                                fileName = fileEntity.getUrl().substring(url.lastIndexOf("/"), url.length());
                            }
                            //文件名称可能重复
                            Integer integer = map.get(fileName);
                            if(integer == null){
                                integer = 1;
                                map.put(fileName,integer);
                            }else{
                                integer = integer +1;
                                map.put(fileName,integer);
                            }
                            if(integer != 1){
                                int fileNameIndex =  integer - 1;
                                int index = fileName.lastIndexOf(".");
                                if(index >-1){
                                    fileName = fileName.substring(0,index)+"("+fileNameIndex+")"+fileName.substring(index);
                                }else{
                                    fileName +="("+fileNameIndex+")";
                                }
                            }
                            zos.putNextEntry(new ZipEntry(dic + File.separator + fileName));
                            int length;
                            while ((length = input.read(buffer)) > 0) {
                                zos.write(buffer, 0, length);
                            }
                            zos.closeEntry();
                            input.close();
                        }
                    }
                }
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                zos.close();
                fos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return new File(zipPath);
    }

    @Override
    public ContContractEntity getByPlatformCode(String platformCode) {
        QueryWrapper<ContContractEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("platform_code", platformCode);
        return this.getOne(queryWrapper);
    }


    @Override
    public Map<String, String> judgeCanReletByContractId(String contractId) throws Exception {
        Map<String, String> result = new HashMap<>();

        ContContractEntity contract = this.getById(contractId);
        RenterEntity renter = renterFegin.getById(contract.getSignerId());
        if(!BaseConstants.BOOLEAN_OF_TRUE.equals(renter.getIsVerify())){
            result.put("code","-1");
            result.put("msg","签约方未实名认证，需实名认证完才能办理续租");
            return result;
        }

        ContContractInfoEntity contInfo = contractInfoService.getByContractId(contractId);
        if(BooleanUtil.isTrue(contInfo.getIsSubsidy())){
            result.put("code","-1");
            result.put("msg","人才合同不能续租");
            return result;
        }

        List<ContContractSourceRelEntity> css = contractSourceRelService.list(new QueryWrapper<ContContractSourceRelEntity>().eq("contract_id", contractId));

        for (int i = 0; i < css.size(); i++) {
            String sourceId = css.get(i).getSourceId();
            BigDecimal price = getPrice(contract, renter, sourceId);

            //比较一下
            ContRentLadderEntity rentLadder = rentLadderService.getRentLadderByContSourceId(css.get(i).getId());
            // TODO modify by linderen on 20210810 续租结转  提交续租申请时当前价格低于旧合同签约价格的判断去掉
//            if(rentLadder.getPrice().compareTo(price) > 0){
//                result.put("code","-1");
//                if(BaseConstants.BOOLEAN_OF_FALSE.equals(contract.getIsOrganize())){
//                    result.put("msg","当前房源的当前租金比原签约租金低，不能续租合同");
//                }else{
//                    result.put("msg","检测到有房源的当前租金比原签约租金低，不能续租合同 ");
//                }
//                return result;
//            }
        }

        if (BaseConstants.BOOLEAN_OF_FALSE.equals(contract.getIsOrganize())) {
            //1.判断当前合同，账单是否已经支付
                boolean b = bilOrderService.orderIsAllPayed(contractId,null);
                if(b){
                    //2.判断上一份合同，是否已经支付
                    BizReleaseEntity oldRelease = bizReleaseService.getByNewContractId(contractId);
                    if(ObjectUtil.isNotNull(oldRelease)){
                        b = bilOrderService.orderIsAllPayed(oldRelease.getContractId(),null);
                    }
                }
                if(!b){
                    result.put("code","-1");
                    result.put("msg","您还有未支付或应支付未推送的账单,请先支付完后再提交办理哦!");
                    return result;
                }
                //租赁止日小于当前日期的合同，应约束不允许续租
        }
        //判断是否能续租
        Map<String, String> approval = bilOrderService.isApproval(contractId, null);
        if("-1".equals(approval.get("code"))){
            return approval;
        }
        result.put("code","1");
        result.put("msg","可以续签");
        return result;
    }

    @Override
    public boolean isSameOrderMode(String contractId) {
        //旧合同账单精度
        ContContractInfoEntity contractInfo = contractInfoService.getByContractId(contractId);
        //当前配置的账单精度
        Integer newScale = Integer.parseInt(resProjectParaService.getByCode(ProjectParaEnum.CALC_NEWSCALE.getValue(), resProjectService.getProjectIdByContractId(contractId)));
        Integer roundingMode = Integer.parseInt(resProjectParaService.getByCode(ProjectParaEnum.CALC_ROUNDINGMODE.getValue(), resProjectService.getProjectIdByContractId(contractId)));
        if(contractInfo.getNewScale().equals(newScale) && contractInfo.getRoundingMode().equals(roundingMode)){
            return true;
        }
        return false;
    }

    //获取租金
    @Override
    public BigDecimal getPrice(ContContractEntity contract, RenterEntity renter, String sourceId) throws Exception {
        ContTempEntity temp = contTempService.getById(contract.getId());

        ResSourceConfigureEntity sourceConfigure = resSourceConfigureService.getOne(new QueryWrapper<ResSourceConfigureEntity>().eq("source_id", sourceId));
        ResProjectInfoServiceImpl.PriceVo priceVo = resProjectInfoService.getContractPrice(contract, sourceId);
        BigDecimal originalRent = sourceConfigure.getPrice();
        return resProjectInfoService.getPrice(priceVo.getFloatSize(), priceVo.getFloatPrice(), originalRent, BigDecimal.ZERO);
    }

    @Override
    public ContContractEntity getOldContractByNewContractId(String newContractId){
        return baseMapper.getOldContractByNewContractId(newContractId);
    }

    @Override
    public List<ContContractEntity> queryOldContract() {
        return baseMapper.queryOldContract();
    }

    @Override
    public List<ContContractEntity> getCheckout1Day() {
        return baseMapper.getCheckout1Day();
    }

    @Override
    public List<ContContractEntity> getOrgFixContract() {
        return baseMapper.getOrgFixContract();
    }

    @Override
    public RenterEntity judgeRenter(String tel, RenterEntity renter, String name, String idType, String idNo) throws Exception {
        if (renter == null) {
            // 新增租客用户
            renter = new RenterEntity();
            renter.setName(name)
                    .setTel(tel)
                    .setIdType(idType)
                    .setIdNo(idNo)
                    .setType(RenterType.COMMON.getValue())
                    .setUsername(tel)
                    .setSalt(AlgorUtil.getSalt())
                    .setPassword(PassWordCreateUtil.createPassWord(6)); // 随机密码生成

            renter = JSON.parseObject(JSON.toJSONString(renterFegin.add(renter).get("data")), RenterEntity.class);
        } else {
            //修改租客信息为用户最新信息
            renter.setName(name)
                    .setIdType(idType)
                    .setIdNo(idNo);
            renterFegin.update(renter);
        }
        return renter;
    }

    @Override
    public RenterEntity judgeRenter2(String tel, RenterEntity renter, String name, String idType, String idNo,String email,String address) throws Exception {
        if (renter == null) {
            // 新增租客用户
            renter = new RenterEntity();
            renter.setName(name)
                    .setTel(tel)
                    .setIdType(idType)
                    .setIdNo(idNo)
                    .setType(RenterType.COMMON.getValue())
                    .setUsername(tel)
                    .setSalt(AlgorUtil.getSalt())
                    .setPassword(PassWordCreateUtil.createPassWord(6)); // 随机密码生成
            //邮箱跟地址
            if(StrUtil.isNotBlank(email)){
                renter.setEmail(email);
            }
            if(StrUtil.isNotBlank(address)){
                renter.setEmail(address);
            }
            renter = JSON.parseObject(JSON.toJSONString(renterFegin.add(renter).get("data")), RenterEntity.class);
        } else {
            //修改租客信息为用户最新信息
            renter.setName(name)
                    .setIdType(idType)
                    .setIdNo(idNo);
            //修改邮箱跟地址
            if(StrUtil.isNotBlank(email)){
                renter.setEmail(email);
            }
            if(StrUtil.isNotBlank(address)){
                renter.setEmail(address);
            }
            renterFegin.update(renter);
        }
        return renter;
    }

    @Override
    public void saveCheckInUser(List<MultipartFile> tzrIDCardFiles, List<MultipartFile> tzrTemporaryFiles, List<ContCheckInUserVo> contCheckInUserList, String sourceId, String tel, ContContractSourceRelEntity contractSourceRelEntity, int idnum, int tenum, String value) throws Exception {
        if (contCheckInUserList.size() > 0) {
            for (ContCheckInUserVo checkInUserVo : contCheckInUserList) {
                if (tel.equals(checkInUserVo.getTel())) {
                    throw new Exception("同住人手机号不能与签约人一致");
                    //return RestResponse.failure("同住人手机号不能与签约人一致");
                }
                QueryWrapper<ContCheckInUserEntity> wrapper = new QueryWrapper();
                wrapper.eq("tel", checkInUserVo.getTel());
                wrapper.and(query ->
                        query.and(checkIn ->
                                checkIn.ne("type", CheckInUserTypeEnum.CHECKIN.getValue()).ne("state", ApprovalStateEnum.REJECT.getValue())
                        ).or().nested(remove ->
                                remove.ne("type", CheckInUserTypeEnum.REMOVE.getValue()).ne("state", ApprovalStateEnum.COMPLETE.getValue())
                        )
                );
                ContCheckInUserEntity checkInUserEntity = new ContCheckInUserEntity();
                long count = checkInUserService.count(wrapper);
                if (count > 0) {
                    checkInUserEntity = checkInUserService.getOne(wrapper);
                }
                RenterEntity checkInRenter = renterFegin.getByTelAndType(checkInUserVo.getTel(), RenterType.COMMON.getValue());
                checkInRenter = judgeRenter(checkInUserVo.getTel(), checkInRenter, checkInUserVo.getName(), checkInUserVo.getIdType(), checkInUserVo.getIdNo());
                BeanUtil.copyProperties(checkInUserVo, checkInUserEntity);
                checkInUserEntity.setContractSourceId(contractSourceRelEntity.getId());
                checkInUserEntity.setRenterId(checkInRenter.getId());
                checkInUserEntity.setState(value);
                checkInUserEntity.setType(CheckInUserTypeEnum.CHECKIN.getValue());
                checkInUserService.saveOrUpdate(checkInUserEntity);
                // 消息推送
                sysPushMsgService.createPushMsg(MsgTypeEnum.XINZENGRUZHURENSHENQING.getValue(), null, sourceId);
                //保存图片
                if (null != tzrIDCardFiles && tzrIDCardFiles.size() > 0) {
                    for (int i = idnum; i < tzrIDCardFiles.size(); i++) {
                        MultipartFile file = tzrIDCardFiles.get(i);
                        if (checkInUserVo.getIdNum() <= 0) {
                            break;
                        }
                        //String url = FileUtil.save(file);
                        String url = minioUtil.save(file);
                        if (StrUtil.isBlank(url)) {
                            throw new BusinessException("上传照片异常");
                        }
                        SysFileEntity entity = new SysFileEntity();
                        entity.setFromId(checkInUserEntity.getId());
                        entity.setType(SysFileTypeEnum.CHECKIN_ID_CARD.getValue());
                        entity.setUrl(url);
                        sysFileService.saveOrUpdate(entity);
                        checkInUserVo.setIdNum(checkInUserVo.getIdNum() - 1);
                        idnum++;
                    }
                }
                if (null != tzrTemporaryFiles && tzrTemporaryFiles.size() > 0) {
                    for (int i = tenum; i < tzrTemporaryFiles.size(); i++) {
                        MultipartFile file = tzrTemporaryFiles.get(i);
                        //String url = FileUtil.save(file);
                        String url = minioUtil.save(file);
                        if (StrUtil.isBlank(url)) {
                            throw new BusinessException("上传照片异常");
                        }
                        SysFileEntity entity = new SysFileEntity();
                        entity.setFromId(checkInUserEntity.getId());
                        entity.setType(SysFileTypeEnum.CHECKIN_TEMPORARY_RESIDENCE.getValue());
                        entity.setUrl(url);
                        sysFileService.saveOrUpdate(entity);
                        tenum++;
                    }
                }
            }
        }
    }

    /**
     * 机构合同签约校验
     *
     * @param contractDTO
     * @return
     */
    @Override
    public String addOrganizationContractExamine(ContractDTO contractDTO) {
        if (StrUtil.isBlank(contractDTO.getContractType())) {
            return "合同类型不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getContractCode())) {
            return "合同编号不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getContractTempletId())) {
            return "合同模板不能为空";
        }
        if (null == contractDTO.getStartDate()) {
            return "合同起始日不能为空";
        }
        if (null == contractDTO.getEndDate()) {
            return "合同到期日不能为空";
        }
        if (null == contractDTO.getSignDate()) {
            return "签约日期不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getPaperCode())) {
            return "纸质合同号不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getPayType())) {
            return "缴费方式不能为空";
        }
        if (!ContractTypeEnum.BUSINESS.getValue().equals(contractDTO.getContractType())
                && StrUtil.isBlank(contractDTO.getPayerType0())) {
            return "租金支付方不能为空";
        }
        if (!ContractTypeEnum.BUSINESS.getValue().equals(contractDTO.getContractType())
                && StrUtil.isBlank(contractDTO.getPayerType1())) {
            return "生活费用支付方不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getCostConfigureId())) {
            return "费用信息不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getOrganizationName())) {
            return "机构名称不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getIdType())) {
            return "证件类型不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getIdNo())) {
            return "证件号码不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getTel())) {
            return "手机号码不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getTaxpayerCode())) {
            return "纳税人代码不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getEnterpriseAddress())) {
            return "机构地址不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getEnterpriseTel())) {
            return "公司电话不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getEnterpriseAccount())) {
            return "机构银行账号不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getEnterpriseBank())) {
            return "开户行不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getInvoiceType())) {
            return "发票类型不能为空";
        }
        if (null == contractDTO.getSourceConfigureEntityList() || contractDTO.getSourceConfigureEntityList().size() < 1) {
            return "房源信息不能为空";
        }

        String msg1 = examineSource(contractDTO);
        if (msg1 != null) return msg1;

        return null;
    }

    /**
     * 机构合同签约校验
     *
     * @param contractDTO
     * @return
     */
    @Override
    public String addShortOrgContractExamine(ContractDTO contractDTO) {
        if (StrUtil.isBlank(contractDTO.getContractType())) {
            return "合同类型不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getContractCode())) {
            return "合同编号不能为空";
        }
        if (null == contractDTO.getStartDate()) {
            return "合同起始日不能为空";
        }
        if (null == contractDTO.getEndDate()) {
            return "合同到期日不能为空";
        }
        if (null == contractDTO.getSignDate()) {
            return "签约日期不能为空";
        }
        if (!ContractTypeEnum.BUSINESS.getValue().equals(contractDTO.getContractType())
                && StrUtil.isBlank(contractDTO.getPayerType0())) {
            return "租金支付方不能为空";
        }
        if (!ContractTypeEnum.BUSINESS.getValue().equals(contractDTO.getContractType())
                && StrUtil.isBlank(contractDTO.getPayerType1())) {
            return "生活费用支付方不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getCostConfigureId())) {
            return "费用信息不能为空";
        }
        /*if (StrUtil.isBlank(contractDTO.getName())) {
            return "机构名称不能为空";
        }*/
        if (StrUtil.isBlank(contractDTO.getIdType())) {
            return "证件类型不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getIdNo())) {
            return "证件号码不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getTel())) {
            return "手机号码不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getTaxpayerCode())) {
            return "纳税人代码不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getEnterpriseAddress())) {
            return "机构地址不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getEnterpriseTel())) {
            return "公司电话不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getEnterpriseAccount())) {
            return "机构银行账号不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getEnterpriseBank())) {
            return "开户行不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getInvoiceType())) {
            return "发票类型不能为空";
        }
        return null;
    }



    /**
     * 短租签约判断
     *
     * @param contractDTO
     * @return
     */
    @Override
    public String addContractExamine(ContractDTO contractDTO) {
        if (StrUtil.isBlank(contractDTO.getTel())) {
            return "手机号码不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getName())) {
            return "租客姓名不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getIdType())) {
            return "身份证件类型不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getIdNo())) {
            return "身份证件号码不能为空";
        }
        if (StrUtil.isBlank(contractDTO.getCostConfigureId())) {
            return "费用信息不能为空";
        }
        if (null == contractDTO.getStartDate()) {
            return "租凭起日不能为空";
        }
        if (null == contractDTO.getEndDate()) {
            return "租凭止日不能为空";
        }
        return null;
    }

    @Nullable
    public String examineSource(ContractDTO contractDTO) {
        String msg = "";
        for (ResSourceConfigureEntity sc : contractDTO.getSourceConfigureEntityList()) {
            ResSourceEntity source = sourceService.getById(sc.getSourceId());
            if (null != source && !BaseConstants.BOOLEAN_OF_TRUE.equals(source.getPublishTarget()) && (SourceTypeEnum.HOUSE.getValue().equals(source.getSourceType()) || "2".equals(source.getSourceType()))) {
                msg += source.getCode() + "房源未发布，";
            }
            if (SourceSignEnum.NOTRENT.getValue().equals(source.getSourceSign()) || SourceSignEnum.RESERVE.getValue().equals(source.getSourceSign())) {
                msg += source.getCode() + "房源" + SourceSignEnum.getNameByValue(source.getSourceSign()) + ",不能签约，";
            }
        }

        if (StrUtil.isNotBlank(msg)) {
            return msg.substring(0, msg.length() - 1);
        }
        return null;
    }

    @Override
    public List<SelectVo> getSourceIdList(String renterId) {
        return baseMapper.getSourceIdList(renterId);
    }

    //TODO modify by zengguoshen 20211012  start  去掉Bpm审批
    /*
    @Override
    @Transactional
    public void audit(String requestId,String guId,String auditResult) throws Exception {
        QueryWrapper<ContContractEntity> query=new QueryWrapper<>();
        query.eq("gu_id",guId);
        query.last("limit 0,1");
        ContContractEntity contract = baseMapper.selectOne(new QueryWrapper<ContContractEntity>().eq("gu_id",guId).last("limit 0,1"));

        if(null==contract){
            throw new BusinessException("未查找到审批单据ID："+guId+"的合同");
        }

        /*if (!ContractStateEnum.STATUS_PROCESS.getValue().equals(contract.getState())) {
            throw new BusinessException("该合同不是待流程审批中状态！");
        }

        if("Y".equals(auditResult)){
            List<ContContractSourceRelEntity> csList = contractSourceRelService.getListByContractId(contract.getId());
            RenterEntity renter = renterFegin.getById(contract.getSignerId());
            for (ContContractSourceRelEntity cs : csList){
                ResSourceEntity source = sourceService.getById(cs.getSourceId());
                ResProjectInfoEntity projectInfo = resProjectInfoService.getProjectInfo(source.getProjectId());
                //1.上传法大大
                RestResponse response = uploadFdd(contract, projectInfo, renter);
                if (!response.getSuccess()) {
                    throw new BusinessException(response.getMessage());
                }
                //将盖章的pdf上传到流程
                SysFileEntity file = (SysFileEntity) response.get("data");
                List<MultipartFile> files = Lists.newArrayList();
                byte[] bytes = urlTobyte(file.getPath());
                FileItem fileItem = createFileItem(bytes,file.getName());
                MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
                files.add(multipartFile);
                fileFegin.workflowUpload(requestId,"fj",files);
                //审核合同
                auditContract(contract, source, renter, true);
                //消息推送
                sysPushMsgService.createPushMsg(MsgTypeEnum.DAIBANRUZHU.getValue(), null, cs.getSourceId());
            }

            BizReleaseEntity release = bizReleaseService.getByNewContractId(contract.getId());
            if(ObjectUtil.isNotNull(release) && ReleaseTypeEnum.RELET.getValue().equals(release.getType()) && BaseConstants.BOOLEAN_OF_FALSE.equals(release.getIsOrg())){
                ContContractEntity oldCont = getById(release.getContractId());
                bizReleaseService.handleContract(oldCont);
            }
        }else{
            this.beenCancel(contract.getId(),"BPM审批不通过");
        }
    }
    */

    @Override
    @Transactional
    public void audit(String contractId,String auditResult) throws Exception {
        ContContractEntity contract = baseMapper.selectById(contractId);
        if(null==contract){
            throw new BusinessException("未查找到合同");
        }

        /*if (!ContractStateEnum.STATUS_PROCESS.getValue().equals(contract.getState())) {
            throw new BusinessException("该合同不是待流程审批中状态！");
        }*/

        if("Y".equals(auditResult)){
            List<ContContractSourceRelEntity> csList = contractSourceRelService.getListByContractId(contract.getId());
            RenterEntity renter = renterFegin.getById(contract.getSignerId());
            for (ContContractSourceRelEntity cs : csList){
                ResSourceEntity source = sourceService.getById(cs.getSourceId());
                ResProjectInfoEntity projectInfo = resProjectInfoService.getProjectInfo(source.getProjectId());
                ResProjectEntity project=resProjectService.queryById(source.getProjectId());
                //1.上传法大大
                // TODO 20211028 暂时去掉法大大接口 企业部分 start
                /*RestResponse response = uploadFdd(contract, projectInfo, renter);
                if (!response.getSuccess()) {
                    throw new BusinessException(response.getMessage());
                }*/
                // TODO 20211028 暂时去掉法大大接口 企业部分 end
                //将盖章的pdf上传到流程
                /*SysFileEntity file = (SysFileEntity) response.get("data");
                List<MultipartFile> files = Lists.newArrayList();
                byte[] bytes = urlTobyte(file.getPath());
                FileItem fileItem = createFileItem(bytes,file.getName());
                MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
                files.add(multipartFile);*/

                //审核合同
                auditContract(contract, source, renter, true);
                //消息推送
                //sysPushMsgService.createPushMsg(MsgTypeEnum.DAIBANRUZHU.getValue(), null, cs.getSourceId());
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("name",renter.getName() );
                params.put("project",project.getName());
                params.put("house",source.getCode());
                // modify by linderen on 20210714 修改通知方式为公众号通知 start
                sysMsgTemplateFegin.sendBySysCompanyId(project.getCompanyId(),"198884",renter.getTel(),JSONUtil.toJsonStr(params));
                //个人租客，将信息推送至小程序
                if(BaseConstants.BOOLEAN_OF_FALSE.equals(contract.getIsOrganize())){
                    params.put("title","签约成功");
                    params.put("renterId",renter.getId());
                    //sysPushMsgService.pushRenterMsg(params);
                }
            }

            BizReleaseEntity release = bizReleaseService.getByNewContractId(contract.getId());
            if(ObjectUtil.isNotNull(release) && ReleaseTypeEnum.RELET.getValue().equals(release.getType()) && BaseConstants.BOOLEAN_OF_FALSE.equals(release.getIsOrg())){
                ContContractEntity oldCont = getById(release.getContractId());
                bizReleaseService.handleContract(oldCont,false);
                //续租时,合同开始时间已经到，新合同状态为生效,修改原合同的充值余额到新合同,原充值余额记录删除
                if(contract.getState().equals(ContractStateEnum.STATUS_TAKE_EFFECT.getValue())){
                    accountBalanceService.toChangeBalance(oldCont.getId(),contract.getId());
                }
            }
            if(ObjectUtil.isNotNull(release) && ReleaseTypeEnum.CHANGE_ROOM.getValue().equals(release.getType())){
                bizSettleService.roomChangeCallBack(release.getId(),ApprovalStateEnum.COMPLETE.getValue());
                ContContractEntity oldCont = getById(release.getContractId());
                bizReleaseService.handleContract(oldCont,true);
                //bizSettleService.carryOverOrder(release.getId());
            }
        }else{
            this.beenCancel(contract.getId(),"审批不通过");
        }
    }

    @Override
    @Transactional
    public void audited(ContContractEntity contract) throws Exception {
        List<ContContractSourceRelEntity> csList = contractSourceRelService.getListByContractId(contract.getId());
        RenterEntity renter = renterFegin.getById(contract.getSignerId());
        for (ContContractSourceRelEntity cs : csList){
            ResSourceEntity source = sourceService.getById(cs.getSourceId());
            //审核合同
            auditContract(contract, source, renter, true);
            try {
                saleCustomerService.createLogByCont(contract.getId(),contract.getSignerId(),CustomerStateEnum.SIGNED.getValue(),"管家${manager}审核通过了合同"+contract.getPaperCode());
            } catch (BusinessException e) {
                e.printStackTrace();
            }
            //消息推送
            try {
                ResProjectEntity project=resProjectService.queryById(source.getProjectId());
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("name",renter.getName());
                params.put("project",project.getName());
                params.put("house",source.getCode());
                sysMsgTemplateFegin.sendByProjectId(project.getId(),"198884",renter.getTel(),JSONUtil.toJsonStr(params));

                //个人租客，将信息推送至小程序
                /*if(BaseConstants.BOOLEAN_OF_FALSE.equals(contract.getIsOrganize())){
                    params.put("title","签约成功");
                    params.put("renterId",renter.getId());
                    sysPushMsgService.pushRenterMsg(params);
                }*/
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        BizReleaseEntity release = bizReleaseService.getByNewContractId(contract.getId());
        if(ObjectUtil.isNotNull(release)
                && BaseConstants.BOOLEAN_OF_FALSE.equals(release.getIsOrg())
                && (ReleaseTypeEnum.RELET.getValue().equals(release.getType())
                    || ReleaseTypeEnum.CHANGE_ROOM.getValue().equals(release.getType()))){
            ContContractEntity oldCont = getById(release.getContractId());
            boolean isChangeRoom = false;
            if(ReleaseTypeEnum.CHANGE_ROOM.getValue().equals(release.getType())){
                bizSettleService.roomChangeCallBack(release.getId(),ApprovalStateEnum.COMPLETE.getValue());
                //bizSettleService.carryOverOrder(release.getId());
                isChangeRoom = true;
            }
            bizReleaseService.handleContract(oldCont,isChangeRoom);
            //续租换房时,合同开始时间已经到，新合同状态为生效,修改原合同的充值余额到新合同,原充值余额记录删除
            if(contract.getState().equals(ContractStateEnum.STATUS_TAKE_EFFECT.getValue())){
                accountBalanceService.toChangeBalance(oldCont.getId(),contract.getId());
            }
        }
    }

    @Override
    @Transactional
    public void sortAudit(String contractId,String auditResult) throws Exception {
        ContContractEntity contract = baseMapper.selectById(contractId);
        if(null==contract){
            throw new BusinessException("未查找合同");
        }
        BizReleaseEntity release = bizReleaseService.getByContractId(contract.getId());
        if(ObjectUtil.isNotNull(release)){
            if("Y".equals(auditResult)){
                if(ReleaseTypeEnum.CHECKOUT.getValue().equals(release.getType())){
                    release.setApprovalState(ApprovalStateEnum.COMPLETE.getValue());
                }else{
                    release.setChangeRoomApprovalState(ApprovalStateEnum.COMPLETE.getValue());
                }
            }else{
                if(ReleaseTypeEnum.CHECKOUT.getValue().equals(release.getType())){
                    release.setApprovalState(ApprovalStateEnum.REJECT.getValue());
                }else{
                    release.setChangeRoomApprovalState(ApprovalStateEnum.REJECT.getValue());
                }
            }
            release.updateById();
        }
    }

    //TODO modify by zengguoshen 20211012  end
    public RestResponse uploadFdd(ContContractEntity contract, ResProjectInfoEntity projectInfo, RenterEntity renter) throws Exception {
        RestResponse response = new RestResponse();
        //法大大合同上传
        ReqUploadDocs reqUploaddocs = new ReqUploadDocs();
        //合同编号
        reqUploaddocs.setContractId(contract.getContractCode());
        //合同标题
        reqUploaddocs.setDocTitle(contract.getContractCode());
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("from_id", contract.getId());
        wrapper.eq("type", SysFileTypeEnum.CONTRACT.getValue());
        SysFileEntity file = sysFileService.getOne(wrapper);
        if (ObjectUtil.isNull(file)) {
            response.setSuccess(false).setMessage("该合同没有电子合同，请联系客服！");
            return response;
        }
        reqUploaddocs.setDocUrl(FileUtil.getPath(file.getUrl()));
        reqUploaddocs.setDocType(".pdf");
        log.info("法大大上传合同开始");

        RestResponse rUpload = fadadaFegin.uploaddocs(reqUploaddocs);;
        /*if("dev".equals(active)){
            String path="d:/";
            File pdfFile=sysFileService.saveUrlAs(reqUploaddocs.getDocUrl(),path,"POST",file.getName());
            reqUploaddocs.setDocUrl(null);
            rUpload = fadadaFegin.uploaddocs(reqUploaddocs);
        }else{
            rUpload= fadadaFegin.uploaddocs(reqUploaddocs);
        }*/
        log.info("返回code:" + rUpload);
        if (!"200".equals(rUpload.get("code").toString())) {
            return rUpload;
        }
        //法大大接口
        ReqExtsignAuto reqExtsignAuto = new ReqExtsignAuto();
        String transactionId = CodeUtil.generateUuid(true);
        //交易号(合同ID作为手动签署的交易号，因此自动签署的交易号随机生成)
        reqExtsignAuto.setTransactionId(transactionId);
        //合同编号
        reqExtsignAuto.setContractId(contract.getContractCode());
        //合同标题
        reqExtsignAuto.setDocTitle(contract.getContractCode());
        //定位关键字
        reqExtsignAuto.setSignKeyword("签章处");
        //关键字策略
        reqExtsignAuto.setKeywordStrategy("0");//2:最后一个关键字签章
        //客户角色
        reqExtsignAuto.setClientRole("1");
        //客户编号（我们的客户编号）
        reqExtsignAuto.setCustomerId(renter.getFadadaCode());
        log.info("法大大进行自动签署");
        RestResponse rExtsign = fadadaFegin.extsignAuto(reqExtsignAuto, projectInfo.getSignature());
        if (!"200".equals(rExtsign.get("code").toString())) {
            return rExtsign;
        }
        //法大大合同下载接口
        log.info("法大大进行合同下载,合同id:" + contract.getContractCode());
        String url = fadadaFegin.downloaddocs(contract.getContractCode());
        if (StrUtil.isEmpty(url)) {
            response.setSuccess(false).setMessage("法大大合同下载id为空！");
            return response;
        }
        log.info("法大大合同下载结果:" + url);
        //将法大大返回的pdf上传到文件服务器
        //url = FileUtil.save(url, "pdf");
        url = minioUtil.save(url, "pdf");
        SysFileEntity fileEntity = new SysFileEntity();
        //先找到该合同原来对应的pdf，并删除
        List<SysFileEntity> files = sysFileService.getListByFromIdAndType(contract.getId(), SysFileTypeEnum.CONTRACT);
        if (!files.isEmpty()) {
            fileEntity = files.get(0);
        }
        String fileName = "";
        if (ObjectUtil.isNotNull(fileEntity)) {
            fileName = fileEntity.getName();
            log.info("删除未盖章合同:" + fileEntity.getId());
            sysFileService.delFile(fileEntity);

        }
        //将从法大大返回的pdf作为该合同的新pdf上传
        fileEntity = new SysFileEntity();
        fileEntity.setUrl(url).setFromId(contract.getId()).setType(SysFileTypeEnum.CONTRACT.getValue()).setName(fileName);
        sysFileService.save(fileEntity);
        return response.setSuccess(true).setData(fileEntity);
    }

    private static byte[] urlTobyte(String url) throws MalformedURLException {
        URL ur = new URL(url);
        BufferedInputStream in = null;
        ByteArrayOutputStream out = null;
        try {
            in = new BufferedInputStream(ur.openStream());
            out = new ByteArrayOutputStream(1024);
            byte[] temp = new byte[1024];
            int size = 0;
            while ((size = in.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                in.close();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        byte[] content = out.toByteArray();
        return content;
    }

    private static FileItem createFileItem(byte[] buffer,String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item = factory.createItem("textField", "application/pdf", true, fileName);
        try {
            OutputStream os = item.getOutputStream();
            os.write(buffer);
            os.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return item;
    }

    @Transactional
    public void beenCancel(String id, String remark) throws BusinessException {
        ContContractEntity entity = this.getById(id);
        entity.setCancelRemark(remark);
        entity.setState(ContractStateEnum.STATUS_CANCEL.getValue());//3为已取消状态
        ContContractSourceRelEntity sourceRelEntity = contractSourceRelService.getOne(new QueryWrapper<ContContractSourceRelEntity>().eq("contract_id", id));
        BilOrderEntity orderEntity  = bilOrderService.getOne(new QueryWrapper<BilOrderEntity>().eq("contract_id",id).eq("order_type", OrderTypeEnum.DEPOSIT.getValue()));
        if(orderEntity != null){
            orderEntity.setIntention("4");
            bilOrderService.saveOrUpdate(orderEntity);
            this.saveOrUpdate(entity);
            return;
        }
        if (ObjectUtil.isNotNull(sourceRelEntity)) {
            //修改房源状态为未出租
            ResSourceEntity sourceEntity = new ResSourceEntity();
            sourceEntity.setId(sourceRelEntity.getSourceId());
            sourceEntity.setState(SourceStateEnum.UNRENT.getValue());
            sourceService.saveOrUpdate(sourceEntity);
            //生成退定金账单
            bilOrderAutoService.createDueRefundOrder(entity, sourceRelEntity.getSourceId());
            // TODO add by linderen on 20211111 取消签约时需要修改对应t_sale_demand表中数据
            SaleDemandEntity demandEntity = saleDemandDao.selectOne(new QueryWrapper<SaleDemandEntity>().eq("user_id", entity.getSignerId())
                    .eq("source_id",sourceRelEntity.getSourceId()).eq("is_fix","1"));
            if(demandEntity != null){
                demandEntity.setIsFix(false);
                demandEntity.updateById();
            }
            // TODO add by linderen on 20211111
        }
        BizReleaseEntity release = bizReleaseService.getByNewContractId(id);
        if(ObjectUtil.isNotNull(release) && ReleaseTypeEnum.RELET.getValue().equals(release.getType())){
            //旧合同变成已生效
            ContContractEntity oldContract = this.getById(release.getContractId());
            oldContract.setState(ContractStateEnum.STATUS_TAKE_EFFECT.getValue());
            baseMapper.updateById(oldContract);

            //房圆变成已出租
            ContContractSourceRelEntity oldCs = contractSourceRelService.getOne(new QueryWrapper<ContContractSourceRelEntity>().eq("contract_id", release.getContractId()));
            if (ObjectUtil.isNotNull(oldCs)) {
                ResSourceEntity sourceEntity = new ResSourceEntity();
                sourceEntity.setId(oldCs.getSourceId());
                sourceEntity.setState(SourceStateEnum.RENT.getValue());
                sourceService.saveOrUpdate(sourceEntity);
            }
            //变成已取消
            release.setState(ReleaseStateEnum.CANCEL.getValue());
            bizReleaseService.updateById(release);
        }
        this.saveOrUpdate(entity);
    }

    @Override
    public String getHydropowerStartTime(String contractId, String type){
        return baseMapper.getHydropowerStartTime(contractId,type);
    }

    @Override
    public IPage<ContContractVo> getExpirings(Map<String, Object> map,Page page) {
        DataScope dataScope = new DataScope(UoneSysUser.id());
        dataScope.setProAlias("C");
        dataScope.setProjectFieldName("project_id");
        return baseMapper.getExpirings(page,dataScope,map);
    }

    @Override
    public IPage<ContContractVo> selectPageByEmp(Page page, String renterId,String contractState) {
        return baseMapper.selectPageByEmp(page, renterId,contractState);
    }

    @Override
    public ContContractEntity getEffectiveContract(String renterId) {
        return baseMapper.getEffectiveContract(renterId);
    }

    @Override
    public ContContractEntity generateContract(RenterEntity renter, ResSourceVo source, String tempId, Date startDate, Date endDate, AlterPriceVo alterPriceVo) throws Exception {
        return this.generateContract(renter,source, tempId, startDate, endDate,null,alterPriceVo);
    }

    @Override
    public List<RenterVo> getRenterList(String projectId, String partitionId,String floor,String sourceId) {
        return baseMapper.getRenterList(projectId,partitionId,floor,sourceId);
    }

    @Override
    @Transactional
    public ContContractEntity generateMultiSourceContract(RenterEntity renter, ContFrameContractEntity frameContract, ContTempEntity temp, Date startDate, Date endDate,String sourceIds,AlterPriceVo alterPriceVo) throws Exception {
        if (ObjectUtil.isNull(temp)) {
            throw new BusinessException("未关联合同模板！");
        }

        //startDate = ObjectUtil.isNotNull(startDate) ? startDate : demand.getCheckInTime();
        //endDate = ObjectUtil.isNotNull(endDate) ? endDate : demand.getCheckOutTime();

        /*QueryWrapper<DemoContractEntity> wrapper = new QueryWrapper<>();
        DemoContractEntity demoContractEntity = demoContractService.getOne(wrapper.eq("source_id",source.getId()).last("limit 0,1"));
        if(null!=demoContractEntity&&DateUtil.beginOfDay(demoContractEntity.getEndTime()).getTime()<DateUtil.beginOfDay(endDate).getTime()){
            throw new BusinessException("合同截止日不能大于主合同截止日");
        }*/

        ContContractEntity contract = new ContContractEntity();
        String contractCode = resProjectService.getContractCodeByProjectId(frameContract.getProjectId());
        contract.setContractCode(contractCode);
        contract.setContractTempletId(temp.getId());
        contract.setSignerId(renter.getId());
        contract.setPayType(frameContract.getPayType());
        contract.setState(ContractStateEnum.STATUS_NO_IN_TIME.getValue());
        contract.setContractType(ContractTypeEnum.APARTMENT.getValue());
        contract.setSignType(SignTypeEnum.OFF_LINE.getValue());
        contract.setStartDate(DateUtil.beginOfDay(startDate));
        contract.setEndDate(DateUtil.endOfDay(endDate));
        contract.setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE);
        contract.setCostConfigureId(frameContract.getCostConfigureId());
        contract.setPlatform(PlatformEnum.YW.getValue());
        BigDecimal price=null;
        BigDecimal cashPledge=null;
        if(ObjectUtil.isNotNull(alterPriceVo)){
            price=alterPriceVo.getPrice();
            cashPledge=alterPriceVo.getDeposit();
            contract.setTotalPrice(price);
        }
//        if(StrUtil.isNotBlank(UoneSysUser.id())&&LoginType.USER.equals(UoneSysUser.loginType())){
        if(StrUtil.isNotBlank(UoneSysUser.id())){
            contract.setManager(UoneSysUser.id());
        }
        /*if(StrUtil.isNotBlank(contractId)){
            contract.setId(contractId);
            //删除记录
            this.deleteContract(contractId);
        }*/
        /*if (CustomerTypeEnum.ENTERPRISE.getValue().equals(temp.getCustomerType())) {
            contract.setIsOrganize(BaseConstants.BOOLEAN_OF_TRUE);
        } else {
            contract.setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE);
        }*/

        saveOrUpdate(contract);
        //2.保存合同房源关系表阶梯
        contractSourceRelService.addContMultiSourceRel(contract, sourceIds, price, cashPledge, temp.getSubsidyPrice(), null);
        //3.保存合同信息
        ContContractInfoEntity contractInfo = contractInfoService.handleContractInfo(new ContContractInfoEntity(), contract.getId(), null, renter, temp, frameContract.getProjectId());
        contractInfoService.save(contractInfo);
        //4.保存客户合同关系表
        /*if (StrUtil.isNotBlank(demand.getCustomerId())) {
            saleCustomerService.reserve(demand.getCustomerId(), contract.getId(), source.getHouseName());
        }*/
        this.auditMultiSourceContract(contract,sourceIds,renter);
        return contract;
    }

    @Override
    public IPage<ContContractVo> getByUnsigned(Page page, ContContractVo vo) {
        return baseMapper.getByUnsigned(page,vo);
    }

    @Override
    public String getLastPaperCode(String projectId) {
        return baseMapper.getLastPaperCode(projectId);
    }
    @Override
    public String getLastYxPaperCode(String projectId) {
        return baseMapper.getLastYxPaperCode(projectId);
    }

    /**
     * 合同通过审核
     *
     * @param contract
     * @param sourceIds
     * @param renter
     * @throws Exception
     */
    @Transactional
    public void auditMultiSourceContract(ContContractEntity contract, String sourceIds, RenterEntity renter) throws Exception {
        log.info("合同签约已通过审核");
        ContContractSourceRelEntity rel = contractSourceRelService.getByContractIdAndSourceId(contract.getId(), sourceIds);
        //1.修改合同状态
        //判断时间，如果时间是当前之后就是未起租状态,正数
        long diffDay = ContractUtil.isBeforeToday(contract.getStartDate());
        if (diffDay <= 0) {
            if(contract.setState(ContractStateEnum.STATUS_TAKE_EFFECT.getValue()).updateById()){
                log.info("合同状态修改为 6生效");
            }else{
                log.info("合同状态修改为6生效，失败");
            };
        } else {
            if(contract.setState(ContractStateEnum.STATUS_NO_IN_TIME.getValue()).updateById()){
                log.info("合同状态修改为 5未启租");
            }else{
                log.info("合同状态修改为5未启租,失败");
            };
        }

        //2.修改房源状态
        String[] sourceIdArr = sourceIds.split(",");
        String projectId = "";//项目id
        for(String sourceId:sourceIdArr){
            ResSourceEntity source = sourceService.getById(sourceId);
            source.setState(SourceStateEnum.RENT.getValue()).updateById();
            checkInHouseService.saveCheckInHouse(rel.getId(), contract.getSignerId(),BaseConstants.BOOLEAN_OF_FALSE);
            checkInUserService.saveCheckInUser(rel.getId(), renter, CheckInUserTypeEnum.CHECKIN, ApprovalStateEnum.APPROVAL, DataFromEnum.AUTO,null);
            projectId = source.getProjectId();
        }
        String createRentType = "1";//租金生成规则 1自然月 2合同月 默认是自然月
        ResProjectParaEntity projectPara = resProjectParaService.getByCodeAndProjectId("CREATE_RENT_TYPE", projectId);//查询租金生成类型 自然月或者合同月
        if(projectPara != null){
            createRentType = projectPara.getParamValue();
        }
        contParService.handerConPar(contract.getId());

        //4.水电周转金以及押金子账单
        bilOrderAutoService.createDepositOrderForMultiSource(contract);
        //5.生成账单
        applicationEventPublisher.publishEvent(new AuditContractTransactionEvent(this, contract,createRentType));
    }

    @Override
    public String generatePaperCode(String projectId) {
        //获取合同编号格式
        String paperCodeFormat = "资产公寓{}号";
        ResProjectParaEntity projectPara = resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.CONT_PAPERCODE.getValue(),projectId);
        if(projectPara != null){
            paperCodeFormat = projectPara.getParamValue();
        }
        int currentYear = Year.now().getValue();
        // 在数据库中查询最后一个值
        String lastPaperCode = this.getLastPaperCode(projectId);

        int lastNumber = 0;
        String lastYearCode = "";
        if (lastPaperCode != null && lastPaperCode.startsWith(StrUtil.subPre(paperCodeFormat,2))) {
            String pattern = "\\d{8}"; // 正则表达式，匹配连续的8个数字
            // 使用正则表达式查找编号中的年份部分
            java.util.regex.Matcher matcher = java.util.regex.Pattern.compile(pattern).matcher(lastPaperCode);
            //lastYearCode = matcher.group();
            if (matcher.find()) {
                lastYearCode = matcher.group();
                lastNumber = Integer.parseInt(matcher.group()) % 10000;

                int lastYear = Integer.parseInt(lastYearCode.substring(0,4));//取数字串最前面4位数年份
                if(currentYear > lastYear){
                    lastNumber = 1;
                } else {
                    lastNumber++; // 递增
                }
            }else{
                lastNumber = 1;
            }
        }else{
            lastNumber = 1;
        }
        String yearNumber = String.format("%d%04d", currentYear, lastNumber);
        return StrUtil.format(paperCodeFormat,yearNumber);
    }

    @Override
    public String generateYxxyPaperCode(String projectId) {
        //获取合同编号格式
        ResProjectEntity project = resProjectService.getById(projectId);
        // 在数据库中查询最后一个值
        String lastPaperCode = this.getLastYxPaperCode(projectId);
        lastPaperCode = StrUtil.isBlank(lastPaperCode)?"0":lastPaperCode;
        int lastNumber = Integer.parseInt(StrUtil.subSuf(lastPaperCode,-4));

        lastNumber++; // 递增
        String number = String.format("%04d", lastNumber);
        return StrUtil.format("{}YXXY{}{}",project.getCode(),DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN),number);
    }

    @Override
    public String generateGjjPaperCode(String projectId) {
        //获取合同编号格式
        String paperCodeFormat = "资产公寓{}号";
        ResProjectParaEntity projectPara = resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.GJJ_PAPERCODE.getValue(),projectId);
        if(projectPara != null){
            paperCodeFormat = projectPara.getParamValue();
        }
        int currentYear = Year.now().getValue();
        // 在数据库中查询最后一个值
        String lastPaperCode = contContractFileRelService.getLastName(projectId);

        int lastNumber = 0;
        int extractedYear = 0;
        if (lastPaperCode != null && lastPaperCode.startsWith(StrUtil.subPre(paperCodeFormat,2))) {
            // 提取编号中的年份部分（假设编号格式为：资产公寓YYYYMMDDHH，其中YYYY为年份，MMDDHH为其他数字）
            String pattern = "(\\d{4})\\d{4}"; // 正则表达式，匹配年份部分（前4位数字）
            java.util.regex.Matcher matcher = java.util.regex.Pattern.compile(pattern).matcher(lastPaperCode);
            if (matcher.find()) {
                extractedYear = Integer.parseInt(matcher.group(1));
                // 提取编号中的序号部分（后4位数字）
                String numberPattern = "\\d{8}$"; // 匹配最后8位数字中的序号部分
                java.util.regex.Matcher numberMatcher = java.util.regex.Pattern.compile(numberPattern).matcher(lastPaperCode);
                if (numberMatcher.find()) {
                    lastNumber = Integer.parseInt(numberMatcher.group().substring(4)) % 10000; // 提取并取模
                }
            }
        }
        // 如果跨年，则重置lastNumber为1
        if (extractedYear != currentYear) {
            lastNumber = 1;
        } else {
            lastNumber++; //否则递增
        }

        String yearNumber = String.format("%d%04d", currentYear, lastNumber);
        return StrUtil.format(paperCodeFormat,yearNumber);
    }

    @Override
    public int getRenterNum(String cityCode) {
        return baseMapper.getRenterNum(cityCode);
    }

    @Override
    public List<Map<String, Object>> getMonthSignCounts() {
        return baseMapper.getMonthSignCounts();
    }

    @Override
    public List<ContContractEntity> getToAuditContractList(Map<String, Object> map) {
        return baseMapper.getToAuditContractList(map);
    }

    @Override
    public int repetitionByPaperCode(String paperCode) {
        return baseMapper.repetitionByPaperCode(paperCode);
    }

    @Override
    public IPage<Map<String, Object>> selectPageByIntent(Page page, Map<String, Object> map) {
        DataScope scope = DataScope.newDataScope(UoneSysUser.id());
        scope.setProAlias("s");
        scope.setProjectFieldName("project_id");
        return baseMapper.selectPageByIntent(page,scope,map);
    }

    @Override
    public Map<String, Object> getIntentBySourceIdAndRenterId(String sourceId, String renterId) {
        return baseMapper.getIntentBySourceIdAndRenterId(sourceId,renterId);
    }

    @Override
    public List<Map<String, String>> getContByRenter(String renterId) {
        return baseMapper.getContByRenter(renterId);
    }

    @Override
    public List<Map<String, Object>> countRentConts(String year) {
        return baseMapper.countRentConts(year);
    }

    @Override
    public BigDecimal countRentContsArea() {
        return baseMapper.countRentContsArea();
    }

    @Override
    public List<ContContractVo> expiringIn30days(List<String> projectCodes) {
        if (CollectionUtils.isEmpty(projectCodes)) {
            return Collections.emptyList();
        }
        return baseMapper.expiringIn30days(projectCodes);
    }

    public RestResponse autoRenterConfirm() {
        Map<String, Object> taskMap = flowTaskService.renterTodoMap();
        List<String> projectCodes = ListUtil.toList("AMYN", "AMBH");
        Set<String> contractIds = taskMap.keySet();
        Map<String, Object> map = new HashMap<>();
        map.put("type", ReleaseTypeEnum.CHECKOUT.getValue());
        map.put("ids", contractIds);
        map.put("projectCodes", projectCodes);
        map.put("contractState", ContractStateEnum.STATUS_AUDIT_CHECKOUT.getValue());
        List<ContContractVo> voList = releaseWaitRenterConfirmList(map);
        Set<String> toCheckOutContractIds = new HashSet<>();
        for (ContContractVo vo : voList) {
            FlowTaskDto flowTask = (FlowTaskDto) taskMap.get(vo.getId());
            if (flowTask != null) {
                Date createTime = flowTask.getCreateTime();
                long betweenDay = DateUtil.betweenDay(createTime, new Date(), true);
                if (betweenDay > 3) {
//                if (true){
                    FlowTaskVo taskVo = new FlowTaskVo()
                            .setTaskId(flowTask.getTaskId())
                            .setAssignee("系统自动确认")
                            .setFlag(false)
                            .setComment("")
                            .setInstanceId(flowTask.getProcInsId())
                            .setVariables(flowTask.getVariable());
                    flowTaskService.complete(taskVo);
                    toCheckOutContractIds.add(vo.getId());
                }
            }
        }
        if (CollUtil.isNotEmpty(toCheckOutContractIds)) {
            Collection<ContContractEntity> entities = listByIds(toCheckOutContractIds);
            entities.forEach(entity -> entity.setState(ContractStateEnum.STATUS_CHECKOUT.getValue()));
            updateBatchById(entities);
        }
        return RestResponse.success();
    }

    @Override
    public List<ContContractEntity> getContractListForTask(List<String> projectCodes) {
        return baseMapper.getContractListForTask(projectCodes);
    }

    @Override
    public List<Map<String, Object>> selectContractsForRecord(Map<String, Object> map) {
        return baseMapper.selectContractsForRecord(map);
    }

    @Override
    public ContContractVo getContractInfoById(String id) {
        return baseMapper.getContractInfoById(id);
    }

}
