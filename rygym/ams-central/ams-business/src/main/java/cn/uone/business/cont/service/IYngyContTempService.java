package cn.uone.business.cont.service;

import cn.uone.application.enumerate.contract.AnnexTypeEnum;
import cn.uone.application.enumerate.contract.TempCodeEnum;
import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import cn.uone.bean.entity.business.cont.ContFrameContractEntity;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.cont.ContTempParamEntity;
import cn.uone.bean.entity.business.cont.ContTempRichEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itextpdf.text.DocumentException;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
public interface IYngyContTempService extends IService<ContTempEntity> {

    String getTempHtml(String contractId, ContTempEntity e,Map<TempCodeEnum,String> map, ContTempRichEntity rich,String costId) throws Exception;

    String getTempHtmlByDelivery(String contractId, ContTempEntity e,Map<TempCodeEnum,String> map, ContTempRichEntity rich) throws BusinessException;

    String getTempHtmlByIntent(Map<String, Object> map, ContTempRichEntity rich) throws Exception;

    String getTempHtmlByGjj(String contFileId, ContTempRichEntity rich) throws BusinessException;

}
