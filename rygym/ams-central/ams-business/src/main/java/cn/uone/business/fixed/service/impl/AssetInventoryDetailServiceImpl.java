package cn.uone.business.fixed.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.application.enumerate.source.DetailInventoryStateEnum;
import cn.uone.bean.entity.business.fixed.*;
import cn.uone.bean.entity.business.fixed.vo.FixedPropertyVo;
import cn.uone.bean.entity.business.fixed.vo.InventoryStatisticsVo;
import cn.uone.business.fixed.dao.AssetInventoryDetailDao;
import cn.uone.business.fixed.service.*;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 固定资产盘点详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service
public class AssetInventoryDetailServiceImpl extends ServiceImpl<AssetInventoryDetailDao, AssetInventoryDetailEntity> implements IAssetInventoryDetailService {

    @Autowired
    AssetInventoryDetailDao assetInventoryDetailDao;

    /*@Resource
    private IAssetInventoryService iAssetInventoryService;*/

    @Resource
    private IAssetCategoryService iAssetCategoryService;

    @Resource
    private IAssetLocationService iAssetLocationService;

    @Resource
    private IAssetFromService iAssetFromService;

    @Resource
    private IAssetDiscardedService iAssetDiscardedService;

    @Resource
    private IFixedPropertyService iFixedPropertyService;

    @Override
    public IPage<AssetInventoryDetailEntity> page(Page page, AssetInventoryDetailEntity entity) {
        QueryWrapper<AssetInventoryDetailEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getInventoryId())) {
            queryWrapper.like("t_fixed_asset_inventory_detail.inventory_id", entity.getInventoryId());
        }
        if (ObjectUtil.isNotEmpty(entity.getInventoryState())) {
            queryWrapper.like("t_fixed_asset_inventory_detail.inventory_state", entity.getInventoryState());
        }
        queryWrapper.orderByDesc("t_fixed_asset_inventory_detail.create_date");
        IPage iPage =  baseMapper.selectPage(page, queryWrapper);
        return iPage;
    }

    @Override
    public IPage<FixedPropertyVo> pageList(Page page, Map<String, Object> map) {
        DataScope scope = new DataScope(UoneSysUser.id());
        scope.setProAlias("s");
        IPage<FixedPropertyVo> iPage = baseMapper.pageList(page,scope,map);
        for(FixedPropertyVo propertyVo:iPage.getRecords()){
            propertyVo.setImgPath(FileUtil.getPath(propertyVo.getImg()));
        }
        return iPage;
    }

    @Override
    public List<FixedPropertyVo> getPropertyList(Map<String, Object> map) {
        DataScope scope=new DataScope(UoneSysUser.id());
        scope.setProAlias("s");
        List<FixedPropertyVo> propertyList = baseMapper.pageList(scope,map);
        for(FixedPropertyVo propertyVo : propertyList){
            String imgPath = FileUtil.getPath(propertyVo.getImg());
            propertyVo.setImgPath(imgPath);
        }
        return propertyList;
    }

    @Override
    @Transactional
    public boolean updateDetailInventory(FixedPropertyVo fixedPropertyVo) {
        boolean res = true;
        String inventoryDetailId = fixedPropertyVo.getInventoryDetailId();
        AssetInventoryDetailEntity detail = this.getById(inventoryDetailId);
        detail.setInventoryState(DetailInventoryStateEnum.COMPLETEDINVENTORY.getValue());//盘点完成
        res = this.saveOrUpdate(detail);
        //res = resDetail;
        return res;
    }

    @Override
    public List<AssetInventoryDetailEntity> getList(Map<String, Object> map) {
        DataScope scope=new DataScope(UoneSysUser.id());
        scope.setProAlias("s");
        return baseMapper.getList(scope,map);
    }

    @Override
    @Transactional
    public boolean deleteDetailByInventoryId(String inventoryId) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("inventoryId",inventoryId);
        boolean res = baseMapper.deleteDetailByInventoryId(map);
        return res;
    }

    @Override
    public int updateByCode(String state, String id, String code) {
        return baseMapper.updateByCode(state,id,code);
    }

    @Override
    public InventoryStatisticsVo getQuantity(String inventoryId) {
        return baseMapper.getQuantity(inventoryId);
    }

    @Override
    public int checkByCode(String id, String code) {
        return baseMapper.checkByCode(id,code);
    }
}
