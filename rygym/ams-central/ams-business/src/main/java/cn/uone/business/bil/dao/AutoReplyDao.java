package cn.uone.business.bil.dao;

import cn.uone.bean.entity.business.bil.AutoReplyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
public interface AutoReplyDao extends BaseMapper<AutoReplyEntity> {

    AutoReplyEntity getByKeyWord(String keyWord);

    IPage<AutoReplyEntity> queryAutoReplyPage(Page page, @Param("map") Map<String, Object> map);

}
