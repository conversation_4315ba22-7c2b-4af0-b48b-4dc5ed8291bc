package cn.uone.business.bil.service.impl;

import cn.uone.bean.entity.business.bil.AutoReplyEntity;
import cn.uone.bean.entity.business.bil.vo.AutoReplyVo;
import cn.uone.business.bil.dao.AutoReplyDao;
import cn.uone.business.bil.service.IAutoReplyService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
@Service
public class AutoReplyServiceImpl extends ServiceImpl<AutoReplyDao, AutoReplyEntity> implements IAutoReplyService {

    @Override
    public AutoReplyEntity getEntityByKeyWord(String keyword) {
        return baseMapper.getByKeyWord(keyword);
    }

    @Override
    public IPage<AutoReplyEntity> findByCondition(Page page, AutoReplyVo autoReplyVo) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("searchVo", autoReplyVo);
        return baseMapper.queryAutoReplyPage(page, map);
    }
}
