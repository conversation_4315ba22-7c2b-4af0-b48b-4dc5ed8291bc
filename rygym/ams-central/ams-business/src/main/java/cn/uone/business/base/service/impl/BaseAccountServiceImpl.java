package cn.uone.business.base.service.impl;

import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.order.AccountTypeEnum;
import cn.uone.bean.entity.business.base.BaseAccountEntity;
import cn.uone.bean.entity.business.bil.BilInvestEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.business.base.dao.BaseAccountDao;
import cn.uone.business.base.service.IBaseAccountService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Service
public class BaseAccountServiceImpl extends ServiceImpl<BaseAccountDao, BaseAccountEntity> implements IBaseAccountService {

    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IBilOrderService bilOrderService;

    @Override
    public IPage<HashMap> findByCondition(Page page, String branchName, String code, String merchantId, String accountType) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("branchName", branchName);
        map.put("code", code);
        map.put("merchantId", merchantId);
        map.put("accountType", accountType);
        map.put("projectId", UoneHeaderUtil.getProjectId());
        return baseMapper.selectBaseAccountByMap(page, map);
    }

    @Override
    public BaseAccountEntity getAccountByOrder(BilOrderEntity order) {
        //获取账号类型
        String accountType = AccountTypeEnum.getEnumByOrderType(order.getOrderType()).getValue();
        //房源为车位且是租金的银行账号类型转换为160
        ResSourceEntity source = resSourceService.getById(order.getSourceId());
        if ("20".equals(accountType) && "2".equals(source.getSourceType())) {
            accountType = "160";
        }
        //根据账号类型 房源账号关系表 获取账号
        return getAccountByType(accountType, source.getProjectId());
    }

    public BaseAccountEntity getAccountByType(String accountType, String projectId) {
        // TODO modify By linderen on ******** 暂时去掉对应的账号管理功能 start
        return baseMapper.getAccountByType("2", projectId);
//            return baseMapper.getAccountByTypes();
        // TODO modify By linderen on ******** 暂时去掉对应的账号管理功能 end
    }

    @Override
    public BaseAccountEntity getAccountByMerchantId(String merchantId) {
        QueryWrapper<BaseAccountEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("merchant_id",merchantId);
        queryWrapper.eq("state", ApprovalStateEnum.COMPLETE.getValue());
        return this.getOne(queryWrapper);
    }

    @Override
    public BaseAccountEntity getAccountByOrderItem(BilOrderItemEntity orderItem) {
        //获取账号类型
        String orderItemType = orderItem.getOrderItemType();
        BilOrderEntity order = bilOrderService.getById(orderItem.getOrderId());
        String accountType = AccountTypeEnum.getEnumByOrderItemType(orderItemType).getValue();
        //房源为车位且是租金的银行账号类型转换为160
        ResSourceEntity source = resSourceService.getById(order.getSourceId());
        if ("20".equals(accountType) && "2".equals(source.getSourceType())) {
            accountType = "160";
        }
        //根据账号类型 房源账号关系表 获取账号
        return getAccountByType(accountType, source.getProjectId());
    }

    @Override
    public BaseAccountEntity getAccountByBilInvest(BilInvestEntity bilInvestEntity) {
        //获取账号类型
        String orderType = "190";
        ResSourceEntity source = resSourceService.getById(bilInvestEntity.getSourceId());
        String accountType = AccountTypeEnum.getEnumByOrderItemType(orderType).getValue();
        if ("20".equals(accountType) && "2".equals(source.getSourceType())) {
            accountType = "160";
        }
        return getAccountByType("2", source.getProjectId());
    }
}
