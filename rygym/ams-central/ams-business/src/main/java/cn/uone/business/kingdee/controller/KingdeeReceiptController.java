package cn.uone.business.kingdee.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptSearchVo;
import cn.uone.business.kingdee.service.IKingdeeApiService;
import cn.uone.business.kingdee.service.IKingdeeReceiptService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@RestController
@RequestMapping("/kingdee-receipt-entity")
public class KingdeeReceiptController extends BaseController {
    @Autowired
    private IKingdeeReceiptService kingdeeReceiptService;
    @Autowired
    private IKingdeeApiService kingdeeApiService;
    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    @RequiresPermissions("kingdeeReport:receipt")
    public RestResponse getListForPage(Page page, KingdeeReceiptSearchVo kingdeeReceiptSearchVo) {
        RestResponse response = new RestResponse();
        if (ObjectUtil.isNotNull(kingdeeReceiptSearchVo.getStartEntryDate())) {
            kingdeeReceiptSearchVo.setStartEntryDate(DateUtil.beginOfDay(kingdeeReceiptSearchVo.getStartEntryDate()));
        }
        if (ObjectUtil.isNotNull(kingdeeReceiptSearchVo.getEndEntryDate())) {
            kingdeeReceiptSearchVo.setEndEntryDate(DateUtil.endOfDay(kingdeeReceiptSearchVo.getEndEntryDate()));
        }
        String projectId = UoneHeaderUtil.getProjectId();
        if(StrUtil.isNotBlank(projectId)){
            kingdeeReceiptSearchVo.setProjectId(UoneHeaderUtil.getProjectId());
        }
        return response.setSuccess(true).setData(kingdeeReceiptService.findByCondition(page, kingdeeReceiptSearchVo));
    }

}
