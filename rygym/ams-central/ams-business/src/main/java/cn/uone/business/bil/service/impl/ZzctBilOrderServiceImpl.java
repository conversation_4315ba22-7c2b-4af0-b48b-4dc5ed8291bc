package cn.uone.business.bil.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.bil.*;
import cn.uone.bean.entity.business.bil.vo.*;
import cn.uone.business.Guomi.service.IGuomiService;
import cn.uone.business.bil.dao.BilLiveErrorLogDao;
import cn.uone.business.bil.dao.BilOrderConfirmDao;
import cn.uone.business.bil.dao.BilOrderDao;
import cn.uone.business.bil.service.*;
import cn.uone.business.cont.dao.ContContractDao;
import cn.uone.business.cont.dao.ContTempRichDao;
import cn.uone.business.cont.service.IContCheckInUserService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.cont.service.IContParService;
import cn.uone.business.kingdee.service.IKingdeeApiService;
import cn.uone.business.res.service.*;
import cn.uone.business.rpt.service.IReportInvoiceService;
import cn.uone.business.sale.service.ISaleCustomerService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.crm.ISysParaFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.fegin.tpi.IFadadaFegin;
import cn.uone.fegin.tpi.IQyWechatFegin;
import cn.uone.fegin.tpi.IWechatFegin;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.MinioUtil;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 适用于漳州城投项目
 * 从漳州城投工程迁移过来
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 * caizhanghe edit 2024-05-23
 */
@Service
@Slf4j

public class ZzctBilOrderServiceImpl extends ServiceImpl<BilOrderDao, BilOrderEntity> implements IZzctBilOrderService {


    @Autowired
    private BilOrderDao bilOrderDao;
    @Autowired
    @Lazy
    private IContCheckInUserService contCheckInUserService;
    @Autowired
    @Lazy
    private IBilOrderItemService itemService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    @Lazy
    private IContContractService contContractService;
    @Autowired
    @Lazy
    private IBilTransferService bilTransferService;
    @Autowired
    private IBilDiscountLogService bilDiscountLogService;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private ISysPushMsgService sysPushMsgService;
    @Autowired
    private IUserFegin userFegin;
    @Autowired
    private IBilInterfaceMsgService bilInterfaceMsgService;
    @Autowired
    private IContContractSourceRelService contContractSourceRelService;
    @Autowired
    private IResProjectParaService resProjectParaService;
    @Autowired
    @Lazy
    private IGuomiService guomiService;
    @Autowired
    private ISaleCustomerService saleCustomerService;
    @Autowired
    @Lazy
    private IContParService contParService;
    @Autowired
    private ISysParaFegin sysParaFegin;
    @Resource
    private ContContractDao contContractDao;
    @Autowired
    private IBilOrderPayInfoService orderPayInfoService;
    @Resource
    BilLiveErrorLogDao errorLogDao;

    @Resource
    private IWechatFegin wechatFegin;

    @Resource
    private IQyWechatFegin qyWechatFegin;

    @Resource
    private BilOrderConfirmDao orderConfirmDao;

    @Autowired
    private IResProjectCompanyService companyService;

    @Autowired
    private IKingdeeApiService kingdeeApiService;

    @Autowired
    private ISysMsgTemplateFegin sysMsgTemplateFegin;

    @Autowired
    @Lazy
    private IReportInvoiceService reportInvoiceService;

    @Resource
    private ContTempRichDao richDao;

    @Autowired
    private ISysFileService sysFileService;

    @Resource
    private IFadadaFegin fadadaFegin;
    @Autowired
    private IResProjectInfoService resProjectInfoService;
    @Autowired
    MinioUtil minioUtil;


    @Override
    public List<BilOrderEntity> getByContractIdAndNoPush(String contractId) {
        QueryWrapper<BilOrderEntity> wrapper = new QueryWrapper();
        wrapper.eq("contract_id", contractId);
        wrapper.eq("is_push","0");
        return this.list(wrapper);
    }

    @Override
    public BilOrderStatisticsVo getStatisticsVoByMap(BilOrderSearchVo bilOrderSearchVo) {
        Map<String, Object> map = Maps.newHashMap();
        bilOrderSearchVo = assembleSearchVo(bilOrderSearchVo);
        DataScope dataScope = getDataScope(bilOrderSearchVo);
        map.put("searchVo", bilOrderSearchVo);
        return baseMapper.getStatisticsVoByMap(map,dataScope);
    }

    /**
     * 根据请求header过滤项目
     *
     * @param bilOrderSearchVo
     * @return
     */
    private BilOrderSearchVo assembleSearchVo(BilOrderSearchVo bilOrderSearchVo) {
        //后台管理
        if (StrUtil.isNotEmpty(UoneHeaderUtil.getProjectId())) {
            bilOrderSearchVo.setProjectId(UoneHeaderUtil.getProjectId());
        }
        return bilOrderSearchVo;
    }

    /**
     * XX公寓 有用户id 不过滤数据权限
     *
     * @param bilOrderSearchVo
     * @return
     */
    private DataScope getDataScope(BilOrderSearchVo bilOrderSearchVo) {
        DataScope dataScope = null;
        if (StrUtil.isEmpty(bilOrderSearchVo.getUserId())) {
            String id = UoneSysUser.id();
            dataScope = new DataScope(UoneSysUser.id());
            dataScope.setProAlias("s");
            dataScope.setProjectFieldName("project_id");
        }
        return dataScope;
    }


}
