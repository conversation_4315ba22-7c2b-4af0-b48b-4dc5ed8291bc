package cn.uone.business.res.service;

import cn.uone.bean.entity.business.res.ResSourceConfigureRecordEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-02-11
 */
public interface IResSourceConfigureRecordService extends IService<ResSourceConfigureRecordEntity> {
    IPage<ResSourceConfigureRecordEntity> pageList(Page page,String sourceId);
}
