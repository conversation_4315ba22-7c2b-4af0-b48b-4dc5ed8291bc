package cn.uone.business.kingdee.dao;

import cn.uone.bean.entity.business.kingdee.KingdeeInvoiceItemEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Repository
public interface KingdeeInvoiceItemDao extends BaseMapper<KingdeeInvoiceItemEntity> {
    List<KingdeeInvoiceItemEntity> getListByInvoiceId(@Param("invoiceId") String invoiceId);
}
