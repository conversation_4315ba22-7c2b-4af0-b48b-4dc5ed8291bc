package cn.uone.business.res.service;

import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-03
 */
public interface IResProjectParaService extends IService<ResProjectParaEntity> {

    String getByCode(String code, String projectId);

    ResProjectParaEntity getByCodeAndProjectId(String value, String projectId);

    String getByCodeAndSourceId(String code, String sourceId);

    boolean needSign(String projectId) throws Exception;

    Boolean checkParam(String projectId, ProjectParaEnum para);

    Map<String,String> getParams(String projectId);

    void deleteByProjectId(String projectId);

    List<ResProjectParaEntity> getListByCodeAndValue(String code, String value);

    BigDecimal parse(BigDecimal payment, String contractId) throws BusinessException;

    /**
     * 是否同步建行ccb
     *
     * @param sourceId
     * @param contractType
     * @param isOrganize
     * @return
     */
    boolean isSyncCCB(String sourceId, String contractType, String isOrganize);
}
