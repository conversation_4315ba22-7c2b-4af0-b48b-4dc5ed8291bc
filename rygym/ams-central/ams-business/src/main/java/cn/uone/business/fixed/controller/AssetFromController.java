package cn.uone.business.fixed.controller;


import cn.uone.bean.entity.business.fixed.AssetFromEntity;
import cn.uone.business.fixed.service.IAssetFromService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 固定资产来源 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@RestController
@RequestMapping("/fixed/fixed-assetFrom-entity")
public class AssetFromController extends BaseController {

    @Autowired
    private IAssetFromService iAssetFromService;

    /*@Autowired
    private IUserService userService;*/

    @GetMapping("/getPageList")
    public RestResponse getPageList(Page page, AssetFromEntity assetFrom) {
        IPage<AssetFromEntity> iPage = iAssetFromService.page(page, assetFrom);
        return RestResponse.success().setData(iPage);
    }

    /**
     * 获取信息
     *
     * @param id 主键
     * @return 资产来源管理列表
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        AssetFromEntity info = iAssetFromService.getById(id);
        return RestResponse.success().setData(info);
    }

    /**
     * 新增
     *
     * @param assetFrom 参数
     * @return 资产来源管理列表
     */
    @PostMapping("/save")
    public RestResponse save(AssetFromEntity assetFrom) {
        String projectId = UoneHeaderUtil.getProjectId();//取默认的小区id
        assetFrom.setProjectId(projectId);
        if(iAssetFromService.save(assetFrom)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    /**
     * 修改
     *
     * @param assetFrom 参数
     * @return 资产来源管理列表
     */
    @PostMapping("/edit")
    public RestResponse edit(AssetFromEntity assetFrom) {
        if(iAssetFromService.updateById(assetFrom)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return 资产来源管理列表
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        if(iAssetFromService.removeByIds(ids)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    /*@RequestMapping("/importAssetFrom")
    public RestResponse importAssetFrom(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return RestResponse.failure("请选择上传文件");
        }
        try {
            List<assetFromVo> rfidEntities  = ExcelDataUtil.importData(file.getInputStream(), assetFromVo.class);
            if(CollectionUtils.isEmpty(rfidEntities)){
                return RestResponse.failure("读取excel异常");
            }else {
                //查重
                List<String> codes = rfidEntities.stream().map(assetFromVo::getCode).collect(Collectors.toList());
                QueryWrapper<AssetFromEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("t_fixed_rfid.code",codes);

                //单独String集合
                List<String> collect = codes.stream().filter(i -> !Objects.equals(i, ""))               // list 对应的 Stream 并过滤""
                        .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum)) // 获得元素出现频率的 Map，键为元素，值为元素出现的次数
                        .entrySet().stream()                       // 所有 entry 对应的 Stream
                        .filter(e -> e.getValue() > 1)         // 过滤出元素出现次数大于 1 (重复元素）的 entry
                        .map(Map.Entry::getKey)                // 获得 entry 的键（重复元素）对应的 Stream
                        .collect(Collectors.toList());
                if(!collect.isEmpty()&& collect.get(0)!=null){
                    return RestResponse.failure("标签编号:"+collect+"重复了");
                }

                List<AssetFromEntity> rfidEntityList = iAssetFromService.list(queryWrapper);
                if(!rfidEntityList.isEmpty()){
                    List<String> codes2 = rfidEntityList.stream().map(AssetFromEntity::getCode).collect(Collectors.toList());
                    return RestResponse.failure("标签编号:"+codes2+"=已存在");
                }
                int i =0;
                List<AssetFromEntity> list = new ArrayList<>();
                for(assetFromVo vo :rfidEntities){
                    i++;
                    if(StringUtils.isEmpty(vo.getCode())){
                        return RestResponse.failure("第:"+i+"行,标签编号不能为空");
                    }
                    if(StringUtils.isEmpty(vo.getLabelSta())){
                        vo.setLabelSta("1");
                    }else {
                        //状态 1=未使用,2=已使用,3=报废
                        if("未使用".equals(vo.getLabelSta())){
                            vo.setLabelSta("1");
                        }else if("已使用".equals(vo.getLabelSta())){
                            vo.setLabelSta("2");
                        }else if("报废".equals(vo.getLabelSta())){
                            vo.setLabelSta("3");
                        }else {
                            vo.setLabelSta("1");
                        }
                    }
                    AssetFromEntity assetFrom = new AssetFromEntity();
                    BeanUtil.copyProperties(vo,assetFrom);
                    list.add(assetFrom);
                }
                iAssetFromService.saveBatch(list);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return RestResponse.failure("读取excel异常");
        }
        return RestResponse.success("导入成功");
    }*/

    @UoneLog("导出资产来源模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        ExcelRender.me("/excel/import/assetFrom.xls").beans(beans).render(response);
    }


}
