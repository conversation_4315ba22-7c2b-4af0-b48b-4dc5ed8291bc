package cn.uone.business.face.controller;


import cn.hutool.core.lang.Console;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.afforest.VegetationEntity;
import cn.uone.bean.entity.business.emp.EmpUserEntity;
import cn.uone.bean.entity.business.face.FaceAuditEntity;
import cn.uone.bean.entity.business.face.FaceLockEntity;
import cn.uone.bean.entity.business.face.FaceRelEntity;
import cn.uone.bean.entity.business.supplies.CategoryEntity;
import cn.uone.bean.entity.business.supplies.CollectApplyEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.business.face.service.IFaceAuditService;
import cn.uone.business.face.service.IFaceLockService;
import cn.uone.business.face.service.IFaceRelService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.cache.util.CacheUtil;
import cn.uone.fegin.tpi.IMinioFegin;
import cn.uone.fegin.tpi.ITrudianFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;
import org.springframework.web.multipart.MultipartFile;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

/**
 * <p>
 * 人脸注册表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@RestController
@RequestMapping("/face-audit-entity")
public class FaceAuditController extends BaseController {

    @Autowired
    IFaceAuditService faceAuditService;
    @Autowired
    IFaceLockService lockService;
    @Autowired
    IFaceRelService relService;
    @Autowired
    MinioUtil minioUtil;
    @Autowired
    ISysFileService sysFileService;
    @Autowired
    ITrudianFegin trudianFegin;
    @Autowired
    IFaceRelService faceRelService;

    @GetMapping("/list")
    public RestResponse list(Page page, FaceAuditEntity search) throws Exception {
        QueryWrapper<FaceAuditEntity> queryWrapper = new QueryWrapper<FaceAuditEntity>();
        if(search!=null){
            if(StrUtil.isNotBlank(search.getPaperCode())){
                queryWrapper.like("paper_code", search.getPaperCode());
            }
            if(StrUtil.isNotBlank(search.getSourceName())){
                queryWrapper.like("source_name", search.getSourceName());
            }
            if(StrUtil.isNotBlank(search.getCardSn())){
                queryWrapper.like("card_sn", search.getCardSn());
            }
            if((StrUtil.isNotBlank(search.getState()))){
                queryWrapper.eq("state", search.getState());
            }
        }
        queryWrapper.eq("project_id", UoneHeaderUtil.getProjectId());
        queryWrapper.orderByDesc("paper_code");
        IPage<FaceAuditEntity> p = faceAuditService.page(page,queryWrapper);
        List<FaceAuditEntity> list = p.getRecords();
        for (FaceAuditEntity v:list) {
            List<SysFileEntity> file = sysFileService.getListByFromIdAndType(v.getId(), SysFileTypeEnum.FACE);
            if(file != null &&  file.size() > 0){
                v.setPath(file.get(0).getPath());
            }
        }
        return RestResponse.success("查询成功").setData(p);
    }

    @GetMapping("/getListBySignerId")
    public RestResponse getListBySignerId(@RequestParam("signerId") String signerId) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("signerId",signerId);
        map.put("isGroupBy","1");
        List<Map<String,Object>> list = faceAuditService.selectFaceAuditList(map);
        return RestResponse.success().setData(list);
    }

    @PostMapping("/addFace")
    @UonePermissions
    public RestResponse addFace(@RequestParam("file") MultipartFile file,@RequestParam("checkInId") String checkInId) {
        if (file.isEmpty()) {
            return RestResponse.failure("请上传图片");
        }
        String url = minioUtil.save(file);

        Map<String,Object> map = Maps.newHashMap();
        map.put("checkInId",checkInId);
        Map<String,Object> checkIn = faceAuditService.selectFaceAuditList(map).get(0);
        FaceAuditEntity faceAuditEntity = new FaceAuditEntity();
        faceAuditEntity.setContractId(checkIn.get("contractId").toString());
        faceAuditEntity.setPaperCode(checkIn.get("paperCode").toString());
        faceAuditEntity.setProjectId(checkIn.get("projectId").toString());
        faceAuditEntity.setSourceName(checkIn.get("sourceName").toString());
        faceAuditEntity.setSourceId(checkIn.get("sourceId").toString());
        faceAuditEntity.setRenterId(checkIn.get("renterId").toString());
        faceAuditEntity.setRenterName(checkIn.get("name").toString());
        faceAuditEntity.setRenterTel(checkIn.get("tel").toString());
        faceAuditEntity.setStartDate(checkIn.get("startDate").toString());
        faceAuditEntity.setEndDate(checkIn.get("endDate").toString());
        faceAuditEntity.setState("1");
        faceAuditEntity.setFaceUrl(url);
        // 设置字符集（包含A-F, 0-9）
        String base = "ABCDEF0123456789";
        // 生成8位长度的字符串
        String randomString = RandomUtil.randomString(base,8);
        faceAuditEntity.setCardSn(randomString);
        faceAuditService.save(faceAuditEntity);

        SysFileEntity entity = new SysFileEntity();
        entity.setType(SysFileTypeEnum.FACE.getValue());
        entity.setSize(file.getSize());
        entity.setName(file.getOriginalFilename());
        entity.setUrl(url);
        entity.setFromId(faceAuditEntity.getId());
        sysFileService.save(entity);

        return RestResponse.success();
    }

    @PostMapping("/uploadFace")
    @UonePermissions
    public RestResponse uploadFace(@RequestParam("file") MultipartFile file,@RequestParam("checkInId") String checkInId) {
        if (file.isEmpty()) {
            return RestResponse.failure("请上传图片");
        }
        String url = minioUtil.save(file);

        FaceAuditEntity faceAuditEntity = faceAuditService.getById(checkInId);
        faceAuditEntity.setState("1");
        faceAuditEntity.setFaceUrl(url);
        faceAuditService.updateById(faceAuditEntity);

        SysFileEntity entity = sysFileService.getByFromIdAndType(faceAuditEntity.getId(),SysFileTypeEnum.FACE.getValue());
        try {
            minioUtil.delete(entity.getUrl());
        } catch (Exception e) {
           e.printStackTrace();
        }
        entity.setUrl(url);
        sysFileService.updateById(entity);

        return RestResponse.success();
    }

    /**
     * 审核通过
     * @param entity
     * @return
     */
    @PostMapping("/pass")
    public RestResponse pass(FaceAuditEntity entity){
        entity.setState("2");
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    /**
     * 审核不通过
     * @param entity
     * @return
     */
    @PostMapping("/fail")
    public RestResponse fail(FaceAuditEntity entity){
        entity.setState("3");
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    /**
     * 获取门禁列表
     * @return
     */
    @RequestMapping("/selectFaceLock")
    public RestResponse selectFaceLock() {
        RestResponse response=new RestResponse();
        QueryWrapper<FaceLockEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("state","1");
        wrapper.eq("project_id", UoneHeaderUtil.getProjectId());
        List<FaceLockEntity> lockEntities=lockService.list(wrapper);
        List<Map<String,Object>> data=new ArrayList<>();
        for(FaceLockEntity lockEntity:lockEntities){
            Map<String,Object> map=new HashMap<>();
            map.put("name",lockEntity.getAddress());
            map.put("value",lockEntity.getId());
            map.put("selected",false);
//            map.put("localId",lockEntity.getLocalId());
            data.add(map);
        }
        return response.setSuccess(true).setData(data);
    }

    /**
     * 获取授权信息
     * @param entity
     * @return
     */
    @RequestMapping("/getEntranceGuard")
    public RestResponse getEntranceGuard(FaceAuditEntity entity) {
        QueryWrapper<FaceRelEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("audit_id",entity.getId());
        List<FaceRelEntity> relEntities=relService.list(wrapper);
        Set<String> set = new HashSet<>();
        for(FaceRelEntity relEntity:relEntities){
            set.add(relEntity.getLockId());
        }
        return RestResponse.success().setData(set);
    }

    /**
     * 授权
     * @param entity
     * @return
     */
    @PostMapping("/authorization")
    @Transactional
    public RestResponse authorization(FaceAuditEntity entity){
        Long cachedTime = (Long) CacheUtil.get("TRUDIAN_CACHED_TIME");
        Console.log("TRUDIAN_CACHED_TIME:{}",cachedTime);
        Long currentTime = new Date().getTime();
        if(cachedTime !=null){
            // 计算两个时间点之间的差异
            Long duration = currentTime - cachedTime;
            Console.log("缓存时间:{}",duration);
            // 检查是否超过30秒
            if (duration < 60000) {
                Console.log("缓存时间未超过1分钟");
                return RestResponse.failure("门禁重启中，请稍后1分钟再试！");
            }
        }
        FaceAuditEntity faceAudit = faceAuditService.getById(entity.getId());
        //先删除
        QueryWrapper<FaceRelEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("audit_id",entity.getId());
//        List<FaceRelEntity> relEntities = relService.list(wrapper);
//        for(FaceRelEntity relEntity: relEntities){
//            FaceLockEntity lockEntity = lockService.getById(relEntity.getLockId());
//            trudianFegin.delFace(faceAudit.getProjectId(),lockEntity.getLocalId(),faceAudit.getCardSn());
//        }
        boolean res = relService.remove(wrapper);
        //后新增
        for (String eg:entity.getEntranceGuard()) {
            FaceRelEntity relEntity = new FaceRelEntity();
            relEntity.setAuditId(entity.getId());
            relEntity.setLockId(eg);
            relEntity.insert();

            FaceLockEntity lockEntity = lockService.getById(relEntity.getLockId());
            String url = StrUtil.subBefore(faceAudit.getPath(),"&",false);
            trudianFegin.addFace(faceAudit.getProjectId(),lockEntity.getLocalId(),faceAudit.getCardSn(),url);
        }
        CacheUtil.put("TRUDIAN_CACHED_TIME",currentTime);
        return RestResponse.success();
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @UoneLog("删除人脸")
    public RestResponse delete(String id) {
        FaceAuditEntity faceAudit = faceAuditService.getById(id);
        QueryWrapper query = new QueryWrapper<>();
        query.eq("audit_id",id);
        List<FaceRelEntity> relEntityList = faceRelService.list(query);
        for(FaceRelEntity relEntity:relEntityList){
            FaceLockEntity lockEntity = lockService.getById(relEntity.getLockId());
            trudianFegin.delFace(lockEntity.getProjectId(),lockEntity.getLocalId(),faceAudit.getCardSn());
        }
        relService.remove(query);
        faceAuditService.removeById(id);

        return RestResponse.success();
    }

}
