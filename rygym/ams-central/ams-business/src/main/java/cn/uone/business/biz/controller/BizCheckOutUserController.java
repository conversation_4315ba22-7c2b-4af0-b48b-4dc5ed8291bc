package cn.uone.business.biz.controller;


import cn.hutool.json.JSONUtil;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.biz.vo.BizCheckOutUserVo;
import cn.uone.bean.parameter.CheckOutUserPo;
import cn.uone.business.biz.service.IBizCheckOutUserService;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-20
 */
@RestController
@RequestMapping("/biz/checkOutUser")
public class BizCheckOutUserController extends BaseController {

    @Autowired
    private IBizCheckOutUserService bizCheckOutUserService;

    @RequestMapping(value = "/page", method = RequestMethod.GET)
    @UonePermissions(value = LoginType.USER)
    public RestResponse page(Page page, CheckOutUserPo checkOut) {
        IPage<BizCheckOutUserVo> result = bizCheckOutUserService.selectPage(page, checkOut);
        return RestResponse.success().setData(result);
    }

    /**
     * 取消搬离
     * <AUTHOR>
     * @date 2018-12-21 15:50
     * @Param:
     * @return
     */
    @UoneLog("机构员工搬离--取消搬离")
    @PostMapping("/cancelMoveaway")
    public RestResponse cancelMoveaway(@RequestParam("id") String id) throws Exception {
        bizCheckOutUserService.cancelMoveaway(id);
        return RestResponse.success("取消搬离成功！");
    }

    /**
     * 确认搬离
     * <AUTHOR>
     * @date 2018-12-21 15:50
     * @Param:
     * @return
     */
    @UoneLog("机构员工--确认搬离")
    @PostMapping("/sureMoveaway")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse sureMoveaway(@RequestParam("id") String id) throws Exception {
        //状态改成已搬离
        bizCheckOutUserService.sureMoveaway(id);
        return RestResponse.success("确认搬离成功！");
    }

    @UoneLog("机构员工--搬离时固耗、能耗账单显示")
    @PostMapping("/getLifeOrderByMoveaway")
    public RestResponse getLifeOrderByMoveaway(@RequestParam("id") String id) throws Exception {
        RestResponse restResponse = new RestResponse();
        Map<String,Object> data = bizCheckOutUserService.getLifeOrderByMoveaway(id);
        return restResponse.setSuccess(true).setData(data);
    }

    @UoneLog("机构员工--确认搬离")
    @PostMapping("/sureMoveaway2")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse sureMoveaway2(@RequestBody Map<String, Object> param) throws Exception {
        String id = (String) param.get("id");
        BilOrderEntity fixOrder = JSONUtil.toBean(JSONUtil.toJsonStr(param.get("fixOrder")),BilOrderEntity.class);
        BilOrderEntity energyOrder = JSONUtil.toBean(JSONUtil.toJsonStr(param.get("energyOrder")),BilOrderEntity.class);
        bizCheckOutUserService.sureMoveaway(id,fixOrder,energyOrder);
        return RestResponse.success("确认搬离成功！");
    }

}
