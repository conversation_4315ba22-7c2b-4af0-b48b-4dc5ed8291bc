package cn.uone.business.rpt.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.order.AccountTypeEnum;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.bean.entity.business.base.BaseAccountEntity;
import cn.uone.bean.entity.business.bil.BilInvestEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.BilTransferEntity;
import cn.uone.bean.entity.business.report.vo.ConfirmAccountVo;
import cn.uone.bean.parameter.ConfirmAccountPo;
import cn.uone.business.base.service.IBaseAccountService;
import cn.uone.business.bil.service.IBilInvestService;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilTransferService;
import cn.uone.business.kingdee.service.IKingdeeApiService;
import cn.uone.business.rpt.dao.ReportConfirmAccountDao;
import cn.uone.business.rpt.service.IReportConfirmAccountService;
import cn.uone.util.CodeUtil;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Service
public class ReportConfirmAccountServiceImpl  extends ServiceImpl<ReportConfirmAccountDao, ConfirmAccountVo> implements IReportConfirmAccountService {

    @Autowired
    private IBaseAccountService baseAccountService;
    @Autowired
    private IBilOrderItemService itemService;
    @Autowired
    private IBilTransferService bilTransferService;
    @Autowired
    private IBilInvestService investService;
    @Autowired
    private IKingdeeApiService kingdeeApiService;

    @Override
    public IPage<ConfirmAccountVo> findConfirmByCondition(Page page, ConfirmAccountPo confirmAccountPo) {
        if (StrUtil.isNotEmpty(confirmAccountPo.getMerchantId())) {
            BaseAccountEntity account = baseAccountService.getAccountByMerchantId(confirmAccountPo.getMerchantId());
            if(account!=null) {
                confirmAccountPo.setAccountType(account.getAccountType());
            }
        }
        return baseMapper.findConfirmByCondition(page,confirmAccountPo);
    }

    @Override
    public List<ConfirmAccountVo> findConfirmByCondition(ConfirmAccountPo confirmAccountPo) {
        return baseMapper.findConfirmByCondition(confirmAccountPo);
    }

    @Override
    public BigDecimal getArriveTotal(ConfirmAccountPo confirmAccountPo) {
        if (StrUtil.isNotEmpty(confirmAccountPo.getMerchantId())) {
            BaseAccountEntity account = baseAccountService.getAccountByMerchantId(confirmAccountPo.getMerchantId());
            confirmAccountPo.setAccountType(account.getAccountType());
        }
        return baseMapper.getArriveTotal(confirmAccountPo);
    }

    @Override
    public IPage<ConfirmAccountVo> findCcbCondition(Page page, ConfirmAccountPo confirmAccountPo) {
        return baseMapper.findCcbCondition(page,confirmAccountPo);
    }

    @Override
    public List<ConfirmAccountVo> findCcbCondition(ConfirmAccountPo confirmAccountPo) {
        return baseMapper.findCcbCondition(confirmAccountPo);
    }

    @Override
    public BigDecimal getCcbTotal(ConfirmAccountPo confirmAccountPo) {
        return baseMapper.getCcbTotal(confirmAccountPo);
    }

    @Override
    public IPage<ConfirmAccountVo> findUnionPayCondition(Page page, ConfirmAccountPo confirmAccountPo) {
        return baseMapper.findUnionPayCondition(page,confirmAccountPo);
    }

    @Override
    public List<ConfirmAccountVo> findUnionPayCondition(ConfirmAccountPo confirmAccountPo) {
        return baseMapper.findUnionPayCondition(confirmAccountPo);
    }

    @Override
    public BigDecimal getUnionPayTotal(ConfirmAccountPo confirmAccountPo) {
        return baseMapper.getUnionPayTotal(confirmAccountPo);
    }

    @Override
    @Transactional
    public RestResponse confirmAccount(List<String> orderIds, String transferId, Date arriveTime,String bankSerialCode) {
        RestResponse response = new RestResponse();
        //1.更新账单状态 ConfirmAccount
        String code = CodeUtil.codeCreate("CA");
        for(String orderId : orderIds){
            List<BilOrderItemEntity> items = itemService.getItemsByOrderId(orderId, Arrays.asList(OrderItemTypeEnum.RENT.getValue(), OrderItemTypeEnum.SUBSIDY.getValue(), OrderItemTypeEnum.ACTIVITY.getValue()));
            for(BilOrderItemEntity item : items){
                item.setArriveTime(arriveTime);
                item.setArriveCode(code);
                item.updateById();
            }
        }
        Collection<BilInvestEntity> investEntities = investService.listByIds(orderIds);
        investEntities.forEach(item->{
            item.setArriveTime(arriveTime);
            item.setArriveCode(code);
            item.updateById();
        });
        BilTransferEntity transfer  = bilTransferService.getById(transferId);
        transfer.setArriveCode(code);
        transfer.setArriveTime(arriveTime);
        transfer.setBankSerialCode(bankSerialCode);
        transfer.updateById();
        try {
            kingdeeApiService.createReceiptBill(transfer.getId(),"1");
        } catch (Exception e) {
            e.printStackTrace();
            return response.setSuccess(false).setMessage(e.getMessage());
        }
        return response.setSuccess(true).setMessage("确认到账！");
    }

    @Override
    public Date getLatelyPayTime(String merchantId, String transferTime, String transferPayment, String projectId) {
        BaseAccountEntity account = baseAccountService.getAccountByMerchantId(merchantId);
        // 确认款项时 根据账号类型 分别查找不同的sql 查找
        if(AccountTypeEnum.PAY.getValue().equals(account.getAccountType()))
            return baseMapper.getLatelyPayTime(account.getAccountType(), transferTime, transferPayment, projectId);
        else
            return baseMapper.getLatelyPayTimeCz(account.getAccountType(), transferTime, transferPayment, projectId);
    }
}
