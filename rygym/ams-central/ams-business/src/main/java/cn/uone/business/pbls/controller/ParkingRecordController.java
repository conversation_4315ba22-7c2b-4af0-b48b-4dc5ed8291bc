package cn.uone.business.pbls.controller;


import cn.uone.business.pbls.service.IParkingRecordService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-24
 */
@RestController
@RequestMapping("/parkingRecord")
public class ParkingRecordController extends BaseController {

    @Autowired
    private IParkingRecordService parkingRecordService ;

    /**
     * 前端充值缴费接口调用
     */
    /**
     * 查询账户余额
     *
     * @return
     */
    @RequestMapping("/toParkPay")
    public RestResponse getBalance(@RequestParam("applyId") String applyId , @RequestParam("endTime") Date endTime ,
                                   @RequestParam("startTime") Date startTime , @RequestParam("allRent")BigDecimal allRent,
                                   @RequestParam("month") int month,@RequestParam("payWay") String payWay) {
        RestResponse response = new RestResponse();
        String type = "0".equals(payWay)?"微信续费":"钱包续费";
        Map<String,Object> maps = parkingRecordService.toPayParking(applyId,startTime,endTime,allRent,month,type);
        return response.setSuccess(true).setData(maps);
    }

    @RequestMapping("/renewByOffline")
    public RestResponse renewByOffline(@RequestParam("applyId") String applyId , @RequestParam("endTime") Date endTime ,
                                   @RequestParam("startTime") Date startTime , @RequestParam("allRent")BigDecimal allRent,@RequestParam("month") int month) {
        RestResponse response = new RestResponse();
        Map<String,Object> maps = parkingRecordService.toPayParking(applyId,startTime,endTime,allRent,month,"线下续费");
        return response.setSuccess(true).setData(maps);
    }
}

