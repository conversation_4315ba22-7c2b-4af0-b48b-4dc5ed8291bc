package cn.uone.business.dev.controller;


import cn.uone.bean.entity.business.dev.DevMaintainEntity;
import cn.uone.business.dev.service.IDevMaintainService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 维护商表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-19
 */
@RestController
@RequestMapping("/dev-maintain-entity")
public class DevMaintainController extends BaseController {

    @Autowired
    private IDevMaintainService devMaintainService;

    @RequestMapping("/queryDevMaintainList")
    public RestResponse queryDevMaintainList( @RequestBody(required = false) DevMaintainEntity devMaintainEntity) {
        QueryWrapper wrapper=new QueryWrapper();
        List<DevMaintainEntity> list=devMaintainService.list(wrapper);
        return RestResponse.success().setData(list);
    }


}
