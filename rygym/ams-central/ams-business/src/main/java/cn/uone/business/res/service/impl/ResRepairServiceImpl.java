package cn.uone.business.res.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.uone.application.enumerate.MsgTypeEnum;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.source.RepairStateEnum;
import cn.uone.application.enumerate.source.RepairTypeEnum;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.res.*;
import cn.uone.bean.entity.business.res.vo.RepairVo;
import cn.uone.bean.entity.business.res.vo.ResRepairEntityVo;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.res.vo.SelectVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.QywxAgentEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.bean.entity.crm.ZoneEntity;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.res.dao.*;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResRepairService;
import cn.uone.business.sale.service.ISaleCustomerService;
import cn.uone.business.sys.service.ISysDictDataService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.fegin.crm.IQywxAgentFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.fegin.tpi.IQyWechatFegin;
import cn.uone.fegin.tpi.IQywxMessageFegin;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.CodeUtil;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BusinessException;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-24
 */
@Service
@RefreshScope
public class ResRepairServiceImpl extends ServiceImpl<ResRepairDao, ResRepairEntity> implements IResRepairService {

    @Resource
    ResProjectDao projectDao;
    @Resource
    ResPlanPartitionDao planPartitionDao;
    @Resource
    ResSourceDao sourceDao;

    @Resource
    ResRepairLogDao logDao;

    @Resource
    IUserFegin userFegin;
    @Autowired
    private ISysMsgTemplateFegin sysMsgTemplateFegin;
    @Autowired
    private ISysPushMsgService sysPushMsgService;

    @Value("${fastdfs.showPrefix}")
    private String showPrefix;
    @Value("${domeke.swagger.serviceUrl}")
    public String serviceUrl;

    @Resource
    private IQyWechatFegin qyWechatFegin;
    @Autowired
    private IContContractService contractService;
    @Autowired
    private MinioUtil minioUtil;
    @Autowired
    private IQywxMessageFegin qywxMessageFegin;
    @Autowired
    private IQywxAgentFegin qywxAgentFegin;
    @Autowired
    IResProjectParaService resProjectParaService;
    @Autowired
    ISaleCustomerService customerService;
    @Autowired
    ISysDictDataService sysDictDataService;

    @Override
    @Transactional
    public boolean add(ResRepairEntity entity, MultipartFile[] files) throws BusinessException {
        if(entity.getPartitionId()==null || entity.getSourceId()==null){
                   return false;
        }
        String userId = UoneSysUser.id();
        entity.setCode(CodeUtil.codeCreate("GZD"));
        RepairTypeEnum repairType = RepairTypeEnum.REPAIR;//默认为客户报修
        if (LoginType.USER.equals(UoneSysUser.loginType())) {  //LoginType.USER身份是管家,此处条件代表登陆的是管家号
            entity.setUserId(userId);
            if ( RepairTypeEnum.REPAIR.toString().equals(entity.getType())){
                repairType = RepairTypeEnum.REPAIR;
            }else if ( RepairTypeEnum.COMPLAIN.toString().equals(entity.getType())){
                repairType = RepairTypeEnum.COMPLAIN;
            }else if ( RepairTypeEnum.SUGGESTION.toString().equals(entity.getType())){
                repairType = RepairTypeEnum.SUGGESTION;
            }else if ( RepairTypeEnum.SYSREPAIR.toString().equals(entity.getType())){
                repairType = RepairTypeEnum.SYSREPAIR;
            }else if ( RepairTypeEnum.CLEAN.toString().equals(entity.getType())){
                repairType = RepairTypeEnum.CLEAN;
            }

        } else {
            entity.setRenterId(userId);

                System.out.println(RepairTypeEnum.SUGGESTION.getValue());
                System.out.println(entity.getType());
                if(RepairTypeEnum.SUGGESTION.getValue().equals(entity.getType())){
                    repairType = RepairTypeEnum.SUGGESTION;
                }else if(RepairTypeEnum.COMPLAIN.getValue().equals(entity.getType())) {
                    repairType = RepairTypeEnum.COMPLAIN;
                }
                else if (RepairTypeEnum.CLEAN.getValue().equals(entity.getType())){
                    repairType = RepairTypeEnum.CLEAN;
                }

        }
        if (StrUtil.isNotBlank(UoneSysUser.nickName())) {
            entity.setRequester(UoneSysUser.nickName());
        } else {
            entity.setRequester(UoneSysUser.loginName());
        }
        entity.setTel(UoneSysUser.tel());
        entity.setType(repairType.toString());
        RepairStateEnum state = RepairStateEnum.TOASSIGN;
        entity.setState(state.toString());
        //获取报修位置
        entity.setPosition(makePosition(entity));
        if (entity.insert()) {
            if (null != files) {
                for (MultipartFile file : files) {
                    //String url = FileUtil.save(file);
                    String url = minioUtil.save(file);
                    SysFileEntity fileEntity = new SysFileEntity();
                    fileEntity.setFromId(entity.getId());
                    fileEntity.setType(SysFileTypeEnum.REPAIR.getValue());
                    fileEntity.setUrl(url);
                    fileEntity.insert();
                }
            }
            //添加报修日志
            createLog(entity, null, false, false);
            //企业微信通知管家(通知配置的管家）
            try {
                ContContractEntity contract=contractService.getContractBySourceId(entity.getSourceId());
                String managerId=contract.getManager();
                UserEntity manager=userFegin.getUserById(managerId);

                List<Map<String,Object>> managerList = this.getRepairManagerList(entity.getProjectId(),"repair_notify_roles");

                //String content = StrUtil.format("{}您好，有一条新的维修单或投诉建议，单号{}，请您及时查看!", manager.getRealName(),entity.getCode());
                QywxAgentEntity agent = qywxAgentFegin.getByUserId(manager.getId());
                //qywxMessageFegin.sendTextMessage(manager.getQywechat(), agent.getQywxCropId(),agent.getQywxAgentId(),agent.getQywxAgentSecret(),content);
                for (Map<String, Object> user : managerList) {
                    String toUserId = user.get("qywechat").toString();
                    String title = "报事报修";
                    String description = StrUtil.format("<div class=\"gray\">{}</div><div class=\"normal\">报修单号：{}</div><div class=\"highlight\">报修位置：{}</div><div class=\"normal\">报修类型：{}</div><div class=\"highlight\">申报人：{}</div><div class=\"normal\">问题描述：{}</div>",
                            DateUtil.formatDate(new Date()),entity.getCode(),entity.getPosition(),RepairTypeEnum.getNameByValue(entity.getType()),entity.getRequester(),entity.getSummary());

                    String url = serviceUrl+"manage/#/pages/content/home/<USER>/faultRepaire?type="+entity.getType();
                    qywxMessageFegin.sendTextCardMessage(toUserId,agent.getQywxCropId(),agent.getQywxAgentId(),agent.getQywxAgentSecret(),title,description,url);
                    sysPushMsgService.createPushMsg(MsgTypeEnum.DAICHULIKEHUTOUSU.getValue(), toUserId, entity.getCode());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                Thread.sleep(1000); // 延时 1000 毫秒，即 1 秒
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            //分配给我
            if(StrUtil.isNotBlank(entity.getAllowMe())){
                entity.setManager(userFegin.getUserById(entity.getAllowMe()).getRealName());
                entity.setManagerId(entity.getAllowMe());
                entity.setState(RepairStateEnum.TOHANDLE.getValue());
                entity.updateById();
                createLog(entity, null, true, false);
            }else{
//                旧的分配
//                //自动分配
//                UserEntity user = userFegin.getManager(entity.getProjectId(), "home:fault:accept", BizTypeEnum.BIZ02.getValue());
//                if (null != user && StrUtil.isNotBlank(user.getId())) {
////                    boolean permission=userFegin.getUserPermission("home:fault:accept",user.getId());
////                    if(permission){
//                        entity.setManager(user.getRealName());
//                        entity.setManagerId(user.getId());
//                        entity.setState(RepairStateEnum.TOHANDLE.getValue());
//                        entity.updateById();
//                        createLog(entity, null, true, false);
//                            // 发送短信的内容
////                        Map<String, Object> params = new HashMap<String, Object>();
////                        params.put("mobile",user.getTel());
////                        params.put("name",user.getRealName());
////                        params.put("repairCode", entity.getCode());
////                        params.put("template_code", "97213");
////                    }
//                }else{
//                    List<UserEntity> users = userFegin.getManagerListByPermission(entity.getProjectId(), "home:fault:accept", BizTypeEnum.BIZ02.getValue());
//                    for(UserEntity u:users){
//                        //客户投诉
//                        if(repairType.equals(RepairTypeEnum.COMPLAIN.getValue())){
//                            sysPushMsgService.createPushMsg(MsgTypeEnum.KEHUTOUSU.getValue(), u.getId(), entity.getCode());
//                        }else{
//                            sysPushMsgService.createPushMsg(MsgTypeEnum.KEHUBAOXIU.getValue(), u.getId(), entity.getCode());
//                        }
//                    }
//                }
                /**新的分配stare*/
                String allotType = "0";
                ResProjectParaEntity paraEntity= resProjectParaService.getByCodeAndProjectId(ProjectParaEnum.REPAIRS_ALLOCATION.getValue(),entity.getProjectId());
                if(paraEntity != null){
                    allotType = paraEntity.getParamValue();
                }
                if("0".equals(allotType)){
                    entity.setState(RepairStateEnum.TOASSIGN.getValue());//待分配
                    entity.updateById();
//                    createLog(entity, null, true, false);
                    return true;
                }
                List<UserEntity> userList = userFegin.selectManagerListByPermission(entity.getProjectId(),"home:fault:accept");
                Map<String, String> userMap = new HashMap<>();
                for (UserEntity user : userList) {
                    userMap.put(user.getId(), user.getRealName());
                }
                String managerId = null;
                List<String> userIdList = userList.stream().map(UserEntity::getId).collect(Collectors.toList());
                if("1".equals(allotType) && userList !=null &&userList.size() > 0){//自动随机分配
                    Collections.shuffle(userIdList);
                    managerId = userIdList.get(0);
                }else if("2".equals(allotType) && userList !=null &&userList.size() > 0){//自动按最少者分配
                    List<Map<String,Object>> userList2 = this.getRepairManagerList(entity.getProjectId());
                    //List<String> userIdList2 = customerService.selectManagerIdsByCount(entity.getProjectId());
                    //List<String> userIdList3 = userIdList2;
                    //userIdList3.retainAll(userIdList);
                    //userIdList2.removeAll(userIdList3);
                    //userIdList.removeAll(userIdList2);
                    //managerId = userIdList.size()>0?userIdList.get(0):userIdList2.get(0);
                    managerId = userList2.get(0).get("managerId").toString();
                }
                UserEntity user = userFegin.getUserById(managerId);
                entity.setManager(user.getRealName());
                entity.setManagerId(user.getId());
                entity.setState(RepairStateEnum.TOHANDLE.getValue());
                entity.updateById();
                createLog(entity, null, true, false);
             /**新的分配end*/

            }
            //企业微信通知管家(通知签约管家）
            try {
                if(RepairTypeEnum.REPAIR.getValue().equals(entity.getType())){
                    ContContractEntity contract=contractService.getContractBySourceId(entity.getSourceId());
                    String managerId=contract.getManager();
                    UserEntity manager=userFegin.getUserById(managerId);

                    String content = StrUtil.format("运营官{}，您有一条新的维修单，单号{}，请您及时处理!", manager.getRealName(),entity.getCode());
                    QywxAgentEntity agent = qywxAgentFegin.getByUserId(manager.getId());
                    qywxMessageFegin.sendTextMessage(manager.getQywechat(), agent.getQywxCropId(),agent.getQywxAgentId(),agent.getQywxAgentSecret(),content);
                    sysPushMsgService.createPushMsg(MsgTypeEnum.DAICHULIKEHUTOUSU.getValue(), manager.getId(), entity.getCode());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return true;
        }
        return false;
    }


    @Override
    public Map<String, Object> countRepairNum(String projectId) {
        DataScope scope = DataScope.newDataScope(UoneSysUser.id());
        boolean searchAll= userFegin.getUserPermission("res:repair:searchAll",UoneSysUser.id());
        String userId = null;
        if(!searchAll){
            userId = UoneSysUser.id();
        }
        return baseMapper.countRepairNum(scope,userId,projectId);
    }

    @Override
    public Map<String, Object> countRepairNum(Map<String,String> map) {
        DataScope scope = DataScope.newDataScope(UoneSysUser.id());
        //boolean searchAll= userFegin.getUserPermission("res:repair:searchAll",UoneSysUser.id());
        return baseMapper.getStatistics(scope,map);
    }

    /***
     * 创建报修日志
     * @param repair 报修单对象
     * @param note 备注
     * @param isSystem 是否系统处理
     * @param isKeep 是否继续处理
     * @throws BusinessException
     * info不拼接备注,备注保存到remark字段 cazhanghe edit 2024-08-28
     */
    @Override
    public void createLog(ResRepairEntity repair, String note, boolean isSystem, boolean isKeep) throws BusinessException {
        if (null == repair || StrUtil.isBlank(repair.getId())) {
            throw new BusinessException("创建报修日志时,请确定报修单");
        }
        ResRepairLogEntity entity = new ResRepairLogEntity();
        if (LoginType.USER.equals(UoneSysUser.loginType())) {
            entity.setUserId(UoneSysUser.id());
        } else {
            entity.setRenterId(UoneSysUser.id());
        }
        if (StrUtil.isNotBlank(UoneSysUser.nickName())) {
            entity.setRequester(UoneSysUser.nickName());
        } else {
            entity.setRequester(UoneSysUser.loginName());
        }
        entity.setTel(UoneSysUser.tel());
        String info = "";

        if (isSystem) {
            entity.setRequester("系统");
        }
        String cancel = "";
        /*String cancel = note;
        if (StrUtil.isNotBlank(note)) {
            note = ",备注:" + note;
        }*/
        if (StrUtil.isNotBlank(cancel)) {
            cancel = ",取消理由:" + cancel;
        }
        String typeName = RepairTypeEnum.getNameByValue(repair.getType());
        if (RepairStateEnum.TOASSIGN.toString().equals(repair.getState())) {//待分配
            info = entity.getRequester() + " 提交了" + typeName + "。";
        } else if (RepairStateEnum.TOHANDLE.toString().equals(repair.getState())) { //待处理(分配操作)
            info = entity.getRequester() + " 把" + typeName + "分配给" + repair.getManager();
            if (isKeep) {
                info = entity.getRequester() + " 对" + typeName + "进行了处理 ,处理结果 ：继续处理";
            }
        } else if (RepairStateEnum.HANDLE.toString().equals(repair.getState())) { //已处理( 处理操作 )
            info = entity.getRequester() + " 对" + typeName + "进行了处理 ,处理结果 ：已处理";
        } else if (RepairStateEnum.CANCEL.toString().equals(repair.getState())) { //已取消
            info = entity.getRequester() + " 取消了" + typeName + cancel;
        }
        entity.setRepairId(repair.getId());
        entity.setInfo(info);
        entity.setCreateDate(new Date(new Date()
                .getTime() + 1000));
        entity.setRemark(note);//增加保存备注字段
        logDao.insert(entity);
    }

    /**
     * 拼接报修位置
     *
     * @param entity
     * @return
     */
    private String makePosition(ResRepairEntity entity) {
        String projectId = entity.getProjectId();//项目ID
        String partitionId = entity.getPartitionId();//分区ID
        String sourceId = entity.getSourceId();//房源ID
        String position = "";
        if (StrUtil.isNotBlank(sourceId)) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("id", sourceId);
            ResSourceVo sourceVo = sourceDao.getAllInfoById(map);
            return sourceVo.getProjectName() + "-" + sourceVo.getPartitionName() + "-" + sourceVo.getCode();
        }
        if (StrUtil.isNotBlank(projectId)) {
            ResProjectEntity project = projectDao.selectById(projectId);
            position += project.getName();
        }

        if (StrUtil.isNotBlank(partitionId)) {
            ResPlanPartitionEntity partition = planPartitionDao.selectById(partitionId);
            position += "-" + partition.getName();
        }
        return position;
    }

    @Override
    public IPage queryPage(Page page, RepairVo vo) {
        if(vo==null){
            vo=new RepairVo();
        }
        String userId = UoneSysUser.id();
        boolean searchAll= userFegin.getUserPermission("res:repair:searchAll",userId);
        if(!searchAll){
            vo.setUserId(userId);
        }
        vo.setCommentName();
        DataScope dataScope=DataScope.newDataScope(UoneSysUser.id());
        IPage result = baseMapper.queryPage(page,dataScope,vo);

        return result;
    }

    @Override
    public List<ResRepairEntity> queryPage(RepairVo vo) {
        boolean searchAll= userFegin.getUserPermission("res:repair:searchAll",UoneSysUser.id());
        if(!searchAll){
            vo.setUserId(UoneSysUser.id());
        }
        DataScope dataScope=DataScope.newDataScope(UoneSysUser.id());
        return baseMapper.queryPage(dataScope,vo);
    }

    /**
     * @param map
     * @param isScope
     * @return
     */
    @Override
    public List<ZoneEntity> getAreas(Map<String, Object> map , Boolean isScope) {
        List<ZoneEntity> areas;
        if (isScope) {
            DataScope scope = DataScope.newDataScope(UoneSysUser.id());
            scope.setZoneFieldName("id");
            scope.setParAlias("p");
            areas = baseMapper.getAreaByDataScopeAndFilter(scope, map);
        } else {
            areas = baseMapper.getAreaByFilter(map);
        }
        return areas;
    }

    @Override
    public List<SelectVo> projects(Map map) {
        List<SelectVo> list = baseMapper.projects(map);
        return list;
    }

    @Override
    public List<SelectVo> partitions(Map map) {
        List<SelectVo> list = baseMapper.partitions(map);
        return list;
    }

    @Override
    public List<SelectVo> codes(Map map) {
        return baseMapper.codes(map);
    }

    @Override
    public List<SelectVo> getCodes(Map map) {
        return baseMapper.getCodes(map);
    }

    /**
     * 获取对应报修和反馈的图片
     * @param
     * @return
     */
    public List<ResRepairEntityVo> findFileById(List<ResRepairEntity> pages){
        List<ResRepairEntityVo> pages2 = new ArrayList<>();
        for (ResRepairEntity res: pages){
            List file = baseMapper.querySysFile(res.getId());
            ResRepairEntityVo rv = new ResRepairEntityVo();
            BeanUtils.copyProperties(res,rv);
            rv.setList(file);
            if (CollectionUtils.isNotEmpty(rv.getList())){
                List tmp=new ArrayList();
                for (int i=0;i<rv.getList().size();i++){
                    tmp.add(showPrefix+rv.getList().get(i));
                }
                rv.getList().clear();
                rv.getList().addAll(tmp);
            }
            pages2.add(rv);
        }
        return pages2;
    }

    @Override
    public ResRepairEntityVo queryResRepair(String id) {
        ResRepairEntityVo resRepairEntityVo=baseMapper.queryResRepair(id);
        resRepairEntityVo.setCommentName();
        if (CollectionUtils.isNotEmpty(resRepairEntityVo.getList())){
            List tmp=new ArrayList();
            for (int i=0;i<resRepairEntityVo.getList().size();i++){
                tmp.add(showPrefix+resRepairEntityVo.getList().get(i));
            }
            resRepairEntityVo.getList().clear();
            resRepairEntityVo.getList().addAll(tmp);
        }
        return resRepairEntityVo;
    }

    @Override
    @Transactional
    public boolean equipmentRepair(ResRepairEntity entity, MultipartFile[] files) throws BusinessException {
        if(entity.getPartitionId()==null || entity.getSourceId()==null){
            return false;
        }
        entity.setCode(CodeUtil.codeCreate("GZD"));
        RepairTypeEnum repairType = RepairTypeEnum.REPAIR;//默认为客户报修
        entity.setType(repairType.toString());
        RepairStateEnum state = RepairStateEnum.TOASSIGN;
        entity.setState(state.toString());
        //获取报修位置
        if (entity.insert()) {
            if (null != files) {
                for (MultipartFile file : files) {
                    //String url = FileUtil.save(file);
                    String url = minioUtil.save(file);
                    SysFileEntity fileEntity = new SysFileEntity();
                    fileEntity.setFromId(entity.getId());
                    fileEntity.setType(SysFileTypeEnum.REPAIR.getValue());
                    fileEntity.setUrl(url);
                    fileEntity.insert();
                }
            }
            //添加报修日志
            createLog(entity, null, false, false);
            return true;
        }
        return false;
    }

    @Override
    public List<Map<String, Object>> countTotalByMonths(String cityCode, List<String> types){
        return this.baseMapper.countTotalByMonths(cityCode, types);
    }

    @Override
    public List<Map<String, Object>> countRepairByMonths(String cityCode, List<String> types){
        return this.baseMapper.countRepairByMonths(cityCode, types);
    }

    @Override
    public List<ResRepairEntity> getBySourceId(String sourceId,String isClean) {
        return baseMapper.getBySourceId(sourceId,isClean);
    }

    @Override
    public List<Map<String, Object>> getRepairUsersByMap(Map<String, Object> map) {
        return baseMapper.getRepairUsersByMap(map);
    }

    @Override
    public List<Map<String,Object>> getRepairManagerList(String projectId){
        //取码表可分配的角色
        List<Map<String, String>> roleNameList = sysDictDataService.getDictDataList("repair_roles");
        //获取角色对应的管理用户列表
        Map<String,Object> map = Maps.newHashMap();
        if(StrUtil.isBlank(projectId)){
            projectId = UoneHeaderUtil.getProjectId();
        }
        map.put("projectId",projectId);
        map.put("roleNameList",roleNameList);
        List<Map<String,Object>> userList1 = userFegin.getUsersByMap(map);
        //将用户列表转成map<用户id,角色>
        if(userList1 == null || userList1.size() == 0){
            return null;
        }
        Map<String, String> userRoleMap = Maps.newHashMap();
        for (Map<String, Object> mapItem : userList1) {
            String id = mapItem.get("id").toString(); // 获取 v1
            String roleName = mapItem.get("roleName").toString();  // 获取 v2
            userRoleMap.put(id,roleName);
        }
        //根据报修分配统计按顺序获取用户列表2
        Map<String,Object> map2 = Maps.newHashMap();
        map2.put("userIdList",userList1);
        List<Map<String,Object>> userList2 = this.getRepairUsersByMap(map2);
        //遍历用户列表2，赋值角色
        for(Map<String,Object> map3 : userList2){
            String roleName = userRoleMap.get(map3.get("id").toString());
            String realName = map3.get("realName").toString();
            map3.put("name",realName+"|"+roleName);
        }
        return userList2;
    }

    @Override
    public List<Map<String,Object>> getRepairManagerList(String projectId,String dictType){
        //取码表可分配的角色
        List<Map<String, String>> roleNameList = sysDictDataService.getDictDataList(dictType);
        Console.log("roleNameList:{}", JSONUtil.toJsonStr(roleNameList));
        //获取角色对应的管理用户列表
        Map<String,Object> map = Maps.newHashMap();
        map.put("projectId",projectId);
        map.put("roleNameList",roleNameList);
        List<Map<String,Object>> userList = userFegin.getUsersByMap(map);
        Console.log("userList:{}", JSONUtil.toJsonStr(userList));
        return userList;
    }
}
