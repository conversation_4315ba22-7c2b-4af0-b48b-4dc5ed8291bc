package cn.uone.business.bil.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.application.enumerate.order.DiscountTypeEnum;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.business.bil.BilDiscountEntity;
import cn.uone.bean.entity.business.bil.BilDiscountLogEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.vo.BilDiscountSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.res.ResSourceConfigureAuditEntity;
import cn.uone.bean.entity.business.res.ResSourceConfigureEntity;
import cn.uone.business.Guomi.service.IGuomiService;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalDetailService;
import cn.uone.business.bil.service.IBilDiscountLogService;
import cn.uone.business.bil.service.IBilDiscountService;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.cont.service.IContParService;
import cn.uone.business.flow.domain.dto.FlowTaskDto;
import cn.uone.business.flow.service.IActReDeploymentService;
import cn.uone.business.flow.service.IFlowTaskService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.CodeUtil;
import cn.uone.util.ExcelRender;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.SafeCompute;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/bil/discount")
public class BilDiscountController extends BaseController {

    @Autowired
    private IBilDiscountService bilDiscountService;
    @Autowired
    private IBilDiscountLogService bilDiscountLogService;
    @Autowired
    private IApprovalCommitService approvalCommitService;
    @Autowired
    private IApprovalDetailService approvalDetailService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IBilOrderItemService bilOrderItemService;
    @Autowired
    private IContParService contParService;
    @Autowired
    private IGuomiService guomiService;

    //流程发起注册
    @Resource
    private IActReDeploymentService iActReDeploymentService;

    @Autowired
    IFlowTaskService flowTaskService;

    /**
     * 优惠券列表
     *
     * @return
     */
    @RequestMapping("/getListByUser")
    public RestResponse getListByUser(@RequestParam(required = false) String keyWord,
                                      @RequestParam(required = false) String isFinish,
                                      @RequestParam(required = false) String isUsed,
                                      @RequestParam(required = false) String approvalState,
                                      @RequestParam(required = false) String projectId) {
        RestResponse response = new RestResponse();
        String userId =  UoneSysUser.ShiroUser().getPermissions().contains("coupon:seeAll") ? "" : UoneSysUser.id();
        return response.setSuccess(true).setData(bilDiscountService.findByCondition(isFinish, keyWord,approvalState,projectId,isUsed, userId));
    }

    /**
     * 分页查询 后台
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(@ModelAttribute Page page,
                                       @RequestParam(required = false) String discountType,
                                       @RequestParam(required = false) String keyWord,
                                       @RequestParam(required = false) String isFinish,
                                       @RequestParam(required = false) String isUsed,
                                       @RequestParam(required = false) String approvalState) {
        RestResponse response = new RestResponse();
        return response.setSuccess(true).setData(bilDiscountService.findByCondition(page, discountType, keyWord, isFinish, isUsed, approvalState));
    }

    /**
     * 获取信息
     *
     * @param id
     * @return
     */
    @RequestMapping("/getInfo")
    public RestResponse getInfo(@RequestParam("id") String id) {
        RestResponse response = new RestResponse();
        try {
            Map<String, Object> resultDataMap = new HashMap<>();
            //优惠信息
            BilDiscountEntity discount = bilDiscountService.getById(id);
            resultDataMap.put("discount", discount);
            if(StrUtil.isNotEmpty(discount.getProjectId())){
                resultDataMap.put("project", resProjectService.getById(discount.getProjectId()));
            }
            List<HashMap> list = bilDiscountLogService.findByCondition(discount.getId());
            resultDataMap.put("items", list);
            //审批信息
            if(StringUtils.isNotBlank(discount.getApprovalState())){
                //获取流程审批
                ApprovalCommitEntity commitEntity = approvalCommitService.getByFun(discount.getId(),null);
                if(ObjectUtil.isNotNull(commitEntity)){
                    resultDataMap.put("approval",approvalDetailService.getApprovalInfo(commitEntity,discount.getId(),commitEntity.getType()));
                }
            }
            response.setSuccess(true).setData(resultDataMap);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public RestResponse save(BilDiscountEntity bilDiscountEntity) {
        RestResponse response = new RestResponse();
        try {
            String discountId = bilDiscountEntity.getId();
            if (StrUtil.isNotEmpty(discountId)) {//编辑
                QueryWrapper<BilDiscountLogEntity> wrapper = new QueryWrapper();
                wrapper.eq("discount_id", bilDiscountEntity.getId());
                List<BilDiscountLogEntity> bilTransferEntityList = bilDiscountLogService.list(wrapper);
                if (!bilTransferEntityList.isEmpty()) {
                    response.setSuccess(false).setMessage("该优惠券已发放不能编辑！");
                    return response;
                }
                BilDiscountEntity old = bilDiscountService.getById(bilDiscountEntity.getId());
                if (ObjectUtil.isNotNull(old.getApprovalState()) && !ApprovalStateEnum.TOBESUBMIT.getValue().equals(old.getApprovalState())) {
                    response.setSuccess(false).setMessage("优惠券不是待提交状态不能编辑！");
                    return response;
                }
                /*ApprovalCommitEntity approvalCommit = approvalCommitService.getByParam(bilDiscountEntity.getId(), ApprovalTypeEnum.COUPON.getValue());
                if (ObjectUtil.isNotNull(approvalCommit)) {
                    response.setSuccess(false).setMessage("申请优惠券不能编辑！");
                    return response;
                }*/
            }else{//新增
                //获取该code最大值
                String prefix = CodeUtil.getBilDiscountCode(bilDiscountEntity.getDiscountType());
                String code = bilDiscountService.getMaxCode(prefix);
                bilDiscountEntity.setProjectId(UoneHeaderUtil.getProjectId());
                bilDiscountEntity.setCode(prefix + code);
                bilDiscountEntity.setUsedNum(0);
                //bilDiscountEntity.setApprovalState(ApprovalStateEnum.COMPLETE.getValue());
                bilDiscountEntity.setApprovalState("0");//新增时默认待审核 caizhenghe edit 2024-08-17
            }
            if (DiscountTypeEnum.LIFE.getValue().equals(bilDiscountEntity.getDiscountType())) {
                response.setSuccess(false).setMessage("由于不便于财务对账生活费用优惠券暂时停用！");
                return response;
            }
            bilDiscountService.saveOrUpdate(bilDiscountEntity);
            if(StrUtil.isEmpty(discountId)){//如果是新增,则发起优惠券审批流程
                discountId = bilDiscountEntity.getId();
                String userId = UoneSysUser.id();
                String projectId = UoneHeaderUtil.getProjectId();
                iActReDeploymentService.bilDiscountAduitStart(discountId,userId,projectId);//发起优惠券审批流程
            }
            response.setSuccess(true).setMessage("保存成功！").setData(bilDiscountEntity);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 关联优惠券
     */
    @RequestMapping("/relationDiscount")
    public RestResponse relationDiscount(@RequestParam String ids, @RequestParam String discountId) {
        RestResponse response = new RestResponse();
        BilDiscountEntity bilDiscountEntity = bilDiscountService.getById(discountId);
        if (validate(bilDiscountEntity)) {
            return response.setSuccess(false).setMessage("优惠券已过期！");
        }
        for (String orderId : ids.split(",")) {
            BilOrderEntity order = bilOrderService.getById(orderId);
            response = guomiService.handleGuomiDiscount(order);
            if (response.getSuccess()) {
                //log
                BilDiscountLogEntity entity = new BilDiscountLogEntity();
                String code = bilDiscountLogService.getMaxCode(bilDiscountEntity.getCode());
                entity.setCode(code);
                entity.setState(BaseConstants.BOOLEAN_OF_FALSE);
                entity.setDiscountId(discountId);
                entity.setUserId(order.getPayerId());
                entity.setOrderId(order.getId());
                bilDiscountLogService.save(entity);
                //处理账单优惠
                handleOrderDiscount(order, entity);
            } else {
                return response;
            }

        }
        return response.setSuccess(true);
    }

    /**
     * 处理账单优惠
     *
     * @param order
     * @param log
     */
    private void handleOrderDiscount(BilOrderEntity order, BilDiscountLogEntity log) {
        BilDiscountEntity discount = bilDiscountService.getById(log.getDiscountId());
        //discount更新优惠券使用次数
        discount.setUsedNum(discount.getUsedNum() + 1);
        bilDiscountService.updateById(discount);
        BigDecimal discountAmount = discount.getDiscountAmount();
        //item添加优惠子账单
        BilOrderItemEntity timeItem = bilOrderItemService.getSumZujin(order.getId());
        BilOrderItemEntity item = new BilOrderItemEntity();
        item.setOrderItemType(OrderItemTypeEnum.ACTIVITY.getValue());
        item.setStartTime(timeItem.getStartTime());
        item.setEndTime(timeItem.getEndTime());
        item.setOrderId(order.getId());
        item.setPayment(discountAmount.negate());
        item.insert();
        //order
        order.setPayablePayment(SafeCompute.sub(order.getPayment(), discountAmount));
        order.setDiscountLogId(log.getId());
        order.updateById();
    }

    private boolean validate(BilDiscountEntity bilDiscountEntity) {
        boolean isDisuse = false;
        //大于结束时间
        if (bilDiscountEntity.getEndDate().compareTo(DateUtil.beginOfDay(new Date())) == -1) {
            isDisuse = true;
        }
        return isDisuse;
    }

    /**
     * 优惠券申请
     */
    @RequestMapping("/discountApply")
    public RestResponse discountApply(BilDiscountEntity bilDiscountEntity) {
        RestResponse response = new RestResponse();
        try {
            if (DiscountTypeEnum.LIFE.getValue().equals(bilDiscountEntity.getDiscountType())) {
                response.setSuccess(false).setMessage("由于不便于财务对账生活费用优惠券暂时停用！");
                return response;
            }
            //获取该code最大值
            String prefix = CodeUtil.getBilDiscountCode(bilDiscountEntity.getDiscountType());
            String code = bilDiscountService.getMaxCode(prefix);
            //bilDiscountEntity.setTotalNum(1);
            bilDiscountEntity.setCode(prefix + code);
            bilDiscountEntity.setUsedNum(0);
            bilDiscountEntity.setApprovalState(ApprovalStateEnum.TOBESUBMIT.getValue());
            bilDiscountService.saveOrUpdate(bilDiscountEntity);
            //提交流程审批
            approvalCommitService.addOrUpdateComit(new Expression(bilDiscountEntity.getId(),ApprovalTypeEnum.COUPON.getValue()));
            response.setSuccess(true).setMessage("保存成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public RestResponse delete(String id) {
        RestResponse response = new RestResponse();
        try {
            QueryWrapper<BilDiscountLogEntity> wrapper = new QueryWrapper();
            wrapper.eq("discount_id", id);
            List<BilDiscountLogEntity> bilTransferEntityList = bilDiscountLogService.list(wrapper);
            if (!bilTransferEntityList.isEmpty()) {
                response.setSuccess(false).setMessage("该优惠券已发放不能删除！");
                return response;
            }
            ApprovalCommitEntity approvalCommit = approvalCommitService.getCommitEntity(id, ApprovalTypeEnum.COUPON.getValue());
            if (ObjectUtil.isNotNull(approvalCommit)) {
                approvalCommitService.removeById(approvalCommit.getId());
            }
            bilDiscountService.removeById(id);
            response.setSuccess(true).setMessage("删除成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 分页查询 后台
     *
     * @return
     */
    @RequestMapping("/selectBilDiscountTotal")
    public RestResponse selectBilDiscountTotal(@ModelAttribute Page page,
                                               @RequestParam(required = false) String keyWord,
                                               @RequestParam(required = false) String createName,
                                               @RequestParam(required = false) String payerName,
                                               @RequestParam(required = false) String approvalState,
                                               @RequestParam(required = false) String state,
                                               @RequestParam(required = false) Date startDate,
                                               @RequestParam(required = false) Date endDate,
                                               @RequestParam(required = false) Date payStartDate,
                                               @RequestParam(required = false) Date payEndDate) {
        Map<String, Object> map = new HashMap<>();
        searchToMap(keyWord, createName, payerName, approvalState, state, payStartDate, payEndDate, startDate, endDate, map);
        return RestResponse.success().setData(bilDiscountService.selectBilDiscountTotal(page, map));
    }

    public void searchToMap(@RequestParam(required = false) String keyWord, @RequestParam(required = false) String createName, @RequestParam(required = false) String payerName, @RequestParam(required = false) String approvalState, @RequestParam(required = false) String state, @RequestParam(required = false) Date payStartDate, @RequestParam(required = false) Date payEndDate, @RequestParam(required = false) Date startDate, @RequestParam(required = false) Date endDate, Map<String, Object> map) {
        map.put("projectId", UoneHeaderUtil.getProjectId());
        if (StrUtil.isNotEmpty(keyWord)) {
            map.put("keyWord", keyWord);
        }
        if (StrUtil.isNotEmpty(createName)) {
            map.put("createName", createName);
        }
        if (StrUtil.isNotEmpty(payerName)) {
            map.put("payerName", payerName);
        }
        if (StrUtil.isNotEmpty(approvalState)) {
            map.put("approvalState", approvalState);
        }
        if (StrUtil.isNotEmpty(state)) {
            map.put("state", state);
        }
        if (ObjectUtil.isNotNull(payStartDate)) {
            map.put("payStartDate", DateUtil.endOfDay(payStartDate));
        }
        if (ObjectUtil.isNotNull(payEndDate)) {
            map.put("payEndDate", DateUtil.endOfDay(payEndDate));
        }
        if (ObjectUtil.isNotNull(startDate)) {
            map.put("startCreateDate", DateUtil.endOfDay(startDate));
        }
        if (ObjectUtil.isNotNull(endDate)) {
            map.put("endCreateDate", DateUtil.endOfDay(endDate));
        }
    }

    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getOrderList")
    public RestResponse getOrderList(Page page, BilOrderSearchVo bilOrderSearchVo) {
        return RestResponse.success().setData(bilDiscountService.getOrderList(page, bilOrderSearchVo));
    }

    @RequestMapping(value = {"/export"}, method = RequestMethod.POST)
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) String keyWord,
                       @RequestParam(required = false) String createName,
                       @RequestParam(required = false) String payerName,
                       @RequestParam(required = false) String approvalState,
                       @RequestParam(required = false) String state,
                       @RequestParam(required = false) Date payStartDate,
                       @RequestParam(required = false) Date payEndDate,
                       @RequestParam(required = false) Date startDate,
                       @RequestParam(required = false) Date endDate,
                       @RequestParam(required = false) List<String> ids) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        Map<String, Object> map = new HashMap<>();
        searchToMap(keyWord, createName, payerName, approvalState, state, payStartDate, payEndDate, startDate, endDate, map);
        map.put("projectId", UoneHeaderUtil.getProjectId());
        if (ObjectUtil.isNotNull(ids)) {
            map.put("ids", ids);
        }
        List<BilDiscountSearchVo> list = bilDiscountService.selectBilDiscountTotal(map);
        list.forEach(e -> {
            e.setApprovalState(ApprovalStateEnum.getNameByValue(e.getApprovalState()));
            e.setDiscountType(DiscountTypeEnum.getNameByValue(e.getDiscountType()));
            e.setState(BaseConstants.BOOLEAN_OF_TRUE.equals(e.getState()) ? "已使用" : "未使用");
        });
        beans.put("vo", list);
        ExcelRender.me("/excel/export/bilDiscount.xlsx").beans(beans).render(response);
    }


    /**
     * 优惠券待审核列表
     * 添加优惠券的待审核记录
     * <AUTHOR> 2024-08-17
     */
    @RequestMapping("/auditPage")
    public RestResponse auditPage(Page page, @RequestParam(required = false) String discountType,
                                  @RequestParam(required = false) String keyWord,
                                  @RequestParam(required = false) String isFinish,
                                  @RequestParam(required = false) String isUsed,
                                  @RequestParam(required = false) String approvalState,@RequestParam("isSelf") boolean isSelf) {
        Map<String,Object> taskMap = Maps.newHashMap();
        try {
            taskMap = flowTaskService.todoMap(isSelf,"优惠券审批");
            taskMap.put("1","1");
        } catch (Exception e) {
            e.printStackTrace();
        }
        //BilDiscountEntity entity = new BilDiscountEntity();
        Set<String> ids = taskMap.keySet();
        /*entity.setIds(ids);
        entity.setApprovalState("0");
        entity.setProjectId(UoneHeaderUtil.getProjectId());*/
        //审核状态默认为待审核状态
        IPage<BilDiscountEntity> pageList = bilDiscountService.findByConditionAndIds(page, discountType, keyWord, isFinish, isUsed, "0",ids);
        for(BilDiscountEntity bilDiscountEntity:pageList.getRecords()){
            FlowTaskDto flowTask = (FlowTaskDto) taskMap.get(bilDiscountEntity.getId());
            if(flowTask!=null){
                bilDiscountEntity.setTaskId(flowTask.getTaskId());
                bilDiscountEntity.setProcInsId(flowTask.getProcInsId());
                bilDiscountEntity.setDeployId(flowTask.getDeployId());
            }
        }
        return RestResponse.success().setData(pageList);
    }

    /**
     * 修改审核状态
     * 优惠券审批流程审批通过和审批不通过,修改优惠券表审核状态
     * <AUTHOR> 2024-08-18
     */
    @RequestMapping("/changeAuditStatus")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse changeAuditStatus(String id,String approvalState) {
        RestResponse response = new RestResponse();
        try {
            BilDiscountEntity bilDiscountEntity = bilDiscountService.getById(id);
            bilDiscountEntity.setApprovalState(approvalState);
            //configureAuditEntity.updateById();
            bilDiscountService.updateById(bilDiscountEntity);
            return response.setSuccess(true).setMessage("保存成功");
        } catch (Exception e){
            e.printStackTrace();
            return response.setSuccess(false).setMessage("保存失败");
        }
    }

    /**
     * 优惠券审批列表
     * 已办列表
     * @return
     * <AUTHOR> 2024-08-17
     */
    @RequestMapping("/finishedDiscountAuditList")
    public RestResponse finishedDiscountAuditList(Page page, @RequestParam(required = false) String discountType,
                                                  @RequestParam(required = false) String keyWord,
                                                  @RequestParam(required = false) String isFinish,
                                                  @RequestParam(required = false) String isUsed,
                                                  @RequestParam(required = false) String approvalState,
                                                  @RequestParam("isSelf") boolean isSelf,
                                                  @RequestParam("definitionName") String definitionName) {
        RestResponse response = new RestResponse();
        Map<String,Object> taskMap = Maps.newHashMap();
        try {
            taskMap = flowTaskService.finishedSignMap(isSelf,definitionName);
            taskMap.put("1","1");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> ids = taskMap.keySet();
        //entity.setIds(ids);
        IPage<BilDiscountEntity> pageList = bilDiscountService.findByConditionAndIds(page, discountType, keyWord, isFinish, isUsed, approvalState,ids);
        for(BilDiscountEntity bilDiscountEntity:pageList.getRecords()){
            FlowTaskDto flowTask = (FlowTaskDto) taskMap.get(bilDiscountEntity.getId());
            if(flowTask!=null){
                bilDiscountEntity.setTaskId(flowTask.getTaskId());
                bilDiscountEntity.setProcInsId(flowTask.getProcInsId());
                bilDiscountEntity.setDeployId(flowTask.getDeployId());
            }
        }
        return response.setSuccess(true).setData(pageList);
    }

}
