package cn.uone.business.rpt.service;

import cn.uone.bean.entity.business.rpt.RptRevenueEntity;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueDataVo;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueSearchVo;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 营收确认明显表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-16
 */
public interface IRptRevenueService extends IService<RptRevenueEntity> {

    IPage<String> selectContractIdsByMap(Page page, RptRevenueSearchVo vo);

    List<RptRevenueVo> findByCondition(RptRevenueSearchVo vo);

    RptRevenueVo selectRevenueTotalByMap(RptRevenueSearchVo vo);

    void batchPut(RptRevenueSearchVo vo);

    void batchDeduct(RptRevenueSearchVo vo);

    String importData(List<RptRevenueDataVo> data);

    List<RptRevenueEntity> selectRevenueForKingdee(String year,String projectId);

    List<RptRevenueEntity> getOrderType();
}
