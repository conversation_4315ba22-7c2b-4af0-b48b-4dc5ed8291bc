package cn.uone.business.cont.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.ContractStateEnum;
import cn.uone.application.enumerate.contract.PayTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.bean.entity.business.bil.BilDiscountEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.PriceStrategyEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractFileRelEntity;
import cn.uone.bean.entity.business.cont.ContContractGjjtqEntity;
import cn.uone.bean.entity.business.cont.vo.ContContractVo;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.common.service.IXmgjjService;
import cn.uone.business.cont.service.IContContractFileRelService;
import cn.uone.business.cont.service.IContContractGjjtqService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.flow.domain.dto.FlowTaskDto;
import cn.uone.business.flow.service.IActReDeploymentService;
import cn.uone.business.flow.service.IFlowTaskService;
import cn.uone.cache.util.CacheUtil;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.CodeUtil;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import cn.uone.web.base.BaseController;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 合同审批文件关联表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@RestController
@RequestMapping("/cont-contract-file-rel-entity")
public class ContContractFileRelController extends BaseController {

    @Autowired
    IContContractFileRelService contContractFileRelService;
    @Autowired
    IFlowTaskService flowTaskService;
    @Autowired
    private IActReDeploymentService actReDeploymentService;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private IContContractGjjtqService contContractGjjtqService;
    @Autowired
    IXmgjjService xmgjjService;

    @RequestMapping("/selectContFilePage")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse selectContFilePage(Page page) throws Exception {
        Map<String,Object> queryMap = Maps.newHashMap();
        queryMap.put("renterId",UoneSysUser.id());
        IPage<ContContractFileRelEntity> iPage = contContractFileRelService.selectContFilePage(page, queryMap);
        return RestResponse.success().setData(iPage);
    }

    /**
     * 授权书审核
     */
    @RequestMapping("/toAuditPage")
    public RestResponse toAuditPage(Page page,@RequestParam(required = false) String projectId,@RequestParam("isSelf") boolean isSelf) {
        Map<String,Object> taskMap = Maps.newHashMap();
        try {
            taskMap = flowTaskService.todoMap(isSelf,"公积金授权委托书审批");
            taskMap.put("1","1");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> ids = taskMap.keySet();
        Map<String,Object> queryMap = Maps.newHashMap();
        queryMap.put("contractIds",ids);
        queryMap.put("state","1");
        queryMap.put("projectId",StrUtil.isBlank(projectId)?UoneHeaderUtil.getProjectId():projectId);
        IPage<ContContractFileRelEntity> iPage = contContractFileRelService.selectContFilePage(page, queryMap);
        for(ContContractFileRelEntity contContractFileRelEntity:iPage.getRecords()){
            FlowTaskDto flowTask = (FlowTaskDto) taskMap.get(contContractFileRelEntity.getContractId());
            if(flowTask!=null){
                contContractFileRelEntity.setTaskId(flowTask.getTaskId());
                contContractFileRelEntity.setProcInsId(flowTask.getProcInsId());
                contContractFileRelEntity.setDeployId(flowTask.getDeployId());
            }
        }
        return RestResponse.success().setData(iPage);
    }

    /**
     * 改变审核状态
     */
    @RequestMapping("/changeAuditStatus")
    public RestResponse changeAuditStatus(String id,String auditStatus) throws Exception {
        ContContractFileRelEntity contContractFileRel = contContractFileRelService.getById(id);
        contContractFileRel.setState(auditStatus);
        contContractFileRel.updateById();
        String contractId = contContractFileRel.getContractId();
        if("6".equals(auditStatus)){
            ContContractEntity contract = contContractService.getById(contractId);
            RenterEntity renter = renterFegin.getById(contract.getSignerId());
            List<ContContractGjjtqEntity> tqList = Lists.newArrayList();
            Map<String,Object> map = Maps.newHashMap();
            map.put("contractId",contractId);
            map.put("orderType", OrderTypeEnum.RENT.getValue());
            map.put("payState",PayStateEnum.NOPAY.getValue());
            map.put("yearMonth", DateUtil.format(new Date(),"yyyyMM"));
            List<BilOrderVo> orderList = bilOrderService.getOrderList(map);
            for(BilOrderVo order: orderList){
                ContContractGjjtqEntity entity = new ContContractGjjtqEntity();
                entity.setContractId(contractId);
                entity.setPaperCode(contract.getPaperCode());
                entity.setPayType(contract.getPayType());
                entity.setOrderId(order.getId());
                entity.setOrderCode(order.getCode());
                entity.setOrderPayablePayment(order.getPayablePayment());
                entity.setStartDate(order.getStartDate());
                entity.setEndDate(order.getEndDate());
                entity.setState("0");
                entity.setZjhm(renter.getIdNo());
                entity.setXingming(renter.getName());
                tqList.add(entity);
            }
            contContractGjjtqService.saveBatch(tqList,tqList.size());
            xmgjjService.xmgjjdkTask(contractId);
        }else{
            contContractGjjtqService.remove(new QueryWrapper<ContContractGjjtqEntity>().eq("contract_id",contractId));
        }
        return RestResponse.success();
    }

    @RequestMapping("/auditedPage")
    public RestResponse auditedPage(Page page,@RequestParam(required = false) String projectId,@RequestParam("isSelf") boolean isSelf, @RequestParam(required = false) String keyWord) {
        Map<String,Object> taskMap = Maps.newHashMap();
        try {
            taskMap = flowTaskService.finishedSignMap(isSelf,"公积金授权委托书审批");
            taskMap.put("1","1");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> contractIds = taskMap.keySet();
        Map<String, Object> map = Maps.newHashMap();
        map.put("project_id", StrUtil.isNotBlank(projectId)?projectId:UoneHeaderUtil.getProjectId());
        map.put("contractIds", contractIds);
        if(StrUtil.isNotBlank(keyWord)){
            map.put("keyWord", keyWord);
        }
        IPage<ContContractFileRelEntity> iPage = contContractFileRelService.selectContFilePage(page, map);
        for(ContContractFileRelEntity record:iPage.getRecords()){
            FlowTaskDto flowTask = (FlowTaskDto) taskMap.get(record.getContractId());
            record.setTaskId(flowTask.getTaskId());
            record.setTaskName(flowTask.getTaskName());
            record.setProcInsId(flowTask.getProcInsId());
            record.setDeployId(flowTask.getDeployId());
        }
        return RestResponse.success().setData(iPage);
    }

    @UoneLog("取消授权")
    @UonePermissions(LoginType.CUSTOM)
    @CacheLock(prefix = "cancelContFile", expire = 30)
    @Transactional
    @RequestMapping(value = "/cancelContFile", method = RequestMethod.POST)
    public RestResponse cancelContFile(@RequestParam("contFileId") String contFileId,@RequestParam("tel") String tel,@RequestParam("smsCode") String smsCode){
        String code = (String) CacheUtil.get(BaseConstants.TEL_SMS_HEADER + tel);
        if(!smsCode.equals(code)){
            return RestResponse.failure("验证码错误");
        }
        ContContractFileRelEntity contFile = contContractFileRelService.getById(contFileId);
        if(contFile != null){
            contFile.setState(ContractStateEnum.STATUS_CANCEL.getValue());
            contContractFileRelService.updateById(contFile);
        }

        //发起取消公积金抵扣授权审批流程
        //actReDeploymentService.cancelContractFileRelStart(contractId,contFile.getRenterId());
        return RestResponse.success("取消成功");
    }

    /**
     * 取消授权书审核
     */
    @RequestMapping("/toAuditCancelPage")
    public RestResponse toAuditCancelPage(Page page,@RequestParam("isSelf") boolean isSelf) {
        Map<String,Object> taskMap = Maps.newHashMap();
        try {
            taskMap = flowTaskService.todoMap(isSelf,"取消公积金授权委托审批");
            taskMap.put("1","1");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> ids = taskMap.keySet();
        Map<String,Object> queryMap = Maps.newHashMap();
        queryMap.put("contractIds",ids);
        queryMap.put("state","1");
        queryMap.put("projectId",UoneHeaderUtil.getProjectId());
        IPage<ContContractFileRelEntity> iPage = contContractFileRelService.selectContFilePage(page, queryMap);
        for(ContContractFileRelEntity contContractFileRelEntity:iPage.getRecords()){
            FlowTaskDto flowTask = (FlowTaskDto) taskMap.get(contContractFileRelEntity.getContractId());
            if(flowTask!=null){
                contContractFileRelEntity.setTaskId(flowTask.getTaskId());
                contContractFileRelEntity.setProcInsId(flowTask.getProcInsId());
                contContractFileRelEntity.setDeployId(flowTask.getDeployId());
            }
        }
        return RestResponse.success().setData(iPage);
    }

    @RequestMapping("/auditedCancelPage")
    public RestResponse auditedCancelPage(Page page,@RequestParam(required = false) String projectId,@RequestParam("isSelf") boolean isSelf, @RequestParam(required = false) String keyWord) {
        Map<String,Object> taskMap = Maps.newHashMap();
        try {
            taskMap = flowTaskService.finishedSignMap(isSelf,"取消公积金授权委托审批");
            taskMap.put("1","1");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> contractIds = taskMap.keySet();
        Map<String, Object> map = Maps.newHashMap();
        map.put("project_id", StrUtil.isNotBlank(projectId)?projectId:UoneHeaderUtil.getProjectId());
        map.put("contractIds", contractIds);
        if(StrUtil.isNotBlank(keyWord)){
            map.put("keyWord", keyWord);
        }
        IPage<ContContractFileRelEntity> iPage = contContractFileRelService.selectContFilePage(page, map);
        for(ContContractFileRelEntity record:iPage.getRecords()){
            FlowTaskDto flowTask = (FlowTaskDto) taskMap.get(record.getContractId());
            record.setTaskId(flowTask.getTaskId());
            record.setTaskName(flowTask.getTaskName());
            record.setProcInsId(flowTask.getProcInsId());
            record.setDeployId(flowTask.getDeployId());
        }
        return RestResponse.success().setData(iPage);
    }

}
