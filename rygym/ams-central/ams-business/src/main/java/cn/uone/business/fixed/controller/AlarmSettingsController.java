package cn.uone.business.fixed.controller;


import cn.uone.bean.entity.business.fixed.AlarmSettingsEntity;
import cn.uone.business.fixed.service.IAlarmSettingsService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 固定资产告警设置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@RestController
@RequestMapping("/fixed/fixed-alarmSettings-entity")
public class AlarmSettingsController extends BaseController {

    @Autowired
    private IAlarmSettingsService iAlarmSettingsService;

    /*@Autowired
    private IUserService userService;*/

    @GetMapping("/getPageList")
    public RestResponse getPageList(Page page, AlarmSettingsEntity alarmSettings) {
        IPage<AlarmSettingsEntity> iPage = iAlarmSettingsService.page(page, alarmSettings);
        return RestResponse.success().setData(iPage);
    }

    /**
     * 获取信息
     *
     * @param id 主键
     * @return 资产告警设置管理列表
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        AlarmSettingsEntity info = iAlarmSettingsService.getById(id);
        return RestResponse.success().setData(info);
    }

    /**
     * 新增
     *
     * @param alarmSettings 参数
     * @return 资产告警设置管理列表
     */
    @PostMapping("/save")
    public RestResponse save(AlarmSettingsEntity alarmSettings) {
        String projectId = UoneHeaderUtil.getProjectId();//取默认的小区id
        alarmSettings.setProjectId(projectId);
        if(iAlarmSettingsService.save(alarmSettings)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    /**
     * 修改
     *
     * @param alarmSettings 参数
     * @return 资产告警设置管理列表
     */
    @PostMapping("/edit")
    public RestResponse edit(AlarmSettingsEntity alarmSettings) {
        if(iAlarmSettingsService.updateById(alarmSettings)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return 资产告警设置管理列表
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        if(iAlarmSettingsService.removeByIds(ids)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    /*@RequestMapping("/importAlarmSettings")
    public RestResponse importalarmSettings(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return RestResponse.failure("请选择上传文件");
        }
        try {
            List<alarmSettingsVo> rfidEntities  = ExcelDataUtil.importData(file.getInputStream(), alarmSettingsVo.class);
            if(CollectionUtils.isEmpty(rfidEntities)){
                return RestResponse.failure("读取excel异常");
            }else {
                //查重
                List<String> codes = rfidEntities.stream().map(alarmSettingsVo::getCode).collect(Collectors.toList());
                QueryWrapper<AlarmSettingsEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("t_fixed_rfid.code",codes);

                //单独String集合
                List<String> collect = codes.stream().filter(i -> !Objects.equals(i, ""))               // list 对应的 Stream 并过滤""
                        .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum)) // 获得元素出现频率的 Map，键为元素，值为元素出现的次数
                        .entrySet().stream()                       // 所有 entry 对应的 Stream
                        .filter(e -> e.getValue() > 1)         // 过滤出元素出现次数大于 1 (重复元素）的 entry
                        .map(Map.Entry::getKey)                // 获得 entry 的键（重复元素）对应的 Stream
                        .collect(Collectors.toList());
                if(!collect.isEmpty()&& collect.get(0)!=null){
                    return RestResponse.failure("标签编号:"+collect+"重复了");
                }

                List<AlarmSettingsEntity> rfidEntityList = iAlarmSettingsService.list(queryWrapper);
                if(!rfidEntityList.isEmpty()){
                    List<String> codes2 = rfidEntityList.stream().map(AlarmSettingsEntity::getCode).collect(Collectors.toList());
                    return RestResponse.failure("标签编号:"+codes2+"=已存在");
                }
                int i =0;
                List<AlarmSettingsEntity> list = new ArrayList<>();
                for(alarmSettingsVo vo :rfidEntities){
                    i++;
                    if(StringUtils.isEmpty(vo.getCode())){
                        return RestResponse.failure("第:"+i+"行,标签编号不能为空");
                    }
                    if(StringUtils.isEmpty(vo.getLabelSta())){
                        vo.setLabelSta("1");
                    }else {
                        //状态 1=未使用,2=已使用,3=报废
                        if("未使用".equals(vo.getLabelSta())){
                            vo.setLabelSta("1");
                        }else if("已使用".equals(vo.getLabelSta())){
                            vo.setLabelSta("2");
                        }else if("报废".equals(vo.getLabelSta())){
                            vo.setLabelSta("3");
                        }else {
                            vo.setLabelSta("1");
                        }
                    }
                    AlarmSettingsEntity alarmSettings = new AlarmSettingsEntity();
                    BeanUtil.copyProperties(vo,alarmSettings);
                    list.add(alarmSettings);
                }
                ialarmSettingsService.saveBatch(list);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return RestResponse.failure("读取excel异常");
        }
        return RestResponse.success("导入成功");
    }*/

    @UoneLog("导出资产告警设置模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        ExcelRender.me("/excel/import/alarmSettings.xls").beans(beans).render(response);
    }


}
