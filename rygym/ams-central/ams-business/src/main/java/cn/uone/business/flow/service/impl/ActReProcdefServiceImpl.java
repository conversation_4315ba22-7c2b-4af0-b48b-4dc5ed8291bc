package cn.uone.business.flow.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.flow.ActReProcdefEntity;
import cn.uone.bean.entity.business.flow.SysFormEntity;
import cn.uone.bean.entity.business.flow.SysListenerEntity;
import cn.uone.bean.entity.business.flow.vo.FlowProcDefDto;
import cn.uone.business.flow.dao.ActReProcdefDao;
import cn.uone.business.flow.service.IActReProcdefService;
import cn.uone.business.flow.service.ISysDeployFormService;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Service
public class ActReProcdefServiceImpl  extends ServiceImpl<ActReProcdefDao, ActReProcdefEntity> implements IActReProcdefService {

    @Resource
    private ISysDeployFormService iSysDeployFormService;

    @Override
    public IPage<FlowProcDefDto> page(Page page, ActReProcdefEntity entity) {
        HashMap<String,Object> map = new HashMap();
        if(ObjectUtil.isNotEmpty(entity.getName())){
            map.put("name",entity.getName());
        }
        map.put("projectId", UoneHeaderUtil.getProjectId());
        IPage<FlowProcDefDto> dataList = baseMapper.selectDeployList(page,map);
        // 加载挂表单
        for (FlowProcDefDto procDef : dataList.getRecords()) {
            SysFormEntity sysForm = iSysDeployFormService.selectSysDeployFormByDeployId(procDef.getDeploymentId());
            if (Objects.nonNull(sysForm)) {
                procDef.setFormName(sysForm.getFormName());
                procDef.setFormId(sysForm.getFormId());
            }
        }
        return dataList;
    }

}
