package cn.uone.business.cont.service.impl;

import cn.uone.bean.entity.business.cont.ContTalentCheckEntity;
import cn.uone.bean.entity.business.cont.vo.ContTalentCheckVo;
import cn.uone.business.cont.dao.ContTalentCheckDao;
import cn.uone.business.cont.service.IContTalentCheckService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Service
public class ContTalentCheckServiceImpl extends ServiceImpl<ContTalentCheckDao, ContTalentCheckEntity> implements IContTalentCheckService {

    @Override
    public ContTalentCheckVo selectWithId(String id) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        return baseMapper.selectWithId(map);
    }

    @Override
    public List<ContTalentCheckVo> query(Map<String, Object> query) {
        List<ContTalentCheckVo> list = baseMapper.query(query);
        return list;
    }


}
