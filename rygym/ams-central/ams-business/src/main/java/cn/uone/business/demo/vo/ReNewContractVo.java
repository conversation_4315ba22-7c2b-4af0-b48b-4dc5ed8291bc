package cn.uone.business.demo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain=true)
public class ReNewContractVo implements Serializable {

   // private String contractCode;


    private String projectId;

    private String sourceId;

    private String roomCode;


    private String ownerName;


    private Date startTime;


    private Date endTime;


    private BigDecimal price;


    private String payType;


    private String state;


}
