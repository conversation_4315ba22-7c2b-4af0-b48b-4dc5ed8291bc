package cn.uone.business.res.dao;

import cn.uone.bean.entity.business.res.ResSourcePublishEntity;
import cn.uone.bean.entity.business.res.vo.HousesourseVo;
import cn.uone.bean.entity.business.res.vo.ResSourcePublishSearchVo;
import cn.uone.bean.entity.business.res.vo.ResSourcePublishVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface ResSourcePublishDao extends BaseMapper<ResSourcePublishEntity> {
    IPage<ResSourcePublishVo> list(Page page, DataScope dataScope, @Param("map") Map<String, Object> map);

    List<ResSourcePublishVo> template(DataScope dataScope, @Param("map") ResSourcePublishSearchVo map);

    void batchUpdate(@Param("map") Map<String, Object> map);

    ResSourcePublishEntity getBySourceId(@Param("sourceId") String sourceId,@Param("type") String type);

    HousesourseVo thirdPartyInfo(@Param("map") Map<String, Object> map);

    List<HousesourseVo> exsitXYCommunity(@Param("projectId") String projectId, @Param("type") String type);

    List<HousesourseVo> exsitXYLayout(@Param("houseTypeId") String houseTypeId, @Param("type") String type);
}
