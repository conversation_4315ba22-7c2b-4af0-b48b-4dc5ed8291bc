package cn.uone.business.mogo.service.impl;

import cn.uone.bean.entity.business.mogo.MogoManagerEntity;
import cn.uone.business.mogo.dao.MogoManagerDao;
import cn.uone.business.mogo.service.IMogoManagerService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-05
 */
@Service
public class MogoManagerServiceImpl extends ServiceImpl<MogoManagerDao, MogoManagerEntity> implements IMogoManagerService {

    @Override
    public MogoManagerEntity getByTel(String tel) {
        return baseMapper.getByTel(tel);
    }
}
