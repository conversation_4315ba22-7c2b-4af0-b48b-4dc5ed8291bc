package cn.uone.business.pbls.dao;

import cn.uone.bean.entity.business.pbls.RenovationApplyEntity;
import cn.uone.bean.entity.business.pbls.vo.RenovationApplyVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
public interface RenovationApplyDao extends BaseMapper<RenovationApplyEntity> {

    IPage<RenovationApplyVo> selectRenovationApplyByMap(Page page, @Param("map")Map<String, Object> map, DataScope dataScope);
}
