package cn.uone.business.dev.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.ZDYSupplierTypeEnum;
import cn.uone.application.enumerate.source.DevEnum;
import cn.uone.bean.entity.business.dev.*;
import cn.uone.bean.entity.business.dev.vo.DevPropertyDeviceEntityVo;
import cn.uone.bean.entity.business.dev.vo.DevPropertyDeviceImportVo;
import cn.uone.bean.entity.business.res.ResPlanPartitionEntity;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.dev.service.*;
import cn.uone.business.res.service.IResPlanPartitionService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.fegin.bus.IDevPropertyDeviceFegin;
import cn.uone.fegin.tpi.IMeterFegin;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.CacheParam;
import cn.uone.web.util.ExcelDataUtil;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Api(value = "物业设备服务", tags = {"物业设备操作"})
@RestController
@RequestMapping("/dev-property-device-entity")
@RefreshScope
public class DevPropertyDeviceController extends BaseController implements IDevPropertyDeviceFegin {

    @Autowired
    private IDevPropertyDeviceService devPropertyDeviceService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    private IDevClassService devClassService;
    @Autowired
    private IDevSupplierService devSupplierService;
    @Autowired
    private IDevMaintainService devMaintainService;
    @Value("${property.owner}")
    private String powner;//权属方
    @Autowired
    private IDevAuthorizationService devAuthorizationService;
    @Autowired
    private IResPlanPartitionService resPlanPartitionService;

    @Autowired
    private IMeterFegin meterFegin;

    @ApiOperation(value = "物业设备添加/编辑")
    @RequestMapping("/addOrUpdateDevPropertyDevice")
    @CacheLock(prefix = "addOrUpdateDevPropertyDevice")
    public RestResponse addOrUpdateDevPropertyDevice(@CacheParam @RequestBody DevPropertyDeviceEntity devPropertyDeviceEntity) {

        devPropertyDeviceEntity.setProjectId(UoneHeaderUtil.getProjectId());
        String str = checkFun(devPropertyDeviceEntity);
        if (StringUtils.isNotBlank(str)) {
            return RestResponse.failure(str);
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("code", devPropertyDeviceEntity.getCode());
        if (StrUtil.isNotBlank(devPropertyDeviceEntity.getId())) {
            wrapper.ne("id", devPropertyDeviceEntity.getId());
        }
        DevPropertyDeviceEntity entity = devPropertyDeviceService.getOne(wrapper);
        if (ObjectUtil.isNotNull(entity)) {
            return RestResponse.failure("该设备编号已经存在");
        }
        //devPropertyDeviceEntity.setState(DevEnum.DEV_STATE_NORMAL.getValue());
        devPropertyDeviceService.saveOrUpdate(devPropertyDeviceEntity);
        return RestResponse.success("保存成功");
    }

    @RequestMapping("/queryDevPropertyDevice")
    public RestResponse queryDevPropertyDevice( @RequestBody DevPropertyDeviceEntity devPropertyDeviceEntity) {
        if (devPropertyDeviceEntity!=null){
            devPropertyDeviceEntity.setProjectId(UoneHeaderUtil.getProjectId());
        }
        List<DevPropertyDeviceEntityVo> list = devPropertyDeviceService.queryDevPropertyDevice(devPropertyDeviceEntity);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/queryDevPropertyDevicePage")
    public RestResponse queryDevPropertyDevicePage(Page page, DevPropertyDeviceEntityVo devPropertyDeviceEntityVo ) {
        if (devPropertyDeviceEntityVo==null){
            devPropertyDeviceEntityVo=new DevPropertyDeviceEntityVo();
        }
        devPropertyDeviceEntityVo.setProjectId(UoneHeaderUtil.getProjectId());
        IPage<DevPropertyDeviceEntityVo> pages = devPropertyDeviceService.queryDevPropertyDevicePage(page, devPropertyDeviceEntityVo);
        //附加条件：如果门锁已有授权记录，则供应商不可变更，设备编号不可变更，型号不可变更（注：除了这3个字段外的其他字段均可修改）
        DevPropertyDeviceEntityVo tmpVo=null;
        DevAuthorizationEntity devAuthorizationEntity=null;
        QueryWrapper queryWrapper=null;
        for (int i=0;i<pages.getRecords().size();i++){
            queryWrapper=new QueryWrapper();
            tmpVo=pages.getRecords().get(i);
            queryWrapper.eq("property_device_id",tmpVo.getId());
            devAuthorizationEntity=devAuthorizationService.getOne(queryWrapper);
            if (devAuthorizationEntity!=null){
                pages.getRecords().get(i).setChangestate("0");
            }else{
                pages.getRecords().get(i).setChangestate("1");
            }
        }
        return RestResponse.success().setData(pages);
    }

    @RequestMapping("/delDevPropertyDevice")
    @CacheLock(prefix = "delDevPropertyDevice")
    public RestResponse delDevPropertyDevice(@CacheParam(name = "id") @RequestParam(value = "id",required = false) String id) {
        if (StringUtils.isBlank(id)) {
            return RestResponse.failure("主键id不能为空");
        }
        //判断不能删除的条件
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("property_device_id",id);
        List auList=devAuthorizationService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(auList)){
            return  RestResponse.failure("存在门锁授权记录的设备不允许删除!");
        }
        devPropertyDeviceService.removeById(id);
        return RestResponse.success("删除成功！");
    }

    @RequestMapping("/export")
    public void export(HttpServletResponse response) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        ExcelRender.me("/excel/export/propertyDevice.xlsx").beans(beans).render(response);
    }

    @PostMapping(value = "/import")
    public RestResponse importExcelDev(@RequestParam("file") MultipartFile multipartFile, HttpServletRequest request) throws Exception {
        Set<String> deviceCodeSet = new HashSet();
        Set<String> deviceNameSet = new HashSet();
        Set<String> supplierNameSet = new HashSet();
        Set<String> maintainNameSet = new HashSet();
        StringBuilder sb = new StringBuilder();
        InputStream is = multipartFile.getInputStream();
        List<DevPropertyDeviceImportVo> tmpList = ExcelDataUtil.importData(is, DevPropertyDeviceImportVo.class);//原生对象没有get和set转换不了
        if (CollectionUtils.isEmpty(tmpList)) {
            return RestResponse.failure("表格不能为空");
        }
        QueryWrapper queryWrapper = null;
        DevPropertyDeviceEntityVo vo = null;
        String resultStr = checkExcel(deviceCodeSet, deviceNameSet, supplierNameSet, maintainNameSet, tmpList);
        if (StringUtils.isNotBlank(resultStr)) {
            return RestResponse.failure(resultStr);
        }
        //设备编号验证
        if (deviceCodeSet.size() != tmpList.size()) {
            return RestResponse.failure("表格中存在重复的设备编号");
        }
        List<DevPropertyDeviceEntityVo> list = new ArrayList<>();
        DevPropertyDeviceEntityVo devPropertyDeviceEntityVo = null;
        for (int i = 0; i < tmpList.size(); i++) {
            devPropertyDeviceEntityVo = new DevPropertyDeviceEntityVo();
            devPropertyDeviceEntityVo.setSn(tmpList.get(i).getSn());
            devPropertyDeviceEntityVo.setCode(tmpList.get(i).getCode());
            devPropertyDeviceEntityVo.setState(tmpList.get(i).getState());
            devPropertyDeviceEntityVo.setSummary(tmpList.get(i).getSummary());
            if (StrUtil.isNotEmpty(tmpList.get(i).getPartition())) {
                ResPlanPartitionEntity planPartitionEntity = resPlanPartitionService.getEntityByName(tmpList.get(i).getPartition());
                if (ObjectUtil.isNull(planPartitionEntity)) {
                    return RestResponse.failure("表格中填写了不存在的区域名称");
                } else {
                    devPropertyDeviceEntityVo.setPartitionId(planPartitionEntity.getId());
                }
            }
            if ("UONE".equals(tmpList.get(i).getPropertyOwner())) {
                devPropertyDeviceEntityVo.setPropertyOwner("1");
            } else if ("业主".equals(tmpList.get(i).getPropertyOwner())) {
                devPropertyDeviceEntityVo.setPropertyOwner("2");
            } else if ("投资方".equals(tmpList.get(i).getPropertyOwner())) {
                devPropertyDeviceEntityVo.setPropertyOwner("3");
            } else if ("其他".equals(tmpList.get(i).getPropertyOwner())) {
                devPropertyDeviceEntityVo.setPropertyOwner("4");
            } else {
                return RestResponse.failure("表格中填写了不存在的权属方");
            }
            ////excel导出的价格格式为string，因此要转成BigDecimal
            devPropertyDeviceEntityVo.setPrice(new BigDecimal(tmpList.get(i).getPrice()));
            //excel导出的日期格式为string，因此要转成Date
            DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
            Date date = fmt.parse(tmpList.get(i).getEnableTime());
            devPropertyDeviceEntityVo.setEnableTime(date);
            if ("1个月".equals(tmpList.get(i).getServiceLife())) {
                devPropertyDeviceEntityVo.setServiceLife("1");
            } else if ("3个月".equals(tmpList.get(i).getServiceLife())) {
                devPropertyDeviceEntityVo.setServiceLife("3");
            } else if ("6个月".equals(tmpList.get(i).getServiceLife())) {
                devPropertyDeviceEntityVo.setServiceLife("6");
            } else if ("12个月".equals(tmpList.get(i).getServiceLife())) {
                devPropertyDeviceEntityVo.setServiceLife("12");
            } else {
                return RestResponse.failure("表格中填写了不正确格式的使用年限");
            }
            devPropertyDeviceEntityVo.setBrand(tmpList.get(i).getBrand());
            devPropertyDeviceEntityVo.setDeviceName(tmpList.get(i).getDeviceName());
            devPropertyDeviceEntityVo.setSupplierName(tmpList.get(i).getSupplierName());
            devPropertyDeviceEntityVo.setMaintainName(tmpList.get(i).getMaintainName());
            devPropertyDeviceEntityVo.setProjectName(tmpList.get(i).getProjectName());
            list.add(devPropertyDeviceEntityVo);
        }
        checkCode(deviceCodeSet, sb);
        //设备分类验证
        checkDevClass(deviceNameSet, sb);
        //供应商检验
        checkSupplier(supplierNameSet, sb);
        //维护商检验
        checkMaintain(maintainNameSet, sb);
        //如果不为空就存在的不合法数据
        if (StringUtils.isNotBlank(sb.toString())) {
            return RestResponse.failure(sb.toString());
        }
        //以上数据验证，以下数据保存
        saveData(list);
        return RestResponse.success();
    }

    public void saveData(List<DevPropertyDeviceEntityVo> list) {
        DevPropertyDeviceEntityVo vo;
        QueryWrapper queryWrapper;
        DevPropertyDeviceEntity devPropertyDeviceEntity;DevClassEntity devClassEntity=null;
        DevSupplierEntity devSupplierEntity=null;
        DevMaintainEntity devMaintainEntity=null;
        List<DevPropertyDeviceEntity> saveList=new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            vo = list.get(i);
            vo.getDeviceName();
            queryWrapper=new QueryWrapper();
            queryWrapper.eq("name",vo.getDeviceName());
            queryWrapper.eq("type",DevEnum.DEV_TYPE_PRO.getValue());
            devClassEntity= devClassService.getOne(queryWrapper);

            queryWrapper=new QueryWrapper();
            queryWrapper.eq("name",vo.getSupplierName());
            devSupplierEntity=devSupplierService.getOne(queryWrapper);

            queryWrapper=new QueryWrapper();
            queryWrapper.eq("name",vo.getMaintainName());
            devMaintainEntity=devMaintainService.getOne(queryWrapper);

            devPropertyDeviceEntity=new DevPropertyDeviceEntity();
            devPropertyDeviceEntity.setProjectId(UoneHeaderUtil.getProjectId());
            devPropertyDeviceEntity.setPartitionId(vo.getPartitionId());
            devPropertyDeviceEntity.setClassId(devClassEntity.getId());
            devPropertyDeviceEntity.setSn(vo.getSn());
            devPropertyDeviceEntity.setCode(vo.getCode());
            devPropertyDeviceEntity.setSupplierId(devSupplierEntity.getId());
            devPropertyDeviceEntity.setState(vo.getState());
            devPropertyDeviceEntity.setSummary(vo.getSummary());
            devPropertyDeviceEntity.setPropertyOwner(vo.getPropertyOwner());
            devPropertyDeviceEntity.setMaintainId(devMaintainEntity.getId());
            devPropertyDeviceEntity.setBrand(vo.getBrand());
            devPropertyDeviceEntity.setPrice(vo.getPrice());
            devPropertyDeviceEntity.setServiceLife(vo.getServiceLife());
            devPropertyDeviceEntity.setEnableTime(vo.getEnableTime());

            saveList.add(devPropertyDeviceEntity);
        }
        devPropertyDeviceService.saveBatch(saveList);
    }

    public void checkMaintain(Set<String> maintainNameSet, StringBuilder sb) {
        boolean ft=true;
        boolean sbft=false;
        QueryWrapper queryWrapper;
        queryWrapper=new QueryWrapper();
        queryWrapper.in("name",maintainNameSet);
        List<DevMaintainEntity> dmList=devMaintainService.list(queryWrapper);
        if (dmList.size()!=maintainNameSet.size()){
            DevMaintainEntity devMaintainEntity=null;
            for (String name:maintainNameSet){
                for (int i=0;i<dmList.size();i++){
                    devMaintainEntity=dmList.get(i);
                    if (name.equals(devMaintainEntity.getName())){
                        ft=false;
                        break;
                    }
                }
                if (ft){
                    sbft=true;
                    sb.append(name);
                }
            }
            if (sbft) {
                sb.append("该维护商不存在!\n");
            }
        }
    }

    public void checkSupplier(Set<String> supplierNameSet, StringBuilder sb) {
        boolean ft=true;
        boolean sbft=false;
        QueryWrapper queryWrapper;
        queryWrapper=new QueryWrapper();
        queryWrapper.in("name",supplierNameSet);
        List<DevSupplierEntity> dsList=devSupplierService.list(queryWrapper);
        if (dsList.size()!=supplierNameSet.size()){
            DevSupplierEntity devSupplierEntity=null;
            for (String name:supplierNameSet){
                for (int i=0;i<dsList.size();i++){
                    devSupplierEntity=dsList.get(i);
                    if (name.equals(devSupplierEntity.getName())){
                        ft=false;
                        break;
                    }
                }
                if (ft){
                    sbft=true;
                    sb.append(name);
                }
            }
            if (sbft) {
                sb.append("该供应商不存在!\n");
            }
        }
    }

    public void checkDevClass(Set<String> deviceNameSet, StringBuilder sb) {
        boolean ft=true;
        boolean sbft=false;
        QueryWrapper queryWrapper;
        queryWrapper = new QueryWrapper();
        queryWrapper.in("name", deviceNameSet);
        queryWrapper.eq("type", DevEnum.DEV_TYPE_PRO.getValue());
        List<DevClassEntity> dcList = devClassService.list(queryWrapper);
        if (dcList.size() != deviceNameSet.size()) {
            for (String className : deviceNameSet) {
                for (int j = 0; j < dcList.size(); j++) {
                    if (className.equals(dcList.get(j).getName())) {
                        ft = false;
                        break;
                    }
                }
                if (ft) {
                    sbft = true;
                    sb.append(className);
                }
            }
            if (sbft) {
                sb.append("该设备分类不存在!\n");
            }
        }
    }

    public void checkCode(Set<String> deviceCodeSet, StringBuilder sb) {
        QueryWrapper queryWrapper;
        queryWrapper = new QueryWrapper();
        queryWrapper.in("code", deviceCodeSet);
        List<DevPropertyDeviceEntity> dpdeList = devPropertyDeviceService.list(queryWrapper);
        DevPropertyDeviceEntity devPropertyDeviceEntity = null;
        if (!CollectionUtils.isEmpty(dpdeList)) {
            for (int i = 0; i < dpdeList.size(); i++) {
                if (i != 0) {
                    sb.append(",");
                }
                devPropertyDeviceEntity = dpdeList.get(i);
                sb.append(devPropertyDeviceEntity.getCode());
            }
            sb.append("该设备编号已存在!\n");
        }
    }

    public String checkExcel(Set<String> deviceCodeSet, Set<String> deviceNameSet, Set<String> supplierNameSet, Set<String> maintainNameSet, List<DevPropertyDeviceImportVo> list) {
        DevPropertyDeviceImportVo vo;
        String resultStrAll = "";
        ResProjectEntity resProjectEntity = resProjectService.getById(UoneHeaderUtil.getProjectId());
        for (int i = 0; i < list.size(); i++) {
            String resultStr = "第" + (i + 3) + "行：\r";
            vo = list.get(i);
            if (StringUtils.isBlank(vo.getProjectName())) {
                resultStr += "项目名称不能为空!\n";
            } else {
                if (!resProjectEntity.getName().equals(vo.getProjectName())) {
                    resultStr += "项目名称请填写对应页面中选择的名称!\n";
                }
            }
            if (StringUtils.isBlank(vo.getDeviceName())) {
                resultStr += "设备分类不能为空!\n";
            } else {
                deviceNameSet.add(vo.getDeviceName());
            }
            if (StringUtils.isBlank(vo.getCode())) {
                resultStr += "设备编号不能为空!\n";
            } else {
                deviceCodeSet.add(vo.getCode());
            }
            if (StringUtils.isBlank(vo.getSupplierName())) {
                resultStr += "供应商名称不能为空!\n";
            } else {
                supplierNameSet.add(vo.getSupplierName());
            }
            if (StringUtils.isBlank(vo.getMaintainName())) {
                resultStr += "维护商不能为空!\n";
            } else {
                maintainNameSet.add(vo.getMaintainName());
            }
            if (StringUtils.isBlank(vo.getPropertyOwner()) || !powner.contains(vo.getPropertyOwner())) {
                resultStr += "权属方不能为空或权属方不存在!\n";
            }
            if (StringUtils.isBlank(vo.getSummary())) {
                resultStr += "位置描述不能为空!\n";
            }
            if (StringUtils.isBlank(vo.getBrand())) {
                resultStr += "品牌不能为空!\n";
            }
            if (StringUtils.isBlank(vo.getPrice())) {
                resultStr += "价格不能为空!\n";
            }
            if (StringUtils.isBlank(vo.getServiceLife())) {
                resultStr += "使用年限不能为空!\n";
            }
            if (StringUtils.isBlank(vo.getEnableTime())) {
                resultStr += "启用时间不能为空!\n";
            }
            if (StringUtils.isBlank(vo.getSn())) {
                resultStr += "型号不能为空!\n";
            }
            if (StringUtils.isBlank(vo.getState())) {
                resultStr += "设备状态不能为空!\n";
            }
            if (!("第" + (i + 3) + "行：").equals(resultStr)) {
                resultStrAll += resultStr + "\n;\n";
            }
        }
        return resultStrAll;
    }

    public String checkFun(DevPropertyDeviceEntity devPropertyDeviceEntity) {
        String str = "";
        if (devPropertyDeviceEntity == null) {
            return "请求对象为空";
        }
        if (StringUtils.isBlank(devPropertyDeviceEntity.getProjectId())) {
            str = "所属项目不能为空";
        }
        if (StringUtils.isBlank(devPropertyDeviceEntity.getCode())) {
            str = "设备编号不能为空";
        }
        if (StringUtils.isBlank(devPropertyDeviceEntity.getClassId())) {
            str = "设备分类不能为空";
        }
//        if (StringUtils.isBlank(devPropertyDeviceEntity.getState())) {
//            str = "设备状态不能为空";
//        }
        if (StringUtils.isBlank(devPropertyDeviceEntity.getPropertyOwner())) {
            str = "权属方不能为空";
        }
        if (StringUtils.isBlank(devPropertyDeviceEntity.getSupplierId())) {
            str = "供应商不能为空";
        }
        if (StringUtils.isBlank(devPropertyDeviceEntity.getMaintainId())) {
            str = "维护商不能为空";
        }
        if (StringUtils.isBlank(devPropertyDeviceEntity.getBrand())) {
            str = "品牌不能为空";
        }
        if (StringUtils.isBlank(devPropertyDeviceEntity.getSn())) {
            str = "型号不能为空";
        }
        if (devPropertyDeviceEntity.getPrice() == null || devPropertyDeviceEntity.getPrice().compareTo(BigDecimal.ZERO) != 1) {
            str = "价格需大于0";
        }
        if (devPropertyDeviceEntity.getEnableTime() == null) {
            str = "启用时间不能为空";
        }
        if (devPropertyDeviceEntity.getServiceLife() == null) {
            str = "使用年限不能为空";
        }
        if (StringUtils.isBlank(devPropertyDeviceEntity.getSummary())) {
            str = "位置描述不能为空";
        }
        return str;
    }

    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, String keyWord, @RequestParam(value = "type", required = false) String type, @RequestParam(value = "propertyDeviceId", required = false) String propertyDeviceId) {
        RestResponse response = new RestResponse();
        Map<String, Object> map = Maps.newHashMap();
        if (StringUtils.isNotBlank(keyWord)) {
            map.put("keyWord", keyWord);
        }
        if (StringUtils.isNotBlank(type)) {
            map.put("type", type);
        }
        if (StringUtils.isNotBlank(propertyDeviceId)) {
            map.put("propertyDeviceId", propertyDeviceId);
        }
        IPage<RenterEntity> list = devPropertyDeviceService.findByCondition(page, map);
        return response.setSuccess(true).setData(list);
    }

    //修改手机号修改小兔开门对应的信息
    @Override
    @RequestMapping("/changeTelToXt")
    public RestResponse changeTelToXt(String renterId, String tel, String newTel) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("renter_id", renterId);
        List<DevAuthorizationEntity> list = devAuthorizationService.list(wrapper);
        String propertyDeviceId;
        RestResponse response = new RestResponse();
        if (list.size() > 0) {
            //两遍for循环是因为小兔冻结接口参数无效会全部删除导致，因此先全删再全加
            for (DevAuthorizationEntity devAuthorizationEntity :
                    list) {
                propertyDeviceId = devAuthorizationEntity.getPropertyDeviceId();
                DevPropertyDeviceEntity entity = devPropertyDeviceService.getById(propertyDeviceId);
                //把旧号码对应的冻结.
                response = meterFegin.doorFrozen(entity.getCode(), ZDYSupplierTypeEnum.XTKM.getValue(), tel);
                if (!response.getSuccess()) {
                    return RestResponse.failure("对旧号码的门禁冻结失败，门禁为：" + entity.getCode() + ";原因为：" + response.getMessage());
                }

            }
            for (DevAuthorizationEntity devAuthorizationEntity :
                    list) {
                propertyDeviceId = devAuthorizationEntity.getPropertyDeviceId();
                DevPropertyDeviceEntity entity = devPropertyDeviceService.getById(propertyDeviceId);
                //给新号码授权
                response = meterFegin.doorAuthority(entity.getCode(), ZDYSupplierTypeEnum.XTKM.getValue(), newTel);
                if (!response.getSuccess()) {
                    return RestResponse.failure("对新号码的门禁授权失败，门禁为：" + entity.getCode() + ";原因为：" + response.getMessage());
                }
            }
        }
        return RestResponse.success();
    }
}
