package cn.uone.business.bil.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ApiTypeEnum;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.application.enumerate.DataFromEnum;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.InvoiceTypeEnum;
import cn.uone.application.enumerate.contract.PayTypeEnum;
import cn.uone.application.enumerate.order.InvoiceStateEnum;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.order.PayWayEnum;
import cn.uone.application.enumerate.order.TransferTypeEnum;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalDetailEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.business.bil.BilDiscountEntity;
import cn.uone.bean.entity.business.bil.BilOrderConfirmEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.BilOrderPayInfoEntity;
import cn.uone.bean.entity.business.bil.BilTransferEntity;
import cn.uone.bean.entity.business.bil.vo.BilEmpOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilOrderVo;
import cn.uone.bean.entity.business.bil.vo.DailyOrderVo;
import cn.uone.bean.entity.business.bil.vo.OrderConfirmPayVo;
import cn.uone.bean.entity.business.bil.vo.OrderImportVo;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractInfoEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.cont.vo.ContContractVo;
import cn.uone.bean.entity.business.invoice.vo.InvoiceBuyerVo;
import cn.uone.bean.entity.business.report.InvoiceEntity;
import cn.uone.bean.entity.business.res.ResProjectCompanyEntity;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResProjectParaEntity;
import cn.uone.bean.entity.business.res.ResSourceConfigureEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.business.urge.UrgePayRecordEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.entity.crm.SysCompanyEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.bean.entity.tpi.fadada.ReqExtsign;
import cn.uone.bean.entity.tpi.fadada.ReqUploadDocs;
import cn.uone.business.Guomi.service.IGuomiService;
import cn.uone.business.apro.dao.ApprovalCommitDao;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalDetailService;
import cn.uone.business.bil.dao.BilOrderDao;
import cn.uone.business.bil.service.IBilDiscountLogService;
import cn.uone.business.bil.service.IBilOrderAutoService;
import cn.uone.business.bil.service.IBilOrderConfirmService;
import cn.uone.business.bil.service.IBilOrderItemService;
import cn.uone.business.bil.service.IBilOrderPayInfoService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.bil.service.IBilTransferService;
import cn.uone.business.biz.service.IBizAccountService;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.cont.service.IContContractInfoService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.cont.service.IContParService;
import cn.uone.business.cont.service.IContRentLadderService;
import cn.uone.business.cont.service.IContTempService;
import cn.uone.business.flow.domain.dto.FlowTaskDto;
import cn.uone.business.flow.service.IFlowTaskService;
import cn.uone.business.res.service.IResProjectCompanyService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceConfigureService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.rpt.service.IReportInvoiceService;
import cn.uone.business.sale.service.ISaleCustomerService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.fegin.bus.IBilOrderFegin;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysCompanyFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.crm.ISysParaFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.fegin.crm.IZzctSysMsgTemplateFegin;
import cn.uone.fegin.tpi.IFadadaFegin;
import cn.uone.fegin.tpi.IFileFeign;
import cn.uone.fegin.tpi.IWechatFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.CodeUtil;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import cn.uone.util.PdfUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.CacheParam;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.ExcelDataUtil;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.HashKeyAdapter;
import cn.uone.web.util.Hashlize;
import cn.uone.web.util.SafeCompute;
import cn.uone.web.util.UoneHeaderUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Entities;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/bil/order")
@Slf4j
public class BilOrderController extends BaseController implements IBilOrderFegin {
    @Autowired
    private IFileFeign fileFeign;
    @Autowired
    private ISysFileService fileService;
    @Value("${spring.profiles.active}")
    private String active;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IBilOrderPayInfoService bilOrderPayInfoService;
    @Autowired
    private IBilOrderItemService bilOrderItemService;
    @Autowired
    private IBilDiscountLogService bilDiscountLogService;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    private IContContractSourceRelService contContractSourceRelService;
    @Autowired
    private IContRentLadderService contRentLadderService;
    @Autowired
    private IContContractInfoService contractInfoService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private ISysMsgTemplateFegin sysMsgTemplateFegin;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private IGuomiService guomiService;
    @Autowired
    private IApprovalCommitService approvalCommitService;
    @Autowired
    private IApprovalDetailService approvalDetailService;
    @Autowired
    private BilOrderDao bilOrderDao;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private IContParService contParService;
    @Autowired
    private IBizReleaseService releaseService;
    @Resource
    private IBilOrderAutoService autoService;
    @Autowired
    private IBilOrderPayInfoService orderPayInfoService;
    @Resource
    private ApprovalCommitDao approvalCommitDao;
    @Autowired
    private IUserFegin userFegin;
    @Autowired
    private IBilOrderConfirmService orderConfirmService;
    @Autowired
    private IResProjectCompanyService projectCompanyService;
    @Resource
    private IResProjectParaService projectParaService;
    @Autowired
    private ISysParaFegin sysParaFegin;
    @Autowired
    private ISysPushMsgService sysPushMsgService;
    @Autowired
    private ISaleCustomerService saleCustomerService;
    @Resource
    private IFadadaFegin iFadadaFegin;
    @Resource
    private IRenterFegin iRenterFegin;
    @Resource
    private IWechatFegin wechatFegin;
    @Autowired
    private IResProjectParaService resProjectParaService;
    @Autowired
    @Lazy
    private IReportInvoiceService reportInvoiceService;
    @Autowired
    private IBilTransferService bilTransferService;
    @Autowired
    private MinioUtil minioUtil;
    @Autowired
    private PdfUtil pdfUtil;
    @Autowired
    IFlowTaskService flowTaskService;
    @Autowired
    private IResSourceConfigureService resSourceConfigureService;
    @Autowired
    private IResProjectService resProjectService;
    @Autowired
    private ISysCompanyFegin sysCompanyFegin;
    @Autowired
    private IContTempService contTempService;
    @Autowired
    private IBizAccountService bizAccountService;
    @Autowired
    private IZzctSysMsgTemplateFegin zzctSysMsgTemplateFegin;


    @ApiOperation("通过账单编号获取账单")
    @PostMapping("/getOrderByCode")
    public BilOrderEntity getOrderByCode(@RequestParam("orderCode") String orderCode) {
        QueryWrapper<BilOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", orderCode);
        BilOrderEntity order = bilOrderService.getOne(queryWrapper);
        return order;
    }

    @ApiOperation("批量更新账单")
    @PostMapping("/batchUpdateOrders")
    public void batchUpdateOrders(@RequestBody List<BilOrderEntity> orderList) {
        bilOrderService.updateBatchById(orderList,orderList.size());
    }


    @ApiOperation("取消账单")
    @UonePermissions(value = LoginType.ANON)
    @RequestMapping("/cancelGuomiOrder")
    public void test6(String id) throws Exception {
        BilOrderEntity order = bilOrderService.getById(id);
        order.setPayState(PayStateEnum.CANCEL.getValue());
        bilOrderService.updateById(order);
        guomiService.cancelGuomiOrder(order);
    }



    /**
     * 查询
     *
     * @return
     */
    @RequestMapping("/getListByUser")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse getListByUser(Page page, BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        String userId = UoneSysUser.id();
        bilOrderSearchVo.setUserId(userId);
        return response.setSuccess(true).setData(bilOrderService.findByCondition(page, bilOrderSearchVo));
    }

    /**
     * 查询当前租房的每个合同，所对应的账单
     *
     * @return
     */
    @RequestMapping("/getListByContract")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse getListByContract(Page page, BilOrderSearchVo bilOrderSearchVo) throws Exception {
        RestResponse response = new RestResponse();
        String userId = UoneSysUser.id();
        //Map<String,Object> queryMap = Maps.newHashMap();
        //IPage<ContContractVo> contractPage=new Page<>();
        //contractPage = contContractService.selectPageByComun(page,userId,queryMap);
        Map<String,Object> map=new HashedMap();
        map.put("signerId",userId);
        String contractState = bilOrderSearchVo.getContractState();//小程序账单查询时过滤取消合同状态 caizhanghe edit 2025-04-09
        //System.out.println("=====传入的contractState参数为:"+contractState);
//        List<ContContractVo> contContractVos=contContractService.selectPageByComun(page,userId).getRecords();
        List<ContContractVo> contContractVos=contContractService.selectPageByEmp(page,userId,contractState).getRecords();
        // contContractService.getContractBySignerId(map);
        List<String> contractIds=new ArrayList();
        for(ContContractVo vo:contContractVos){
            contractIds.add(vo.getId());
        }
        List<Map<String,Object>> list=new ArrayList<>();
        bilOrderSearchVo.setUserId(userId);
        for(String contractId:contractIds){
           bilOrderSearchVo.setContractId(contractId);
           //bilOrderSearchVo.setDaily("1");//先注释，解决轨道项目租客在小程序支付完公摊水电费以后就查不到的问题 caizhanghe edit 2025-03-26
           List<BilOrderVo> bilList= bilOrderService.getListByVo(bilOrderSearchVo);
           if(bilList.size()==0){//如果查不到记录，则不带Daily条件查询查询最前面5条账单记录,保证每份合同都有账单而不会被过滤 caizhanghe edit 2023-12-03
                bilOrderSearchVo.setDaily(null);
                bilList = bilOrderService.getPartListByVo(bilOrderSearchVo);
            }
           if(bilList.size()>0){
               boolean isPayed=false;
               for(BilOrderVo vo:bilList){
                   if(PayStateEnum.PAYCONFIR.getValue().equals(vo.getPayState())){
                     isPayed=true;
                  }else if(PayStateEnum.NOPAY.getValue().equals(vo.getPayState()) ||
                           PayStateEnum.PART.getValue().equals(vo.getPayState())){
                     isPayed=false;
                     break;
                   }
               }
               Map<String,Object> bilMap=Maps.newHashMap();
               bilMap.put("list",bilList);
               bilMap.put("isPayed",isPayed);
               bilMap.put("isAll",false);
               bilMap.put("check",false);
               list.add(bilMap);
           }

        }
        //定金账单没有关联的合同,不能用contractId找
        bilOrderSearchVo.setContractId(null);
        bilOrderSearchVo.setOrderType(OrderTypeEnum.DEPOSIT.getValue());
        bilOrderSearchVo.setContractFlag("1");
        bilOrderSearchVo.setDaily("");
        IPage<BilOrderVo>  bilList = bilOrderService.findByCondition(page,bilOrderSearchVo);
//        List<BilOrderVo> dueList = bilOrderService.getListByVo(bilOrderSearchVo);
        List<BilOrderVo> dueList = bilList.getRecords();
        if(dueList.size()>0){
            boolean isPayed=false;
            for(BilOrderVo vo:dueList){
                if(PayStateEnum.PAYCONFIR.getValue().equals(vo.getPayState())){
                    isPayed=true;
                }else if(PayStateEnum.NOPAY.getValue().equals(vo.getPayState()) ||
                        PayStateEnum.PART.getValue().equals(vo.getPayState())){
                    isPayed=false;
                    break;
                }
            }
            Map<String,Object> dueMap=Maps.newHashMap();
            dueMap.put("list",dueList);
            dueMap.put("isPayed",isPayed);
            dueMap.put("isAll",false);
            dueMap.put("check",false);
            list.add(dueMap);
        }
        log.info(list.toString());
//        IPage<List<BilOrderVo>> finalPage=new Page<>();
//        finalPage.setCurrent(page.getCurrent());
//        finalPage.setRecords(list);
        return response.setSuccess(true).setData(list);
    }

    /**
     * 查询统计
     *
     * @return
     */
    @RequestMapping("/getCountByUser")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse getCountByUser(BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        String userId = UoneSysUser.id();
        bilOrderSearchVo.setUserId(userId);
        return response.setSuccess(true).setData(bilOrderService.countByCondition(bilOrderSearchVo));
    }

    /**
     * 查询统计
     *
     * @return
     */
    @RequestMapping("/getCount")
    public RestResponse getCount(BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        return response.setSuccess(true).setData(bilOrderService.countByCondition(bilOrderSearchVo));
    }

    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    @UoneLog("推送账单-数据加载/筛选")
    public RestResponse getListForPage(Page page, BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndCreateDate())) {
            bilOrderSearchVo.setEndCreateDate(DateUtil.endOfDay(bilOrderSearchVo.getEndCreateDate()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayTime())) {
            bilOrderSearchVo.setEndPayTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPushTime())) {
            bilOrderSearchVo.setEndPushTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPushTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayableTime())) {
            bilOrderSearchVo.setEndPayableTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayableTime()));
        }
        bilOrderSearchVo.setCarKeyWord(true);
        //BigDecimal total=bilOrderService.getTotal(bilOrderSearchVo);
        BigDecimal total=bilOrderService.countPayment(bilOrderSearchVo);
        total = total == null?BigDecimal.ZERO:total;
        IPage<BilOrderVo> data=bilOrderService.findByCondition(page, bilOrderSearchVo);
        return response.setSuccess(true).setData(data).setMessage(total.toPlainString());  //前端页面显示总金额，试过和setAny()中，layui识别不到，只能放在message中便于传递
    }


    /**
     * 意向金合同分页查询
     *
     * @return
     */
    @RequestMapping("/getPageForIntention")
    public RestResponse getPageForIntention(Page page, BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndCreateDate())) {
            bilOrderSearchVo.setEndCreateDate(DateUtil.endOfDay(bilOrderSearchVo.getEndCreateDate()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayTime())) {
            bilOrderSearchVo.setEndPayTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPushTime())) {
            bilOrderSearchVo.setEndPushTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPushTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayableTime())) {
            bilOrderSearchVo.setEndPayableTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayableTime()));
        }
        //bilOrderSearchVo.setOrderType(OrderTypeEnum.DEPOSIT.getValue());
        //bilOrderSearchVo.setState(PayStateEnum.PAYCONFIR.getValue());
        bilOrderSearchVo.setIntention("1");
        bilOrderSearchVo.setCarKeyWord(false);
        List<String> orderTypes = Lists.newArrayList();
        orderTypes.add(OrderTypeEnum.DEPOSIT.getValue());
        orderTypes.add(OrderTypeEnum.DEPOSITREFUND.getValue());
        bilOrderSearchVo.setOrderTypes(orderTypes);
        List<String> states = Lists.newArrayList();
        states.add(PayStateEnum.PAYCONFIR.getValue());
        states.add(PayStateEnum.REFUNDPENDING.getValue());
        states.add(PayStateEnum.REFUNDED.getValue());
        states.add(PayStateEnum.REFUNDTOAUDIT.getValue());
        bilOrderSearchVo.setStates(states);
//        BigDecimal total=bilOrderService.getTotal(bilOrderSearchVo);
        IPage<BilOrderVo> data=bilOrderService.findByCondition(page, bilOrderSearchVo);
        return response.setSuccess(true).setData(data);  //前端页面显示总金额，试过和setAny()中，layui识别不到，只能放在message中便于传递
    }

    /**
     * 查询智能设备对应的账单
     *
     * @return
     */
    @RequestMapping("/getListByDevice")
    public RestResponse getListByDevice(Page page, String deviceId,String type) {
        RestResponse response = new RestResponse();
        return response.setSuccess(true).setData(bilOrderItemService.getDeviceBill(page, deviceId,type));
    }


    /**
     * 查询统计(企业端)
     *
     * @return
     */
    @RequestMapping("/getEmpOrderCount")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse getEmpOrderCount(BilEmpOrderSearchVo bilEmpOrderSearchVo) {
        RestResponse response = new RestResponse();
        String userId = UoneSysUser.id();
        bilEmpOrderSearchVo.setUserId(userId);
        bilEmpOrderSearchVo.setIsPush(BaseConstants.BOOLEAN_OF_TRUE);
        return response.setSuccess(true).setData(bilOrderService.countEmpOrderByCondition(bilEmpOrderSearchVo));
    }

    /**
     * 分页查询(企业端)
     *
     * @return
     */
    @RequestMapping("/getEmpOrderListForPage")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse getEmpOrderListForPage(Page page, BilEmpOrderSearchVo bilEmpOrderSearchVo) {
        RestResponse response = new RestResponse();
        String userId = UoneSysUser.id();
        bilEmpOrderSearchVo.setUserId(userId);
        bilEmpOrderSearchVo.setIsPush(BaseConstants.BOOLEAN_OF_TRUE);
        return response.setSuccess(true).setData(bilOrderService.findEmpOrderByCondition(page, bilEmpOrderSearchVo));
    }

    /**
     * 导出
     *
     * @param response
     * @param bilOrderSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/export")
    @UoneLog("推送账单-导出")
    public void export(HttpServletResponse response, BilOrderSearchVo bilOrderSearchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        List<BilOrderVo> list = bilOrderService.bilExport(bilOrderSearchVo);
        for (BilOrderVo vo : list) {
            vo.setOrderType(OrderTypeEnum.getNameByValue(vo.getOrderType()));
            vo.setPayState(PayStateEnum.getNameByValue(vo.getPayState()));
            vo.setInvoiceState(InvoiceStateEnum.getNameByValue(vo.getInvoiceState()));
            vo.setPayWay(PayWayEnum.getNameByValue(vo.getPayWay()));
            if("0".equals(vo.getPlatform())){
                vo.setPlatform("社会化合同");
            }else if("3".equals(vo.getPlatform())){
                vo.setPlatform("员工合同");
            }
            if(ObjectUtil.isNotNull(vo.getPayment())){
                vo.setReceivable(vo.getPayment());
                if(ObjectUtil.isNotNull(vo.getDiscountAmount())){
                    vo.setReceivable(vo.getPayment().subtract(vo.getDiscountAmount()));
                }
            }
            List<BilOrderItemEntity> detail=bilOrderItemService.list(new QueryWrapper<BilOrderItemEntity>().eq("order_id",vo.getId()));
            for(BilOrderItemEntity d:detail){
                d.setOrderItemType(OrderItemTypeEnum.getNameByValue(d.getOrderItemType()));
            }
            vo.setDetail(detail);
            vo.setAddress(vo.getPartitionName()+vo.getAddress());
        }
        beans.put("orders", list);
        ExcelRender.me("/excel/export/bilOrder.xlsx").beans(beans).render(response);
    }


    /**
     * 财务导出
     *
     * @param response
     * @param bilOrderSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/financeExport")
    @UoneLog("财务导出")
    public void financeExport(HttpServletResponse response, BilOrderSearchVo bilOrderSearchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        bilOrderSearchVo.setFinanceExport("1");
        bilOrderSearchVo.setFinacelExportDate(new Date());
        List<BilOrderVo> list = bilOrderService.finaceExport(bilOrderSearchVo);
        for (BilOrderVo vo : list) {
            String payState  = vo.getPayState()  ;
            vo.setOrderType(OrderTypeEnum.getNameByValue(vo.getOrderType()));
            vo.setPayState(PayStateEnum.getNameByValue(vo.getPayState()));
            vo.setInvoiceState(InvoiceStateEnum.getNameByValue(vo.getInvoiceState()));
            vo.setPayWay(PayWayEnum.getNameByValue(vo.getPayWay()));
            if("7".equals(vo.getCState())){ // 获取当前月账单的 开始 结束日期  以及退房日期
                BilOrderItemEntity entity = null ;
                List<BilOrderItemEntity> details = bilOrderItemService.list(new QueryWrapper<BilOrderItemEntity>().eq("order_id",vo.getId()).eq("order_item_type","20").orderByAsc("start_time"));
                for(BilOrderItemEntity detail :details){
                    if(detail.getStartTime().before(vo.getCheckoutDate()) && vo.getCheckoutDate().before(detail.getEndTime()))
                        entity = detail ;
                }
                if(null == entity)
                    continue;
                long days = DateUtil.between(vo.getCheckoutDate(),entity.getStartTime(), DateUnit.DAY)+1;
                long monthDay = DateUtil.between(entity.getEndTime(),entity.getStartTime(), DateUnit.DAY);
                BigDecimal mtPayment = vo.getCashPledge().divide(new BigDecimal(monthDay),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(days));
                vo.setMtPayment(mtPayment);
                BigDecimal taxs = new BigDecimal(1).add(vo.getTax());
                vo.setNoTaxPrice(mtPayment.divide(taxs,2, BigDecimal.ROUND_HALF_UP));
                vo.setQichuyue(new BigDecimal(0));
                vo.setQimoyue(new BigDecimal(0));
                vo.setNqimoyue(new BigDecimal(0));
                continue;
            }

            if(PayTypeEnum.ONE_ONE.getValue().equals(vo.getPayType()) || PayTypeEnum.TWO_ONE.getValue().equals(vo.getPayType())){
                List<BilOrderItemEntity> detail = bilOrderItemService.list(new QueryWrapper<BilOrderItemEntity>().eq("order_id",vo.getId()).orderByAsc("start_time"));
                BilOrderItemEntity entity = null ;
                Date lastDate = DateUtil.parseDate("2022-12-31");
                for(BilOrderItemEntity itemEntity :detail){
                    if(itemEntity.getStartTime()!=null && itemEntity.getStartTime().before(lastDate) && lastDate.before(itemEntity.getEndTime()))
                        entity = itemEntity ;
                }
                Date startDate = DateUtil.parseDate("2022-12-01");
                if(null!=entity && entity.getStartTime().after(startDate)){
                    long days = DateUtil.between(lastDate,entity.getStartTime(), DateUnit.DAY)+1;
                    BigDecimal mtPayment = vo.getCashPledge().divide(new BigDecimal(31),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(days));
                    vo.setMtPayment(mtPayment);
                    BigDecimal taxs = new BigDecimal(1).add(vo.getTax());
                    vo.setNoTaxPrice(mtPayment.divide(taxs,2, BigDecimal.ROUND_HALF_UP));
                }
                if(PayStateEnum.PAYCONFIR.getValue().equals(payState)){
                    vo.setQichuyue(new BigDecimal(0));
                    vo.setQimoyue(new BigDecimal(0));
                    vo.setNqimoyue(new BigDecimal(0));
                }else{
                    vo.setQichuyuef(vo.getYswq().multiply(new BigDecimal(-1)));
                    vo.setQimoyuef((vo.getYswq().add(vo.getMtPayment())).multiply(new BigDecimal(-1)));
                    vo.setNqimoyuef(vo.getQimoyuef().divide((vo.getTax().add(new BigDecimal(1))), 2, BigDecimal.ROUND_HALF_UP));
                }
            } else if(PayTypeEnum.ONE_THREE.getValue().equals(vo.getPayType())||PayTypeEnum.TWO_THREE.getValue().equals(vo.getPayType())){
                List<BilOrderItemEntity> detail = bilOrderItemService.list(new QueryWrapper<BilOrderItemEntity>().eq("order_id",vo.getId()).orderByAsc("start_time"));
                int i = 0 ;
                for(BilOrderItemEntity entity : detail){
                    if(DateUtil.compare(vo.getStartDate(),entity.getEndTime())< 0)
                        i++;
                }
                BilOrderItemEntity entity = null ;
                Date lastDate = DateUtil.parseDate("2022-12-31");
                for(BilOrderItemEntity itemEntity :detail){
                    if(itemEntity.getStartTime()!=null  && itemEntity.getStartTime().before(lastDate) && lastDate.before(itemEntity.getEndTime()))
                        entity = itemEntity ;
                }
                Date startDate = DateUtil.parseDate("2022-12-01");
                if(null!=entity && entity.getStartTime().after(startDate)){
                    long days = DateUtil.between(lastDate,entity.getStartTime(), DateUnit.DAY)+1;
                    BigDecimal mtPayment = vo.getCashPledge().divide(new BigDecimal(31),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(days));
                    vo.setMtPayment(mtPayment);
                    BigDecimal taxs = new BigDecimal(1).add(vo.getTax());
                    vo.setNoTaxPrice(mtPayment.divide(taxs,2, BigDecimal.ROUND_HALF_UP));
                }
                if(PayStateEnum.PAYCONFIR.getValue().equals(payState)){
                    if(i==3){
                        vo.setQichuyue(new BigDecimal(0));
                        vo.setQimoyue(vo.getPrice().multiply(new BigDecimal(2)));
                    } else if (i==2){
                        vo.setQichuyue(vo.getPrice().multiply(new BigDecimal(2)));
                        vo.setQimoyue(vo.getPrice().multiply(new BigDecimal(1)));
                    }else {
                        vo.setQichuyue(vo.getPrice().multiply(new BigDecimal(1)));
                        vo.setQimoyue(vo.getPrice().multiply(new BigDecimal(0)));
                    }
                    vo.setNqimoyue(vo.getQimoyue().divide((vo.getTax().add(new BigDecimal(1))), 2, BigDecimal.ROUND_HALF_UP));
                }else{
                    if(i==3){
                        vo.setQichuyuef(vo.getYswq().multiply(new BigDecimal(-1)));
                        vo.setQimoyuef((vo.getYswq().add(vo.getPrice())).multiply(new BigDecimal(-1)));
                    } else if (i==2){
                        vo.setQichuyuef((vo.getYswq().add(vo.getPrice())).multiply(new BigDecimal(-1)));
                        vo.setQimoyuef((vo.getYswq().add(vo.getPrice().multiply(new BigDecimal(2)))).multiply(new BigDecimal(-1)));
                    }else if (i==1){
                        vo.setQichuyuef((vo.getYswq().add(vo.getPrice().multiply(new BigDecimal(2)))).multiply(new BigDecimal(-1)));
                        vo.setQimoyuef((vo.getYswq().add(vo.getPrice().multiply(new BigDecimal(3)))).multiply(new BigDecimal(-1)));
                    }
                    vo.setNqimoyuef(vo.getQimoyuef().divide((vo.getTax().add(new BigDecimal(1))), 2, BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        beans.put("orders", list);
        ExcelRender.me("/excel/export/finacelBill.xlsx").beans(beans).render(response);
    }

    /**
     * 财务水电导出
     *
     * @param response
     * @param bilOrderSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/hydropowerExport")
    public void hydropowerExport(HttpServletResponse response, BilOrderSearchVo bilOrderSearchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        List<BilOrderVo> list = bilOrderService.hydropowerExport(bilOrderSearchVo);
        beans.put("orders", list);
        ExcelRender.me("/excel/export/finacelBill.xlsx").beans(beans).render(response);
    }



    /**
     * 获取账单信息
     *
     * @param id
     * @return
     */
    @RequestMapping("/getInfo")
    @UoneLog("推送账单-查看账单详情")
    public RestResponse getInfo(String id) {
        RestResponse response = new RestResponse();
        try {
            Map<String, Object> resultDataMap = new HashMap<>();
            //账单信息
            BilOrderEntity order = bilOrderService.getById(id);
            resultDataMap.put("order", order);
            //支付人信息
            RenterEntity renter = renterFegin.getById(order.getPayerId());
            resultDataMap.put("renter", renter);
            //优惠金额
            BilDiscountEntity discount = bilDiscountLogService.getDiscountByOrder(order);
            if (ObjectUtil.isNotNull(discount) && PayStateEnum.PAYCONFIR.getValue().equals(order.getPayState())) {
                resultDataMap.put("discountAmount", discount.getDiscountAmount());
            }
            //房源信息
            ResSourceVo source = resSourceService.getInfoById(order.getSourceId());
            resultDataMap.put("source", source);
            if (ObjectUtil.isNull(source)) {
                response.setSuccess(false).setMessage("找不到该账单房源信息！");
                return response;
            }
            //合同信息
            String contractId = order.getContractId();
            ContContractEntity contract = contContractService.getById(contractId);
            if (ObjectUtil.isNull(contract)) {
                contract = new ContContractEntity();
            }
            resultDataMap.put("contract", contract);
            //合同房源关系表
            ContContractSourceRelEntity contractSourceRel = contContractSourceRelService.getByContractIdAndSourceId(contract.getId(), source.getId());
            if (ObjectUtil.isNull(contractSourceRel)) {
                contractSourceRel = new ContContractSourceRelEntity();
            }
            //阶梯表
            ContRentLadderEntity ladder = contRentLadderService.getRentLadderByContSourceIdAndCreatDate(contractSourceRel.getId());
            if (ObjectUtil.isNull(ladder)) {
                ladder = new ContRentLadderEntity();
            }
            //合同信息
            ContContractInfoEntity contractInfo = contractInfoService.getByContractId(contractId);
            contractInfo = ObjectUtil.isNull(contractInfo) ? new ContContractInfoEntity() : contractInfo;
            if(OrderTypeEnum.DEPOSIT.getValue().equals(order.getOrderType())){
                contractInfo.setName(renter.getName());
                contractInfo.setIdType(renter.getIdType());
                contractInfo.setIdNo(renter.getIdNo());
                contractInfo.setTel(renter.getTel());
                QueryWrapper query = new QueryWrapper();
                query.eq("source_id",order.getSourceId());
                query.eq("approval_state","1");
                ResSourceConfigureEntity sourceConfigure = resSourceConfigureService.getOne(query);
                if(sourceConfigure != null){
                    ladder.setPrice(sourceConfigure.getPrice());
                    contractSourceRel.setCashPledge(sourceConfigure.getPrice());
                }
            }
            resultDataMap.put("contractSourceRel", contractSourceRel);
            resultDataMap.put("ladder", ladder);
            resultDataMap.put("contractInfo", contractInfo);


            //子账单设备信息,包含子账单的账单类型,金额等信息
            List<Map<String, String>> oAmountDetails = bilOrderItemService.findAmountDetails(id);
            List<Map<String, String>> amountDetails = new ArrayList<>();
            for(int i=0;i<oAmountDetails.size();i++){
                Map<String,String> detail=oAmountDetails.get(i);
                if (detail.get("start_read")==null) {
                      detail.put("start_read","0");
                }
                if (detail.get("end_read")==null) {
                    detail.put("end_read","0");
                }
                amountDetails.add(detail);
            }


            resultDataMap.put("amountDetails", amountDetails);

            //审批信息
            if(StringUtils.isNotBlank(order.getApprovalState())){
                //获取当前流程审批
                List<ApprovalCommitEntity>  commitEntitys= approvalCommitDao.getAllAppro(order.getId(),null);
                commitEntitys.forEach(c->{
                    List<ApprovalDetailEntity> lists=approvalDetailService.getApprovalInfo(c,null,null);
                    c.setDetails(lists);
                    List<SysFileEntity> files = sysFileService.getListByFromIdAndType(c.getId(), SysFileTypeEnum.PAY_PIC);
                    c.setFiles(files);
                    BilOrderSearchVo bilOrderSearchVo = new BilOrderSearchVo();
                    bilOrderSearchVo.setIds(Arrays.asList(c.getCodeId().split(",")));
                    List<BilOrderVo> orderList = bilOrderService.findByCondition(bilOrderSearchVo);
                    c.setOrderList(orderList);
                });
                resultDataMap.put("approval", commitEntitys);
                //获取支付信息
                List<SysFileEntity> allFiles = Lists.newArrayList();
                QueryWrapper<BilOrderPayInfoEntity> queryWrapper = new QueryWrapper();
                queryWrapper.eq("order_id", order.getId());
                List<BilOrderPayInfoEntity> payInfos = bilOrderPayInfoService.list(queryWrapper);
                for (BilOrderPayInfoEntity payInfo : payInfos) {
                    String applyUserName = null;
                    UserEntity user = userFegin.getUserById(payInfo.getApplyUser());
                    if(user == null){
                        RenterEntity applyUser = renterFegin.getById(payInfo.getApplyUser());
                        applyUserName = applyUser==null?"":applyUser.getName();
                    }else{
                        applyUserName = user.getRealName();
                    }
                    payInfo.setApplyUser(applyUserName);
                    List<SysFileEntity> files = sysFileService.getListByFromIdAndType(payInfo.getApprovalId(), SysFileTypeEnum.PAY_PIC);
                    payInfo.setFiles(files);
                    allFiles.addAll(files);
                }
                resultDataMap.put("payInfos", payInfos);
                resultDataMap.put("files", allFiles);
            }
            response.setSuccess(true).setData(resultDataMap);
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 获取账单信息
     *
     * @param id
     * @return
     */
    @RequestMapping("/getCustomOrderInfo")
    @UonePermissions
    public RestResponse getCustomOrderInfo(String id) {
        RestResponse response = new RestResponse();
        try {
            Map<String, Object> resultDataMap = new HashMap<>();
            //账单信息
            BilOrderEntity order = bilOrderService.getById(id);
            resultDataMap.put("order", order);
            //优惠金额
            BilDiscountEntity discount = bilDiscountLogService.getDiscountByOrder(order);
            if (ObjectUtil.isNotNull(discount) && PayStateEnum.PAYCONFIR.getValue().equals(order.getPayState())) {
                resultDataMap.put("discountAmount", discount.getDiscountAmount());
            }
            //房源信息
            ResSourceVo source = resSourceService.getInfoById(order.getSourceId());
            resultDataMap.put("source", source);
            if (ObjectUtil.isNull(source)) {
                response.setSuccess(false).setMessage("找不到该账单房源信息！");
                return response;
            }
            //子账单设备信息
            List<Map<String, String>> amountDetails = bilOrderItemService.findAmountDetails(id);
            resultDataMap.put("amountDetails", amountDetails);
            response.setSuccess(true).setData(resultDataMap);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 获取企业账单信息（企业端）
     *
     * @param id
     * @return
     */
    @RequestMapping("/getEmpOrderInfo")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse getEmpOrderInfo(String id) {
        RestResponse response = new RestResponse();
        try {
            Map<String, Object> resultDataMap = new HashMap<>();
            //账单信息
            BilOrderEntity order = bilOrderService.getById(id);
            resultDataMap.put("order", order);
            //优惠金额
            BilDiscountEntity discount = bilDiscountLogService.getDiscountByOrder(order);
            if (ObjectUtil.isNotNull(discount) && PayStateEnum.PAYCONFIR.getValue().equals(order.getPayState())) {
                resultDataMap.put("discountAmount", discount.getDiscountAmount());
            }
            //房源信息
            ResSourceVo source = resSourceService.getInfoById(order.getSourceId());
            resultDataMap.put("source", source);
            if (ObjectUtil.isNull(source)) {
                response.setSuccess(false).setMessage("找不到该账单房源信息！");
                return response;
            }
            //子账单设备信息
            List<Map<String, String>> amountDetails = bilOrderItemService.findAmountDetails(id);
            resultDataMap.put("amountDetails", amountDetails);
            response.setSuccess(true).setData(resultDataMap);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 获取确定支付账单信息
     *
     * @param ids
     * @return
     */
    @RequestMapping("/getInfoByIds")
    public RestResponse getInfoByIds(@RequestParam List<String> ids) {
        RestResponse response = new RestResponse();
        try {
            Map<String, Object> resultDataMap = new HashMap<>();
            BilOrderSearchVo bilOrderSearchVo = new BilOrderSearchVo();
            bilOrderSearchVo.setIds(ids);
            List<BilOrderVo> orderList = bilOrderService.findByCondition(bilOrderSearchVo);
            resultDataMap.put("orderList", orderList);
            BigDecimal total = BigDecimal.ZERO;
            BigDecimal payableTotal = BigDecimal.ZERO;
            BigDecimal actual = BigDecimal.ZERO;
            for (BilOrderEntity order: orderList) {
                total = SafeCompute.add(total,order.getPayment());
                payableTotal = SafeCompute.add(payableTotal, order.getPayablePayment());
                actual = SafeCompute.add(actual, order.getActualPayment());
            }
            resultDataMap.put("total", total);
            resultDataMap.put("payableTotal", payableTotal);
            resultDataMap.put("actual", actual);
            //合同信息
            String contractId = orderList.get(0).getContractId();
            ContContractEntity contract = contContractService.getById(contractId);
            if (ObjectUtil.isNull(contract)) {
                contract = new ContContractEntity();
            }
            resultDataMap.put("contract", contract);
            //合同信息
            ContContractInfoEntity contractInfo = contractInfoService.getByContractId(contractId);
            contractInfo = ObjectUtil.isNull(contractInfo) ? new ContContractInfoEntity() : contractInfo;
            resultDataMap.put("contractInfo", contractInfo);
            String renterId = contract.getSignerId();
            RenterEntity renter = renterFegin.getById(renterId);
            resultDataMap.put("renter", renter);
            response.setSuccess(true).setData(resultDataMap);
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @UoneLog("添加账单")
    public RestResponse save(String sourceId, BigDecimal repairFee, BigDecimal serviceFee, BigDecimal otherFee,BigDecimal propertyBaseFee, String remark) {
        RestResponse response = new RestResponse();
        try {
            repairFee = ObjectUtil.isNull(repairFee) ? BigDecimal.ZERO : repairFee;
            serviceFee = ObjectUtil.isNull(serviceFee) ? BigDecimal.ZERO : serviceFee;
            otherFee = ObjectUtil.isNull(otherFee) ? BigDecimal.ZERO : otherFee;
            propertyBaseFee = ObjectUtil.isNull(propertyBaseFee) ? BigDecimal.ZERO : propertyBaseFee;//物业费
            BigDecimal allPayment = SafeCompute.add(repairFee, serviceFee).add(otherFee).add(propertyBaseFee);
            ContContractEntity contract = contContractService.getContractBySourceId(sourceId);

            BilOrderEntity order = bilOrderService.saveOrder(OrderTypeEnum.QITAFEE, true, allPayment, sourceId, contract, null, remark, DataFromEnum.INPUT);
            if (ObjectUtil.isNotNull(repairFee) && (!(repairFee.compareTo(BigDecimal.ZERO)==0))) {//不为空、不等于0才去保存
                bilOrderItemService.saveOrderItem(OrderItemTypeEnum.WEIXIUPEICHANGJIN, order.getId(), repairFee, null);
            }
            if (ObjectUtil.isNotNull(serviceFee) && (!(serviceFee.compareTo(BigDecimal.ZERO)==0))) {
                bilOrderItemService.saveOrderItem(OrderItemTypeEnum.FUWUFEE, order.getId(), serviceFee, null);
            }
            if (ObjectUtil.isNotNull(otherFee) && (!(otherFee.compareTo(BigDecimal.ZERO)==0))) {
                bilOrderItemService.saveOrderItem(OrderItemTypeEnum.ELSEFEE, order.getId(), otherFee, null);
            }
            if (ObjectUtil.isNotNull(propertyBaseFee) && (!(propertyBaseFee.compareTo(BigDecimal.ZERO)==0))) {//物业费
                bilOrderItemService.saveOrderItem(OrderItemTypeEnum.PROPERTY_BASEFEE, order.getId(), propertyBaseFee, null);
            }
            //guomiService.addGuomiOrder(order);
            response.setSuccess(true).setMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 备注
     */
    @RequestMapping("/addRemark")
    public RestResponse addRemark(String id, String remark) {
        RestResponse response = new RestResponse();
        try {
            BilOrderEntity entity = bilOrderService.getById(id);
            entity.setRemark(remark);
            bilOrderService.updateById(entity);
            response.setSuccess(true).setMessage("操作成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 移入回收站discard=1移出回收站discard=0
     */
    @RequestMapping("/transferRecycle")
    @UoneLog("账单移入/移出回收站")
    public RestResponse transferRecycle(@RequestParam List<String> ids, String discard, String remark) {
        RestResponse response = new RestResponse();
        try {
            for (String id : ids) {
                BilOrderEntity entity = bilOrderService.getById(id);
                if (discard.equals(BaseConstants.BOOLEAN_OF_TRUE)) {
                    if (Arrays.asList(PayStateEnum.PAYCONFIR.getValue(), PayStateEnum.REFUNDED.getValue(), PayStateEnum.PART.getValue()).contains(entity.getPayState())) {
                        response.setSuccess(false).setMessage(
                                PayStateEnum.getNameByValue(entity.getPayState()) + "的账单不能移入回收站！");
                        return response;
                    }
                }
            }
            return bilOrderService.batchUpdate(ids, discard, remark);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 退款信息查询
     */
    @RequestMapping(value = "/getRefundInfo")
    public RestResponse getRefundInfo(String id) {
        RestResponse response = new RestResponse();
        try {
            List<HashMap> refundInfo = bilOrderService.selectRefundInfoByOrderId(id);
            response.setData(refundInfo);
            response.setSuccess(true).setMessage("操作成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    @RequestMapping("/isAllPay")
    public RestResponse isAllPay(String contractId){
        List<BilOrderEntity> bilOrderList = bilOrderDao.selectList(new QueryWrapper<BilOrderEntity>()
                .eq("contract_id", contractId)
                .eq("pay_state", PayStateEnum.NOPAY.getValue()));//未支付
        if (CollUtil.isNotEmpty(bilOrderList)) {
            return RestResponse.success().setData(false);
        }
        return RestResponse.success().setData(true);
    }


    /**
     * 取消
     */
    @RequestMapping("/cancelApply")
    @UoneLog("取消账单申请")
    public RestResponse cancelApply(@RequestParam List<String> ids, String remark) {
        RestResponse response = new RestResponse();
        try {
            List<BilOrderEntity> billList = bilOrderService.getByIds(ids);
            for(BilOrderEntity bill : billList){
                bill.setApprovalState(ApprovalStateEnum.TOBESUBMIT.getValue());
                bill.updateById();
            }
            Expression expression = new Expression();
            expression.setSourceId(billList.get(0).getSourceId());
            expression.setCodeId(StringUtils.join(ids.toArray(), ","));
            expression.setType(ApprovalTypeEnum.CANCELORDER.getValue());
            expression.setMemo(remark);
            approvalCommitService.addOrUpdateComit(expression);
            response.setSuccess(true).setMessage("申请提交成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }


    /**
     * 退款BPM审批
     *
     * @param
     * @return
     * @throws
     */
    @RequestMapping("/refundApply")
    @Transactional
    @CacheLock(prefix = "refundApply", expire = 60)
    @UoneLog("退款流程审批")
    public RestResponse refundApply(BilOrderEntity order) throws Exception {
        RestResponse response = new RestResponse();
        ContContractEntity contract = contContractService.getById(order.getContractId());
        //BilOrderEntity orderEntity=bilOrderService.getById(order.getId());
        //orderEntity.setPayUnit(order.getPayUnit());
        order.setPayState(PayStateEnum.REFUNDED.getValue());
        order.setApprovalState(ApprovalStateEnum.COMPLETE.getValue());
        order.setActualPayment(order.getPayment());
        order.setPayableTime(new Date());
        bilOrderService.updateById(order);
        return response.setSuccess(true).setMessage("确认退款成功！");
        //将退款单拆分付款单和转款单
        /*String orderId=order.getId();
        String sourceId=orderEntity.getSourceId();
        String renterId=contract.getSignerId();
        Map PTmap=kingdeeApiService.getPaymentAndTransferBill(orderId,sourceId,renterId,order.getPayTime());
        //从PTmap中将转款单与退款单的值取出
        KingdeePaymentEntity payment=new KingdeePaymentEntity();
        //KingdeeTransferEntity transfer=new KingdeeTransferEntity();
        payment= (KingdeePaymentEntity) PTmap.get("payment");
        //transfer=(KingdeeTransferEntity) PTmap.get("transfer");
        List<KingdeePaymentItemEntity> paymentList= (List<KingdeePaymentItemEntity>) PTmap.get("paymentItemList");
        List<KingdeeTransferItemEntity> transferList= (List<KingdeeTransferItemEntity>) PTmap.get("transferItemList");

        BizReleaseEntity releaseEntity=releaseService.getOne(new QueryWrapper<BizReleaseEntity>().eq("contract_id",contract.getId()).eq("type", ReleaseTypeEnum.CHECKOUT.getValue())) ;

        RenterEntity renter = renterFegin.getById(renterId);
        ResSourceVo source = resSourceService.getInfoById(sourceId);

        //转款表与退款表取值
        List<Map<String,String>> paymentItemList=new ArrayList<>();
        List<Map<String,String>> transferItemList=new ArrayList<>();
        for(KingdeePaymentItemEntity entity:paymentList){
            Map<String,String> map=new HashMap();
            map.put("refundType",entity.getExpenseType());
            map.put("refundAmount",entity.getAmount().stripTrailingZeros().toPlainString());
            if(entity.getCostDescription()==null){
                map.put("refundInfo","无信息");
            }else{
                map.put("refundInfo",entity.getCostDescription());
            }
            paymentItemList.add(map);
        }
        for(KingdeeTransferItemEntity entity:transferList) {
            Map<String, String> map = new HashMap();
            map.put("tranOutType", entity.getOutType());
            map.put("tranOutAmout", entity.getOutAmount().stripTrailingZeros().toPlainString());
            map.put("tranInType", entity.getIntoType());
            map.put("tranInAmout", entity.getIntoAmount().stripTrailingZeros().toPlainString());
            if(entity.getRemarks()==null){
                map.put("tranInfo","无信息");
            }else{
                map.put("tranInfo",entity.getRemarks());
            }
            transferItemList.add(map);
        }

        String instanceIds = "";
        List<BpmWorkflowEntity> relWorkflowList = bpmWorkflowService.getListByRelId(contract.getId());
        if(relWorkflowList != null && relWorkflowList.size()>0){
            List<String> list = Lists.newArrayList();
            for(BpmWorkflowEntity workflowEntity:relWorkflowList){
                list.add(workflowEntity.getInstanceId());
                instanceIds = CollUtil.join(list,",");
            }
        }

        String period= dayComparePrecise(contract.getStartDate(),releaseEntity.getCheckoutDate());
        XyRefundVo xyRefundVo=new XyRefundVo()
                .setContractCode(contract.getContractCode())
                .setSourceCode(source.getCode())
                .setRentalUnit(source.getHouseName())
                .setStartDate(DateUtil.formatDateTime(contract.getStartDate()))
                .setEndDate(DateUtil.formatDateTime(contract.getEndDate()))
                .setTenantName(renter.getName())
                .setTenantTel(renter.getTel())
                .setTenantID(renter.getIdNo())
                .setSignPrice(contract.getTotalPrice().stripTrailingZeros().toPlainString())
                .setPeriod(period)
                .setRefundDate(DateUtil.formatDateTime(order.getPayTime()))
                .setReason(releaseEntity.getConfirmReason())
                .setTotalRefund(order.getPayment().stripTrailingZeros().toPlainString())
                .setAccountName(payment.getCustomer())
                .setBankAccount(payment.getCollectionAccount())
                .setBankName(payment.getReceivingBank())
                .setAccountTel(renter.getTel())
                //关联签约审批流程
                .setRelatedProcess(instanceIds)
                .setPaymentItemList(paymentItemList)
                .setTransferItemList(transferItemList)
                .setProject(source.getProjectBpmCode());

        XyWorkflowVo workflowVo = new XyWorkflowVo();

        //流程参数中的billName,退租流程统一设置成CHECKOUT,长租_退租审批,不需要区分是否退租
        String guId= IdUtil.simpleUUID();
        contract.setGuId(guId).updateById();
        workflowVo.setBillname(XyWorkflowEnum.REFUND.getBillname());
        workflowVo.setBosType(XyWorkflowEnum.REFUND.getBosType());
        workflowVo.setModule(XyWorkflowEnum.REFUND.getModule());
        workflowVo.setIsuploadfile(BaseConstants.BOOLEAN_OF_TRUE);
        workflowVo.setGuid(guId);
        workflowVo.setData(JSONUtil.parseObj(xyRefundVo));
        workflowVo.setCompany(getCompany(contract.getId()));
        workflowVo.setUserCode(getUserCode());
        workflowVo.setProject(source.getProjectBpmCode());
        //根据sysPara的value值,如果是1,走bpm审批流程,如果不是1,跳过流程,直接改审批状态.para的值在基础系统中修改
        String para=sysParaFegin.getByCode("test");
        String requestId = "";
        if("1".equals(para)){
            JSONObject res = xybpmFegin.workflowCreate(workflowVo);
            String code = res.getString("code");
            if(!"0".equals(code)){
                return response.setSuccess(false).setMessage(res.getString("message"));
            }
            requestId = res.getString("requestId");

            List<MultipartFile> files = Lists.newArrayList();
            List<SysFileEntity> sysFileEntities = fileService.getListByFromIdAndType(contract.getId(),SysFileTypeEnum.CONTRACT);
            for(SysFileEntity file: sysFileEntities){
                byte[] bytes = BizReleaseController.urlTobyte(file.getPath());
                FileItem fileItem = BizReleaseController.createFileItem(bytes,file.getName());
                MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
                files.add(multipartFile);
            }
            if(files.size()>0){
                JSONObject upload=fileFeign.workflowUpload(requestId,"fj",files);
                System.out.println("upload");
            }
        }

        BpmWorkflowEntity workflowEntity = new BpmWorkflowEntity();
        if(!"1".equals(sysParaFegin.getByCode("test"))){
            workflowEntity.setAuditResult("Y");
        }
        BeanUtils.copyProperties(workflowVo, workflowEntity);
        workflowEntity.setLaunchDate(new Date());
        workflowEntity.setData(JSONUtil.toJsonStr(xyRefundVo));
        workflowEntity.setInstanceId(requestId);
        workflowEntity.setRelId(order.getId());
        bpmWorkflowService.save(workflowEntity);*/

        //return response.setSuccess(true).setMessage("已发起退款流程审批!");
    }

    public String getUserCode(){
        String id=UoneSysUser.id();
        return userFegin.getUserById(id).getQywechat();
    }

    public String getCompany(String contractId){
        //根据contractId找到sourceId,再根据sourceId找到projectId
        String projectId=resSourceService.getProjectId(contractId);
        //根据projectId,从t_res_project_para 表中获得param value值
        String companyValue=projectParaService.getByCode("KINGDEE_COMPANY",projectId);
        return companyValue;
    }





    /**
     * 提交确定退款申请
     */
    @RequestMapping("/confirmRefundApply")
    @UoneLog("提交确定退款申请")
    public RestResponse confirmRefundApply(BilOrderEntity order) {
        RestResponse response = new RestResponse();
        if (StrUtil.isNotEmpty(order.getContractId())) {
            ContContractEntity contract = contContractService.getById(order.getContractId());
            if (BaseConstants.BOOLEAN_OF_TRUE.equals(contract.getPlatform())) {
                return response.setSuccess(false).setMessage("该笔账单不能在XX公寓平台退款！");
            }
            //查找退租单
            BizReleaseEntity release = releaseService.getByContractId(order.getContractId());
            if(ObjectUtil.isNull(release) || StrUtil.isBlank(release.getAccountId())){
                return response.setSuccess(false).setMessage("该账单未关联到退款账号，请走线下退款！");
            }
        }

        long confirm= orderConfirmService.count(new QueryWrapper<BilOrderConfirmEntity>().eq("type","1").eq("state","0").eq("order_id",order.getId()));
        if(confirm>0){
            return RestResponse.failure("该账单已提交确认，请勿重复提交");
        }

        ResProjectCompanyEntity companyEntity =  projectCompanyService.getById(order.getPayUnit());

        BilOrderEntity bill=bilOrderService.getById(order.getId());
        bill.setPayUnit(companyEntity.getName());
        bill.setApprovalState(ApprovalStateEnum.APPROVAL.getValue());
        bill.updateById();

        ContContractEntity contract = contContractService.getById(bill.getContractId());
        //支付申请审批
        BilOrderConfirmEntity orderConfirmEntity = new BilOrderConfirmEntity();
        orderConfirmEntity.setOrderId(bill.getId())
                .setSigner(contract.getSignerId())
                .setPrice(bill.getPayment())
                .setApplyTime(order.getPayTime())
                .setName(companyEntity.getName())
                .setCard(companyEntity.getCard())
                .setCode(companyEntity.getCode())
                .setBank(companyEntity.getBank())
                .setRemark(order.getRemark())
                .setState("0")
                .setType("1");
        orderConfirmEntity.insert();

/*            Expression expression = new Expression();
        expression.setCodeId(order.getId());
        expression.setMemo(order.getRemark());
        expression.setType(ApprovalTypeEnum.ORDERCONFIRMREFUND.getValue());
        expression.setApplyTime(order.getPayTime());
        approvalCommitService.addOrUpdateComit(expression);*/
        response.setSuccess(true).setMessage("申请提交成功！");
        return response;
    }

    /**
     * 提交确定支付申请
     */
    @RequestMapping("/confirmPayApply")
    @UoneLog("提交确定支付申请")
    public RestResponse confirmPayApply(@RequestParam List<String> ids,BilOrderEntity order, HttpServletRequest request) {
        Console.log("提交确定支付申请");
        RestResponse response = new RestResponse();
        try {
            List<BilOrderEntity> billList = bilOrderService.getByIds(ids);
            Expression expression = new Expression();
            String signer="";
            String projectId = null;
            Console.log("billList:{}",billList.size());
            for(BilOrderEntity bill : billList){
                ResSourceEntity source = resSourceService.getById(bill.getSourceId());
                expression.setProjectId(source.getProjectId());
                if(StrUtil.isBlank(projectId)){
                    projectId = source.getProjectId();
                }else if(!source.getProjectId().equals(projectId)){
                    return response.setSuccess(false).setMessage("合单支付仅支持同一项目下的账单！");
                }
                if (PayStateEnum.NOPAY.getValue().equals(bill.getPayState())) {
                    if (StrUtil.isNotEmpty(bill.getContractId())) {
                        ContContractEntity contract = contContractService.getById(bill.getContractId());
                        /*if (BaseConstants.BOOLEAN_OF_TRUE.equals(contract.getPlatform())) {
                            return response.setSuccess(false).setMessage("该笔账单不能在XX公寓平台支付！");
                        }*/
                        signer=contract.getSignerId();
                    }
                     /*
                    t_con_par表里，没有对应的contractId，导致空指针异常，确认支付时会出错，先将这段判断代码注释
                    ContParEntity parEntity = contParService.getByContractIdAndType(order.getContractId(), ContParEnum.IS_CAN_PAY_ORDER.getValue());
                    if (source.getSourceType().equals(SourceTypeEnum.HOUSE.getValue()) && BaseConstants.BOOLEAN_OF_FALSE.equals(parEntity.getValue())) {
                        return response.setSuccess(false).setMessage("该笔账单不能在XX公寓平台支付！");
                    }
                     */

                    //如果是定金判断房源是否已出租或者已预订
                    if (bill.getOrderType().equals(OrderTypeEnum.DEPOSIT.getValue())) {
                        String sourceState = source.getState();
                        /*if (SourceStateEnum.BOOKED.getValue().equals(sourceState)) {
                            return response.setSuccess(false).setMessage("此房源已被预定！");
                        } else*/ if (SourceStateEnum.RENT.getValue().equals(sourceState)) {
                            return response.setSuccess(false).setMessage("此房源已出租！");
                        }
                    }
                }else if(PayStateEnum.PAYCONFIR.getValue().equals(bill.getPayState())){
                    return response.setSuccess(false).setMessage(bill.getCode()+"此账单已支付，无需发起申请！");
                }
            }

            //支付申请审批
            BilOrderConfirmEntity orderConfirmEntity = orderConfirmService.getOne(new QueryWrapper<BilOrderConfirmEntity>().eq("trade_code",order.getTradeCode()));
            //前端上传图片的方法，当图片有多个时，会多次向 发请示，导致表中出现多条记录。在此处加上判断，确认是否已存在记录，如有，则不再save。
            if(orderConfirmEntity != null){
                return response.setSuccess(false).setMessage("交易单号已存在！");
            }
            Console.log("thisPayment:{}",request.getParameter("thisPayment"));
            orderConfirmEntity = new BilOrderConfirmEntity();
            orderConfirmEntity.setOrderId(StringUtils.join(ids.toArray(), ","))
                    .setRemark(order.getRemark())
                    .setPayWay(order.getPayWay())
                    .setPrice(billList.size()==1?new BigDecimal(request.getParameter("thisPayment")):new BigDecimal(request.getParameter("payableTotal")))
                    .setTradeCode(order.getTradeCode())
                    .setApplyTime(order.getPayTime())
                    .setSigner(signer)
                    .setState("0")
                    .setType("2");
            orderConfirmEntity.insert();

            //修改账单状态
            for(BilOrderEntity bill : billList){
                bill.setApprovalState(ApprovalStateEnum.TOBESUBMIT.getValue());
                bill.setPayState(PayStateEnum.PAIEDTOAUDIT.getValue());
                bill.updateById();
                BilOrderPayInfoEntity info = new BilOrderPayInfoEntity();
                info.setApplyUser(UoneSysUser.id());
                info.setOrderId(bill.getId());
                info.setTradeCode(order.getTradeCode());
                BigDecimal thisPayment = new BigDecimal(request.getParameter("thisPayment"));
                info.setPayTime(order.getPayTime());
                info.setPayWay(order.getPayWay());
                info.setApprovalId(orderConfirmEntity.getId());
                info.setActualPayment(ObjectUtil.isNull(bill.getActualPayment())?new BigDecimal(0):bill.getActualPayment());
                info.setRemark(order.getRemark());
                if(billList.size()==1){
                    info.setPayment(thisPayment);
                }else{
                    info.setPayment(bill.getPayablePayment());
                }
                orderPayInfoService.save(info);
            }
            //清空图片
            sysFileService.delFileByFromIdAndType(orderConfirmEntity.getId(),SysFileTypeEnum.PAY_PIC);
            //上传附件
            sysFileService.saveFilesByRequest(request,SysFileTypeEnum.PAY_PIC.getValue(),orderConfirmEntity.getId());
            response.setSuccess(true).setMessage("申请提交成功！");
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 确定付款
     */
    @RequestMapping("/confirmPay")
    @UoneLog("确定付款")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse confirmPay(@RequestParam("jsonList") String jsonList, String remark, Date payTime, String approvalId) {
        RestResponse response = new RestResponse();
        try {
            ApprovalCommitEntity entity = approvalCommitService.getById(approvalId);
            List<OrderConfirmPayVo> list= JSON.parseArray(jsonList,OrderConfirmPayVo.class);
            response = bilOrderService.confirmPay(list, remark, payTime,entity.getPayWay(),entity.getTradeCode(),approvalId);
            if((boolean)response.get("success")){
                entity.setPayStatus(BaseConstants.BOOLEAN_OF_TRUE);
                entity.setPayTime(payTime);
                entity.setSummary(remark);
                entity.updateById();
            }
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 确定退款
     */
    @RequestMapping("/confirmRefund")
    @UoneLog("确定退款")
    public RestResponse confirmRefund(String id, String remark, BigDecimal payment, Date refundTime,String approvalId) {
        RestResponse response = new RestResponse();
        try {
            response = bilOrderService.confirmRefund(id, remark, payment, refundTime);
            if((boolean)response.get("success")) {
                ApprovalCommitEntity entity = approvalCommitService.getById(approvalId);
                entity.setPayStatus(BaseConstants.BOOLEAN_OF_TRUE);
                entity.setPayTime(refundTime);
                entity.setSummary(remark);
                entity.updateById();
            }
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 发票预览
     */
    @RequestMapping("/previewInvoice")
    @UoneLog("预览发票")
    public RestResponse previewInvoice(@RequestBody InvoiceBuyerVo invoiceBuyerVo) {
        return bilOrderService.previewInvoice(invoiceBuyerVo);
    }

    /**
     * 开票
     */
    @RequestMapping("/makeInvoice")
    @UoneLog("开具发票")
    @CacheLock(prefix = "makeInvoice", expire = 60)
    public RestResponse makeInvoice(@CacheParam @RequestBody InvoiceBuyerVo invoiceBuyerVo) {
        RestResponse response = new RestResponse();

        // 初始化日志上下文，便于问题追踪
        String requestId = UUID.randomUUID()
                               .toString()
                               .replace("-", "");
        MDC.put("requestId", requestId);
        MDC.put("orderId", invoiceBuyerVo.getOrderId());
        MDC.put("operation", "makeInvoice");

        log.info("开始处理开票请求, 订单ID: {}, 请求ID: {}", invoiceBuyerVo.getOrderId(), requestId);

        try {
            // 获取项目开票渠道配置
            String projectId = UoneHeaderUtil.getProjectId();
            ResProjectParaEntity paraEntity = projectParaService.getByCodeAndProjectId(
                ProjectParaEnum.INVOICE_WAY.getValue(), projectId);
            if (paraEntity == null) {
                log.warn("项目开票渠道未配置, 项目ID: {}, 请求ID: {}", projectId, requestId);
                response.setSuccess(false);
                response.setMessage("项目开票渠道未配置");
                return response;
            }

            log.info("获取项目开票配置成功, 项目ID: {}, 开票渠道: {}, 请求ID: {}",
                    projectId, paraEntity.getParamValue(), requestId);

            // 发票状态前置校验：检查订单的当前发票状态
            BilOrderEntity orderEntity = bilOrderService.getById(invoiceBuyerVo.getOrderId());
            if (orderEntity == null) {
                log.warn("订单不存在, 订单ID: {}, 请求ID: {}", invoiceBuyerVo.getOrderId(), requestId);
                response.setSuccess(false);
                response.setMessage("订单不存在");
                return response;
            }

            // 记录审计日志
            log.info("开票请求校验: 订单号[{}], 订单状态[{}], 请求ID: {}", orderEntity.getCode(),
                    InvoiceStateEnum.getNameByValue(orderEntity.getInvoiceState()), requestId);

            // 检查发票状态，只有"未开票"状态的订单才能开票
            if (!InvoiceStateEnum.UNBILLED.getValue()
                                          .equals(orderEntity.getInvoiceState())) {
                String currentStatus = InvoiceStateEnum.getNameByValue(orderEntity.getInvoiceState());
                log.warn("订单当前状态不允许开票, 订单号: {}, 当前状态: {}, 请求ID: {}", orderEntity.getCode(), currentStatus, requestId);
                response.setSuccess(false);
                response.setMessage("订单[" + orderEntity.getCode() + "]当前状态为[" + currentStatus + "]，不允许重复开票");
                return response;
            }

            // 处理特定开票类型的业务账户更新
            if ("01".equals(invoiceBuyerVo.getInvoiceTypeCode())) {
                log.info("处理开票类型01的业务账户更新, 订单号: {}, 请求ID: {}", orderEntity.getCode(), requestId);
                bizAccountService.updateAccountByBuyer(invoiceBuyerVo);
            }

            // 根据项目配置选择开票渠道
            if (paraEntity.getParamValue().equals(ApiTypeEnum.BAIWANG.getValue())) {
                log.info("使用百望开票渠道, 订单号: {}, 请求ID: {}", orderEntity.getCode(), requestId);
                try {
                    // 不在这里修改状态，而是将状态管理委托给baiwangInvoice方法内部统一处理
                    // baiwangInvoice方法内部会负责设置状态为"开票中"并在失败时回滚
                    RestResponse baiwangResponse = bilOrderService.baiwangInvoice(invoiceBuyerVo);
                    if (!baiwangResponse.getSuccess()) {
                        log.warn("百望开票未成功, 订单号: {}, 原因: {}, 请求ID: {}", orderEntity.getCode(),
                                baiwangResponse.getMessage(), requestId);
                    } else {
                        log.info("百望开票成功, 订单号: {}, 请求ID: {}", orderEntity.getCode(), requestId);
                    }
                    return baiwangResponse;
                } catch (Exception e) {
                    log.error("百望开票处理异常, 订单号: {}, 请求ID: {}", orderEntity.getCode(), requestId, e);
                    response.setSuccess(false);
                    response.setMessage("百望开票失败: " + e.getMessage());
                    return response;
                }
            } else {
                log.info("使用Report开票渠道, 订单号: {}, 请求ID: {}", orderEntity.getCode(), requestId);
                try {
                    // ReportInvoiceService.makeInvoice方法内部会负责状态检查和设置
                    reportInvoiceService.makeInvoice(orderEntity);
                    log.info("Report开票成功, 订单号: {}, 请求ID: {}", orderEntity.getCode(), requestId);
                    response.setSuccess(true).setMessage("操作成功！");
                } catch (Exception e) {
                    log.error("Report开票失败, 订单号: {}, 请求ID: {}", orderEntity.getCode(), requestId, e);
                    response.setSuccess(false);
                    response.setMessage("Report开票失败: " + e.getMessage());
                    return response;
                }
            }

            log.info("开票请求处理完成, 订单号: {}, 请求ID: {}", orderEntity.getCode(), requestId);
            return response;
        } catch (Exception e) {
            // 记录详细错误日志
            log.error("开票操作发生未预期异常, 订单ID: {}, 请求ID: {}", invoiceBuyerVo.getOrderId(), requestId, e);

            // 返回错误响应
            response.setSuccess(false);
            response.setMessage("开票失败：系统异常，请联系管理员。错误ID: " + requestId);
            return response;
        } finally {
            // 清理日志上下文
            MDC.remove("requestId");
            MDC.remove("orderId");
            MDC.remove("operation");
        }
    }

    /**
     * 回滚发票状态并记录错误信息
     *
     * @param orderId  订单ID
     * @param errorMsg 错误信息
     */
    private void rollbackInvoiceStatus(String orderId, String errorMsg) {
        String requestId = MDC.get("requestId");
        try {
            log.info("开始回滚订单开票状态, 订单ID: {}, 请求ID: {}", orderId, requestId);

            BilOrderEntity order = bilOrderService.getById(orderId);
            if (order == null) {
                log.error("回滚订单状态失败：订单不存在，订单ID: {}, 请求ID: {}", orderId, requestId);
                return;
            }

            BilOrderEntity rollbackEntity = new BilOrderEntity();
            rollbackEntity.setId(orderId);
            rollbackEntity.setInvoiceState(InvoiceStateEnum.INVOICEFAILD.getValue());

            // 将错误信息添加到订单备注中
            String originalRemark = order.getRemark() == null ? "" : order.getRemark();
            String newRemark;

            // 格式化错误消息，避免过长，保留核心信息
            String formattedErrorMsg = errorMsg;
            if (formattedErrorMsg != null && formattedErrorMsg.length() > 200) {
                formattedErrorMsg = formattedErrorMsg.substring(0, 197) + "...";
            }

            if (originalRemark.contains("【开票失败：")) {
                // 如果已有错误信息，则更新它
                newRemark = originalRemark.replaceAll("(?<=【开票失败：)[^】]*(?=】)", formattedErrorMsg);
            } else {
                // 添加新的错误信息
                newRemark = originalRemark + "【开票失败：" + formattedErrorMsg + "】";
            }

            rollbackEntity.setRemark(newRemark);

            boolean updateSuccess = bilOrderService.updateById(rollbackEntity);
            if (!updateSuccess) {
                log.error("回滚订单状态失败：更新失败，订单ID: {}, 请求ID: {}", orderId, requestId);
            } else {
                log.info("订单状态已回滚为[开票失败]，订单ID: {}, 订单号: {}, 请求ID: {}", orderId, order.getCode(), requestId);
            }
        } catch (Exception ex) {
            // 记录回滚操作失败日志
            log.error("回滚订单状态过程发生异常，订单ID: {}, 请求ID: {}", orderId, requestId, ex);
        }
    }

    /**
     * 提前开票申请
     */
    @RequestMapping("/appleInvoice")
    public RestResponse appleInvoice(@RequestParam List<String> ids) {
        RestResponse response = new RestResponse();
        try {
            for(String id : ids){
                BilOrderEntity entity = bilOrderService.getById(id);
                reportInvoiceService.appleInvoice(entity);
            }
            response.setSuccess(true).setMessage("申请成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }
    /**
     * 提前开票申请-不通过
     */
    @RequestMapping("/appleInvoiceFail")
    public RestResponse appleInvoiceFail(@RequestParam List<String> ids) {
        RestResponse response = new RestResponse();
        try {
            for(String id : ids){
               InvoiceEntity invoice = reportInvoiceService.getById(id);
                BilOrderEntity bilOrderEntity = new BilOrderEntity();
                bilOrderEntity.setId(invoice.getOrderId());
                bilOrderEntity.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());
                reportInvoiceService.removeById(id);
                 bilOrderService.updateById(bilOrderEntity);
            }
            response.setSuccess(true).setMessage("审核结果不通过-成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 红冲发票
     */
    @RequestMapping("/redInvoice")
    @UoneLog("发票管理-红冲发票")
    public RestResponse redInvoice(@RequestParam("invoiceIds") String invoiceIds,@RequestParam("remark") String remark) {
        RestResponse response = new RestResponse();
        try {
            //获取开票渠道
            String projectId = UoneHeaderUtil.getProjectId();
            ResProjectParaEntity paraEntity = projectParaService.getByCodeAndProjectId(ProjectParaEnum.INVOICE_WAY.getValue(),projectId);
            if(paraEntity == null){
                response.setSuccess(false);
                response.setMessage("项目开票渠道未配置");
                return response;
            }
            if(paraEntity.getParamValue().equals(ApiTypeEnum.BAIWANG.getValue())){
                return bilOrderService.baiwangRedInvoice(invoiceIds,remark);
            }else{
                //BilOrderEntity entity = bilOrderService.getById(invoiceBuyerVo.getOrderId());
                //reportInvoiceService.makeInvoice(entity);
            }
            response.setSuccess(true).setMessage("操作成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }


    /**
     * 罚没定金退款
     */
    @RequestMapping("/forfeiture")
    public RestResponse forfeiture(@RequestParam List<String> ids) {
        RestResponse response = new RestResponse();
        try {
            StringBuilder msg = validate(ids);
            if (StrUtil.isNotEmpty(msg.toString())) {
                response.setSuccess(false).setMessage(msg.toString());
            } else {
                for (String id : ids) {
                    BilOrderEntity order = bilOrderService.getById(id);
                    order.setPayableTime(new Date());
                    order.setPayState(PayStateEnum.CANCEL.getValue());
                    order.updateById();
                    ContContractInfoEntity info = contractInfoService.getByContractId(order.getContractId());
                    info.setIsForfeiture(true);
                    info.setForfeitureSum(order.getPayment().negate());
                    info.updateById();
                }
                response.setSuccess(true).setMessage("罚没成功！");
            }
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    private StringBuilder validate(List<String> ids) {
        StringBuilder msg = new StringBuilder();
        for (String id : ids) {
            BilOrderEntity order = bilOrderService.getById(id);
            if (StrUtil.isNotEmpty(order.getApprovalState())
                    && !Arrays.asList(ApprovalStateEnum.CANCEL.getValue(), ApprovalStateEnum.REJECT.getValue())
                    .contains(order.getApprovalState())) {
                msg.append(String.format("%s账单在审批中，不能罚没！<br>", order.getCode()));
            }
        }
        return msg;
    }

    @RequestMapping("/makeInvoiceByCustom")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse makeInvoiceByCustom(String id) {
        RestResponse response = new RestResponse();
        try {
            BilOrderEntity entity = bilOrderService.getById(id);
            entity.setInvoiceType(InvoiceTypeEnum.PERSONAL.getValue());
            entity.setInvoiceState(InvoiceStateEnum.INVOICING.getValue());
            entity.setApplyInvTime(new Date());
            bilOrderService.updateById(entity);
            response.setSuccess(true).setMessage("申请成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 催付
     */
    @RequestMapping("/urge")
    @UoneLog("催付提醒")
    public RestResponse urge(String id,String type) {
        RestResponse response = new RestResponse();
        try {
            BilOrderEntity entity = bilOrderService.getById(id);
            RenterEntity renter = renterFegin.getById(entity.getPayerId());
            Map<String,Object> maps = Maps.newHashMap();
            maps.put("id",entity.getSourceId());
            ResSourceVo resEntity = resSourceService.getSourceInfoByComun(maps);
//            if(StringUtils.isBlank(renter.getOpenid())){
//                return response.setSuccess(false).setMessage("该租客未绑定公众号");
//            }else{
//                sendUrgeMsg(renter,entity.getPayment().toString() ,resEntity,type);
//            }
            if(renter==null){
                return response.setSuccess(false).setMessage("该租客不存在");
            }else{
            sendUrgeMsg(renter,entity.getPayment().toString() ,resEntity,type);
            }
            // 简单邮件的发送
//            MailUtil e = new MailUtil ();
//            e.SendSimpleEmail("账单催付通知书","尊敬的承租户:"+renter.getName()+"您好！您本期账单已经生成，请及时前往小程序-》个人中心—》我的账单模块完成账单支付，以免造成您的信用问题。感谢支持","<EMAIL>");
            renter.setTime(renter.getTime() + 1 );
            renter.setUrgeTime(new Date());
            renterFegin.update(renter);
            response.setSuccess(true).setMessage("发送成功！");
            //保存催付记录表
            UrgePayRecordEntity recordEntity = new UrgePayRecordEntity();
            recordEntity.setContractCode(entity.getMergeCode());
            recordEntity.setContractId(entity.getContractId());
            recordEntity.setOrderId(entity.getId());
            recordEntity.setPayment(entity.getPayment());
            recordEntity.setPushDate(entity.getPushTime());
            recordEntity.setOrderCode(entity.getCode());
            recordEntity.setUrgeDate(new Date());
            recordEntity.setRenterId(renter.getId());
            recordEntity.setRenterName(renter.getName());
            recordEntity.setSourceId(resEntity.getId());
            recordEntity.setSourceName(resEntity.getProjectName()+"-"+resEntity.getPartitionName()+"-"+resEntity.getCode());
            recordEntity.setOperator(UoneSysUser.nickName());
            recordEntity.setOperatorId(UoneSysUser.id());
            recordEntity.insert();
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage("催付信息发送失败;"+e.getMessage());
        }
        return response;
    }

    /**
     * 批量催付
     */
    @RequestMapping("/batchUrgeByIds")
    public RestResponse batchUrgeByIds(@RequestParam List<String> ids,@RequestParam("type") String type) {
        StringBuilder message = new StringBuilder();
        StringBuilder failure = new StringBuilder();
        for (String id : ids) {
            BilOrderEntity entity = null;
            try {
                entity = bilOrderService.getById(id);
                RenterEntity renter = renterFegin.getById(entity.getPayerId());
                Map<String, Object> maps = Maps.newHashMap();
                maps.put("id", entity.getSourceId());
                ResSourceVo resEntity = resSourceService.getSourceInfoByComun(maps);
                sendUrgeMsg(renter, entity.getPayment().toString(), resEntity, type);
                renter.setTime(renter.getTime() + 1);
                renter.setUrgeTime(new Date());
                renterFegin.update(renter);
                if(message.lastIndexOf("{")<=0){
                    message.append("{").append(entity.getCode()).append("}");
                }
                //保存催付记录表
                UrgePayRecordEntity recordEntity = new UrgePayRecordEntity();
                recordEntity.setContractCode(entity.getMergeCode());
                recordEntity.setContractId(entity.getContractId());
                recordEntity.setOrderId(entity.getId());
                recordEntity.setPayment(entity.getPayment());
                recordEntity.setPushDate(entity.getPushTime());
                recordEntity.setOrderCode(entity.getCode());
                recordEntity.setUrgeDate(new Date());
                recordEntity.setRenterId(renter.getId());
                recordEntity.setRenterName(renter.getName());
                recordEntity.setSourceId(resEntity.getId());
                recordEntity.setSourceName(resEntity.getProjectName() + "-" + resEntity.getPartitionName() + "-" + resEntity.getCode());
                recordEntity.setOperator(UoneSysUser.nickName());
                recordEntity.setOperatorId(UoneSysUser.id());
                recordEntity.insert();
            } catch (Exception e) {
                e.printStackTrace();
                if (failure.lastIndexOf("{") <= 0) {
                    failure.append("{").append(entity.getCode()).append("}");
                }
            }
        }
        if(message.length()>0){
            message.append("催付成功！");
        }
        if(failure.length()>0){
            message.append("催付失败！");
        }
        return RestResponse.success(message.toString());
    }


    /**
     * 一键催付
     *
     * @return
     */
    @RequestMapping("/batchUrge")
    public RestResponse batchUrge(BilOrderSearchVo bilOrderSearchVo) throws Exception {
        RestResponse response = new RestResponse();
        bilOrderSearchVo.setState(PayStateEnum.NOPAY.getValue());
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndCreateDate())) {
            bilOrderSearchVo.setEndCreateDate(DateUtil.endOfDay(bilOrderSearchVo.getEndCreateDate()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayTime())) {
            bilOrderSearchVo.setEndPayTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndRefundTime())) {
            bilOrderSearchVo.setEndRefundTime(DateUtil.endOfDay(bilOrderSearchVo.getEndRefundTime()));
        }
        bilOrderSearchVo.setIsPush(BaseConstants.BOOLEAN_OF_TRUE);
        bilOrderSearchVo.setState(PayStateEnum.NOPAY.getValue());
        bilOrderSearchVo.setIsApproval(BaseConstants.BOOLEAN_OF_FALSE);
        List<BilOrderVo> bilOrderVoList = bilOrderService.findByCondition(bilOrderSearchVo);

        Iterator<BilOrderVo> it = bilOrderVoList.iterator();
        while (it.hasNext()) {
            BilOrderVo vo = it.next();
            if (OrderTypeEnum.SUBSIDY.getValue().equals(vo.getOrderType())) {
                it.remove();
            }
        }
        StringBuilder message = new StringBuilder();
        StringBuilder failure = new StringBuilder();

        if(bilOrderVoList.isEmpty()){
            return response.setSuccess(false).setMessage("没有需要催付的账单！");
        }

        //按用户
        HashMap<String, List<BilOrderVo>>  bilOrderMap = Hashlize.hashlizeObjects(bilOrderVoList, new HashKeyAdapter(new String[]{"payerId"}));

        for (String key : bilOrderMap.keySet()) {
            RenterEntity renter = renterFegin.getById(key);
            if(ObjectUtil.isNotNull(renter)&&StrUtil.isNotEmpty(renter.getTel())&&StrUtil.isNotBlank(renter.getOpenid())){ //部分用户没有绑定微信公众号，无法催付
                List<BilOrderVo> orderVos = bilOrderMap.get(key);
                if(isPayable(orderVos)){
                    try{
                        sendUrgeMsg(renter,orderVos.get(0).getProjectId());
                        //保存催付记录表
                        Map<String,Object> maps = Maps.newHashMap();
                        maps.put("id",orderVos.get(0).getSourceId());
                        ResSourceVo resEntity = resSourceService.getSourceInfoByComun(maps);
                        UrgePayRecordEntity recordEntity = new UrgePayRecordEntity();
                        recordEntity.setContractCode(orderVos.get(0).getMergeCode());
                        recordEntity.setContractId(orderVos.get(0).getContractId());
                        recordEntity.setOrderId(orderVos.get(0).getId());
                        recordEntity.setPayment(orderVos.get(0).getPayment());
                        recordEntity.setPushDate(orderVos.get(0).getPushTime());
                        recordEntity.setOrderCode(orderVos.get(0).getCode());
                        recordEntity.setUrgeDate(new Date());
                        recordEntity.setRenterId(renter.getId());
                        recordEntity.setRenterName(renter.getName());
                        recordEntity.setSourceId(resEntity.getId());
                        recordEntity.setSourceName(resEntity.getProjectName()+"-"+resEntity.getPartitionName()+"-"+resEntity.getCode());
                        recordEntity.setOperator(UoneSysUser.nickName());
                        recordEntity.setOperatorId(UoneSysUser.id());
                        recordEntity.insert();
                    }catch (Exception e){
                        continue;
                    }
                    renter.setTime(renter.getTime() + 1 );
                    renter.setUrgeTime(new Date());
                    renterFegin.update(renter);
                    if(message.lastIndexOf("{")<=0){
                        message.append("{").append(renter.getName()).append("}");
                    }
                }else{
                    if (failure.lastIndexOf("{") <= 0) {
                        failure.append("{").append(renter.getName()).append("}");
                    }
                }
            }
        }
        if(message.length()>0){
            message.append("等人催付成功！");
        }
        if(failure.length()>0){
            failure.append("等人近期已催付过，莫频繁催付哦！");
        }
        System.out.println(message.append(failure).toString());
        return response.setSuccess(true).setMessage(message.append(failure).toString());
    }

    private boolean isPayable(List<BilOrderVo> orderVos) {
        Boolean isPayable = false;
        for(BilOrderVo orderVo : orderVos){
            if(BaseConstants.BOOLEAN_OF_TRUE.equals(orderVo.getIsPayable())){
                return true;
            }
        }
        return isPayable;
    }

    private void sendUrgeMsg(RenterEntity renter,String projectId) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        // modify by linderen on 20210714 修改通知方式为公众号通知 start
        sysMsgTemplateFegin.sendByProjectId(projectId,"173774",renter.getTel(), JSONUtil.toJsonStr(params));
//        cn.hutool.json.JSONObject result=wechatFegin.sendMsgByTempWithOpenid(renter.getOpenid(), "XX公寓", "账单提醒",
//                    "您好，本月的账单已出，可登录小程序查看，请您及时支付账单费用，" +
//                            "避免因逾期造成不便。如有问题请联系运营官。");
            //modify by linderen on 20210714 修改通知方式为公众号通知 end
//        if(ObjectUtil.isNotNull(result.get("errmsg"))){
//            throw  new Exception(result.get("errmsg").toString());
//        }
//        System.out.println(result.toString());
    }

    private void sendUrgeMsg(RenterEntity renter ,String payment ,ResSourceVo vo,String type) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("name",renter.getName());
        params.put("project",vo.getProjectName());
        params.put("house",vo.getCode());
        String smsCode = null;
        if("0".equals(type) || StrUtil.isBlank(type)){
            params.put("price",payment);
            smsCode = "198888";
        }else if("3".equals(type)){
            params.put("price",payment);
            smsCode = "198887";
        }else if("5".equals(type)){
            smsCode = "198886";
        }else if("7".equals(type)){
            smsCode = "198885";
        }
        sysMsgTemplateFegin.sendByProjectId(vo.getProjectId(),smsCode,renter.getTel(),JSONUtil.toJsonStr(params));
        params.put("title","账单缴费提醒");
        params.put("renterId",renter.getId());
        //sysPushMsgService.pushRenterMsg(params);
    }

    @UoneLog("费用导入模板下载")
    @RequestMapping(value="/exportFee")
    public void exportFee(HttpServletResponse response) throws BusinessException {
        ExcelRender.me("/excel/import/feeTemplate.xls").beans(new HashMap<>()).render(response);
    }

    @UoneLog("房间费用批量导入")
    @RequestMapping("importRoomLivingFee")
    @CacheLock(prefix = "importRoomLivingFee", expire = 60)
    public RestResponse importRoomLivingFee(@RequestParam("file") MultipartFile file, @CacheParam String month) throws Exception {
        if(file.isEmpty()){
            return RestResponse.failure("请选择上传文件");
        }
        InputStream input = file.getInputStream();
        List<OrderImportVo> order = ExcelDataUtil.importData(input, OrderImportVo.class);
        if(input != null){
            input.close();
        }
        if(CollectionUtils.isEmpty(order)){
            return RestResponse.success().setData("读取excel异常");
        }
        String ret=bilOrderService.importRoomLivingFee(order,month);
        return RestResponse.success().setData(ret);
    }

    @UoneLog("房间费用批量导入")
    @RequestMapping("importRoomLivingFeeN")
    @CacheLock(prefix = "importRoomLivingFeeN", expire = 60)
    public RestResponse importRoomLivingFeeN(@RequestParam("file") MultipartFile file, @CacheParam String monthStart ,@CacheParam String monthEnd ) throws Exception {
        if(file.isEmpty()){
            return RestResponse.failure("请选择上传文件");
        }
        InputStream input = file.getInputStream();
        List<OrderImportVo> order = ExcelDataUtil.importData(input, OrderImportVo.class);
        if(input != null){
            input.close();
        }
        if(CollectionUtils.isEmpty(order)){
            return RestResponse.success().setData("读取excel异常");
        }
        String ret=bilOrderService.importRoomLivingFeeN(order,monthStart,monthEnd);
        return RestResponse.success().setData(ret);
    }

    @RequestMapping("/checkLive")
    @UonePermissions
    public RestResponse checkLive() throws Exception {
        return autoService.checkLiveCost(DateUtil.date());
    }

    @RequestMapping("/getReleaseOrderCode")
    @UonePermissions(LoginType.USER)
    public RestResponse getReleaseOrderCode(String contractId,String sourceId) {
        String code = bilOrderService.getReleaseOrderCode(contractId,sourceId);
        return RestResponse.success().setData(code);
    }


    /**
     * 修改尾期分页查询
     *
     * @return
     */
    @RequestMapping("/getFixLastListForPage")
    public RestResponse getFixLastListForPage(Page page, BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        bilOrderSearchVo.setFixLastOrder(true);
        bilOrderSearchVo.setCarKeyWord(true);
        return response.setSuccess(true).setData(bilOrderService.findByCondition(page, bilOrderSearchVo));
    }

    /**
     * 修改尾期账单详细页
     * @param ids
     * @return
     */
    @RequestMapping("/getFixLastInfoByIds")
    public RestResponse getFixLastInfoByIds(@RequestParam List<String> ids) {
        RestResponse response = new RestResponse();
        try {
            Map<String, Object> resultDataMap = new HashMap<>();
            BilOrderSearchVo bilOrderSearchVo = new BilOrderSearchVo();
            bilOrderSearchVo.setIds(ids);
            bilOrderSearchVo.setFixSort(true);
            List<BilOrderVo> orderList = bilOrderService.findByCondition(bilOrderSearchVo);
            orderList.forEach(o->{
                List<BilOrderItemEntity> detail=bilOrderItemService.list(new QueryWrapper<BilOrderItemEntity>().eq("order_id",o.getId()).orderByDesc("start_time").ne("order_item_type",OrderItemTypeEnum.ACTIVITY.getValue()));
                if(CollectionUtil.isNotEmpty(detail)){
                    if (Arrays.asList(OrderTypeEnum.RENT.getValue(), OrderTypeEnum.SUBSIDY.getValue()).contains(o.getOrderType())) {
                        if(ObjectUtil.isNull(detail.get(0).getFixBefore())) {
                            detail.get(0).setFixBefore(detail.get(0).getPayment());
                        }
                        List<BilOrderItemEntity> ot=ListUtil.sub(detail,1,detail.size());
                        detail =  ListUtil.sub(detail,0,1);
                        BigDecimal rento=BigDecimal.ZERO;
                        for(BilOrderItemEntity t:ot){
                            rento = SafeCompute.add(rento,t.getPayment());
                        }
                        o.setPrice(rento);
                    }else{
                        for(int i=0;i<detail.size();i++){
                            if(ObjectUtil.isNull(detail.get(i).getFixBefore())){
                                detail.get(i).setFixBefore(detail.get(i).getPayment());
                            }
                        }
                    }
                }
                o.setDetail(detail);
            });
            resultDataMap.put("orderList", orderList);
            response.setSuccess(true).setData(resultDataMap);
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }


    /**
     * 修改尾期账单账单金额
     * @param jsonList
     * @return
     */
    @RequestMapping("/fixOrder")
    public RestResponse fixOrder(@RequestParam("jsonList") String jsonList, String memo) {
        List<BilOrderItemEntity> bilItems =JSON.parseArray(jsonList,BilOrderItemEntity.class);
        if(CollectionUtil.isEmpty(bilItems)){
            return RestResponse.failure("提交修改账单不能为空！");
        }
        Set<String> ids = Sets.newHashSet();
        BigDecimal fixAfter = BigDecimal.ZERO;
        BigDecimal fixBefore=BigDecimal.ZERO;
        BigDecimal sub=BigDecimal.ZERO;
        BigDecimal afterAll = BigDecimal.ZERO;
        BigDecimal beforeAll = BigDecimal.ZERO;
        for(BilOrderItemEntity b:bilItems){
            BilOrderItemEntity bilOrderItemEntity = bilOrderItemService.getById(b.getId());
            ids.add(b.getOrderId());
            b.setFixBefore(b.getPayment());
            fixBefore = SafeCompute.add(fixBefore,bilOrderItemEntity.getPayment());
            fixAfter = SafeCompute.add(fixAfter,b.getFixAfter());
        }
        sub= SafeCompute.sub(fixBefore,fixAfter);
        List<BilOrderEntity> bils= bilOrderService.list(new QueryWrapper<BilOrderEntity>().in("id", ids));
        for(BilOrderEntity b:bils){
            beforeAll = SafeCompute.add(beforeAll,b.getPayment());
            b.setApprovalState(ApprovalStateEnum.TOBESUBMIT.getValue()).updateById();
        }
        afterAll = SafeCompute.sub(beforeAll,sub);
        Expression expression=new Expression();
        expression.setContractId(bils.get(0).getContractId());
        expression.setType(ApprovalTypeEnum.FIXLASTORDER.getValue());
        expression.setMemo(memo);
        expression.setPrice(afterAll);
        expression.setCodeId(StringUtils.join(ids.toArray(), ","));
        approvalCommitService.addOrUpdateComit(expression);
        bilOrderItemService.saveOrUpdateBatch(bilItems);
        return RestResponse.success();
    }

    /**
     * 推送
     */
    @RequestMapping("/pushOrder")
    @UoneLog("推送账单-推送账单")
    public RestResponse pushOrder(@RequestParam List<String> ids) {
        RestResponse response = new RestResponse();
        try {
            List<BilOrderEntity> billList = bilOrderService.getByIds(ids);
            autoService.pushOrder(billList);
            response.setSuccess(true).setMessage("推送成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    @UonePermissions(LoginType.USER)
    @RequestMapping("/getPayment")
    public RestResponse getPayment(String ids) {
        RestResponse response=new RestResponse();
        List idList= Arrays.asList(ids.split(","));
        if(bilOrderItemService.listByIds(idList).size()<idList.size()){
            return response.setSuccess(false).setMessage("未找到账单信息");
        }
        Collection<BilOrderItemEntity> entityList=bilOrderItemService.listByIds(idList);
        BigDecimal payment=new BigDecimal(0.0);
        for(BilOrderItemEntity entity:entityList){
          payment=payment.add(entity.getPayment());
        }
//        String taxPayment=payment.toString();
        Map<String,Object> invoiceMap=new HashMap<>();
        invoiceMap.put("taxPayment",payment);
        invoiceMap.put("itemEntity",entityList);
        return response.setSuccess(true).setData(invoiceMap);
    }

    /**
     * 计算2个日期之间相差的  相差多少年月日
     * 比如：2011-02-02 到  2017-03-02 相差 6年，1个月，0天
     * @param fromDate
     * @param toDate
     * @return
     */
    public static String dayComparePrecise(Date fromDate,Date toDate){
        Calendar  from  =  Calendar.getInstance();
        from.setTime(fromDate);
        Calendar  to  =  Calendar.getInstance();
        to.setTime(toDate);
        int fromYear = from.get(Calendar.YEAR);
        int fromMonth = from.get(Calendar.MONTH);
        int fromDay = from.get(Calendar.DAY_OF_MONTH);

        int toYear = to.get(Calendar.YEAR);
        int toMonth = to.get(Calendar.MONTH);
        int toDay = to.get(Calendar.DAY_OF_MONTH);

        int year = toYear  -  fromYear;
        int month = toMonth  - fromMonth;
        int day = toDay  - fromDay;
        return year+"年"+month+"月"+day+"日";
    }

    /**
     * 查询金蝶的ReceivableItem所对应的orderItem数据
     *
     * @return
     */
    @RequestMapping("/getItemList")
    @UonePermissions(value = LoginType.CUSTOM)
    public RestResponse getItemListByRecItemId(Page page,String receivableItemId) {
        RestResponse response = new RestResponse();
        return response.setSuccess(true).setData(bilOrderItemService.pageByRecItemId(page, receivableItemId));
    }

    /**
     *
     *
     * @return
     */
    @RequestMapping("/test")
    @UonePermissions
    public RestResponse test(BigDecimal num) {
        RestResponse response = new RestResponse();
        return response.setSuccess(true);
    }

    // 签约-XX公寓
    @UoneLog("签订意向书")
    @UonePermissions(LoginType.CUSTOM)
    @RequestMapping(value = "/signIntention", method = RequestMethod.POST)
    @CacheLock(prefix = "signIntention", expire = 30)
    public RestResponse signIntention(@RequestBody BilOrderEntity bill) throws Exception {
        Map<String, Object> map = new HashMap<>();
        SysFileEntity sysFileEntity = new SysFileEntity();
        if("1".equals(bill.getIntention())){
            sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", bill.getId()).eq("type", SysFileTypeEnum.INTENTION.getValue()));
        }else{
            // PDF生成
            sysFileEntity = bilOrderService.createIntentionPdf(bill);
        }
        map.put("intentionPDF", sysFileEntity);
        return RestResponse.success("签订成功").setData(map);
    }

    @UoneLog("签订意向书")
    @UonePermissions(LoginType.CUSTOM)
    @RequestMapping(value = "/signIntentionById", method = RequestMethod.POST)
    @CacheLock(prefix = "signIntentionById", expire = 30)
    public RestResponse signIntentionById(@RequestParam("orderId") String orderId) {
        BilOrderEntity bill = bilOrderService.getById(orderId);
        Map<String, Object> map = new HashMap<>();
        SysFileEntity sysFileEntity = new SysFileEntity();
        if("1".equals(bill.getIntention())){
            sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", bill.getCcbBillId()).eq("type", SysFileTypeEnum.INTENTION.getValue()));
        }else{
            // PDF生成
            try {
                sysFileEntity = bilOrderService.createIntentionPdf(bill);
            } catch (Exception e) {
                return RestResponse.failure(e.getMessage());
            }
        }
        map.put("intentionPDF", sysFileEntity);
        return RestResponse.success("签订成功").setData(map);
    }

    //手动签署获取页面地址
    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping(value = "/getExtSignUrl", method = RequestMethod.POST)
    public RestResponse getExtSignUrl(@RequestParam("orderId") String orderId) throws Exception {
        BilOrderEntity order = bilOrderService.getById(orderId);
        //上传PDF文件
        //法大大合同上传
        ReqUploadDocs reqUploaddocs = new ReqUploadDocs();
        //合同编号
        reqUploaddocs.setContractId(order.getCode());
        //合同标题
        reqUploaddocs.setDocTitle(order.getCode());
        QueryWrapper<SysFileEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("from_id",orderId);
        wrapper.eq("type", SysFileTypeEnum.INTENTION.getValue());
        SysFileEntity entity1 = sysFileService.getOne(wrapper);
        if (entity1 == null) {
            return RestResponse.failure("该意向书没有电子意向书，请联系客服");
        }
        reqUploaddocs.setDocUrl(FileUtil.getPath(entity1.getUrl()));
        reqUploaddocs.setDocType(".pdf");
        log.info("法大大上传意向书开始");
        //TODO modify by zengguoshen 20211027  start
        //开发环境获得的文件url是本地地址，发请求法大大会报参数异常，因此在开发环境下，需将文件保存在本地后，
        // 再以File对象的形式传过去。因为二者传参不一样，所以得加以区分
        RestResponse rUpload = iFadadaFegin.uploaddocs(reqUploaddocs);
        /*if("dev".equals(active)){
            String path="d:/";
            File file=sysFileService.saveUrlAs(reqUploaddocs.getDocUrl(),path,"POST",entity1.getName());
            reqUploaddocs.setDocUrl(null);
            rUpload = iFadadaFegin.uploaddocs(reqUploaddocs,file);
        }else{
            rUpload= iFadadaFegin.uploaddocs(reqUploaddocs,null);
        }*/
        //TODO modify by zengguoshen 20211027  end
        log.info("返回code:" + rUpload);
        if (!"200".equals(rUpload.get("code").toString())) {
            return rUpload;
        }

        //法大大手动签署
        ReqExtsign reqExtsign = new ReqExtsign();
        String transactionId = CodeUtil.generateUuid(true);
        reqExtsign.setContractId(order.getId());
        //交易号(交易号随机生成)
        reqExtsign.setTransactionId(transactionId);
        //合同编号
        reqExtsign.setContractCode(order.getCode());
        //合同标题
        reqExtsign.setDocTitle(order.getCode());
        //定位关键字
        reqExtsign.setSignKeyword("电子签章");
        //关键字策略
        reqExtsign.setKeywordStrategy("0");//2:最后一个关键字签章 0:所有关键字签章
        //客户编号（我们的客户编号）
        RenterEntity renterEntity = iRenterFegin.getById(order.getPayerId());
        reqExtsign.setCustomerId(renterEntity.getFadadaCode());
        reqExtsign.setNotifyUrl("intention");
        log.info("法大大进行手动签署");
        return iFadadaFegin.getExtSignUrl(reqExtsign);
    }

    // 手动签署回调
    @UonePermissions
    @RequestMapping(value = "/intentionNotify")
    public RestResponse intentionNotify(@RequestParam("contract_id") String contract_id) throws Exception {
        BilOrderEntity order = bilOrderService.getByCode(contract_id);
        if(null==order){
            return RestResponse.failure("未查找到意向订单数据");
        }
        order.setIntention("1");
        bilOrderService.updateById(order);
        //法大大合同下载接口
        log.info("法大大进行合同下载,合同id:" + order.getCode());
        String url = iFadadaFegin.downloaddocs(order.getCode());
        if (StrUtil.isEmpty(url)) {
            return RestResponse.failure("法大大合同下载id为空！");
        }
        log.info("法大大合同下载结果:" + url);
        //将法大大返回的pdf上传到文件服务器
        //url = FileUtil.save(url, "pdf");
        url = minioUtil.save(url, "pdf");
        SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", order.getId()).eq("type", SysFileTypeEnum.INTENTION.getValue()));
        if (null != sysFileEntity) {
            sysFileEntity.setUrl(url);
            sysFileService.updateById(sysFileEntity);
        }
        return RestResponse.success();
    }

    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping(value = "/downloadBill", method = RequestMethod.POST)
    public RestResponse downloadBill(@RequestParam("orderid") String orderid,@RequestParam("ordercode") String ordercode,@RequestParam("payment") double payment,@RequestParam("sourcename") String sourcename){
        SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", orderid).eq("type", SysFileTypeEnum.DEPOSIT_BILL.getValue()));
        String url = "";
        if(sysFileEntity != null){
            url = sysFileEntity.getPath();
            return RestResponse.success().setAny("url",url);
        }
        String renterId = UoneSysUser.id();
        RenterEntity renter = null;
        try {
            renter = renterFegin.getById(renterId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        BilOrderEntity order = bilOrderService.getById(orderid);
        ResSourceEntity source = resSourceService.getById(order.getSourceId());
        ResProjectParaEntity resProjectParaEntity = projectParaService.getByCodeAndProjectId("BILL_CODE",source.getProjectId());
        String billcodeStr = resProjectParaEntity.getParamValue();
        String prex = billcodeStr.substring(0,6);
        String prexYM = billcodeStr.substring(6,12);
        String yearMonth = DateUtil.format(new Date(),"yyyyMM");
        int billcode = Integer.valueOf(billcodeStr.substring(6));
        String suffix = yearMonth+"0001";
        if(yearMonth.equals(prexYM)){
            suffix = (billcode + 1)+"";
        }
        billcodeStr = prex+suffix;
        resProjectParaEntity.setParamValue(billcodeStr);
        projectParaService.saveOrUpdate(resProjectParaEntity);
        //获取项目水印地址
        ResProjectParaEntity projectWatermark = projectParaService.getByCodeAndProjectId(ProjectParaEnum.PDF_WATERMARK.getValue(),source.getProjectId());
        String watermarkImage = projectWatermark==null?"":projectWatermark.getParamValue();
        Map<String,Object> beans = Maps.newHashMap();
        beans.put("watermarkImage", watermarkImage);
        beans.put("username", renter.getName());
        beans.put("year", DateUtil.thisYear());
        beans.put("month", DateUtil.thisMonth()+1);
        beans.put("day", DateUtil.thisDayOfMonth());
        beans.put("price", payment);
        beans.put("amountCN", Convert.digitToChinese(payment));
        if("10".equals(order.getOrderType())){
            beans.put("note", "该意向金在签订正式租赁合同之后将自动结转为房屋押金，押金不足部分将另行缴纳。");
            beans.put("project","意向金");
        }else if("5".equals(order.getOrderType())){
            beans.put("note", sourcename);
            beans.put("project","押金");
        }
        beans.put("billcode", billcodeStr);
        beans.put("sealPic",sysFileService.getByFromIdAndType("*********","seal").getPath());
        try {
            String html = ResourceUtil.readUtf8Str("excel/export/bill.html");
            //org.springframework.core.io.Resource resource = new ClassPathResource("/excel/export/bill.html");
            //FileReader fileReader = new FileReader(resource.getURI().getPath());
            //String html = fileReader.readString();
            String pdfName = ordercode+"pj.pdf";
            url = pdfUtil.pdf(html,beans,pdfName);

            SysFileEntity pdfEntity = new SysFileEntity();
            pdfEntity.setFromId(orderid)
                    .setName(pdfName)
                    .setType(SysFileTypeEnum.DEPOSIT_BILL.getValue())
                    .setUrl(url)
                    .insert();
            url = pdfEntity.getPath();
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
       return RestResponse.success().setAny("url",url);
    }

    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping(value = "/downBill", method = RequestMethod.POST)
    public RestResponse downBill(@RequestParam("orderid") String orderid,@RequestParam("ordercode") String ordercode,@RequestParam("payment") double payment,@RequestParam("sourcename") String sourcename,@RequestParam("payer") String payer){
        SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", orderid).eq("type", SysFileTypeEnum.DEPOSIT_BILL.getValue()));
        String url = "";
        if(sysFileEntity != null){
            url = sysFileEntity.getPath();
            return RestResponse.success().setAny("url",url);
        }
        BilOrderEntity order = bilOrderService.getById(orderid);
        ResSourceEntity source = resSourceService.getById(order.getSourceId());
        ResProjectParaEntity resProjectParaEntity = projectParaService.getByCodeAndProjectId("BILL_CODE",source.getProjectId());
        String billcodeStr = resProjectParaEntity.getParamValue();
        String prexYM = billcodeStr.substring(0,6);
        String yearMonth = DateUtil.format(new Date(),"yyyyMM");
        int billcode = Integer.valueOf(billcodeStr.substring(8));
        if(yearMonth.equals(prexYM)){
            billcode = billcode + 1;
        }else{
            billcode = 1;
        }
        billcodeStr = yearMonth+StrUtil.padPre(billcode+"",3,"0");
        resProjectParaEntity.setParamValue(billcodeStr);
        projectParaService.saveOrUpdate(resProjectParaEntity);
        //获取项目水印地址
        ResProjectParaEntity projectWatermark = projectParaService.getByCodeAndProjectId(ProjectParaEnum.PDF_WATERMARK.getValue(),source.getProjectId());
        String watermarkImage = projectWatermark==null?"":projectWatermark.getParamValue();
        Map<String,Object> beans = Maps.newHashMap();
        beans.put("watermarkImage", watermarkImage);
        beans.put("username", payer);
        Date payTime = order.getPayTime();
        beans.put("year", DateUtil.year(payTime));
        beans.put("month", DateUtil.month(payTime));
        beans.put("day", DateUtil.dayOfMonth(payTime));
        beans.put("price", payment);
        beans.put("amountCN", Convert.digitToChinese(payment));
        ResSourceVo sourceVo = resSourceService.getInfoById(order.getSourceId());
        String drawer = resProjectParaService.getByCodeAndProjectId("DRAWER",sourceVo.getProjectId()).getParamValue();
        beans.put("drawer",drawer);
        if("10".equals(order.getOrderType())){
            beans.put("note", "该意向金在签订正式租赁合同之后将自动结转为房屋押金，押金不足部分将另行缴纳。");
            beans.put("project","意向金");
        }else if("5".equals(order.getOrderType())){
            beans.put("note", sourcename);
            beans.put("project","押金");
        }else if("30".equals(order.getOrderType())){
            beans.put("note", sourcename);
            beans.put("project","能耗金");
        }
        beans.put("billcode", billcodeStr);

        //获取项目信息
        String projectId=UoneHeaderUtil.getProjectId();
        ResProjectEntity project = resProjectService.getById(projectId);
        SysCompanyEntity companyEntity = sysCompanyFegin.getById(project.getCompanyId());
        beans.put("companyName",companyEntity.getName());
        beans.put("sealPic",sysFileService.getByFromIdAndType(project.getCompanyId(),"seal").getPath());

        try {
            String html = ResourceUtil.readUtf8Str("excel/export/bill.html");
            String pdfName = ordercode+"pj.pdf";
            url = pdfUtil.pdf(html,beans,pdfName);

            SysFileEntity pdfEntity = new SysFileEntity();
            pdfEntity.setFromId(orderid)
                    .setName(pdfName)
                    .setType(SysFileTypeEnum.DEPOSIT_BILL.getValue())
                    .setUrl(url)
                    .insert();
            url = pdfEntity.getPath();
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        return RestResponse.success().setAny("url",url);
    }

    /**
     * 导出
     *
     * @param response
     * @param bilOrderSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/exportBill")
    @UoneLog("票据管理-导出票据")
    public void exportBill(HttpServletResponse response, BilOrderSearchVo bilOrderSearchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        bilOrderSearchVo.setBillInfo("1");
        List<BilOrderVo> list = bilOrderService.bilExport(bilOrderSearchVo);
        for (BilOrderVo vo : list) {
            if(OrderTypeEnum.YAJIN.getValue().equals(vo.getOrderType())){
                vo.setBillType("押金");
            }else if(OrderTypeEnum.DEPOSIT.getValue().equals(vo.getOrderType())){
                vo.setBillType("意向金");
                if(StrUtil.isNotBlank(vo.getContractId()) && "6".equals(vo.getCState())){
                    vo.setChanges("已转成押金");
                    ContContractEntity contContractEntity = contContractService.getById(vo.getContractId());
                    vo.setStartTimeStr(DateUtil.format(contContractEntity.getSignDate(), "yyyy-MM-dd"));
                }
                else
                    vo.setChanges("否");
            } else
                vo.setBillType("无效票据");
            vo.setBillName("电子票据");
            if(PayStateEnum.PAYCONFIR.getValue().equals(vo.getPayState())){
                vo.setPushType("已开票");
            } else if(PayStateEnum.NOPAY.getValue().equals(vo.getPayState())){
                vo.setPushType("待支付");
            } else
                vo.setPushType("无效票据");
            vo.setAddress(vo.getPartitionName()+vo.getAddress());
        }
        beans.put("orders", list);
        ExcelRender.me("/excel/export/billInfo.xlsx").beans(beans).render(response);
    }

    /**
     * 财务日报导出功能
     *
     * @param response
     * @param bilOrderSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/dayReportExport")
    @UoneLog("日报导出")
    public void dayReportExport(HttpServletResponse response, BilOrderSearchVo bilOrderSearchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        bilOrderSearchVo.setDaily("1");
        List<BilOrderVo> list = bilOrderService.bilExport(bilOrderSearchVo);
        for (BilOrderVo vo : list) {
            if(InvoiceStateEnum.INVOICED.getValue().equals(vo.getInvoiceState())){
                vo.setInvoiceState("已开票");
            }else
                vo.setInvoiceState("未开票");
            if(OrderTypeEnum.YAJIN.getValue().equals(vo.getOrderType()) || OrderTypeEnum.DEPOSIT.getValue().equals(vo.getOrderType())){
                if(PayStateEnum.PAYCONFIR.getValue().equals(vo.getPayState())){
                    vo.setInvoiceState("已开票");
                }else
                    vo.setInvoiceState("未开票");
            }
            vo.setPayment(vo.getActualPayment());
            vo.setOrderType(OrderTypeEnum.getNameByValue(vo.getOrderType()));
            vo.setPayState(PayStateEnum.getNameByValue(vo.getPayState()));
            vo.setPayWay(PayWayEnum.getNameByValue(vo.getPayWay()));
            vo.setFeeCharge(vo.getPayment().multiply(new BigDecimal("0.003")).setScale(2,BigDecimal.ROUND_HALF_UP));
            if(null!=vo.getInvoiceAmount()&&null!=vo.getTaxAmount())
                vo.setAmountNoTax(vo.getInvoiceAmount().subtract(vo.getTaxAmount()));
            vo.setAddress(vo.getPartitionName()+vo.getAddress());
        }
        beans.put("orders", list);
        ExcelRender.me("/excel/export/daysReport.xlsx").beans(beans).render(response);
    }

    @RequestMapping("/toVoidedBill")
    @UoneLog("票据作废")
    @Transactional
    public RestResponse toVoidedBill(@RequestParam String orderId, String remark){
        RestResponse response = new RestResponse();
        // 1 查找当前账单获取当前账单信息
        BilOrderEntity orderEntity = bilOrderService.getById(orderId);
        // 2 组装票据需要的参数
        Map<String,Object> beans = Maps.newHashMap();
        String url = "" ;
        beans.put("year", orderEntity.getCode().substring(5,9));
        beans.put("month", orderEntity.getCode().substring(9,11));
        beans.put("day", orderEntity.getCode().substring(11,13));
        beans.put("price", orderEntity.getPayment());
        beans.put("prices", "￥:" + orderEntity.getPayment());
        beans.put("amountCN", Convert.digitToChinese(orderEntity.getPayment()));
        ResSourceVo sourceVo = resSourceService.getInfoById(orderEntity.getSourceId());
        String drawer = resProjectParaService.getByCodeAndProjectId("DRAWER",sourceVo.getProjectId()).getParamValue();
        beans.put("drawer",drawer);
        if("10".equals(orderEntity.getOrderType())){
            beans.put("note", "该意向金在签订正式租赁合同之后将自动结转为房屋押金，押金不足部分将另行缴纳。");
            beans.put("project","意向金");
        }else if("5".equals(orderEntity.getOrderType())){
            beans.put("note", sourceVo.getHouseName());
            beans.put("project","押金");
        }
        beans.put("billcode", orderEntity.getBillsCode());
        beans.put("voidePic",sysFileService.getByFromIdAndType("*********","voide").getPath());
        beans.put("sealPic",sysFileService.getByFromIdAndType("*********","seal").getPath());
        try {
            beans.put("username", renterFegin.getById(orderEntity.getPayerId()).getName());
            String html = ResourceUtil.readUtf8Str("excel/export/voidBill.html");
            org.jsoup.nodes.Document doc = Jsoup.parse(html);
            // jsoup标准化标签，生成闭合标签
            doc.outputSettings().syntax(org.jsoup.nodes.Document.OutputSettings.Syntax.xml);
            doc.outputSettings().escapeMode(Entities.EscapeMode.xhtml);
            html = doc.html();
            String pdfName = orderEntity.getCode()+"pj.pdf";
            //获取项目水印地址
            ResProjectParaEntity projectWatermark = projectParaService.getByCodeAndProjectId(ProjectParaEnum.PDF_WATERMARK.getValue(),sourceVo.getProjectId());
            String watermarkImage = projectWatermark==null?"":projectWatermark.getParamValue();
            beans.put("watermarkImage", watermarkImage);
            url = pdfUtil.pdf(html,beans,pdfName);
            SysFileEntity pdfEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", orderId).eq("type", SysFileTypeEnum.DEPOSIT_BILL.getValue()));
            pdfEntity.setUrl(url);
            sysFileService.updateById(pdfEntity);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        // 修改订单信息
        orderEntity.setBillsState("1");
        orderEntity.setRemark(remark);
        bilOrderService.updateById(orderEntity);
        return response.setSuccess(true).setMessage("票据作废成功！！！") ;
    }


    /**
     * 财务信息 日报汇总分页查询
     *
     * @return
     */
    @RequestMapping("/summaryForPage")
    public RestResponse summaryForPage(Page page, BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndCreateDate())) {
            bilOrderSearchVo.setEndCreateDate(DateUtil.endOfDay(bilOrderSearchVo.getEndCreateDate()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayTime())) {
            bilOrderSearchVo.setEndPayTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPushTime())) {
            bilOrderSearchVo.setEndPushTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPushTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayableTime())) {
            bilOrderSearchVo.setEndPayableTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayableTime()));
        }
        bilOrderSearchVo.setCarKeyWord(true);
        IPage<DailyOrderVo> data = bilOrderService.findsummaryByCondition(page, bilOrderSearchVo);
        return response.setSuccess(true).setData(data);
    }

    /**
     * 财务信息 日报分页查询
     *
     * @return
     */
    @RequestMapping("/dailyForPage")
    public RestResponse dailyForPage(Page page, BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndCreateDate())) {
            bilOrderSearchVo.setEndCreateDate(DateUtil.endOfDay(bilOrderSearchVo.getEndCreateDate()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayTime())) {
            bilOrderSearchVo.setEndPayTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPushTime())) {
            bilOrderSearchVo.setEndPushTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPushTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayableTime())) {
            bilOrderSearchVo.setEndPayableTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayableTime()));
        }
        bilOrderSearchVo.setCarKeyWord(true);
        IPage<DailyOrderVo> data = bilOrderService.findDailyByCondition(page, bilOrderSearchVo);
        return response.setSuccess(true).setData(data);
    }

    /**
     * 日报导出功能
     *
     * @param response
     * @param bilOrderSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/dailyExport")
    public void dailyExport(HttpServletResponse response, BilOrderSearchVo bilOrderSearchVo) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        List<DailyOrderVo> list = bilOrderService.dailyExport(bilOrderSearchVo);
        String projectName = "" ;
        if(list.size()>0)
            projectName = list.get(0).getProjectName();
        beans.put("projectName",projectName);
        beans.put("orders", list);
        ExcelRender.me("/excel/export/dailyReport.xls").beans(beans).render(response);
    }

    /**
     *  票据信息查询
     *
     * @return
     */
    @RequestMapping("/getNotesPage")
    @UoneLog("票据管理-数据加载/筛选")
    public RestResponse getNotesPage(Page page, BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndCreateDate())) {
            bilOrderSearchVo.setEndCreateDate(DateUtil.endOfDay(bilOrderSearchVo.getEndCreateDate()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayTime())) {
            bilOrderSearchVo.setEndPayTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPushTime())) {
            bilOrderSearchVo.setEndPushTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPushTime()));
        }
        if (ObjectUtil.isNotNull(bilOrderSearchVo.getEndPayableTime())) {
            bilOrderSearchVo.setEndPayableTime(DateUtil.endOfDay(bilOrderSearchVo.getEndPayableTime()));
        }
        bilOrderSearchVo.setCarKeyWord(true);
        bilOrderSearchVo.setBillInfo("1");
        bilOrderSearchVo.setState("20");
        IPage<BilOrderVo> data=bilOrderService.findByCondition(page, bilOrderSearchVo);
        return response.setSuccess(true).setData(data);  //前端页面显示总金额，试过和setAny()中，layui识别不到，只能放在message中便于传递
    }

    /**
     * 下载票据信息
     *
     * @param response
     * @param bilOrderSearchVo
     * @throws BusinessException
     */
    @RequestMapping("/downZip")
    public void downZip(HttpServletResponse response, BilOrderSearchVo bilOrderSearchVo) throws Exception {
        bilOrderSearchVo.setBillInfo("1");
        List<BilOrderVo>  files = bilOrderService.getBillPdf(bilOrderSearchVo);
        OSS ossClient = new OSSClientBuilder().build(FileUtil.getEndPoint(), FileUtil.getAki(), FileUtil.getAks());
        response.setContentType("application/x-zip-compressed");
        ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream());
        for (BilOrderVo vo : files) {
            OSSObject ossObject = ossClient.getObject(FileUtil.getBucketName(), vo.getAddress());
            ZipEntry zipEntry = new ZipEntry(vo.getBillsCode()+"_"+vo.getSigner()+".pdf");
            zipOutputStream.putNextEntry(zipEntry);
            IOUtils.copy(ossObject.getObjectContent(), zipOutputStream);
            ossObject.close();
        }
        zipOutputStream.closeEntry();
        zipOutputStream.finish();
    }

    /**
     * 释放房源
     */
    @RequestMapping("/toReleaseHouse")
    @Transactional
    public RestResponse toReleaseHouse(String id) {
        RestResponse response = new RestResponse();
        try {
            BilOrderEntity entity = bilOrderService.getById(id);
            ResSourceEntity sourceEntity = resSourceService.getById(entity.getSourceId());
            sourceEntity.setState(SourceStateEnum.UNRENT.getValue());
            resSourceService.updateById(sourceEntity);
            entity.setIntention("0");
            bilOrderService.updateById(entity);
            response.setSuccess(true).setMessage("释放成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 手动批量确认支付
     */
    @RequestMapping("/batchConfirmPay")
    @UoneLog("批量确认支付")
    @Transactional
    public RestResponse batchConfirmPay(@RequestParam List<String> ids,Date payTime,String payWay,String tradeCode,String remark) {
        RestResponse response = new RestResponse();
        try {
            boolean checkState = true;//默认状态检查通过
            for (String id : ids) {
                BilOrderEntity entity = bilOrderService.getById(id);
                String payState = entity.getPayState();
                if(!payState.equals("10")){//存在待支付以外的其他状态,则设置状态检查为不通过
                    checkState  = false;
                }
            }
            if(!checkState){
                response.setSuccess(false).setMessage("存在待支付以外的其他账单,请检查!");
                return response;
            }
            for (String id : ids) {
                BilOrderEntity billOrder = bilOrderService.getById(id);//账单记录
                BigDecimal payablePayment = billOrder.getPayablePayment();//应付金额
                billOrder.setActualPayment(payablePayment);//更新实际支付金额
                billOrder.setPayState(PayStateEnum.PAYCONFIR.getValue());//更新支付状态
                billOrder.setTradeCode(tradeCode);//更新支付单号
                billOrder.setRemark(remark);
                billOrder.setPayTime(payTime);
                billOrder.setPayWay(payWay);
                boolean isPush = billOrder.getPush();
                if(!isPush){//如果账单未推送,将其改为已推送 caizhanghe edit 2025-05-11
                    billOrder.setPush(true);
                }
                //entity.setUpdateDate(new Date());
                //entity.setInvoiceType(InvoiceTypeEnum.PERSONAL.getValue());//更新为已支付
                //entity.setInvoiceState(InvoiceStateEnum.INVOICED.getValue());
                bilOrderService.updateById(billOrder);
                String billOrderId = billOrder.getId();
                BilOrderConfirmEntity confirmEntity = new BilOrderConfirmEntity();//支付确认申请记录
                confirmEntity.setOrderId(billOrderId);
                confirmEntity.setSigner(billOrder.getPayerId());
                confirmEntity.setType("2");//type 2 收款
                confirmEntity.setState("1");//state 1 已确认
                confirmEntity.setTradeCode(tradeCode);//更新支付单号
                confirmEntity.setRemark(remark);
                confirmEntity.setPrice(payablePayment);
                confirmEntity.setApplyTime(payTime);
                confirmEntity.setPayWay(payWay);
                orderConfirmService.save(confirmEntity);
                BilTransferEntity bilTransfer = new BilTransferEntity();//账单转账记录
                bilTransfer.setTransferType(TransferTypeEnum.OTHER.getValue());
                bilTransfer.setPayment(payablePayment);
                bilTransfer.setTransferTime(payTime);
                bilTransfer.setOrderId(billOrderId);
                bilTransfer.setRemark(remark);
                bilTransfer.setApprovalState(ApprovalStateEnum.COMPLETE.getValue());
                bilTransferService.save(bilTransfer);
                //reportInvoiceService.makeInvoice(entity);
            }
            response.setSuccess(true).setMessage("提交成功！");
        } catch (Exception e) {
            e.printStackTrace();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /*@RequestMapping("/toTestPushMsg")
    @UonePermissions
    public void testUnionNotify() throws Exception {
        List<BilOverdueVo> orders = bilOrderService.getOverdueBil();
        autoService.overduePushMsg(orders);
    }*/

    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping(value = "/billCall", method = RequestMethod.POST)
    public RestResponse billCall(@RequestParam("orderid") String orderid) throws Exception {
        SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", orderid).eq("type", SysFileTypeEnum.CALL_BILL.getValue()));
        String url = "";
        if(sysFileEntity != null){
            url = sysFileEntity.getPath();
            return RestResponse.success().setAny("url",url);
        }
        BilOrderEntity order = bilOrderService.getById(orderid);
        RenterEntity renter = renterFegin.getById(order.getPayerId());
        ResSourceVo source = resSourceService.getSourceVoByResourceOperation(order.getSourceId());
        String times = DateUtil.format(order.getPayableTime(),"yyyy-mm-dd");
        String year = times.substring(0,4);
        String month = times.substring(5,7);
        String day = times.substring(8,10);
        String now = DateUtil.format(new Date() ,"yyyy-mm-dd");
        Map<String,Object> beans = Maps.newHashMap();
        beans.put("rentName", renter.getName());
        beans.put("year", year);
        beans.put("month", month);
        beans.put("day", day);
        beans.put("cyear", now.substring(0,4));
        beans.put("cmonth", now.substring(5,7));
        beans.put("cday", now.substring(8,10));
        beans.put("payment", order.getPayment());
        beans.put("sourceName",source.getProjectName()+source.getPartitionName()+source.getCode());
        beans.put("billCycle",DateUtil.format(order.getPayableTime(),"yyyy-mm-dd"));
        beans.put("billType",OrderTypeEnum.getNameByValue(order.getOrderType()));
        beans.put("amountCN", Convert.digitToChinese(order.getPayment()));
        try {
            String html = ResourceUtil.readUtf8Str("excel/export/reminder.html");
            String pdfName = order.getCode()+"pj.pdf";
            //获取项目水印地址
            ResProjectParaEntity projectWatermark = projectParaService.getByCodeAndProjectId(ProjectParaEnum.PDF_WATERMARK.getValue(),source.getProjectId());
            String watermarkImage = projectWatermark==null?"":projectWatermark.getParamValue();
            beans.put("watermarkImage", watermarkImage);
            url = pdfUtil.pdf(html,beans,pdfName);
            SysFileEntity pdfEntity = new SysFileEntity();
            pdfEntity.setFromId(orderid)
                    .setName(pdfName)
                    .setType(SysFileTypeEnum.CALL_BILL.getValue())
                    .setUrl(url)
                    .insert();
            url = pdfEntity.getPath();
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        return RestResponse.success().setAny("url",url);
    }

    /**
     * 催缴书
     * @param orderId
     * @return
     * @throws Exception
     */
    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping(value = "/billCallByContTemp", method = RequestMethod.POST)
    @UoneLog("生成催缴书")
    public RestResponse billCallByContTemp(@RequestParam("orderId") String orderId,@RequestParam("unPaidRent") BigDecimal unPaidRent,@RequestParam("weiyujin") BigDecimal weiyujin) throws Exception {
        SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", orderId).eq("type", SysFileTypeEnum.CALL_BILL.getValue()));
        if(sysFileEntity != null){
            try {
                minioUtil.delete(sysFileEntity.getUrl());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String url = contTempService.remindPdf(orderId,unPaidRent,weiyujin);
        return RestResponse.success().setAny("url",FileUtil.getPath(url));
    }

    /**
     * 催缴书
     * @param orderId
     * @return
     * @throws Exception
     */
    @UonePermissions(value = LoginType.CUSTOM)
    @RequestMapping(value = "/sendBillCall", method = RequestMethod.POST)
    @UoneLog("发送催缴书")
    public RestResponse sendBillCall(@RequestParam("orderId") String orderId,@RequestParam("unPaidRent") BigDecimal unPaidRent,@RequestParam("weiyujin") BigDecimal weiyujin) throws Exception {
        SysFileEntity sysFileEntity = sysFileService.getOne(new QueryWrapper<SysFileEntity>().eq("from_id", orderId).eq("type", SysFileTypeEnum.CALL_BILL.getValue()));
        if(sysFileEntity != null){
            try {
                minioUtil.delete(sysFileEntity.getUrl());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String url = contTempService.remindPdf(orderId,unPaidRent,weiyujin);
        BilOrderEntity order = bilOrderService.getOrderById(orderId);
        RenterEntity renter = renterFegin.getById(order.getPayerId());
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("mobile", renter.getTel());
        params.put("name",renter.getName());
        params.put("url",url);
        params.put("template_code", "97866");
        zzctSysMsgTemplateFegin.send(params);
        return RestResponse.success();
    }

    /**
     * 查询意向金退款账单-管家审核-待办任务
     *miniUnionPay
     * @return
     * <AUTHOR>
     * @date 2019-01-14 14:20
     * @Param:
     */
    @RequestMapping("/intentRefundPage")
    public RestResponse intentRefundPage(Page page,String payState,@RequestParam("isSelf") boolean isSelf) {
        Map<String,Object> taskMap = Maps.newHashMap();
        try {
            taskMap = flowTaskService.todoMap(isSelf,"意向金退款审批");
            taskMap.put("1","1");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> contractIds = taskMap.keySet();
        Map<String, Object> map = Maps.newHashMap();
        map.put("project_id",UoneHeaderUtil.getProjectId());
        map.put("contractIds", contractIds);
        map.put("payState",payState);
        IPage<Map<String, Object>> iPage = bilOrderService.selectPageByIntent(page,map);
        for(Map<String, Object> record:iPage.getRecords()){
            FlowTaskDto flowTask = (FlowTaskDto) taskMap.get(record.get("id"));
            record.put("taskId",flowTask.getTaskId());
            record.put("procInsId",flowTask.getProcInsId());
            record.put("deployId",flowTask.getDeployId());
            record.put("taskName",flowTask.getTaskName());
            record.put("createTime",DateUtil.formatDateTime(flowTask.getCreateTime()));
        }
        return RestResponse.success().setData(iPage);
    }

    //退款审核
    @RequestMapping("/toAudit")
    @UoneLog("意向金退款审核")
    @Transactional
    public RestResponse toAudit(String id,boolean yesOrNo,String remark) throws Exception {
        BilOrderEntity order = bilOrderService.getById(id);
        order.setRemark(remark);
        if(yesOrNo){
            order.setPayState(PayStateEnum.REFUNDPENDING.getValue());
        }else{
            order.setPayState(PayStateEnum.REFUNDAUDITNOT.getValue());
        }
        bilOrderService.updateById(order);
        return RestResponse.success();
    }

    /**
     * 查询意向金退款账单-管家审核-已办任务
     *miniUnionPay
     * @return
     * <AUTHOR>
     * @date 2019-01-14 14:20
     * @Param:
     */
    @RequestMapping("/finishedIntentRefundPage")
    public RestResponse finishedIntentRefundPage(Page page,@RequestParam("payStates") List<String> payStates,@RequestParam("isSelf") boolean isSelf,@RequestParam(required = false) String keyWord) {
        Map<String,Object> taskMap = Maps.newHashMap();
        try {
            taskMap = flowTaskService.finishedSignMap(isSelf,"意向金退款审批");
            taskMap.put("1","1");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> contractIds = taskMap.keySet();
        Map<String, Object> map = Maps.newHashMap();
        map.put("project_id",UoneHeaderUtil.getProjectId());
        map.put("contractIds", contractIds);
        map.put("payStates", payStates);
        if(StrUtil.isNotBlank(keyWord)){
            map.put("keyWord", keyWord);
        }
        IPage<Map<String, Object>> iPage = bilOrderService.selectPageByIntent(page,map);
        for(Map<String, Object> record:iPage.getRecords()){
            FlowTaskDto flowTask = (FlowTaskDto) taskMap.get(record.get("id"));
            record.put("taskId",flowTask.getTaskId());
            record.put("taskName",flowTask.getTaskName());
            record.put("procInsId",flowTask.getProcInsId());
            record.put("deployId",flowTask.getDeployId());
            record.put("createTime",DateUtil.formatDateTime(flowTask.getCreateTime()));
        }
        return RestResponse.success().setData(iPage);
    }

    /**
     * 取消退款审核
     * @param id
     * @param remark
     * @return
     */
    @PostMapping("/cancelRefundAudit")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "cancelRefundAudit", expire = 30)
    @UoneLog("取消退款审核")
    public RestResponse cancelRefundAudit(String id, String remark){
        if (StrUtil.isEmpty(remark)) {
            return RestResponse.failure("取消原因不能为空");
        }
        BilOrderEntity order = bilOrderService.getById(id);
        order.setRemark(remark);
        order.setPayState(PayStateEnum.REFUNDTOAUDIT.getValue());
        bilOrderService.updateById(order);
        return RestResponse.success();
    }

    @RequestMapping("/getToPayRentOrderList")
    public RestResponse getToPayRentOrderList(String contractId) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("contractId",contractId);
        map.put("orderType",OrderTypeEnum.RENT.getValue());
        map.put("payState",PayStateEnum.NOPAY.getValue());
        map.put("yearMonth",DateUtil.format(new Date(),"yyyyMM"));
        List<BilOrderVo> list = bilOrderService.getOrderList(map);
        return RestResponse.success().setData(list);
    }

    /**
     * 批量开票
     */
    @RequestMapping("/batchMakeInvoice")
    @UoneLog("批量开具发票")
    @CacheLock(prefix = "batchMakeInvoice", expire = 120)
    public RestResponse batchMakeInvoice(@RequestParam List<String> ids) {
        RestResponse response = new RestResponse();
        
        // 初始化日志上下文，便于问题追踪
        String batchRequestId = UUID.randomUUID().toString().replace("-", "");
        MDC.put("batchRequestId", batchRequestId);
        MDC.put("operation", "batchMakeInvoice");
        
        log.info("开始处理批量开票请求, 订单数量: {}, 批次ID: {}", ids.size(), batchRequestId);
        
        try {
            if (ids == null || ids.isEmpty()) {
                log.warn("批量开票参数为空, 批次ID: {}", batchRequestId);
                response.setSuccess(false);
                response.setMessage("请选择至少一个订单进行开票");
                return response;
            }
            
            // 调用批量开票服务
            BatchInvoiceResultVo result = reportInvoiceService.batchMakeInvoice(ids);
            
            // 构建响应消息
            StringBuilder message = new StringBuilder();
            message.append("处理完成：共").append(result.getTotalCount()).append("个订单，");
            message.append("成功").append(result.getSuccessCount()).append("个，");
            message.append("失败").append(result.getFailureCount()).append("个。");
            
            // 如果有失败的订单，添加失败详情
            if (result.getFailureCount() > 0) {
                message.append("\n失败订单详情：");
                for (BatchInvoiceResultVo.FailedInvoiceDetail detail : result.getFailureList()) {
                    message.append("\n订单 ").append(detail.getOrderId())
                           .append(" 失败原因: ").append(detail.getReason());
                }
            }
            
            log.info("批量开票请求处理完成, 批次ID: {}, 总数: {}, 成功: {}, 失败: {}", 
                    batchRequestId, result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
            
            response.setSuccess(true);
            response.setMessage(message.toString());
            response.setData(result);
            
        } catch (Exception e) {
            // 记录详细错误日志
            log.error("批量开票操作发生未预期异常, 批次ID: {}", batchRequestId, e);
            
            // 返回错误响应
            response.setSuccess(false);
            response.setMessage("批量开票失败：系统异常，请联系管理员。错误ID: " + batchRequestId);
        } finally {
            // 清理日志上下文
            MDC.remove("batchRequestId");
            MDC.remove("operation");
        }
        
        return response;
    }

}
