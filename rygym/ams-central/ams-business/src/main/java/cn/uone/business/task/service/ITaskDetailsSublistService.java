package cn.uone.business.task.service;

import cn.uone.bean.entity.business.task.TaskDetailsSublistEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
public interface ITaskDetailsSublistService extends IService<TaskDetailsSublistEntity> {

    List<TaskDetailsSublistEntity> getByDetailsId(String detailsId);
}
