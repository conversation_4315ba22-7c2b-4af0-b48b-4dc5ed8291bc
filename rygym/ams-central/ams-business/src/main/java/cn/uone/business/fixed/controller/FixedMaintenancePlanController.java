package cn.uone.business.fixed.controller;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.multipart.UploadFile;
import cn.hutool.json.JSONUtil;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.base.BaseAccountEntity;
import cn.uone.bean.entity.business.fixed.FixedMaintenanceAssertEntity;
import cn.uone.bean.entity.business.fixed.FixedMaintenancePlanEntity;
import cn.uone.bean.entity.business.fixed.FixedMaintenanceTaskEntity;
import cn.uone.bean.entity.business.fixed.FixedMaintenanceTeamEntity;
import cn.uone.bean.entity.business.fixed.vo.FixedMaintenanceAssertVo;
import cn.uone.bean.entity.business.fixed.vo.FixedMaintenancePlanVo;
import cn.uone.bean.entity.business.fixed.vo.FixedMaintenanceTaskVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.business.fixed.service.IFixedMaintenanceAssertService;
import cn.uone.business.fixed.service.IFixedMaintenancePlanService;
import cn.uone.business.fixed.service.IFixedMaintenanceTaskService;
import cn.uone.business.fixed.service.IFixedMaintenanceTeamService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.cache.util.CacheUtil;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;
import org.springframework.web.multipart.MultipartFile;

import javax.tools.FileObject;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 维保方案表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@RestController
@RequestMapping("/fixed/fixed-maintenance-plan-entity")
public class FixedMaintenancePlanController extends BaseController {
    @Autowired
    IFixedMaintenancePlanService fixedMaintenancePlanService;
    @Autowired
    IFixedMaintenanceTaskService fixedMaintenanceTaskService;
    @Autowired
    IFixedMaintenanceAssertService fixedMaintenanceAssertService;
    @Autowired
    ISysFileService sysFileService;
    @Autowired
    MinioUtil minioUtil;
    /**
     * 获取信息
     *
     * @param fixedMaintenancePlanEntity
     * @return 维保方案列表 分页
     */
    @GetMapping("/page")
    public RestResponse page(Page page, FixedMaintenancePlanEntity fixedMaintenancePlanEntity) {
        IPage<FixedMaintenancePlanEntity> iPage = fixedMaintenancePlanService.page(page, fixedMaintenancePlanEntity);
        return RestResponse.success().setData(iPage);
    }

    /**
     * 获取信息
     *
     * @param id 主键
     * @return 维保方案信息
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        FixedMaintenancePlanEntity info = fixedMaintenancePlanService.getById(id);
        return RestResponse.success().setData(info);
    }

    /**
     * 新增
     *
     * @return
     */
    @PostMapping("/save")
    @Transactional
    public RestResponse save(@RequestBody FixedMaintenancePlanVo data) {
        //保存更新方案信息
        try {
            String id = data.getId();
            if(StrUtil.isNotBlank(id)){
                //删除旧任务
                List<FixedMaintenanceTaskEntity> oldTaskList = fixedMaintenanceTaskService.getByPlanId(id);
                for(FixedMaintenanceTaskEntity entity:oldTaskList){
                    //删除任务文件
                    sysFileService.delFileByFromIdAndType(entity.getId(), SysFileTypeEnum.MAINTENANCE_TASK);
                    //删除耗材
                    fixedMaintenanceAssertService.deleteByTaskId(entity.getId());
                    fixedMaintenanceTaskService.removeById(entity.getId());
                }
                fixedMaintenancePlanService.updateById(data);
            }else{
                data.setCode(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN));
                fixedMaintenancePlanService.save(data);
                id = data.getId();
            }

            int hours = 0;
            //保存任务
            long i = 1l;
            for(FixedMaintenanceTaskVo vo:data.getTaskList()) {
                vo.setPlanId(id);
                vo.setCode(i++);
                fixedMaintenanceTaskService.save(vo);
                //计算用时
                hours += Integer.valueOf(vo.getPlanHours());
                //保存文件
                List<String> files = vo.getFiles();
                if(files != null && files.size()>0){
                    for (String url : files) {
                        SysFileEntity fileEntity = new SysFileEntity();
                        fileEntity.setUrl(url);
                        fileEntity.setType(SysFileTypeEnum.MAINTENANCE_TASK.getValue());
                        fileEntity.setFromId(vo.getId());
                        sysFileService.save(fileEntity);
                    }
                }
                //保存耗材
                List<FixedMaintenanceAssertEntity> assertList = Lists.newArrayList();
                List<FixedMaintenanceAssertVo> asserts = vo.getAsserts();
                if(asserts != null && asserts.size()>0){
                    for (FixedMaintenanceAssertVo assertVo : asserts) {
                        FixedMaintenanceAssertEntity entity = new FixedMaintenanceAssertEntity();
                        entity.setTaskId(vo.getId());
                        entity.setAssertId(assertVo.getId());
                        entity.setNum(assertVo.getNum());
                        assertList.add(entity);
                    }
                    if (assertList != null && assertList.size() > 0) {
                        fixedMaintenanceAssertService.saveBatch(assertList, assertList.size());
                    }
                }
            }
            //更新结束日期
            if(hours >0){
                Date enDate = DateUtil.offsetHour(data.getStartDate(),hours);
                data.setEndDate(enDate);
                data.setPlanHours(hours+"");
                fixedMaintenancePlanService.updateById(data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        return RestResponse.success("保存成功");
    }

    /**
     * 多文件上传
     * @param files
     * @return
     */
    @RequestMapping("/uploadFiles")
    public RestResponse uploadFiles(List<MultipartFile> files) {
        RestResponse response = new RestResponse();
        List<String> urls = Lists.newArrayList();
        try {
            for(MultipartFile file:files){
                //String url = FileUtil.save(file);
                String url = minioUtil.save(file);
                urls.add(url);
            }
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response.setSuccess(true).setData(urls);
    }

    /**
     * 修改
     *
     * @param fixedMaintenancePlanEntity 参数
     * @return
     */
    @PostMapping("/edit")
    public RestResponse edit(FixedMaintenancePlanEntity fixedMaintenancePlanEntity) {
        if(fixedMaintenancePlanService.updateById(fixedMaintenancePlanEntity)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        if(fixedMaintenancePlanService.removeByIds(ids)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
}
