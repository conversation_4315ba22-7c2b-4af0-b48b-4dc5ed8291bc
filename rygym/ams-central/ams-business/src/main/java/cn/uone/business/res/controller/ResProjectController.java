package cn.uone.business.res.controller;


import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.ApprovalTemplateEnum;
import cn.uone.application.enumerate.ProjectParaEnum;
import cn.uone.application.enumerate.source.OperateStateEnum;
import cn.uone.application.enumerate.source.PriceTypeEnum;
import cn.uone.bean.entity.business.apro.ApprovalProjectParaEntity;
import cn.uone.bean.entity.business.res.*;
import cn.uone.bean.entity.business.res.vo.*;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.ProjectEntity;
import cn.uone.bean.parameter.DataScopeVo;
import cn.uone.business.apro.service.IApprovalProjectParaService;
import cn.uone.business.res.service.*;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.util.BaiduMapUtil;
import cn.uone.fegin.bus.IResProjectFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.constant.BaseConstants;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/res-project-entity")
public class ResProjectController extends BaseController implements IResProjectFegin {

    @Autowired
    private IResProjectService resProjectService;

    @Autowired
    private IResPropertyPartitionService resPropertyPartitionService;

    @Autowired
    private IResSourceService resSourceService;

    @Autowired
    private IResCostConfigureService resCostConfigureService;

    @Autowired
    private IResCostConfigureDetailService resCostConfigureDetailService;

    @Autowired
    private IResPlanPartitionService resPlanPartitionService;

    @Autowired
    private IResProjectInfoService resProjectInfoService;

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private IResCostLadderService resCostLadderService;

    @Autowired
    private IResProjectParaService resProjectParaService;


    @Autowired
    private IUserFegin userFegin;
    @Autowired
    private IApprovalProjectParaService projectParaService;
    @Autowired
    private IResCollectService resCollectService;

    /**
     * 获取项目列表
     *
     * @return
     */
    @RequestMapping("/getList")
    @UonePermissions
    public RestResponse getList() {
        RestResponse response = new RestResponse();
        QueryWrapper<ResProjectEntity> wrapper = new QueryWrapper<ResProjectEntity>();
        wrapper.eq("operate_state", OperateStateEnum.ON_SHELVES.getValue());
        return response.setSuccess(true).setData(resProjectService.list(wrapper));
    }

//    @RequestMapping("/selectRoom")
//    public RestResponse getRoomList(String id) {
//        RestResponse response = new RestResponse();
//        QueryWrapper<ResProjectEntity> wrapper = new QueryWrapper<ResProjectEntity>();
//        wrapper.eq("id", id);
//        return response.setSuccess(true).setData(resProjectService.list(wrapper));
//    }

    @GetMapping("/selectProjectByName")
    @ApiOperation(value = "获取项目列表")
    public RestResponse selectProjectByName(ResProjectEntity project, Page page) {
        IPage<ResProjectEntity> rpe = resProjectService.queryProject(page, project);
        return RestResponse.success().setData(rpe);
    }

    @GetMapping("/selectPageByDataScope")
    @ApiOperation(value = "获取项目列表")
    @RequiresPermissions("projectList")
    public RestResponse selectPageByDataScope(Page page) {
        IPage<ResProjectEntity> rpe = resProjectService.selectPageByDataScope(page);
        return RestResponse.success().setData(rpe);
    }

    @RequestMapping("/allProjects")
    @ApiOperation(value = "租客端获取项目列表")
    @UonePermissions
    public RestResponse allProjects(Page page, ProjectSearchVo searchVo) {
        GPS gps = BaiduMapUtil.gcj02_To_Bd09(searchVo.getLatitude(),searchVo.getLongitude());
        searchVo.setLatitude(gps.getLat());
        searchVo.setLongitude(gps.getLon());
        searchVo.setIsHot("1");//临时处理
        IPage<ProjectByHouseTypeVo> rpv = resProjectService.getAllProjects(page,searchVo);
        return RestResponse.success().setData(rpv);
    }

    @PostMapping("/selectProjectWithPartition")
    @ApiOperation(value = "获取项目和权属分区")
    public RestResponse selectProjectWithPartition(String id) {
        ResProjectEntity entity=resProjectService.queryById(id);
        ResPropertyPartitionEntity partitionEntity = new ResPropertyPartitionEntity();
        partitionEntity.setProjectId(id);
        List<Map<String,Object>> list = resPropertyPartitionService.listWithOwner(id);
        Map<String,Object> map = new HashMap<>();
        map.put("project",entity);
        map.put("partitionList",list);
        return RestResponse.success().setData(map);
    }

    @ApiOperation(value = "新建项目")
    @PostMapping("/insertProject")
    public RestResponse insertProject(ResProjectEntity entity) {
        if(entity==null){
            return RestResponse.failure("请求对象为空");
        }
        if (entity.getCode().length() > 10) {
            return RestResponse.failure("无法输入超过10位字符。");
        }
        if (StrUtil.isNotEmpty(entity.getCode())) {
            ResProjectEntity project = resProjectService.getByCondition(new ResProjectEntity().setCode(entity.getCode()));
            if (ObjectUtil.isNotNull(project)) {
                return RestResponse.failure("该编码和其他项目重复，请重新填写！");
            }
        }
        entity.setCityCode(UoneHeaderUtil.getCityCode());
        if (StrUtil.isNotBlank(entity.getId())) {
            resProjectService.saveOrUpdate(entity);
            return RestResponse.success("提交成功").setData(entity);
        }
        resProjectService.saveOrUpdate(entity);
        //添加费用配置
        resProjectService.insertCharge(entity.getId());
        //费用配置名称
        List<Map<String, Object>> list = PriceTypeEnum.toList();
        //费用配置类别
        ResCostConfigureEntity rcce = new ResCostConfigureEntity();
        rcce.setProjectId(entity.getId());
        rcce.setName("基础配置");
        rcce.setType("0");
        resCostConfigureService.save(rcce);
        //因为基础费用配置的主体和费用标准并没有控制先后填写，所以为了保证填写费用标准时已经有费用配置类型，必须先建立好
        for (Map<String, Object> map : list) {
            ResCostConfigureDetailEntity rccde = new ResCostConfigureDetailEntity();
            rccde.setCostConfigureId(rcce.getId());
            rccde.setCostType(map.get("value").toString());
            resCostConfigureDetailService.save(rccde);
        }
        //新建项目参数配置
        List<Map<String, Object>> paraList = ProjectParaEnum.toList();
        for (Map<String, Object> map : paraList) {
            ResProjectParaEntity projectParaEntity = new ResProjectParaEntity();
            projectParaEntity.setProjectId(entity.getId());
            projectParaEntity.setParamName(map.get("name").toString());
            projectParaEntity.setParamCode(map.get("value").toString());
            //第三方平台参数默认为0
            if (ProjectParaEnum.INCLUDE_THIRD.getValue().equals(map.get("value").toString())
            ) {
                projectParaEntity.setParamValue(BaseConstants.BOOLEAN_OF_FALSE);
            } else {
                projectParaEntity.setParamValue(BaseConstants.BOOLEAN_OF_TRUE);
            }
            //账单催付时间 默认值为48小时
            if (ProjectParaEnum.PAY_TIME.getValue().equals(map.get("value").toString())) {
                projectParaEntity.setParamValue("48");
            }
            //配置三级巡查到期前几天提醒 默认值为7天
            if (ProjectParaEnum.PATROL_REMIND.getValue().equals(map.get("value").toString())) {
                projectParaEntity.setParamValue("7");
            }
            projectParaEntity.insert();
        }
        //审批模板
        List<Map<String, String>> temps= ApprovalTemplateEnum.toList2();
        temps.forEach(temp->{
            ApprovalProjectParaEntity param= projectParaService.getOne(new QueryWrapper<ApprovalProjectParaEntity>().eq("project_id",entity.getId()).eq("type",temp.get("type")));
            if(ObjectUtil.isNull(param)){
                param = new ApprovalProjectParaEntity();
                param.setProjectId(entity.getId());
                param.setApprovalTempId(temp.get("value"));
                param.setName(temp.get("name"));
                param.setType(temp.get("type"));
                projectParaService.save(param);
            }
        });
        return RestResponse.success("提交成功").setData(entity);
    }

    @ApiOperation(value = "编辑项目状态")
    @PostMapping("/updateStateProject")
    @Transactional
    public RestResponse updateStateProject(String id,
                                           String expandState,
                                           String operateState){
        if(StrUtil.isBlank(id)){
            return RestResponse.failure("请求项目为空");
        }
        if(StrUtil.isBlank(expandState)){
            return RestResponse.failure("拓展状态为空");
        }
        if(StrUtil.isBlank(operateState)){
            return RestResponse.failure("运营状态为空");
        }

        ResProjectEntity projectEntity = new ResProjectEntity();
        //项目改为上架状态后增加创建人的权限
        if (OperateStateEnum.ON_SHELVES.getValue().equals(operateState)) {
            projectEntity = resProjectService.getById(id);
            if (ObjectUtil.isNotNull(projectEntity)) {
                if (StrUtil.isEmpty(projectEntity.getCode())) {
                    return RestResponse.failure("请先填写项目编码再完成上架");
                }
                QueryWrapper wrapper = new QueryWrapper();
                wrapper.eq("project_id", id);
                List<ResPropertyPartitionEntity> propertyPartitionList = resPropertyPartitionService.list(wrapper);
                //获取该创建人原有的权限
                DataScopeVo vo = userFegin.getDataScopeByUserId2(projectEntity.getCreateBy());
                Boolean isChangeScop = false;
                //先判断是否已经有这个项目的权限，没有才添加
                if (!Arrays.asList(vo.getProjects()).contains(id)) {
                    String[] projects = vo.getProjects();
                    projects = ArrayUtil.insert(projects, projects.length, id);
                    vo.setProjects(projects);
                    isChangeScop = true;
                }
                String[] newZone = vo.getZones();
                //先判断是否已经有这个分区的权限，没有才添加
                for (ResPropertyPartitionEntity entity1 : propertyPartitionList) {
                    if (!Arrays.asList(vo.getZones()).contains(entity1.getId())) {
                        newZone = ArrayUtil.insert(newZone, newZone.length, entity1.getId());
                        isChangeScop = true;
                    }
                }
                vo.setZones(newZone);
                if (isChangeScop) {
                    vo.setUserId(projectEntity.getCreateBy());
                    userFegin.grantDataScope(vo);
                }
            }
        } else if (OperateStateEnum.NOT_ON_SHELVES.getValue().equals(operateState)) {
            /*QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("project_id", id);
            ResSourceEntity sourceEntity = resSourceService.getOne(queryWrapper);
            if (ObjectUtil.isNotNull(sourceEntity)) {
                return RestResponse.failure("已关联房源的项目不允许下架");
            }*/
            //项目改为未上架取消创建人的权限
           /* projectEntity = resProjectService.getById(id);
            if (ObjectUtil.isNotNull(projectEntity)) {
                QueryWrapper wrapper = new QueryWrapper();
                wrapper.eq("project_id", id);
                List<ResPropertyPartitionEntity> list = resPropertyPartitionService.list(wrapper);
                //获取该创建人原有的权限
                DataScopeVo vo = userFegin.getDataScopeByUserId2(projectEntity.getCreateBy());
                Boolean isChangeScop = false;
                //先判断是否已经有这个项目的权限，有就去掉
                if (Arrays.asList(vo.getProjects()).contains(id)) {
                    String[] projects = vo.getProjects();
                    String[] newProject = {};
                    for (String project : projects) {
                        if (!id.equals(project)) {
                            newProject = ArrayUtil.insert(newProject, newProject.length, project);
                            isChangeScop = true;
                        }
                    }
                    vo.setProjects(newProject);
                    if (list.size() > 0) {
                        String[] zones = vo.getZones();
                        String[] propertyPartitions = {};
                        String[] newZone = {};
                        //获取该项目下所有的分区数组
                        for (ResPropertyPartitionEntity propertyPartitionEntity : list) {
                            propertyPartitions = ArrayUtil.insert(propertyPartitions, propertyPartitions.length, propertyPartitionEntity.getId());
                        }
                        for (String zone : zones) {
                            if (!Arrays.asList(propertyPartitions).contains(zone)) {
                                newZone = ArrayUtil.insert(newZone, newZone.length, zone);
                                isChangeScop = true;
                            }
                        }
                        vo.setZones(newZone);
                    }
                }
                if (isChangeScop) {
                    vo.setUserId(projectEntity.getCreateBy());
                    userFegin.grantDataScope(vo);
                }
            }*/
        }
        projectEntity = new ResProjectEntity();
        projectEntity.setId(id);
        projectEntity.setExpandState(expandState);
        projectEntity.setOperateState(operateState);
        resProjectService.updateProject(projectEntity);
        return RestResponse.success("提交成功");
    }

    @ApiOperation(value = "编辑项目")
    @PostMapping("/updateProject")
    public RestResponse updateProject(ResProjectEntity entity){
        if(entity==null){
            return RestResponse.failure("请求对象为空");
        }
        if (ObjectUtil.isNotNull(entity.getCode()) && entity.getCode().length() > 4) {
            return RestResponse.failure("无法输入超过4位字符。");
        }
        if (StrUtil.isNotEmpty(entity.getCode())) {
            ResProjectEntity project = resProjectService.getByCondition(new ResProjectEntity().setCode(entity.getCode()));
            if (ObjectUtil.isNotNull(project) && !project.getId().equals(entity.getId())) {
                return RestResponse.failure("该编码和其他项目重复，请重新填写！");
            }
        }
        resProjectService.updateProject(entity);
//        try {
//            ResProjectParaEntity para=new ResProjectParaEntity();
//             para.setProjectId(entity.getId());
//             para.setParamName("门店房源是否可搜索");
//             para.setParamCode("sourceCanSearch");
//             para.setParamValue(entity.getSourceCanSearch());
//            para.insertOrUpdate();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return RestResponse.success("提交成功");
    }

    @ApiOperation(value = "删除项目")
    @PostMapping("/deleteProject")
    public RestResponse deleteProject(String id){
        ResPropertyPartitionEntity rppe = new ResPropertyPartitionEntity();
        if(StrUtil.isNotBlank(id)){
            rppe.setProjectId(id);
        }else {
            return RestResponse.failure("请选择项目");
        }
        ResSourceEntity entity = new ResSourceEntity();
        entity.setProjectId(id);
        ResSourceEntity entity1 = resSourceService.getResSourceEntity(entity);
        if(entity1!=null){
            return  RestResponse.failure("该项目已关联到房源，不能删除！");
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("project_id",id);
        //删除费用配置明细表
        List<ResCostConfigureDetailEntity>  list=resCostConfigureDetailService.queryList(id);
        for (ResCostConfigureDetailEntity detailEntity :list ) {
            QueryWrapper wrapper1 = new QueryWrapper();
            wrapper1.eq("configure_detail_id",detailEntity.getId());
            //删除费用阶梯
            resCostLadderService.remove(wrapper1);
            if(StrUtil.isNotBlank(detailEntity.getId())){
                resCostConfigureDetailService.removeById(detailEntity.getId());
            }
        }
        //删除费用配置
        resCostConfigureService.remove(wrapper);
        //删除物业设备
        //devPropertyDeviceService.remove(wrapper);
        //删除项目信息
        resProjectInfoService.remove(wrapper);
        //删除计划分区
        resPlanPartitionService.remove(wrapper);
        //删除图片
        SysFileEntity fileEntity = new SysFileEntity();
        fileEntity.setFromId(id);
        sysFileService.delFile(fileEntity);
        //删除权属分区
        resPropertyPartitionService.deletePropertyPartition(wrapper);
        //删除项目跟进日志
       // resProjectLogService.remove(wrapper);
        //删除项目参数配置
        resProjectParaService.deleteByProjectId(id);
        //删除项目
        resProjectService.deleteProject(id);
        return RestResponse.success("删除成功");
    }

    @ApiOperation(value = "项目是否存在")
    @PostMapping("/selectProjectExist")
    public RestResponse selectProjectExist(String name,String id) {
        QueryWrapper<ResProjectEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("name",name);
        wrapper.ne("id",id);
        if(resProjectService.projectExist(wrapper)){
            return RestResponse.failure("该项目名称已经存在");
        }else{
            return RestResponse.success();
        }
    }

    @ApiOperation(value = "编辑时查询项目")
    @PostMapping("/updateQueryProject")
    public RestResponse updateQueryProject(String id){
        ResProjectEntity entity=resProjectService.queryById(id);
        return RestResponse.success().setData(entity);
    }

    @ApiOperation(value = "查询项目公共方法")
    @RequestMapping("/queryProjectList")
    @UonePermissions
    public RestResponse queryProjectList(){
        List<ResProjectEntityVo> list = resProjectService.queryList();
        return RestResponse.success().setData(list);
    }

    @ApiOperation(value = "查询项目公共方法")
    @RequestMapping("/custom/queryProjectList")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse customqueryProjectList(){
        List<ResProjectEntityVo> list = resProjectService.queryList();
        return RestResponse.success().setData(list);
    }

    @ApiOperation(value = "查询租客项目列表")
    @RequestMapping("/custom/queryRenterProjectList")
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse queryRenterProjectList(){
        List<ResProjectEntityVo> list = resProjectService.queryRenterList();
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/getProjectDetail")
    @UonePermissions
    public RestResponse getProjectDetail(@RequestParam("id") String id,@RequestParam(value="id",required = false)String createBy){
        ResProjectVo vo = resProjectService.getProjectById(id);
        GPS gps = BaiduMapUtil.bd09_To_Gcj02(Double.valueOf(vo.getLatitude()),Double.valueOf(vo.getLongitude()));
        vo.setLatitude(gps.getLat()+"");
        vo.setLongitude(gps.getLon()+"");
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("project_id",vo.getProjectId());
        ResProjectInfoEntity entity2 = resProjectInfoService.getOne(wrapper);
        if (entity2 !=null) {
            vo.setPhoneNumber(entity2.getSalesPhone());
        }
        if(StrUtil.isNotBlank(createBy)){
            QueryWrapper wrapper2 = new QueryWrapper();
            wrapper2.eq("collect_id",id);
            wrapper2.eq("collect_type","1");
            wrapper2.eq("create_by",createBy);
            ResCollectEntity collectEntity = resCollectService.getOne(wrapper2);
            vo.setCollected(false);
            if (collectEntity != null && "1".equals(collectEntity.getState())) {
                vo.setCollected(true);
            }
        }
        return RestResponse.success().setData(vo);
    }

    @Override
    @GetMapping("/getById")
    public ResProjectEntity getById(@RequestParam("id") String id) {
        return resProjectService.getById(id);
    }

    @Override
    @RequestMapping("/getByCode")
    public ResProjectEntity getByCode(@RequestParam("code") String code) {
        QueryWrapper query = new QueryWrapper();
        query.eq("code",code);
        return resProjectService.getOne(query);
    }
}
