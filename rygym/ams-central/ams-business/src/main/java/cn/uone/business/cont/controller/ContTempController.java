package cn.uone.business.cont.controller;


import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.RenterType;
import cn.uone.application.enumerate.contract.*;
import cn.uone.application.enumerate.source.SourceTypeEnum;
import cn.uone.bean.constant.ContTempParamConstants;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContTempEntity;
import cn.uone.bean.entity.business.cont.ContTempParamEntity;
import cn.uone.bean.entity.business.cont.ContTempRichEntity;
import cn.uone.bean.entity.business.res.ResCostConfigureEntity;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContFrameContractService;
import cn.uone.business.cont.service.IContTempService;
import cn.uone.business.res.service.IResCostConfigureService;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.util.TempHtmlUtil;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysParaFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
@RestController
@RequestMapping("/cont/temp")
public class ContTempController extends BaseController {

    @Autowired
    private IResProjectService projectService;

    @Autowired
    private IResCostConfigureService configureService;

    @Autowired
    private IContContractService contContractService;
    @Autowired
    private IContTempService contTempService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private IBizReleaseService bizReleaseService;
    @Autowired
    private IContFrameContractService frameContractService;
    @Autowired
    private ISysParaFegin sysParaFegin;


    @GetMapping("/page")
    public RestResponse page(Page page,ContTempEntity e,@RequestParam(value = "contTypes[]",required = false) String[] contTypes){
        e.setTypes(contTypes);

        Page<ContTempEntity> p= contTempService.page(page,e);
        return RestResponse.success().setData(p);
    }


    @PostMapping("/addOrUpdate")
    @UoneLog("创建/修改合同模板")
    public RestResponse addOrUpdate(ContTempEntity e, ContTempRichEntity rich, HttpServletRequest request,String costId) throws Exception {
        //公寓平台系统,每个门店只有一个企业主合同模板，所以先判断有没有，有则不再生成。且主合同模板在子合同模板生成时自动生成
        String projectId=UoneHeaderUtil.getProjectId();
        //boolean success=contTempService.createFrameTemp(projectId); //公寓平台，生成主合同temp，在生成子合同temp时自动生成
        e.setProjectId(projectId);
        if(StringUtils.isNotBlank(rich.getPreContent())){
            String preContent= HtmlUtil.unescape(rich.getPreContent());
            preContent=this.replaceLtGt(preContent);
            preContent = preContent.replaceAll("_ueditor_page_break_tag_", TempHtmlUtil.pageLine());
            rich.setPreContent(preContent);
        }
        if(StringUtils.isNotBlank(rich.getForeword())){
            String foreword = HtmlUtil.unescape(rich.getForeword());
            foreword=this.replaceLtGt(foreword);
            foreword = foreword.replaceAll("_ueditor_page_break_tag_", TempHtmlUtil.pageLine());
            rich.setForeword(foreword);
        }
        if(StringUtils.isNotBlank(rich.getGeneral())){
            String general = HtmlUtil.unescape(rich.getGeneral());
            general=this.replaceLtGt(general);
            rich.setGeneral(general);
        }
        if(StringUtils.isNotBlank(rich.getAnnex())){
            String annex = HtmlUtil.unescape(rich.getAnnex());
            annex=this.replaceLtGt(annex);
            annex = annex.replaceAll("_ueditor_page_break_tag_", TempHtmlUtil.pageLine());
            annex = annex.replaceAll("_annex_page_break_tag_", TempHtmlUtil.pageLine());
            rich.setAnnex(annex);
        }
        if (SubjectTypeEnum.FRAME.getValue().equals(request.getParameter("subjectType"))) {
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("project_id", projectId);
            wrapper.eq("name", e.getName());
            wrapper.eq("subject_type", SubjectTypeEnum.FRAME.getValue());
            if (StrUtil.isNotBlank(e.getId())) {
                wrapper.ne("id", e.getId());
            }
            long i = contTempService.count(wrapper);
            if (i > 0) {
                return RestResponse.failure("模板名称已存在，不允许添加");
            }
        }
        if(StrUtil.isBlank(e.getId())){
            if (CustomerTypeEnum.USER.getValue().equals(request.getParameter("customerType"))) {
                //个人合同模版只允许存在一条数据
                long i = contTempService.count(new QueryWrapper<ContTempEntity>().eq("name", e.getName()).eq("project_id", projectId));
                if (i > 0) {
                    return RestResponse.failure("模板名称已存在，不允许添加");
                }
            }
            if(ContractTempletTypeEnum.CARCHECKOUT.getValue().equals(request.getParameter("type")) ||
                    ContractTempletTypeEnum.CHECKOUT.getValue().equals(request.getParameter("type"))){
                //个人合同模版只允许存在一条数据
                long i = contTempService.count(new QueryWrapper<ContTempEntity>().eq("type", e.getType()).eq("project_id",e.getProjectId()));
                if (i > 0) {
                    return RestResponse.failure("模板已存在，不允许重复添加");
                }
            }
        }

        if (null!=e.getSubsidyPrice()&&BaseConstants.BOOLEAN_OF_TRUE.equals(e.getIsTalent())) {
            if (BigDecimal.ZERO.compareTo(e.getSubsidyPrice()) > 0) {
                return RestResponse.failure("保存合同模板失败，补贴金额不能小于0");
            }
        }

        Map<String,String[]> params = request.getParameterMap();
        List<ContTempParamEntity> plist = Lists.newArrayList();
        for (String key:params.keySet()){
            if(StrUtil.isNotBlank(TempCodeEnum.getNameByValue(key))&&StrUtil.isNotBlank(params.get(key)[0])){
                String text = params.get(key)[0];
                text = replaceLtGt(text);
                ContTempParamEntity pe = new ContTempParamEntity();
                pe.setParamCode(key);
                pe.setParamValue(text);
                plist.add(pe);
            }
        }
        List<String> costIds = new ArrayList<>();
        if (StrUtil.isNotBlank(costId)) {
            costIds = Arrays.asList(costId.split(","));
        }
        if(contTempService.saveOrUpdate(e,rich,plist,costIds)){
            return RestResponse.success();
        }else{
            return RestResponse.failure("保存合同模板失败，请联系管理员");
        }
    }

    private String replaceLtGt(String text) {
        text=text.replaceAll("&lt;","&laquo;");
        text=text.replaceAll("&gt;","&raquo;");
        text=text.replaceAll("&lt;&lt;","&laquo;");
        text=text.replaceAll("&gt;&gt;","&raquo;");
        return text;
    }

    /**
     * 预览
     *
     * @param e
     * @param rich
     * @param request
     * @param costId
     * @return
     */
    @PostMapping("/preview")
    public RestResponse preview(ContTempEntity e, ContTempRichEntity rich, HttpServletRequest request, String costId) throws BusinessException {
        String projectId = e.getProjectId();
        if (StrUtil.isBlank(projectId)) {
            e.setProjectId(UoneHeaderUtil.getProjectId());
        }
        Map<String, String[]> params = request.getParameterMap();
        Map<TempCodeEnum, String> map = Maps.newHashMap();
        for (String key : params.keySet()) {
            if (null != TempCodeEnum.getEnumByValue(key) && StrUtil.isNotBlank(params.get(key)[0])) {
                map.put(TempCodeEnum.getEnumByValue(key), params.get(key)[0]);
            }
        }

        String costIdNew = null;
        if (StrUtil.isNotBlank(costId)) {
            List<String> costIds = Arrays.asList(costId.split(","));
            costIdNew = costIds.get(0);
        }
        return RestResponse.success().setData(contTempService.getTempHtml(null, e, map, rich, costIdNew));
    }

    /**
     * 框架合同预览
     *
     * @param e
     * @param rich
     * @param request
     * @return
     */
    @PostMapping("/previewFrame")
    public RestResponse previewFrame(ContTempEntity e, ContTempRichEntity rich, HttpServletRequest request) throws BusinessException {
        String projectId = e.getProjectId();
        if (StrUtil.isBlank(projectId)) {
            e.setProjectId(UoneHeaderUtil.getProjectId());
        }
        Map<String, String[]> params = request.getParameterMap();
        Map<TempCodeEnum, String> map = Maps.newHashMap();
        for (String key : params.keySet()) {
            if (null != TempCodeEnum.getEnumByValue(key) && StrUtil.isNotBlank(params.get(key)[0])) {
                map.put(TempCodeEnum.getEnumByValue(key), params.get(key)[0]);
            }
        }
        return RestResponse.success().setData(contTempService.getFrameTempHtml(null, e, map, rich));
    }

    /**
     * 小程序预览附加协议
     *
     * @param sourceId
     * @return
     */
    @UonePermissions
    @PostMapping("/previewAnnex")
    public RestResponse previewAnnex(String sourceId) {
        return RestResponse.success().setData(contTempService.getTempAnnexById(sourceId, AnnexTypeEnum.RESERVENOTICE));
    }

    /**
     * 小程序预览特别提示
     *
     * @param sourceId
     * @return
     */
    @UonePermissions
    @PostMapping("/checkInAnnex")
    public RestResponse checkInAnnex(String sourceId) {
        return RestResponse.success().setData(contTempService.getTempAnnexById(sourceId, AnnexTypeEnum.CHECKINTIPS));
    }

    /**
     * 小程序预览
     * @param tempId
     * @param costId
     * @return
     */
    @UonePermissions
    @PostMapping("/previewAttach")
    public RestResponse previewAttach(String tempId, String costId) throws BusinessException {
        return RestResponse.success().setData(contTempService.getTempById(tempId, costId));
    }

    /**
     * 小程序车位预览
     *
     * @param sourceId
     * @return
     */
    @UonePermissions
    @PostMapping("/previewAttachCar")
    public RestResponse previewAttachCar(String sourceId) throws BusinessException {
        //因为车位就默认一个合同模板，一个费用配置
        ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
        ContTempEntity tempEntity = contTempService.getTempleteBySource(sourceEntity).get(0);
        ResCostConfigureEntity costConfigureEntity = configureService.queryByTemplateId(tempEntity.getId()).get(0);
        return RestResponse.success().setData(contTempService.getTempById(tempEntity.getId(), costConfigureEntity.getId()));
    }

    /**
     * 刪除合同模板
     *
     * @param id
     */
    @UoneLog("刪除合同模板")
    @PostMapping("/delete")
    public RestResponse delete(@RequestParam("id") String id) throws Exception {
        // 1.参数判断; 2.判断合同模板是否关联到合同; 3.刪除合同模板;

        // 1.参数判断;
        if (StringUtils.isBlank(id)) {
            return RestResponse.failure("参数异常");
        }
        //根据id,查找出对应的合同模版/附加协议
        ContTempEntity temp = contTempService.getById(id);
        if(temp == null){
            return RestResponse.failure("查找不到对应的合同模板/附加协议");
        }
        String type = temp.getType();

        if (SubjectTypeEnum.TEMP.getValue().equals(temp.getSubjectType())) {
            // 2.判断合同模板是否关联到合同;
            if (contContractService.existContContractByContractTempId(id)) {
                return RestResponse.failure("存在关联的合同，无法删除");
            }
        } else if (SubjectTypeEnum.FRAME.getValue().equals(temp.getSubjectType())) {
            // 2.判断合同模板是否关联到合同;
            if (frameContractService.existFrameContractByTempId(id)) {
                return RestResponse.failure("存在关联的合同，无法删除");
            }
        }else{
            // 2.判断附加协议是否有被使用到
        }

        // 3.刪除合同模板;
        contTempService.delete(id);

        return RestResponse.success("删除成功");
    }


    @GetMapping("/getPValue")
    public RestResponse getPValue(@RequestParam(value = "id",required = false) String id){
        if(StrUtil.isNotBlank(id)){
            List<ContTempParamEntity> params =  contTempService.getParamByTempId(id);
            Map<String,Object> map = Maps.newHashMap();
            if(params!=null&&params.size()>0){
                for (ContTempParamEntity p:params){
                    if(TempCodeEnum.SHOW_ANNEX.getValue().equals(p.getParamCode())){
                        map.put(p.getParamCode(),p.getParamValue().split(","));
                    }else{
                        map.put(p.getParamCode(),p.getParamValue());
                    }
                }
            }
            return RestResponse.success().setData(map);
        }else{
            String projectId = UoneHeaderUtil.getProjectId();
            ResProjectEntity p = projectService.selectAllAddByID(projectId);

            ContTempParamConstants c = ContTempParamConstants.getInstance();
            c.setRES_NAME(p.getAddress()+c.getRES_NAME());
            c.setPB_CAR_ADD(p.getAddress()+c.getPB_CAR_ADD());
            JSONObject json = JSONUtil.parseObj(c);
            String preContent = sysParaFegin.getByCode("CONFIG_CLAUSE");
            json.put("preContent",preContent);
            return RestResponse.success().setData(json);
        }


    }

    @GetMapping("/costId")
    public RestResponse getConfigsById(String costId){
        return RestResponse.success().setData(configureService.queryByCostConfigureId(costId));
    }

    @GetMapping("/getInfo")
    public RestResponse getInfo(String tempId){
        ContTempEntity e = contTempService.getById(tempId);
        if(null==e){
            return RestResponse.failure("获取数据失败");
        }
        ContTempRichEntity r = contTempService.getRichByTempId(tempId);
        List<String> costIds=contTempService.getCostIdsByTempId(tempId);

        JSONObject json = JSONUtil.parseObj(e);
        json.put("preContent",r.getPreContent());
        json.put("foreword",r.getForeword());
        json.put("general",r.getGeneral());
        String annex = r.getAnnex();
        annex = annex.replaceAll(TempHtmlUtil.pageLine(),"_annex_page_break_tag_");
        json.put("annex",annex);
        json.put("costIds",costIds.toArray());

        return RestResponse.success().setData(json);
    }

    @RequestMapping("/getAnnex")
    public RestResponse getAnnex(){
        String projectId = UoneHeaderUtil.getProjectId();
        List<ContTempEntity> temps = contTempService.list(new QueryWrapper<ContTempEntity>().eq("project_id", projectId).eq("subject_type", "1"));
        return RestResponse.success().setData(temps);
    }

    @UoneLog("根据条件查询合同模板List")
    @RequestMapping("/queryListByParam")
    @UonePermissions(value = LoginType.ANON)
    public RestResponse queryListByParam(@RequestParam(value = "customerType", required = false) String customerType,
                                         @RequestParam(value = "type", required = false) String type,//合同模板类型
                                         @RequestParam(value = "subjectType", required = false) String subjectType,
                                         @RequestParam(value = "sourceId", required = false) String sourceId) throws Exception {
        ContTempEntity searchVo = new ContTempEntity();
        String projectid = UoneHeaderUtil.getProjectId();
        searchVo.setProjectId(projectid);
        searchVo.setCustomerType(customerType);
        searchVo.setType(type);
        searchVo.setSubjectType(subjectType);
        if (ObjectUtil.isNotNull(sourceId)) {
            ResSourceEntity source = resSourceService.getById(sourceId);
            searchVo.setIsTalent(source.getTalent() ? BaseConstants.BOOLEAN_OF_TRUE : BaseConstants.BOOLEAN_OF_FALSE);
        }
        List<ContTempEntity> list = contTempService.queryByParam(searchVo);
        return RestResponse.success().setData(list);
    }

    /*
      项目系统发起签约时获得合同模板列表
     */
    @RequestMapping("/listBySource")
    public RestResponse listBySource(String id) throws Exception {
        String sourceId=id;
        return this.queryListBySource(sourceId,null,null);
    }

    @UoneLog("根据房源查询合同模板List")
    @RequestMapping("/queryListBySource")
    @UonePermissions(value = LoginType.ANON)
    public RestResponse queryListBySource(@RequestParam(value = "sourceId") String sourceId,
                                          @RequestParam(value = "isOrg", required = false) String isOrg,
                                          @RequestParam(value = "renterId", required = false) String renterId) throws Exception {
        ContTempEntity searchVo = new ContTempEntity();
        ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
        if (ObjectUtil.isNotNull(sourceEntity)) {
            searchVo.setProjectId(sourceEntity.getProjectId());
            searchVo.setType(ContractTempletTypeEnum.getEnumBySourceTypeEnum(SourceTypeEnum.getEnumByValue(sourceEntity.getSourceType())).getValue());
        } else {
            String projectid = UoneHeaderUtil.getProjectId();
            searchVo.setProjectId(projectid);
        }
        if (StrUtil.isNotBlank(isOrg)) {
            if (BaseConstants.BOOLEAN_OF_TRUE.equals(isOrg)) {
                searchVo.setCustomerType(CustomerTypeEnum.ENTERPRISE.getValue());
            } /*else {
                searchVo.setCustomerType(CustomerTypeEnum.USER.getValue());
            }*/
        }
        if (StrUtil.isNotBlank(renterId)) {
            RenterEntity renter = renterFegin.getById(renterId);
            if (ObjectUtil.isNotNull(renter) && ObjectUtil.isNotNull(sourceEntity)) {
                boolean talent = contTempService.isTalent(sourceEntity, renter);
                if (!talent) {
                    searchVo.setIsTalent(BaseConstants.BOOLEAN_OF_FALSE);
                }
                /*if (RenterType.COMMON.getValue().equals(renter.getType())) {
                    searchVo.setCustomerType(CustomerTypeEnum.USER.getValue());
                } else*/ if (RenterType.ENTERPRISE.getValue().equals(renter.getType())) {
                    searchVo.setCustomerType(CustomerTypeEnum.ENTERPRISE.getValue());
                }
            }
        }
        searchVo.setSubjectType(SubjectTypeEnum.TEMP.getValue());
        List<ContTempEntity> list = contTempService.queryByParam(searchVo);
        return RestResponse.success().setData(list);
    }

    // 退房确认书
    @UonePermissions(LoginType.CUSTOM)
    @RequestMapping(value = "/createAgreementContract")
    @Transactional(rollbackFor = Exception.class)
    public RestResponse createAgreementContract(@RequestParam("id") String id) throws Exception {
        ContContractEntity entity = contContractService.getById(id);
        //生产pdf
        contTempService.generateCheckOutPdf(id);
        entity.setIsAgreement(BaseConstants.BOOLEAN_OF_TRUE).updateById();
        BizReleaseEntity release = bizReleaseService.getByContractId(id);
        release.setState(ReleaseStateEnum.CONFIRMED.getValue()).updateById();
        bizReleaseService.handleRelease(release);
        return RestResponse.success("终止成功");
    }

    @PostMapping("/stopView")
    public RestResponse stopView(ContTempEntity e, ContTempRichEntity rich, HttpServletRequest request) throws BusinessException {
        Map<String,String[]> params = request.getParameterMap();
        List<ContTempParamEntity> plist = Lists.newArrayList();
        for (String key:params.keySet()){
            if(StrUtil.isNotBlank(TempCodeEnum.getNameByValue(key))&&StrUtil.isNotBlank(params.get(key)[0])){
                ContTempParamEntity pe = new ContTempParamEntity();
                pe.setParamCode(key);
                pe.setParamValue(params.get(key)[0]);
                plist.add(pe);
            }
        }
       return RestResponse.success().setData(contTempService.buildHtml(e,plist,rich,true));
    }

    @UoneLog("查询合同模板")
    @RequestMapping("/getById")
    public RestResponse getById(String id) {
        ContTempEntity templet = contTempService.getById(id);
        return RestResponse.success().setData(templet);
    }


}
