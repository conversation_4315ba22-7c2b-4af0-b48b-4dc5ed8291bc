package cn.uone.business.kingdee.service;

import com.alibaba.fastjson.JSONObject;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
public interface IKingdeeApiService {
    void createReceiptBillByJob(String entryDate);
    void createReceiptBill(String id,String type) throws Exception;
    void createPaymentAndTransferBill(String orderId,String sourceId,String renterId) throws Exception;
    void createReceivableBill(String yearMonth,String projectId);
    void createAmortizeBill(String yearMonth,String projectId);
    void createInvoiceBill(Date startDate, Date endDate);
    Map getPaymentAndTransferBill(String orderId, String sourceId, String renterId, Date payTime) throws Exception;
    JSONObject AmortizePush(String id);
    JSONObject PaymentPush(String id);
    JSONObject TransferPush(String id);
    JSONObject ReceivablePush(String id);

    JSONObject AmortizeDelete(String id);
}
