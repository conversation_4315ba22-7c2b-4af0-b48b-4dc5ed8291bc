package cn.uone.business.kingdee.service;

import cn.uone.bean.entity.business.kingdee.KingdeePaymentItemEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeePaymentItemVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
public interface IKingdeePaymentItemService extends IService<KingdeePaymentItemEntity> {
    IPage<KingdeePaymentItemVo> getVoListByPaymentId(Page page, String paymentId);
    IPage<KingdeePaymentItemVo> getVoList(Page page, Map<String,Object> map);
}
