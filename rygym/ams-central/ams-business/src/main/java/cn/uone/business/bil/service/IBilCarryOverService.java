package cn.uone.business.bil.service;

import cn.uone.bean.entity.business.bil.BilCarryOverEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 账单结转表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
public interface IBilCarryOverService extends IService<BilCarryOverEntity> {
    /**
     * 结转记录保存
     * @param orderId
     * @param orderItemId
     * @param fromOrdeId
     * @param fromOrderItemId
     * @param payment
     * @param remark
     */
    BilCarryOverEntity saveCarryOver(String orderId, String orderItemId, String fromOrdeId,String fromOrderItemId, BigDecimal payment, String remark);
    /**
     * 账单结转到押金、租金
     * @param contractId 结转到账单的合同id
     * @param jzOrderId  结转账单id
     * @param jzItemType 结转明细类型
     */
    void carryOverOrder(String contractId,String jzOrderId,String jzItemType);
    /**
     * 退房退款/收款 账单结转
     * @param contractId
     * @param jzOrder
     */
    void carryOverOrder(String contractId, BilOrderEntity jzOrder);

    /**
     * 账单结转
     * @param carryOverPayment
     * @param fromOrder
     * @param toOrder
     * @param jzItemType
     * @return
     */
    BigDecimal carryOver(BigDecimal carryOverPayment,BilOrderEntity fromOrder,BilOrderEntity toOrder,String jzItemType,String remark);
}
