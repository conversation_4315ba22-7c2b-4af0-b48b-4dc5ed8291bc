package cn.uone.business.res.dao;

import cn.uone.bean.entity.business.res.ResProjectOwnerEntity;
import cn.uone.bean.entity.business.res.vo.ResProjectOwnerVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-15
 */
public interface ResProjectOwnerDao extends BaseMapper<ResProjectOwnerEntity> {
    @Select("select * from t_res_project_owner")
    List<ResProjectOwnerEntity> queryName();
    IPage<ResProjectOwnerVo> queryPage(Page page, @Param("map") Map map);

    @Select("select pp.property_owner from t_res_property_partition pp join t_res_project p on pp.project_id=p.id where p.id=#{id}")
    String getOwnerId(@Param("id") String id);
}
