package cn.uone.business.xhcosmic.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.order.PayWayEnum;
import cn.uone.bean.constant.CosmicConstant;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.cosmic.vo.CosmicTransactionVo;
import cn.uone.bean.entity.business.res.ResProjectEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicIncomeEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicIncomeItemEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicTransactionEntity;
import cn.uone.bean.entity.tpi.cosmic.BaseQueryPageVo;
import cn.uone.bean.entity.tpi.cosmic.transaction.TransactionCreditedQueryDto;
import cn.uone.bean.entity.tpi.cosmic.transaction.TransactionCreditedQueryVo;
import cn.uone.business.bil.dao.BilOrderDao;
import cn.uone.business.bil.dao.BilOrderItemDao;
import cn.uone.business.res.dao.ResProjectDao;
import cn.uone.business.res.dao.ResSourceDao;
import cn.uone.business.xhcosmic.dao.CosmicIncomeDao;
import cn.uone.business.xhcosmic.dao.CosmicTransactionDao;
import cn.uone.business.xhcosmic.service.ICosmicCollectionService;
import cn.uone.business.xhcosmic.service.ICosmicIncomeService;
import cn.uone.business.xhcosmic.service.ICosmicTransactionService;
import cn.uone.fegin.tpi.cosmic.ITransactionFeign;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 金蝶(星瀚)交易明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Slf4j
@Service
public class CosmicTransactionServiceImpl extends ServiceImpl<CosmicTransactionDao, CosmicTransactionEntity> implements ICosmicTransactionService {

    @Autowired
    private ITransactionFeign transactionFeign;
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    @Autowired
    private ICosmicIncomeService cosmicIncomeService;

    @Autowired
    private CosmicTransactionDao cosmicTransactionDao;

    @Autowired
    private CosmicIncomeDao cosmicIncomeDao;

    @Autowired
    private ICosmicCollectionService cosmicCollectionService;

    @Autowired
    private BilOrderItemDao bilOrderItemDao;

    @Autowired
    private BilOrderDao bilOrderDao;

    @Autowired
    private ResSourceDao resSourceDao;
    
    @Autowired
    private ResProjectDao resProjectDao;

    @Override
    @Transactional
    public List<CosmicTransactionEntity> insertBatch(List<CosmicTransactionEntity> entities) {
        if (CollectionUtil.isEmpty(entities)) {
            return Collections.emptyList();
        }
        List<String> bankIds = entities.stream().map(CosmicTransactionEntity::getBankId)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(bankIds)) {
            List<CosmicTransactionEntity> duplicateList = baseMapper.selectList(
                    new LambdaQueryWrapper<CosmicTransactionEntity>()
                            .in(CosmicTransactionEntity::getBankId, bankIds)
            );
            Map<String, CosmicTransactionEntity> bankIdMap = duplicateList.stream().collect(Collectors.toMap(CosmicTransactionEntity::getBankId, Function.identity(), (a, b) -> b));
            entities.forEach(entity -> entity.setId(bankIdMap.getOrDefault(entity.getBankId(), new CosmicTransactionEntity()).getId()));
        }
        saveOrUpdateBatch(entities);
        return entities;
    }

    @Override
    @Transactional
    public List<CosmicTransactionEntity> cosmicQuery(LocalDate bizDate, List<String> companyNumber, String bankAccountNumber,
                                    String acctPropertyNumber, BigDecimal creditAmount, BigDecimal noClaimAmount,
                                    LocalDateTime modifyTime, Boolean isFullQuery, Integer pageNo, Integer pageSize) throws Exception {
        TransactionCreditedQueryDto dto = new TransactionCreditedQueryDto()
                .setBizDate(bizDate == null ? null : DateUtil.date(bizDate).toJdkDate())
                .setIsFullQuery(isFullQuery)
                .setCompanyNumber(companyNumber)
                .setCreditAmount(creditAmount)
                .setNoClaimAmount(noClaimAmount)
                .setModifyTime(modifyTime == null ? null : DateTimeUtil.localDateTimeToDate(modifyTime))
                .setPageNo(pageNo)
                .setPageSize(pageSize)
                .setBankAccountNumber(bankAccountNumber)
                .setAcctPropertyNumber(acctPropertyNumber);
        return cosmicQuery(dto);
    }

    /**
     * 从金蝶查询交易明细
     * 根据业务需求，只有已认领的交易明细才会入库
     *
     * @param dto 查询参数
     * @return 交易明细列表
     * @throws Exception 查询异常
     */
    @Override
    @Transactional
    public List<CosmicTransactionEntity> cosmicQuery(TransactionCreditedQueryDto dto) throws Exception {
        if (CollUtil.isEmpty(dto.getCompanyNumber())) {
            throw new BusinessException("资金组织.编码组织guid或id不能为空");
        }
        if (dto.getPageSize() == null) {
            throw new BusinessException("分页数量不能为空");
        }
        RestResponse response = transactionFeign.transactionDetailCreditedQuery(dto);
        if (BooleanUtil.isTrue(response.getSuccess())) {
            BaseQueryPageVo<TransactionCreditedQueryVo> pageVo = JSONObject.from(response.get("data")).to(
                    new TypeReference<BaseQueryPageVo<TransactionCreditedQueryVo>>() {
                    }
            );
            List<CosmicTransactionEntity> entities = pageVo.getRows().stream().map(this::cosmicVoToEntity)
                    .collect(Collectors.toList());
            // 不再直接入库，而是返回查询结果，让调用方决定是否入库
            return entities;
        }
        log.error("走到这说明报错了哦:{}", response.getMessage());
        return Collections.emptyList();
    }

    /**
     * 自动认领交易明细
     * 只处理未认领的交易明细，并且只有成功认领的交易明细才会入库
     *
     * @param entities 交易明细列表
     */
    @Override
    @Transactional
    public void autoClaimTransactionDetail(List<CosmicTransactionEntity> entities) {
        if (CollectionUtil.isEmpty(entities)) {
            return;
        }
        
        // 为了保持代码的健壮性，仍然过滤一下未认领的交易明细
        // 因为entities可能来自本地数据库查询，而不是金蝶接口
        List<CosmicTransactionEntity> unclaimedTransactions = entities.stream()
            .filter(entity -> {
                Integer claim = entity.getClaim();
                return claim == null || claim == 0;
            })
                .collect(Collectors.toList());
        
        if (CollectionUtil.isEmpty(unclaimedTransactions)) {
            log.info("没有未认领的交易明细，无需处理");
            return;
        }
        
        // 先查询AMBH和AMYH两个项目的ID
        List<ResProjectEntity> targetProjects = resProjectDao.selectList(
                new LambdaQueryWrapper<ResProjectEntity>()
                        .in(ResProjectEntity::getCode, ListUtil.toList("AMBH", "AMYN"))
        );
        
        if (CollectionUtil.isEmpty(targetProjects)) {
            log.error("未找到AMBH和AMYN项目，无法进行自动认领");
            return;
        }
        
        // 获取项目ID列表
        List<String> targetProjectIds = targetProjects.stream()
                .map(ResProjectEntity::getId)
                .collect(Collectors.toList());
        
        log.info("找到目标项目：{}", targetProjects.stream().map(ResProjectEntity::getCode).collect(Collectors.joining(",")));
        
        // 分离公积金交易明细和普通交易明细
        List<CosmicTransactionEntity> housingFundTransactions = unclaimedTransactions.stream()
            .filter(entity -> StrUtil.contains(entity.getOppUnit(), "厦门市住房公积金中心"))
            .collect(Collectors.toList());
        
        List<CosmicTransactionEntity> normalTransactions = unclaimedTransactions.stream()
            .filter(entity -> !StrUtil.contains(entity.getOppUnit(), "厦门市住房公积金中心"))
            .collect(Collectors.toList());
        
        // 处理公积金交易明细
        if (CollectionUtil.isNotEmpty(housingFundTransactions)) {
            processHousingFundTransactions(housingFundTransactions, targetProjectIds);
        }
        
        // 处理普通交易明细
        if (CollectionUtil.isNotEmpty(normalTransactions)) {
            processNormalTransactions(normalTransactions, targetProjectIds);
        }
    }
    
    /**
     * 处理公积金交易明细
     * 
     * @param housingFundTransactions 公积金交易明细列表
     * @param targetProjectIds 目标项目ID列表
     */
    private void processHousingFundTransactions(List<CosmicTransactionEntity> housingFundTransactions, List<String> targetProjectIds) {
        log.info("开始处理{}条公积金交易明细", housingFundTransactions.size());
        
        for (CosmicTransactionEntity transaction : housingFundTransactions) {
            try {
                // 查询与交易金额匹配的公积金订单项
                BigDecimal transactionAmount = transaction.getCreditAmount();
                
                // 查询符合条件的公积金订单项
                List<Map<String, Object>> housingFundOrderItems = cosmicTransactionDao.selectHousingFundOrdersWithProjectInfo(
                    targetProjectIds,
                    transaction.getBizDate(),
                    "448" // 公积金类型
                );


                
                if (CollectionUtil.isEmpty(housingFundOrderItems)) {
                    log.info("未找到匹配的公积金订单项，交易明细：{}，金额：{}", 
                        transaction.getBankId(), transaction.getCreditAmount());
                    continue;
                }
                
                // 按项目ID分组
                Map<String, List<Map<String, Object>>> itemsByProject = housingFundOrderItems.stream()
                    .collect(Collectors.groupingBy(item -> (String) item.get("projectId")));
                
                // 遍历每个项目的公积金订单项
                for (Map.Entry<String, List<Map<String, Object>>> entry : itemsByProject.entrySet()) {
                    List<Map<String, Object>> projectItems = entry.getValue();
                    
                    // 计算项目公积金总额
                    BigDecimal totalHousingFundAmount = projectItems.stream()
                        .map(item -> (BigDecimal) item.get("payment"))
                        .map(BigDecimal::abs) // 转为正数
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    
                    // 如果金额匹配
                    if (Math.abs(totalHousingFundAmount.subtract(transactionAmount).doubleValue()) < 0.01) {
                        // 获取订单ID列表
                        List<String> orderIds = projectItems.stream()
                            .map(item -> (String) item.get("orderId"))
                            .distinct()
                            .collect(Collectors.toList());
                        
                        // 查询对应的应收单
                        List<CosmicIncomeEntity> incomeEntities = cosmicIncomeDao.selectList(
                            new LambdaQueryWrapper<CosmicIncomeEntity>()
                                .in(CosmicIncomeEntity::getSourceBillNumber, orderIds)
                        );

                        
                        if (CollectionUtil.isEmpty(incomeEntities)) {
                            log.warn("未找到订单{}对应的应收单", orderIds);
                            continue;
                        }
                        
                        // 执行认领逻辑
                        List<String> incomeIds = incomeEntities.stream()
                            .map(CosmicIncomeEntity::getId)
                            .collect(Collectors.toList());
                        
                        try {
                            claimTransaction(transaction.getBankId(), incomeIds, transaction);
                            log.info("公积金交易明细{}自动认领应收单{}成功", transaction.getBankId(), incomeIds);
                            
                            // 自动推送收款单到金蝶
                            try {
                                // 获取刚创建的收款单ID
                                List<String> collectionIds = baseMapper.findCollectionIdsByTransactionId(transaction.getId());
                                if (CollectionUtil.isNotEmpty(collectionIds)) {
                                    // 自动推送收款单到金蝶
                                    log.info("开始自动推送公积金收款单，收款单ID:{}", collectionIds);
                                    RestResponse pushResponse = cosmicCollectionService.push(collectionIds);
                                    if (BooleanUtil.isTrue(pushResponse.getSuccess())) {
                                        log.info("公积金收款单自动推送成功，收款单ID:{}", collectionIds);
                                    } else {
                                        log.warn("公积金收款单自动推送失败，将通过定时任务重试，收款单ID:{}，错误信息:{}", 
                                            collectionIds, pushResponse.getMessage());
                                    }
                                }
                            } catch (Exception e) {
                                // 捕获异常但不影响主流程，记录日志后继续
                                log.error("公积金收款单自动推送异常，将通过定时任务重试，交易明细ID:{}", transaction.getId(), e);
                            }
                            
                            // 认领成功后跳出循环
                            break;
                        } catch (BusinessException e) {
                            log.error("公积金交易明细{}自动认领应收单{}失败: {}", transaction.getBankId(), incomeIds, e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("处理公积金交易明细{}时出错", transaction.getBankId(), e);
            }
        }
    }
    
    /**
     * 处理普通交易明细
     * 
     * @param normalTransactions 普通交易明细列表
     * @param targetProjectIds 目标项目ID列表
     */
    private void processNormalTransactions(List<CosmicTransactionEntity> normalTransactions, List<String> targetProjectIds) {
        // 2. 按交易日期(bizDate)分组处理
        Map<LocalDate, List<CosmicTransactionEntity>> transactionsByDate = normalTransactions.stream()
                .collect(Collectors.groupingBy(CosmicTransactionEntity::getBizDate));
        
        for (Map.Entry<LocalDate, List<CosmicTransactionEntity>> entry : transactionsByDate.entrySet()) {
            LocalDate bizDate = entry.getKey();
            List<CosmicTransactionEntity> dailyTransactions = entry.getValue();
            
            // 业务日期范围包括当天和前一天(银行T+1到账)
            LocalDate previousDate = bizDate.minusDays(1);
            
            try {
                // 分别处理当天和前一天的订单
                for (LocalDate processDate : new LocalDate[]{bizDate, previousDate}) {
                    log.info("处理{}的订单数据", processDate);

                    // 使用优化的联表查询，一次性获取已支付订单及其项目信息
                    List<Map<String, Object>> orderProjectInfoList = cosmicTransactionDao.selectPaidOrdersWithProjectInfo(
                            targetProjectIds,
                            processDate,
                            PayStateEnum.PAYCONFIR.getValue(),
                            ListUtil.toList(PayWayEnum.WECHAT.getValue(), PayWayEnum.WECHAT2.getValue())
                    );

                    if (CollectionUtil.isEmpty(orderProjectInfoList)) {
                        log.info("没有找到对应日期{}的已支付订单，跳过处理", processDate);
                        continue;
                    }

                    // 按项目ID分组订单，计算每个项目的总支付金额
                    Map<String, List<Map<String, Object>>> ordersByProject = orderProjectInfoList.stream()
                            .collect(Collectors.groupingBy(order -> (String) order.get("projectId")));

                    log.info("找到{}个项目的订单数据，共{}条订单", ordersByProject.size(), orderProjectInfoList.size());

                    // 5. 计算每个项目的总支付金额并与交易明细待认领金额比对
                    for (Map.Entry<String, List<Map<String, Object>>> projectEntry : ordersByProject.entrySet()) {
                        String projectId = projectEntry.getKey();
                        List<Map<String, Object>> projectOrderInfos = projectEntry.getValue();

                        // 计算项目总支付金额
                        BigDecimal totalProjectPayment = projectOrderInfos.stream()
                                .map(orderInfo -> (BigDecimal) orderInfo.get("actualPayment"))
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        log.info("项目{}在日期{}的总支付金额为:{}", projectId, processDate, totalProjectPayment);

                        // 查找匹配的交易明细(金额相等)
                        List<CosmicTransactionEntity> matchedTransactions = dailyTransactions.stream()
                                .filter(transaction -> {
                                    BigDecimal creditAmount = transaction.getCreditAmount();

                                    // 比较金额是否匹配(允许0.01的误差)
                                    return Math.abs(creditAmount.subtract(totalProjectPayment).doubleValue()) < 0.01;
                                })
                                .collect(Collectors.toList());

                        if (CollectionUtil.isEmpty(matchedTransactions)) {
                            log.info("项目{}在日期{}没有找到匹配的交易明细，总金额:{}", projectId, processDate, totalProjectPayment);
                            continue;
                        }

                        // 对每个匹配的交易明细进行认领
                        for (CosmicTransactionEntity matchedTransaction : matchedTransactions) {
                            // 获取项目对应订单的应收单IDs
                            List<String> sourceBillNumbers = projectOrderInfos.stream()
                                    .map(orderInfo -> (String) orderInfo.get("orderId"))
                                    .collect(Collectors.toList());

                            if (CollectionUtil.isEmpty(sourceBillNumbers)) {
                                continue;
                            }
                            
                            // 查询对应的应收单
                            List<CosmicIncomeEntity> incomeEntities = cosmicIncomeDao.selectList(
                                    new LambdaQueryWrapper<CosmicIncomeEntity>()
                                            .in(CosmicIncomeEntity::getSourceBillNumber, sourceBillNumbers)
                            );
                            
                            if (CollectionUtil.isEmpty(incomeEntities)) {
                                log.warn("未找到订单{}对应的应收单", sourceBillNumbers);
                                continue;
                            }
                            
                            // 执行认领逻辑，生成收款处理单
                            List<String> incomeIds = incomeEntities.stream()
                                    .map(CosmicIncomeEntity::getId)
                                    .collect(Collectors.toList());
                            
                            try {
                                // 注意：claimTransaction方法内部会处理交易明细的入库操作
                                // 只有在认领成功后才会入库
                                claimTransaction(matchedTransaction.getBankId(), incomeIds,matchedTransaction);
                                log.info("交易明细{}自动认领应收单{}成功", matchedTransaction.getBankId(), incomeIds);
                                
                                // 自动推送收款单到金蝶
                                try {
                                    // 获取刚创建的收款单ID
                                    List<String> collectionIds = baseMapper.findCollectionIdsByTransactionId(matchedTransaction.getId());
                                    if (CollectionUtil.isNotEmpty(collectionIds)) {
                                        // 自动推送收款单到金蝶
                                        log.info("开始自动推送收款单，收款单ID:{}", collectionIds);
                                        RestResponse pushResponse = cosmicCollectionService.push(collectionIds);
                                        if (BooleanUtil.isTrue(pushResponse.getSuccess())) {
                                            log.info("收款单自动推送成功，收款单ID:{}", collectionIds);
                                        } else {
                                            log.warn("收款单自动推送失败，将通过定时任务重试，收款单ID:{}，错误信息:{}", 
                                                collectionIds, pushResponse.getMessage());
                                        }
                                    }
                                } catch (Exception e) {
                                    // 捕获异常但不影响主流程，记录日志后继续
                                    log.error("收款单自动推送异常，将通过定时任务重试，交易明细ID:{}", matchedTransaction.getId(), e);
                                }
                                
                                // 已经认领成功的交易明细需要从dailyTransactions中移除，避免重复认领
                                dailyTransactions.remove(matchedTransaction);
                            } catch (BusinessException e) {
                                log.error("交易明细{}自动认领应收单{}失败: {}", matchedTransaction.getBankId(), incomeIds, e.getMessage());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("处理{}日期的交易明细自动认领时出错", bizDate, e);
            }
        }
    }

    @Override
    @Transactional
    public void autoClaimTransactionDetailByBankId(List<String> bankIds) {
        if (CollectionUtil.isEmpty(bankIds)) {
            return;
        }
        List<CosmicTransactionEntity> list = list(
                new LambdaQueryWrapper<CosmicTransactionEntity>()
                        .in(CosmicTransactionEntity::getBankId, bankIds)
                        .eq(CosmicTransactionEntity::getClaim, 0)
        );
        autoClaimTransactionDetail(list);
    }

    @Override
    public IPage<CosmicTransactionEntity> queryPage(Page<CosmicTransactionEntity> page, CosmicTransactionVo cosmicTransactionVo) throws BusinessException {
        // 1. 从金蝶系统查询全量数据
        TransactionCreditedQueryDto queryDto = TransactionCreditedQueryDto.builder()
                // .bankAccountNumber(cosmicTransactionVo.getBankAccountNumber())
                .companyNumber(ListUtil.toList(CosmicConstant.ORG_NUMBER))
                .isFullQuery(true)
                .pageSize(1000)
                .build();

        List<CosmicTransactionEntity> kingdeeTransactions = new ArrayList<>();
        RestResponse response = callKingdeeWithRetry(queryDto);
        if (BooleanUtil.isTrue(response.getSuccess())) {
            BaseQueryPageVo<TransactionCreditedQueryVo> pageVo = JSONObject.from(response.get("data")).to(
                    new TypeReference<BaseQueryPageVo<TransactionCreditedQueryVo>>() {
                    }
            );
            kingdeeTransactions = pageVo.getRows().stream()
                    .map(this::cosmicVoToEntity)
                    .collect(Collectors.toList());
        }

        // 2. 查询本地数据库中的交易记录
        IPage<CosmicTransactionEntity> localTransactions = baseMapper.selectPage(
                page.setDesc("create_date"),
                new LambdaQueryWrapper<CosmicTransactionEntity>()
                        .eq(cosmicTransactionVo.getClaim() != null,
                                CosmicTransactionEntity::getClaim,
                                cosmicTransactionVo.getClaim())
                        .like(StrUtil.isNotBlank(cosmicTransactionVo.getBillNo()),
                                CosmicTransactionEntity::getBillNo,
                                cosmicTransactionVo.getBillNo())
                        .eq(StrUtil.isNotBlank(cosmicTransactionVo.getReceredType()),
                                CosmicTransactionEntity::getReceredType,
                                cosmicTransactionVo.getReceredType())
        );

        // 3. 合并金蝶系统和本地系统的数据，金蝶数据优先
        List<CosmicTransactionEntity> mergedList = new ArrayList<>();
        
        // 添加金蝶中存在但本地不存在的记录
        Map<String, CosmicTransactionEntity> localTransactionMap = localTransactions.getRecords().stream()
                .collect(Collectors.toMap(CosmicTransactionEntity::getBankId, Function.identity(), (a, b) -> a));
                
        // 先添加金蝶数据 根据应收单金额过滤
        kingdeeTransactions.stream()
                .filter(kt -> !localTransactionMap.containsKey(kt.getBankId()))
            // .filter(kt -> priceList.contains(kt.getCreditAmount().toString()))
                .filter(kt -> cosmicTransactionVo.getClaim() == null || 
                        kt.getClaim().equals(cosmicTransactionVo.getClaim()))
                .filter(kt -> StrUtil.isBlank(cosmicTransactionVo.getBillNo()) || 
                        StrUtil.contains(kt.getBillNo(), cosmicTransactionVo.getBillNo()))
            .filter(kt -> StrUtil.isBlank(cosmicTransactionVo.getReceredType())
                ||
                        kt.getReceredType().equals(cosmicTransactionVo.getReceredType()))
                .forEach(mergedList::add);

        // 将金蝶数据缓存到redis然后认领是时候会使用到它
        log.info("将金蝶数据缓存到redis", JSONObject.toJSONString(mergedList));
        stringRedisTemplate.opsForValue().set("busCosmicTransactions", JSONObject.toJSONString(mergedList), 1,
                TimeUnit.DAYS);
        // 再添加本地数据
        mergedList.addAll(localTransactions.getRecords());
        
        // 4. 手动实现分页
        int start = (int) ((page.getCurrent() - 1) * page.getSize());
        int end = (int) Math.min(start + page.getSize(), mergedList.size());
        
        if (start < mergedList.size()) {
            localTransactions.setRecords(mergedList.subList(start, end));
        } else {
            localTransactions.setRecords(new ArrayList<>());
        }
        localTransactions.setTotal((long) mergedList.size());

        return localTransactions;
    }

    /**
     * 前端页面手动认领
     *
     * @param bankId        手动认领
     * @param incomeIds 应付单ids
     */
    @Override
    @Transactional
    public void claimTransaction(String bankId, Collection<String> incomeIds, CosmicTransactionEntity matchedTransaction) throws BusinessException {
        // 获取应收单信息
        Collection<CosmicIncomeEntity> incomeEntities = cosmicIncomeDao.selectItemById(new ArrayList<>(incomeIds));
        if (CollectionUtil.isEmpty(incomeEntities)) {
            throw new BusinessException("未找到对应的应收单信息");
        }
        CosmicTransactionEntity cosmicTransaction = matchedTransaction;
        if (ObjectUtil.isNull(matchedTransaction)) {

        // 从redis获取交易明细信息
        String redisKey = "busCosmicTransactions";
        String redisValue = stringRedisTemplate.opsForValue().get(redisKey);
        if (StrUtil.isBlank(redisValue)) {
            throw new BusinessException("未找到对应的交易明细信息");
        }
        List<CosmicTransactionEntity> redisTransactions = JSON.parseArray(redisValue, CosmicTransactionEntity.class);
        cosmicTransaction = redisTransactions.stream()
                                                                 .filter(t -> t.getBankId()
                                                                               .equals(bankId))
                                                                 .findFirst()
                                                                 .orElseThrow(() -> new BusinessException("未找到对应的交易明细信息"));
        }

        // 校验参数
        if (!verify(cosmicTransaction, incomeEntities)) {
            //如果校验不通过
            throw new BusinessException("当前交易明细与应收单不匹配");
        }

        // 先调用金蝶接口进行认领
        log.info("交易明细开始认领，交易明细ID:{}", cosmicTransaction.getBankId());
        RestResponse response = transactionFeign.transactionDetailRewrite(cosmicTransaction.getBankId());
        if (BooleanUtil.isFalse(response.getSuccess())) {
            throw new BusinessException("交易明细保存推送金蝶失败");
        }
        
        // 金蝶认领成功后，再保存到本地数据库
        cosmicTransaction.setClaim(1);
        saveOrUpdate(cosmicTransaction);

        // 更新应收单与交易明细的关联关系 - 使用中间表存储多对多关系
        cosmicIncomeDao.saveIncomeTransactionRel(cosmicTransaction.getId(), incomeIds);

        // 根据已经认领的交易明细和应收单创建新的收款处理单
        cosmicCollectionService.insertFromClaimTransaction(cosmicTransaction, incomeEntities);
        log.info("交易明细认领成功，交易明细ID:{}", cosmicTransaction.getId());
    }

    /**
     * 校验是否可以认领
     * 同一个项目 同一个日期 金额相等
     *
     * @param transaction
     * @param incomeEntities
     * @return
     */
    private boolean verify(CosmicTransactionEntity transaction, Collection<CosmicIncomeEntity> incomeEntities) {

        //允许交易时间差一天 跟金蝶开发对过
        // if (incomeEntities.stream().anyMatch(incomeEntity -> !incomeEntity.getBizDate().toLocalDate().equals(transaction.getBizDate()) && !incomeEntity.getBizDate().toLocalDate().equals(transaction.getBizDate().plusDays(-1)))) {
        //     return false;
        // }

        // 判断项目是否相同
        Set<String> projectNumber = new HashSet<>();
        incomeEntities.forEach(incomeEntity -> {
            incomeEntity.getItems().forEach(item -> {
                projectNumber.add(item.getProjectNumber());
            });
        });
        if (projectNumber.size() > 1) {
            return false;
        }

        // 提取sourceBillNumber集合（订单ID）
        List<String> sourceBillNumbers =
            incomeEntities.stream().map(CosmicIncomeEntity::getSourceBillNumber).collect(Collectors.toList());
        
        if (CollectionUtil.isEmpty(sourceBillNumbers)) {
            return false;
        }
        
        // 公积金支付特殊处理 - 当交易为"厦门市住房公积金中心"时
        if (StrUtil.contains(transaction.getOppUnit(), "厦门市住房公积金中心")) {
            log.info("检测到公积金支付交易，交易对手：{}，交易金额：{}", transaction.getOppUnit(), transaction.getCreditAmount());
            
            // 直接查询订单项中公积金类型(448)的金额
            LambdaQueryWrapper<BilOrderItemEntity> housingFundQueryWrapper = new LambdaQueryWrapper<>();
            housingFundQueryWrapper.in(BilOrderItemEntity::getOrderId, sourceBillNumbers)
                .eq(BilOrderItemEntity::getOrderItemType, "448"); // 公积金抵扣
            
            List<BilOrderItemEntity> housingFundItems = bilOrderItemDao.selectList(housingFundQueryWrapper);
            
            if (CollectionUtil.isNotEmpty(housingFundItems)) {
                // 计算公积金总额，数据库为负的，得转正数
                BigDecimal housingFundAmount = housingFundItems.stream()
                    .map(BilOrderItemEntity::getPayment) 
                    .map(BigDecimal::abs) // 转为正数
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                log.info("订单公积金金额：{}，交易金额：{}", housingFundAmount, transaction.getCreditAmount());
                
                // 如果公积金总额等于交易金额，直接返回true
                if (housingFundAmount.compareTo(transaction.getCreditAmount()) == 0) {

                    return true;
                }
            }
            
            // 公积金金额不匹配，返回false
            return false;
        }
        
        // 从t_bil_order表获取actual_payment支付金额
        List<BilOrderEntity> orderEntities = bilOrderDao.selectList(
            new LambdaQueryWrapper<BilOrderEntity>()
                .in(BilOrderEntity::getId, sourceBillNumbers)
        );
        
        if (CollectionUtil.isEmpty(orderEntities)) {
            return false;
        }
        
        // 计算本次要认领的订单总支付金额
        BigDecimal totalActualPayment = orderEntities.stream()
            .map(BilOrderEntity::getActualPayment)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 获取incomeEntities的id集合
        List<String> incomeIds = incomeEntities.stream().map(CosmicIncomeEntity::getId).collect(Collectors.toList());

        // 计算交易明细已经认领的金额
        BigDecimal alreadyClaimedAmount = BigDecimal.ZERO;
        List<String> existingIncomeIds = cosmicIncomeDao.findTransactionIdsByIncomeId(incomeIds.get(0));
        if (CollectionUtil.isNotEmpty(existingIncomeIds)) {

            // 根据existingIncomeIds查询已存在的交易明细ID集合
            LambdaQueryWrapper<CosmicTransactionEntity> queryWrapper = new LambdaQueryWrapper<CosmicTransactionEntity>().in(CosmicTransactionEntity::getId, existingIncomeIds);
            List<CosmicTransactionEntity> existingTransactions = cosmicTransactionDao.selectList(queryWrapper);

            //计算已经认领的金额
            if (CollectionUtil.isNotEmpty(existingTransactions)) {
                alreadyClaimedAmount = existingTransactions.stream()
                      .map(CosmicTransactionEntity::getCreditAmount)
                      .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        }

        // 剩余待认领金额
        BigDecimal remainingAmount = totalActualPayment.subtract(alreadyClaimedAmount);

        // 如果本次认领后的总金额大于交易明细金额，则不允许认领
        if (remainingAmount.compareTo(transaction.getCreditAmount()) < 0) {
            return false;
        }

        // 默认情况：判断总金额是否相等交易明细金额
        if (totalActualPayment.compareTo(transaction.getCreditAmount()) == 0) {
            return true;
        }
        
        // 查询该订单下是否有公积金支付项
        LambdaQueryWrapper<BilOrderItemEntity> itemQueryWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapper.in(BilOrderItemEntity::getOrderId, sourceBillNumbers).eq(BilOrderItemEntity::getOrderItemType,
            "448"); // 公积金抵扣
        List<BilOrderItemEntity> housingFundItems = bilOrderItemDao.selectList(itemQueryWrapper);

        // 检查是否存在公积金支付项
        if (housingFundItems.size() > 0) {

            // 计算公积金总额，数据库为负的，得转正数
            BigDecimal housingFundAmount = housingFundItems.stream()
                .map(BilOrderItemEntity::getPayment)
                .map(BigDecimal::abs) // 转为正数
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 如果公积金总额等于交易金额，直接返回true
            if (housingFundAmount.compareTo(transaction.getCreditAmount()) == 0) {
                return true;
            }
            // 计算需要补交的金额amount - housingFundAmount
            BigDecimal needPayAmount = totalActualPayment.subtract(housingFundAmount);
            if (needPayAmount.compareTo(transaction.getCreditAmount()) == 0) {
                return true;
            }

        }
        return false;
    }

    private CosmicTransactionEntity cosmicVoToEntity(TransactionCreditedQueryVo vo) {
        CosmicTransactionEntity cosmicTransactionEntity = new CosmicTransactionEntity();
        BeanUtils.copyProperties(vo, cosmicTransactionEntity);
        cosmicTransactionEntity.setBizDate(LocalDate.parse(DateUtil.formatDate(vo.getBizDate()), DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .setBizTime(LocalDateTime.parse(DateUtil.formatDateTime(vo.getBizTime()), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            .setModifyTime(LocalDateTime.parse(DateUtil.formatDateTime(vo.getModifyTime()),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            .setClaim(0); // 设置为未认领状态
        return cosmicTransactionEntity;
    }

    /**
     * 调用金蝶接口的重试机制
     * @param dto 查询参数
     * @return 查询结果
     */
    private RestResponse callKingdeeWithRetry(TransactionCreditedQueryDto dto) throws BusinessException {
        String cacheKey = "kingdee:transaction:" + JSONObject.toJSONString(dto);
        // 先尝试从缓存获取
        String cachedData = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotBlank(cachedData)) {
            return JSONObject.parseObject(cachedData, RestResponse.class);
        }
        
        // 重试次数和间隔
        int maxRetries = 3;
        long retryIntervalMs = 1000;
        
        RestResponse response = null;
        Exception lastException = null;
        
        for (int i = 0; i < maxRetries; i++) {
            try {
                response = transactionFeign.transactionDetailCreditedQuery(dto);
                if (BooleanUtil.isTrue(response.getSuccess())) {
                    // 请求成功，缓存结果（设置5分钟过期）
                    stringRedisTemplate.opsForValue().set(cacheKey, JSONObject.toJSONString(response), 3,
                        TimeUnit.MINUTES);
                    return response;
                }
                log.warn("金蝶接口调用失败，第{}次重试，错误信息：{}", i + 1, response.getMessage());
            } catch (Exception e) {
                lastException = e;
                log.error("金蝶接口调用异常，第{}次重试", i + 1, e);
            }
            
            if (i < maxRetries - 1) {
                try {
                    Thread.sleep(retryIntervalMs * (i + 1));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        // 所有重试都失败了
        if (response != null) {
            return response;
        } else {
            throw new BusinessException("调用金蝶接口失败，请稍后重试{}"+lastException);
        }
    }

    /**
     * 判断交易明细是否已经认领完成（认领的应收单金额总和等于交易明细金额）
     * @param transactionId 交易明细ID
     * @return 是否认领完成
     */
    @Override
    public boolean isTransactionFullyClaimed(String transactionId) {
        // 获取交易明细信息
        CosmicTransactionEntity transaction = getById(transactionId);
        if (transaction == null) {
            return false;
        }
        
        // 获取剩余可认领金额
        BigDecimal remainingAmount = getRemainingClaimableAmount(transactionId);
        
        // 如果剩余可认领金额为0，则认为已认领完成
        return remainingAmount.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 获取交易明细的剩余可认领金额
     * @param transactionId 交易明细ID
     * @return 剩余可认领金额
     */
    @Override
    public BigDecimal getRemainingClaimableAmount(String transactionId) {
        // 获取交易明细信息
        CosmicTransactionEntity transaction = getById(transactionId);
        if (transaction == null) {
            return BigDecimal.ZERO;
        }
        
        // 获取该交易明细关联的所有应收单
        List<String> incomeIds = cosmicIncomeDao.findIncomeIdsByTransactionId(transactionId);
        if (CollectionUtil.isEmpty(incomeIds)) {
            return transaction.getCreditAmount();
        }

        // 获取所有关联应收单的明细
        List<CosmicIncomeEntity> incomeEntities = cosmicIncomeDao.selectItemById(incomeIds);
        if (CollectionUtil.isEmpty(incomeEntities)) {
            return transaction.getCreditAmount();
        }
        
        // 计算所有应收单明细的总金额
        BigDecimal totalClaimedAmount = BigDecimal.ZERO;
        for (CosmicIncomeEntity income : incomeEntities) {
            for (CosmicIncomeItemEntity item : income.getItems()) {
                totalClaimedAmount = totalClaimedAmount.add(item.getTaxUnitPrice());
            }
        }
        
        // 计算剩余可认领金额
        return transaction.getCreditAmount().subtract(totalClaimedAmount);
    }

    /**
     * 按日期范围批量自动认领交易明细
     * 只有已认领的交易明细才会被处理和入库
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 处理的交易明细数量
     * @throws Exception 处理异常
     */
    @Override
    @Transactional
    public int batchAutoClaimByDateRange(LocalDate startDate, LocalDate endDate) throws Exception {
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            throw new BusinessException("日期范围无效");
        }
        
        int processedCount = 0;
        
        // 遍历日期范围
        for (LocalDate currentDate = startDate; !currentDate.isAfter(endDate); currentDate = currentDate.plusDays(1)) {
            try {
                log.info("处理{}的交易明细自动认领", currentDate);
                
                // 查询当天的交易明细 - 注意：现在cosmicQuery方法不会自动入库，只返回查询结果
                TransactionCreditedQueryDto queryDto = new TransactionCreditedQueryDto()
                        .setBizDate(DateUtil.date(currentDate).toJdkDate())
                        .setCompanyNumber(Collections.singletonList(CosmicConstant.ORG_NUMBER))
                        .setBankAccountNumber("*******************")
                        .setIsFullQuery(false)
                        .setPageNo(1)
                        .setPageSize(2000);
                
                RestResponse response = transactionFeign.transactionDetailCreditedQuery(queryDto);
                if (!BooleanUtil.isTrue(response.getSuccess())) {
                    log.error("查询{}的交易明细失败: {}", currentDate, response.getMessage());
                    continue;
                }
                
                BaseQueryPageVo<TransactionCreditedQueryVo> pageVo = JSONObject.from(response.get("data")).to(
                        new TypeReference<BaseQueryPageVo<TransactionCreditedQueryVo>>() {
                        }
                );
                
                List<CosmicTransactionEntity> transactions = pageVo.getRows().stream()
                        .map(this::cosmicVoToEntity)
                        .collect(Collectors.toList());
                
                if (CollectionUtil.isEmpty(transactions)) {
                    log.info("{}没有找到待处理的交易明细", currentDate);
                    continue;
                }
                
                log.info("{}找到{}条交易明细，准备自动认领", currentDate, transactions.size());
                processedCount += transactions.size();
                
                // 执行自动认领 - autoClaimTransactionDetail方法会在认领成功后入库
                autoClaimTransactionDetail(transactions);
                
            } catch (Exception e) {
                log.error("处理{}日期的交易明细自动认领失败", currentDate, e);
                throw e;
            }
        }
        
        return processedCount;
    }

}
