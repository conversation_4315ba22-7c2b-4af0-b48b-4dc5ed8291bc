package cn.uone.business.xhcosmic.controller;


import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.uone.bean.entity.business.xhcosmic.CosmicReimbursementEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicReimbursementItemEntity;
import cn.uone.bean.entity.business.xhcosmic.vo.ReimbursementSearchVo;
import cn.uone.business.xhcosmic.service.ICosmicReimbursementService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 金蝶(星瀚)报账工单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@RestController
@RequestMapping("/cosmic-reimbursement-entity")
public class CosmicReimbursementController extends BaseController {

    @Autowired
    private ICosmicReimbursementService cosmicReimbursementService;

    @GetMapping("/page")
    @ApiOperation("/分页列表")
    public RestResponse page(Page<CosmicReimbursementEntity> page, ReimbursementSearchVo searchVo) {
        IPage<CosmicReimbursementEntity> cosmicReimbursementEntityIPage = cosmicReimbursementService.listForPage(page, searchVo);
        return RestResponse.success().setData(cosmicReimbursementEntityIPage);
    }

    @ApiOperation("根据id获取详情列表")
    @GetMapping("/itemListById")
    public RestResponse itemListById(Page<CosmicReimbursementItemEntity> page, @RequestParam String id) {
        IPage<CosmicReimbursementItemEntity> itemEntityIPage = cosmicReimbursementService.itemListById(page, id);
        return RestResponse.success().setData(itemEntityIPage);
    }

    @ApiOperation("推送")
    @PostMapping("/push")
    public RestResponse push(@RequestBody List<String> ids) {
        return cosmicReimbursementService.pushToCosmic(ids.toArray(new String[0]));
    }

    @ApiOperation("生成报账工单")
    @GetMapping("/generate")
    @UonePermissions
    public RestResponse generate(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX") Date startTime
            , @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX") Date endTime
            , @RequestParam(required = false) List<String> orderIds) {
        RestResponse restResponse = cosmicReimbursementService.cosmicIncomeBatchAdd(startTime, endTime, orderIds);
        return restResponse;
    }
}
