package cn.uone.business.kingdee.service;

import cn.uone.bean.entity.business.kingdee.KingdeeAmortizeEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeAmortizeVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
public interface IKingdeeAmortizeService extends IService<KingdeeAmortizeEntity> {
    IPage<KingdeeAmortizeEntity> findByCondition(Page page, KingdeeAmortizeVo kingdeeAmortizeVo);
    KingdeeAmortizeVo selectVoById(String id);
}
