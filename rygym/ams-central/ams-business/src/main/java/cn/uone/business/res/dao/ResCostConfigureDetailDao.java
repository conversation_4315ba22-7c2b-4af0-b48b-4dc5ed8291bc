package cn.uone.business.res.dao;

import cn.uone.bean.entity.business.res.ResCostConfigureDetailEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface ResCostConfigureDetailDao extends BaseMapper<ResCostConfigureDetailEntity> {
    List<ResCostConfigureDetailEntity> queryList(@Param("id") String id);

    List<Map<String, Object>> queryListBasics(@Param("map") Map<String, Object> map);

}
