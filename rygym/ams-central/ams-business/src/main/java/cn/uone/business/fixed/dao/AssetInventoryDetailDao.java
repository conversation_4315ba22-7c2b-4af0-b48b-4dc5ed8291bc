package cn.uone.business.fixed.dao;

import cn.uone.bean.entity.business.fixed.AssetInventoryDetailEntity;
import cn.uone.bean.entity.business.fixed.vo.AssetInventoryDetailVo;
import cn.uone.bean.entity.business.fixed.vo.FixedPropertyVo;
import cn.uone.bean.entity.business.fixed.vo.InventoryStatisticsVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 固定资产盘点详情表 盘点子表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface AssetInventoryDetailDao extends BaseMapper<AssetInventoryDetailEntity> {
    IPage<FixedPropertyVo> pageList(Page page, DataScope dataScope, @Param("map") Map<String, Object> map);

    List<AssetInventoryDetailEntity> getList(DataScope dataScope, @Param("map") Map<String, Object> map);

    boolean deleteDetailByInventoryId(@Param("map") Map<String, Object> map);

    List<FixedPropertyVo> pageList(DataScope dataScope, @Param("map") Map<String, Object> map);

    int updateByCode(@Param("state")String state,@Param("id")String id,@Param("code")String code);

    int checkByCode(@Param("id")String id,@Param("code")String code);

    InventoryStatisticsVo getQuantity(String inventoryId);
}
