package cn.uone.business.apro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.ApprovalTemplateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.crm.reit.ReitReimbursementEntity;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.Operation;
import cn.uone.fegin.crm.IReimFegin;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.CodeUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@Service
public class Reimbursemettion implements Operation {

    @Resource
    private IReimFegin reimFegin ;
    @Resource
    private IApprovalCommitService approvalCommitService;

    @Override
    public ApprovalCommitEntity apply(Expression expression) {
        ReitReimbursementEntity entity=reimFegin.getReit(expression.getCodeId());
        ApprovalCommitEntity  commitEntity=null;
        if(ObjectUtil.isNotNull(entity)){
            commitEntity= approvalCommitService.getCommitEntity(expression.getCodeId(),ApprovalTypeEnum.REIMBURSEMENT.getValue());
            if(commitEntity==null){
                commitEntity=new ApprovalCommitEntity();
                commitEntity.setCode(UUID.randomUUID().toString().replaceAll("-",""));
                commitEntity.setCodeId(expression.getCodeId());
                commitEntity.setUserid(UoneSysUser.id());
                commitEntity.setStatus(ApprovalStateEnum.APPROVAL.getValue());
                commitEntity.setType(ApprovalTypeEnum.REIMBURSEMENT.getValue());
                commitEntity.setApprovalNum(entity.getCode());
                commitEntity.setPrice(entity.getPrice());
                commitEntity.setApplyTime(new Date());
                commitEntity.setAuthorizedPrice(entity.getPrice());
                commitEntity.setTableName(ApprovalTypeEnum.REIMBURSEMENT.getTable());
                commitEntity.setTitle(ApprovalTypeEnum.REIMBURSEMENT.getName());
                commitEntity.setTitle1("报销部门:"+entity.getDept());
                commitEntity.setTitle2("报销项目:"+entity.getProjectId());
                commitEntity.setTitle3("报销类型:"+entity.getPayType());
                commitEntity.setPayUnit(entity.getPayUnit());
                if(entity.getPrice().compareTo(new BigDecimal(1000000))==-1){
                    commitEntity.setTemplateid(ApprovalTemplateEnum.REIMBURSEMENTLOW.getValue());
                }else{
                    commitEntity.setTemplateid(ApprovalTemplateEnum.REIMBURSEMENTHIGH.getValue());
                }
                commitEntity.setImage(UoneSysUser.icon());
                approvalCommitService.save(commitEntity);
                approvalCommitService.changeStatus(commitEntity,false);
            }else{
                commitEntity.setAuthorizedPrice(entity.getPrice());
                commitEntity.setPrice(entity.getPrice());
                commitEntity.setOldCode(commitEntity.getCode());
                commitEntity.setCode(CodeUtil.generateUuid(true));
                commitEntity.setApplyTime(new Date());
                approvalCommitService.resetInitCommit(commitEntity, "报销部门:" + entity.getDept(), "报销项目:" + entity.getProjectId(), "报销类型:" + entity.getPayType(),ApprovalStateEnum.APPROVAL.getValue());
                approvalCommitService.updateById(commitEntity);
            }
        }

        return commitEntity;
    }


}
