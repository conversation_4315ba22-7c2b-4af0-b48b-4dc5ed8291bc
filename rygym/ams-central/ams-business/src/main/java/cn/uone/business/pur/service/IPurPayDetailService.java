package cn.uone.business.pur.service;

import cn.uone.bean.entity.business.pur.PurPayDetailEntity;
import cn.uone.bean.entity.business.pur.vo.PurPayDetailEntityVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 * 采购订单付款详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-14
 */
public interface IPurPayDetailService extends IService<PurPayDetailEntity> {

    void addPurPayDetail(PurPayDetailEntity purPayDetailEntity);

    PurPayDetailEntityVo queryPurPayDetail(PurPayDetailEntity purPayDetailEntity);
    IPage<PurPayDetailEntityVo> queryPurPayDetailPage(Page page, Map map);
}
