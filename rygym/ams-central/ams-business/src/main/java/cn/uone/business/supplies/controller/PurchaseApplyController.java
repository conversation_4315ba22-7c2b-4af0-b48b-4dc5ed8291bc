package cn.uone.business.supplies.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.supplies.CategoryEntity;
import cn.uone.bean.entity.business.supplies.PurchaseApplyEntity;
import cn.uone.bean.entity.business.supplies.WarehousingEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.business.supplies.service.ICategoryService;
import cn.uone.business.supplies.service.IPurchaseApplyService;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 * 采购申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-30
 */
@RestController
@RequestMapping("/supplies/purchaseApply")
public class PurchaseApplyController extends BaseController {

    @Autowired
    private IPurchaseApplyService service;
    @Autowired
    private ICategoryService categoryService;
    @Autowired
    private IUserFegin userFegin;


    @GetMapping("/page")
    public RestResponse page(Page<PurchaseApplyEntity> page, PurchaseApplyEntity entity){
        IPage<PurchaseApplyEntity> p = service.getListByPage(page,entity);
        return RestResponse.success().setData(p);
    }


    @PostMapping("/save")
    public RestResponse save(PurchaseApplyEntity entity){
        if(!StrUtil.isNotBlank(entity.getAuditor())){
            entity.setApplicant(UoneSysUser.nickName());
            entity.setApplicantId(UoneSysUser.id());
            entity.setMobile(UoneSysUser.tel());
        }
        CategoryEntity categoryEntity = categoryService.getById(entity.getMaterialCategoryId());
        if("否".equals(entity.getSpecial()) && entity.getQuantity()>categoryEntity.getQuota()){
            return RestResponse.failure("此物料采购限额"+categoryEntity.getQuota()+",如需继续申请，请选择特殊采购！");
        }
        entity = audit(entity);
        entity.setApplyTime(new Date());
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    @PostMapping("/edit")
    public RestResponse edit(PurchaseApplyEntity entity){
        CategoryEntity categoryEntity = categoryService.getById(entity.getMaterialCategoryId());
        if("否".equals(entity.getSpecial()) && entity.getQuantity()>categoryEntity.getQuota()){
            return RestResponse.failure("此物料采购限额"+categoryEntity.getQuota()+",如需继续申请，请选择特殊采购！");
        }
        PurchaseApplyEntity purchaseApplyEntity = service.getById(entity.getId());
        String state = purchaseApplyEntity.getState();
        if(!"0".equals(state)){
            return RestResponse.failure("修改失败！审核状态已改变，请刷新");
        }
        entity = audit(entity);
        entity.setApplyTime(new Date());
        entity.insertOrUpdate();
        return RestResponse.success();
    }
    @PostMapping("/pass")
    public RestResponse pass(PurchaseApplyEntity purchaseApplyEntity){

        String grade = userFegin.getGradeByUserId(purchaseApplyEntity.getAuditorId());
        Map<String,Object> map = userFegin.getAuditor(purchaseApplyEntity.getAuditorId(),grade);
        if(map!=null){
            //更新当前审核人
            purchaseApplyEntity.setAuditor(map.get("realName")==null?null:map.get("realName").toString());
            purchaseApplyEntity.setAuditorId( map.get("id")==null?null:map.get("id").toString());
        }
        purchaseApplyEntity.setFlowNode(grade);
        purchaseApplyEntity.setState("1");
        if("1".equals(grade)){
            purchaseApplyEntity.setDeptLeadData(new Date());
            purchaseApplyEntity.setDeptResult("审核通过");
            purchaseApplyEntity.insertOrUpdate();
        }else if("2".equals(grade)){
            purchaseApplyEntity.setBranchedLeadData(new Date());
            purchaseApplyEntity.setBranchedResult("审核通过");
            purchaseApplyEntity.insertOrUpdate();
        }else if("3".equals(grade)){
            purchaseApplyEntity.setAuditor("流程结束");
            purchaseApplyEntity.setFlowNode("4");
            purchaseApplyEntity.setGeneralManagerData(new Date());
            purchaseApplyEntity.setState("2");
            purchaseApplyEntity.setGeneralResult("审核通过");
            //生成入库表
          boolean insert = ware(purchaseApplyEntity);
            if(insert){
                purchaseApplyEntity.insertOrUpdate();
            }else{
                return RestResponse.failure("操作失败");
            }
        }else {
            //当没有上级部门时自己就是审核人
            purchaseApplyEntity.setAuditor("流程结束");
            purchaseApplyEntity.setAuditorId(purchaseApplyEntity.getAuditorId());
            purchaseApplyEntity.setFlowNode("4");
            purchaseApplyEntity.setGeneralManagerData(new Date());
            purchaseApplyEntity.setState("2");
            //生成入库表
            boolean insert = ware(purchaseApplyEntity);
            if(insert){
                purchaseApplyEntity.insertOrUpdate();
            }else{
                return RestResponse.failure("操作失败");
            }
        }
        return RestResponse.success();
    }
    @PostMapping("/fail")
    public RestResponse fail(PurchaseApplyEntity entity){
        String grade = userFegin.getGradeByUserId(entity.getAuditorId());
        if("1".equals(grade)){
            entity.setDeptLeadData(new Date());
            entity.setDeptResult("审核不通过");
        }else if("2".equals(grade)){
            entity.setBranchedLeadData(new Date());
            entity.setBranchedResult("审核不通过");
        }else if("3".equals(grade)){
            entity.setGeneralManagerData(new Date());
            entity.setGeneralResult("审核不通过");
        }
        entity.setAuditor("流程结束");
        entity.setFlowNode("4");
        entity.setState("3");
        entity.setAuditTime(new Date());
        entity.insertOrUpdate();
        return RestResponse.success();
    }
//

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        PurchaseApplyEntity purchaseApplyEntity = new PurchaseApplyEntity();
        for (String id:ids) {
            purchaseApplyEntity = service.getById(id);

            String state = purchaseApplyEntity.getState();
            if(!"0".equals(state)){
                return RestResponse.failure("删除失败！审核状态已改变，请刷新");
            }
        }
        service.removeByIds(ids);
        return RestResponse.success();
    }

    /*
      获取默认值
    */
    @RequestMapping("/getLoginInfo")
    public RestResponse getLoginInfo() {
        Map<String,String> map=new HashMap<>();
        map.put("applicant",UoneSysUser.nickName());
        map.put("applicantId",UoneSysUser.id());
        map.put("mobile",UoneSysUser.tel());
        return RestResponse.success().setData(map);
    }
    /*
     获取所有用户
   */
    @RequestMapping("/getAuditor")
    public RestResponse getAuditor() {
        List<UserEntity> userEntityList = userFegin.getUserList("");
        List<Map<String,String>> data=new ArrayList<>();
        Map<String,String> m=new HashMap<>();
        m.put("name",UoneSysUser.nickName());
        m.put("value",UoneSysUser.id());
        m.put("mobile",UoneSysUser.tel());
        data.add(m);
        for(UserEntity userEntity:userEntityList){
            if(!(userEntity.getId()).equals(UoneSysUser.id())){
                Map<String,String> map=new HashMap<>();
                map.put("name",userEntity.getRealName());
                map.put("value",userEntity.getId());
                map.put("mobile",userEntity.getTel());
                data.add(map);
            }
        }
        return RestResponse.success().setData(data);
    }
    @GetMapping("/auditList")
    public RestResponse auditList(Page<PurchaseApplyEntity> page, PurchaseApplyEntity entity){

        entity.setAuditorId(UoneSysUser.id());
        entity.setState("审批");
//        wrapper.eq("auditor_id",UoneSysUser.id());
//        wrapper.in("state","0","1");
        IPage<PurchaseApplyEntity> p = service.getListByPage(page,entity);
        return RestResponse.success().setData(p);
    }
    //获取采购限额
    @GetMapping("/getQuota")
    public RestResponse getQuota(String categoryId){
        CategoryEntity categoryEntity = categoryService.getById(categoryId);
        return RestResponse.success().setData(categoryEntity);
    }

    public PurchaseApplyEntity audit(PurchaseApplyEntity entity){
        String grade = userFegin.getGradeByUserId(entity.getApplicantId());
        Map<String,Object> map = userFegin.getAuditor(entity.getApplicantId(),grade);
        if(map==null){
            map = new HashMap<>();
            //无上级审核人则取本身
            map.put("realName",entity.getApplicant());
            map.put("id",entity.getApplicantId());
        }
        entity.setState("0");
        if("3".equals(grade)){
            entity.setGeneralManagerData(new Date());
            entity.setGeneralResult("发起申请");
            entity.setGeneralManager(entity.getApplicant());
            entity.setGeneralManagerId(entity.getApplicantId());
            entity.setAuditor("流程结束");
            entity.setFlowNode("4");
            entity.setState("2");
            //生成入库表
            ware(entity);
            return entity;
        }
        entity.setFlowNode(grade);
        //当前审核人
        String nowName = map.get("realName")==null?null:map.get("realName").toString();
        String nowId = map.get("id")==null?null:map.get("id").toString();
        entity.setAuditor(nowName);
        entity.setAuditorId(nowId);
        //下一个审核人
        String nextGrade = userFegin.getGradeByUserId(nowId);
        Map<String,Object> nextMap = userFegin.getAuditor(nowId,nextGrade);
        String nextName = "";
        String nextId = "";
        if(nextMap!=null){
             nextName = nextMap.get("realName")==null?null:nextMap.get("realName").toString();
             nextId = nextMap.get("id")==null?null:nextMap.get("id").toString();
        }
        //记录各个节点审核人
        if("0".equals(grade)){
            //部门主管
            entity.setDeptLead(nowName);
            entity.setDeptLeadId(nowId);
            //分管领导
            entity.setBranchedLead(nextName);
            entity.setBranchedLeadId(nextId);
            //总经理
            String generalGrade = userFegin.getGradeByUserId(nextId);
            if(!ObjectUtil.isEmpty(generalGrade)){
                Map<String,Object> generalMap = userFegin.getAuditor(nextId,generalGrade);
                entity.setGeneralManager(generalMap.get("realName")==null?null:generalMap.get("realName").toString());
                entity.setGeneralManagerId(generalMap.get("id")==null?null:generalMap.get("id").toString());
            }else {
                entity.setGeneralManager(entity.getApplicant());
                entity.setGeneralManagerId(entity.getApplicantId());
            }

        }else if("1".equals(grade)){
            entity.setDeptLead(entity.getApplicant());
            entity.setDeptLeadId(entity.getApplicantId());
            entity.setDeptLeadData(new Date());
            entity.setDeptResult("发起申请");
            //分管领导
            entity.setBranchedLead(nowName);
            entity.setBranchedLeadId(nowId);
            //总经理
            entity.setGeneralManager(nextName);
            entity.setGeneralManagerId(nextId);
        }else if("2".equals(grade)){
            entity.setBranchedLeadData(new Date());
            entity.setBranchedResult("发起申请");
            entity.setBranchedLead(entity.getApplicant());
            entity.setBranchedLeadId(entity.getApplicantId());
            //总经理
            entity.setGeneralManager(nowName);
            entity.setGeneralManagerId(nowId);
        }
        return entity;
    }
    public boolean ware(PurchaseApplyEntity entity) {
        WarehousingEntity warehousingEntity = new WarehousingEntity();
        warehousingEntity.setMaterialCategoryId(entity.getMaterialCategoryId());
        warehousingEntity.setItemName(entity.getItemName());
        warehousingEntity.setQuantity(entity.getQuantity());
        warehousingEntity.setPurchaseId(entity.getId());
        warehousingEntity.setType("1");
        warehousingEntity.setState("1");
        return warehousingEntity.insertOrUpdate();
    }
}
