package cn.uone.business.util;

import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
@RefreshScope
public class SendSmsVo {

    private String mobile;
    private String cr_code;
    private String cr_content;
    private String ali_code;
    private String ali_content;


    public SendSmsVo() {

    }

    public void generate(Map<String, Object> map) throws Exception {

    }


    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCr_code() {
        return cr_code;
    }

    public void setCr_code(String cr_code) {
        this.cr_code = cr_code;
    }

    public String getCr_content() {
        return cr_content;
    }

    public void setCr_content(String cr_content) {
        this.cr_content = cr_content;
    }

    public String getAli_code() {
        return ali_code;
    }

    public void setAli_code(String ali_code) {
        this.ali_code = ali_code;
    }

    public String getAli_content() {
        return ali_content;
    }

    public void setAli_content(String ali_content) {
        this.ali_content = ali_content;
    }


}
