package cn.uone.business.fixed.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.uone.bean.entity.business.fixed.FixedPropertyEntity;
import cn.uone.bean.entity.business.fixed.FixedRfidBatchEntity;
import cn.uone.bean.entity.business.fixed.FixedRfidEntity;
import cn.uone.bean.entity.business.fixed.vo.FixedRfidVo;
import cn.uone.business.fixed.service.IFixedPropertyService;
import cn.uone.business.fixed.service.IFixedRfidService;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.ExcelDataUtil;
import cn.uone.web.util.ExcelRender;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import cn.uone.web.base.BaseController;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * RFID标签 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@RestController
@RequestMapping("/fixed/fixed-rfid-entity")
public class FixedRfidController extends BaseController {
    @Resource
    private IFixedRfidService iFixedRfidService;
    @Resource
    private IFixedPropertyService iFixedPropertyService;


    @RequestMapping("/selectRFID")
    public RestResponse selectRFID(FixedRfidEntity entity) {
        RestResponse response=new RestResponse();
        QueryWrapper<FixedRfidEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("batch_id",entity.getBatchId());
        List<FixedRfidEntity> batchEntities =iFixedRfidService.list(wrapper);
        return response.setSuccess(true).setData(batchEntities);
    }
    /**
     * 获取信息
     *
     * @param fixedRfid
     * @return 标签管理列表 分页
     */
    @GetMapping("/page")
    public RestResponse page(Page page, FixedRfidEntity fixedRfid) {
        IPage<FixedRfidEntity> iPage = iFixedRfidService.page(page, fixedRfid);

        return RestResponse.success().setData(iPage);
    }

    /**
     * 获取信息
     *
     * @param id 主键
     * @return 标签管理列表
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        FixedRfidEntity info = iFixedRfidService.getById(id);
        return RestResponse.success().setData(info);
    }

    /**
     * 新增
     *
     * @param fixedRfid 参数
     * @return 标签管理列表
     */
    @PostMapping("/save")
    public RestResponse save(FixedRfidEntity fixedRfid) {
        if(iFixedRfidService.save(fixedRfid)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    /**
     * 修改
     *
     * @param fixedRfid 参数
     * @return 标签管理列表
     */
    @PostMapping("/edit")
    public RestResponse edit(FixedRfidEntity fixedRfid) {
        if(iFixedRfidService.updateById(fixedRfid)){
            if("1".equals(fixedRfid.getLabelSta())){
                //解绑
                UpdateWrapper<FixedPropertyEntity> update = new UpdateWrapper<>();
                update.eq("t_fixed_property.rfid_id",fixedRfid.getId());
                update.set("t_fixed_property.rfid_id",null);
                update.set("t_fixed_property.rfid_code",null);
                iFixedPropertyService.update(new FixedPropertyEntity(),update);
            }
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return 标签管理列表
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        if(iFixedRfidService.removeByIds(ids)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }
    @RequestMapping("/importRfid")
    public RestResponse importRfid(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return RestResponse.failure("请选择上传文件");
        }
        try {
            List<FixedRfidVo> rfidEntities  = ExcelDataUtil.importData(file.getInputStream(), FixedRfidVo.class);
            if(CollectionUtils.isEmpty(rfidEntities)){
                return RestResponse.failure("读取excel异常");
            }else {
                //查重
                List<String> codes = rfidEntities.stream().map(FixedRfidVo::getCode).collect(Collectors.toList());
                QueryWrapper<FixedRfidEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("t_fixed_rfid.code",codes);

                //单独String集合
                List<String> collect = codes.stream().filter(i -> !Objects.equals(i, ""))               // list 对应的 Stream 并过滤""
                        .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum)) // 获得元素出现频率的 Map，键为元素，值为元素出现的次数
                        .entrySet().stream()                       // 所有 entry 对应的 Stream
                        .filter(e -> e.getValue() > 1)         // 过滤出元素出现次数大于 1 (重复元素）的 entry
                        .map(Map.Entry::getKey)                // 获得 entry 的键（重复元素）对应的 Stream
                        .collect(Collectors.toList());
                if(!collect.isEmpty()&& collect.get(0)!=null){
                    return RestResponse.failure("标签编号:"+collect+"重复了");
                }

                List<FixedRfidEntity> rfidEntityList = iFixedRfidService.list(queryWrapper);
                if(!rfidEntityList.isEmpty()){
                    List<String> codes2 = rfidEntityList.stream().map(FixedRfidEntity::getCode).collect(Collectors.toList());
                    return RestResponse.failure("标签编号:"+codes2+"=已存在");
                }
                int i =0;
                List<FixedRfidEntity> list = new ArrayList<>();
                for(FixedRfidVo vo :rfidEntities){
                    i++;
                    if(StringUtils.isEmpty(vo.getCode())){
                        return RestResponse.failure("第:"+i+"行,标签编号不能为空");
                    }
                    if(StringUtils.isEmpty(vo.getLabelSta())){
                        vo.setLabelSta("1");
                    }else {
                        //状态 1=未使用,2=已使用,3=报废
                        if("未使用".equals(vo.getLabelSta())){
                            vo.setLabelSta("1");
                        }else if("已使用".equals(vo.getLabelSta())){
                            vo.setLabelSta("2");
                        }else if("报废".equals(vo.getLabelSta())){
                            vo.setLabelSta("3");
                        }else {
                            vo.setLabelSta("1");
                        }
                    }
                    FixedRfidEntity fixedRfid = new FixedRfidEntity();
                    BeanUtil.copyProperties(vo,fixedRfid);
                    list.add(fixedRfid);
                }
                iFixedRfidService.saveBatch(list);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return RestResponse.failure("读取excel异常");
        }
        return RestResponse.success("导入成功");
    }

    @UoneLog("导出Rfid模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        ExcelRender.me("/excel/import/fixedRFID.xls").beans(beans).render(response);
    }
}
