package cn.uone.business.kingdee.controller;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * @ClassName Test
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/11 18:39
 * @Version 1.0
 */
public class Test2 {

    public static void main(String[] args) {
        JSONObject paras = new JSONObject();
        paras.put("startEntryDate","2021-06-22");
        paras.put("endEntryDate","2021-06-23");
        paras.put("company","SHMYFD");
        String token = getToken();
        /*String jsonStr = HttpRequest.post("http://www.uone.cn/xy-business/xyKingdee/getReceiptList")
                .header("uone_token",token)
                .body(JSONUtil.toJsonStr(paras))
                .execute().body();
        System.out.println(jsonStr);*/

        String jsonStr = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        try{
            //第一步：创建HttpClient对象
            httpClient = HttpClients.createDefault();

            //第二步：创建httpPost对象
            HttpPost httpPost = new HttpPost("http://www.uone.cn/xy-business/xyKingdee/getReceiptList");

            //第三步：给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(JSONUtil.toJsonStr(paras),"utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setHeader("uone_token", token);
            httpPost.setEntity(requestEntity);

            //第四步：发送HttpPost请求，获取返回值
            jsonStr = httpClient.execute(httpPost,responseHandler); //调接口获取返回值时，必须用此方法

        }catch(Exception e){
            e.printStackTrace();
        }finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        System.out.println(jsonStr);
    }

    private static String getToken() {
        String token = null;
        /*String codeKey = String.format("%04d",new Random().nextInt(9999));
        String code = getCode(codeKey);
        Map<String,Object> paras = Maps.newHashMap();
        paras.put("username","dmk");
        paras.put("password","123456");
        paras.put("code",code);
        paras.put("codeKey",codeKey);
        paras.put("type","1");
        String jsonStr = HttpUtil.post("http://www.uone.cn/xy-crm/uaa/login",paras);
        JSONObject json = JSON.parseObject(jsonStr);
        if(200 == json.getInteger("code")){
            token = json.getJSONObject("data").getString(BaseConstants.HTTP_HEADER_NAME);
        }
        return token;*/

        String codeKey = String.format("%04d",new Random().nextInt(9999));
        String code = getCode(codeKey);
        //1.创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //2.创建HttpPost对象，设置URL地址
        HttpPost httpPost = new HttpPost("http://www.uone.cn/xy-crm/uaa/login");
        //声明list集合，用来分装表单中的参数
        //要求：设置请求的地址是：http://yun.itheima.com/search?keys=java
        List params = new ArrayList();
        params.add(new BasicNameValuePair("username", "dmk"));
        params.add(new BasicNameValuePair("password","123456"));
        params.add(new BasicNameValuePair("code",code));
        params.add(new BasicNameValuePair("codeKey",codeKey));
        params.add(new BasicNameValuePair("type","1"));
        // 创建表单的Entity对象,第一个参数是封装好的表单数据，第二个参数就是编码方式
        UrlEncodedFormEntity formEntity = null;
        try {
            formEntity = new UrlEncodedFormEntity(params, "utf8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //设置表单的Entity对象到Post请求中
        httpPost.setEntity(formEntity);
        //使用httpClient发起响应获取repsonse
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            //4.解析响应，获取数据
            //判断状态码是否是200
            if (response.getStatusLine().getStatusCode() == 200) {
                HttpEntity httpEntity = response.getEntity();
                String content = EntityUtils.toString(httpEntity, "utf8");
                System.out.println(content.length());
                JSONObject json = JSON.parseObject(content);
                if(200 == json.getInteger("code")){
                    token = json.getJSONObject("data").getString(BaseConstants.HTTP_HEADER_NAME);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return token;
    }

    private static String getCode(String codeKey){
        String code = null;
        System.out.println(codeKey);
        String jsonStr = HttpUtil.get("http://www.uone.cn/xy-crm/uaa/getCodeForTpi?codeKey="+codeKey);
        JSONObject json = JSON.parseObject(jsonStr);
        if(200 == json.getInteger("code")){
            code = json.getString("data");
        }
        return code;
    }
}
