package cn.uone.business.bil.service;

import cn.uone.application.enumerate.DataFromEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.bil.vo.*;
import cn.uone.bean.entity.business.biz.vo.SettleVo;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContFrameContractEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IYngyBilOrderService extends IService<BilOrderEntity> {
    /**
     * 保存账单
     *
     * @param orderTypeEnum
     * @param isFirst
     * @param payment
     * @param sourceId
     * @param contract
     * @param payerId
     * @return
     */
    BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                             ContContractEntity contract, String payerId, DataFromEnum dataFromEnum);

    /**
     * 保存账单
     *
     * @param orderTypeEnum
     * @param isFirst
     * @param payment
     * @param sourceId
     * @param contract
     * @param payerId
     * @return
     */
    BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                             ContContractEntity contract, String payerId, Date orderStartDate, DataFromEnum dataFromEnum);
    /**
     * 保存账单
     *
     * @param orderTypeEnum
     * @param isFirst
     * @param payment
     * @param sourceId
     * @param contract
     * @param payerId
     * @return
     */
    BilOrderEntity saveOrder(OrderTypeEnum orderTypeEnum, boolean isFirst, BigDecimal payment, String sourceId,
                             ContContractEntity contract, String payerId, String remark, DataFromEnum dataFromEnum);
    /**
     * 根据合同处理账单开票类型
     *
     * @param order
     * @param cont
     * @return
     */
    BilOrderEntity handleOrder(BilOrderEntity order, ContContractEntity cont);

    /**
     * 综合管理费导入
     * @param orders
     * @param importMonth
     * @param monthEnd
     * @return
     * @throws Exception
     */
    @Transactional
    String importSynthesizeFeeN(List<OrderImportVo> orders, String importMonth, String monthEnd) throws Exception;

    /**
     * 获取付款方
     *
     * @param cont
     * @param cs
     * @param type 支付方类型（租金/生活费用）
     * @return
     */
    String getPayerId(ContContractEntity cont, ContContractSourceRelEntity cs, String type);

    String importRoomLivingFeeN(List<OrderImportVo> order, String monthStart ,String monthEnd)  throws Exception;

    /**
     * 账单通知
     *
     * @param payerId
     */
    void sendMessage(String payerId,String projectId);

}
