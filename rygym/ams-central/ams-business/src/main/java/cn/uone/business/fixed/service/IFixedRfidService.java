package cn.uone.business.fixed.service;

import cn.uone.bean.entity.business.fixed.FixedRfidEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * RFID标签 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
public interface IFixedRfidService extends IService<FixedRfidEntity> {
    IPage<FixedRfidEntity> page(Page page, FixedRfidEntity entity);
}
