package cn.uone.business.emp.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.source.*;
import cn.uone.bean.entity.business.emp.EmpUserEntity;
import cn.uone.bean.entity.business.res.*;
import cn.uone.bean.entity.business.res.vo.SourceImportVo;
import cn.uone.bean.entity.business.res.vo.StaffVo;
import cn.uone.business.emp.service.IEmpUserService;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.ExcelRender;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.ExcelDataUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * 员工表
 *
 * @Auther: ljl
 * @Date: 2019/1/16 17:41
 * @Description:
 */

@RestController
@RequestMapping("/empUser")
public class EmpUserController extends BaseController {

    @Autowired
    private IEmpUserService empUserService;

    @RequestMapping(value = "/pass/query", method = RequestMethod.GET)
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse page(HttpServletRequest request) throws Exception {
        return RestResponse.success().setData(getPage(request));
    }

    private IPage<EmpUserEntity> getPage(HttpServletRequest request) throws Exception {
        Page page = new Page();
        page.setCurrent(Long.parseLong(request.getParameter("current")));
        page.setSize(Long.parseLong(request.getParameter("size")));

        Map<String, Object> map = Maps.newHashMap();
        map.put("name", request.getParameter("name"));
        map.put("tel", request.getParameter("tel"));
        map.put("noInCheckIn",request.getParameter("noInCheckIn"));
        map.put("noInRenterId",request.getParameter("noInRenterId"));
        map.put("contractType",request.getParameter("contractType"));
        IPage<EmpUserEntity> iPage = empUserService.selectPage(page, map);
        return iPage;
    }

    @RequestMapping(value = "/custom/query", method = RequestMethod.GET)
    @UonePermissions(LoginType.CUSTOM)
    public RestResponse customPage(HttpServletRequest request) throws Exception {
        return RestResponse.success().setData(getPage(request));
    }

    @RequestMapping(value = "/addOrUpdate", method = RequestMethod.POST)
    @UonePermissions(LoginType.CUSTOM)
    @UoneLog("添加或更新企业员工")
    public RestResponse addOrUpdate(EmpUserEntity entity) throws Exception {
        return empUserService.saveOrUpdateByEntity(entity);
    }

    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @UonePermissions(LoginType.CUSTOM)
    @UoneLog("删除企业员工")
    public RestResponse del(@RequestBody List<String> ids) {
        RestResponse response = new RestResponse();
        StringBuilder msg = new StringBuilder();
        for (String id : ids) {
            EmpUserEntity empUser = empUserService.getById(id);
            if (empUserService.isExist(empUser.getRenterId(), UoneSysUser.id())) {
                msg.append(String.format("%s该员工申请过入住，不能删除<br/>", empUser.getName()));
            } else {
                empUserService.removeById(id);
            }
        }
        if (StrUtil.isEmpty(msg.toString())) {
            response.setSuccess(true).setMessage("删除成功！");
        } else {
            response.setSuccess(false).setMessage(msg.toString());
        }

        return response;
    }

    @RequestMapping(value = "/export")
    public void export(HttpServletRequest request, HttpServletResponse response) throws BusinessException {
        Map<String, Object> map = Maps.newHashMap();
        ExcelRender.me("/excel/import/importStaff.xlsx").beans(map).render(response);
    }

    @PostMapping(value = "/importStaff", headers = "content-type=multipart/form-data")
    public RestResponse importSource(@RequestParam("file") MultipartFile multipartFile) {
        InputStream is = null;
        try {
            is = multipartFile.getInputStream();
            List<StaffVo> list = ExcelDataUtil.importData(is, StaffVo.class);
            int i = 3;
            int j = 1;
            List<EmpUserEntity> staffs = new ArrayList<>();
            EmpUserEntity empUserEntity = null;
            if (list.size() > 0) {
                for (StaffVo vo : list) {
                    if (StringUtils.isBlank(vo.getStaffName())) {
                        return RestResponse.failure("第" + i + "行，名称不能为空");
                    }
                    if (StringUtils.isBlank(vo.getTel())) {
                        return RestResponse.failure("第" + i + "行，手机号不能为空");
                    }
                    if (StringUtils.isBlank(vo.getIdNo())) {
                        return RestResponse.failure("第" + i + "行，身份证号不能为空");
                    }
                    empUserEntity = new EmpUserEntity();
                    empUserEntity.setIdNo(vo.getIdNo());
                    empUserEntity.setTel(vo.getTel());
                    empUserEntity.setName(vo.getStaffName());
                    empUserEntity.setIdType("1");
                    empUserEntity.setSex(vo.getSex());
                    empUserService.saveOrUpdateByEntity(empUserEntity);
                }
                return RestResponse.success("导入成功！");
            }else{
                return RestResponse.failure("导入失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            if(is != null){
                try {
                    is.close();
                } catch (IOException ioException) {
                    ioException.printStackTrace();
                }
            }
        }

        return RestResponse.success();
    }

}
