package cn.uone.business.rpt.service;

import cn.uone.bean.entity.business.report.vo.ConfirmReleaseVo;
import cn.uone.bean.parameter.ConfirmReleasePo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 退租明细
 */
public interface IReportConfirmReleaseService {

    /**
     * 分页查询
     * @param page
     * @param po
     * @return
     */
    IPage<ConfirmReleaseVo> report(Page page, ConfirmReleasePo po);

    /**
     * 报表导出list
     * @param po
     * @return
     */
    List<ConfirmReleaseVo> report(ConfirmReleasePo po);

    void releaseOrderTask(String projectId,String orderId);

}
