package cn.uone.business.demo.service.impl;

import cn.uone.bean.entity.business.demo.StudentEntity;
import cn.uone.business.demo.dao.CourseDao;
import cn.uone.business.demo.dao.ScoreDao;
import cn.uone.business.demo.dao.StudentDao;
import cn.uone.business.demo.service.IScoreService;
import cn.uone.business.demo.service.IStudentService;
import cn.uone.business.demo.vo.RecordVo;
import cn.uone.business.demo.vo.StudentVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-21
 */
@Service
@Slf4j
public class StudentServiceImpl extends ServiceImpl<StudentDao, StudentEntity> implements IStudentService {
    @Autowired
    StudentDao studentDao;
    @Autowired
    ScoreDao scoreDao;
    @Autowired
    CourseDao courseDao;
    @Autowired
    IScoreService scoreService;

    @Override
    @Transactional
    public void insertRecord(RecordVo recordVo) {
       // QueryWrapper<CourseEntity> query = new QueryWrapper<>();
      //  CourseEntity course=courseDao.selectOne(query.eq("c_name", recordVo.getCname()));
        if(findStuById(recordVo.getStuId())==null){
           StudentEntity stu=new StudentEntity().
                   setStuAge(recordVo.getStuAge()).
                   setStuId(recordVo.getStuId()).
                   setStudName(recordVo.getStudName()).
                   setStuSex(recordVo.getStuSex());
            this.save(stu);
//            studentDao.insert(stu);
           // scoreService.insertScore(recordVo);
        }
            scoreService.insertScore(recordVo);
    }

    @Override
    @Transactional
    public void update(StudentVo studentVo) {
        studentDao.updateStudent(studentVo.getStuId(),studentVo.getStudName());
        scoreDao.updateScore(studentVo.getScore(),studentVo.getStuId(),studentVo.getCname());

    }

    @Override
    @Transactional
    public Integer deleteStu(String stuId) {
        int num=studentDao.deleteById(stuId);
        return num;
    }

    @Override
    public StudentEntity findStuById(String stuId) {
        return studentDao.find(stuId);
    }
}
