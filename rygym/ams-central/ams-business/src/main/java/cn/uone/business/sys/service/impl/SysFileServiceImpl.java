package cn.uone.business.sys.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.SysFileSuffixEnum;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.vo.FastDFSVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.parameter.DeleteFileVo;
import cn.uone.business.sys.dao.SysFileDao;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import sun.misc.BASE64Decoder;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Service
public class SysFileServiceImpl extends ServiceImpl<SysFileDao, SysFileEntity> implements ISysFileService {
    @Autowired
    MinioUtil minioUtil;

    @Override
    public void delFile(SysFileEntity entity) {
        QueryWrapper wrapper = new QueryWrapper();
        if (StringUtils.isNotBlank(entity.getId())) {
            wrapper.eq("id", entity.getId());
        } else {
            if (StringUtils.isNotBlank(entity.getFromId())) {
                wrapper.eq("from_id", entity.getFromId());
            }
            if (StringUtils.isNotBlank(entity.getType())) {
                wrapper.eq("type", entity.getType());
            }
        }
        baseMapper.delete(wrapper);
    }

    @Override
    public List<SysFileEntity> getListByFromIdAndType(String fromId, SysFileTypeEnum type) {
        QueryWrapper<SysFileEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("from_id", fromId);
        if (ObjectUtil.isNotNull(type)) {
            queryWrapper.eq("type", type.getValue());
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<SysFileEntity> getListByFromIdAndTypes(String fromId, String types) {
        QueryWrapper<SysFileEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("from_id", fromId);
        if (ObjectUtil.isNotNull(types)) {
            String[] typeArr = types.split(",");
            List<String> typeList = Arrays.asList(typeArr);
            queryWrapper.in("type",typeList);
        }

        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public SysFileEntity getByFromIdAndType(String fromId, String type) {
        QueryWrapper<SysFileEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("from_id", fromId);
        queryWrapper.eq("type", type);
        queryWrapper.orderByDesc("create_date");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<SysFileEntity> getListByFromId(String fromId) {
        QueryWrapper<SysFileEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("from_id", fromId);
        List<SysFileEntity> list=baseMapper.selectList(queryWrapper);
        List<SysFileEntity> fileList=new ArrayList<>();
//        for(SysFileEntity entity:list){
//            entity.setPath(entity.getPath());
//            fileList.add(entity);
//        }
        return list;
    }


    @Override
    @Deprecated
    //用 getListByFromIdAndType 代替
    public List<SysFileEntity> getFiles(SysFileEntity entity) {
        QueryWrapper wrapper = null;
        if (entity != null) {
            wrapper = new QueryWrapper();
            if (StringUtils.isNotBlank(entity.getFromId())) {
                wrapper.eq("from_id", entity.getFromId());
            }
            if (StringUtils.isNotBlank(entity.getType())) {
                wrapper.eq("type", entity.getType());
            }
        }
        return baseMapper.selectList(wrapper);

    }

    @Override
    public boolean delFileByFromIdAndType(String fromId, SysFileTypeEnum type) {
        List<SysFileEntity> list = baseMapper.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", fromId).eq("type", type.getValue()));
        for (SysFileEntity file : list) {
            //FileUtil.delete(file.getUrl());
            minioUtil.delete(file.getUrl());
        }
        return baseMapper.delete(new UpdateWrapper<SysFileEntity>().eq("from_id", fromId).eq("type", type.getValue())) > 0;
    }

    @Override
    public boolean delFileByFromId(String fromId) {
        List<SysFileEntity> list = baseMapper.selectList(new QueryWrapper<SysFileEntity>().eq("from_id", fromId));
        for (SysFileEntity file : list) {
            //FileUtil.delete(file.getUrl());
            minioUtil.delete(file.getUrl());
        }
        return baseMapper.delete(new UpdateWrapper<SysFileEntity>().eq("from_id", fromId)) > 0;
    }

    @Override
    public boolean delFileByFileIds(List<String> ids) {
        if(ids==null){
            return false;
        }
        for (String id:ids) {
            SysFileEntity sysFileEntity = baseMapper.selectById(id);
            if(sysFileEntity!=null){
                sysFileEntity.deleteById(id);
                minioUtil.delete(sysFileEntity.getUrl());
            }
        }
        return true;
    }

    @Override
    public void deleteFiles(DeleteFileVo vo) throws Exception {
        QueryWrapper<SysFileEntity> query = new QueryWrapper<>();
        if (StrUtil.isBlank(vo.getFromId())) {
            throw new Exception("formid 为空");//防止文件被误删
        }
        query.eq("from_id", vo.getFromId());
        if (null != vo.getFilesIds() && vo.getFilesIds().length > 0) {
            query.notIn("id", vo.getFilesIds());
        }
        if (null != vo.getTypes() && vo.getTypes().length > 0) {
            query.in("type", vo.getTypes());
        }
        List<SysFileEntity> files = this.list(query);
        //更新合同附件
        if (files != null) {
            for (SysFileEntity file : files) {
                //FileUtil.delete(file.getUrl());
                minioUtil.delete(file.getUrl());
                this.removeById(file.getId());
            }
        }
    }

    @Override
    public SysFileEntity saveImg(MultipartFile file, String fromId, String type, String name) throws Exception {
        //String url = FileUtil.save(file);
        String url = minioUtil.save(file);

        if (StrUtil.isBlank(url)) {
            throw new Exception("文件上传失败");
        }
        SysFileEntity entity = new SysFileEntity();
        entity.setFromId(fromId);
        entity.setType(type);
        entity.setUrl(url);
        if (StrUtil.isNotBlank(name)) {
            entity.setName(name);
        }
        this.saveOrUpdate(entity);
        return entity;
    }

    @Override
    public void saveFiles(List<MultipartFile> files, String fromId, String type) {
        if (ObjectUtil.isNotNull(files)) {
            for (MultipartFile file : files) {
                if (StrUtil.isNotEmpty(file.getOriginalFilename())) {
                    //String url = FileUtil.save(file);
                    String url = minioUtil.save(file);
                    SysFileEntity fileEntity = new SysFileEntity();
                    fileEntity.setFromId(fromId);
                    fileEntity.setType(type);
                    fileEntity.setName(file.getOriginalFilename());
                    fileEntity.setUrl(url);
                    fileEntity.insert();
                }
            }
        }

    }

    @Override
    public void uploadFiles(List<MultipartFile> files, String formId, String msg, SysFileTypeEnum fileTypeEnum) throws Exception {
        if (null != files && files.size() > 0) {
            for (MultipartFile file : files) {
                //String url = FileUtil.save(file);
                String url = minioUtil.save(file);
                if (StrUtil.isBlank(url)) {
                    throw new Exception(msg);
                }
                SysFileEntity entity = new SysFileEntity();
                entity.setFromId(formId);
                entity.setType(fileTypeEnum.getValue());
                entity.setUrl(url);
                entity.insertOrUpdate();
            }
        }
    }

    @Override
    public void uploadFiles(List<MultipartFile> files, String fromId, String fileType) throws Exception {
        if (null != files && files.size() > 0) {
            for (MultipartFile file : files) {
                //String url = FileUtil.save(file);
                String url = minioUtil.save(file);
                if (StrUtil.isBlank(url)) {
                    throw new Exception("上传文件失败");
                }
                SysFileEntity entity = new SysFileEntity();
                entity.setFromId(fromId);
                entity.setType(fileType);
                entity.setUrl(url);
                entity.insertOrUpdate();
            }
        }
    }

    @Override
    public void saveFilesByRequest(HttpServletRequest request, String value, String id) {
        try {
            CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());
            commonsMultipartResolver.setDefaultEncoding("utf-8");
            if (commonsMultipartResolver.isMultipart(request)) {
                MultipartHttpServletRequest mulReq = (MultipartHttpServletRequest) request;
                Map<String, MultipartFile> map = mulReq.getFileMap();
                for (Map.Entry<String, MultipartFile> entry : map.entrySet()) {
                    saveImg(entry.getValue(), id, value, null);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public SysFileEntity getByUrl(String url) {
        QueryWrapper<SysFileEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("url", url);
        return getOne(queryWrapper);
    }

    @Override
    public  File saveUrlAs(String url, String filePath, String method,String name){
        //System.out.println("fileName---->"+filePath);
        //创建不同的文件夹目录
        File file=new File(filePath);
        //判断文件夹是否存在
        if (!file.exists())
        {
            //如果文件夹不存在，则创建新的的文件夹
            file.mkdirs();
        }
        FileOutputStream fileOut = null;
        HttpURLConnection conn = null;
        InputStream inputStream = null;
        try
        {
            // 建立链接
            URL httpUrl=new URL(url);
            conn=(HttpURLConnection) httpUrl.openConnection();
            //以Post方式提交表单，默认get方式
            conn.setRequestMethod(method);
            conn.setDoInput(true);
            conn.setDoOutput(true);
            // post方式不能使用缓存
            conn.setUseCaches(false);
            //连接指定的资源
            conn.connect();
            //获取网络输入流
            inputStream=conn.getInputStream();
            BufferedInputStream bis = new BufferedInputStream(inputStream);
            //判断文件的保存路径后面是否以/结尾
            if (!filePath.endsWith("/")) {

                filePath += "/";

            }
            //写入到文件（注意文件保存路径的后面一定要加上文件的名称）
            fileOut = new FileOutputStream(filePath+name);
            File newFile=new File(filePath+name);
            file=newFile;
            BufferedOutputStream bos = new BufferedOutputStream(fileOut);

            byte[] buf = new byte[4096];
            int length = bis.read(buf);
            //保存文件
            while(length != -1)
            {
                bos.write(buf, 0, length);
                length = bis.read(buf);
            }
            bos.close();
            bis.close();
            conn.disconnect();
        } catch (Exception e)
        {
            e.printStackTrace();
            System.out.println("抛出异常！！");
        }
        return file;
    }

    //TODO modify by zengguoshen 20211113  start base64转成MultipartFile
    @Override
    public MultipartFile base64ToMultipart(String base,String contentType) {
        try {
            String[] baseStrs = base.split(",");

            BASE64Decoder decoder = new BASE64Decoder();
            byte[] b = new byte[0];
            b = decoder.decodeBuffer(baseStrs[1]);

            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            MultipartFile file=new MockMultipartFile(contentType,b);
            return file;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }
    //TODO modify by zengguoshen 20211113  end

}
