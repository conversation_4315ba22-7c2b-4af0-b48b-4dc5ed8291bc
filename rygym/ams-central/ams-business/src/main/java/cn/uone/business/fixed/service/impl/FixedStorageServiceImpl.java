package cn.uone.business.fixed.service.impl;

import cn.uone.bean.entity.business.fixed.FixedStorageEntity;
import cn.uone.business.fixed.dao.FixedStorageDao;
import cn.uone.business.fixed.service.IFixedStorageService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 固定资产入库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Service
public class FixedStorageServiceImpl extends ServiceImpl<FixedStorageDao, FixedStorageEntity> implements IFixedStorageService {

    @Override
    public IPage<FixedStorageEntity> getListByPage(Page page, FixedStorageEntity entity) {
        return baseMapper.getListByPage(page,entity);
    }

    @Override
    public FixedStorageEntity getById(String id) {
        return baseMapper.getById(id);
    }
}
