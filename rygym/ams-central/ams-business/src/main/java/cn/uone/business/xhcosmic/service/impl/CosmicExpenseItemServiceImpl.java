package cn.uone.business.xhcosmic.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.xhcosmic.CosmicExpenseItemEntity;
import cn.uone.business.xhcosmic.dao.CosmicExpenseItemDao;
import cn.uone.business.xhcosmic.service.ICosmicExpenseItemService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单明细对应收支项目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Service
public class CosmicExpenseItemServiceImpl extends ServiceImpl<CosmicExpenseItemDao, CosmicExpenseItemEntity> implements ICosmicExpenseItemService {

    public static final Map<String, CosmicExpenseItemEntity> INIT_MAP = new Hashtable<>();

    public static final Map<String, CosmicExpenseItemEntity> INIT_MAP_BY_EXPENSE_ITEM_NUMBER = new Hashtable<>();

    public static final Set<CosmicExpenseItemEntity> INIT_SET = new HashSet<>();

    @PostConstruct
    public void init() {
        List<CosmicExpenseItemEntity> list = list();
        if (INIT_SET.isEmpty()) {
            INIT_SET.addAll(list);
        }
        if (INIT_MAP.isEmpty()) {
            INIT_MAP.putAll(INIT_SET.stream().collect(Collectors.toMap(CosmicExpenseItemEntity::getOrderItemType, Function.identity())));
        }
        if (INIT_MAP_BY_EXPENSE_ITEM_NUMBER.isEmpty()) {
            INIT_MAP_BY_EXPENSE_ITEM_NUMBER.putAll(INIT_SET.stream()
                .collect(Collectors.toMap(
                    CosmicExpenseItemEntity::getExpenseItemNumber,
                    Function.identity(),
                    (existing, replacement) -> existing // 保留第一个出现的值
                )));
        }
    }

    @Override
    public String findNumberByOrderItemType(String orderItemType) {
        return INIT_MAP.getOrDefault(orderItemType, new CosmicExpenseItemEntity()).getExpenseItemNumber();
    }

    @Override
    public IPage<CosmicExpenseItemEntity> queryPage(Page<CosmicExpenseItemEntity> page, String expenseItemName) {
        return page(
                page,
                new LambdaQueryWrapper<CosmicExpenseItemEntity>()
                        .like(StrUtil.isNotBlank(expenseItemName), CosmicExpenseItemEntity::getExpenseItemName, expenseItemName));
    }
}
