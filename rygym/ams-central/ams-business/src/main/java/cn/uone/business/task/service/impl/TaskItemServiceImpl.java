package cn.uone.business.task.service.impl;


import cn.uone.bean.entity.business.task.TaskItemEntity;
import cn.uone.business.task.dao.TaskItemDao;
import cn.uone.business.task.service.ITaskItemService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 任务项管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@Service
public class TaskItemServiceImpl extends ServiceImpl<TaskItemDao, TaskItemEntity> implements ITaskItemService {


    @Autowired
    TaskItemDao taskItemDao;
    @Override
    public IPage<TaskItemEntity> selectPageByPlanId(Page page, String planId, String taskType) {
        return taskItemDao.selectPageByPlanId(page,planId,taskType);
    }

    @Override
    public IPage<TaskItemEntity> selectSublistByPlanId(Page page, String planId) {
        return taskItemDao.selectSublistByPlanId(page,planId);
    }

    @Override
    public List<TaskItemEntity> getByPlanId(String planId) {
        return taskItemDao.getByPlanId(planId);
    }

}
