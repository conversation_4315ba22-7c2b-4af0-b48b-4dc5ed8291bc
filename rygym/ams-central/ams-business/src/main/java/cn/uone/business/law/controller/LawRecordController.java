package cn.uone.business.law.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.law.LawRecordEntity;
import cn.uone.business.law.service.ILawRecordService;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@RestController
@RequestMapping("/law-record-entity")
public class LawRecordController extends BaseController {
    @Resource
    private ILawRecordService iLawRecordService;

    @GetMapping("/recordPage")
    public RestResponse recordPage(Page<LawRecordEntity> page, LawRecordEntity lawRecordEntity) {
        return RestResponse.success().setData(iLawRecordService.queryPage(page, lawRecordEntity));
    }

    /**
     * 保存
     */
    @PostMapping("/saveOrUpdate")
    public RestResponse saveOrUpdate(LawRecordEntity lawRecordEntity) {
        iLawRecordService.saveOrUpdate(lawRecordEntity);
        return RestResponse.success();
    }


    /**
     * 信息
     */
    @RequestMapping("/info")
    public RestResponse info(String id) {
        return RestResponse.success().setData(iLawRecordService.getById(id));
    }

    /**
     * 删除
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        iLawRecordService.removeByIds(ids);
        return RestResponse.success();
    }
}
