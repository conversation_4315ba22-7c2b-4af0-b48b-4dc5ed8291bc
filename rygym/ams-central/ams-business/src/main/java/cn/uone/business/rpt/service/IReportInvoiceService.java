package cn.uone.business.rpt.service;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.vo.BatchInvoiceResultVo;
import cn.uone.bean.entity.business.report.InvoiceEntity;
import cn.uone.bean.entity.business.report.vo.InvoiceVo;
import cn.uone.bean.parameter.InvoicePo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.commons.httpclient.util.DateParseException;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 开票管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
public interface IReportInvoiceService extends IService<InvoiceEntity> {

    IPage<InvoiceVo> selectPages(Page page, InvoicePo param);

    List<InvoiceVo> getList(InvoicePo param);

    List<InvoiceVo> export(InvoicePo param);

    String againPushInvoice(BilOrderEntity order) throws Exception;

    List<String> queryTaxCodeList(Map<String, Object> query);

    String getLastSerialNoByUserId(String id);

    List<Map<String,Object>> getTaxListForKingdee(Map<String, Object> map);

    List<InvoiceEntity> getInvoicing();
    //开票
    void makeInvoice(BilOrderEntity order) throws Exception;
    //开票申请
    void appleInvoice(BilOrderEntity order) throws Exception;

    void queryInvoiceState();

    IPage<InvoiceVo> getInvoicePages(Page page, InvoicePo param);

    void queryInvoice(String id);

    void queryBaiwangInvoiceState() throws DateParseException;
    
    /**
     * 批量开票
     * @param orderIds 订单ID列表
     * @return 批量开票结果
     */
    BatchInvoiceResultVo batchMakeInvoice(List<String> orderIds);
}
