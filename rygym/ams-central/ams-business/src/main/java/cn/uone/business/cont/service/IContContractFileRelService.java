package cn.uone.business.cont.service;

import cn.uone.bean.entity.business.cont.ContContractFileRelEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 合同审批文件关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
public interface IContContractFileRelService extends IService<ContContractFileRelEntity> {

    IPage<ContContractFileRelEntity> selectContFilePage(Page page,Map<String, Object> map);

    String getLastName(String projectId);

    List<SysFileEntity> getFilesByContractId(String contractId);

    Map<String,Object> getStatistics(String projectId);
}
