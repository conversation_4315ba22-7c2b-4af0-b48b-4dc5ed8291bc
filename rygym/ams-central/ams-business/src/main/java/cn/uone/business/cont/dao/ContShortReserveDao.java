package cn.uone.business.cont.dao;

import cn.uone.bean.entity.business.cont.ContShortReserveEntity;
import cn.uone.bean.entity.business.cont.vo.ContShortReserveVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-16
 */
public interface ContShortReserveDao extends BaseMapper<ContShortReserveEntity> {

    IPage<ContShortReserveVo> selectBy(Page page, @Param("map") Map<String, Object> map, DataScope dataScope);

    List<ContShortReserveVo> selectBy(@Param("map") Map<String, Object> map);
}
