package cn.uone.business.biz.service;

import cn.uone.bean.entity.business.biz.BizInspectEntity;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 * 查房表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-05
 */
public interface IBizInspectService extends IService<BizInspectEntity> {

        /**
     * 获取最后一次申请单
     * @param filter
     * @return
     */
    BizInspectEntity getLastApply(Map<String,Object> filter);

    BizInspectEntity addInspect(BizReleaseEntity release, ContContractSourceRelEntity contContractSourceRelEntity);

    BizInspectEntity getByReleaseId(String id);


    //IPage<BizInspectVo> queryList(Page page, HashMap<String, Object> filter);

}
