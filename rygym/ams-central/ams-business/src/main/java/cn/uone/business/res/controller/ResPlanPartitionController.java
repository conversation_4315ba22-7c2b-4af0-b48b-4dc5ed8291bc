package cn.uone.business.res.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.res.ResPlanPartitionEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.business.res.service.IResPlanPartitionService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Api(value="规划分区服务",tags={"分区新增、修改等接口"})
@RestController
@RequestMapping("/res-plan-partition-entity")
public class ResPlanPartitionController extends BaseController{

    @Autowired
    private IResPlanPartitionService resPlanPartitionService;
    @Autowired
    private IResSourceService resSourceService;

    @RequestMapping("/addOrUpdate")
    public RestResponse addOrUpdate(ResPlanPartitionEntity entity){
        if (entity==null){
            return RestResponse.failure("请求对象为空");
        }
        entity.setProjectId(UoneHeaderUtil.getProjectId());
        if(resPlanPartitionService.queryone(entity)!=null){
            return RestResponse.failure("区域名称和已有的分区重复，请重新编辑！");
        }
        boolean b=resPlanPartitionService.saveOrUpdate(entity);
        if (b){
            return RestResponse.success("保存成功");
        }
        return RestResponse.failure("保存失败");
    }

    @RequestMapping("/delete")
    public RestResponse delete(@RequestParam String id){

        ResSourceEntity source=new ResSourceEntity();
        source.setPartitionId(id);
        if(resSourceService.getResSourceEntity(source)!=null){
            return RestResponse.failure("该分区已关联到房源，不能删除！");
        }
        boolean b=resPlanPartitionService.removeById(id);
        if (b){
            return RestResponse.success("删除成功");
        }
        return RestResponse.failure("删除失败");

    }

    @GetMapping("/getList")
    public RestResponse getList() {
        DataScope dataScope=new DataScope(UoneSysUser.id());
        dataScope.setProAlias("tpp");
        dataScope.setParAlias("tpp");
        dataScope.setZoneFieldName("id");
        Map<String,Object> map= Maps.newHashMap();
        map.put("projectId", UoneHeaderUtil.getProjectId());
        IPage pages =resPlanPartitionService.list(dataScope,map);
        return RestResponse.success().setData(pages);
    }

    /**
     * 获取区域（包含有短租房源）
     *
     * @return
     */
    @PostMapping("/getShortList")
    public RestResponse getShortList() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("projectId", UoneHeaderUtil.getProjectId());
        return RestResponse.success().setData(resPlanPartitionService.listShort(map));
    }

    /**
     * 获取区域（包含有短租房源）
     *
     * @return
     */
    @PostMapping("/getListById")
    public RestResponse getListById() {
        QueryWrapper<ResPlanPartitionEntity> wrapper=new QueryWrapper<ResPlanPartitionEntity>();
        wrapper.eq("project_id", UoneHeaderUtil.getProjectId());
        return RestResponse.success().setData(resPlanPartitionService.list(wrapper));
    }

    @RequestMapping("/getListByProjectId")
    public RestResponse getListByProjectId(@RequestParam(required = false) String projectId) {
        QueryWrapper<ResPlanPartitionEntity> wrapper=new QueryWrapper<ResPlanPartitionEntity>();
        wrapper.eq("project_id", projectId);
        wrapper.orderByAsc("name","CHAR_LENGTH(name)");
        return RestResponse.success().setData(resPlanPartitionService.list(wrapper));
    }

    @RequestMapping("/getByProjectId")
    public RestResponse getByProjectId(@RequestParam(required = false) String propertyNature) {
        QueryWrapper<ResPlanPartitionEntity> wrapper = new QueryWrapper<ResPlanPartitionEntity>();
        wrapper.eq("project_id", UoneHeaderUtil.getProjectId());
        if (StrUtil.isNotBlank(propertyNature)) {
            wrapper.eq("property_nature", propertyNature);
        }
        return RestResponse.success().setData(resPlanPartitionService.list(wrapper));
    }

    /**
     * 根据项目id获取分区
     *
     * @param projectId
     * @return
     */
    @RequestMapping("/getResPlanPartitionEntity")
    public RestResponse getResPlanPartitionEntity(@RequestParam(required = false) String projectId,
                                                  @RequestParam(required = false) String propertyNature,
                                                  @RequestParam(required = false) String isPublish) {
        QueryWrapper<ResPlanPartitionEntity> wrapper = new QueryWrapper<ResPlanPartitionEntity>();
        if (projectId == null) {
            projectId = UoneHeaderUtil.getProjectId();
        }
        wrapper.eq("project_id", projectId);
        if (StrUtil.isNotBlank(propertyNature)) {
            wrapper.eq("property_nature", propertyNature);
        }
        if(StrUtil.isNotEmpty(isPublish) && isPublish.equals("1")){//传入已发布状态时,只查询已发布房源
            wrapper.exists("select 1 from t_res_source where t_res_source.partition_id=t_res_plan_partition.id and t_res_source.publish_target='1'");
        }
        wrapper.orderByAsc("name");
        return RestResponse.success().setData(resPlanPartitionService.list(wrapper));
    }

    /**
     * 根据项目id获取分区(不包含车位)
     * @param projectId
     * @return
     */
    @RequestMapping("/getPlanPartitionNotCar")
    public RestResponse getPlanPartitionNotCar(@RequestParam(required = false) String projectId){
        QueryWrapper<ResPlanPartitionEntity> wrapper=new QueryWrapper<ResPlanPartitionEntity>();
        if(projectId==null){
            projectId=UoneHeaderUtil.getProjectId();
        }
        wrapper.eq("project_id",projectId);
        /*wrapper.ne("property_nature", PropertyNatureEnum.CAR.getValue());*/
        return RestResponse.success().setData(resPlanPartitionService.list(wrapper));
    }
}
