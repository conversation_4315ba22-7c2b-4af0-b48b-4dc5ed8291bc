package cn.uone.business.cont.service;

import cn.uone.bean.entity.business.cont.ContParEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
public interface IContParService extends IService<ContParEntity> {

    boolean contractSynToCCB(String contractId);

    boolean orderSynToCCB(String contractId);

    ContParEntity getByContractIdAndType(String contractId,String type);

    void handerConPar(String contractId);

    void saveOrUpdate(String contractId,String type,String value);

    //勿删
    void dealOldConParData(String contractId);
}
