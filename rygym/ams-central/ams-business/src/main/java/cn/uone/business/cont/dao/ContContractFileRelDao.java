package cn.uone.business.cont.dao;

import cn.uone.bean.entity.business.cont.ContContractFileRelEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 合同审批文件关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
public interface ContContractFileRelDao extends BaseMapper<ContContractFileRelEntity> {

    IPage<ContContractFileRelEntity> selectContFilePage(Page page, @Param("map") Map<String, Object> map);

    List<SysFileEntity> getFilesByContractId(@Param("contractId") String contractId);

    Map<String,Object> getStatistics(@Param("projectId") String projectId);
}
