package cn.uone.business.pur.service.impl;

import cn.uone.bean.entity.business.pur.PurPurchaseDetailEntity;
import cn.uone.bean.entity.business.pur.vo.PurPurchaseDetailEntityVo;
import cn.uone.business.pur.dao.PurPurchaseDetailDao;
import cn.uone.business.pur.service.IPurPurchaseDetailService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Service
public class PurPurchaseDetailServiceImpl extends ServiceImpl<PurPurchaseDetailDao, PurPurchaseDetailEntity> implements IPurPurchaseDetailService {

    @Override
    public Integer queryDeviceByPurchaseId(String purchaseId) {
        return baseMapper.queryDeviceByPurchaseId(purchaseId);
    }

    @Override
    public PurPurchaseDetailEntityVo queryPurchaseDetailLinkDevDevice(Map map) {
        return baseMapper.queryPurchaseDetailLinkDevDevice(map);
    }

    @Override
    public IPage<PurPurchaseDetailEntityVo> queryDetailDev(Page page, String purchaseId) {
        return baseMapper.queryDetailDev(page,purchaseId);
    }

}
