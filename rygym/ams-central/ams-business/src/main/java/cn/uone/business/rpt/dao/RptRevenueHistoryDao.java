package cn.uone.business.rpt.dao;

import cn.uone.bean.entity.business.rpt.RptRevenueHistoryEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 营收确认明显表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-18
 */
public interface RptRevenueHistoryDao extends BaseMapper<RptRevenueHistoryEntity> {

    /**
     * 查询分页合同id
     *
     */
    IPage<String> selectContractByMap(Page page, @Param("year") String year,@Param("projectId") String projectId,@Param("keyWord") String keyWord);

    /**
     * 查询不分页合同id
     *
     */
    List<String> selectContractByMap(@Param("year") String year, @Param("projectId") String projectId, @Param("keyWord") String keyWord);


    RptRevenueHistoryEntity getSum(@Param("year") String year, @Param("projectId") String projectId, @Param("keyWord") String keyWord);

}
