package cn.uone.business.bil.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.application.enumerate.order.TransferModeEnum;
import cn.uone.application.enumerate.order.TransferResultEnum;
import cn.uone.application.enumerate.order.TransferTypeEnum;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilTransferEntity;
import cn.uone.bean.entity.business.bil.vo.BilOrderSearchVo;
import cn.uone.bean.entity.business.biz.BizAccountEntity;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.bil.service.IBilRefundConfirmService;
import cn.uone.business.bil.service.IBilTransferService;
import cn.uone.business.biz.service.IBizAccountService;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/bil/refund/confirm")
public class BilRefundConfirmController extends BaseController {

    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IBilRefundConfirmService bilRefundConfirmService;
    @Autowired
    private IBilTransferService bilTransferService;
    @Autowired
    private IBizAccountService bizAccountService;
    @Autowired
    private IBizReleaseService bizReleaseService;
    @Autowired
    private IApprovalCommitService approvalCommitService;

    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, BilOrderSearchVo bilOrderSearchVo) {
        RestResponse response = new RestResponse();
        String codeIds = approvalCommitService.getOrderCodeIds(Arrays.asList(ApprovalTypeEnum.ORDERCONFIRMREFUND.getValue()));
        bilOrderSearchVo.setIds(Arrays.asList(codeIds.split(",")));
        return response.setSuccess(true).setData(bilRefundConfirmService.findByCondition(page, bilOrderSearchVo));
    }


    /**
     * 确认跑批转账给业主账户
     */
    @RequestMapping("/transferModeForJob")
    public RestResponse transferModeForJob(@RequestParam List<String> ids) {
        RestResponse response = new RestResponse();
        try {
            for (String id : ids) {
                BilOrderEntity entity = bilOrderService.getById(id);
                entity.setTransferMode(TransferModeEnum.JOB2RENTER.getValue());
                bilOrderService.updateById(entity);
                bilTransferService.saveRefundTransfer(entity, TransferTypeEnum.CCB_REFUND);
            }
            response.setSuccess(true).setMessage("操作成功！");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public RestResponse save(BizAccountEntity account, String orderId, String releaseId) {
        RestResponse response = new RestResponse();
        try {
            BilOrderEntity order = bilOrderService.getById(orderId);
            QueryWrapper<BilTransferEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("transfer_type", TransferTypeEnum.CCB_REFUND.getValue());
            queryWrapper.eq("order_id", orderId);
            BilTransferEntity transfer = bilTransferService.getOne(queryWrapper);
            if (ObjectUtil.isNotNull(transfer) && !transfer.getResult().equals(TransferResultEnum.RESULTFAIL.getValue())) {
                response.setSuccess(false).setMessage("需要转账失败才能修改银行账号！");
            } else {
                //添加新的账号
                bizAccountService.save(account);
                //更新当前退款关联的账号
                BizReleaseEntity release = bizReleaseService.getById(releaseId);
                release.setAccountId(account.getId());
                bizReleaseService.updateById(release);
                //删除转账记录
                bilTransferService.removeById(transfer);
                //修改账单状态
                order.setTransferMode(TransferModeEnum.UNCONFIRMED.getValue());
                bilOrderService.updateById(order);
                response.setSuccess(true).setMessage("操作成功！");
            }
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }
}
