package cn.uone.business.kingdee.dao;

import cn.uone.bean.entity.business.kingdee.KingdeeTransferEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeTransferVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Repository
public interface KingdeeTransferDao extends BaseMapper<KingdeeTransferEntity> {
    IPage<KingdeeTransferEntity> selectKingdeeTransferByMap(Page page, @Param("map") Map<String, Object> map, DataScope dataScope);
    KingdeeTransferVo selectVoById(@Param("id") String id);
}
