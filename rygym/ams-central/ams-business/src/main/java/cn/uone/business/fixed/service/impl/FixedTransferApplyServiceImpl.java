package cn.uone.business.fixed.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.fixed.FixedLifeLogEntity;
import cn.uone.bean.entity.business.fixed.FixedPropertyEntity;
import cn.uone.bean.entity.business.fixed.FixedTransferApplyEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.business.fixed.dao.FixedTransferApplyDao;
import cn.uone.business.fixed.service.IFixedLifeLogService;
import cn.uone.business.fixed.service.IFixedPropertyService;
import cn.uone.business.fixed.service.IFixedTransferApplyService;
import cn.uone.web.base.RestResponse;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 申请审核单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Service
public class FixedTransferApplyServiceImpl extends ServiceImpl<FixedTransferApplyDao, FixedTransferApplyEntity> implements IFixedTransferApplyService {
    @Resource
    private IFixedPropertyService iFixedPropertyService;
    @Resource
    private IFixedLifeLogService iFixedLifeLogService;

    @Override
    public IPage<FixedTransferApplyEntity> page(Page page, FixedTransferApplyEntity entity) {
        QueryWrapper<FixedTransferApplyEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(entity.getStateList())) {
            queryWrapper.in("t_fixed_transfer_apply.state", entity.getStateList());
        }
        if (ObjectUtil.isNotEmpty(entity.getState())) {
            queryWrapper.eq("t_fixed_transfer_apply.state", entity.getState());
        }
        if (ObjectUtil.isNotEmpty(entity.getExecuteMan())) {
            queryWrapper.like("t_fixed_transfer_apply.execute_man", entity.getExecuteMan());
        }
        if (ObjectUtil.isNotEmpty(entity.getCode())) {
            queryWrapper.like("t_fixed_transfer_apply.code", entity.getCode());
        }
        if (ObjectUtil.isNotEmpty(entity.getItemName())) {
            queryWrapper.like("t_fixed_transfer_apply.item_name", entity.getItemName());
        }
        if (ObjectUtil.isNotEmpty(entity.getApplicant())) {
            queryWrapper.like("t_fixed_transfer_apply.applicant", entity.getApplicant());
        }
        if (ObjectUtil.isNotEmpty(entity.getApplicantAddress())) {
            queryWrapper.like("t_fixed_transfer_apply.applicant_address", entity.getApplicantAddress());
        }
        if (ObjectUtil.isNotEmpty(entity.getMobile())) {
            queryWrapper.like("t_fixed_transfer_apply.mobile", entity.getMobile());
        }
        if (ObjectUtil.isNotEmpty(entity.getTitle())) {
            queryWrapper.like("t_fixed_transfer_apply.title", entity.getTitle());
        }
        if (ObjectUtil.isNotEmpty(entity.getType())) {
            queryWrapper.eq("t_fixed_transfer_apply.type", entity.getType());
        }
        if (ObjectUtil.isNotEmpty(entity.getAuditorId())) {
            queryWrapper.eq("t_fixed_transfer_apply.auditor_id", entity.getAuditorId());
        }
        if (ObjectUtil.isNotEmpty(entity.getCreateBy())) {
            queryWrapper.eq("t_fixed_transfer_apply.create_by", entity.getCreateBy());
        }
        queryWrapper.orderByDesc("t_fixed_transfer_apply.apply_time");
        IPage iPage = baseMapper.selectPage(page, queryWrapper);

        return iPage;
    }

    @Override
    @Transactional
    public RestResponse pass(List<String> ids) {
        QueryWrapper<FixedTransferApplyEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("t_fixed_transfer_apply.id", ids);
        List<FixedTransferApplyEntity> list = list(queryWrapper);

        List<FixedTransferApplyEntity> editList = new ArrayList<>();
        List<FixedPropertyEntity> editFixedList = new ArrayList<>();
        List<FixedLifeLogEntity> addLogList = new ArrayList<>();

        List<String> fixedIds = list.stream().map(FixedTransferApplyEntity::getFixedPropertyId).collect(Collectors.toList());
        QueryWrapper<FixedPropertyEntity> queryWrapper2 = new QueryWrapper<>();
        queryWrapper.in("t_fixed_property.id", fixedIds);
        Map<String, FixedPropertyEntity> mapFixds = iFixedPropertyService.list(queryWrapper2).stream().collect(Collectors.toMap(FixedPropertyEntity::getId, Function.identity(), (key1, key2) -> key2));
        for (FixedTransferApplyEntity applyEntity : list) {
            applyEntity.setAuditorId(applyEntity.getNextAuditorId());
            if (ObjectUtil.isEmpty(applyEntity.getNextAuditorId())) {
                applyEntity.setFlowNode("流程结束");
                applyEntity.setState("2");//(0待审核1审核中 2 审核通过 3作废 )
            } else {
                //流程未结束
                applyEntity.setAuditorId(applyEntity.getNextAuditorId());
                applyEntity.setAuditor(applyEntity.getNextAuditor());
                applyEntity.setFlowNode("流程_2");
            }
            editList.add(applyEntity);

            FixedPropertyEntity fixedProperty = mapFixds.get(applyEntity.getFixedPropertyId());
            if (fixedProperty == null) {
                return RestResponse.failure("资料不存在");
            }
            if (fixedProperty.getPropertySta().contains("中")) {
                return RestResponse.success(applyEntity.getItemName() + "存在资产正在" + fixedProperty.getPropertySta() + "请等待执行完毕");
            }
            FixedPropertyEntity editFixe = new FixedPropertyEntity();
            editFixe.setId(applyEntity.getFixedPropertyId());
            editFixe.setPropertySta(applyEntity.getType() + "中");
            if (ObjectUtil.isNotEmpty(applyEntity.getAddressId())) {
                editFixe.setAddressId(applyEntity.getAddressId());
                editFixe.setAddress(applyEntity.getApplicantAddress());
            }
            //执行人
            if (ObjectUtil.isNotEmpty(applyEntity.getExecuteManId())) {
                editFixe.setUserId(applyEntity.getExecuteManId());
                editFixe.setUserName(applyEntity.getExecuteMan());
            }
            //申请人
            if (ObjectUtil.isNotEmpty(applyEntity.getApplicantId())) {
                editFixe.setUserId(applyEntity.getApplicantId());
                editFixe.setUserName(applyEntity.getApplicant());
            }
            //转移才更换使用部门
            if (applyEntity.getType().contains("转移")) {
                editFixe.setDeptId(applyEntity.getAddressId());
                editFixe.setDeptName(applyEntity.getApplicantAddress());
            }

            editFixedList.add(editFixe);

            //生命周期
            FixedLifeLogEntity fixedLifeLog = new FixedLifeLogEntity();
            fixedLifeLog.setName(fixedProperty.getPropertySta());
            fixedLifeLog.setType(applyEntity.getType());
            fixedLifeLog.setSourceId(editFixe.getId());
            fixedLifeLog.setRemark(fixedProperty.getPropertyName() + "资产 等待 " + applyEntity.getApplicant() + " 执行 " + applyEntity.getType() + "。");
            addLogList.add(fixedLifeLog);
        }
        if (!editList.isEmpty()) {
            updateBatchById(editList);
            iFixedPropertyService.updateBatchById(editFixedList);
            iFixedLifeLogService.saveBatch(addLogList);
            return RestResponse.success();
        } else {
            return RestResponse.failure("失败");
        }

    }

    /**
     * @param entity fixedPropertyList 放入{[{id:xx,propertyName:资产名称}]},可修改资产信息
     * @return 提交审批
     */
    @Override
    @Transactional
    public RestResponse addSave(FixedTransferApplyEntity entity,List<FixedPropertyEntity> fixed) {

        //如需审批流再做修改
//        entity.setAuditor(entity.getExecuteMan());//当前审核人
//        entity.setAuditorId(entity.getExecuteManId());//当前审核人
        entity.setFlowNode("流程开始");
        //        entity.setNextAuditorId(entity.getAuditorId());//下一个审核人
        if (!fixed.isEmpty()) {
            List<String> propertyIds = fixed.stream().map(FixedPropertyEntity::getId).collect(Collectors.toList());
            QueryWrapper<FixedPropertyEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", propertyIds);
//            List<FixedPropertyEntity> propertyEntityList = iFixedPropertyService.list(queryWrapper);
            Map<String, FixedPropertyEntity> mapFixds = iFixedPropertyService.list(queryWrapper).stream().collect(Collectors.toMap(FixedPropertyEntity::getId, Function.identity(), (key1, key2) -> key2));
            List<FixedTransferApplyEntity> listAdd = new ArrayList<>();
            for (FixedPropertyEntity fixedProperty : fixed) {
                fixedProperty.setPropertySta(entity.getType());
                FixedTransferApplyEntity entityAdd = new FixedTransferApplyEntity();
                BeanUtil.copyProperties(entity, entityAdd);
                entityAdd.setFixedPropertyId(fixedProperty.getId());
                if(ObjectUtil.isNotEmpty(fixedProperty.getPropertyName())){
                    entityAdd.setItemName(fixedProperty.getPropertyName());
                }else  if(mapFixds.get(fixedProperty.getId())!=null){
                    entityAdd.setItemName(mapFixds.get(fixedProperty.getId()).getPropertyName()+"["+mapFixds.get(fixedProperty.getPropertyCode())+"]");
                } else {
                    //未关联资产
                }
                fixedProperty.setPropertyName(null);
                listAdd.add(entityAdd);
            }
//            iFixedPropertyService.updateBatchById(fixed);
            saveOrUpdateBatch(listAdd);
            return RestResponse.success();
        } else if (ObjectUtil.isNotEmpty(entity.getFixedPropertyId())) {
            if (save(entity)) {
//                FixedPropertyEntity fixedProperty = new FixedPropertyEntity();
//                fixedProperty.setId(entity.getFixedPropertyId());
//                if("转移".equals(entity.getType())){
//                    fixedProperty.setPropertySta("转移中");
//                    iFixedPropertyService.updateById(fixedProperty);
//                } else {
//                    fixedProperty.setPropertySta(entity.getType()+"中");
//                    iFixedPropertyService.updateById(fixedProperty);
//                }

                return RestResponse.success();
            } else {
                return RestResponse.failure("失败");
            }
        }
        return RestResponse.failure("失败,资产不能为空");
    }


}
