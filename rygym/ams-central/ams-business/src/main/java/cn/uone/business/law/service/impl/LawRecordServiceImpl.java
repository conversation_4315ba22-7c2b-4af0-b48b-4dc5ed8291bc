package cn.uone.business.law.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.law.LawRecordEntity;
import cn.uone.business.law.dao.LawRecordDao;
import cn.uone.business.law.service.ILawRecordService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@Service
public class LawRecordServiceImpl extends ServiceImpl<LawRecordDao, LawRecordEntity> implements ILawRecordService {

    @Override
    public IPage<LawRecordEntity> queryPage(Page page, LawRecordEntity lawRecord) {
        QueryWrapper<LawRecordEntity> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(lawRecord.getState())) {
            queryWrapper.eq("t_law_record.state", lawRecord.getState());
        }
        if (ObjectUtil.isNotEmpty(lawRecord.getContractCode())) {
            queryWrapper.like("t_law_record.contract_code", lawRecord.getContractCode());
        }
        if (ObjectUtil.isNotEmpty(lawRecord.getFloor())) {
            queryWrapper.like("t_law_record.floor", lawRecord.getFloor());
        }
        if (ObjectUtil.isNotEmpty(lawRecord.getTenantry())) {
            queryWrapper.like("t_law_record.tenantry", lawRecord.getTenantry());
        }
        if (ObjectUtil.isNotEmpty(lawRecord.getStartDate())) {
            queryWrapper.ge("t_law_record.law_date", lawRecord.getStartDate());
        }
        if (ObjectUtil.isNotEmpty(lawRecord.getEndDate())) {
            queryWrapper.le("t_law_record.law_date", lawRecord.getEndDate());
        }
        queryWrapper.orderByDesc("t_law_record.law_date");

        return page(page, queryWrapper);
    }
}
