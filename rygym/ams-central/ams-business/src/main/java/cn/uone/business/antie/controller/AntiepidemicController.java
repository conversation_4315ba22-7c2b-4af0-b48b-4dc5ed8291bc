package cn.uone.business.antie.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.application.enumerate.DataFromEnum;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.InvoiceTypeEnum;
import cn.uone.application.enumerate.order.*;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalDetailEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.business.bil.*;
import cn.uone.bean.entity.business.bil.vo.*;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractInfoEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.cont.vo.ContContractVo;
import cn.uone.bean.entity.business.res.ResProjectCompanyEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.Guomi.service.IGuomiService;
import cn.uone.business.apro.dao.ApprovalCommitDao;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalDetailService;
import cn.uone.business.bil.dao.BilOrderDao;
import cn.uone.business.bil.service.*;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.bpm.service.IBpmWorkflowService;
import cn.uone.business.cont.dao.ContContractSourceRelDao;
import cn.uone.business.cont.service.*;
import cn.uone.business.kingdee.service.IKingdeeApiService;
import cn.uone.business.res.service.IResProjectCompanyService;
import cn.uone.business.res.service.IResProjectParaService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.crm.ISysParaFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.fegin.tpi.IFileFeign;
import cn.uone.fegin.tpi.IWechatFegin;
import cn.uone.fegin.tpi.IXybpmFegin;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.CacheParam;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@RestController
@RequestMapping("/antie/antiepidemic")
@Slf4j
public class AntiepidemicController extends BaseController {

    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page) {
        RestResponse response = new RestResponse();
        IPage<Map<String,Object>> data = new Page<>();
        BigDecimal total= new BigDecimal(8);
        String[] names = {"张三","赵有为","李国祥","王玉龙","林丽艳","刘德","高莉莉","张小天"};
        String[] tels = {"18056367892","13098876557","18965889763","18033677823","13098876528","13905732776","18096635621","16067655478"};
        String[] stimes = {"2021-03-28","2021-08-15","2021-07-29","2022-01-12","2022-02-03","2021-12-12","2022-02-26","2022-03-06"};
        String[] etimes = {"2021-04-05","2021-11-14","2021-08-02","2022-01-19","2022-02-11","2022-01-05","2022-03-02","2022-03-10"};
        List<Map<String,Object>> list = Lists.newArrayList();
        for(int i=0;i<8;i++){
            Map<String,Object> map = Maps.newHashMap();
            map.put("name",names[i]);
            map.put("tel",tels[i]);
            map.put("company","长春一汽分公司"+(new Random().nextInt(2)+1));
            map.put("type","出差");
            map.put("address","长春一汽公司总部");
            map.put("stime",stimes[i]);
            map.put("etime",etimes[i]);
            list.add(map);
        }
        data.setRecords(list);
        data.setCurrent(1);
        data.setSize(10);
        data.setTotal(8l);
        data.setPages(1);
        return response.setSuccess(true).setData(data).setMessage(total.toPlainString());  //前端页面显示总金额，试过和setAny()中，layui识别不到，只能放在message中便于传递
    }

}
