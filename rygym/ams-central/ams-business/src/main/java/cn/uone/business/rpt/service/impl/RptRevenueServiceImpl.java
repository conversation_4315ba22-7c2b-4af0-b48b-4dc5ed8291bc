package cn.uone.business.rpt.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Month;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.contract.ContractStateEnum;
import cn.uone.application.enumerate.order.OrderItemTypeEnum;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.rpt.RptRevenueEntity;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueDataVo;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueSearchVo;
import cn.uone.bean.entity.business.rpt.vo.RptRevenueVo;
import cn.uone.business.bil.dao.BilOrderItemDao;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.rpt.dao.RptRevenueDao;
import cn.uone.business.rpt.service.IRptRevenueService;
import cn.uone.business.util.DateUtils;
import cn.uone.web.util.DateTimeUtil;
import cn.uone.web.util.SafeCompute;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 营收确认明显表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-16
 */
@Service
public class RptRevenueServiceImpl extends ServiceImpl<RptRevenueDao, RptRevenueEntity> implements IRptRevenueService {

    @Autowired
    private BilOrderItemDao bilOrderItemDao;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    private IContContractSourceRelService contContractSourceRelService;

    @Override
    public IPage<String> selectContractIdsByMap(Page page, RptRevenueSearchVo vo) {
        getStrDate(vo);
        return baseMapper.selectContractIdsByMap(page, vo);
    }

    private void getStrDate(RptRevenueSearchVo vo) {
        if (StrUtil.isNotBlank(vo.getYear())) {
            String startMon = "" + vo.getStartMonth();
            String endMon = "" + vo.getEndMonth();
            if (vo.getStartMonth() < 10) {
                startMon = "0" + vo.getStartMonth();
            }
            if (vo.getEndMonth() < 10) {
                endMon = "0" + vo.getEndMonth();
            }
            vo.setContractStartDate(DateUtil.parse(vo.getYear() + startMon + "01"));
            vo.setContractEndDate(DateUtil.endOfMonth(DateUtil.parse(vo.getYear() + endMon + "01")));
        }
    }

    @Override
    public List<RptRevenueVo> findByCondition(RptRevenueSearchVo vo) {
        getStrDate(vo);
        //        rptRevenueVos.forEach(item->{
//            String contractCode = item.getContractCode();
//            String orderType = item.getOrderType();
//            String orderItemType = item.getOrderItemType();
//            Date startDate = item.getStartDate();
//            Date endDate = item.getEndDate();
//
//            QueryWrapper<ContContractEntity> queryWrapper = new QueryWrapper();
//            queryWrapper.eq("contract_code",contractCode);
//            ContContractEntity contractEntity = contContractService.getOne(queryWrapper);
//
//            QueryWrapper<BilOrderEntity> orderWrapper = new QueryWrapper();
//            queryWrapper.eq("contract_id",contractEntity.getId());
//            queryWrapper.eq("order_type",orderType);
//            BilOrderEntity orderEntity = iBilOrderService.getOne(orderWrapper);
//
//            QueryWrapper<BilOrderItemEntity> orderItemWrapper = new QueryWrapper();
//            queryWrapper.eq("order_id",orderEntity.getId());
//            queryWrapper.eq("order_item_type",orderItemType);
//            queryWrapper.between("start_time",startDate,endDate);
//            BilOrderItemEntity orderItemEntity = iBilOrderItemService.getOne(orderItemWrapper);
//        });
        return baseMapper.selectRevenueByMap(vo);
    }

    @Override
    public RptRevenueVo selectRevenueTotalByMap(RptRevenueSearchVo vo) {
        return baseMapper.selectRevenueTotalByMap(vo);
    }

    //生成收款记录
    @Override
    public void batchPut(RptRevenueSearchVo vo) {
        vo.setIsExist(BaseConstants.BOOLEAN_OF_TRUE);
        vo.setContractId(null);
        List<ContContractSourceRelEntity> csList = baseMapper.getContractSources(vo);
        if (CollectionUtil.isNotEmpty(csList)) {
            for (ContContractSourceRelEntity cs : csList) {
                RptRevenueEntity entity = new RptRevenueEntity();
                entity.setOrderType(vo.getOrderType());
                entity.setOrderItemType(vo.getOrderItemType());
                entity.setContractId(cs.getContractId());
                entity.setSourceId(cs.getSourceId());
                entity.setStartDate(cs.getContractStartDate());
                entity.setEndDate(cs.getContractEndDate());
                entity.setType("1");
                List<BilOrderItemEntity> collect = cs.getOrderItems().stream().filter(item -> (item.getPayment().compareTo(new BigDecimal(0))) > 0).collect(Collectors.toList());
                ContContractEntity contractEntity = contContractService.getById(cs.getContractId());
                Map<String, Map<Month, BigDecimal>> itemMap = getPutYearMon(collect,contractEntity);
                fillByMap(entity, itemMap);
            }
        }
    }

    //生成冲减记录
    @Override
    public void batchDeduct(RptRevenueSearchVo vo) {
        //获取需要冲减的合同(查询退租信息)
        List<BizReleaseEntity> releases = baseMapper.getNeedDeductRelease(vo.getOrderType(), vo.getOrderItemType());
        for (BizReleaseEntity release : releases) {
            String contractId = release.getContractId();
            vo.setContractId(contractId);
            vo.setIsExist(BaseConstants.BOOLEAN_OF_FALSE);
            //实际退租时间(审批完成时间)
            Date realLeaveDate = release.getUpdateDate();
            //租客提出退租时间
            Date checkoutDate = release.getCheckoutDate();

            ContContractEntity contract = contContractService.getOne(new QueryWrapper<ContContractEntity>().eq("id", release.getContractId()));
            //合同起始日期
            Date contractStartDate = contract.getStartDate();
            //获取租金记录
            List<ContContractSourceRelEntity> csList = baseMapper.getContractSources(vo);
            for (ContContractSourceRelEntity cs : csList) {
                //合同起租时间

                RptRevenueEntity entity = new RptRevenueEntity();
                entity.setContractId(cs.getContractId());
                entity.setSourceId(cs.getSourceId());
                entity.setOrderType(vo.getOrderType());
                entity.setOrderItemType(vo.getOrderItemType());
                entity.setType("2");
                //获取合同退房退款账单
                Map<String, Object> map = new HashMap<>();
                map.put("contractId", contractId);
                map.put("orderTypes", Arrays.asList(OrderTypeEnum.CHECKOUTSHOU.getValue(), OrderTypeEnum.ZHUANZUFEE.getValue(), OrderTypeEnum.HUANFANGFEE.getValue(), OrderTypeEnum.CHECKOUTTUI.getValue()));
                map.put("orderItemType", vo.getOrderItemType());
                //map.put("payStates", Arrays.asList(PayStateEnum.NOPAY.getValue(), PayStateEnum.PAYCONFIR.getValue(), PayStateEnum.REFUNDPENDING.getValue(), PayStateEnum.REFUNDED.getValue()));
                Map<String, Object> paymentInfo = bilOrderItemDao.getPaymentInfo(map);
                if (ObjectUtil.isNotNull(paymentInfo)) {
                    BigDecimal CheckoutPayment = (BigDecimal) paymentInfo.get("payment");
                    map.put("payState", PayStateEnum.CANCEL.getValue());
                    if (ObjectUtil.isNotNull(bilOrderItemDao.getPaymentInfo(map))) {
                        paymentInfo.put("payment", BigDecimal.ZERO);
                    }
                    if (/*CheckoutPayment.compareTo(BigDecimal.ZERO) == 0 ||*/ObjectUtil.isEmpty(paymentInfo.get("startTime")) || ObjectUtil.isEmpty(paymentInfo.get("endDate"))) {
                        if (ObjectUtil.isNull(release.getCheckoutDate())) {
                            continue;
                        }
                        paymentInfo.put("startTime", DateUtil.format(release.getCheckoutDate(), "yyyy-MM-dd"));
                        paymentInfo.put("endDate", DateUtil.format(release.getCheckoutDate(), "yyyy-MM-dd"));
                    }

                    Map<String, Map<Month, BigDecimal>> itemMap = Maps.newHashMap();
                    DateTime startCheckoutTime = DateUtil.parse(String.valueOf(paymentInfo.get("startTime")), "yyyy-MM-dd");
                    DateTime endCheckoutTime = DateUtil.parse(String.valueOf(paymentInfo.get("endDate")), "yyyy-MM-dd");


                    //获取revenue的数据
                    List<BilOrderItemEntity> items = cs.getOrderItems().stream().filter(item->item.getStartTime()!=null).collect(Collectors.toList());
                    //shareItems(items);
                    //提前退租
                    if (release.getCheckoutDate()!=null && release.getCheckoutDate().before(cs.getContractEndDate())) {
                        entity.setStartDate(endCheckoutTime);
                        //实际退租与申请退租月份差
                        int monthDiff = DateUtils.getDifMonth(checkoutDate, realLeaveDate);
                        itemMap = getEarlierRent(items, Math.abs(monthDiff), realLeaveDate, checkoutDate, contractStartDate);
                    } else {
                        entity.setStartDate(startCheckoutTime);
                        itemMap = getNormalRent(items, paymentInfo);
                    }
//                    BigDecimal usedPayment = getUsedPayment(vo);
//                    if (CheckoutPayment.compareTo(BigDecimal.ZERO) > 0) {
//                        endCheckoutTime=DateUtil.offsetDay(endCheckoutTime,1);
//                        entity.setStartDate(endCheckoutTime);
//                        itemMap = getLessDeduct(items, paymentInfo, usedPayment);
//                    } else {
//                        entity.setStartDate(startCheckoutTime);
//                        itemMap = getOverDeduct(items, paymentInfo, usedPayment);
//                    }
                    entity.setEndDate(realLeaveDate);

                    fillByMap(entity, itemMap);
                }
            }
        }

    }

    //正常摊销
    private Map<String, Map<Month, BigDecimal>> getNormalRent(List<BilOrderItemEntity> items, Map<String, Object> paymentInfo) {
        Map<String, Map<Month, BigDecimal>> result = Maps.newHashMap();
        Map<Month, BigDecimal> allMap = Maps.newHashMap();
        BigDecimal unRent = BigDecimal.ZERO;
        BigDecimal all = BigDecimal.ZERO;
        for (BilOrderItemEntity item : items) {
            item.setPayment(BigDecimal.ZERO);
            assembleMap(result, item.getStartTime(), item.getPayment().negate());
            all = all.add(item.getPayment().negate());
        }
        allMap.put(Month.JANUARY, unRent.add(all));
        result.put("ALL", allMap);
        return result;
    }

    //退租日期与审批日期有差距
    private Map<String, Map<Month, BigDecimal>> getEarlierRent(List<BilOrderItemEntity> items, int i, Date realLeaveDate, Date checkoutDate, Date contractStartDate) {
        Map<String, Map<Month, BigDecimal>> result = Maps.newHashMap();
        Map<Month, BigDecimal> allMap = Maps.newHashMap();
        BigDecimal unRent = BigDecimal.ZERO;
        BigDecimal all = BigDecimal.ZERO;
        BigDecimal resultPay = BigDecimal.ZERO;
        if (i == 0) {
            //不足月,当月审批完成
            //申请退租时间-合同签订时间<合同所在当月天数
            BigDecimal realRentDay = BigDecimal.valueOf(DateUtil.between(checkoutDate, contractStartDate, DateUnit.DAY));
            BigDecimal dayOfCurrentMonth = BigDecimal.valueOf(DateTimeUtil.getDaysOfMonth(checkoutDate));
            BigDecimal payment = items.get(0).getPayment();
            if (realRentDay.compareTo(dayOfCurrentMonth) < 0) {
                BigDecimal between = BigDecimal.valueOf((DateUtil.between(contractStartDate, checkoutDate, DateUnit.DAY) + 1));
                BigDecimal realPay = payment.multiply(between).divide(dayOfCurrentMonth, 2, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
                resultPay = payment.subtract(realPay);
            }else{
                Date currentDate = DateUtil.beginOfMonth(checkoutDate).toJdkDate();
                BigDecimal between = BigDecimal.valueOf((DateUtil.between(currentDate, checkoutDate, DateUnit.DAY) + 1));
                BigDecimal realPay = payment.multiply(between).divide(dayOfCurrentMonth, 2, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
                resultPay = payment.subtract(realPay);
            }
        } else if (i > 0) {
            //不足月,非当月审批完成
            //如果申请退租时间-合同签订时间<合同所在当月天数
            BigDecimal realRentDay = BigDecimal.valueOf(DateUtil.between(checkoutDate, contractStartDate, DateUnit.DAY));
            BigDecimal dayOfCurrentMonth = BigDecimal.valueOf(DateTimeUtil.getDaysOfMonth(contractStartDate));
            if (realRentDay.compareTo(dayOfCurrentMonth) < 0) {
                BigDecimal payment = items.get(0).getPayment();
                BigDecimal lastPayment = items.get(items.size() - 1).getPayment();
                BigDecimal realPay = payment.multiply(realRentDay).divide(dayOfCurrentMonth, 2, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal extraPay = items.get(0).getPayment().subtract(realPay);
                int difMonth = DateUtils.getDifMonth(checkoutDate, realLeaveDate);
                resultPay = lastPayment.multiply(BigDecimal.valueOf(difMonth)).add(extraPay);
            } else {
                //不足月,但是下一个月不足月 >
                //申请时间/申请时间所在月份*当月payment
                //实际退租时间-申请退租时间 * payment + 冲销
                BigDecimal lastPayment = items.get(items.size() - 1).getPayment();
                BigDecimal currentMonthDay = BigDecimal.valueOf(DateTimeUtil.getDaysOfMonth(checkoutDate));
                BigDecimal dayOfMonth = BigDecimal.valueOf(DateUtil.dayOfMonth(checkoutDate));
                BigDecimal extraPayment = lastPayment.subtract(lastPayment.multiply(dayOfMonth).divide(currentMonthDay, 2, BigDecimal.ROUND_HALF_UP));
                BigDecimal extraMonth = BigDecimal.valueOf(DateUtils.getDifMonth(checkoutDate, realLeaveDate));
                if (extraMonth.compareTo(BigDecimal.ZERO) == 0) {
                    resultPay = extraPayment;
                } else {
                    resultPay = lastPayment.multiply(extraMonth).add(extraPayment);
                }
            }
        }
        String realLeaveDateStr = DateTimeUtil.formatDateTimetoString(realLeaveDate, "yyyy-MM");
        //设置除了实际退租月其他月份冲减金额
//        for (int n = 0; n < items.size() - 1; n++) {
//            if (items.get(n).getStartTime() != null) {
//                String dateStr = DateTimeUtil.formatDateTimetoString(items.get(n).getStartTime(), "yyyy-MM");
//                if (realLeaveDateStr.equals(dateStr)) {
//                    items.get(n).setPayment(resultPay);
//                }else{
//                    items.get(n).setPayment(BigDecimal.ZERO);
//                }
//            }
//        }
        for (BilOrderItemEntity item : items) {
            //设置除了实际退租月其他月份冲减金额
            String dateStr = DateTimeUtil.formatDateTimetoString(item.getStartTime(), "yyyy-MM");
            if(realLeaveDateStr.equals(dateStr)){
                item.setPayment(resultPay.abs());
            }else{
                item.setPayment(BigDecimal.ZERO);
            }
            assembleMap(result, item.getStartTime(), item.getPayment().negate());
            all = all.add(item.getPayment().negate());
        }
        allMap.put(Month.JANUARY, unRent.add(all));
        result.put("ALL", allMap);
        return result;
    }

    private Map<String, Map<Month, BigDecimal>> getPutYearMon(List<BilOrderItemEntity> items, ContContractEntity contractEntity) {
        Map<String, Map<Month, BigDecimal>> result = Maps.newHashMap();
        BigDecimal all = BigDecimal.ZERO;
        //分摊
        //shareItems(items);
        int yearMonth = 0;
        String state = contractEntity.getState();
        Date signDate = contractEntity.getSignDate();
        if(signDate!=null){
           yearMonth  = DateUtils.getYearMonth(signDate);
        }
        for (BilOrderItemEntity item : items) {
            int yearMonth1 = DateUtils.getYearMonth(item.getEndTime());
            if(ContractStateEnum.STATUS_CHECKOUT.getValue().equals(state)||
               ContractStateEnum.STATUS_TERMINATION.getValue().equals(state)){
                if(yearMonth!=0 && yearMonth1>yearMonth){
                    item.setPayment(BigDecimal.ZERO);
                }
            }

            //给每个月进行赋值
            assembleMap(result, item.getStartTime(), item.getPayment());
            //获取总合同价
            all = all.add(item.getPayment());
        }
        Map<Month, BigDecimal> allMap = Maps.newHashMap();
        allMap.put(Month.JANUARY, all);
        result.put("ALL", allMap);
        return result;
    }

    private void shareItems(List<BilOrderItemEntity> items) {
        BigDecimal total = BigDecimal.ZERO;
        int itemNum = items.size();
        int shareNum = items.size();
        //查询出来的数据按照时间进行排序了，所以取时间最前面一个
        BilOrderItemEntity first = items.get(0);
        BilOrderItemEntity last = items.get(itemNum - 1);
        BigDecimal firstDays = new BigDecimal(DateUtil.betweenDay(first.getStartTime(), first.getEndTime(), true) + 1);
        BigDecimal fullFirstDays = new BigDecimal(DateUtil.betweenDay(DateUtil.beginOfMonth(first.getStartTime()), first.getEndTime(), true) + 1);
        //头尾存在不满一个月的
        if (first.getStartTime().compareTo(DateUtil.beginOfMonth(first.getStartTime())) != 0 ||
                last.getEndTime().compareTo(DateUtil.endOfMonth(last.getEndTime())) != 0) {
            shareNum--;
        }
        for (BilOrderItemEntity item : items) {
            //获取退租单、收款单总额
            total = total.add(item.getPayment());
        }
        BigDecimal avg = SafeCompute.div(total, new BigDecimal(shareNum), 2);
        BigDecimal firstPayment = BigDecimal.ZERO;
        for (int i = 0; i < itemNum; i++) {
            BilOrderItemEntity item = items.get(i);
            //不存在头尾不满一个月
            if (shareNum == itemNum) {
                item.setPayment(avg);
            } else {
                if (i == 0) {
                    firstPayment = SafeCompute.multiply(avg, SafeCompute.div(firstDays, fullFirstDays, 6)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    item.setPayment(firstPayment);
                } else if (i == itemNum - 1) {
                    item.setPayment(SafeCompute.sub(SafeCompute.sub(total, firstPayment), SafeCompute.multiply(avg, new BigDecimal(itemNum - 2))));
                } else {
                    item.setPayment(avg);
                }
            }
        }
    }

    //少付租金
    private Map<String, Map<Month, BigDecimal>> getLessDeduct(List<BilOrderItemEntity> items, Map<String, Object> paymentInfo, BigDecimal usedPayment) {
        Map<String, Map<Month, BigDecimal>> result = Maps.newHashMap();
        DateTime endCheckoutTime = DateUtil.parse(String.valueOf(paymentInfo.get("endDate")), "yyyy-MM-dd");
        BigDecimal CheckoutPayment = (BigDecimal) paymentInfo.get("payment");
        BigDecimal all = BigDecimal.ZERO;
        BigDecimal temp = BigDecimal.ZERO;
        BigDecimal unRent;//退房月 未使用钱
        for (BilOrderItemEntity item : items) {
            //子账单开始时间大于退房租金结束
            if (item.getStartTime().compareTo(endCheckoutTime) > 0) {
                assembleMap(result, item.getStartTime(), item.getPayment().negate());
                all = all.add(item.getPayment().negate());
            }
            //分摊使用金额
            if (item.getStartTime().compareTo(endCheckoutTime) <= 0) {
                temp = temp.add(item.getPayment());
            }
        }
        unRent = temp.subtract(usedPayment.add(CheckoutPayment)).negate();
        if (unRent.compareTo(BigDecimal.ZERO) != 0) {
            assembleMap(result, endCheckoutTime, unRent);
        }

        Map<Month, BigDecimal> allMap = Maps.newHashMap();
        allMap.put(Month.JANUARY, unRent.add(all));
        result.put("ALL", allMap);
        return result;
    }

    //多付租金
    private Map<String, Map<Month, BigDecimal>> getOverDeduct(List<BilOrderItemEntity> items, Map<String, Object> paymentInfo, BigDecimal usedPayment) {
        Map<String, Map<Month, BigDecimal>> result = Maps.newHashMap();
        DateTime startCheckoutTime = DateUtil.parse(String.valueOf(paymentInfo.get("startTime")), "yyyy-MM-dd");
        BigDecimal CheckoutPayment = (BigDecimal) paymentInfo.get("payment");
        BigDecimal all = BigDecimal.ZERO;
        BigDecimal temp = BigDecimal.ZERO;
        BigDecimal unRent;//退房月 未使用钱
        for (BilOrderItemEntity item : items) {
            //子账单开始时间大于退房租金开始
            if (item.getStartTime().compareTo(startCheckoutTime) > 0) {
                assembleMap(result, item.getStartTime(), item.getPayment().negate());
                all = all.add(item.getPayment().negate());
            }
            //分摊使用金额
            if (item.getStartTime().compareTo(startCheckoutTime) <= 0) {
                temp = temp.add(item.getPayment());
            }
        }
        unRent = usedPayment.add(CheckoutPayment).subtract(temp);
        if (unRent.compareTo(BigDecimal.ZERO) != 0) {
            assembleMap(result, startCheckoutTime, unRent);
        }

        Map<Month, BigDecimal> allMap = Maps.newHashMap();
        allMap.put(Month.JANUARY, unRent.add(all));
        result.put("ALL", allMap);
        return result;
    }

    /***
     * 获取已使用金额
     * @param vo
     * @return
     */
    private BigDecimal getUsedPayment(RptRevenueSearchVo vo) {
        Map<String, Object> map = new HashMap<>();
        map.put("contractId", vo.getContractId());
        map.put("orderType", vo.getOrderType());
        map.put("orderItemType", vo.getOrderItemType());
        map.put("payStates", Arrays.asList(PayStateEnum.PAYCONFIR.getValue(), PayStateEnum.REFUNDED.getValue()));
        Map<String, Object> paymentInfo = bilOrderItemDao.getPaymentInfo(map);
        return ObjectUtil.isNotEmpty(paymentInfo) ? (BigDecimal) paymentInfo.get("payment") : BigDecimal.ZERO;
    }

    //组装map
    private void assembleMap(Map<String, Map<Month, BigDecimal>> result, Date time, BigDecimal payment) {
        String year = DateUtil.year(time) + "";
        Map<Month, BigDecimal> checkoutMap = result.get(year) != null ? result.get(year) : Maps.newHashMap();
        checkoutMap.put(DateUtil.monthEnum(time), payment);
        result.put(year, checkoutMap);
    }

    //填充记录
    private void fillByMap(RptRevenueEntity entity, Map<String, Map<Month, BigDecimal>> itemMap) {
        BigDecimal all = itemMap.get("ALL").get(Month.JANUARY);
        entity.setTotal(all);
        for (String year : itemMap.keySet()) {
            if (!"ALL".equals(year)) {
                entity.setId(null);
                Map<Month, BigDecimal> map = itemMap.get(year);
                entity.setReportYear(year);
                entity.setJan(map.get(Month.JANUARY));
                entity.setFeb(map.get(Month.FEBRUARY));
                entity.setMar(map.get(Month.MARCH));
                entity.setApr(map.get(Month.APRIL));
                entity.setMay(map.get(Month.MAY));
                entity.setJun(map.get(Month.JUNE));
                entity.setJul(map.get(Month.JULY));
                entity.setAug(map.get(Month.AUGUST));
                entity.setSept(map.get(Month.SEPTEMBER));
                entity.setOct(map.get(Month.OCTOBER));
                entity.setNov(map.get(Month.NOVEMBER));
                entity.setDece(map.get(Month.DECEMBER));
                entity.insert();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importData(List<RptRevenueDataVo> data) {
        StringBuffer sb = new StringBuffer();
        if (CollectionUtils.isEmpty(data)) {
            return sb.append("读取excel异常").toString();
        }
        int index = 2;
        for (RptRevenueDataVo vo : data) {
            index++;
            String contractCode = vo.getContractCode();
            if (StrUtil.isBlank(contractCode)) {
                sb.append("第" + index + "行合同编号不能为空！<br>");
                continue;
            }
//            if(StrUtil.isBlank(vo.getPartitionName())){
//                sb.append("第" + index + "行区域名称不能为空！<br>");
//                continue;
//            }
//            if(StrUtil.isBlank(vo.getSourceCode())){
//                sb.append("第" + index + "行房间编号不能为空！<br>");
//                continue;
//            }
            if (StrUtil.isBlank(vo.getReportYear())) {
                sb.append("第" + index + "行报表年份不能为空！<br>");
                continue;
            }
            if (StrUtil.isBlank(vo.getType())) {
                sb.append("第" + index + "行是否冲减不能为空！<br>");
                continue;
            }
            if (StrUtil.isBlank(vo.getOrderItemType())) {
                sb.append("第" + index + "行收费项目不能为空！<br>");
                continue;
            }


            //通过合同编号，查找合同
            ContContractEntity contract = contContractService.getOne(new QueryWrapper<ContContractEntity>().eq("contract_code", contractCode));
            if (ObjectUtil.isNull(contract)) {
                sb.append("第" + index + "行合同编号对应的合同不存在！<br>");
                continue;
            }
            vo.setContractId(contract.getId());
            vo.setStartDate(contract.getStartDate());
            vo.setEndDate(contract.getEndDate());

            //查找对应的房源id
            //String sourceId = contContractSourceRelService.getSourceId(contract.getId(),vo.getPartitionName(),vo.getSourceCode());
            List<ContContractSourceRelEntity> css = contContractSourceRelService.getListByContractId(contract.getId());
            if (CollectionUtils.isEmpty(css)) {
                sb.append("第" + index + "行合同编号查找不到对应房源！<br>");
                continue;
            }
            if (css.size() >= 2) {
                sb.append("第" + index + "行合同编号存在多个房源！<br>");
                continue;
            }
            String sourceId = css.get(0).getSourceId();
            if (StrUtil.isEmpty(sourceId)) {
                sb.append("第" + index + "行合同编号,区域名称，房间编号对应的房间不存在！<br>");
                continue;
            }
            vo.setSourceId(sourceId);
            if ("是".equals(vo.getType())) {
                vo.setType("2");
            } else {
                vo.setType("1");
            }
            if ("租金".equals(vo.getOrderItemType())) {
                vo.setOrderType(OrderTypeEnum.RENT.getValue());
                vo.setOrderItemType(OrderItemTypeEnum.RENT.getValue());
            } else {
                vo.setOrderType(OrderTypeEnum.FIXED.getValue());
                vo.setOrderItemType(OrderItemTypeEnum.SYNTHESIZE_BASEFEE.getValue());
            }

            //删除已存在的数据
            QueryWrapper<RptRevenueEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("contract_id", vo.getContractId()).eq("source_id", vo.getSourceId())
                    .eq("type", vo.getType()).eq("report_year", vo.getReportYear())
                    .eq("order_type", vo.getOrderType())
                    .eq("order_item_type", vo.getOrderItemType());
            this.remove(wrapper);
            RptRevenueEntity d = new RptRevenueEntity();
            BeanUtils.copyProperties(vo, d);
            if (StrUtil.isNotBlank(vo.getJan())) {
                d.setJan(new BigDecimal(vo.getJan()));
            }
            if (StrUtil.isNotBlank(vo.getFeb())) {
                d.setFeb(new BigDecimal(vo.getFeb()));
            }
            if (StrUtil.isNotBlank(vo.getMar())) {
                d.setMar(new BigDecimal(vo.getMar()));
            }
            if (StrUtil.isNotBlank(vo.getApr())) {
                d.setApr(new BigDecimal(vo.getApr()));
            }
            if (StrUtil.isNotBlank(vo.getMay())) {
                d.setMay(new BigDecimal(vo.getMay()));
            }
            if (StrUtil.isNotBlank(vo.getJun())) {
                d.setJun(new BigDecimal(vo.getJun()));
            }
            if (StrUtil.isNotBlank(vo.getJul())) {
                d.setJul(new BigDecimal(vo.getJul()));
            }
            if (StrUtil.isNotBlank(vo.getAug())) {
                d.setAug(new BigDecimal(vo.getAug()));
            }
            if (StrUtil.isNotBlank(vo.getSept())) {
                d.setSept(new BigDecimal(vo.getSept()));
            }
            if (StrUtil.isNotBlank(vo.getOct())) {
                d.setOct(new BigDecimal(vo.getOct()));
            }
            if (StrUtil.isNotBlank(vo.getNov())) {
                d.setNov(new BigDecimal(vo.getNov()));
            }
            if (StrUtil.isNotBlank(vo.getDece())) {
                d.setDece(new BigDecimal(vo.getDece()));
            }
            if (StrUtil.isNotBlank(vo.getTotal())) {
                d.setTotal(new BigDecimal(vo.getTotal()));
            }
            this.save(d);
        }
        return sb.toString();
    }

    @Override
    public List<RptRevenueEntity> selectRevenueForKingdee(String year, String projectId) {
        return baseMapper.selectRevenueForKingdee(year, projectId);
    }

    @Override
    public List<RptRevenueEntity> getOrderType() {
        return baseMapper.getOrderType();
    }

}
