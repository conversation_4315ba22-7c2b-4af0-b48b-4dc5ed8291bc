package cn.uone.business.investment.service.impl;

import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.investment.InvestmentPlanEntity;
import cn.uone.bean.entity.business.investment.vo.InvestmentPlanVo;
import cn.uone.business.investment.dao.InvestmentPlanDao;
import cn.uone.business.investment.service.IInvestmentPlanService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.FileUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 招商计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Service
public class InvestmentPlanServiceImpl extends ServiceImpl<InvestmentPlanDao, InvestmentPlanEntity> implements IInvestmentPlanService {

    @Autowired
    InvestmentPlanDao investmentPlanDao;

    @Autowired
    private ISysFileService fileService;

    @Autowired
    private IUserFegin userFegin;

    @Override
    public IPage<InvestmentPlanVo> getPageByParams(Page page, Map<String, Object> map) {
        DataScope scope = new DataScope(UoneSysUser.id());
        scope.setProAlias("s");
        IPage<InvestmentPlanVo> iPage = baseMapper.getPageByParams(page,scope,map);
        for(InvestmentPlanVo planEntity:iPage.getRecords()){
            planEntity.setAttachmentPath(FileUtil.getPath(planEntity.getPlanAttachment()));
        }
        return iPage;
    }

    @Override
    public List<InvestmentPlanVo> getTaskList(Map<String, Object> map) {
        DataScope scope = new DataScope(UoneSysUser.id());
        scope.setProAlias("s");
        return baseMapper.getPageByParams(scope,map);
    }

    @Override
    @Transactional
    public boolean addOrUpdate(InvestmentPlanEntity investmentPlan, List<MultipartFile> annexFiles) {
        try {
            this.saveOrUpdate(investmentPlan);
            String investmentPlanId = investmentPlan.getId();
            if(annexFiles != null && annexFiles.size() > 0){
                String fileName = annexFiles.get(0).getOriginalFilename();
                if(fileName != null && !fileName.equals("")){//解决修改时上传文件丢失的问题,主要判断修改时是否真的上传文件,文件名为空代表可能没有上传文件,只有上一次上传的文件
                    fileService.delFileByFromIdAndType(investmentPlanId,SysFileTypeEnum.INVESTMENT_PLAN);
                    fileService.saveFiles(annexFiles,investmentPlanId, SysFileTypeEnum.INVESTMENT_PLAN.getValue());
                }
            }
            return true;
        } catch (Exception e){
            return false;
        }
    }
}
