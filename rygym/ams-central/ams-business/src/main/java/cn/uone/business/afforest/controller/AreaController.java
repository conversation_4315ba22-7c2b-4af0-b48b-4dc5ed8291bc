package cn.uone.business.afforest.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.afforest.AreaEntity;
import cn.uone.business.afforest.service.IAreaService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 物料类别表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@RestController
@RequestMapping("/afforest/area")
public class AreaController extends BaseController {

    @Autowired
    private IAreaService service;

    @GetMapping("/page")
    public RestResponse page(Page<AreaEntity> page, AreaEntity entity){
        QueryWrapper<AreaEntity> wrapper = new QueryWrapper<>();

//        wrapper.eq("project_id",UoneHeaderUtil.getProjectId());
        if(StrUtil.isNotBlank(entity.getArea())){
            wrapper.like("area","%"+entity.getArea()+"%");
        }
        if(StrUtil.isNotBlank(entity.getScope())){
            wrapper.like("scope","%"+entity.getScope()+"%");
        }
        if(StrUtil.isNotBlank(entity.getPrincipal())){
            wrapper.like("principal","%"+entity.getPrincipal()+"%");
        }
        IPage<AreaEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/save")
    public RestResponse save(AreaEntity entity){
        entity.insertOrUpdate();
        return RestResponse.success();
    }



    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }



}
