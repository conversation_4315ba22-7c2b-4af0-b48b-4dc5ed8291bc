package cn.uone.business.biz.service;

import cn.uone.application.enumerate.source.PriceTypeEnum;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.biz.vo.BizReleaseSearchVo;
import cn.uone.bean.entity.business.biz.vo.BizReleaseVo;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-03
 */
public interface IBizReleaseService extends IService<BizReleaseEntity> {

    RestResponse addRelease(BizReleaseEntity apply, boolean isManagerUserApply) throws Exception;

    RestResponse addRelease(List<BizReleaseEntity> apply, boolean isManagerUserApply) throws Exception;

    IPage<BizReleaseVo> findByCondition(Page page, BizReleaseSearchVo bizReleaseSearchVo);

    IPage<BizReleaseVo> findCheckHouseByCondition(Page page, BizReleaseSearchVo bizReleaseSearchVo);

    BizReleaseEntity getByContractId(String id);

    BizReleaseEntity getByNewContractId(String id);

    BizReleaseEntity getByNewContractIdIncludeCancel(String id);

    void initFineDeposit(String releaseId);

    void handleRelease(BizReleaseEntity release) throws Exception;

    void cancelOrder(BizReleaseEntity release ,String sourceId) throws Exception;

    void forceRelease(BizReleaseEntity release);

    void roomRelease(BizReleaseEntity apply) throws Exception;

    Map<String, Object> calculate(Map<String, Object> par) throws Exception;

    Map<String, Object> calculateForMove(Map<String, Object> par) throws Exception;

    Map<String, Object> calculateRent(Map<String, Object> par) throws Exception;

    Map<String, Object> calculateWater(Map<String, Object> par) throws Exception;

    Map<String, Object> calculateElec(Map<String, Object> par) throws Exception;

    Map<String, Object> calculateWaterPb(Map<String, Object> par) throws Exception;

    Map<String, Object> calculateElecPb(Map<String, Object> par) throws Exception;

    Map<String,Object> calculateFixed(Map<String,Object> par,PriceTypeEnum type) throws Exception;

    List<BizReleaseEntity> queryInSettle(Map<String, Object> par);

    /**
     *  处理旧合同业务
     * @param oldContract
     */
    void handleContract(ContContractEntity oldContract,boolean isChangeRoom) throws Exception;

    RestResponse organizeRelease(List<ContContractEntity> contractList) throws Exception;

    /**
     * 到期前发送通知
     *
     * @return
     */
    RestResponse checkOutBeforeSendMsg();
}
