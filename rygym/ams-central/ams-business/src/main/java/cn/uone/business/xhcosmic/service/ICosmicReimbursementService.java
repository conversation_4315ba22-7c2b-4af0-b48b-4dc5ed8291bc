package cn.uone.business.xhcosmic.service;

import cn.uone.bean.entity.business.bil.BilCarryOverEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicIncomeEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicIncomeItemEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicReimbursementEntity;
import cn.uone.bean.entity.business.xhcosmic.CosmicReimbursementItemEntity;
import cn.uone.bean.entity.business.xhcosmic.vo.ReimbursementSearchVo;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 金蝶(星瀚)报账工单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface ICosmicReimbursementService extends IService<CosmicReimbursementEntity> {


    /**
     * 批量保存报账工单与明细
     *
     * @param entities
     */
    void batchInsertWithItem(List<CosmicReimbursementEntity> entities);

    /**
     * 按时间范围批量获取符合条件的订单并保存到内部数据库 并且 推送到金蝶(星瀚)系统
     * 该方法  = getListCanPushCosmicReimbursement + pushToCosmic
     *
     * @param startTime 开始时间范围
     * @param endTime   结束时间范围
     * @return
     */
    RestResponse cosmicIncomeBatchAdd(Date startTime, Date endTime);

    /**
     * 按时间范围批量获取符合条件的订单并保存到内部数据库 并且 推送到金蝶(星瀚)系统
     * 该方法  = getListCanPushCosmicReimbursement + pushToCosmic
     *
     * @param startTime 开始时间范围
     * @param endTime   结束时间范围
     * @param orderIds  订单ID列表，用于精确筛选特定订单
     * @return
     */
    RestResponse cosmicIncomeBatchAdd(Date startTime, Date endTime, List<String> orderIds);

    /**
     * 获取可以推送到金蝶报账工单的数据先存入我们的内部数据库
     *
     * @param startTime 查询 -开始时间
     * @param endTime   查询 结束时间
     * @return
     */
    List<CosmicReimbursementEntity> getListCanPushCosmicReimbursement(Date startTime, Date endTime);

    /**
     * 获取可以推送到金蝶报账工单的数据先存入我们的内部数据库
     *
     * @param startTime 查询 -开始时间
     * @param endTime   查询 结束时间
     * @param orderIds  订单ID列表，用于精确筛选特定订单
     * @return
     */
    List<CosmicReimbursementEntity> getListCanPushCosmicReimbursement(Date startTime, Date endTime, List<String> orderIds);

    /**
     * 批量推送到金蝶系统
     *
     * @param entities
     * @return
     */
    RestResponse pushToCosmic(List<CosmicReimbursementEntity> entities);

    /**
     * 批量推送到金蝶系统
     *
     * @param ids 报账工单数据表id数组
     * @return
     */
    RestResponse pushToCosmic(String... ids);


    IPage<CosmicReimbursementEntity> listForPage(Page<CosmicReimbursementEntity> page, ReimbursementSearchVo searchVo);


    IPage<CosmicReimbursementItemEntity> itemListById(Page<CosmicReimbursementItemEntity> page, String id);


    /**
     * 报账工单查询接口(金蝶(星瀚)系统报账工单)并反写凭证号
     *
     * @param sourceNumberList 订单号集合
     * @return
     */
    RestResponse cosmicReimbursementQueryAndRewrite(List<String> sourceNumberList, Date modifyTime, String sourceBillType);


}
