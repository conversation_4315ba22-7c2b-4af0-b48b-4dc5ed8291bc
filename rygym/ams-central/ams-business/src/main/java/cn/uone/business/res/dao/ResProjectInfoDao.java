package cn.uone.business.res.dao;

import cn.uone.bean.entity.business.res.ResProjectInfoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface ResProjectInfoDao extends BaseMapper<ResProjectInfoEntity> {

    // 查询项目信息表
    ResProjectInfoEntity getProjectInfo(@Param("map") Map<String, Object> map);

    ResProjectInfoEntity getProjectInfoBySourceId(@Param("sourceId") String sourceId);

}
