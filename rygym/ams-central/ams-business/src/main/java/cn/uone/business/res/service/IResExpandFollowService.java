package cn.uone.business.res.service;

import cn.uone.bean.entity.business.res.ResExpandFollowEntity;
import cn.uone.bean.entity.business.res.vo.ResExpandFollowVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-18
 */
public interface IResExpandFollowService extends IService<ResExpandFollowEntity> {

    IPage<ResExpandFollowVo> queryPage(String projectId, Page page);
}
