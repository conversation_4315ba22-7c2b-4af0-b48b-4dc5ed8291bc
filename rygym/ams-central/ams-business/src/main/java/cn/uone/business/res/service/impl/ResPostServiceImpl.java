package cn.uone.business.res.service.impl;

import cn.uone.bean.entity.business.res.ResPostEntity;
import cn.uone.business.res.dao.ResPostDao;
import cn.uone.business.res.service.IResPostService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 岗位表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Service
public class ResPostServiceImpl extends ServiceImpl<ResPostDao, ResPostEntity> implements IResPostService {

    @Override
    public ResPostEntity getPostList() {
        return baseMapper.getPostList();
    }
}
