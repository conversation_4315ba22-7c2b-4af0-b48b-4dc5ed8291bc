package cn.uone.business.res.dao;

import cn.uone.bean.entity.business.res.ResPlanPartitionEntity;
import cn.uone.bean.entity.business.res.vo.ResplanPartitionVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface ResPlanPartitionDao extends BaseMapper<ResPlanPartitionEntity> {
    IPage<ResplanPartitionVo> list(Page page, DataScope dataScope, @Param("map") Map<String, Object> map);

    List<ResPlanPartitionEntity> listShort(@Param("map") Map map);

    List<ResPlanPartitionEntity> queryIndexCount(DataScope dataScope, @Param("map") Map map);
}
