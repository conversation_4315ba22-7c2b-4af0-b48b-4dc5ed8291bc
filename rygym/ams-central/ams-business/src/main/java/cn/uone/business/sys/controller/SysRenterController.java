package cn.uone.business.sys.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.afforest.VegetationEntity;
import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.bean.parameter.RenterPo;
import cn.uone.business.base.service.IBaseEnterpriseService;
import cn.uone.business.biz.dao.BizAccountDao;
import cn.uone.business.cont.dao.ContContractDao;
import cn.uone.business.sys.dao.SysRenterDao;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/renter")
@Api("租客管理")
public class SysRenterController {

    @Autowired
    private SysRenterDao renterDao;
    @Autowired
    private ContContractDao contractDao;
    @Autowired
    private BizAccountDao accountDao;
    @Autowired
    private IBaseEnterpriseService enterpriseService;
    @Autowired
    private ISysFileService fileService;
    @Autowired
    private IRenterFegin renterFegin;

    @ApiOperation("查询租客管理列表")
    @RequestMapping(value = "/page", method = RequestMethod.GET)
    public RestResponse page(Page page, RenterPo po) {
        return RestResponse.success().setData(renterDao.selectRenter(page, po));
    }

    @RequestMapping("/changeTel")
    public RestResponse changeTel(String singerId,String tel) throws Exception {
        RenterEntity entity = renterFegin.getByTel(tel);
        if(entity==null){
            RenterEntity renterEntity = renterFegin.getById(singerId);
            renterEntity.setTel(tel);
            renterFegin.update(renterEntity);
            return RestResponse.success();
        }else{
            return RestResponse.failure("手机号重复");
        }

    }

    @RequestMapping("/changeType")
    public RestResponse changeType(String singerId,String clientType) throws Exception {
        RenterEntity renterEntity = renterFegin.getById(singerId);
        if(renterEntity==null){
            return RestResponse.failure("修改失败");
        }else{
            renterEntity.setClientType(clientType);
            renterFegin.update(renterEntity);
            return RestResponse.success();
        }

    }

    @ApiOperation("查询租客管理详情")
    @RequestMapping(value = "/getRenterById", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse getRenterById(String id) {
        RenterPo po = new RenterPo();
        po.setRenterId(id);
        return RestResponse.success().setData(renterDao.selectRenter(po));
    }

    @ApiOperation("查询租客管理合同列表")
    @RequestMapping(value = "/getContracts", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse getContracts(String signerId) {
        return RestResponse.success().setData(renterDao.selectContractBySignerId(signerId));
    }


    @ApiOperation("查询资产管理租客列表")
    @RequestMapping(value = "/bossPage", method = RequestMethod.GET)
    @RequiresPermissions("renterManage")
    public RestResponse bossPage(Page page, RenterPo po) {
        return RestResponse.success().setData(renterDao.bossPage(page, po));
    }

    @ApiOperation("查询资产管理意向租客列表")
    @RequestMapping(value = "/intentedRenterPage", method = RequestMethod.GET)
    @RequiresPermissions("renterManage")
    public RestResponse intentedRenterPage(Page page, RenterPo po) {
        return RestResponse.success().setData(renterDao.getIntentedRenterList(page, po));
    }

    @ApiOperation("资产管理租客管理查看详情")
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public RestResponse detail(String id) {
        Map<String,Object> map = Maps.newHashMap();
        BaseEnterpriseEntity enterprise = enterpriseService.getByRenterId(id);
        enterprise = ObjectUtil.isNull(enterprise) ? new BaseEnterpriseEntity() : enterprise;
        map.put("invoice", enterprise);
        List<SysFileEntity> files=fileService.getListByFromIdAndType(id,SysFileTypeEnum.BUSINESS_LICENSE);
        map.put("files", files);
        return RestResponse.success().setData(map);
    }

    @RequestMapping(value = "/renterContractList", method = RequestMethod.GET)
    public RestResponse renterContractList(Page page,String id,String isOrganize,String state) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("signerId",id);
        map.put("isOrganize",isOrganize);
        map.put("state",state);
        return RestResponse.success().setData(contractDao.renterContractList(page,map));
    }

    @RequestMapping(value = "/renterCheckOutList", method = RequestMethod.GET)
    public RestResponse renterCheckOutList(Page page,String id,String isOrganize) {
        Map<String, Object> map = new HashMap<>();
        map.put("isDel", BaseConstants.BOOLEAN_OF_FALSE);
        map.put("renterId",id);
        return RestResponse.success().setData(accountDao.queryList(page,map));
    }

    @RequestMapping("/getListByProject")
    public RestResponse getListByProject(Page page, String keyWord, @RequestParam(value = "type", required = false) String type, @RequestParam(value = "projectId" ,required = false)String projectId) {
        RestResponse response = new RestResponse();
        Map<String, Object> map = Maps.newHashMap();
        if(StrUtil.isBlank(projectId)){
            projectId= UoneHeaderUtil.getProjectId();
        }
        if (StrUtil.isNotBlank(keyWord)) {
            map.put("keyWord", keyWord);
        }
        if (StrUtil.isNotBlank(type)) {
            map.put("type", type);
        }
        if (StrUtil.isNotBlank(projectId)){
            map.put("projectId",projectId);
        }
        return response.setSuccess(true).setData(renterDao.selectRenterByProject(page, map));
    }
}
