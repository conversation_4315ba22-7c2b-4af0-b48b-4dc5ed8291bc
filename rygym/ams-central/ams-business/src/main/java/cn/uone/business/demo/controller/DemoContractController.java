package cn.uone.business.demo.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.demo.DemoContractEntity;
import cn.uone.bean.entity.business.demo.DemoOwnerEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.business.demo.service.IDemoContractService;
import cn.uone.business.demo.service.IDemoOwnerService;
import cn.uone.business.demo.vo.ContractVo;
import cn.uone.business.demo.vo.NewContractVo;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-28
 */
@RestController
@RequestMapping("/demo-contract")
public class DemoContractController extends BaseController {
    @Autowired
    IDemoContractService demoContractService;
    @Autowired
    IResSourceService resSourceService;
    @Autowired
    IResProjectService resProjectService;
    @Autowired
    IDemoOwnerService ownerService;

    @Autowired
    ISysFileService sysFileService;
    @Autowired
    MinioUtil minioUtil;




    @RequestMapping("/queryPage")
    public RestResponse queryPage(int current,int size,String search) {
//        Map map=new HashMap<>();
//        map.put("contractCode",search);
//        map.put("name",search);
//        map.put("ownerName",search);
//        map.put("roomCode",search);
        String projectId= UoneHeaderUtil.getProjectId();
        Page page = new Page();
        page.setSize(size);
        page.setCurrent(current);
        IPage<ContractVo> pageList=new Page<>();
        pageList=demoContractService.queryPage(page,search,projectId);
//        if(StrUtil.isNotBlank(search)) {
//          pageList=demoContractService.search(page,search);
//        }else{pageList=demoContractService.queryPage(page);}
        return RestResponse.success().setData(pageList);
    }


    @RequestMapping("/query")
    public RestResponse getContract(String contractCode){
        DemoContractEntity contract=demoContractService.getContract(contractCode);
        try{
            contract.setRoomCode(resSourceService.getCodeById(contract.getSourceId()));
            contract.setProjectName(resProjectService.queryById(contract.getProjectId()).getName());
//        contract.setOwnerName(ownerService.getNameById()  getNameById(contract.getOwnerId()));
            contract.setOwnerName(ownerService.getById(contract.getOwnerId()).getOwnerName());
        }catch (Exception e){
            return RestResponse.failure("获取合同信息失败");
        }
        if(contract==null) {
            return RestResponse.failure("获取失败");
        }
        return new RestResponse().success().setData(contract);
    }



    @RequestMapping("/add")
    public RestResponse addContract(NewContractVo contract){
        try {
            System.out.println(contract.toString());
            demoContractService.insertContract (contract);
            return RestResponse.success("数据添加完成");
        } catch (Exception e){
            e.printStackTrace();
            return  RestResponse.failure("注册失败");
        }
    }

    @RequestMapping("/delete")
    public RestResponse deleteContract(String contractCode){
        demoContractService.deleteContract(contractCode);
            return  RestResponse.success("成功删除记录");
    }

    @RequestMapping("/queryOwnerList")
    public RestResponse queryOwnerList(){
        List<DemoOwnerEntity> list = demoContractService.queryOwnerList();
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/update")
    public RestResponse changeState(String contractCode,String state){
        int num=demoContractService.changeState(contractCode,state);
        if(num==0){
            return RestResponse.failure("变更失败");
        }
        return RestResponse.success("成功");
    }

    @RequestMapping("/uploadFiles")
    public RestResponse uploadFiles(String fromId, String filerImgs, @RequestParam(required = false) List<String> delImgs, @RequestParam (required = false) List<MultipartFile> files){
            if (ObjectUtil.isNotNull(files) && !files.isEmpty()) {
            String[] filers = filerImgs.split(",");
            saveFiles(fromId, files, filers);
        }
        if (ObjectUtil.isNotNull(delImgs) && !delImgs.isEmpty()) {
            delFiles(delImgs);
        }
        return  RestResponse.success("保存成功");
    }

    private void saveFiles(String id, List<MultipartFile> files, String[] filers) {
        for (MultipartFile file : files) {
            if (file.getSize() > 0l && !Arrays.asList(filers).contains(file.getOriginalFilename())) {
                //String url = FileUtil.save(file);
                String url = minioUtil.save(file);
                SysFileEntity entity = new SysFileEntity();
                entity.setFromId(id);
                entity.setType("");
                entity.setSize(file.getSize());
                entity.setName(file.getOriginalFilename());
                entity.setUrl(url);
                sysFileService.save(entity);
            }
        }
    }

    private void delFiles(List<String> delImgs) {
        for (String img : delImgs) {
            SysFileEntity file = sysFileService.getById(img);
            //String isTrue = FileUtil.delete(file.getUrl());
            minioUtil.delete(file.getUrl());
            sysFileService.removeById(img);
        }
    }

}
