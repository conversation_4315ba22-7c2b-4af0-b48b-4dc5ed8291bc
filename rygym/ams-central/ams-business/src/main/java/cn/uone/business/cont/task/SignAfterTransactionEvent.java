package cn.uone.business.cont.task;

import cn.uone.application.enumerate.contract.RecordTypeEnum;
import org.springframework.context.ApplicationEvent;

public class SignAfterTransactionEvent extends ApplicationEvent {

    private String contractId;

    private RecordTypeEnum type;



    public SignAfterTransactionEvent(Object source, String contractId,RecordTypeEnum type) {
        super(source);
        this.type = type;
        this.contractId = contractId;
    }

    public String getContractId() {
        return contractId;
    }

    public  RecordTypeEnum getType(){
        return type;
    }
}
