package cn.uone.business.demo.dao;

import cn.uone.bean.entity.business.demo.StudentEntity;
import cn.uone.business.demo.vo.StudentVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-21
 */

@Repository
public interface StudentDao extends BaseMapper<StudentEntity> {

    StudentVo findStuScore(@Param("stuId")String stuId,@Param("cname") String cname);

    Integer updateStudent(@Param("stuId")String stuId,@Param("studName")String stuName);

    @Select("select * from demo_student where stu_id=#{stuId}")
    StudentEntity find(String stuId);
}
