package cn.uone.business.apro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.application.enumerate.ApprovalTemplateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.bean.entity.business.apro.ApprovalProjectParaEntity;
import cn.uone.bean.entity.business.apro.Expression;
import cn.uone.bean.entity.business.cont.ContDesignContractEntity;
import cn.uone.business.apro.service.IApprovalCommitService;
import cn.uone.business.apro.service.IApprovalProjectParaService;
import cn.uone.business.apro.service.Operation;
import cn.uone.business.cont.service.IContDesignContractService;
import cn.uone.util.wechat.ApprovalStateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 优惠券
 */
@Service
public class ContractPaytion implements Operation {

    @Resource
    private IContDesignContractService contDesignContractService ;
    @Resource
    private IApprovalCommitService approvalCommitService ;
    @Resource
    private IApprovalProjectParaService projectParaService;

    @Override
    public ApprovalCommitEntity apply(Expression expression) {
        ContDesignContractEntity entity=contDesignContractService.getById(expression.getCodeId());
        ApprovalProjectParaEntity temp=projectParaService.get(entity.getProjectId(),ApprovalTemplateEnum.CONTRACTPAY.getType(),true);
        ApprovalCommitEntity commitEntity=null;
        if(ObjectUtil.isNotNull(entity)){
            commitEntity=new ApprovalCommitEntity();
            ApprovalStateUtil.initCommit(commitEntity,expression.getCodeId());
            commitEntity.setPrice(expression.getPrice());
            commitEntity.setApprovalNum(entity.getCode());
            commitEntity.setTemplateid(temp.getApprovalTempId());
            commitEntity.setType(ApprovalTypeEnum.CONTRACTPAY.getValue());
            commitEntity.setTableName(ApprovalTypeEnum.DESIGNCONTRACT.getTable());
            commitEntity.setTitle(ApprovalTypeEnum.CONTRACTPAY.getName());
            commitEntity.setTitle1("合同编号:"+entity.getCode());
            commitEntity.setTitle2("合同名称:"+entity.getName());
            commitEntity.setTitle3("本次申请付款金额:" + expression.getPrice() + "元");
            approvalCommitService.save(commitEntity);
        }
        return commitEntity;
    }


}
