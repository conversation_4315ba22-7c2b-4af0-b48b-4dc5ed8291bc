package cn.uone.business.fixed.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.uone.bean.entity.business.fixed.FixedMaintenanceTeamEntity;
import cn.uone.bean.entity.business.fixed.FixedRfidEntity;
import cn.uone.business.fixed.dao.FixedMaintenanceTeamDao;
import cn.uone.business.fixed.service.IFixedMaintenanceTeamService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 维保班组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@Service
public class FixedMaintenanceTeamServiceImpl extends ServiceImpl<FixedMaintenanceTeamDao, FixedMaintenanceTeamEntity> implements IFixedMaintenanceTeamService {
    @Override
    public IPage<FixedMaintenanceTeamEntity> page(Page page, FixedMaintenanceTeamEntity entity) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("team",entity);
        IPage iPage = baseMapper.queryByPage(page,map);
        return iPage;
    }
}
