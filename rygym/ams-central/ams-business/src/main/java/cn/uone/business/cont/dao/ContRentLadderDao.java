package cn.uone.business.cont.dao;

import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.cont.vo.ContRentLadderVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Repository
public interface ContRentLadderDao extends BaseMapper<ContRentLadderEntity> {

    // 批量新增租金阶梯
    int batchAdd(List<ContRentLadderEntity> rentLadderEntityList);

    // 查询租金阶梯
    List<ContRentLadderVo> selectListById(@Param("id") String id);
}
