package cn.uone.business.flow.service;

import cn.uone.bean.entity.business.flow.SysExpressionEntity;
import cn.uone.bean.entity.business.flow.SysListenerEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 流程表达式 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
public interface ISysExpressionService extends IService<SysExpressionEntity> {
    IPage<SysExpressionEntity> page(Page page, SysExpressionEntity entity);

    List<SysExpressionEntity> list(SysExpressionEntity entity);
}
