package cn.uone.business.biz.service;

import cn.uone.bean.entity.business.biz.BizReservationEntity;
import cn.uone.bean.entity.business.biz.vo.BizReservationEntityVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IBizReservationService extends IService<BizReservationEntity> {

    IPage<BizReservationEntityVo> selectPageByMap(Page page, String state);
}
