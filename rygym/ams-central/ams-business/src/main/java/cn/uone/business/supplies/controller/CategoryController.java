package cn.uone.business.supplies.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.supplies.CategoryEntity;
import cn.uone.business.supplies.service.ICategoryService;
import cn.uone.business.supplies.service.IPurchaseApplyService;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 物料类别表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@RestController
@RequestMapping("/supplies/category")
public class CategoryController extends BaseController {

    @Autowired
    private ICategoryService service;
    @Autowired
    private IPurchaseApplyService purchaseApplyService;

    @GetMapping("/page")
    public RestResponse page(Page<CategoryEntity> page, CategoryEntity entity){
        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("create_date");
//        wrapper.eq("project_id",UoneHeaderUtil.getProjectId());
        if(StrUtil.isNotBlank(entity.getName())){
            wrapper.like("name","%"+entity.getName()+"%");
        }
        if(StrUtil.isNotBlank(entity.getType())){
            wrapper.like("type","%"+entity.getType()+"%");
        }
        if(StrUtil.isNotBlank(entity.getState())){
            wrapper.like("state","%"+entity.getState()+"%");
        }
        if(StrUtil.isNotBlank(entity.getId())){
            String[] ids =entity.getId().split(",");
            wrapper.in("id",ids);
        }
        IPage<CategoryEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

//    @GetMapping("/listBySourceId")
//    public RestResponse listBySourceId(String sourceId){
//        QueryWrapper<ResProjectCompanyEntity> wrapper = new QueryWrapper<>();
//        wrapper.exists("select 1 from t_res_source s where s.project_id = t_res_project_company.project_id and s.id = '"+sourceId+"'");
//        List<ResProjectCompanyEntity> list =  service.list(wrapper);
//        list.forEach(item->{
//            item.setValue(item.getId());
//        });
//        return RestResponse.success().setData(list);
//    }

    @PostMapping("/save")
    public RestResponse save(CategoryEntity entity){
        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("name",entity.getName());
        long count = service.count(wrapper);
        if(count>0){
            return RestResponse.failure("添加失败！该物料已存在！");
        }
        entity.setTotalQuantity(0);
        entity.setRecipientsQuantity(0);
        entity.setLendQuantity(0);
        entity.setNowQuantity(0);
        entity.setState("1");
        entity.insertOrUpdate();
        return RestResponse.success();
    }
    @PostMapping("/save2")
    public RestResponse save2(CategoryEntity entity){
        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("name",entity.getName());
        long count = service.count(wrapper);
        if(count>0){
            return RestResponse.failure("添加失败！该物料已存在！");
        }
        int totalQuantity = entity.getNowQuantity()+entity.getLendQuantity()+entity.getRecipientsQuantity();
        entity.setTotalQuantity(totalQuantity);
        entity.setState("1");
        entity.insertOrUpdate();
        return RestResponse.success();
    }


    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        for (String id:ids) {
            CategoryEntity categoryEntity = service.getById(id);
            if(categoryEntity.getNowQuantity()!=0){
                return RestResponse.failure("删除失败！只能删除库存量为0的数据！");
            }
            int number  = purchaseApplyService.getQuantityByState(id);
            if(number>0){
                return RestResponse.failure("删除失败！该物料正在采购审核中！");
            }
        }
        service.removeByIds(ids);
        return RestResponse.success();
    }
    //生效
    @PostMapping("/efficient")
    public RestResponse efficient(CategoryEntity entity){
        entity.setState("1");
        entity.insertOrUpdate();
        return RestResponse.success();
    }
    //失效
    @PostMapping("/loseEfficacy")
    public RestResponse loseEfficacy(CategoryEntity entity){
        entity.setState("0");
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    /*
       下拉菜单获取物料名称对应的库存数量
     */
    @RequestMapping("/selectQuantity")
    public RestResponse selectQuantity(String id) {
        Integer nowQuantity = 0;
        if(!StrUtil.isBlank(id)){
            CategoryEntity categoryEntity = service.getById(id);
             nowQuantity = categoryEntity.getNowQuantity();
        }

        return RestResponse.success().setData(nowQuantity);
    }
    /*
       下拉菜单获取物料名称
     */
    @RequestMapping("/selectItemName")
    public RestResponse selectCompany(CategoryEntity entity) {
        RestResponse response=new RestResponse();
        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();
        if(StrUtil.isNotBlank(entity.getId())){
            wrapper.eq("id",entity.getId());
        }
        wrapper.eq("state","1");
        List<CategoryEntity> categoryEntitys=service.list(wrapper);
        List<Map<String,Object>> data=new ArrayList<>();
        for(CategoryEntity categoryEntity:categoryEntitys){
            Map<String,Object> map=new HashMap<>();
            map.put("name",categoryEntity.getName());
            map.put("value",categoryEntity.getId());
            map.put("quota",categoryEntity.getQuota());
            data.add(map);
        }
        return response.setSuccess(true).setData(data);
    }

    @RequestMapping("/getApplyByUser")
    public RestResponse getApplyByUser(Page page, String userId) {
        RestResponse response = new RestResponse();
        userId = UoneSysUser.id();
        return response.setSuccess(true).setData(service.getApplyByUser(page, userId));
    }

    @PostMapping("/edit")
    public RestResponse edit(CategoryEntity entity){

        entity.insertOrUpdate();
        return RestResponse.success();
    }


}
