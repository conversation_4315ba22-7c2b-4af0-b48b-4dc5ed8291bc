package cn.uone.business.rpt.dao;

import cn.uone.bean.entity.business.report.vo.ConfirmAccountVo;
import cn.uone.bean.parameter.ConfirmAccountPo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Repository
public interface ReportConfirmAccountDao  extends BaseMapper<ConfirmAccountVo> {

    IPage<ConfirmAccountVo> findConfirmByCondition(Page page,@Param("searchPo") ConfirmAccountPo param);

    List<ConfirmAccountVo> findConfirmByCondition(@Param("searchPo") ConfirmAccountPo confirmAccountPo);

    BigDecimal getArriveTotal(@Param("searchPo") ConfirmAccountPo confirmAccountPo);

    IPage<ConfirmAccountVo> findCcbCondition(Page page, @Param("searchPo") ConfirmAccountPo confirmAccountPo);

    List<ConfirmAccountVo> findCcbCondition(@Param("searchPo") ConfirmAccountPo confirmAccountPo);

    BigDecimal getCcbTotal(@Param("searchPo") ConfirmAccountPo confirmAccountPo);

    IPage<ConfirmAccountVo> findUnionPayCondition(Page page, @Param("searchPo") ConfirmAccountPo confirmAccountPo);

    List<ConfirmAccountVo> findUnionPayCondition(@Param("searchPo") ConfirmAccountPo confirmAccountPo);

    BigDecimal getUnionPayTotal(@Param("searchPo") ConfirmAccountPo confirmAccountPo);

    Date getLatelyPayTime(@Param("accountType") String accountType, @Param("transferTime") String transferTime, @Param("transferPayment") String transferPayment, @Param("projectId") String projectId);

    Date getLatelyPayTimeCz(@Param("accountType") String accountType, @Param("transferTime") String transferTime, @Param("transferPayment") String transferPayment, @Param("projectId") String projectId);
}
