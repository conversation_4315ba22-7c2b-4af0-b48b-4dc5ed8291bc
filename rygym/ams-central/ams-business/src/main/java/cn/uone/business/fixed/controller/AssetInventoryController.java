package cn.uone.business.fixed.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.fixed.AssetInventoryEntity;
import cn.uone.bean.entity.business.fixed.vo.AssetInventoryVo;
import cn.uone.bean.entity.business.fixed.vo.FixedPropertyVo;
import cn.uone.business.fixed.service.IAssetInventoryDetailService;
import cn.uone.business.fixed.service.IAssetInventoryService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 固定资产盘点 前端控制器
 * 盘点主表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@RestController
@RequestMapping("/fixed/fixed-assetInventory-entity")
public class AssetInventoryController extends BaseController {

    @Autowired
    private IAssetInventoryService assetInventoryService;

    @Autowired
    private IAssetInventoryDetailService iAssetInventoryDetailService;

    /*@Autowired
    private IUserService userService;*/

    @GetMapping("/getPageList")
    public RestResponse getPageList(Page page, AssetInventoryEntity assetInventory) {
        IPage<AssetInventoryEntity> iPage = assetInventoryService.page(page, assetInventory);
        return RestResponse.success().setData(iPage);
    }


    @RequestMapping("/pageListByLogin")
    public RestResponse pageListByLogin(Page page, AssetInventoryEntity assetInventory) {
        Map<String, Object> map = Maps.newHashMap();
        //map.put("projectId", UoneHeaderUtil.getProjectId());
        map.put("projectId", assetInventory.getProjectId());
        map.put("inventoryCode", assetInventory.getInventoryCode());
        map.put("inventoryName", assetInventory.getInventoryName());
        map.put("inventoryState", assetInventory.getInventoryState());
        map.put("handleStatus", assetInventory.getHandleStatus());
        map.put("inventoryType", assetInventory.getInventoryType());
//        map.put("inventoryBy", "69f12f50f12a11e8b5bfb083feb0418b");
        map.put("inventoryBy", UoneSysUser.id());
        return RestResponse.success().setData(assetInventoryService.pageList(page, map));
    }

    @RequestMapping("pageList")
    public RestResponse pageList(Page page, AssetInventoryEntity assetInventory) {
        Map<String, Object> map = Maps.newHashMap();
        //map.put("projectId", UoneHeaderUtil.getProjectId());
        map.put("projectId", assetInventory.getProjectId());
        map.put("inventoryCode", assetInventory.getInventoryCode());
        map.put("inventoryName", assetInventory.getInventoryName());
        map.put("inventoryState", assetInventory.getInventoryState());
        map.put("handleStatus", assetInventory.getHandleStatus());
        map.put("inventoryType", assetInventory.getInventoryType());
        /*if (ObjectUtil.isNotNull(isShort)) {
            map.put("isShort", isShort);
        }*/
        return RestResponse.success().setData(assetInventoryService.pageList(page, map));
    }

    /**
     * 获取信息
     *
     * @param keyword
     * @param flag
     * @return 获取资产分类等列表
     */
    @GetMapping("/keyword")
    public RestResponse keywordList(Page page, @Param("keyword")String keyword,@Param("flag")String flag ) {
        return RestResponse.success().setData(assetInventoryService.keywordList(page, keyword,flag));
    }

    /**
     * 获取信息
     *
     * @param id 主键
     * @return 资产盘点管理列表
     */
    @GetMapping("/info")
    public RestResponse info(@Param(value = "id")String id) {
        AssetInventoryEntity info = assetInventoryService.getById(id);
        return RestResponse.success().setData(info);
    }

    /**
     * 获取盘点信息以及资产列表
     *
     * @param inventoryId 主键
     * @return 资产盘点管理列表
     */
    @GetMapping("/getInventory")
    public RestResponse getInventory(@Param(value = "inventoryId")String inventoryId) {
        AssetInventoryVo inventoryVo = assetInventoryService.getInventoryById(inventoryId);
        Map<String,Object> map = Maps.newHashMap();
        map.put("inventoryId",inventoryId);
        List<FixedPropertyVo> propertyList = iAssetInventoryDetailService.getPropertyList(map);
        inventoryVo.setPropertyList(propertyList);
        return RestResponse.success().setData(inventoryVo);
    }

    /**
     * 新增盘点任务
     *
     * @param
     * @return 资产盘点管理列表
     */
    @PostMapping("/save")
    public RestResponse save(AssetInventoryVo assetInventoryVo) throws Exception {
        if(assetInventoryService.saveInventory(assetInventoryVo)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 修改盘点任务
     *
     * @param
     * @return 资产盘点管理列表
     */
    @PostMapping("/update")
    public RestResponse update(AssetInventoryVo assetInventoryVo) throws Exception {
        if(assetInventoryService.updateInventory(assetInventoryVo)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 修改
     *
     * @param assetInventory 参数
     * @return 资产盘点管理列表
     */
    @PostMapping("/edit")
    public RestResponse edit(AssetInventoryEntity assetInventory) {
        if(assetInventoryService.updateById(assetInventory)){
            return RestResponse.success();
        }else {
            return RestResponse.failure("失败");
        }
    }

    /**
     * 删除
     * 物理删除盘点任务及盘点详情表
     * 单个盘点任务删除
     * @param ids
     * @return 资产盘点管理列表
     */
    @PostMapping("/del")
    public RestResponse del(@RequestBody List<String> ids) {
        //boolean res = true;
        try {
            boolean resInventory = assetInventoryService.removeByIds(ids);//删除盘点任务主表
            String inventoryId = ids.get(0);
            boolean resInventoryDetail = iAssetInventoryDetailService.deleteDetailByInventoryId(inventoryId);//删除盘点任务详情表
            if(resInventory && resInventoryDetail){
                return RestResponse.success();
            } else {
                return RestResponse.failure("失败");
            }
        } catch(Exception e){
            e.printStackTrace();
            return RestResponse.failure("失败");
        }
    }

    /*@RequestMapping("/importAssetInventory")
    public RestResponse importAssetInventory(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return RestResponse.failure("请选择上传文件");
        }
        try {
            List<AssetInventoryVo> rfidEntities  = ExcelDataUtil.importData(file.getInputStream(), assetInventoryVo.class);
            if(CollectionUtils.isEmpty(rfidEntities)){
                return RestResponse.failure("读取excel异常");
            }else {
                //查重
                List<String> codes = rfidEntities.stream().map(AssetInventoryVo::getCode).collect(Collectors.toList());
                QueryWrapper<AssetInventoryEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("t_fixed_rfid.code",codes);

                //单独String集合
                List<String> collect = codes.stream().filter(i -> !Objects.equals(i, ""))               // list 对应的 Stream 并过滤""
                        .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum)) // 获得元素出现频率的 Map，键为元素，值为元素出现的次数
                        .entrySet().stream()                       // 所有 entry 对应的 Stream
                        .filter(e -> e.getValue() > 1)         // 过滤出元素出现次数大于 1 (重复元素）的 entry
                        .map(Map.Entry::getKey)                // 获得 entry 的键（重复元素）对应的 Stream
                        .collect(Collectors.toList());
                if(!collect.isEmpty()&& collect.get(0)!=null){
                    return RestResponse.failure("标签编号:"+collect+"重复了");
                }

                List<AssetInventoryEntity> rfidEntityList = iAssetInventoryService.list(queryWrapper);
                if(!rfidEntityList.isEmpty()){
                    List<String> codes2 = rfidEntityList.stream().map(AssetInventoryEntity::getCode).collect(Collectors.toList());
                    return RestResponse.failure("标签编号:"+codes2+"=已存在");
                }
                int i =0;
                List<AssetInventoryEntity> list = new ArrayList<>();
                for(AssetInventoryVo vo :rfidEntities){
                    i++;
                    if(StringUtils.isEmpty(vo.getCode())){
                        return RestResponse.failure("第:"+i+"行,标签编号不能为空");
                    }
                    if(StringUtils.isEmpty(vo.getLabelSta())){
                        vo.setLabelSta("1");
                    }else {
                        //状态 1=未使用,2=已使用,3=报废
                        if("未使用".equals(vo.getLabelSta())){
                            vo.setLabelSta("1");
                        }else if("已使用".equals(vo.getLabelSta())){
                            vo.setLabelSta("2");
                        }else if("报废".equals(vo.getLabelSta())){
                            vo.setLabelSta("3");
                        }else {
                            vo.setLabelSta("1");
                        }
                    }
                    AssetInventoryEntity assetInventory = new AssetInventoryEntity();
                    BeanUtil.copyProperties(vo,assetInventory);
                    list.add(assetInventory);
                }
                iAssetInventoryService.saveBatch(list);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return RestResponse.failure("读取excel异常");
        }
        return RestResponse.success("导入成功");
    }*/

    @UoneLog("导出资产盘点模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        ExcelRender.me("/excel/import/assetInventory.xls").beans(beans).render(response);
    }


}
