# 付款单明细备注格式化需求文档

## 项目概述
在现有的付款申请系统中，需要为付款单明细添加格式化的备注信息。当前系统中的 `getOrderItemsWithOrderTypeFilter` 方法返回的 `allItems` 包含地址信息，需要将这些信息按照特定格式添加到付款单明细的备注中。

## 需求背景
- 当前系统：CosmicPayApplyServiceImpl 中的付款申请处理流程
- 数据来源：BilOrderItemWithOrderVo 对象中的 address 字段（通过 bilOrderVo.address 获取）
- 目标：生成格式化的备注信息用于付款单明细

## 功能需求

### 1. 备注格式规范
备注格式：`（退【滨海里2号及5号公寓**房间号**租户】【物业费】【2025-05-15】至【2025-05-31】）`

格式说明：
- `退` - 固定前缀，表示退款
- `【滨海里2号及5号公寓**房间号**租户】` - 地址信息，包含：
  - 项目名称：从 address 字段解析
  - 房间号：从 address 字段解析
  - 租户信息：从订单相关信息获取
- `【物业费】` - 费用类型，从 orderItemType 转换为中文名称
- `【2025-05-15】至【2025-05-31】` - 费用周期，从 startTime 和 endTime 获取

### 2. 数据字段映射
- **地址信息**：BilOrderItemWithOrderVo.bilOrderVo.address
- **费用类型**：BilOrderItemWithOrderVo.orderItemType（需要通过 OrderItemTypeEnum.getNameByValue() 转换）
- **开始时间**：BilOrderItemWithOrderVo.startTime
- **结束时间**：BilOrderItemWithOrderVo.endTime
- **租户信息**：BilOrderItemWithOrderVo.bilOrderVo.payer 或 singer

### 3. 技术实现要点
- 地址解析：需要从完整地址中提取项目名称和房间号
- 时间格式化：使用 yyyy-MM-dd 格式
- 费用类型转换：使用现有的 OrderItemTypeEnum 枚举
- 集成点：在 CosmicPayApplyServiceImpl 的付款申请处理流程中添加备注生成逻辑

## 技术约束
- 基于现有的 Spring Boot + MyBatis 架构
- 使用现有的枚举类进行类型转换
- 保持现有代码结构，最小化侵入性修改
- 确保备注信息的准确性和完整性

## 验收标准
1. 备注格式严格按照规范生成
2. 所有动态字段正确填充
3. 时间格式正确（yyyy-MM-dd）
4. 费用类型正确转换为中文名称
5. 地址信息正确解析和格式化
6. 集成到现有付款申请流程中无异常

## 风险评估
- 地址格式可能不统一，需要健壮的解析逻辑
- 时间字段可能为空，需要空值处理
- 费用类型可能存在未定义的值，需要默认处理

# 批量开票功能需求规格说明书 (PRD)

## 1. 概述

### 1.1 背景
当前系统已有单个订单开票功能，但缺少批量处理能力。财务和运营人员需要为多个订单同时开票的场景较为常见，目前只能逐个处理，效率低下。

### 1.2 目标
开发批量开票功能，允许用户选择多个订单并一次性发起开票请求，提高工作效率。

### 1.3 范围
1. 扩展现有的开票接口，支持批量操作
2. 基于现有的makeInvoice方法进行扩展
3. 保持与现有makeInvoice接口相同的安全和锁定机制
4. 提供批量操作结果的详细反馈

## 2. 功能需求

### 2.1 批量开票接口
- 接口路径: `/bil/order/batch-invoice`
- 请求方式: POST
- 请求参数: 订单ID列表 (List<String> ids)
- 响应数据: 包含成功和失败订单列表的结果对象

### 2.2 批量处理逻辑
- 接收多个订单ID，逐个调用现有的单个开票方法
- 每个订单处理应独立事务，一个订单失败不影响其他订单处理
- 为每个订单ID添加分布式锁，防止并发处理同一订单
- 跳过不符合开票条件的订单（如已开票、未支付等）
- 收集所有订单的处理结果，包括成功与失败的详情

### 2.3 结果反馈
- 返回批量处理的总体统计：总数、成功数、失败数
- 提供详细的失败原因，便于用户快速修正问题订单
- 记录每个订单的处理结果和时间

### 2.4 日志和监控
- 记录详细的操作日志，包括批量处理的开始和结束
- 记录每个订单处理的关键步骤和状态变更
- 记录处理异常，便于问题排查

## 3. 非功能需求

### 3.1 性能
- 批量处理应有合理的并发控制，避免系统过载
- 支持至少100个订单的一次性批量处理

### 3.2 安全
- 继承现有接口的权限控制和验证机制
- 确保敏感操作有日志记录

### 3.3 可维护性
- 代码结构清晰，便于维护和扩展
- 充分复用现有功能，减少重复代码

## 4. 技术实现

### 4.1 接口定义
- 在BilOrderController中增加新的批量开票接口
- 在ReportInvoiceService中增加对应的批量处理方法

### 4.2 关键实现点
- 基于现有makeInvoice方法，创建batchMakeInvoice方法
- 使用循环处理订单列表，为每个订单独立创建事务
- 使用try-catch块捕获每个订单处理中的异常，确保一个订单失败不影响整体处理
- 实现结果收集机制，记录每个订单的处理结果

### 4.3 安全机制
- 使用现有的分布式锁和状态检查机制，防止重复开票
- 对每个订单在处理前进行状态验证，跳过不符合条件的订单

## 5. 测试场景

### 5.1 功能测试
- 测试批量开票接口的基本功能
- 测试不同状态订单的混合处理（未开票、已开票、开票中等）
- 测试错误处理和异常恢复

### 5.2 性能测试
- 测试大批量订单处理的性能和稳定性
- 测试并发请求下的系统行为

### 5.3 集成测试
- 与现有开票流程的集成测试
- 与发票状态查询和更新功能的集成测试
