{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-pro", "maxTokens": 1200000, "temperature": 0.2}, "research": {"provider": "google", "modelId": "gemini-2.5-pro", "maxTokens": 1200000, "temperature": 0.2}, "fallback": {"provider": "openai", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 1200000, "temperature": 0.1, "baseURL": "https://www.dmxapi.cn/v1"}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********", "defaultTag": "master"}}