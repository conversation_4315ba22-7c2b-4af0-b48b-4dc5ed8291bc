package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum ReleaseDescribeEnum {

    HTDQ("0", "合同到期"),

    GZDD("1", "工作调动"),

    FZTG("2", "房租太贵"),

    TCSFG("3", "停车收费贵"),

    FWBMY("4", "服务不满意"),

    LJBMY("5", "邻居不满意"),

    WWHJBYH("6", "外围环境不友好"),

    FWZLWT("7", "房屋质量问题"),

    FXBSH("8", "房型不适合/配套不齐全"),

    ZBPTBZ("9", "周边配套不足"),

    LCBMY("11", "楼层不满意"),

    TZR("12", "增加或减少同住人"),

    QT("10", "其他");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    ReleaseDescribeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        ReleaseDescribeEnum[] enums = ReleaseDescribeEnum.values();
        for (ReleaseDescribeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ReleaseDescribeEnum enumerate : ReleaseDescribeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }
}
