package cn.uone.application.enumerate.bpm;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName XyWorkflowEnum
 * @Description 流程枚举
 * <AUTHOR>
 * @Date 2021/5/13 11:38
 * @Version 1.0
 */
public enum XyWorkflowEnum {

    //所有工作流程的module都是一样的
    INDIVIDUAL_CONTRACT(BpmConfig.individualContractBillName, BpmConfig.individualContractBosType, BpmConfig.individualContractModule),
    CHECKOUT(BpmConfig.checkoutBillName, BpmConfig.checkoutBosType, BpmConfig.checkoutModule),
    SUBLET(BpmConfig.subletBillName, BpmConfig.subletBosType, BpmConfig.subletModule),
    RENTCHANGE(BpmConfig.rentchangeBillName, BpmConfig.rentchangeBosType, BpmConfig.rentchangeModule),
    REFUND(BpmConfig.refundBillName, BpmConfig.refundBosType, BpmConfig.refundModule),
    CONTRACT_TEMPLATE(BpmConfig.contractTemplateBillName, BpmConfig.contractTemplateBosType, BpmConfig.contractTemplateModule);

    private static List<Map<String, Object>> list = new ArrayList<>();

    private String billname;
    private String bosType;
    private String module;

    XyWorkflowEnum(String billname, String bosType,String module) {
        this.billname = billname;
        this.bosType = bosType;
        this.module = module;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (XyWorkflowEnum enumerate : XyWorkflowEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("billname", enumerate.getBillname());
                enumMap.put("bosType", enumerate.getBosType());
                enumMap.put("module", enumerate.getModule());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getBillname() {
        return billname;
    }

    public void setBillname(String billname) {
        this.billname = billname;
    }

    public String getBosType() {
        return bosType;
    }

    public void setBosType(String bosType) {
        this.bosType = bosType;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }


}
