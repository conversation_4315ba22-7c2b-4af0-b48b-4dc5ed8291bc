package cn.uone.application.enumerate.source;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 巡查级别
 */
public enum PatrolStateEnum {

    TOHANDLE("1", "待处理"),
    ACCEPTANCE("2", "待验收"),
    FOLLOWUP("3", "已挂起"),
    HANDLE("4", "已完成");

    private String value;
    private String name;

    private static List<Map<String, Object>> list = new ArrayList<>();

    PatrolStateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static PatrolStateEnum find(String value) {
        for (PatrolStateEnum s : PatrolStateEnum.values()) {
            if (!StrUtil.isEmpty(value) && value.equals(s.getValue())) {
                return s;
            }
        }
        return null;
    }

    public static String getNameByValue(String value) {
        String name = "";
        PatrolStateEnum[] enums = PatrolStateEnum.values();
        for (PatrolStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (PatrolStateEnum enumerate : PatrolStateEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    @Override
    public String toString() {
        return this.value.toString();
    }
}
