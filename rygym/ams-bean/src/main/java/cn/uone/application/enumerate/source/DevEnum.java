package cn.uone.application.enumerate.source;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum DevEnum {

    DEV_TYPE_PUR("0","采购设备类型"),
    DEV_TYPE_PRO("1","物业设备类型"),
    DEV_STATE_NORMAL("1","正常"),
    DEV_STATE_MAINTAIN("0","维护");

    private String value;
    private String name;
    private static List<Map<String, Object>> list = new ArrayList<>();
    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (DevEnum enumerate : DevEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }
    DevEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
