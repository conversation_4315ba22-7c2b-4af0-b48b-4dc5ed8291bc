package cn.uone.application.enumerate.order;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 价格策略合同类型
 * Created by caizhanghe on 2024-05-14.
 */
public enum PriceContractTypeEnum {

    //价格策略类型
    PERSONAL_CONTRACT("1", "个人"),
    //
    ORGANIZE_CONTRACT("2", "企业");


    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    PriceContractTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        PriceContractTypeEnum[] enums = PriceContractTypeEnum.values();
        for (PriceContractTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (PriceContractTypeEnum enumerate : PriceContractTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
