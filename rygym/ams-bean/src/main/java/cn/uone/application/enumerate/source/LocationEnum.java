package cn.uone.application.enumerate.source;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 巡查区域
 */
public enum LocationEnum {

    SOURCE("1", "房源区"),
    BUSINESS("2", "商业区"),
    GROUND("3", "地库区"),
    OUTDOOR("4", "户外区"),
    TIANTAI("5", "天台区"),
    LOBBY("6", "大堂区"),
    EQUIPMENT("7", "设备房"),
    CENTRAL("8", "中控室");

    private String value;
    private String name;

    private static List<Map<String, Object>> list = new ArrayList<>();

    LocationEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static LocationEnum find(String value) {
        for (LocationEnum s : LocationEnum.values()) {
            if (!StrUtil.isEmpty(value) && value.equals(s.getValue())) {
                return s;
            }
        }
        return null;
    }

    public static String getNameByValue(String value) {
        String name = "";
        LocationEnum[] enums = LocationEnum.values();
        for (LocationEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (LocationEnum enumerate : LocationEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    @Override
    public String toString() {
        return this.value.toString();
    }
}
