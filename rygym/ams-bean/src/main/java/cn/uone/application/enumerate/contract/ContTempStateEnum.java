package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum ContTempStateEnum {

    // 公寓合同
    APARTMENT("1", "审批中"),
    // 商业合同
    BUSINESS("2", "审批通过"),
    // 车位合同
    CARPORT("3", "被驳回");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    ContTempStateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        ContTempStateEnum[] enums = ContTempStateEnum.values();
        for (ContTempStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static String getValueByName(String name) {
        String value = "";
        ContTempStateEnum[] enums = ContTempStateEnum.values();
        for (ContTempStateEnum temp : enums) {
            if (temp.getName().equals(name)) {
                value = temp.getValue();
                break;
            }
        }
        return value;
    }

    public static ContTempStateEnum getEnumBySourceType(String sourceType) {
        ContTempStateEnum type = null;
        switch (sourceType) {
            case "0":
                type = APARTMENT;
                break;
            case "1":
                type = BUSINESS;
                break;
            case "2":
                type = CARPORT;
                break;
            default:
                break;
        }
        return type;

    }
    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ContTempStateEnum enumerate : ContTempStateEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }


    public String getName() {
        return name;
    }
}
