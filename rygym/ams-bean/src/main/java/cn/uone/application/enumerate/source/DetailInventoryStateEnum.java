package cn.uone.application.enumerate.source;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 固定资产盘点详情，用于盘点每一条资产
 * 盘点状态字典码表
 */
public enum DetailInventoryStateEnum {

    UNINVENTORY("1", "未盘点"),
    INVENTORYLOSS("2", "盘亏"),
    COMPLETEDINVENTORY("3", "已盘点"),
    INVENTORY_PROFIT("8", "盘盈");
    /*PENDINGAPPROVAL("4", "待审核"),
    INREVIEW("5", "审核中"),
    APPVOVED("6", "审核通过");*/
    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    DetailInventoryStateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        DetailInventoryStateEnum[] enums = DetailInventoryStateEnum.values();
        for (DetailInventoryStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static String getValueByName(String name) {
        String value = "";
        DetailInventoryStateEnum[] enums = DetailInventoryStateEnum.values();
        for (DetailInventoryStateEnum temp : enums) {
            if (temp.getName().equals(name)) {
                value = temp.getValue();
                break;
            }
        }
        return value;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (DetailInventoryStateEnum enumerate : DetailInventoryStateEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
