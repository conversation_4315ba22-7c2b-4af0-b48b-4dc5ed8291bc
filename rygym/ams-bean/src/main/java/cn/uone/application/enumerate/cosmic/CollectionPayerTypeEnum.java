package cn.uone.application.enumerate.cosmic;

import lombok.Getter;

@Getter
public enum CollectionPayerTypeEnum {

    CUSTOMER("bd_customer", "客户"),

    SUPPLIER("bd_supplier", "供应商"),

    ORG("bos_org", "公司"),

    USER("bos_user", "职员"),

    OTHER("other", "其他"),
    ;
    private String type;

    private String name;

    CollectionPayerTypeEnum(String type, String name) {
        this.type = type;

        this.name = name;
    }
}
