package cn.uone.application.enumerate.source;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资产类别
 */
public enum AssetTypeEnum {

    ENTRUST("1", "委托"),
    TRANSFER("2", "划转注入"),
    PURCHASE("3", "自购"),
    ENTITY("4", "实物资产"),
    FIX("5", "固定资产"),
    INTANGIBLE("6", "无形资产"),
    FINANCE("7", "金融资产");
    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    AssetTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        AssetTypeEnum[] enums = AssetTypeEnum.values();
        for (AssetTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static String getValueByName(String name) {
        String value = "";
        AssetTypeEnum[] enums = AssetTypeEnum.values();
        for (AssetTypeEnum temp : enums) {
            if (temp.getName().equals(name)) {
                value = temp.getValue();
                break;
            }
        }
        return value;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (AssetTypeEnum enumerate : AssetTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
