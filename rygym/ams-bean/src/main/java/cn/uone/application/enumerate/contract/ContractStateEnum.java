package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合同状态枚举类
 *
 * <AUTHOR>
 * @date 2018-12-18 20:44
 * @return
 */
public enum ContractStateEnum {

    STATUS_NO_IN_TIME("5","待生效"),
    STATUS_SIGNING("9", "待签约"),//4
    STATUS_OPERATE("2", "办理中"),//4
    STATUS_REVIEW("1", "待审核"),//4
    STATUS_PROCESS("11", "流程审批中"),
    STATUS_REJECT("10", "已驳回"),//4
    STATUS_TAKE_EFFECT("6", "已生效"),//1
    STATUS_AUDIT_CHECKOUT("17", "退租待确认"),//5
    STATUS_CHECKOUT("8", "退租中"),//5
    STATUS_TERMINATION("7", "已退租"),//2
    STATUS_EARLY_STOP("71","提前终止"),
    STATUS_CANCEL("3", "已取消"),//7
    STATUS_APPLY_RELET("12", "待续租"),
    STATUS_AUDIT_RELET("18", "续租待审核"),
    STATUS_APPLY_CHECKOUT("13", "待退租"),
    STATUS_RELETTING("14", "续租中"),
    STATUS_APPLY_CHANGEROOM("15", "待换房"),
    STATUS_CONFIRM_CHANGEROOM("19", "换房待确认"),
    STATUS_AUDIT_CHANGEROOM("20", "换房待审核"),
    STATUS_CHANGROOMING("16", "换房中");


    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    ContractStateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        ContractStateEnum[] enums = ContractStateEnum.values();
        for (ContractStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ContractStateEnum enumerate : ContractStateEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
