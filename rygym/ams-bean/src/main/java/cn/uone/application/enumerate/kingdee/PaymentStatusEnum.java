package cn.uone.application.enumerate.kingdee;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 金蝶付款状态
 */
public enum PaymentStatusEnum {
    TOPUSH("0", "待推送"),
    TOPAY("1", "待付款"),
    PAIED("2", "已付款");
    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    PaymentStatusEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        PaymentStatusEnum[] enums = PaymentStatusEnum.values();
        for (PaymentStatusEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (PaymentStatusEnum enumerate : PaymentStatusEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
