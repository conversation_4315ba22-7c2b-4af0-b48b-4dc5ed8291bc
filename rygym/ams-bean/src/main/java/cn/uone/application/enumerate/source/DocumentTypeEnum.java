package cn.uone.application.enumerate.source;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业主证件类型枚举
 * Created by l<PERSON><PERSON><PERSON> on 2018-12-21.
 */
public enum DocumentTypeEnum {
    BUSINESS_LICENSE("1", "营业执照"),
    IDCARD("2", "居民身份证"),
    TEMPORARYWIDCARD("3", "临时居民身份证"),
    HOUSEHOLD_REGISTER("4", "户口簿（未成年人）"),
    PASSPORT("5", "护照"),
    MILITARY_ID("6", "军官证"),
    ARMEDPOLICE_OFFICERCARD("7", "武警警官证"),
    SOLDIER_CARD("8", "士兵证"),
    PSTUDENT_CARD("9", "学员证（军人）"),
    FOREIGNER_PERMANENT_RESIDENCE("10", "外国人永久居留证"),
    FOREIGNER_EXIT_ENTRY("11", "外国人出入境证"),
    PASSFOR_HONGKONG_MACAO("12", "港澳居民来往内地通行证"),
    PASSFOR_TAIWAN("13", "台湾居民来往大陆通行证");


    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    DocumentTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (DocumentTypeEnum enumerate : DocumentTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static DocumentTypeEnum find(String value) {
        for (DocumentTypeEnum s : DocumentTypeEnum.values()) {
            if (!StrUtil.isEmpty(value) && value.equals(s.getValue())) {
                return s;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.value.toString();
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
