package cn.uone.application.enumerate.source;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xmlin on 2018-12-13.
 */

public enum PropertyType2Enum {

    LANDAPPROVAL("0", "用地批复"),
    STATEOWNEDLAND("1", "国有土地使用证"),
    BUILDLAND("2", "建设用地规划许可证"),
    BUILDENGINEERINGPLAN("3", "建设工程规划许可证"),
    BUILDENGINEERINGCONSTRUCTION("4", "建筑工程施工许可证"),
    PROPERTYOWNERSHIPCERTIFICATE("5", "房产证"),
    SALECONTRACT("7", "购房合同"),
    OTHER("8", "其他"),
    REALPROPERTY("6", "不动产权证");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    PropertyType2Enum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (PropertyType2Enum enumerate : PropertyType2Enum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
