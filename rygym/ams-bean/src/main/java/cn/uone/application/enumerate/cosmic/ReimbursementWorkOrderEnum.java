package cn.uone.application.enumerate.cosmic;

/**
 * 报账工单映射枚举
 */
public enum ReimbursementWorkOrderEnum {
    /*
     * 定金转租金	S02.01	租赁意向金
     * 定金转租金	S02.04	租金
     * 定金转物业费	S02.01	租赁意向金
     * 定金转物业费	S02.05	物业费
     * 定金转租赁保证金（押金）	S02.01	租赁意向金
     * 定金转租赁保证金（押金）	S02.02	收 / 付租赁保证金
     */

    DEPOSIT_GUARANTEE("110", "S02.02", "收/付租赁保证金"),
    DEPOSIT_CARRYOVER("111", "S02.02", "收/付租赁保证金"),
    RENT("20", "S02.04", "租金"),
    PROPERTY("240", "S02.05", "物业费");

    private final String orderTypeValue;
    private final String pushCode;
    private final String nameZh;

    ReimbursementWorkOrderEnum(String orderTypeValue, String pushCode, String nameZh) {
        this.orderTypeValue = orderTypeValue;
        this.pushCode = pushCode;
        this.nameZh = nameZh;
    }

    public String getOrderTypeValue() {
        return orderTypeValue;
    }

    public String getPushCode() {
        return pushCode;
    }

    public String getNameZh() {
        return nameZh;
    }
}