package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * 客户紧急程度
 */
public enum CustomerLevelEnum {
    ONE("1", "意向低"),
    TWO("2", "意向中"),
    THREE("3", "意向高");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    CustomerLevelEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        CustomerLevelEnum[] enums = CustomerLevelEnum.values();
        for (CustomerLevelEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (CustomerLevelEnum enumerate : CustomerLevelEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
