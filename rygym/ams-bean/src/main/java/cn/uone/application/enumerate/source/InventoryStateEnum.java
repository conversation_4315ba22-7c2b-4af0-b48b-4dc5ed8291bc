package cn.uone.application.enumerate.source;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 固定资产盘点任务，用于盘点主任务
 * 盘点状态字典码表
 */
public enum InventoryStateEnum {

    UNINVENTORY("1", "未盘点"),
    PARTINVENTORY("2", "部分盘点"),
    COMPLETEDINVENTORY("3", "盘点完成"),
    PENDINGAPPROVAL("4", "待审核"),
    INREVIEW("5", "审核中"),
    APPVOVED("6", "审核通过");
    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    InventoryStateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        InventoryStateEnum[] enums = InventoryStateEnum.values();
        for (InventoryStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static String getValueByName(String name) {
        String value = "";
        InventoryStateEnum[] enums = InventoryStateEnum.values();
        for (InventoryStateEnum temp : enums) {
            if (temp.getName().equals(name)) {
                value = temp.getValue();
                break;
            }
        }
        return value;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (InventoryStateEnum enumerate : InventoryStateEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
