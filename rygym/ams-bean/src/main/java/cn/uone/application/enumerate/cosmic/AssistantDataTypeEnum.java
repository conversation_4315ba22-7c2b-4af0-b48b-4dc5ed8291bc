package cn.uone.application.enumerate.cosmic;

import lombok.Getter;

/**
 * 金蝶辅助资料类别编码
 * 类别编码	类别名称
 * 01	金融资产类型
 * 02	金融产品名称
 * 04	来源方式
 * 05	存货类别
 * 06	车牌号
 * 07	业态类型
 * 08	收款渠道
 * 09	收入类型
 * 10	长期待摊项目
 * 11	楼栋房号
 * 12	债券名称
 * 13	支出类型
 * 14	项目
 * 15	投资产品名称
 * 16	负债类别
 * 17	合同
 * 18	物料类别
 * 19	资产类别
 */
@Getter
public enum AssistantDataTypeEnum {

    FINANCIAL_ASSETS("01", "金融资产类型"),

    FINANCIAL_PRODUCTS("02", "金融产品名称"),

    SOURCE_METHOD("04", "来源方式"),

    INVENTORY_CATEGORY("05", "存货类别"),

    PLATE_NUMBER("06", "车牌号"),

    BIZ_TYPE("07", "业态类型"),

    CHANNEL("08", "收款渠道"),

    INCOME("09", "收入类型"),

    PREPAID_AND_DEFERRED_EXPENSES("10", "长期待摊项目"),

    BUILDING_HOUSE("11", "楼栋房号"),

    BOND_NAME("12", "债券名称"),

    EXPENSES("13", "支出类型"),

    PROJECT("14", "项目"),

    INVESTMENT_PRODUCTS("15", "投资产品名称"),

    LIABILITIES("16", "负债类别"),

    CONTRACT("17", "合同"),

    MATERIAL("18", "物料类别"),

    ASSET("19", "资产"),

    ;

    private String typeNumber;

    private String typeName;

    AssistantDataTypeEnum(String typeNumber, String typeName) {
        this.typeNumber = typeNumber;
        this.typeName = typeName;
    }

    public static AssistantDataTypeEnum parseEnum(String typeNumber) {
        for (AssistantDataTypeEnum value : AssistantDataTypeEnum.values()) {
            if (value.getTypeNumber().equals(typeNumber)) {
                return value;
            }
        }
        return null;
    }

    public static AssistantDataTypeEnum parseEnumByName(String typeName) {
        for (AssistantDataTypeEnum value : AssistantDataTypeEnum.values()) {
            if (value.getTypeName().equals(typeName)) {
                return value;
            }
        }
        return null;
    }
}
