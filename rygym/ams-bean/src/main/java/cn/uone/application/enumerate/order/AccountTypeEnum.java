package cn.uone.application.enumerate.order;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账号类型
 * Created by xmlin on 2018-12-13.
 */
public enum AccountTypeEnum {

    PAY("1","缴费"),
    RECHARGE("2","充值"),
/*
    DEPOSIT("10", "定金"),
    @Deprecated
    PUBLICPROPERTYFEE("100", "物业公摊水电"),
    NETFEE("120", "宽带费"),
    PROPERTYBASIC("130", "物业基本户"),
    PROPERTYPUBLICVIKING("140", "公共维修金"),
    CARPARKRENR("160", "车位租金"),
    RENT("20", "租金"),
    SUBSIDY("21", "人才公寓补贴预付款"),
    HUANFANGFEE("220", "换房费"),
    ZHUANZUFEE("221", "转租费"),
    @Deprecated
    PROPERTY_TAX("230", "房产税抵扣"),
    WEIYUEJIN("240", "违约金"),
    SEPECIAL_SERVICEFEE("250", "特殊管理费"),
    SEPECIAL_MARGINFEE("260", "特殊履约保证金"),
    CHECKOUT_RECEIPTS("270", "退房收款"),
    YAJIN("5", "押金"),
    WATERFEE("50", "水费"),
    PUBLICWATERFEE("51", "套内公摊水费"),
    PROPERTYWATERFEE("52", "公摊水费"),
    FEE_YAJIN("6", "水电周转金"),
    POWERFEE("70", "电费"),
    PUBLICPOWERFEE("71", "套内公摊电费"),
    PROPERTYPOWERFEE("72", "公摊电费"),
    GASFEE("80", "燃气费"),
    ELSEFEE("500", "其他费用"),

    ZHFUWUFEE("340", "综合服务费"),
    FUWUFEE("350", "特约服务费"),
    SWEEPFEE("330", "清扫费"),
    MENJINCARDCOST("310", "门禁卡成本"),
    PROPERTY_GARBAGEFEE("222", "垃圾清运费"),
    WEIXIUPEICHANGJIN("88", "维修费")*/;



    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    AccountTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        AccountTypeEnum[] enums = AccountTypeEnum.values();
        for (AccountTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (AccountTypeEnum enumerate : AccountTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public static AccountTypeEnum getEnumByOrderType(String orderType) {
//        AccountTypeEnum type = PAY;
//        switch (orderType) {
//            case "5":
//                type = YAJIN;
//                break;//押金---押金
//            case "6":
//                type = FEE_YAJIN;
//                break;//水电押金---水电押金
//            case "10":
//                type = DEPOSIT;
//                break;//定金---定金
//            case "130":
//                type = CHECKOUT_RECEIPTS;
//                break;//退房收款---退房收款
//            case "160":
//                type = CARPARKRENR;
//                break;//车位租金---车位租金
//            case "170":
//                type = CARPARKRENR;
//                break;//车位退租租金---车位租金
//            case "20":
//                type = RENT;
//                break;//租金---租金
//            case "21":
//                type = SUBSIDY;
//                break;//人才补贴---人才补贴
//        }

        return PAY;

    }

    public static AccountTypeEnum getEnumByOrderItemType(String orderItemType) {
//        AccountTypeEnum type = null;
//        switch (orderItemType) {
//            case "20":
//            case "90":
//            case "170":
//            case "290":
//                type = RENT;
//                break;//租金 超期房租 退房房租 车位退租租金---房租
//            case "30":
//                type = WATERFEE;
//                break;//水费---水费
//            case "120":
//                type = PUBLICWATERFEE;
//                break;//公共水费---公共水费
//            case "50":
//                type = POWERFEE;
//                break;//电费---电费
//            case "140":
//                type = PUBLICPOWERFEE;
//                break;//公共电费---公共电费
//            case "160":
//            case "230":
//                type = GASFEE;
//                break;//公共煤气 燃气费---燃气费
//            case "130":
//                type = NETFEE;
//                break;//宽带费---宽带费
//            case "190":
//                type = PROPERTYWATERFEE;
//                break;//公摊水费---物业公摊水电
//            case "200":
//                type = PROPERTYPOWERFEE;
//                break;//公摊电费---公摊电费
//            case "100":
//                type = ELSEFEE;
//                break;//其他费用---其他费用
//            case "350":
//                type = FUWUFEE;
//                break;//服务费---服务费
//            case "330":
//                type = SWEEPFEE;
//                break;//清扫费---清扫费
//            case "310":
//                type = MENJINCARDCOST;
//                break;//门禁卡成本---门禁卡成本
//            case "220":
//                type = PROPERTY_GARBAGEFEE;
//                break;//垃圾清运费---垃圾清运费
//            case "320":
//            case "80":
//            case "380":
//                type = PROPERTY_GARBAGEFEE;
//                break;//维修服务费 维修赔偿金 维修费---维修费
//            case "60":
//            case "180":
//                type = PROPERTYBASIC;
//                break;//物业费  物业基本服务费 ---物业基本户
//            case "210":
//                type = PROPERTYPUBLICVIKING;
//                break;//公共维修金---公共维修金
//            case "370":
//                type = HUANFANGFEE;
//                break;//换房费---换房费
//            case "371":
//                type = ZHUANZUFEE;
//                break;//转租费---转租费
//            case "390":
//                type = PROPERTY_TAX;
//                break;//房产税抵扣---房产税抵扣
//            case "70":
//                type = WEIYUEJIN;
//                break;//违约金---违约金
//            case "400":
//                type = SEPECIAL_SERVICEFEE;
//                break;//特殊管理费---特殊管理费
//            case "410":
//                type = SEPECIAL_MARGINFEE;
//                break;//特殊履约保证金---特殊履约保证金
//            case "110":
//                type = YAJIN;
//                break;//押金---押金
//            case "240":
//                type = ZHFUWUFEE;
//                break;//综合服务费---综合服务费
//
//
//        }

        return PAY;

    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
