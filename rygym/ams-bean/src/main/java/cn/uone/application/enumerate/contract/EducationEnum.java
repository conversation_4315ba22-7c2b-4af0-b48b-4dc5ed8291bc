package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: lzc
 * @Date: 2019/01/23 17:44
 * @Description:
 */
public enum EducationEnum {

    BACHELOR("1", "本科"),
    MASTER("2", "硕士"),
    DOCTOR("3", "博士"),
    OTHER("4", "其他");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    EducationEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        EducationEnum[] enums = EducationEnum.values();
        for (EducationEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (EducationEnum enumerate : EducationEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
