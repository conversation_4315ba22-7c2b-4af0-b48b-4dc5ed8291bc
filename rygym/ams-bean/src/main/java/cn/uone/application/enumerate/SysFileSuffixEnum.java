package cn.uone.application.enumerate;


public enum SysFileSuffixEnum {

    FAX("fax", "image/fax"),
    GIF("gif", "image/gif"),
    ICO("ico", "image/x-icon"),
    JFIF("jfif", "image/jpeg"),
    JPE("jpe", "image/jpeg"),
    JPEG("jpeg", "image/jpeg"),
    JPG("jpg", "image/jpeg"),
    PDF("pdf", "application/pdf"),
    PNG("png", "image/png"),
    PNG2("png", "application/x-png"),
    SVG("html", "text/xml"),
    TIF("tif", "image/tiff"),
    TIF2("tif", "application/x-tif"),
    TIFF("tiff", "image/tiff");

    private String value;
    private String name;

    SysFileSuffixEnum(String name, String value) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        SysFileSuffixEnum[] enums = SysFileSuffixEnum.values();
        for (SysFileSuffixEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }
    public static String getValueByName(String name) {
        String value = "";
        SysFileSuffixEnum[] enums = SysFileSuffixEnum.values();
        for (SysFileSuffixEnum temp : enums) {
            if (temp.getName().equals(name)) {
                value = temp.getValue();
                break;
            }
        }
        return value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
