package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: ljl
 * @Date: 2018/12/19 17:44
 * @Description:
 */
public enum SimPayTypeEnum {

    MONTH("1", "月付"),
    SEASON("2", "季付");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    SimPayTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static SimPayTypeEnum getEnumByValue(String value) {
        SimPayTypeEnum[] enums = SimPayTypeEnum.values();
        for (SimPayTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                return temp;
            }
        }
        return null;
    }

    public static String getNameByValue(String value) {
        String name = "";
        SimPayTypeEnum[] enums = SimPayTypeEnum.values();
        for (SimPayTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (SimPayTypeEnum enumerate : SimPayTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
