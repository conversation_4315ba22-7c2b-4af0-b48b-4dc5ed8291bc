package cn.uone.application.enumerate.source;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public enum AliPayWayEnum {
    ONE("1","付1"),
    TWO("2", "付2"),
    THREE("3","付3"),
    FOUR("4", "付4"),
    FIVE("5", "付5"),
    SIX("6", "付6"),
    SEVEL("7", "付7"),
    EIGHT("8", "付8"),
    NIGN("9", "付9"),
    TEN("10", "付10"),
    ELEVEN("11", "付11"),
    TWELVE("12", "付12");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    AliPayWayEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        AliPayWayEnum[] enums = AliPayWayEnum.values();
        for (AliPayWayEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (AliPayWayEnum enumerate : AliPayWayEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
