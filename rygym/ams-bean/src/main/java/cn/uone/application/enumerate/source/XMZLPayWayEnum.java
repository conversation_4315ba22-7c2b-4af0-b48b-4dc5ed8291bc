package cn.uone.application.enumerate.source;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public enum XMZLPayWayEnum {
    ONE("1","押1付3"),
    TWO("2", "押1付2"),
    THREE("3","押1付1"),
    FOUR("4", "押2付1"),
    FIVE("5", "年付不押"),
    SIX("6", "半年付不押"),
    SEVEN("7", "面议");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    XMZLPayWayEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        XMZLPayWayEnum[] enums = XMZLPayWayEnum.values();
        for (XMZLPayWayEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (XMZLPayWayEnum enumerate : XMZLPayWayEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
