package cn.uone.application.enumerate.cosmic;

/**
 * 财务收款单映射枚举
 */
public enum FinancialCollectionBillEnum {
    DEPOSIT("10", "S02.01", "租赁意向金"),
    RENT("20", "S02.04", "租金"),
    PROPERTY("240", "S02.05", "物业费"),
    // ENERGY("30", "S02.06", "公维金"),
    UTILITIES("110", "S02.07", "其他收入"),
    // 水费-收入、成本模式
    WATER("301","S02.11","水费-收入、成本模式"),
    // 电费-收入、成本模式
    ELECTRICITY("501","S02.12","电费-收入、成本模式"),
    // 收/付租赁保证金
    COLLECT_AND_PAY_THE_LEASE_DEPOSIT("5","S02.02","收/付租赁保证金");
    // 收/付装修保证金
    // 收_付装修保证金("120","S02.03","收/付装修保证金");

    private final String orderTypeValue;
    private final String pushCode;
    private final String nameZh;

    FinancialCollectionBillEnum(String orderTypeValue, String pushCode, String nameZh) {
        this.orderTypeValue = orderTypeValue;
        this.pushCode = pushCode;
        this.nameZh = nameZh;
    }

    public String getOrderTypeValue() {
        return orderTypeValue;
    }

    public String getPushCode() {
        return pushCode;
    }

    public String getNameZh() {
        return nameZh;
    }
} 