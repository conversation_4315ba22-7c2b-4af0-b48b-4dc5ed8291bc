package cn.uone.application.enumerate.order;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 子账单类型
 * Created by xmlin on 2018-12-13.
 */
public enum OrderItemTypeEnum {
    //预定相关
    DEPOSIT("10", "意向金"),
    DEPOSITREFUND("340", "意向金退款"),
    YAJIN("110", "押金"),
    YAJINREFUND("115", "押金退款"),
    DIKOUYAJIN("111", "押金结转"),
    FEE_YAJIN("112", "水电周转金"),
    YAJINCHECKOUTTUI("113", "水电周转金"),
    //租金相关
    RENT("20", "租金"),//stime,etime
    SUBSIDY("21", "人才公寓补贴预付款"),//stime,etime
    ACTIVITY("360", "优惠"),
    DIFFERENCE("361", "差额"),
    //生活费用相关
    PROPERTY_PUBLICFEE("210", "公共维修金"),//stime,etime
    PROPERTY_GARBAGEFEE("220", "垃圾清运费"),//stime,etime
    PROPERTY_BASEFEE("180", "物业费"),//stime,etime
    UTILITIES("30", "水费"),//stime,etime,sread,eread
    WANGLUOFEI("130", "宽带费"),//stime,etime
    PUBLICWATER("120", "套内公摊水费"),//stime,etime,sread,eread
    PROPERTY_WATERFEE("190", "公摊水费"),//stime,etime
    DIANFEI("50", "电费"),//stime,etime,sread,eread
    PUBLICPOWERRATE("140", "套内公摊电费"),//stime,etime,sread,eread
    PROPERTY_POWERFEE("200", "公摊电费"),//stime,etime
    @Deprecated
    LICGAS("230", "燃气费"),//stime,etime
    PUBLICGAS("160", "燃气费"),//stime,etime,sread,eread
    SYNTHESIZE_BASEFEE("240", "综合管理费"),
    ZHGLFJIEZHUAN("241", "综合管理费结转"),
    //业务相关
    HUANFANGFEE("370", "换房费"),
    ZHUANZUFEE("371", "转租费"),
    MENJINCARDCOST("310", "门禁卡成本"),
    SWEEPFEE("330", "清扫费"),
    FUWUFEE("350", "特约服务费"),
    ELSEFEE("100", "其他费用"),
    RECHARGE("420", "充值抵扣"),
    SEPECIAL_SERVICEFEE("400", "特殊管理费"),
    SEPECIAL_MARGINFEE("410", "特殊保证金"),
    WEIYUEJIN("70", "违约金"),
    WEIXIUPEICHANGJIN("80", "维修费"),
    SPRECHARGE("333", "水费预充值"),
    DPRECHARGE("444", "电费预充值"),
    @Deprecated
    HOUSEDUTY("390", "房产税抵扣"),
    @Deprecated
    CARPARKRENR("280", "车位租金"),//stime,etime
    @Deprecated
    CARPARKCHECKOUTFEE("300", "退租费"),
    @Deprecated
    ARERENT("90", "超期租金"),
    @Deprecated
    //退房房租 改成 租金
    CHECKOUTRENT("170", "退房房租"),//stime,etime
    @Deprecated
    //退房房租 改成 租金
    CARPARKCHECKOUTRENT("290", "车位退租租金"),//stime,etime
    @Deprecated
    //维修赔偿金 改成 维修费
    REPAIRFEE("320", "维修赔偿金"),
    @Deprecated
    WEIXIUFUWUFEE("380", "维修服务费"),
    @Deprecated
    WUYEFEI("60", "物业费"),//stime,etime
    JIEZHUAN("445", "结转费"),
    WELFARE("446", "集团福利费"),
    YXJIEZHUAN("447","意向金结转"),
    INTENTCONFISCATE("101","意向金罚没"),
    YAJINCONFISCATE("102","押金罚没"),
    ZJJIEZHUAN("201","租金结转"),
    GJJDK("448","公积金抵扣"),
    OTHER_UTILITIES("301", "其他费用(代收水费)"),//stime,etime,sread,eread
    OTHER_DIANFEI("501", "其他费用(代收电费)");//stime,etime,sread,eread




    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    OrderItemTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        OrderItemTypeEnum[] enums = OrderItemTypeEnum.values();
        for (OrderItemTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (OrderItemTypeEnum enumerate : OrderItemTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
