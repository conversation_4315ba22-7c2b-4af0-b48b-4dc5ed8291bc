package cn.uone.application.enumerate.source;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 费用配置名称枚举
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018-12-21.
 */
public enum CollectStateEnum {
    CANCEL("0", "取消收藏"),
    COLLECTED("1", "已收藏");


    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;
    CollectStateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        CollectStateEnum[] enums = CollectStateEnum.values();
        for (CollectStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (CollectStateEnum enumerate : CollectStateEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static CollectStateEnum findPayStatusEnum(String value) {
        for (CollectStateEnum s : CollectStateEnum.values()) {
            if (!StrUtil.isEmpty(value) && value.equals(s.getValue())) {
                return s;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
