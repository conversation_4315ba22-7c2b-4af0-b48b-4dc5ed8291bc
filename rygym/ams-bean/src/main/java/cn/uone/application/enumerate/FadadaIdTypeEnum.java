package cn.uone.application.enumerate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: ljl
 * @Date: 2019/1/18 15:31
 * @Description:
 */
public enum FadadaIdTypeEnum {

    // 身份证
    IDENTITY_CARD("1", "居民身份证");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    FadadaIdTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        FadadaIdTypeEnum[] enums = FadadaIdTypeEnum.values();
        for (FadadaIdTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (FadadaIdTypeEnum enumerate : FadadaIdTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
