package cn.uone.application.enumerate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审批状态
 * Created by xmlin on 2018-12-13.
 */
public enum ApprovalStateEnum {

    TOBESUBMIT("0", "待提交"),
    APPROVAL("1", "审批中"),
    COMPLETE("2", "已完成"),
    REJECT("3", "已驳回"),
    CANCEL("4", "已撤销");//子节点为已转审批
    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    ApprovalStateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        ApprovalStateEnum[] enums = ApprovalStateEnum.values();
        for (ApprovalStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ApprovalStateEnum enumerate : ApprovalStateEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
