package cn.uone.application.enumerate.reit;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审批状态
 * Created by xmlin on 2018-12-13.
 */
public enum ReitReimbursementEnum {

    EMPLOYEE("1", "员工报销"),
    EXTERNALCONTRACT("2", "对外付款-合同付款"),
    EXTERNALDAILY("3", "对外付款-日常采购付款"),
    EXTERNALCOMMING("4", "对外付款-代垫往来款"),
    EXTERNALPAY("5", "对外付款-代垫费用");
    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    ReitReimbursementEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        ReitReimbursementEnum[] enums = ReitReimbursementEnum.values();
        for (ReitReimbursementEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ReitReimbursementEnum enumerate : ReitReimbursementEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
