package cn.uone.application.enumerate.source;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xmlin on 2018-12-13.
 */

public enum ProjectSignatureEnum {

    HOTEL("14990302AC8F8C92AB1ED9FC21621481", "厦门XX酒店管理有限责任公司"),
    APPARTMENT("D141320DC12FCBB4E52DA671FDC4EAE5", "厦门XX公寓管理有限责任公司"),
    PROPERTY("FBC925E97F23C223E745CC8CD1DDC7E6", "厦门优租物业管理有限责任公司"),
    RUITING("089989F42DDCE114A7747B06BCBD78C3", "厦门瑞廷公寓管理有限公司");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    ProjectSignatureEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ProjectSignatureEnum enumerate : ProjectSignatureEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public static String getNameByValue(String value) {
        String name = "";
        SourceStateEnum[] enums = SourceStateEnum.values();
        for (SourceStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
