package cn.uone.application.enumerate.source;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * 报修物件的类型枚举类
 *
 *
 */
public enum RepairItemTypeEnum {

    FURNITURE("1", "家具"),

    EQUIPMENT("2", "电器"),

    BATHROOM("3", "卫浴物品"),

    PIPE("4","管道"),

    ELECTRIC("5","电路");


    private String value;
    private String name;

    private static List<Map<String, Object>> list = new ArrayList<>();

    RepairItemTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static RepairItemTypeEnum find(String value) {
        for (RepairItemTypeEnum s : RepairItemTypeEnum.values()) {
            if (!StrUtil.isEmpty(value) && value.equals(s.getValue())) {
                return s;
            }
        }
        return null;
    }

    public static String getNameByValue(String value) {
        String name = "";
        RepairItemTypeEnum[] enums = RepairItemTypeEnum.values();
        for (RepairItemTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (RepairItemTypeEnum enumerate : RepairItemTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    @Override
    public String toString() {
        return this.value.toString();
    }
}
