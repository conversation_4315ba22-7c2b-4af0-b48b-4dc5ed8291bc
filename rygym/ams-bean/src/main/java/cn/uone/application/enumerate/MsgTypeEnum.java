package cn.uone.application.enumerate;

public enum MsgTypeEnum {
    //素材类型
    YUDING("1", "预定提醒"),
    DAISHENHEQIANYUE("2", "待审核签约提醒"),
    TUIFANGSHENQING("3", "退房申请提醒"),//contract  特指系统生成的合同文件
    DAIBANRUZHU("4", "待办入住提醒"),//SIGNINGZ  TALENTSIDENTITY
    GUZHANGBAOXIU("5", "故障报修提醒"),
    DAICHULIKEHU("6", "待处理客户提醒"),
    DAIBANRENWU("7", "待办任务提醒"),//对应的fromId有2类， 一个是renter_id一个是contract_id，
    YUYUERUZHU("8", "预约入住提醒"),
    BANLIRUZHURENSHENQING("9", "搬离入住人申请提醒"),
    XINZENGRUZHURENSHENQING("10", "新增入住人申请提醒"),//TALENTSMARRY
    HUANFANGSHENQING("11", "换房申请提醒"),//TALENTSLABOR
    XUZUSHENQING("12", "续租申请提醒"),//TALENTSSOCIAL
    // HOUSEHOLD("13", "退房申请提醒"),//TALENTSHOUSE
    YICIXINGMENSUO("14", "一次性门锁提醒"),//TALENTSNOHOUSING
    KEHUBAOXIU("15", "客户报修提醒"),
    KEHUTOUSU("16", "客户投诉提醒"),
    DAICHULIKEHUTOUSU("17", "待处理客户投诉提醒"),
    XUNCHADANJIJIANGDAOQI("18", "巡查单即将到期提醒"),
    HETONGJIJIANGDAOQI("19", "合同即将到期提醒"),
    INTENTTOAUDIT("20", "意向书审核提醒"),
    DELIVERYAFFIRM("21", "交付确认书查看提醒"),
    KEHUJIANYI("22", "客户建议提醒"),
    DELIVERY_CREATE("23", "交付确认书生成提醒");

    private String value;
    private String name;

    MsgTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static MsgTypeEnum getEnumByValue(String value) {
        MsgTypeEnum[] enums = MsgTypeEnum.values();
        for (MsgTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                return temp;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

}
