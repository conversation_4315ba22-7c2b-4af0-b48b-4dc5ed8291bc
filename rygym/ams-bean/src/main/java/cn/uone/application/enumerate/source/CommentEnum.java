package cn.uone.application.enumerate.source;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * 报修物件的类型枚举类
 *
 *
 */
public enum CommentEnum {

    POSITIVE("1", "好评"),

    NEUTRAL("2", "中评"),

    NEGATIVE("3", "差评") ,

    FAILREPAIR("4", "差评:未修复"),

    UNPROFESSIONAL("5", "差评:维修人员不专业"),

    UNTIMELY("6", "差评:处理不及时"),

    INEFFICIENT("7", "差评:维修效率低");

    private String value;
    private String name;

    private static List<Map<String, Object>> list = new ArrayList<>();

    CommentEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static CommentEnum find(String value) {
        for (CommentEnum s : CommentEnum.values()) {
            if (!StrUtil.isEmpty(value) && value.equals(s.getValue())) {
                return s;
            }
        }
        return null;
    }

    public static String getNameByValue(String value) {
        String name = "";
        CommentEnum[] enums = CommentEnum.values();
        for (CommentEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (CommentEnum enumerate : CommentEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    @Override
    public String toString() {
        return this.value.toString();
    }
}
