package cn.uone.application.enumerate.kingdee;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName KingdeeConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/23 15:30
 * @Version 1.0
 */
@Configuration
@Data
public class KingdeeConfig {
    public static String bosTypePaymentName;
    public static String bosTypePaymentCode;
    public static String bosTypeReceiptName;
    public static String bosTypeReceiptCode;
    public static String bosTypeReceivableName;
    public static String bosTypeReceivableCode;
    public static String bosTypeTransferName;
    public static String bosTypeTransferCode;
    public static String bosTypeAmortizeName;
    public static String bosTypeAmortizeCode;
    public static String bosTypeInvoiceName;
    public static String bosTypeInvoiceCode;
    public static String paymentTypePayName;
    public static String paymentTypePayCode;

    @Value("${kingdee.bosType.paymentName}")
    public void setBosTypePaymentName(String bosTypePaymentName) {
        KingdeeConfig.bosTypePaymentName = bosTypePaymentName;
    }

    @Value("${kingdee.bosType.paymentCode}")
    public void setBosTypePaymentCode(String bosTypePaymentCode) {
        KingdeeConfig.bosTypePaymentCode = bosTypePaymentCode;
    }

    @Value("${kingdee.bosType.receiptName}")
    public void setBosTypeReceiptName(String bosTypeReceiptName) {
        KingdeeConfig.bosTypeReceiptName = bosTypeReceiptName;
    }

    @Value("${kingdee.bosType.receiptCode}")
    public void setBosTypeReceiptCode(String bosTypeReceiptCode) {
        KingdeeConfig.bosTypeReceiptCode = bosTypeReceiptCode;
    }

    @Value("${kingdee.bosType.receivableName}")
    public void setBosTypeReceivableName(String bosTypeReceivableName) {
        KingdeeConfig.bosTypeReceivableName = bosTypeReceivableName;
    }

    @Value("${kingdee.bosType.receivableCode}")
    public void setBosTypeReceivableCode(String bosTypeReceivableCode) {
        KingdeeConfig.bosTypeReceivableCode = bosTypeReceivableCode;
    }

    @Value("${kingdee.bosType.transferName}")
    public void setBosTypeTransferName(String bosTypeTransferName) {
        KingdeeConfig.bosTypeTransferName = bosTypeTransferName;
    }

    @Value("${kingdee.bosType.transferCode}")
    public void setBosTypeTransferCode(String bosTypeTransferCode) {
        KingdeeConfig.bosTypeTransferCode = bosTypeTransferCode;
    }

    @Value("${kingdee.bosType.amortizeName}")
    public void setBosTypeAmortizeName(String bosTypeAmortizeName) {
        KingdeeConfig.bosTypeAmortizeName = bosTypeAmortizeName;
    }

    @Value("${kingdee.bosType.amortizeCode}")
    public void setBosTypeAmortizeCode(String bosTypeAmortizeCode) {
        KingdeeConfig.bosTypeAmortizeCode = bosTypeAmortizeCode;
    }

    @Value("${kingdee.bosType.invoiceName}")
    public void setBosTypeInvoiceName(String bosTypeInvoiceName) {
        KingdeeConfig.bosTypeInvoiceName = bosTypeInvoiceName;
    }

    @Value("${kingdee.bosType.invoiceCode}")
    public void setBosTypeInvoiceCode(String bosTypeInvoiceCode) {
        KingdeeConfig.bosTypeInvoiceCode = bosTypeInvoiceCode;
    }

    @Value("${kingdee.paymentType.payName}")
    public void setPaymentTypePayName(String paymentTypePayName) {
        KingdeeConfig.paymentTypePayName = paymentTypePayName;
    }

    @Value("${kingdee.paymentType.payCode}")
    public void setPaymentTypePayCode(String paymentTypePayCode) {
        KingdeeConfig.paymentTypePayCode = paymentTypePayCode;
    }
}
