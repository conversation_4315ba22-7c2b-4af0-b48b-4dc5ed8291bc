package cn.uone.application.enumerate.source;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 费用配置名称枚举
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018-12-21.
 */
public enum ExpandStateEnum {
    FOLLOW_UP("0", "跟进 / 维护"),
    KEY_DEVELOPMENT("1", "重点拓展"),
    SIGNED("2", "已签约"),
    GIVE_UP("3","放弃");


    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;
    ExpandStateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        ExpandStateEnum[] enums = ExpandStateEnum.values();
        for (ExpandStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ExpandStateEnum enumerate : ExpandStateEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static ExpandStateEnum findPayStatusEnum(String value) {
        for (ExpandStateEnum s : ExpandStateEnum.values()) {
            if (!StrUtil.isEmpty(value) && value.equals(s.getValue())) {
                return s;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
