package cn.uone.application.enumerate.order;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum TransferTypeEnum {
    UNION_PAY("1", "银联支付"),

    CCB_TRANSFER("2", "建行"),

    UNION_TRANSFER("3", "银联划付"),

    CCB_REFUND("4", "建行退款"),

    OTHER("9", "其他");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    TransferTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        TransferTypeEnum[] enums = TransferTypeEnum.values();
        for (TransferTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (TransferTypeEnum enumerate : TransferTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
