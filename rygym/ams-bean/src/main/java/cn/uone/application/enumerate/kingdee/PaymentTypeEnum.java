package cn.uone.application.enumerate.kingdee;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 金蝶收付类型
 */
public enum PaymentTypeEnum {
    PAY(KingdeeConfig.paymentTypePayCode, KingdeeConfig.paymentTypePayName),
    COLLECT("SR01", "长租公寓收款");
    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    PaymentTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        PaymentTypeEnum[] enums = PaymentTypeEnum.values();
        for (PaymentTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (PaymentTypeEnum enumerate : PaymentTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
