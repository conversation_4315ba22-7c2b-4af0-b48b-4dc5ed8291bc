package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum RecordAreaEnum {

    SM("350203000000", "思明区"),
    HC("350205000000", "海沧区"),
    HL("350206000000", "湖里区"),
    JM("350211000000", "集美区"),
    TA("350212000000", "同安区"),
    XA("350213000000", "翔安区");


    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    RecordAreaEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        RecordAreaEnum[] enums = RecordAreaEnum.values();
        for (RecordAreaEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (RecordAreaEnum enumerate : RecordAreaEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
