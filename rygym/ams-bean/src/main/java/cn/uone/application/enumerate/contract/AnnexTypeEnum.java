package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: ljl
 * @Date: 2018/12/17 11:07
 * @Description:
 */
public enum AnnexTypeEnum {

    PET("1", "宠物协议"),
    CHECKINTIPS("2", "办理入住-特别提示"),
    RESERVENOTICE("3", "预定须知"),
    OTHER("4", "其他");


    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    AnnexTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static AnnexTypeEnum getEnumByValue(String value) {
        AnnexTypeEnum[] enums = AnnexTypeEnum.values();
        for (AnnexTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                return temp;
            }
        }
        return null;
    }

    public static String getNameByValue(String value) {
        String name = "";
        AnnexTypeEnum[] enums = AnnexTypeEnum.values();
        for (AnnexTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static String getValueByName(String name) {
        String value = "";
        AnnexTypeEnum[] enums = AnnexTypeEnum.values();
        for (AnnexTypeEnum temp : enums) {
            if (temp.getName().equals(name)) {
                value = temp.getValue();
                break;
            }
        }
        return value;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (AnnexTypeEnum enumerate : AnnexTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
