package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 退租原因
*/
public enum ReleaseChangeRoomStatEnum {

    NO("0", "不收取"),

    YES("1", "收取"),

    AUDIT("2", "提交审核");

    private static List<Map<String, Object>> list = new ArrayList<>();

    private String value;
    private String name;

    ReleaseChangeRoomStatEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        ReleaseChangeRoomStatEnum[] enums = ReleaseChangeRoomStatEnum.values();
        for (ReleaseChangeRoomStatEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ReleaseChangeRoomStatEnum enumerate : ReleaseChangeRoomStatEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
