package cn.uone.util.wechat;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "Item")
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "ItemName",
        "ItemUserId",
        "ItemParty",
        "ItemImage",
        "ItemStatus",
        "ItemSpeech",
        "ItemOpTime",
})
@Data
public class Item {
    //分支审批人姓名
    private String ItemName;
    //分支审批人userid
    private String ItemUserId;
    //分支审批人所在部门
    private String ItemParty;
    //分支审批人头像
    private String ItemImage;
    //分支审批审批操作状态：1-审批中；2-已同意；3-已驳回；4-已转审
    private String ItemStatus;
    //分支审批人审批意见
    private String ItemSpeech;
    //分支审批人操作时间
    private long ItemOpTime;
}
