package cn.uone.util;

import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.web.calc.Constants;
import cn.uone.web.calc.Digests;
import cn.uone.web.calc.Encodes;

public class AlgorUtil extends cn.uone.web.util.AlgorUtil{
    /**
     * 设定安全的密码，生成随机的salt并经过1024次 sha-1 hash
     */
    public static void entryptPassword(UserEntity user) {
        byte[] salt = Digests.generateSalt(Constants.SALT_SIZE);
        user.setSalt(Encodes.encodeHex(salt));
        byte[] hashPassword = Digests.sha1(user.getPassword().getBytes(), salt, Constants.HASH_INTERATIONS);
        user.setPassword(Encodes.encodeHex(hashPassword));
    }

    public static void main(String[] args) {
        byte[] salt = Digests.generateSalt(Constants.SALT_SIZE);
        System.out.println(Encodes.encodeHex(salt));
        byte[] hashPassword = Digests.sha1("123456".getBytes(), salt, Constants.HASH_INTERATIONS);
        System.out.println(Encodes.encodeHex(hashPassword));
    }

}
