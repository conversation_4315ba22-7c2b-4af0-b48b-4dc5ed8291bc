package cn.uone.util.wechat;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "xml")
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "ToUserName",
        "FromUserName",
        "CreateTime",
        "MsgType",
        "Event",
        "AgentID",
        "ApprovalInfo",
})
@Data
public class ApprovalCallback implements Serializable {
    private String ToUserName;
    private String FromUserName;
    private long CreateTime;
    private String MsgType;
    private String Event;
    private String AgentID;
    private ApprovalInfo ApprovalInfo;
}
