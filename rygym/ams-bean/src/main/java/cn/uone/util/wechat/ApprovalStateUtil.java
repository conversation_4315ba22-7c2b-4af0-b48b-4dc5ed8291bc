package cn.uone.util.wechat;

import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.ApprovalTypeEnum;
import cn.uone.bean.entity.business.apro.ApprovalCommitEntity;
import cn.uone.shiro.util.UoneSysUser;

import java.util.Arrays;
import java.util.Date;
import java.util.UUID;


public class ApprovalStateUtil {

    public static boolean type(String type) {
      boolean b=false;
        if(Arrays.asList(ApprovalTypeEnum.COUPON.getValue(),
                ApprovalTypeEnum.ORDERCONFIRMREFUND.getValue(),
                ApprovalTypeEnum.ORDERCONFIRMPAY.getValue(),
                ApprovalTypeEnum.DESIGNCONTRACT.getValue(),
                ApprovalTypeEnum.PROCUREMENTBUDGET.getValue(),
                ApprovalTypeEnum.PROCUREMENTPAY.getValue(),
                ApprovalTypeEnum.CONFIRMCHECKOUT.getValue(),
                ApprovalTypeEnum.PATROL.getValue(),
                ApprovalTypeEnum.RETURNDEPOSIT.getValue()
                ,ApprovalTypeEnum.CHANGEROOM.getValue(),
                ApprovalTypeEnum.CHANGERENTER.getValue(),
                ApprovalTypeEnum.CANCELORDER.getValue(),
                ApprovalTypeEnum.SOURCEPRICE.getValue(),
                ApprovalTypeEnum.FIXLASTORDER.getValue()).contains(type)){
            b=true;
        }
      return b;
    }

    public static void initCommit(ApprovalCommitEntity commitEntity, String codeid){
        commitEntity.setCode(UUID.randomUUID().toString().replaceAll("-",""));
        commitEntity.setCodeId(codeid);
        commitEntity.setUserid(UoneSysUser.id());
        commitEntity.setImage(UoneSysUser.icon());
        commitEntity.setApplyTime(new Date());
        commitEntity.setStatus(ApprovalStateEnum.TOBESUBMIT.getValue());
    }

}
