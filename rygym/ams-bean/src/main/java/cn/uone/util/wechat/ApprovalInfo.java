package cn.uone.util.wechat;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "ApprovalInfo")
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "ThirdNo",
        "OpenSpName",
        "OpenTemplateId",
        "OpenSpStatus",
        "ApplyTime",
        "ApplyUserName",
        "ApplyUserId",
        "ApplyUserParty",
        "ApplyUserImage",
        "ApprovalNodes",
})
@Data
public class ApprovalInfo {
    private String ThirdNo;
    private String OpenSpName;
    private long OpenTemplateId;
    private String OpenSpStatus;
    private String ApplyTime;
    private String ApplyUserName;
    private String ApplyUserId;
    private String ApplyUserParty;
    private String ApplyUserImage;
    private ApprovalNodes ApprovalNodes;
}
