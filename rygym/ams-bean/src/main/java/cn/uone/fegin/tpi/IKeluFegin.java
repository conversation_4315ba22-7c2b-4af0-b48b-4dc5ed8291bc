package cn.uone.fegin.tpi;

import cn.uone.web.base.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("ams-tpi")
public interface IKeluFegin {
    //电表读数
    @GetMapping("/Kelu/meterInfo")
    RestResponse meterInfo(@RequestParam("meterNo") String meterNo);

    //电表打开/关闭
    @GetMapping("/Kelu/ctrl")
    RestResponse ctrl(@RequestParam("meterNo") String meterNo, @RequestParam("status") String status);

}
