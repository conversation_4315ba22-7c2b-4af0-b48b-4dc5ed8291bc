package cn.uone.fegin.crm;

import cn.uone.bean.entity.crm.DeptEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ams-crm")
public interface IDeptFegin {

    @PostMapping("/dept/get")
    DeptEntity getDeptByCode(@RequestParam("code") String code);

}
