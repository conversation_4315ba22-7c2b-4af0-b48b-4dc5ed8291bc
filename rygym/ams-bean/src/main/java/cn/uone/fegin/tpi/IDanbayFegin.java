package cn.uone.fegin.tpi;

import cn.uone.web.base.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("ams-tpi")
public interface IDanbayFegin {
    //水电表读数
    @GetMapping("/Danbay/getRead")
    RestResponse getRead(@RequestParam("code") String code) throws Exception;

    //水电表打开/关闭
    @GetMapping("/Danbay/openClose")
    RestResponse openClose(@RequestParam("code") String code, @RequestParam("action") String action) throws Exception;

    /**
     * 门锁授权
     */
    @PostMapping("/Danbay/authorization")
    RestResponse authorization(@RequestParam("code") String code, @RequestParam("password") String password, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) throws Exception;

    //门锁冻结
    @PostMapping("/Danbay/frozen")
    RestResponse frozen(@RequestParam("code") String code, @RequestParam("pwdId") String pwdId) throws Exception;

    //临时密码
    @PostMapping("/Danbay/getTempPassword")
    RestResponse getTempPassword(@RequestParam("code") String code) throws Exception;

    @PostMapping("/Danbay/getLockRecords")
    RestResponse getLockRecords(@RequestParam("code")String code, @RequestParam("beginDate")String beginDate,@RequestParam("endDate")String endDate) throws Exception;
}
