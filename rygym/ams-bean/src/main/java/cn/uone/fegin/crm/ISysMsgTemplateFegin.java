package cn.uone.fegin.crm;

import cn.uone.bean.entity.crm.SysMsgTemplateEntity;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by xmlin on 2018-12-21.
 */
@FeignClient("ams-crm")
public interface ISysMsgTemplateFegin {

    /**
     * 页面查询
     *
     * @param page
     * @return
     */
    @RequestMapping("/sys/sms/queryIPage")
    RestResponse queryIPage(Page page);

    @RequestMapping("/sys/sms/query")
    RestResponse query(@RequestParam(value = "id", required = false) String id);

    @PostMapping("/sys/sms/sendByOs")
    void sendByOs(@RequestParam("mobile")String mobile,@RequestParam("smsCode")String smsCode,@RequestParam("paramsJson") String paramsJson) throws Exception;
    @PostMapping("/sys/sms/sendByProjectId")
    void sendByProjectId(@RequestParam("projectId")String projectId,@RequestParam("smsCode")String smsCode,@RequestParam("mobile")String mobile,@RequestParam("paramsJson") String paramsJson) throws Exception;
    @PostMapping("/sys/sms/sendBySysCompanyId")
    void sendBySysCompanyId(@RequestParam("sysCompanyId")String sysCompanyId,@RequestParam("smsCode")String smsCode,@RequestParam("mobile")String mobile,@RequestParam("paramsJson") String paramsJson) throws Exception;
    @PostMapping("/sys/sms/sendByQysCompanyId")
    void sendByQysCompanyId(@RequestParam("qysCompanyId")String qysCompanyId,@RequestParam("smsCode")String smsCode,@RequestParam("mobile")String mobile,@RequestParam("paramsJson") String paramsJson) throws Exception;


    @RequestMapping("/sys/sms/getByCode")
    SysMsgTemplateEntity getByCode(@RequestParam("code") String code);

    @RequestMapping("/sys/sms/getEntity")
    SysMsgTemplateEntity getEntity(@RequestParam("code")String code);

    @RequestMapping("/queryList")
    RestResponse queryList(Page page) throws Exception;
}
