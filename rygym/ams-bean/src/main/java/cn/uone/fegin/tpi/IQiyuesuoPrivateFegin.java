package cn.uone.fegin.tpi;

import cn.uone.bean.entity.business.qys.QysAuthuserEntity;
import cn.uone.bean.entity.tpi.qiyuesuo.SignContractVo;
import cn.uone.web.base.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("ams-tpi")
public interface IQiyuesuoPrivateFegin {

    @PostMapping("/qiyuesuoPrivate/getUserAuthurl")
    RestResponse getUserAuthurl(@RequestParam("mobile") String mobile,@RequestParam("name")String name,
                                @RequestParam("cardNo")String cardNo,@RequestParam("authMode")String authMode);

    @GetMapping("/qiyuesuoPrivate/createDocument")
    RestResponse createDocument(@RequestParam("url")String url,@RequestParam("title")String title);

    @GetMapping("/qiyuesuoPrivate/createContract")
    RestResponse createContract(@RequestBody SignContractVo signContractVo);

    /**
     * 获取签署页面
     * @param contractId
     * @param documentId
     * @param tenantName
     * @param tenantType
     * @param actionType
     * @param actionName
     * @param operatorContact
     * @param serialNo
     * @return
     */
    @PostMapping("/qiyuesuoPrivate/getSignContractPageUrl")
    RestResponse getSignContractPageUrl(@RequestParam("contractId")Long contractId,@RequestParam("documentId")Long documentId,
                                        @RequestParam("tenantName")String tenantName,@RequestParam("tenantType")String tenantType,
                                        @RequestParam("actionType")String actionType,@RequestParam("actionName")String actionName,
                                        @RequestParam("operatorContact")String operatorContact,@RequestParam("serialNo") Long serialNo);

    @PostMapping("/qiyuesuoPrivate/downloadContract")
    byte[] downloadContract(@RequestParam("contractId")Long contractId);

    @PostMapping("/qiyuesuoPrivate/downloadDocument")
    void downloadDocument(@RequestParam("documentId")Long documentId);

    @PostMapping("/qiyuesuoPrivate/cancelContract")
    RestResponse cancelContract(@RequestParam("contractId") Long contractId,@RequestParam("reason") String reason);

    @PostMapping("/qiyuesuoPrivate/voidContract")
    RestResponse voidContract(@RequestParam("contractId") Long contractId,@RequestParam("reason") String reason);

    /**
     * 获取单位印章静默签署授权链接
     * @param authuser
     * @return
     */
    @PostMapping("/qiyuesuoPrivate/getCompanyAuthSignUrl")
    RestResponse getCompanyAuthSignUrl(@RequestBody QysAuthuserEntity authuser);

    /**
     * 法人单位静默签署
     * @param contractId
     * @param documentId
     * @param sealId
     * @param companyName
     * @param operatorMobile
     * @return
     */
    @PostMapping("/qiyuesuoPrivate/signContractByCompany")
    RestResponse signContractByCompany(@RequestParam("contractId")Long contractId,@RequestParam("documentId")Long documentId,@RequestParam("sealId")String sealId,@RequestParam("companyName")String companyName,@RequestParam("operatorMobile")String operatorMobile);

}
