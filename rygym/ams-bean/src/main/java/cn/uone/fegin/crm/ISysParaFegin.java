package cn.uone.fegin.crm;

import cn.uone.bean.entity.crm.SysParaEntity;
import cn.uone.web.base.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Created by xmlin on 2018-12-21.
 */
@FeignClient("ams-crm")
public interface ISysParaFegin {

    @RequestMapping("/sys/para/saveOrUpdate")
    RestResponse saveOrUpdate(@RequestBody SysParaEntity entity);

    /**
     * 获取paramCode
     *
     * @param code
     * @return
     */
    @RequestMapping("/sys/para/getByCode")
    String getByCode(@RequestParam("code") String code);

    @RequestMapping("/sys/para/getSysParaByCode")
    SysParaEntity getSysParaByCode(@RequestParam("code") String code);



}
