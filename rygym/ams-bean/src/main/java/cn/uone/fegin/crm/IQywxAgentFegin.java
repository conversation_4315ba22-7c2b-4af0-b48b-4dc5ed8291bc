package cn.uone.fegin.crm;

import cn.uone.bean.entity.crm.QywxAgentEntity;
import cn.uone.shiro.bean.UonePermissions;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "ams-crm")
public interface IQywxAgentFegin {

    @RequestMapping(value = "/qywx-agent-entity/getByUserId")
    QywxAgentEntity getByUserId(@RequestParam("userId") String userId);

    @PostMapping("/qywx-agent-entity/getByUserIds")
    List<QywxAgentEntity> getByUserIds(@RequestBody List<String> userIds);
}
