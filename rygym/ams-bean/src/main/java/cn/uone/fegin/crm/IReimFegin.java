package cn.uone.fegin.crm;

import cn.uone.bean.entity.crm.reit.ReitReimbursementEntity;
import cn.uone.bean.entity.crm.reit.vo.ReitReimbursementSearchVo;
import cn.uone.web.base.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ams-crm")
public interface IReimFegin {


    @RequestMapping("/reimbursement/getReit")
    ReitReimbursementEntity getReit(@RequestParam("id") String id);


    @RequestMapping("/reimbursement/pageList2")
    RestResponse pageList(@RequestBody ReitReimbursementSearchVo vo);

}
