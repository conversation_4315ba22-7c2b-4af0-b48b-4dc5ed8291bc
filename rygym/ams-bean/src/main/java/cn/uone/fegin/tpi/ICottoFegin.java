package cn.uone.fegin.tpi;

import cn.hutool.json.JSONObject;
import cn.uone.bean.entity.tpi.Cotto.CottoCarVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 */
@FeignClient("ams-tpi")
public interface ICottoFegin {

    @RequestMapping(value = "/cotto/addCarCardNo", method = RequestMethod.GET)
    JSONObject addCarCardNo(@RequestBody CottoCarVo carVo);

    @RequestMapping(value = "/cotto/modifyCarCardNo", method = RequestMethod.GET)
    JSONObject modifyCarCardNo(@RequestBody CottoCarVo carVo);

    @RequestMapping(value = "/cotto/payCarCardFee", method = RequestMethod.GET)
    JSONObject payCarCardFee(@RequestBody CottoCarVo carVo);

    @RequestMapping(value = "/cotto/modifyCardStatus", method = RequestMethod.GET)
    JSONObject modifyCardStatus(@RequestParam("cardId") String cardId,@RequestParam("flag") String flag);

    @RequestMapping(value = "/cotto/getCarCardInfo", method = RequestMethod.GET)
    JSONObject getCarCardInfo(@RequestParam("plateNo") String plateNo,@RequestParam("state") Integer state) throws Exception;
}
