package cn.uone.fegin.crm;

import cn.uone.bean.entity.crm.SysMsgTemplateEntity;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 从漳州城投工程迁移过来的
 * Created by xmlin on 2018-12-21.
 * caizhanghe edit 2024-05-22
 */
@FeignClient("ams-crm")
public interface IZzctSysMsgTemplateFegin {


    @RequestMapping("/zzct/sys/sms/send")
    void send(@RequestBody Map<String, Object> params) throws Exception;

}
