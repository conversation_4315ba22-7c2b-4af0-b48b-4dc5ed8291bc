package cn.uone.fegin.bus;

import cn.uone.web.base.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Created by xmlin on 2019-01-09.
 */
@FeignClient("ams-business")
public interface IDevPropertyDeviceFegin {

    @RequestMapping(value = "/dev-property-device-entity/changeTelToXt", method = RequestMethod.POST)
    RestResponse changeTelToXt(@RequestParam("renterId") String renterId, @RequestParam("tel") String newTel, @RequestParam("newTel") String tel) throws Exception;
}
