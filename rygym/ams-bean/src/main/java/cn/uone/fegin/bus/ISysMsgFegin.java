package cn.uone.fegin.bus;


import cn.uone.web.base.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 发送短信服务
 */
@FeignClient("ams-business")
public interface ISysMsgFegin {

    /***
     * 发送短信验证码
     * @param tel
     * @param code
     * @return
     */
    @PostMapping("/sys/sms/sendCode")
    RestResponse sendCode(@RequestParam("tel") String tel, @RequestParam("code") String code) throws Exception;

    @PostMapping("/sys/sms/sendRegInfo")
    RestResponse sendRegInfo(@RequestParam("tel") String tel, @RequestParam("name") String name, @RequestParam("username") String username, @RequestParam("password") String password) throws Exception;

    @PostMapping("/sys/sms/sendCustomerRegInfo")
    RestResponse sendCustomerRegInfo(@RequestParam("tel") String tel, @RequestParam("name") String name, @RequestParam("username") String username, @RequestParam("password") String password, @RequestParam("type") String type) throws Exception;
}
