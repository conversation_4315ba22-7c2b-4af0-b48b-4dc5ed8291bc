package cn.uone.fegin.tpi.cosmic;

import cn.uone.bean.entity.tpi.cosmic.BasePojo;
import cn.uone.bean.entity.tpi.cosmic.reimbursement.ReimbursementSaveDto;
import cn.uone.web.base.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 金蝶 报账工单
 */
@FeignClient("ams-tpi")
public interface IReimbursementFeign {

    /**
     * 报账工单保存
     *
     * @param dtoList 保存对象实体列表
     * @return BaseSaveVo
     */
    @PostMapping("/cosmic/reimbursement/save")
    RestResponse reimbursementSave(@RequestBody List<ReimbursementSaveDto> dtoList);

    /**
     * 报账工单反写查询
     *
     * @param base       来源三要素
     * @param modifyTime 更新时间
     * @return 见 ReimbursementApi.reimbursementRewriteQuery()的方法注释
     */
    @PostMapping("/cosmic/reimbursement/rewriteQuery")
    RestResponse reimbursementRewriteQuery(@RequestBody BasePojo base, @RequestParam("modifyTime") String modifyTime);
}
