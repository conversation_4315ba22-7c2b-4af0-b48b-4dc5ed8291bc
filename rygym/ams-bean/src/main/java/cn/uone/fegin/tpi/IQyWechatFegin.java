package cn.uone.fegin.tpi;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.uone.bean.parameter.CheckInUserPo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient("ams-tpi")
public interface IQyWechatFegin {

    @RequestMapping(value = "/qyWechat/getDepartments", method = RequestMethod.GET)
    JSONArray getDepartments(@RequestParam("rootId") String rootId);

    @RequestMapping(value = "/qyWechat/getXieZuoDepartments", method = RequestMethod.GET)
    JSONArray getXieZuoDepartments(@RequestParam("rootId") String rootId);

    @RequestMapping(value = "/qyWechat/sendMessage", method = RequestMethod.GET)
    boolean sendMessage(@RequestParam("qywechatid") String qywechatid, @RequestParam("text") String text);

    @RequestMapping(value = "/qyWechat/getuserinfo", method = RequestMethod.GET)
    String getuserinfo(@RequestParam("code") String code,@RequestParam(value="systemCode",required = false) String systemCode, @RequestParam(value = "isNotApp", required = false) Boolean isNotApp);

    @RequestMapping(value = "/qyWechat/getTagList", method = RequestMethod.GET)
    JSONArray getTagList();

    @RequestMapping(value = "/qyWechat/getUsersByDeptId", method = RequestMethod.GET)
    JSONArray getUsersByDeptId(@RequestParam("deptId") String deptId, @RequestParam("isAll") Boolean isAll);

    @RequestMapping(value = "/qyWechat/getLxDoc", method = RequestMethod.GET)
    Map<String, Object> getLxDoc(@RequestParam("docId") String docId);

    @RequestMapping(value = "/qyWechat/getLxDocList", method = RequestMethod.GET)
    List<Map<String, Object>> getLxDocList(@RequestParam("categoryId") String categoryId) throws Exception;

    @RequestMapping(value = "/qyWechat/getTagUsers", method = RequestMethod.GET)
    JSONArray getTagUsers(@RequestParam("tagId") String tagId);

    @RequestMapping(value = "/qyWechat/getLoginUrl", method = RequestMethod.GET)
    String getLoginUrl();

    @RequestMapping(value = "/qyWechat/getProLoginUrl", method = RequestMethod.GET)
    String getProLoginUrl();

    @RequestMapping(value = "/qyWechat/getPcLoginUrl", method = RequestMethod.GET)
    String getPcLoginUrl();

    @RequestMapping(value = "/qyWechat/getProPcLoginUrl", method = RequestMethod.GET)
    String getProPcLoginUrl();
    @RequestMapping(value = "/qyWechat/getULoginUrl", method = RequestMethod.GET)
    String getULoginUrl();

    @RequestMapping(value = "/qyWechat/getBackUrl", method = RequestMethod.GET)
    String getBackUrl();

    @RequestMapping(value = "/qyWechat/getProBackUrl", method = RequestMethod.GET)
    String getProBackUrl();

    @RequestMapping(value = "/qyWechat/getUBackUrl", method = RequestMethod.GET)
    String getUBackUrl();

    @RequestMapping(value = "/qyWechat/getopenapprovaldata", method = RequestMethod.GET)
    JSONObject getopenapprovaldata(@RequestParam("thirdNo") String thirdNo);

    @RequestMapping(value = "/qyWechat/getCheckInUsers", method = RequestMethod.GET)
    JSONArray getCheckInUsers(@RequestBody CheckInUserPo po);

    /**
     * 验证服务器url
     * @param msg_signature
     * @param timestamp
     * @param nonce
     * @param echostr
     */
    @RequestMapping(value = "/qyWechat/checkUrl", method = RequestMethod.GET)
    String checkUrl(@RequestParam("msg_signature") String msg_signature, @RequestParam("timestamp") String timestamp, @RequestParam("nonce") String nonce, @RequestParam("echostr") String echostr) throws Exception;

    /**
     * 回调处理
     * @param msg_signature
     * @param timestamp
     * @param nonce
     * @param str
     */
    @RequestMapping(value = "/qyWechat/callback", method = RequestMethod.GET)
    public void callback(@RequestParam("msg_signature") String msg_signature,@RequestParam("timestamp") String timestamp,@RequestParam("nonce") String nonce,@RequestParam("str") String str) throws Exception;

    @RequestMapping(value = "/qyWechat/sign")
    public Map<String,String> sign(@RequestParam("url") String url);

    @RequestMapping(value = "/qyWechat/agentSign")
    public Map<String,String> agentSign(@RequestParam("url") String url);

    @RequestMapping(value = "/qyWechat/externalContactSign")
    public Map<String,String> externalContactSign(@RequestParam("url") String url);

    @RequestMapping(value = "/qyWechat/externalContactAgentSign")
    public Map<String,String> externalContactAgentSign(@RequestParam("url") String url);


    @RequestMapping(value = "/qyWechat/getCustomInfo")
    JSONObject getCustomInfo(@RequestParam("externalUserid") String externalUserid);

    @RequestMapping(value = "/qyWechat/getFollowUserList")
    JSONArray getFollowUserList();

    @RequestMapping(value = "/qyWechat/getCutomers")
    JSONArray getCutomers(@RequestParam("qywehat") String qywehat);

    @RequestMapping(value = "/qyWechat/getTabs")
    JSONArray getTabs();

    @RequestMapping(value = "/qyWechat/remarkCustomer")
    void remarkCustomer(@RequestParam("userid")String userid,@RequestParam("externalUserid")String externalUserid,@RequestParam("remark")String remark);

    @RequestMapping(value = "/qyWechat/addTabsCustomer")
    void addTabsCustomer(@RequestParam("userid")String userid,@RequestParam("externalUserid")String externalUserid,@RequestParam("addTag") String addTag );

    @RequestMapping(value = "/qyWechat/removeTabsCustomer")
    void removeTabsCustomer(@RequestParam("userid")String userid,@RequestParam("externalUserid")String externalUserid,@RequestParam("removeTag") String removeTag);


    @RequestMapping(value = "/qyWechat/getTemplateInfo")
    JSONObject getTemplateInfo(@RequestParam("templateId") String templateId);
}
