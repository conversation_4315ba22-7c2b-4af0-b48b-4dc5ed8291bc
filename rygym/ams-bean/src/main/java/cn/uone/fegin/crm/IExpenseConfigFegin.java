package cn.uone.fegin.crm;

import cn.hutool.json.JSONObject;
import cn.uone.bean.entity.crm.ExpenseProjectEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ams-crm")
public interface IExpenseConfigFegin {

    @RequestMapping(value = "/expense-config-entity/getExpenseConfigByPro")
    JSONObject getExpenseConfigByPro(@RequestParam("expenseProjectId") String expenseProjectId, @RequestParam("configType")String configType);

    @RequestMapping(value = "/expense-config-entity/getExpenseConfigByCode")
    JSONObject getExpenseConfigByCode(@RequestParam("code") String code, @RequestParam("configType")String configType);

    @RequestMapping(value = "/expense-config-entity/getExpenseConfigByNotifyId")
    JSONObject getExpenseConfigByNotifyId(@RequestParam("notifyId") String notifyId, @RequestParam("configType") String configType);

}
