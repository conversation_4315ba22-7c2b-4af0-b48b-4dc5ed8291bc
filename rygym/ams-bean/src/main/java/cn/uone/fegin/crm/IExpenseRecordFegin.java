package cn.uone.fegin.crm;

import cn.uone.web.base.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ams-crm")
public interface IExpenseRecordFegin {

    @RequestMapping(value = "/t-expense-record-entity/addRecord")
    RestResponse addRecord(@RequestParam("expenseProjectId") String expenseProjectId,@RequestParam("expenseType") String expenseType,@RequestParam("recordId") String recordId);
}
