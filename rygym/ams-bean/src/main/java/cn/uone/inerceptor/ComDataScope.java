package cn.uone.inerceptor;

import cn.uone.bean.entity.crm.SysCompanyEntity;

import java.util.HashMap;
import java.util.List;

/**
 * @ClassName CompanyDataScope
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/16 14:03
 * @Version 1.0
 */
public class ComDataScope extends HashMap {
    private String userId = "";
    private String comFieldName = "company_id";
    private String comAlias = "";
    private String ids = "";

    public ComDataScope() {
    }
    public ComDataScope(String userId) {
        this.userId = userId;
    }

    public static ComDataScope newDataScope(String userId) {
        return new ComDataScope(userId);
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getComFieldName() {
        return comFieldName;
    }

    public void setComFieldName(String comFieldName) {
        this.comFieldName = comFieldName;
    }

    public String getComAlias() {
        return comAlias;
    }

    public void setComAlias(String comAlias) {
        this.comAlias = comAlias;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ComDataScope)) {
            return false;
        } else {
            ComDataScope other = (ComDataScope)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label71: {
                    Object this$userId = this.getUserId();
                    Object other$userId = other.getUserId();
                    if (this$userId == null) {
                        if (other$userId == null) {
                            break label71;
                        }
                    } else if (this$userId.equals(other$userId)) {
                        break label71;
                    }

                    return false;
                }

                Object this$comFieldName = this.getComFieldName();
                Object other$comFieldName = other.getComFieldName();
                if (this$comFieldName == null) {
                    if (other$comFieldName != null) {
                        return false;
                    }
                } else if (!this$comFieldName.equals(other$comFieldName)) {
                    return false;
                }

                Object this$comAlias = this.getComAlias();
                Object other$comAlias = other.getComAlias();
                if (this$comAlias == null) {
                    if (other$comAlias != null) {
                        return false;
                    }
                } else if (!this$comAlias.equals(other$comAlias)) {
                    return false;
                }

                return false;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ComDataScope;
    }

    public int hashCode() {
        int result = 1;
        Object $userId = this.getUserId();
        result = result * 59 + ($userId == null ? 43 : $userId.hashCode());
        Object $comFieldName = this.getComFieldName();
        result = result * 59 + ($comFieldName == null ? 43 : $comFieldName.hashCode());
        Object $comAlias = this.getComAlias();
        result = result * 59 + ($comAlias == null ? 43 : $comAlias.hashCode());
        return result;
    }

    public String toString() {
        return "ComDataScope(userId=" + this.getUserId() + ", comFieldName=" + this.getComFieldName()  + ", comAlias=" + this.getComAlias() + ")";
    }
}
