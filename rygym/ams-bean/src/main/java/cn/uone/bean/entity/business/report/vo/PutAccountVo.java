package cn.uone.bean.entity.business.report.vo;

import cn.uone.application.enumerate.order.InvoiceStateEnum;
import cn.uone.bean.entity.business.report.ReportPutAccountEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PutAccountVo extends ReportPutAccountEntity {

    private String sourceName;

    private String sourceType;

    private BigDecimal area;

    private String invoiceStr;

    private Date arriveDate;

    private String invoiceState;


    public String getInvoiceStr() {

        return InvoiceStateEnum.getNameByValue(invoiceState);
    }

}
