package cn.uone.bean.entity.business.kingdee;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_kingdee_transfer")
public class KingdeeTransferEntity extends BaseModel<KingdeeTransferEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 单据编号
     */
    @TableField("number")
    private String number;

    /**
     * 公司
     */
    @TableField("company")
    private String company;

    /**
     * 期间
     */
    @TableField("period")
    private String period;

    /**
     * 项目段
     */
    @TableField("project")
    private String project;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 来源账单id
     */
    @TableField("order_id")
    private String orderId;

    @TableField("kingdee_id")
    private String kingdeeId;

    @TableField(value="company_name",exist = false)
    private String companyName;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
