package cn.uone.bean.entity.business.cont;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cont_talent_check")
public class ContTalentCheckEntity extends BaseModel<ContTalentCheckEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;

    /**
     * 申请中id ,租户id
     */
    @TableField("rent_id")
    private String rentId;

    /**
     * 性别
     */
    @TableField("sex")
    private String sex;

    /**
     * 电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 证件号码
     */
    @TableField("id_no")
    private String idNo;

    /**
     * 省
     */
    @TableField("province_id")
    private String provinceId;

    /**
     * 市
     */
    @TableField("city_id")
    private String cityId;

    /**
     * 县
     */
    @TableField("district_id")
    private String districtId;

    /**
     * 职务
     */
    @TableField("job")
    private String job;

    /**
     * 婚姻状况
     */
    @TableField("marriage")
    private String marriage;

    /**
     * 意向户型
     */
    @TableField("house_type")
    private String houseType;

    /**
     * 工作单位
     */
    @TableField("work_unit")
    private String workUnit;

    /**
     * 职称
     */
    @TableField("title")
    private String title;

    /**
     * 学历
     */
    @TableField("education")
    private String education;

    /**
     * 不通过原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 审核结果
     *  0-未通过
     *  1-已通过
     */
    @TableField("result")
    private String result;

    /**
     * 审核状态
     * 0-待审核
     * 1-已审核
     */
    @TableField("state")
    private String state;

    /***
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "apply_date")
    private Date applyDate;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
