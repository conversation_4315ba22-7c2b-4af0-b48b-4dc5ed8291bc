package cn.uone.bean.entity.business.supplies.vo;

import cn.uone.bean.entity.business.supplies.CategoryEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by xmlin on 2018-12-15.
 */
@Data
public class CategoryVo extends CategoryEntity implements Serializable {

    /**
     * 采购 领用 物品名称
     */
    private String itemName;

    /**
     * 采购 领用 数量
     */
    private Integer quantity ;

    /**
     *  申请日期
     */
    private Date applyTime ;

    /**
     *  当前审核人
     */
    private String auditor ;

    /**
     * 审核日期
     */
    private Date auditTime ;

    /**
     *  当前状态 审核通过 失败 待审
     */
    private String states ;

    /**
     * 采购 领用 类型
     */
    private String types ;



}
