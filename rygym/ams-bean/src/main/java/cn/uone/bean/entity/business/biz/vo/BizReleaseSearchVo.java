package cn.uone.bean.entity.business.biz.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by xmlin on 2018-12-15.
 */
@Data
public class BizReleaseSearchVo implements Serializable {

    private String type;//类型 参见 ReleaseType枚举

    /**
     * 租客
     */
    private String userId;
    /**
     * 申请单状态
     */
    private String state;
    /**
     * 关键字
     */
    private String keyWord;
    /**
     * 合同类型
     */
    private String contractType;
    /**
     * 是否交易完成
     */
    private String done;
    /**
     * 是否机构
     */
    private String org;

    private String typesStr;

    /**
     * 查询条件    多个项目id , 分隔
     */
    private String projectIds;


    /**
     * 查询条件 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginApplyDate;

    /**
     * 查询条件 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endApplyDate;

    /**
     * 查询条件    进度，多个用  ， 分隔
     */
    private String states;

    /**
     * 是否交易完成
     *
     *
     *  0费用未结清        对应查询结果-----》》   unpaidNum>0 && state in 5,7
     *  1费用已结清        对应查询结果-----》》   unpaidNum=0 && state in 5,7
     *  2费用未生成        对应查询结果-----》》   state in ('0','1','2','3','4')
     *
     */
    private String dones;

    private String isFrame;




}



