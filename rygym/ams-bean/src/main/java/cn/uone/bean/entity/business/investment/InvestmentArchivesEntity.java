package cn.uone.bean.entity.business.investment;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import cn.uone.web.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 招商档案
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_investment_archives")
public class InvestmentArchivesEntity extends BaseModel<InvestmentArchivesEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 年度
     */
    @TableField("year")
    private String year;

    /**
     * 季度
     */
    @TableField("quarter")
    private String quarter;

    /**
     * 季度目标
     */
    @TableField("quarterly_target")
    private String quarterlyTarget;

    /**
     * 季度收益
     */
    @TableField("quarterly_revenue")
    private String quarterlyRevenue;

    /**
     * 季度投入
     */
    @TableField("quarterly_investment")
    private String quarterlyInvestment;

    /**
     * 广告投入
     */
    @TableField("advertising_investment")
    private String advertisingInvestment;

    /**
     * 其他费用投入
     */
    @TableField("other_investment")
    private String otherInvestment;

    /**
     * 进度
     */
    @TableField("progress")
    private String progress;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    @Override
    protected Serializable pkVal() {
        return id;
    }

}
