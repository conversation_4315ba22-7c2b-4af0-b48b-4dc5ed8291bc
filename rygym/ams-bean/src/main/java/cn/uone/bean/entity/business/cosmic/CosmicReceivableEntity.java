package cn.uone.bean.entity.business.cosmic;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDate;
import cn.uone.bean.entity.base.BaseModel;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应收单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cosmic_receivable")
public class CosmicReceivableEntity extends BaseModel<CosmicReceivableEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 来源系统（业务系统标识）
     */
    @TableField("xmgd_sourcesystem")
    private String xmgdSourcesystem;

    /**
     * 来源单据类型（业务系统单据类型）
     */
    @TableField("xmgd_sourcetype")
    private String xmgdSourcetype;

    /**
     * 来源单据编码（业务系统单据id）
     */
    @TableField("xmgd_sourcenumber")
    private String xmgdSourcenumber;

    /**
     * 结算组织（业务发生的组织，传组织guid或组织id）
     */
    @TableField("org_number")
    private String orgNumber;

    /**
     * 单据类型.编码（默认：arfin_other_BT_S（财务应收））
     */
    @TableField("billtype_number")
    private String billtypeNumber;

    /**
     * 单据日期（确认应收款项的日期）
     */
    @TableField("bizdate")
    private LocalDate bizdate;

    /**
     * 发票日期（实际开票日期）
     */
    @TableField("invoicedate")
    private LocalDate invoicedate;

    /**
     * 到期日（往来双方约定的应收款项最晚收款日期）
     */
    @TableField("duedate")
    private LocalDate duedate;

    /**
     * 往来类型 bd_customer:客户, bd_supplier:供应商, bos_user:人员（往来户的归类。分为供应商、客户、人员）
     */
    @TableField("asstacttype")
    private String asstacttype;

    /**
     * 往来户.编码（与公司发生往来业务的单位或个人）
     */
    @TableField("asstact_number")
    private String asstactNumber;

    /**
     * 付款方式 CASH:现销, CREDIT:赊销
     */
    @TableField("paymode")
    private String paymode;

    /**
     * 部门.编码（发生应收业务的部门，传guid或组织id）
     */
    @TableField("department_number")
    private String departmentNumber;

    /**
     * 创建时间
     */
    @TableField("createtime")
    private LocalDateTime createtime;

    /**
     * 修改时间
     */
    @TableField("modifytime")
    private LocalDateTime modifytime;

    /**
     * 创建人.工号
     */
    @TableField("creator_number")
    private String creatorNumber;

    /**
     * 修改人.工号
     */
    @TableField("modifier_number")
    private String modifierNumber;

    /**
     * 结算方式
     */
    @TableField("settlementtype_number")
    private String settlementtypeNumber;

    /**
     * 凭证类型
     */
    @TableField("xmgd_vouchertype_number")
    private String xmgdVouchertypeNumber;

    /**
     * 账单开始日期
     */
    @TableField("xmgd_startdate")
    private LocalDate xmgdStartdate;

    /**
     * 账单结束日期
     */
    @TableField("xmgd_finishdate")
    private LocalDate xmgdFinishdate;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 返回结果
     */
    @TableField("return_result")
    private String returnResult;

    /**
     * 结算组织名称
     */
    @TableField("org_name")
    private String orgName;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
