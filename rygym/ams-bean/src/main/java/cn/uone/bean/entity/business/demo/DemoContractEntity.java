package cn.uone.bean.entity.business.demo;


import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_rpt_contract")
public class DemoContractEntity extends BaseModel<DemoContractEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("contract_code")
    private String contractCode;

    @TableField("project_id")
    private String projectId;

    @TableField(exist = false)
    private String projectName;

    @TableField("source_id")
    private String sourceId;


    @TableField(exist = false)
    private String roomCode;

    @TableField("owner_id")
    private String ownerId;
    @TableField(exist = false)
    private String ownerName;

    @TableField("start_time")
    private Date startTime;

    @TableField("end_time")
    private Date endTime;

    @TableField("price")
    private BigDecimal price;

    @TableField("pay_type")
    private String payType;



    @TableField("state")
    private String state;

    @TableField("file_url")
    private String fileUrl;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
