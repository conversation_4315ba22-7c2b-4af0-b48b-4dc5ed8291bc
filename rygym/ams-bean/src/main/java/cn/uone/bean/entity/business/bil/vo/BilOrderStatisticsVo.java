package cn.uone.bean.entity.business.bil.vo;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 统计账单总额
 * caizhanghe edit 2024-07-03
 */
@Data
public class BilOrderStatisticsVo {

    //统计账单金额
    private BigDecimal totalPayment;

    //统计账单应付金额
    private BigDecimal totalPayablePayment;

    //统计账单实付金额
    private BigDecimal totalActualPayment;
}
