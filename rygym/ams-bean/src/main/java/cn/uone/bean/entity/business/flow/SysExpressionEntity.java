package cn.uone.bean.entity.business.flow;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 流程表达式
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_expression")
public class SysExpressionEntity extends BaseModel<SysExpressionEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 表达式名称
     */
    @TableField("name")
    private String name;

    /**
     * 表达式内容
     */
    @TableField("expression")
    private String expression;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
