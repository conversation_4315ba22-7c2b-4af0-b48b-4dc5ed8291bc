package cn.uone.bean.entity.business.bil;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 价格策略指定月份明细表实体类对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_price_month_detail")
public class PriceMonthDetailEntity extends BaseModel<PriceMonthDetailEntity> {

    private static final long serialVersionUID = 1L;

    /**
     *  价格策略id
     */
    @TableField("price_strategy_id")
    private String priceStrategyId;

    /**
     *  项目门店id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 月份顺序号
     */
    @TableField("month_sequ")
    private int monthSequ;

    /**
     * 优惠金额
     */
    @TableField("fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 1 生效 0 失效
     */
    @TableField("status")
    private String status;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
