package cn.uone.bean.entity.business.base;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_base_account")
public class BaseAccountEntity extends BaseModel<BaseAccountEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("bank_id")
    private String bankId;

    @TableField("branch_id")
    private String branchId;

    @TableField("branch_name")
    private String branchName;

    @TableField("name")
    private String name;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 编号
     */
    @TableField("code")
    private String code;

    /**
     * 子商户号
     */
    @TableField("merchant_id")
    private String merchantId;

    /**
     * 账号类型
     */
    @TableField("account_type")
    private String accountType;

    /**
     * 状态
     */
    @TableField("state")
    private String state;

    /**
     * 公司编码
     */
    @TableField("company_code")
    private String companyCode;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
