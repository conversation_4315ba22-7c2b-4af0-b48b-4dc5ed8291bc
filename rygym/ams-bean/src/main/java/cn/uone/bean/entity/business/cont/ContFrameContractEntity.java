package cn.uone.bean.entity.business.cont;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cont_frame_contract")
public class ContFrameContractEntity extends BaseModel<ContFrameContractEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 主合同编号
     */
    @TableField("code")
    private String code;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 合同模板id
     */
    @TableField("contract_templet_id")
    private String contractTempletId;


    /**
     * 主合同类型
     */
    @TableField("type")
    private String type;

    /**
     * 签约用户id
     */
    @TableField("signer_id")
    private String signerId;

    /**
     * 租凭起日
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 租凭止日
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 租金支付方
     */
    @TableField("rent_pay_payer")
    private String rentPayPayer;

    /**
     * 能耗费用支付方
     */
    @TableField("life_pay_payer")
    private String lifePayPayer;
    /**
     * 固耗费用支付方
     */
    @TableField("fix_life_pay_payer")
    private String fixLifePayPayer;

    /**
     * 签约时间
     */
    @TableField("sign_date")
    private Date signDate;

    /**
     * 主合同状态1有效  0终止 2待生效
     */
    @TableField("state")
    private String state;

    /**
     * 开票类型
     */
    @TableField("invoice_type")
    private String invoiceType;

    /**
     * 子合同模板id（没有子合同功能，所以原子合同功能的模板id整个主合同就只有一个）
     */
    @TableField(exist = false)
    private String templateId;

    /**
     * 子合同收费配置（没有子合同功能，所以原子合同功能的收费配置整个主合同统一用一个）
     */
    @TableField(exist = false)
    private String costConfigureId;
    /**
     * 子合同收费配置（没有子合同功能，所以原子合同功能的收费方式整个主合同统一用一个）
     */
    @TableField(exist = false)
    private String payType;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
