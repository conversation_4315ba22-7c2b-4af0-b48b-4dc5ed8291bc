package cn.uone.bean.entity.business.dev.vo;

import cn.uone.bean.entity.business.dev.DevDeviceLogEntity;

public class DevDeviceLogVo extends DevDeviceLogEntity {

    private String className;//设备类型
    private String projectName;//项目名称
    private String partitionName;//楼栋
    private String userName;//用户名
    private String roomName;//房间名
    private String code;//设备编号
    private String deviceType;//读数设备类型:1水，2电，3煤气
    private String did;//与原对象中的deviceId是同个字段，这里防止冲突
    private String partitionId;//区域id

    private String floor;//楼层

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }


    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getPartitionName() {
        return partitionName;
    }

    public void setPartitionName(String partitionName) {
        this.partitionName = partitionName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getPartitionId() {
        return partitionId;
    }

    public void setPartitionId(String partitionId) {
        this.partitionId = partitionId;
    }
}
