package cn.uone.bean.entity.business.rpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class RptRevenueDataVo {

    /**
     * 合同
     */
    private String contractCode;
    /**
     * 房间
     */
    private String sourceCode;

    /**
     * 区域名称
     */
    private String partitionName;
    /**
     * 客户姓名
     */
    private String name;

    private String contractId;

    private String sourceId;

    /**
     * 1：收款，2：冲减
     */
    private String type;

    /**
     * 账单类型
     */
    private String orderType;

    /**
     * 账单类型
     */
    private String orderItemType;

    /**
     * 租凭起日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 租凭止日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;


    /**
     * 合同总金额
     */
    private String total;

    /**
     * 年份
     */
    private String reportYear;

    /**
     * 一月 (含税金额)
     */
    private String jan;


    /**
     * 二月
     */
    private String feb;

    /**
     * 三月
     */
    private String mar;

    /**
     * 四月
     */
    private String apr;

    /**
     * 五月
     */
    private String may;

    /**
     * 六月
     */
    private String jun;

    /**
     * 七月
     */
    private String jul;


    /**
     * 八月
     */
    private String aug;

    /**
     * 九月
     */
    private String sept;


    /**
     * 十月
     */
    private String oct;


    /**
     * 十一月
     */
    private String nov;

    /**
     * 十二月
     */
    private String dece;

}
