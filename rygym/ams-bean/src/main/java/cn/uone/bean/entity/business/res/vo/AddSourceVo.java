package cn.uone.bean.entity.business.res.vo;

import cn.uone.bean.entity.business.res.ResSourceEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AddSourceVo extends ResSourceEntity {


    private BigDecimal price;

    private BigDecimal lowPrice;

    private BigDecimal deposit;

    private BigDecimal yearIncrease;

    private BigDecimal yearNum;

    //审批状态
    private String approvalState;
}