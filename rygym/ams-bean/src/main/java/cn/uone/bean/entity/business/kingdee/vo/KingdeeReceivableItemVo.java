package cn.uone.bean.entity.business.kingdee.vo;

import cn.uone.bean.entity.business.kingdee.KingdeeReceivableItemEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName KingdeeReceiptVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/2 11:14
 * @Version 1.0
 */
@Data
public class KingdeeReceivableItemVo extends KingdeeReceivableItemEntity implements Serializable {
    private String paymentTypeName;
    private String customerName;
    private String productName;
}
