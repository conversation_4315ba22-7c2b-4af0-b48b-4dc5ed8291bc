package cn.uone.bean.entity.business.xhcosmic;

import cn.uone.web.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 金蝶(星瀚)财务应收单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cosmic_income")
public class CosmicIncomeEntity extends BaseModel<CosmicIncomeEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 金蝶报账工单号
     */
    @TableField("bill_no")
    private String billNo;

    /**
     * 来源系统编码(本系统)
     */
    @TableField("source_system")
    private String sourceSystem;

    /**
     * 单据来源类型
     */
    @TableField("source_bill_type")
    private String sourceBillType;

    /**
     * 来源单据编码
     */
    @TableField("source_bill_number")
    private String sourceBillNumber;

    /**
     * 是否开发费用
     */
    @TableField("is_dev_free")
    private String isDevFree;

    /**
     * 结算组织的guid或者id
     */
    @TableField("org_number")
    private String orgNumber;

    /**
     * 单据类型
     */
    @TableField("bill_type_number")
    private String billTypeNumber;

    /**
     * 单据日期
     */
    @TableField("biz_date")
    private LocalDateTime bizDate;

    /**
     * 发票日期(实际开票日期)
     */
    @TableField("invoice_date")
    private LocalDateTime invoiceDate;

    /**
     * 到期日
     */
    @TableField("due_date")
    private LocalDateTime dueDate;

    /**
     * 往来类型 bd_customer:客户, bd_supplier:供应商, bos_user:人员（往来户的归类。分为供应商、客户、人员）
     */
    @TableField("asst_act_type")
    private String asstActType;

    /**
     * 往来户编码
     */
    @TableField("asst_act_number")
    private String asstActNumber;

    /**
     * 付款客户 统一社会信用代码
     */
    @TableField("payment_customer_id_number")
    private String paymentCustomerIdNumber;

    /**
     * CASH:现销, CREDIT:赊销
     */
    @TableField("pay_mode")
    private String payMode;

    /**
     * 部门 编码 应收业务的部门 guid或id
     */
    @TableField("department_number")
    private String departmentNumber;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private LocalDateTime modifyTime;

    /**
     * 创建人编号
     */
    @TableField("creator_number")
    private String creatorNumber;

    /**
     * 修改人编号
     */
    @TableField("modifier_number")
    private String modifierNumber;

    /**
     * 用于存储业务系统原单地址
     */
    @TableField("url")
    private String url;

    /**
     * 凭证类型 编码
     */
    @TableField("voucher_type_number")
    private String voucherTypeNumber;

    /**
     * 凭证号
     */
    @TableField("voucher_number")
    private String voucherNumber;

    /**
     * 账单开始日期
     */
    @TableField("start_date")
    private LocalDateTime startDate;

    /**
     * 账单结束日期
     */
    @TableField("finish_date")
    private LocalDateTime finishDate;

    /**
     * 结算方式编码
     */
    @TableField("settle_type_number")
    private String settleTypeNumber;

    /**
     * 报账日期
     */
    @TableField("book_date")
    private Date bookDate;

    /**
     * 交易明细ID（保留用于向后兼容）
     */
    @TableField("transaction_id")
    private String transactionId;

    /**
     * 是否已推送
     */
    @TableField("push")
    private boolean push;

    /**
     * 应收单明细
     */
    @TableField(exist = false)
    private List<CosmicIncomeItemEntity> items = Lists.newArrayList();
    
    /**
     * 关联的交易明细ID列表（多对多关系）
     */
    @TableField(exist = false)
    private List<String> transactionIds = Lists.newArrayList();


    @Override
    protected Serializable pkVal() {
        return id;
    }

}
