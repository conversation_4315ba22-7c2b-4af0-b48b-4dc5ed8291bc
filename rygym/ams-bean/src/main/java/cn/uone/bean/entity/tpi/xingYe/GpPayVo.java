package cn.uone.bean.entity.tpi.xingYe;

import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2022-12-02 14:21
 **/
@Data
public class GpPayVo {
    //yyyyMMddHHmmss
    private String orderDate;
    // order_no是商户订单号，由商户系统生成，应当注意订单号在商户系统中应当全局唯一，即不会出现两笔订单有相同的订单号
    private String orderNo;
    // remote_ip为用户IP地址（客户端），其它参数含义同上，可以从HttpSevletRequest中获取（注意是jsp或servlet里的）:
    // 这里作为示例，直接使用一个定值（实际使用时不能写死）：持卡人IP
    private String orderIp;
    //支付金额
    private String orderAmount;
    //订单标题
    private String orderTitle;
    //订单详情
    private String orderDesc;

}
