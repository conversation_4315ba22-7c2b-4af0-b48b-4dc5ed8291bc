package cn.uone.bean.entity.business.fixed.vo;

import cn.uone.web.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * RFID标签
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
public class FixedRfidVo {

    private String code;//编码
    private String labelNorms;//标签规格
    private String labelAntenna;//天线规格
    private String labelChip;//芯片
    private String labelQuality;//材质
    private String labelSta;//状态 1=未使用,2=已使用,3=报废
    private String remark;//备注

}
