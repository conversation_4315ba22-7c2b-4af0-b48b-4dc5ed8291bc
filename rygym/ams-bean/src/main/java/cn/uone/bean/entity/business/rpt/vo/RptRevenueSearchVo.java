package cn.uone.bean.entity.business.rpt.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RptRevenueSearchVo {

    /**
     * amortize_item_id
     */
    private String amortizeItemId;

    /**
     * 合同id
     */
    private String projectId;
    /**
     * 合同id
     */
    private String contractId;
    /**
     * 合同id
     */
    private List contractIds;
    /**
     * 账单类型
     */
    private String orderType;

    /**
     * 账单类型
     */
    private String orderItemType;
    /**
     * 是否检查已生成
     */
    private String isExist;
    /**
     * 年
     */
    private String year;
    /**
     * 房间号
     */
    private String sourceCode;
    /**
     * 客户名称
     */
    private String name;
    /**
     * id
     */
    private String idNo;
    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 开始月份
     */
    private Integer startMonth = 1;

    /**
     * 截止月份
     */
    private Integer endMonth = 12;

    /**
     * 合同所属期间起始日（开始）
     */
    private Date startContractBegin;
    /**
     * 合同所属期间起始日（结束）
     */
    private Date endContractBegin;

    /**
     * 合同所属期间结束日（开始）
     */
    private Date startContractFinish;
    /**
     * 合同所属期间结束日（结束）
     */
    private Date endContractFinish;

    /**
     * 合同日期（开始）
     */
    private Date startCreateDate;
    /**
     * 合同日期（结束）
     */
    private Date endCreateDate;

    /**
     * 合同所属期间结束日（开始）
     */
    private Date contractStartDate;
    /**
     * 合同所属期间结束日（结束）
     */
    private Date contractEndDate;



}
