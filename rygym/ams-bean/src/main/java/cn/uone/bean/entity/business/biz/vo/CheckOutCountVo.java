package cn.uone.bean.entity.business.biz.vo;

import lombok.Data;

@Data
public class CheckOutCountVo {

    /**
     * 公寓--查房
     */
    private long checkApartNum;
    /**
     * 公寓--验房
     */
    private long recheckApartNum;
    /**
     * 公寓--退房中
     */
    private long checkingApartNum;
    /***
     * 公寓--终止
     */

    private long checkoutApartNum;
    /**
     * 商业--查房
     */
    private long checkBusNum;
    /**
     * 商业--验房
     */
    private long recheckBusNum;
    /**
     * 商业--退房中
     */
    private long checkingBusNum;

    /**
     * 商业--退房中
     */
    private long checkoutBusNum;
    /**
     * 车位--退租中
     */
    private long checkingCarNum;
    /**
     * 车位--已退租
     */
    private long checkoutCarNum;

    /**
     * 公寓--全部
     */
    public long getApartAllNum() {
        return checkApartNum + recheckApartNum + checkingApartNum + checkoutApartNum;
    }

    /**
     * 商业--全部
     */
    public long getBusAllNum() {
        return checkBusNum + recheckBusNum + checkingBusNum + checkoutBusNum;
    }

    /**
     * 车位--全部
     */
    public long getCarAllNum() {
        return checkingCarNum + checkoutCarNum;
    }
}
