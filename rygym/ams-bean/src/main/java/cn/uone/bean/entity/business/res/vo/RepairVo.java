package cn.uone.bean.entity.business.res.vo;

import cn.uone.application.enumerate.source.CommentEnum;
import lombok.Data;

@Data
public class RepairVo {

    private String keyword;

    private String[] statein;

    private String state;

    private String[] types;

    private String type;

    private String userType;

    private String startTime;

    private String endTime;

    private String updateStartTime;

    private String updateEndTime;
    //是否查看全部权限
    private boolean searchAll;

    private String userId;

    private String projectId;

    private String sourceId;

    private String requester;

    private String manager;

    private String reWithoutRenter;

    private String repairItemType;

    private String comment;

    /**
     *验收时间
     */

    private String checkedDate;

    /**
     *租客评价时间
     */

    private String commentDate;

    /**
     *评价内容
     */

    private String commentName;

    public void setCommentName() {
        this.commentName = CommentEnum.getNameByValue(comment);
    }




}
