package cn.uone.bean.entity.tpi.icbcPay;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class IcbcPayVo {
    /**
     * 支付金额，单位元
     */
    private BigDecimal totalFee;

    /**
     * 账单编号
     */
    private String outTradeNo;

    /**
     * 商品描述
     */
    private String body;

    /**
     * 用户端IP
     */
    private String ip;

    /**
     * 微信小程序下用户唯一标识
     */
    private String openId;

    /**
     * 接口配置
     */
    private IcbcPayConfigVo icbcPayConfig;

}
