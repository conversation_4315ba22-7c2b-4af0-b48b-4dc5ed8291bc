package cn.uone.bean.entity.business.onlineBid;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 竞价详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_bid_details")
public class BidDetailsEntity extends BaseModel<BidDetailsEntity> {

    private static final long serialVersionUID = 1L;


    /**
     * 是否是原租客
     */
    @TableField("is_old_tenant")
    private String isOldTenant;
    /**
     * 招标ID
     */
    @TableField("bid_id")
    private String bidId;

    /**
     * 申请ID
     */
    @TableField("apply_id")
    private String applyId;

    /**
     * 保证金表ID
     */
    @TableField("bail_id")
    private String bailId;

    /**
     * 资产信息
     */
    @TableField("property_info")
    private String propertyInfo;

    /**
     * 资产ID
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 所属项目
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 所属楼栋
     */
    @TableField("partition_name")
    private String partitionName;

    /**
     * 房源
     */
    @TableField("source_name")
    private String sourceName;


    /**
     * 竞标人
     */
    @TableField("bidder")
    private String bidder;

    /**
     * 竞标人id
     */
    @TableField("bidder_id")
    private String bidderId;


    /**
     * 身份证
     */
    @TableField("identity_card")
    private String identityCard;


    /**
     * 联系方式
     */
    @TableField("phone")
    private String phone;

    /**
     * 底价
     */
    @TableField("floor_price")
    private BigDecimal floorPrice;

    /**
     * 报价时间
     */
    @TableField("bid_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bidTime;

    /**
     * 报价金额
     */
    @TableField("money")
    private BigDecimal money;


    /**
     * 是否中标（1未中标，2中标）
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;



    @Override
    public Serializable pkVal() {
        return id;
    }

}
