package cn.uone.bean.entity.business.dev;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物业设备表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_dev_property_device")
public class DevPropertyDeviceEntity extends BaseModel<DevPropertyDeviceEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("project_id")
    private String projectId;

    /**
     * 规划分区
     */
    @TableField("partition_id")
    private String partitionId;

    /**
     * 设备类型
     */
    @TableField("class_id")
    private String classId;

    /**
     * 型号
     */
    @TableField("sn")
    private String sn;

    /**
     * 设备编码
     */
    @TableField("code")
    private String code;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    private String supplierId;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 状态
     */
    @TableField("state")
    private String state;

    /**
     * 位置描述
     */
    @TableField("summary")
    private String summary;

    /**
     * 权属所属
     */
    @TableField("property_owner")
    private String propertyOwner;

    /**
     * 启用时间
     */
    @TableField("enable_time")
    private Date enableTime;

    /**
     * 使用年限
     */
    @TableField("service_life")
    private String serviceLife;

    /**
     * 维护商id
     */
    @TableField("maintain_id")
    private String maintainId;

    /**
     * 品牌
     */
    @TableField("brand")
    private String brand;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
