package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_res_dept")
public class ResDeptEntity extends BaseModel<ResDeptEntity> {

    private static final long serialVersionUID = 1L;

    /** 部门ID */
    @TableField("dept_id")
    private String deptId;

    /** 部门名称 */
    @TableField("dept_name")
    private String deptName;

    /** 公司ID */
    @TableField("company_id")
    private String companyId;

    /** 父部门id */
    @TableField("parent_id")
    private String parentId;

    /** 祖级列表 */
    @TableField("ancestors")
    private String ancestors;

    /** 显示顺序 */
    @TableField("order_num")
    private Integer orderNum;

    /** 负责人 */
    @TableField("leader")
    private String leader;

    /** 联系电话 */
    @TableField("phone")
    private String phone;

    /** 邮箱 */
    @TableField("email")
    private String email;

    /** 部门状态（0正常 1停用） * */
    @TableField("status")
    private String status;



    @TableField(exist = false)
    private String value;



    @Override
    public Serializable pkVal() {
        return id;
    }

}
