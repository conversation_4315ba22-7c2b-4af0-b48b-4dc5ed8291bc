package cn.uone.bean.parameter;

import cn.hutool.core.date.DateUtil;
import cn.uone.web.util.UoneHeaderUtil;
import lombok.Data;

import java.util.Date;

@Data
public class PutAccountPo {


    /***
     * 项目ID
     */
    private String projectId = UoneHeaderUtil.getProjectId();

    /**
     * 分区ID
     */
    private String partitionId;

    /**
     * 签约人
     */
    private String signer;

    /**
     * 房间号
     */
    private String sourceCode;

    /**
     * 是否人才
     */
    private String subsidy;

    /**
     * 合同编号
     */
    private String contCode;

    /**
     * 关键字
     */
    private String keyWord;

    /**
     * 报表月份
     */
    private String reportDate;

    /**
     * 状态：
     * 1：已生效
     * 2：新签
     * 3：退租中
     * 4：已退租
     * 5：未转账
     * 6：开票
     */
    private Integer state;

    private Date startDate;

    private Date endDate;
    /**
     * 房源类型
     */
    private String sourceType;

    public Date getStartDate() {
        if (null != this.reportDate) {
            Date reportDate = DateUtil.parse(this.reportDate, "yyyy-MM");
            this.startDate = DateUtil.beginOfMonth(reportDate);
        }
        return this.startDate;
    }

    public Date getEndDate() {
        if (null != this.reportDate) {
            Date reportDate = DateUtil.parse(this.reportDate, "yyyy-MM");
            this.endDate = DateUtil.endOfMonth(reportDate);
        }
        return this.endDate;
    }

}
