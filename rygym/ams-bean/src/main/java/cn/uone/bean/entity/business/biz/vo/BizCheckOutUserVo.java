package cn.uone.bean.entity.business.biz.vo;


import cn.uone.bean.entity.business.biz.BizCheckOutUserEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 退房处理vo
 *
 * <AUTHOR>
 * @date 2018-12-28 16:23
 * @Param:
 * @return
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizCheckOutUserVo extends BizCheckOutUserEntity implements Serializable {

    //用户名
    private String pname;
    //房源名称
    private String sourceName;
    //房源id
    private String sourceId;
    //合同开始时间
    private String startDate;
    //合同结束时间
    private String endDate;
    //手机号码
    private String tel;
    //合同编号
    private String contractCode;
    //合同类型
    private String contractType;

    private String state;

}

