package cn.uone.bean.entity.business.cont.vo;

import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContTalentEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Auther: ljl
 * @Date: 2019/1/21 19:29
 * @Description:
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContTalentPageVo extends ContTalentEntity {
    private String projectName; // 项目名称

    /**
     * 证件类型
     */
    private String idTypeStr;

    /**
     * 人才类型
     */
    private String talentTypeStr;

    /**
     * 合同相关
     */
    private List<ContContractEntity> contracts;

    /**
     * 合同code  用于导出  , 分割
     */
    private String contractCodes;
}
