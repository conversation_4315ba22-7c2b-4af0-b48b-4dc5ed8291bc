package cn.uone.bean.entity.business.sys;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_sys_push_msg")
public class SysPushMsgEntity extends BaseModel<SysPushMsgEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 内容
     */
    @TableField("content")
    private String content;

    /**
     * 接收人
     */
    @TableField("recipient")
    private String recipient;

    /**
     * 是否看过
     */
    @TableField("is_seen")
    private Boolean seen;

    @TableField("type")
    private String type ;

    @TableField("status")
    private String status;

    @TableField("result_code")
    private int resultCode;

    @TableField("result_msg")
    private String resultMsg;

    @TableField(exist = false)
    private String pushDate;


    @TableField(exist = false)
    private Date startDate;

    @TableField(exist = false)
    private Date endDate;

    /**
     * 接收人姓名
     */
    @TableField(exist = false)
    private String name;


    public String getPushDate() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(getCreateDate());
    }



    @Override
    public Serializable pkVal() {
        return id;
    }

}
