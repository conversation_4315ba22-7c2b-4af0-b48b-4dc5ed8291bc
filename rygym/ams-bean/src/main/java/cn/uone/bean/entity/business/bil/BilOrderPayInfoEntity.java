package cn.uone.bean.entity.business.bil;

import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_bil_order_pay_info")
public class BilOrderPayInfoEntity extends BaseModel<BilOrderPayInfoEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("approval_id")
    private String approvalId;

    @TableField("order_id")
    private String orderId;

    /**
     * 申请人
     */
    @TableField("apply_user")
    private String applyUser;

    /**
     * 支付订单编号
     */
    @TableField("trade_code")
    private String tradeCode;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private Date payTime;

    /**
     * 支付方式
     */
    @TableField("pay_way")
    private String payWay;

    /**
     * 支付金额
     */
    @TableField("payment")
    private BigDecimal payment;
    /**
     * 实际支付金额
     */
    @TableField("actual_payment")
    private BigDecimal actualPayment;

    @TableField("remark")
    private String remark;

    @TableField("data_from")
    private String dataFrom;

    @TableField(exist = false)
    List<SysFileEntity> files = Lists.newArrayList();



    @Override
    public Serializable pkVal() {
        return id;
    }

}
