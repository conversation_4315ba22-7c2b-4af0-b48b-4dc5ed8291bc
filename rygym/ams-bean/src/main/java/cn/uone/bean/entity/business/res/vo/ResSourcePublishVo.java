package cn.uone.bean.entity.business.res.vo;


import cn.uone.bean.entity.business.res.ResSourceEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ResSourcePublishVo extends ResSourceEntity implements Serializable {
    private String projectName;
    private String partitionName;
    private String sourceName;
    private String houseTypeName;
    //对外表价
    private BigDecimal price;
    //定金
    private BigDecimal deposit;
    private String title;
    //1整租 2合租
    private String rentMode;
    private String taobao;
    private String payMethodOne;
    private String payMethodXm;
    private Integer totalFloor;
    private String startDate;
    private String manageName;
    private String manageTel;
    //
    private String buildingNature;
    private String ccbProjectId;
    private String approvalState;
}
