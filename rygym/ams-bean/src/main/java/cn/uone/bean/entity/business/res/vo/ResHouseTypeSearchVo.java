package cn.uone.bean.entity.business.res.vo;

import lombok.Data;

import java.io.Serializable;


@Data
public class ResHouseTypeSearchVo implements Serializable {

    private  String id;

    private String name;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 项目名
     */
    private String projectName;


    /**
     * 关键字
     */
    private String keyWord;
    //私有居室
    private String room;
    //私有客厅
    private String hall;
    //私有餐厅
    private String restaurant;
    //私有卫生间
    private String toilet;
    //私有阳台
    private String balcony;
    // 私有厨房
    private String kitchen;
    //共有客厅
    private String publicHall;
    //共有餐厅
    private String publicRestaurant;
    //共有卫生间
    private String publicToilet;
    //共有阳台
    private String publicBalcony;
    //共有厨房
    private String publicKitchen;

    private String href;

    private double latitude;
    private double longitude;

    private String isHot;

}
