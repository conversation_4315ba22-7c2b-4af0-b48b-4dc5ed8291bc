package cn.uone.bean.entity.business.cont.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Auther:
 * @Date:
 * @Description: 合同租金，押金，定金
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlterPriceVo  {
    private BigDecimal price; // 合同价格
    private BigDecimal deposit; // 押金
    private BigDecimal due; // 定金
}