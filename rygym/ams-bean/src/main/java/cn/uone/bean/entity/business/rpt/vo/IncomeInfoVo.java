package cn.uone.bean.entity.business.rpt.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class IncomeInfoVo {
    //房源类型
    private String sourceType;
    //账单类型
    private String orderType;
    //应收
    private BigDecimal billNumber = BigDecimal.ZERO;
    //未收
    private BigDecimal uncollectedNumber = BigDecimal.ZERO;
    //实收
    private BigDecimal collectedNumber = BigDecimal.ZERO;
    //优惠
    private BigDecimal discount = BigDecimal.ZERO;
    //退款
    private BigDecimal refundNumber = BigDecimal.ZERO;
    //罚没
    private BigDecimal fineNumber = BigDecimal.ZERO;
    //实际收入
    private BigDecimal summaryNumber = BigDecimal.ZERO;
    //历史欠缴
    private BigDecimal unpaid = BigDecimal.ZERO;
    //补收欠缴
    private BigDecimal repairUnpaid = BigDecimal.ZERO;

    private String city;

    private String projectName;

    private BigDecimal ratio;

    private String projectId;


    public IncomeInfoVo add(IncomeInfoVo vo){
        if(null==vo){
            return this;
        }
        if(null!=vo.getBillNumber()){
            billNumber = billNumber.add(vo.getBillNumber());
        }
        if(null!=vo.getUncollectedNumber()){
            uncollectedNumber = uncollectedNumber.add(vo.getUncollectedNumber());
        }
        if(null!=vo.getCollectedNumber()){
            collectedNumber = collectedNumber.add(vo.getCollectedNumber());
        }
        if(null!=vo.getDiscount()){
            discount = discount.add(vo.getDiscount());
        }
        if(null!=vo.getRefundNumber()){
            refundNumber = refundNumber.add(vo.getRefundNumber());
        }
        if(null!=vo.getFineNumber()){
            fineNumber = fineNumber.add(vo.getFineNumber());
        }
        if(null!=vo.getSummaryNumber()){
            summaryNumber = summaryNumber.add(vo.getSummaryNumber());
        }
        if(null!=vo.getUnpaid()){
            unpaid = unpaid.add(vo.getUnpaid());
        }
        if(null!=vo.getRepairUnpaid()){
            repairUnpaid = repairUnpaid.add(vo.getRepairUnpaid());
        }

        return this;
    }
}
