package cn.uone.bean.entity.business.index;

import lombok.Data;

@Data
public class IndexRightVo {

    private int maintainCount;//维修数量
    private int expandCount;//重点拓展数量
    private int signedCount;//签约数量
    private int giveUpCount;//放弃数量
    private int putawayCount;//上架数量
    private int notonCount;//未上架数量
    private int overCount;//结束数量
    private long themeCount;//主题数量
    private int partitionCount;//规划区域数量
    private int sourceCount;//房源数量
    private int carCount;//车位数量
    private long houseCount;//户型数量
    private long contractCount;//设计合同数量
    private long applyCount;//采购预算
    private int purOrderCount;//采购订单
    private long devCount;//入库设备
    private int uoneCount;//XX发布数量
    private int zfbCount;//支付宝发布数量
    private int xianyuCount;//闲鱼发布数量
    private int moguCount;//蘑菇发布数量
    private int unpublishedCount;//为发布数量

}
