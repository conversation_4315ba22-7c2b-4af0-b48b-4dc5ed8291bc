package cn.uone.bean.entity.business.kingdee;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_kingdee_amortize")
public class KingdeeAmortizeEntity extends BaseModel<KingdeeAmortizeEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 单据编号
     */
    @TableField("number")
    private String number;

    /**
     * 公司
     */
    @TableField("company")
    private String company;

    /**
     * 推送期间
     */
    @TableField("push_period")
    private String pushPeriod;

    /**
     * 对应期间
     */
    @TableField("dy_period")
    private String dyPeriod;

    /**
     * 项目段
     */
    @TableField("project")
    private String project;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 金蝶回调id
     */
    @TableField("kingdee_id")
    private String kingdeeId;

    @TableField(value="company_name",exist = false)
    private String companyName;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
