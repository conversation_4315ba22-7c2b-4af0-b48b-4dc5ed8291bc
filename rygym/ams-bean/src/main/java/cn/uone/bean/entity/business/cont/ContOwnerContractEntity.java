package cn.uone.bean.entity.business.cont;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cont_owner_contract")
public class ContOwnerContractEntity extends BaseModel<ContOwnerContractEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 业主名称
     */
    @TableField("owner_name")
    private String ownerName;

    /**
     * 业主电话
     */
    @TableField("owner_tel")
    private String ownerTel;

    /**
     * 租凭起日
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 租凭止日
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 签约人
     */
    @TableField("signer_name")
    private String signerName;

    /**
     * 签约时间
     */
    @TableField("sign_date")
    private Date signDate;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
