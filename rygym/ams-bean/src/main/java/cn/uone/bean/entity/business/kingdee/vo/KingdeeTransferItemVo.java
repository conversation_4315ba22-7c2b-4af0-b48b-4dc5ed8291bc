package cn.uone.bean.entity.business.kingdee.vo;

import cn.uone.bean.entity.business.kingdee.KingdeeTransferItemEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName KingdeeTransferItemVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/2 11:14
 * @Version 1.0
 */
@Data
public class KingdeeTransferItemVo extends KingdeeTransferItemEntity implements Serializable {
    private String projectName;
    private String productName;
    private String customerName;
    private String outTypeName;
    private String intoCustomerName;
    private String intoTypeName;
}
