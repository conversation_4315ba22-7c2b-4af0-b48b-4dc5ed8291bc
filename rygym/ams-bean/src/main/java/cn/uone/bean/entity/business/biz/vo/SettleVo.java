package cn.uone.bean.entity.business.biz.vo;


import cn.uone.bean.entity.business.biz.validate.PassCarSettle;
import cn.uone.bean.entity.business.biz.validate.PassSettle;
import cn.uone.bean.entity.business.biz.validate.RenterCarSettle;
import cn.uone.bean.entity.business.biz.validate.RenterSettle;
import cn.uone.bean.entity.business.dev.DeviceSourceCheckEntity;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SettleVo {

    @NotEmpty(message="申请单号不能为空",groups = {RenterSettle.class,RenterCarSettle.class,PassSettle.class,PassCarSettle.class})
    private String releaseId;

    @NotEmpty(message="结算的房源不能为空",groups = {RenterSettle.class,RenterCarSettle.class,PassSettle.class,PassCarSettle.class})
    private String sourceId;

    /**
     * 主合同id
     */
    private String frameContractId;

    /**
     * 管家复核原因 ReleaseReasonEnum
     */
    //@NotEmpty(message="退租原因不能为空",groups = {RenterSettle.class,RenterCarSettle.class})
    private String confirmReason;

    /**
     * 管家复核说明  ReleaseDescribeEnum
     */
    private String confirmDescribe;

    /**
     * 管家备注
     */
    private String confirmRemark;

    //@NotNull(message="退租日期不能为空",groups = {RenterSettle.class,RenterCarSettle.class})
    private Date checkoutDate;

    //已支付的 押金+水电周转金
    //private BigDecimal refundMoney;

    //是否退押金,水电周转金(0不退, 1退, 2待审核)
    private String refundState;

    //是否收取换房费(0不收, 1收取, 2待审核)
    private String changeRoomState;

    private String changeRoomApprovalReason;

    /**
     * 审批理由
     */
    private String approvalReason;

    /**
     * 租金
     */
    @NotNull(message="租金不能为空",groups = {RenterSettle.class,RenterCarSettle.class,PassSettle.class,PassCarSettle.class})
    private BigDecimal rentFee;

    /**
     * 租金缴交起始日
     */
    private Date rentStartDate;

    /**
     * 押金
     */
    private BigDecimal refundDeposit;

    /**
     * 押金罚没
     */
    private BigDecimal fineDeposit;


    /**
     * 租金缴交到期日
     */
    private Date rentEndDate;

    /**
     * 公摊水费
     */
    private BigDecimal publicWaterFee;

    /**
     * 公摊水数
     */
    private BigDecimal publicWaterNum;

    /**
     * 公摊水费抄表日期
     */
    private Date publicWaterStartDate;

    private Date publicWaterEndDate;


    /**
     * 公摊电费
     */
    private BigDecimal publicElecFee;

    /**
     * 公摊电数
     */
    private BigDecimal publicElecNum;

    /**
     * 公摊电费抄表日期
     */
    private Date publicElecStartDate;

    private Date publicElecEndDate;

    /**
     * 公摊煤气费
     */
    private BigDecimal publicGasFee;

    /**
     * 公摊煤气数
     */
    private BigDecimal publicGasNum;

    /**
     * 公摊煤气费抄表日期
     */
    private Date publicGasStartDate;

    /**
     * 公摊煤气费抄表日期
     */
    private Date publicGasEndDate;

    /**
     * 水费
     */
    private BigDecimal waterFee;

    /**
     * 水数
     */
    private BigDecimal waterNum;

    /**
     * 水费抄表日期
     */
    private Date waterStartDate;

    private Date waterEndDate;


    /**
     * 电费
     */
    private BigDecimal elecFee;

    /**
     * 电数
     */
    private BigDecimal elecNum;

    /**
     * 电费抄表日期
     */
    private Date elecStartDate;

    private Date elecEndDate;
    /**
     * 综合服务费
     */
    private BigDecimal synthesizeFee;

    /**
     * 综合服务费缴交起始日
     */
    private Date synthesizeFeeStartDate;

    /**
     * 综合服务费缴交到期日
     */
    private Date synthesizeFeeEndDate;

    /**
     * 物业服务费
     */
    private BigDecimal propertyFee;

    /**
     * 物业服务费缴交起始日
     */
    private Date propertyStartDate;

    /**
     * 物业服务费缴交到期日
     */
    private Date propertyEndDate;

    /**
     * 网络费
     */
    private BigDecimal netFee;

    /**
     * 网络费缴交起始日
     */
    private Date netStartDate;

    /**
     * 网络费缴交到期日
     */
    private Date netEndDate;

    /**
     * 门禁卡成本
     */
    private BigDecimal accessCardFee;

    /**
     * 维修费
     */
    private BigDecimal repairFee;

    /**
     * 清扫费
     */
    private BigDecimal cleanFee;

    /**
     * 违约费
     */
    private BigDecimal breakFee;

    /**
     * 换房费
     */
    private BigDecimal changeRoomFee;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 充值抵扣金额
     */
    private BigDecimal recharge;


    private BigDecimal propertyWaterFee;//物业公摊水费
    private Date propertyWaterStartDate;
    private Date propertyWaterEndDate;

    private BigDecimal propertyElecFee;//物业公摊电费
    private Date propertyElecStartDate;
    private Date propertyElecEndDate;

    private BigDecimal propertyPublicFee;//房屋公维金
    private Date propertyPublicStartDate;
    private Date propertyPublicEndDate;

    private BigDecimal propertyGarbageFee;//垃圾清运费
    private Date propertyGarbageStartDate;
    private Date propertyGarbageEndDate;

    private BigDecimal sepecialServiceFee;//特殊管理费
    private Date sepecialServiceStartDate;
    private Date sepecialServiceEndDate;

    private BigDecimal sepecialMarginFee;//特殊履约保证金
    private Date sepecialMarginStartDate;
    private Date sepecialMarginEndDate;

    private BigDecimal changeRoomMoney;//换房费
    private BigDecimal changeRenterMoney;//转租费

    //租客id
    private String renterId;

    //银行卡户名
    private String accountName;

    //银行id
    private String bankId;

    //银行卡号
    private String accountCode;

    //银行支行名称
    private String bankName;

    //银行行号
    private String bankCode;

    private String deviceCheckList;

    //退租性质(1无责退房, 2有责退房)
    private String refundNature;

    //办理意见
    private String handleRemark;

}
