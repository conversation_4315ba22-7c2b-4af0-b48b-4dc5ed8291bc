package cn.uone.bean.entity.business.face;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 人脸审核关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_face_rel")
public class FaceRelEntity extends BaseModel<FaceRelEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 审核id
     */
    @TableField("audit_id")
    private String auditId;

    /**
     * 门禁id
     */
    @TableField("lock_id")
    private String lockId;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
