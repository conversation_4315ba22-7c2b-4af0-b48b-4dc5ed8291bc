package cn.uone.bean.entity.business.kingdee;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_kingdee_receipt_item")
public class KingdeeReceiptItemEntity extends BaseModel<KingdeeReceiptItemEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 收款单id
     */
    @TableField("receipt_id")
    private String receiptId;

    /**
     * 交易流水号
     */
    @TableField("serial_number")
    private String serialNumber;

    /**
     * 收款编码
     */
    @TableField("code")
    private String code;

    /**
     * 收款类型
     */
    @TableField("payment_type")
    private String paymentType;

    /**
     * 收款金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 收款账户
     */
    @TableField("account")
    private String account;

    /**
     * 收款日期
     */
    @TableField("collection_date")
    private LocalDate collectionDate;

    /**
     * 收款方式
     */
    @TableField("payment_method")
    private String paymentMethod;

    /**
     * 客户段
     */
    @TableField("customer")
    private String customer;

    /**
     * 产品段
     */
    @TableField("product")
    private String product;

    /**
     * 项目段
     */
    @TableField("project")
    private String project;

    /**
     * 账单编号
     */
    @TableField("bill_number")
    private String billNumber;

    /**
     * 子帐单类型文本
     */
    @TableField("cz_type")
    private String czType;

    /**
     * 房间编号
     */
    @TableField("room")
    private String room;

    /**
     * 签约人
     */
    @TableField("contractor")
    private String contractor;


    @TableField(value="source_id",exist = false)
    private String sourceId;

    @TableField(value="order_item_type",exist = false)
    private String orderItemType;

    @TableField(value="type",exist = false)
    private String type;

    @TableField(value="order_item_id",exist = false)
    private String orderItemId;


    @Override
    public Serializable pkVal() {
        return id;
    }

    public String getType() {
        return this.paymentType;
    }
}
