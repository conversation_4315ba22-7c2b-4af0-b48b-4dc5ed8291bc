package cn.uone.bean.entity.business.investment.vo;


import cn.uone.bean.entity.business.investment.InvestmentPlanEntity;
import cn.uone.util.FileUtil;
import lombok.Data;

import java.io.Serializable;

@Data
public class InvestmentPlanVo extends InvestmentPlanEntity implements Serializable {

    /**
     * 招商任务名称
     */
    private String taskName;

    /**
     * 计划分配人姓名
     */
    private String planAssignorName;

    /**
     * 计划负责人姓名
     */
    private String planOwnerName;

    /**
     * 创建用户
     */
    private String createByUser;


    private String url;

    public void setUrl(String url){
        this.url = FileUtil.getPath(url);
    }
}
