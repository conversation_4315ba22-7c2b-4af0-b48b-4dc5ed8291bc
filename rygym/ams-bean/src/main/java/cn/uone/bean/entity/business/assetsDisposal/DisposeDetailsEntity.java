package cn.uone.bean.entity.business.assetsDisposal;

import java.math.BigDecimal;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 处置详情记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_dispose_details")
public class DisposeDetailsEntity extends BaseModel<DisposeDetailsEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 资产处置表id
     */
    @TableField("disposal_id")
    private String disposalId;

    /**
     * 来源类型来源类型（1租入、2购入、3自建、4划入、5委托）
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 来源单位
     */
    @TableField("source_unit")
    private String sourceUnit;

    /**
     * 接收移交时间
     */
    @TableField("accept_time")
    private Date acceptTime;

    /**
     * 接收文件清单
     */
    @TableField("accept_inventory")
    private String acceptInventory;

    /**
     * 遗留问题
     */
    @TableField("leftover_problem")
    private String leftoverProblem;

    /**
     * 评估价格
     */
    @TableField("evaluated_price")
    private BigDecimal evaluatedPrice;

    /**
     * 处置底价
     */
    @TableField("floor_price")
    private BigDecimal floorPrice;

    /**
     * 处置方式
     */
    @TableField("disposal_way")
    private String disposalWay;

    /**
     * 挂牌机构
     */
    @TableField("listed_institution")
    private String listedInstitution;

    /**
     * 受让方
     */
    @TableField("acquiring_party")
    private String acquiringParty;

    /**
     * 成交金额
     */
    @TableField("turnover")
    private BigDecimal turnover;

    /**
     * 处置收益
     */
    @TableField("disposal_earnings")
    private BigDecimal disposalEarnings;

    /**
     * 抵押对象
     */
    @TableField("pledge_object")
    private String pledgeObject;

    /**
     * 抵押金额
     */
    @TableField("mortgage_amount")
    private BigDecimal mortgageAmount;

    /**
     * 金融机构
     */
    @TableField("financing_institution")
    private String financingInstitution;

    /**
     * 抵押期限
     */
    @TableField("mortgage_term")
    private Date mortgageTerm;

    /**
     * 变更事由
     */
    @TableField("change_cause")
    private String changeCause;

    /**
     * 变更内容
     */
    @TableField("change_content")
    private String changeContent;

    /**
     * 变更时间
     */
    @TableField("change_time")
    private Date changeTime;

    /**
     * 解押对象
     */
    @TableField("released_mortgage_object")
    private String releasedMortgageObject;

    /**
     * 解押时间
     */
    @TableField("released_mortgage_time")
    private Date releasedMortgageTime;

    /**
     * 解押结果
     */
    @TableField("released_mortgage_result")
    private String releasedMortgageResult;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 变更时间
     */
    @TableField("selling_time")
    private Date sellingTime;

    /**
     * 债权人
     */
    @TableField("creditor")
    private String creditor;


    /**
     * 租赁开始时间
     */
    @TableField("lease_start")
    private Date leaseStart;

    /**
     * 租赁结束时间
     */
    @TableField("lease_end")
    private Date leaseEnd;

    /**
     * 租赁人
     */
    @TableField("leaseholder")
    private String leaseholder;

    /**
     * 租赁单价（月）
     */
    @TableField("lease_price")
    private BigDecimal leasePrice;

    /**
     * 资产信息
     */
    @TableField("property_info")
    private String propertyInfo;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
