package cn.uone.bean.parameter;

import cn.uone.web.util.UoneHeaderUtil;
import lombok.Data;

import java.util.List;

@Data
public class ReportReceiptPo {

    private String projectId = UoneHeaderUtil.getProjectId();

    private String partitionId;

    /**
     * 规划用途
     */
    private String propertyNature;

    private String payStartTime;
    private String payEndTime;

    private String createStartTime;
    private String createEndTime;

    private String pushStartTime;
    private String pushEndTime;

    private String keyword;

    private String contractCode;

    private String orderCode;

    private String orderType;

    private String payWay;

    private List<String> ids;

}
