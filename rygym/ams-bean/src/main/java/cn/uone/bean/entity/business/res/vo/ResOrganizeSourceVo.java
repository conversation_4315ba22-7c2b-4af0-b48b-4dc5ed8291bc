package cn.uone.bean.entity.business.res.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by xmlin on 2019-07-04.
 */
@Data
public class ResOrganizeSourceVo implements Serializable {
    /**
     * 房源
     */
    private String sourceId;
    /**
     * 房间号
     */
    private String code;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 区域名称
     */
    private String partitionName;
    /**
     * 房源类型
     */
    private String sourceType;
    /**
     * 房源地址
     */
    private String address;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 租金
     */
    private BigDecimal price;
    /**
     * 押金
     */
    private BigDecimal cashPledge;
    /**
     * 是否入住
     */
    private String isCheckIn;
    /**
     * 租客姓名
     */
    private String name;
    /**
     * 车牌号
     */
    private String num;
    /**
     * 备案号
     */
    private String recordCode;
    /**
     * 备案状态
     */
    private String  recordState;

}
