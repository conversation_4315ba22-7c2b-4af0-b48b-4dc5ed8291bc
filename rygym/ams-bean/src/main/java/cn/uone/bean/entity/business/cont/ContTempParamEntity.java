package cn.uone.bean.entity.business.cont;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 合同模板生成参数配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cont_temp_param")
public class ContTempParamEntity extends BaseModel<ContTempParamEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @TableField("temp_id")
    private String tempId;

    /**
     * 参数code【枚举TempCodeEnum】
     */
    @TableField("param_code")
    private String paramCode;

    /**
     * 参数值
     */
    @TableField("param_value")
    private String paramValue;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
