package cn.uone.bean.entity.business.dev.vo;

import cn.uone.bean.entity.business.dev.DevDeviceEntity;
import lombok.Data;

import java.util.List;

@Data
public class DevDeviceEntityVo extends DevDeviceEntity {

    private String purchaseCode;//采购单号
    private String className;//设备名称
    private String typeName;//设备类型
    private String supplierName;//供应商名称
    //private String typeId;//设备类型Id
    private String purchaseId;//采购单id
    private String isPublic;//是否公区设备
    private List<DevClassEntityVo> parentList;//父类菜单
    private String sourcenames;//已关联房源

    private String check = "1";
    private String explain = "";
    private String codeUrl; //二维码路径
}
