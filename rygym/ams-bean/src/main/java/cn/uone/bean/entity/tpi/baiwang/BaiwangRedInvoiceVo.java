package cn.uone.bean.entity.tpi.baiwang;

import lombok.Data;

import java.util.List;

@Data
public class BaiwangRedInvoiceVo {
    /**
     * 销方税号
     * 长度 20
     * 必填：是
     */
    private String taxNo;
    /**
     * 开票单号（红票）
     * 长度：20
     * 必填：否
     */
    private String orderNo;

    /**
     * 开票终端/数电账号
     * 长度 30
     * 必填：否
     */
    private String invoiceTerminalCode;
    /**
     * 原蓝票请求流水号(申请流水号/开票单号，任选一项必填)
     * 长度：20
     * 必填：否
     */
    private String originalSerialNo;
    /**
     * 原蓝票开票单号(申请流水号/开票单号，任选一项必填)
     * 长度：20
     * 必填：否
     */
    private String originalOrderNo;
    /**
     * 原蓝票发票代码
     * 长度：12
     * 必填：否
     */
    private String originalInvoiceCode;
    /**
     * 原蓝票发票号码
     * 长度：8
     * 必填：否
     */
    private String originalInvoiceNo;
    /**
     * 原蓝票数电发票号码
     * 长度：20
     * 必填：否
     */
    private String originalDigitInvoiceNo;
    /**
     * 快捷冲红类型:
     * 0-普通冲红(仅生成红字信息表/红字确认单),
     * 1-快捷冲红(生成红字信息表/红字确认单后，自动开具红票)
     * 注：该字段仅在税控专票和数电纸票时有效
     * 必填：否
     */
    private String fastIssueRedType;

    /**
     * 红票/红字信息表/红字确认单回传地址
     * 长度：255
     * 必填：否
     */
    private String callBackUrl;

    /**
     * 发票明细列表(部分冲红时必填)
     * 长度：2000
     * 必填：否
     */
    private List<BaiwangRedInvoiceDetailVo> details;


}
