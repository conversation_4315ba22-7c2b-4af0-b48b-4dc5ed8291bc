package cn.uone.bean.entity.business.bil.vo;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by xmlin on 2018-12-15.
 */
@Data
public class BilInvestVo extends BilOrderEntity implements Serializable {
    /**
     * 房源地址
     */
    private String address;
    /**
     * 付款方
     */
    private String payer;
    /**
     * 签约方
     */
    private String singer;
    /**
     * 员工姓名
     */
    private String empName;
    private String isPush;
    /**
     * 员工电话
     */
    private String empTel;
    /**
     * 账单支付方
     */
    private String payerType;
    /**
     * 合同编号
     */
    private String contractCode;
    /**
     * 合同类型
     */
    private String contractType;
    /**
     * 是否机构
     */
    private String isOrganize;
    /**
     * 账单日期
     */
    private String orderTime;
    /**
     * 申请时间
     */
    private String applyTime;

    /***
     *  账单最小日期
     */
    private Date minDate;
    /***
     *  账单最大日期
     */
    private Date maxDate;

    /**
     * 到账时间
     */
    private Date arriveTime;
    /**
     * 可催付
     */
    private String isPayable;
    /**
     * 催款次数
     */
    private Integer time;

    /**
     * 优惠编码
     */
    private String discountCode;
    /**
     * 优惠id
     */
    private String discountId;
    /**
     * 优惠金额
     */
    private String discountPayment;
    private String name;
    private String taxpayerCode;
    private String enterpriseAddress;
    private String enterpriseTel;
    private String enterpriseAccount;
    private String enterpriseBank;
    private String projectName;
    private String partitionName;
    private BigDecimal cashPledge;
    private BigDecimal  price;
    private BigDecimal   receivable;
    private Date transferTime;
    private Date startDate;
    private Date endDate;
    private String signer;
    private String tel;

    private List<BilOrderItemEntity> detail;
    private String singerId;
    private BigDecimal fixAfter;
    /**
     * 固耗 服务费子账单金额
     */
    private BigDecimal itemPayment;

    private String projectId;
    private BigDecimal thisPayment;
    private String payTimeStr ;
    private BigDecimal feeCharge;
    private String startTimeStr ;
    private String endTimeStr ;

    private String arriveCode;

}
