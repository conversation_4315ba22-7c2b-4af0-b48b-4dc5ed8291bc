package cn.uone.bean.entity.business.applyCard;

import cn.uone.bean.entity.base.BaseModel;
import cn.uone.bean.entity.business.cont.ContCheckInUserEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 办卡申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_apply_card")
public class ApplyCardEntity extends BaseModel<ApplyCardEntity> {

    private static final long serialVersionUID = 1L;



    /**
     *审核时间
     */
    @TableField("audit_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;
    /**
     *房源类型
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 学（工）号
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 状态
     */
    @TableField("status")
    private String status;
    /**
     * 片区
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 片区id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 楼栋
     */
    @TableField("partition_name")
    private String partitionName;

    /**
     * 楼栋id
     */
    @TableField("partition_id")
    private String partitionId;

    /**
     * 房间号
     */
    @TableField("code")
    private String code;

    /**
     * 房源名称
     */
    @TableField("source")
    private String source;

    /**
     * 房源Id
     */
    @TableField("source_id")
    private String sourceId;


    /**
     * 产权人
     */
    @TableField("property_owner")
    private String propertyOwner;
    /**
     * 产权人电话
     */
    @TableField("property_owner_tel")
    private String propertyOwnerTel;

    /**
     * 产权人身份证
     */
    @TableField("property_owner_card")
    private String propertyOwnerCard;

    /**
     * 申报时间
     */
    @TableField("declare_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date declareTime;

    /**
     * 出租人
     */
    @TableField("leaser")
    private String leaser;

    /**
     * 承租人
     */
    @TableField("renter")
    private String renter;

    /**
     * 办卡人
     */
    @TableField("applicant")
    private String applicant;

    /**
     * 办卡人性别
     */
    @TableField("gender")
    private String gender;

    /**
     * 办卡人Id
     */
    @TableField("applicant_id")
    private String applicantId;

    /**
     * 办卡人身份证
     */
    @TableField("applicant_card")
    private String applicantCard;

    /**
     * 办卡原因（1新办、2补卡、3注销、4延期）
     */
    @TableField("cause")
    private String cause;

    /**
     * 关联Id
     */
    @TableField("link_id")
    private String linkId;

    /**
     * 卡类别
     */
    @TableField("type")
    private String type;

    /**
     * 主副卡
     */
    @TableField("main_or_assistant")
    private String mainOrAssistant;

    /**
     * 有效期
     */
    @TableField("expiry_date")
    private String expiryDate;

    /**
     * 使用状态
     */
    @TableField("use_state")
    private String useState;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @TableField(exist = false)
    private String tel;

    @TableField(exist = false)
    private int serialNumber;

    @TableField(exist = false)
    List<ContCheckInUserEntity> checkInUsers;

    @TableField(exist = false)
    private String imgUrl;

    @TableField(exist = false)
    private List<String> statusList;

    @TableField(exist = false)
    private List<String> causeList;

    @TableField(exist = false)
    private String startDate;
    @TableField(exist = false)
    private String endDate;
    @TableField(exist = false)
    private String auditStart;
    @TableField(exist = false)
    private String auditEnd;

    @TableField(exist = false)
    private List<SysFileEntity> headImages;
    @TableField(exist = false)
    private List<SysFileEntity> titleDeedImages;
    @TableField(exist = false)
    private List<SysFileEntity> contractImages;
    @TableField(exist = false)
    private List<SysFileEntity> familyImages;
    @TableField(exist = false)
    private List<SysFileEntity> idCardImages;
    @TableField(exist = false)
    private List<ApplyCardEfficientEntity> efficientEntities;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
