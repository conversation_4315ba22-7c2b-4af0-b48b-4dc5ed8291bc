package cn.uone.bean.entity.business.kingdee;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_kingdee_amortize_item")
public class KingdeeAmortizeItemEntity extends BaseModel<KingdeeAmortizeItemEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("amortize_id")
    private String amortizeId;

    /**
     * 应收类型
     */
    @TableField("payment_type")
    private String paymentType;

    /**
     * 子账单类型文本
     */
    @TableField("cz_type")
    private String czType;

    /**
     * 对应期间
     */
    @TableField("period")
    private String period;

    /**
     * 摊销金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 客户段
     */
    @TableField("customer")
    private String customer;

    /**
     * 产品段
     */
    @TableField("product")
    private String product;

    @TableField(value="type",exist = false)
    private String type;

    @Override
    public Serializable pkVal() {
        return id;
    }

    public String getType() {
        return this.paymentType;
    }

}
