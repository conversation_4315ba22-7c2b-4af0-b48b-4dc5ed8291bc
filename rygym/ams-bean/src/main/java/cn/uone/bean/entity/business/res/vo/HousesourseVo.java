package cn.uone.bean.entity.business.res.vo;

import cn.uone.bean.entity.business.res.ResSourceEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class  HousesourseVo extends ResSourceEntity {
	//总楼层
	private Integer totalFloor;
	private BigDecimal lowPrice;
	/**
	 * 对外表价
	 */
	private BigDecimal price;

	/**
	 * 定金
	 */
	private BigDecimal deposit;

	/**
	 * 宠物
	 */
	private String petKept;

	/**
	 * 房屋配置
	 */
	private String houseConf;

	/**
	 * 公共配置
	 */
	private String publicConf;

	/**
	 * 房屋标签
	 */
	private String houseLabel;

	/**
	 * 描述
	 */
	private String summary;

	/**
	 * 项目详细地址
	 */
	private String address;

	/**
	 * 经度
	 */
	private String longitude;

	/**
	 * 纬度
	 */
	private String latitude;
	/**
	 * 小区（项目）名称
	 */
	private String projectName;

	/**
	 * 房间
	 */
	private Integer room=0;

	/**
	 * 大厅
	 */
	private Integer hall=0;
	private Integer kitchen=0;
	/**
	 * 卫生间
	 */
	private Integer toilet=0;
	/**
	 * 出租类型
	 */
	private String rentType;
	/**
	 * 出租方式
	 */
	private String rentMode;
	/**
	 * 是否短租
	 */
	private Boolean isShortRent;

	/**
	 * 房管姓名
	 */
	private String manageName;
	//房屋标题
	private String title;
	/**
	 * 房管电话
	 */
	private String manageTel;

	/**
	 * 租凭起日
	 */
	private String startDate;

	/**
	 * 房管淘宝会员号
	 */
	private String taobao;

	/**
	 * 支付宝租房/闲鱼租房付款方式
	 */
	private String payMethodOne;
	/**
	 * 春眠/房产之窗付款方式
	 */
	private String payMethodTwo;
	private String payMethodXm;
	private String themeName;
	private String themeDesc;
	private String thirdPartyCommunity;
	private String thirdPartyRoom;
	//省1
	private String province;
	//市2
	private String city;
	//区3
	private String district;
	private String thirdPartyLayout;
	//building
	private String partitionName;
	private String thirdPartyImages;
	private String buildingNature;
	private Date JYSJ;
	private String sourceName;
	private String areaCode;
	private String streetCode;

	private String recordName ;

	private String recordPassword ;
}