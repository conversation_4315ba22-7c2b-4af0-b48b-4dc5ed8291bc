package cn.uone.bean.entity.business.res;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 房源状态天数表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_source_state_days")
public class SourceStateDaysEntity extends BaseModel<SourceStateDaysEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 房源id
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 天数
     */
    @TableField("days")
    private Integer days;

    /**
     * 状态
     */
    @TableField("state")
    private String state;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
