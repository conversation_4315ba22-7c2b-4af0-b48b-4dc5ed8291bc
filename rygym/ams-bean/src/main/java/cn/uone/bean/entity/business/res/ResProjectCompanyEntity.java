package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 项目公司表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_project_company")
public class ResProjectCompanyEntity extends BaseModel<ResProjectCompanyEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 项目关联ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 公司编码
     */
    @TableField("code")
    private String code;

    /**
     * 开户行
     */
    @TableField("bank_code")
    private String bankCode;

    /**
     * 开户行
     */
    @TableField("bank")
    private String bank;

    /**
     * 卡号
     */
    @TableField("card")
    private String card;

    /**
     * 分账比例
     */
    @TableField("split_proportion")
    private BigDecimal splitProportion;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("contact")
    private String contact;

    @TableField("telphone")
    private String telphone;

    @TableField("legal_person")
    private String legalPerson;

    @TableField("type")
    private String type;

    @TableField("parent_id")
    private String parentId;

    @TableField("parent_name")
    private String parentName;

    @TableField(exist = false)
    private String value;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
