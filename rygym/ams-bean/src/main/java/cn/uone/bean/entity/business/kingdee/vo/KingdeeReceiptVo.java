package cn.uone.bean.entity.business.kingdee.vo;

import cn.uone.bean.entity.business.kingdee.KingdeeReceiptEntity;
import cn.uone.bean.entity.business.kingdee.KingdeeReceiptItemEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName KingdeeReceiptVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/2 11:14
 * @Version 1.0
 */
@Data
public class KingdeeReceiptVo extends KingdeeReceiptEntity implements Serializable {
    private String orderId;
    private List<KingdeeReceiptItemEntity> entrys;
}
