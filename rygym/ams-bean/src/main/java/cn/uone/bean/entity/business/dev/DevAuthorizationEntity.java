package cn.uone.bean.entity.business.dev;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 门禁授权表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_dev_authorization")
public class DevAuthorizationEntity extends BaseModel<DevAuthorizationEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 物业设备id
     */
    @TableField("property_device_id")
    private String propertyDeviceId;

    /**
     * 前端用户id
     */
    @TableField("renter_id")
    private String renterId;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
