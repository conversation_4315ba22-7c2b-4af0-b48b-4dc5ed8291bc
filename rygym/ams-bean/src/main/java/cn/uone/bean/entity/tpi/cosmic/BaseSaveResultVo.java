package cn.uone.bean.entity.tpi.cosmic;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>保存结果明细-通用</p>
 *
 * {
 *     "billIndex":0,
 *     "billStatus":true,
 *     "errors":[],
 *     "id":"1955213429241313280",
 *     "keys":{
 *      "xmgd_sourcetype":"MYERP_Skd",
 *      "xmgd_sourcenumber":"97ef6aaa-f310-48b9-46e2-08db836c4b7e",
 *      "xmgd_sourcesystem":"MYERP"
 *     },
 *     "number":"cs000001",
 *     "type":"Add"
 *    }
 */
@Data
public class BaseSaveResultVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据索引
     */
    private Integer billIndex;

    /**
     * 操作状态 true成功  false失败
     */
    private Boolean billStatus;

    /**
     * 错误信息
     */
    private List<BaseSaveErrorVo> errors = new ArrayList<>();

    /**
     * 保存成功返回id
     */
    private Long id;

    /**
     * xmgd_sourcetype 来源单据类型
     * xmgd_sourcenumber 来源基础资料编码
     * xmgd_sourcesystem 来源系统
     */
    private Map<String, Object> keys = new HashMap<>();

    /**
     * 编码
     */
    private String number;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 反序列化来源基础信息
     */
    @JSONField(serialize = false, deserialize = false)
    private BasePojo sourceInfo;

    public BasePojo getSourceInfo() {
        if (sourceInfo == null) {
            this.sourceInfo = BasePojo.mapToBean(keys, BasePojo.class);
        }
        return sourceInfo;
    }
}
