package cn.uone.bean.entity.crm;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_modular")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModularEntity extends BaseModel<ModularEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 模块名称
     */
    @TableField("name")
    private String name;

    /**
     * 所属系统【1：资产管理系统，2：，3：】
     */
    @TableField("system_code")
    private String systemCode;


    /**
     * 所属系统【1：资产管理系统，2：，3：】
     */
    @TableField("icon")
    private String icon;

    /**
     * 路由
     */
    @TableField("route")
    private String route;

    /**
     * 图标路径
     */
    @TableField("icon_url")
    private String iconUrl;

    /***
     * 菜单列表
     */
    @TableField(exist = false)
    private List<MenuEntity> menus = Lists.newArrayList();



    @Override
    public Serializable pkVal() {
        return id;
    }

}
