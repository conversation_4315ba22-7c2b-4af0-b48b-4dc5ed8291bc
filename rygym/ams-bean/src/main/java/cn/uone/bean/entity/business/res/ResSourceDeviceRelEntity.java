package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_source_device_rel")
public class ResSourceDeviceRelEntity extends BaseModel<ResSourceDeviceRelEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("source_id")
    private String sourceId;

    @TableField("device_id")
    private String deviceId;
    /**
     * 是否公区
     */
    @TableField("is_public")
    private Boolean isPublic;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
