package cn.uone.bean.entity.business.report.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ConfirmReleaseVo {

    private String projectId;//项目id
    private String partitionId;//区域id
    private String code;//坐落
    private String contractId;
    private String contractCode;
    private String name;//签约方
    private String orderCode;
    private String orderId;
    private String orderType;


    private BigDecimal yszj;//已收租金

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date yszjStartTime;//已收租金起始日期

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date yszjEndTime;//已收租金截止日期

    private BigDecimal ysyj;//已收押金

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date outOrChangeTime;//退租日期

    private BigDecimal ytzj;//应退租金

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ytzjStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ytzjEndTime;

    private BigDecimal ytyj;
    private BigDecimal fmyj;

    private BigDecimal cqzj;//应补缴租金

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cqzjStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cqzjEndTime;

    private BigDecimal hff;
    private BigDecimal hj;//应抵扣/应退生活费用
    /**
     * 實退租金
     */
    private BigDecimal stzj;
    /**
     * 實退押金
     */
    private BigDecimal styj;
    /**
     * 實際罰沒押金
     */
    private BigDecimal sjfmyj;
    /**
     * 實退生活費
     */
    private BigDecimal stshf;

    private BigDecimal cqzjSj;//实补金额

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date payTime;

    private String payState;
    private String newContractCode;
    private String newContractId;
    private  String confirmReason;

    private BigDecimal wxj;//公共维修金
    private BigDecimal wyf;//物业服务费
    private BigDecimal wyj;//违约金
    private BigDecimal zhfwf;//综合服务费
    private BigDecimal tnsf;//套内公摊水费
    private BigDecimal tndf;//套内公摊电费
    private BigDecimal tnmq;//套内公摊煤气费
    private BigDecimal wlf;//网络费
    private BigDecimal sf;//水费
    private BigDecimal df;//电费
    private BigDecimal qsf;//清扫费
    private BigDecimal wxf;//维修费
    private BigDecimal mjf;//门禁卡费用
    private BigDecimal qtf;//其他费用
    private BigDecimal wygtsf;//物业公摊水费
    private BigDecimal wygtdf;//物业公摊电费


    private String memo;


    private String isOrganize;
    private String sourceType;
    private String newIsOrganize;
    private String sourceId;
    private String contractType;
    private String newContractType;
    private String newSourceId;
}
