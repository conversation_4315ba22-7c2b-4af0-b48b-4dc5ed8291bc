package cn.uone.bean.entity.business.supplies;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_supplies_check_sublist")
public class SuppliesCheckSublistEntity extends BaseModel<SuppliesCheckSublistEntity> {

    private static final long serialVersionUID = 1L;


    /** 公司id */
    @TableField("company_id")
    private String companyId;

    /**
     * 盘点表id
     */
    @TableField("check_id")
    private String checkId;

    /**
     * 物资id
     */
    @TableField("asserts_id")
    private String assertsId;

    /**
     * 库存数量
     */
    @TableField("stock_quantity")
    private Integer stockQuantity;

    /**
     * 实盘存数
     */
    @TableField("actual_quantity")
    private Integer actualQuantity;

    /**
     * 盘点状态
     */
    @TableField("state")
    private String state;

    /**
     * 盈亏量
     */
    @TableField("quantity_variance")
    private Integer quantityVariance;

    /**
     * 盈亏原因
     */
    @TableField("cause")
    private String cause;

    /**
     * 处理意见
     */
    @TableField("dispose")
    private String dispose;

    /**
     * 处理人
     */
    @TableField("handler")
    private String handler;

    /**
     * 处理人ID
     */
    @TableField("handler_id")
    private String handlerId;

    /**
     * 物料名称
     */
    @TableField("name")
    private String name;

    /**
     * 类别(1 固资 2  消耗品)
     */
    @TableField("type")
    private String type;

    /**
     * 是否异常(1 正常 2  异常)
     */
    @TableField("is_abnormal")
    private String isAbnormal;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;



    @Override
    public Serializable pkVal() {
        return id;
    }

}
