package cn.uone.bean.entity.business.onlineBid;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 报名表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_bid_apply")
public class BidApplyEntity extends BaseModel<BidApplyEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 招标标题
     */
    @TableField("title")
    private String title;

    /**
     * 招标公告ID
     */
    @TableField("bid_id")
    private String bidId;


    /**
     * 保证金表ID
     */
    @TableField("bail_id")
    private String bailId;


    /**
     * 招标编号
     */
    @TableField("code")
    private String code;

    /**
     * 资产信息
     */
    @TableField("property_info")
    private String propertyInfo;

    /**
     * 资产ID
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 所属项目
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 所属楼栋
     */
    @TableField("partition_name")
    private String partitionName;

    /**
     * 房源
     */
    @TableField("source_name")
    private String sourceName;

    /**
     * 申请人
     */
    @TableField("applicant")
    private String applicant;

    /**
     * 申请id
     */
    @TableField("applicant_id")
    private String applicantId;

    /**
     * 身份证
     */
    @TableField("identity_card")
    private String identityCard;


    /**
     * 性质
     */
    @TableField("nature")
    private String nature;


    /**
     * 公司名称
     */
    @TableField("company")
    private String company;

    /**
     * 底价
     */
    @TableField("floor_price")
    private BigDecimal floorPrice;


    /**
     * 当前报名总人数
     */
    @TableField(exist = false)
    private int applyCount;


    /**
     * 联系方式
     */
    @TableField("phone")
    private String phone;

    /**
     * 报名日期
     */
    @TableField("apply_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 审核人
     */
    @TableField("auditor")
    private String auditor;

    /**
     * 审核人ID
     */
    @TableField("auditor_id")
    private String auditorId;

    /**
     * 审核时间
     */
    @TableField("auditor_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditorTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;



    /**
     * 身份证或营业执照图片路径
     */
    @TableField(exist = false)
    private List<String> papersPhotoUrl;
    /**
     * 缴费凭证图片路径
     */
    @TableField(exist = false)
    private String playPhotoUrl;

    /**
     * 保证金支付状态
     */
    @TableField(exist = false)
    private String payStatus;

    /**
     * 招标状态
     */
    @TableField(exist = false)
    private String bidStatus;

    /**
     * 竞价开始时间
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String biddingStart;

    /**
     * 竞价结束时间
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String biddingEnd;

    /**
     * 保证金
     */
    @TableField(exist = false)
    private BigDecimal earnestMoney;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
