package cn.uone.bean.entity.business.cont;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 合同信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cont_contract_info")
public class ContContractInfoEntity extends BaseModel<ContContractInfoEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 合同id
     */
    @TableField("contract_id")
    private String contractId;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;

    /**
     * 电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 证件类型
     */
    @TableField("id_type")
    private String idType;

    /**
     * 证件号码
     */
    @TableField("id_no")
    private String idNo;

    /**
     * 是否有补贴
     */
    @TableField("is_subsidy")
    private Boolean isSubsidy;

    /**
     * 补贴金额
     */
    @TableField("subsidy_sum")
    private BigDecimal subsidySum;
    /**
     * 是否有罚没
     */
    @TableField("is_forfeiture")
    private Boolean isForfeiture;

    /**
     * 罚没金额
     */
    @TableField("forfeiture_sum")
    private BigDecimal forfeitureSum;

    /**
     * 补贴人
     */
    @TableField("subsidy_person")
    private String subsidyPerson;

    /**
     * 特殊条款
     */
    @TableField("special_item")
    private String specialItem;

    /**
     * 特别约定
     */
    @TableField("special_agreement")
    private String specialAgreement;

    /**
     * 用途
     */
    @TableField("purpose")
    private String purpose;

    /**
     * 经营范围
     */
    @TableField("business_scope")
    private String businessScope;

    /**
     * 经营品牌
     */
    @TableField("business_brand")
    private String businessBrand;

    /**
     * 联系地址
     */
    @TableField("address")
    private String address;

    /**
     * 紧急联系人
     */
    @TableField("urgenter")
    private String urgenter;
    /**
     * 紧急联系电话
     */
    @TableField("urgent_tel")
    private String urgentTel;

    /**
     * 计算结果保留位数
     */
    @TableField("new_scale")
    private Integer newScale;
    /**
     * 计算结果保留方法
     */
    @TableField("rounding_mode")
    private Integer roundingMode;
    /**
     * 开票机构名称
     */
    @TableField("tax_org_name")
    private String taxOrgName;
    /**
     * 机构地址
     */
    @TableField("enterprise_address")
    private String enterpriseAddress;

    /**
     * 公司电话
     */
    @TableField("enterprise_tel")
    private String enterpriseTel;

    /**
     * 机构银行账号
     */
    @TableField("enterprise_account")
    private String enterpriseAccount;

    /**
     * 开户行
     */
    @TableField("enterprise_bank")
    private String enterpriseBank;

    /**
     * 纳税人代码
     */
    @TableField("taxpayer_code")
    private String taxpayerCode;


    @TableField(exist = false)
    private Date signDate;

    /**
     * 租客类型
     */
    @TableField(exist = false)
    private String type;

    @TableField(exist = false)
    private String email;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
