package cn.uone.bean.entity.business.act;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableName;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;

/**
 * <p>
 * 活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_act_activity")
public class ActActivityEntity extends BaseModel<ActActivityEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 活动名称
     */
    @TableField("name")
    private String name;

    /**
     * 活动日期
     */
    @TableField("act_date")
    private Date actDate;

    @TableField(exist = false)
    private String actionDate;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private String startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private String endTime;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 项目名
     */
    @TableField(exist = false)
    private String projectName;

    /**
     * 活动详情
     */
    @TableField("detail")
    private String detail;

    /**
     * 是否发布
     */
    @TableField("is_publish")
    private String isPublish;

    /**
     * 消息类型
     */
    @TableField("type")
    private String type ;

    /**
     * 消息摘要
     */
    @TableField("remark")
    private String remark ;

    @TableField("title_img")
    private String titleImg;

    @TableField("cover_img")
    private String coverImg;

    @TableField("extra_info")
    private String extraInfo;

    /**
     * 星期
     */
    @TableField(exist = false)
    private String week;

    /**
     * 状态
     */
    @TableField(exist = false)
    private String state;

    /**
     * 状态中文
     */
    @TableField(exist = false)
    private String stateStr;

    public String getWeek() {
        return DateUtil.dayOfWeekEnum(this.actDate).toChinese();
    }

    public String getActionDate() {
        SimpleDateFormat formatter = new SimpleDateFormat("MM/dd");
        String dateString = formatter.format(this.actDate);
        return dateString;
    }

//    public String getStartTime() {
//      return  this.startTime.substring(0,5);
//
//    }
//
//    public String getEndTime() {
//        return  this.endTime.substring(0,5);
//    }

    public String getState() {
        Calendar now = Calendar.getInstance();
        Calendar act = Calendar.getInstance();
        now.setTime(new Date());
        now.set(Calendar.HOUR_OF_DAY, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        act.setTime(actDate);
        act.set(Calendar.HOUR_OF_DAY, 0);
        act.set(Calendar.MINUTE, 0);
        act.set(Calendar.SECOND, 0);
        act.set(Calendar.MILLISECOND, 0);
        if(now.before(act)){
            this.stateStr="未开始";
            return "0";
        };
        if(now.compareTo(act)==0){
            this.stateStr="进行中";
            return "1";
        };
        if(now.after(act)){
            this.stateStr="已过期";
            return "2";
        };
        return null;
    }

    @Override
    public Serializable pkVal() {
        return id;
    }
}
