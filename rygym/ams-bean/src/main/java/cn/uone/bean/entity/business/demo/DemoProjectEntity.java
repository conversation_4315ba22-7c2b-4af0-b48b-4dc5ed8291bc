package cn.uone.bean.entity.business.demo;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_project")
public class DemoProjectEntity extends BaseModel<DemoProjectEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("ccb_project_id")
    private String ccbProjectId;

    @TableField("ccb_community_id")
    private String ccbCommunityId;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    @TableField("code")
    private String code;

    /**
     * 项目名
     */
    @TableField("name")
    private String name;

    /**
     * 建筑面积
     */
    @TableField("area")
    private BigDecimal area;

    @TableField("owner_id")
    private String ownerId;

    /**
     * 预计房间数（间）
     */
    @TableField("room_num")
    private Integer roomNum;




    /**
     * 省
     */
    @TableField("province_id")
    private String provinceId;

    /**
     * 市
     */
    @TableField("city_id")
    private String cityId;

    /**
     * 县
     */
    @TableField("district_id")
    private String districtId;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 经度
     */
    @TableField("longitude")
    private String longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private String latitude;

    /**
     * 项目简介
     */
    @TableField("summary")
    private String summary;

    /**
     * 拓展状态
     */
    @TableField("expand_state")
    private String expandState;

    /**
     * 运营状态
     */
    @TableField("operate_state")
    private String operateState;

    /**
     * 项目来源
     */
    @TableField("source")
    private String source;

    /**
     * 其他备注
     */
    @TableField("note")
    private String note;

    /**
     * 部门编码
     */
    @TableField("dept_code")
    private String deptCode;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
