package cn.uone.bean.entity.business.res.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Auther: ljl
 * @Date: 2018/12/26 11:20
 * @Description:
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResSourceDeviceRelVo {

    private String id;

    /**
     * 房源id
     */
    private String sourceId;

    private String supplierId;

    private Boolean isPublic;

    /**
     * 设备id
     */
    private String deviceId;

    /***
     * 创建时间
     */
    private Date createDate;

    /***
     * 创建人
     */
    private String createBy;

    /***
     * 最后修改时间
     */
    private Date updateDate;

    /***
     * 最后修改人
     */
    private String updateBy;

    // 设备表
    private String code; // 设备编号
    private BigDecimal price; // 价格
    private BigDecimal damage; // 赔偿金

    // 设备类型表
    private String name; // 名称

    //读数
    private BigDecimal readNum;
}
