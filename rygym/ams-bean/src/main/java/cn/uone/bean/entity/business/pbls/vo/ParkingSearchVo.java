package cn.uone.bean.entity.business.pbls.vo;

import cn.uone.bean.entity.business.pbls.ParkingApplyEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by xmlin on 2018-12-15.
 */
@Data
public class ParkingSearchVo extends ParkingApplyEntity implements Serializable {

    /**
     * 申请状态值集合
     */
    private List<String>  states ;

    /**
     * 使用状态
     */
    private String useState;
}
