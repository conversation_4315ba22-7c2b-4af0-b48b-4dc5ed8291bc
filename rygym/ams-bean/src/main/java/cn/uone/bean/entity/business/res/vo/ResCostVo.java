package cn.uone.bean.entity.business.res.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Auther: Wu
 * @Date: 2018/12/26 09:19
 * @Description:
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResCostVo {

    private String id;
    // 费用配置明细表
    private String costType; // 费用类型
    // 费用阶梯表
    private String sourceType; // 房源类别

    private String billingMethod;//计费方式 (0:按面积,1:按房间)

    private BigDecimal price; // 价格

    private BigDecimal topLimit; // 上限

    private BigDecimal lowerLimit; //下限

    private Boolean isCost; //是否成本

    private BigDecimal total;//计算过的价格
}
