package cn.uone.bean.entity.business.qys;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_qys_authuser")
public class QysAuthuserEntity extends BaseModel<QysAuthuserEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 系统用户ID
     */
    @TableField("sys_user_id")
    private String sysUserId;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;

    /**
     * 手机号码
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 用户身份证号
     */
    @TableField("card_no")
    private String cardNo;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 员工编号
     */
    @TableField("number")
    private String number;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 账号
     */
    @TableField("account_no")
    private String accountNo;

    /**
     * 第三方用户id
     */
    @TableField("open_user_id")
    private String openUserId;

    /**
     * 授权状态 0待授权 1已授权
     */
    @TableField("state")
    private String state;

    /**
     * 启用状态 0停用 1启用
     */
    @TableField("enable")
    private String enable;

    /**
     * 授权截止时间
     */
    @TableField("auth_end_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date authEndDate;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 印章id
     */
    @TableField("seal_id")
    private String sealId;

    /**
     * 印章名称
     */
    @TableField("seal_name")
    private String sealName;

    /**
     * 授权单位名称
     */
    @TableField(exist = false)
    private String authCompanyName;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
