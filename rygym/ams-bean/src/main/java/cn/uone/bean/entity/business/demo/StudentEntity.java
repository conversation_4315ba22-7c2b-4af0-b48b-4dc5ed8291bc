package cn.uone.bean.entity.business.demo;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("demo_student")
public class StudentEntity extends BaseModel<StudentEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "stu_id", type = IdType.UUID)
    private String stuId;

    @TableField("stud_name")
    private String studName;

    @TableField("stu_age")
    private String stuAge;

    @TableField("stu_sex")
    private String stuSex;


    @Override
    public Serializable pkVal() {
        return this.stuId;
    }

}
