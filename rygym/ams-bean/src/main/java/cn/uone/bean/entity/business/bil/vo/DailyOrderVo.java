package cn.uone.bean.entity.business.bil.vo;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by xmlin on 2018-12-15.
 */
@Data
public class DailyOrderVo extends BilOrderEntity implements Serializable {
    /**
     * 项目地址
     */
    private String projectName;
    /**
     * 付款方
     */
    private String rentName;
    /**
     * 房源地址
     */
    private String sourceName;
    /**
     *  租金费用
     */
    private BigDecimal rentPayment;
    /**
     * 押金费用
     */
    private BigDecimal depositPayment;
    /**
     * 意向金费用
     */
    private BigDecimal intentionPayment;
    /**
     * 水费
     */
    private BigDecimal waterPayment;
    /**
     * 电费
     */
    private BigDecimal electricPayment;

    /**
     *  租金手续费用
     */
    private BigDecimal rentFee;
    /**
     * 押金手续费用
     */
    private BigDecimal depositFee;
    /**
     * 意向金手续费用
     */
    private BigDecimal intentionFee;
    /**
     * 水手续费
     */
    private BigDecimal waterFee;
    /**
     * 电手续费
     */
    private BigDecimal electricFee;

    /**
     * 到账金额
     */
    private BigDecimal allPayment;
    /**
     * 总手续费用户
     */
    private BigDecimal allFee;

    /**
     * 备注信息
     */
    private String remarks ;
}
