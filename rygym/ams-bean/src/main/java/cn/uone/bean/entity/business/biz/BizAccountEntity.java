package cn.uone.bean.entity.business.biz;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_account")
public class BizAccountEntity extends BaseModel<BizAccountEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("renter_id")
    private String renterId;

    /**
     * 退款账号
     */
    @NotEmpty(message="账号不能为空")
    @TableField("code")
    private String code;

    /**
     * 开户名
     */
    @NotEmpty(message="开户名不能为空")
    @TableField("name")
    private String name;

    /**
     * 开户行id
     */
    @TableField("bank_id")
    private String bankId;

    @TableField(exist = false)
    private String bankName;

    @TableField(exist = false)
    private String pdf;

    //---------------新版
    @TableField("province_id")
    private String provinceId;

    @TableField("branch_id")
    private String branchId;
    //---------------新版
   /**
     * 支行名称
     */
    @TableField("bank_branch")
    private String bankBranch;

    /**
     * 账号备注
     */
    @TableField("remark")
    private String remark;

    @TableField("is_del")
    private String isDel;

    @TableField("bank_code")
    private String bankCode;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
