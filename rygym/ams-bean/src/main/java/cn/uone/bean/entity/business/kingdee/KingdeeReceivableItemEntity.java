package cn.uone.bean.entity.business.kingdee;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_kingdee_receivable_item")
public class KingdeeReceivableItemEntity extends BaseModel<KingdeeReceivableItemEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("receivable_id")
    private String receivableId;

    /**
     * 应收类型
     */
    @TableField("payment_type")
    private String paymentType;

    /**
     * 子账单类型文本
     */
    @TableField("cz_type")
    private String czType;

    /**
     * 实收金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 税率
     */
    @TableField("tax_rate")
    private BigDecimal taxRate;

    /**
     * 税金
     */
    @TableField("tax_amount")
    private BigDecimal taxAmount;

    /**
     * 不含税金额
     */
    @TableField("no_tax_amount")
    private BigDecimal noTaxAmount;

    /**
     * 客户段
     */
    @TableField("customer")
    private String customer;

    /**
     * 产品段
     */
    @TableField("product")
    private String product;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
