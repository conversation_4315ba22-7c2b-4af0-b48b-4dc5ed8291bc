package cn.uone.bean.entity.business.biz;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_settle")
public class BizSettleEntity extends BaseModel<BizSettleEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("release_id")
    private String releaseId;

    @TableField(exist = false)
    private String address;

    @TableField("source_id")
    private String sourceId;

    @TableField(exist = false)
    private String sourceType;

    @TableField("contract_id")
    private String contractId;

    /**
     * 账单金额
     */
    @TableField("payment")
    private BigDecimal payment;

    @TableField("pay_state")
    private String payState;

    /**
     * 账单类型
     */
    @TableField("order_type")
    private String orderType;

    @TableField("payer_id")
    private String payerId;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
