package cn.uone.bean.entity.business.dev;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 门锁密码表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_dev_lock_password")
public class DevLockPasswordEntity extends BaseModel<DevLockPasswordEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 租客id
     */
    @TableField("renter_id")
    private String renterId;

    /**
     * 密码id
     */
    @TableField("pwd_id")
    private String passwordId;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 租客姓名
     */
    @TableField(exist = false)
    private String name;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
