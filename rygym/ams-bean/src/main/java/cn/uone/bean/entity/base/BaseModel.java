package cn.uone.bean.entity.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

public abstract class BaseModel<T extends Model<?>> extends Model<T>{
    @TableId(
            value = "id",
            type = IdType.UUID
    )
    protected String id;
    @TableField(
            value = "create_date",
            fill = FieldFill.INSERT
    )
    private Date createDate;
    @TableField(
            value = "create_by",
            fill = FieldFill.INSERT
    )
    private String createBy;
    @TableField(
            value = "update_date",
            fill = FieldFill.INSERT_UPDATE
    )
    private Date updateDate;
    @TableField(
            value = "update_by",
            fill = FieldFill.INSERT_UPDATE
    )
    private String updateBy;

    public BaseModel() {
    }

    public String getId() {
        return this.id;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public String getUpdateBy() {
        return this.updateBy;
    }

    public BaseModel<T> setId(final String id) {
        this.id = id;
        return this;
    }

    public BaseModel<T> setCreateDate(final Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public BaseModel<T> setCreateBy(final String createBy) {
        this.createBy = createBy;
        return this;
    }

    public BaseModel<T> setUpdateDate(final Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public BaseModel<T> setUpdateBy(final String updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public String toString() {
        return "BaseModel(id=" + this.getId() + ", createDate=" + this.getCreateDate() + ", createBy=" + this.getCreateBy() + ", updateDate=" + this.getUpdateDate() + ", updateBy=" + this.getUpdateBy() + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof cn.uone.web.base.BaseModel)) {
            return false;
        } else {
            BaseModel<?> other = (BaseModel)o;
            if (!other.canEqual(this)) {
                return false;
            } else if (!super.equals(o)) {
                return false;
            } else {
                label73: {
                    Object this$id = this.getId();
                    Object other$id = other.getId();
                    if (this$id == null) {
                        if (other$id == null) {
                            break label73;
                        }
                    } else if (this$id.equals(other$id)) {
                        break label73;
                    }

                    return false;
                }

                Object this$createDate = this.getCreateDate();
                Object other$createDate = other.getCreateDate();
                if (this$createDate == null) {
                    if (other$createDate != null) {
                        return false;
                    }
                } else if (!this$createDate.equals(other$createDate)) {
                    return false;
                }

                label59: {
                    Object this$createBy = this.getCreateBy();
                    Object other$createBy = other.getCreateBy();
                    if (this$createBy == null) {
                        if (other$createBy == null) {
                            break label59;
                        }
                    } else if (this$createBy.equals(other$createBy)) {
                        break label59;
                    }

                    return false;
                }

                Object this$updateDate = this.getUpdateDate();
                Object other$updateDate = other.getUpdateDate();
                if (this$updateDate == null) {
                    if (other$updateDate != null) {
                        return false;
                    }
                } else if (!this$updateDate.equals(other$updateDate)) {
                    return false;
                }

                Object this$updateBy = this.getUpdateBy();
                Object other$updateBy = other.getUpdateBy();
                if (this$updateBy == null) {
                    if (other$updateBy != null) {
                        return false;
                    }
                } else if (!this$updateBy.equals(other$updateBy)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof BaseModel;
    }

    public int hashCode() {
        //int PRIME = true;
        int result = super.hashCode();
        Object $id = this.getId();
        result = result * 59 + ($id == null ? 43 : $id.hashCode());
        Object $createDate = this.getCreateDate();
        result = result * 59 + ($createDate == null ? 43 : $createDate.hashCode());
        Object $createBy = this.getCreateBy();
        result = result * 59 + ($createBy == null ? 43 : $createBy.hashCode());
        Object $updateDate = this.getUpdateDate();
        result = result * 59 + ($updateDate == null ? 43 : $updateDate.hashCode());
        Object $updateBy = this.getUpdateBy();
        result = result * 59 + ($updateBy == null ? 43 : $updateBy.hashCode());
        return result;
    }
}
