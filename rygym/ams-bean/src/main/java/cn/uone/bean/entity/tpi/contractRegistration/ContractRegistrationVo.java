package cn.uone.bean.entity.tpi.contractRegistration;

import cn.uone.bean.entity.tpi.record.RecordConfigVo;
import lombok.Data;
import java.util.Date;


/**
 * <AUTHOR>
 * @createTime 2024-12-04 15:10
 **/
@Data
public class ContractRegistrationVo{

    private RecordConfigVo configVo;
    // 第三方系统的唯一标识
    private String id;
    // 业务类型（详见字典3.1合同业务类型）
    private String ywlx;
    // 申请人姓名
    private String sqrxm;
    // 申请人手机号
    private String sqrsjh;
    // 申请人证件类型（详见字典3.4人员证件类型）
    private String sqrzjlx;
    // 申请人证件号
    private String sqrzjh;
    // 合同编号
    private String htbh;
    // 原合同备案号（ywlx为2、3、4时为必填）
    private String yhtbabh;
    // 是否由房地产经纪机构待办（详见字典3.10是否）
    private String sfjjdb;
    // 房地产经纪机构名称（sfjjdb为1时必填）
    private String jjjgmc;
    // 房地产经纪机构统一信用代码（sfjjdb为1时必填）
    private String jjjgxydm;
    // 租赁期限开始日期（yyyy-MM-dd）
    private String zlqxks;
    // 租赁期限结束日期（yyyy-MM-dd）
    private String zlqxjs;
    // 合同签订日期（yyyy-MM-dd）
    private String htqdrq;
    // 房屋交付日期（yyyy-MM-dd）
    private String fwjfrq;
    // 是否转租（详见字典3.10是否）
    private String sfzz;
    // 前置合同租赁开始日期（yyyy-MM-dd）
    private String qzhtkssj;
    // 前置合同租赁结束日期（yyyy-MM-dd）
    private String qzhtjzsj;
    // 租赁用途
    private String zlyt;
    // 居住面积
    private Double juzhumianji;
    // 使用面积
    private Double symj;
    // 出租面积
    private Double czmj;
    // 押付方式（详见字典3.8押付方式）
    private String yffs;
    // 支付方式（详见字典3.9支付方式）
    private String zffs;
    // 押金金额（单位元）
    private Double yjje;
    // 是否使用租金贷款（详见字典3.10是否）
    private String sfzjdk;
    // 月租金金额
    private Double zjje;
    // 押金交付日期（yyyy-MM-dd）
    private Date yjjfrq;
    // 首次租金交付日期（yyyy-MM-dd）
    private String sczjjfrq;
    // 房屋套数
    private Integer fwts;
    // 承租人是否为保障对象（详见字典3.10是否）
    private String sfbzdxpz;
    // 合同备注
    private String htbz;
    // 上传房屋租赁合同相关附件，文件上传返回的对象数组转字符串
    private String fjBctk;
    // 上传其他材料扫描件，文件上传返回的对象数组转字符串
    private String fjQt;
    // 注销日期（yyyy-MM-dd）
    private String zxrq;
    // 注销原因
    private String zxyy;
    // 所属项目名称
    private String ssxmmc;
    // 项目所属单位统一社会信用代码
    private String tyshxydm;
    // 权属信息数组
    private CovenantOwner[] covenantOwnerList;
    // 出租方信息数组
    private CovenantHirer[] covenantHirerList;
    // 承租方信息数组
    private CovenantLessee[] covenantLesseeList;

    // 权属信息内部类
    @Data
    public static class CovenantOwner {
        // 权属证件类型（详见字典3.3权属证件类型）
        private String qszjlx;
        // 权属证件号（详见字典4.1权属证件编号规则）
        private String qszjbh;
        // 标准地址（格式：福建省厦门市XX区XX路XXX号XXX室）
        private String baBzdz;
        // 行政区划编码（详见字典3.2附件）
        private String baXzqh;
        // 镇街编码（详见字典3.2附件）
        private String baZj;
        // 村居编码（详见字典3.2附件）
        private String baCj;
        // 房屋性质（详见字典3.11房屋性质）
        private String fwxz;
        // 房屋用途（详见字典3.12房屋用途）
        private String fwyt;
        // 室（房屋用途或房屋性质为10时，必填）
        private Integer wqS;
        // 厅（房屋用途或房屋性质为10时，必填）
        private Integer wqTing;
        // 卫（房屋用途或房屋性质为10时，必填）
        private Integer wqW;
        // 厨（房屋用途或房屋性质为10时，必填）
        private Integer wqC;
        // 租赁方式（详见字典3.5租赁方式）
        private String zlfs;
        // 出租部位（租赁方式为零租时必填）
        private String bw;
        // 装修程度（详见字典3.6装修程度，房屋用途或房屋性质为10时，必填）
        private String zxcd;
        // 建筑面积（使用、居住、建筑面积三选一必填）
        private Double jzmj;
        // 产权人信息数组
        private CovenantOwner.Cqr[] cqrList;

        // 产权人信息内部类
        @Data
        public static class Cqr {
            // 产权人姓名
            private String cqrxm;
            // 产权人证件类型（详见字典3.4人员证件类型）
            private String cqrzjlx;
            // 产权人证件号码
            private String cqrzjhm;
            // 上传产权证扫描件，文件上传返回的对象数组转字符串
            private String cqzsmj;
        }
    }

    // 出租方信息内部类
    @Data
    public static class CovenantHirer {
        // 出租方类型（详见字典3.7备案类型）
        private String hirerType;
        // 出租方姓名
        private String hirerName;
        // 出租方手机号
        private String hirerPhone;
        // 出租方证件类型（详见字典3.4人员证件类型）
        private String hirerCardType;
        // 出租方证件号码
        private String hirerCardNum;
        // 出租方附件，文件上传返回的对象数组转字符串
        private String hirerCardFile;
        // 出租方代理人姓名（出租方类型为企业备案时必填）
        private String hirerAgentName;
        // 出租方代理人手机号（出租方类型为企业备案时必填）
        private String hirerAgentPhone;
        // 出租方代理人证件类型（详见字典3.4人员证件类型，出租方类型为企业备案时必填）
        private String hirerAgentCardType;
        // 出租方代理人证件号码（出租方类型为企业备案时必填）
        private String hirerAgentCardNum;
        // 出租方代理人身份证明附件，文件上传返回的对象数组转字符串（出租方类型为企业备案时必填）
        private String hirerAgentCardFile;
    }

    // 承租方信息内部类
    @Data
    public static class CovenantLessee {
        // 承租方类型（详见字典3.7备案类型）
        private String lesseeType;
        // 承租方姓名
        private String lesseeName;
        // 承租方手机号
        private String lesseePhone;
        // 承租方证件类型（详见字典3.4人员证件类型）
        private String lesseeCardType;
        // 承租方证件号码
        private String lesseeCardNum;
        // 承租方证件，文件上传返回的对象数组转字符串
        private String lesseeCardFile;
        // 承租方代理人姓名（承租方类型为企业备案时必填）
        private String lesseeAgentName;
        // 承租方代理人手机号（承租方类型为企业备案时必填）
        private String lesseeAgentPhone;
        // 承租方代理人证件类型（详见字典3.4人员证件类型，承租方类型为企业备案时必填）
        private String lesseeAgentCardType;
        // 承租方代理人证件号码（承租方类型为企业备案时必填）
        private String lesseeAgentCardNum;
        // 承租方代理人身份证明附件，文件上传返回的对象数组转字符串（承租方类型为企业备案时必填）
        private String lesseeAgentCardFile;
    }

}
