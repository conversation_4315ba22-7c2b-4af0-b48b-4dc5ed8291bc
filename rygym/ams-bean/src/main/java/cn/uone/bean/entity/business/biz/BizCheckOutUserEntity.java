package cn.uone.bean.entity.business.biz;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_check_out_user")
public class BizCheckOutUserEntity extends BaseModel<BizCheckOutUserEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("is_org")
    private String isOrg;

    /**
     * 查房表id
     */
    @TableField("inspect_id")
    private String inspectId;

    /**
     * 租客id
     */
    @TableField("renter_id")
    private String renterId;

    @TableField("contract_source_id")
    private String contractSourceId;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
