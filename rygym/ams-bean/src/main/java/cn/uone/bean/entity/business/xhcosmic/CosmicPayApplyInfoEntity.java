package cn.uone.bean.entity.business.xhcosmic;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import cn.uone.web.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金蝶(星瀚)系统 付款申请单申请明细分录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cosmic_pay_apply_info")
public class CosmicPayApplyInfoEntity extends BaseModel<CosmicPayApplyInfoEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 付款申请id
     */
    @TableField("pay_apply_id")
    private String payApplyId;

    /**
     * 申请明细.申请结算方式(本笔申请期望结算方式，可以是现汇、支票、票据、现金等等。)
     */
    @TableField("settlement_type_number")
    private String settlementTypeNumber;

    /**
     * 申请明细 收款金额
     */
    @TableField("payee_amount")
    private BigDecimal payeeAmount;

    /**
     * 紧急程度
     */
    @TableField("priority")
    private String priority;

    /**
     * 收款人类型 bd_customer:客户, bd_supplier:供应商, bos_user:职员, bos_org:公司, other:其他
     */
    @TableField("payee_type")
    private String payeeType;

    /**
     * 申请明细.收款人
     * 备注：根据收款人类型传不同的数据
     * 客户、供应商——>社会信用代码
     * 公司——>guid/id
     * 职员——>guid
     */
    @TableField("payee_name")
    private String payeeName;

    /**
     * 收款账号
     */
    @TableField("payee_acc_bank_num")
    private String payeeAccBankNum;

    /**
     * 账户名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 收款银行.编码（基础资料：行名行号）
     * 备注：应对收款人类型为其他和职员，或者供应商/客户的银行账户信息不全的情况下，需要填写对应的银行账户的收款银行的行名行号
     */
    @TableField("payee_bank_number")
    private String payeeBankNumber;

    /**
     * 特殊场景关联编号
     */
    @TableField("special_scene")
    private String specialScene;


    @Override
    protected Serializable pkVal() {
        return id;
    }

}
