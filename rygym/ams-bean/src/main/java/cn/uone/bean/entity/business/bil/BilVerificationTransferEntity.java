package cn.uone.bean.entity.business.bil;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 退款/收款确认表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_bil_verification_transfer")
public class BilVerificationTransferEntity extends BaseModel<BilVerificationTransferEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @TableField("prject_id")
    private String prjectId;

    /**
     * 租客id
     */
    @TableField("renter_id")
    private String renterId;

    /**
     * 租客类型
     */
    @TableField("renter_tye")
    private String renterTye;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 已核销金额
     */
    @TableField("verified_payment")
    private BigDecimal verifiedPayment;

    /**
     * 待核销金额
     */
    @TableField("verifying_payment")
    private BigDecimal verifyingPayment;

    /**
     * 付款方式
     */
    @TableField("pay_way")
    private String payWay;

    /**
     * 付款日期 
     */
    @TableField("pay_date")
    private Date payDate;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 支付定单编号
     */
    @TableField("payment_order_code")
    private String paymentOrderCode;

    /**
     * 收款公司id
     */
    @TableField("collection_company_id")
    private String collectionCompanyId;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
