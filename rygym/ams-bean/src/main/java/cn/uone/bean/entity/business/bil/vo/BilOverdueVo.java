package cn.uone.bean.entity.business.bil.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BilOverdueVo {

    private String id;
    /***
     * 逾期天数
     */
    private int overdue;

    /***
     * 应付金额
     */
    private BigDecimal payablePayment;

    /**
     * 应付时间
     */
    private Date payableTime ;

    /**
     * 付款人
     */
    private String name ;

    /**
     * 联系电话
     */
    private String tel ;

    /**
     * 消息推送ID
     */
    private String openid ;

    /**
     * 账单编号
     */
    private String code ;

    /**
     * 合同编号
     */
    private String contractCode ;

    /**
     * 租客ID
     */
    private String renterId ;

    /**
     * 账单类型
     */
    private String orderType ;
}
