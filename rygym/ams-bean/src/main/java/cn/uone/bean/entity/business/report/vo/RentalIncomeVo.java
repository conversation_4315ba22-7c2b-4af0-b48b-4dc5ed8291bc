package cn.uone.bean.entity.business.report.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RentalIncomeVo {
    /**
     * 账单id
     */
    private String id;
    /**
     * 租金期间
     */
    private String period;
    /**
     * 房屋名称
     */
    private String houseName;
    /**
     * 交款人（承租人）
     */
    private String renter;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 开票年月日
     */
    private String invoiceDate;
    /**
     * 发票号
     */
    private String invoiceNumber;
    /**
     * 金额（不含税）
     */
    private BigDecimal noTaxPrice;
    /**
     * 税额
     */
    private BigDecimal taxAmount;
    /**
     * 开票金额（价税合计）
     */
    private BigDecimal totalInvoiceAmount;
    /**
     * 开票税率
     */
    private BigDecimal taxRate;
    /**
     * 收款月份
     */
    private String collectionMonth;
    /**
     * 数量（月份）
     */
    private String monthQuantity;
    /**
     * 月租（不含税）
     */
    private BigDecimal monthlyRentNoTax;
    /**
     * 1月收款金额
     */
    private BigDecimal januaryAmount;
    /**
     * 2月收款金额
     */
    private BigDecimal februaryAmount;
    /**
     * 3月收款金额
     */
    private BigDecimal marchAmount;
    /**
     * 4月收款金额
     */
    private BigDecimal aprilAmount;
    /**
     * 5月收款金额
     */
    private BigDecimal mayAmount;
    /**
     * 6月收款金额
     */
    private BigDecimal juneAmount;
    /**
     * 7月收款金额
     */
    private BigDecimal julyAmount;
    /**
     * 8月收款金额
     */
    private BigDecimal augustAmount;
    /**
     * 9月收款金额
     */
    private BigDecimal septemberAmount;
    /**
     * 10月收款金额
     */
    private BigDecimal octoberAmount;
    /**
     * 11月收款金额
     */
    private BigDecimal novemberAmount;
    /**
     * 12月收款金额
     */
    private BigDecimal decemberAmount;
    /**
     * 下一年待确认
     */
    private BigDecimal nextYear;

    /**
     * 年份
     */
    private String year;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 账单金额
     */
    private String payment;

    private String isZzct;

}
