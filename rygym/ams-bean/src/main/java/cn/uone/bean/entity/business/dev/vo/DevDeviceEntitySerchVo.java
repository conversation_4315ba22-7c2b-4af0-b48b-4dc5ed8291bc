package cn.uone.bean.entity.business.dev.vo;

import lombok.Data;

import java.util.List;

@Data
public class DevDeviceEntitySerchVo  {

    private String purchaseCode;//采购单号
    private String supplierId;
    private String classTypeId;
    private String  classNameId;
    private String  code;
    private String id;
    private String noIn;
    private String projectId;
    private String isPublic;
    //智能设备
    private String intelligence;
    //非智能设备
    private String noIntelligence;
    //导出公区模板设备
    private String exportPublic;
    //设备名称列表
    private List<String> names;
    //房源id
    private String sourceId;
    //设备名称
    private String name;
    //搜索类型
    private String searchType;
    //关键字查询
    private String keyword;
    //所属规划分区id 楼栋id
    private String partitionId;
    //房源类型
    private String sourceType;
    //验收状态
    private String publishTarget;

}
