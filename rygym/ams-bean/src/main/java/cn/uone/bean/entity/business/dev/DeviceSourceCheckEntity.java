package cn.uone.bean.entity.business.dev;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 换房退租结算物品清单检查表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_device_source_check")
public class DeviceSourceCheckEntity extends BaseModel<DeviceSourceCheckEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("device_id")
    private String deviceId;

    @TableField("source_id")
    private String sourceId;

    @TableField("release_id")
    private String releaseId;

    @TableField("contract_id")
    private String contractId;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 设备名称
     */
    @TableField("device_name")
    private String deviceName;

    /**
     * 设备编号
     */
    @TableField("code")
    private String code;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 赔偿金
     */
    @TableField("damage")
    private BigDecimal damage;

    /**
     * 是否删除 默认0未删除
     */
    @TableField("is_discard")
    private BigDecimal isDiscard;

    /**
     * 数据来源
     */
    @TableField("data_from")
    private String dataFrom;

    /**
     * 数量
     */
    @TableField("num")
    private Integer num;

    /**
     * 检查验收状态【1：通过，0：不通过】
     */
    @TableField("check_status")
    private String checkStatus;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;



    @TableField(exist = false)
    List<String> sourceIds= Lists.newArrayList();

    @Override
    public Serializable pkVal() {
        return id;
    }

}
