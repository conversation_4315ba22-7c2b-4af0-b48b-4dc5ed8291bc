package cn.uone.bean.entity.business.rpt;

import cn.uone.bean.entity.base.BaseModel;
import cn.uone.web.util.SafeCompute;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 营收确认明显表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_rpt_revenue")
public class RptRevenueEntity extends BaseModel<RptRevenueEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("contract_id")
    private String contractId;

    @TableField("source_id")
    private String sourceId;

    /**
     * 1：收款，2：冲减
     */
    @TableField("type")
    private String type;

    /**
     * 账单类型
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 账单类型
     */
    @TableField("order_item_type")
    private String orderItemType;

    /**
     * 租凭起日
     */
    @TableField("start_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 租凭止日
     */
    @TableField("end_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;


    /**
     * 合同总金额
     */
    @TableField("total")
    private BigDecimal total;

    /**
     * 年份
     */
    @TableField("report_year")
    private String reportYear;

    /**
     * 一月 (含税金额)
     */
    @TableField("jan")
    private BigDecimal jan;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal janTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal jan1AndTax;

    /**
     * 二月
     */
    @TableField("feb")
    private BigDecimal feb;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal febTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal feb1AndTax;

    /**
     * 三月
     */
    @TableField("mar")
    private BigDecimal mar;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal marTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal mar1AndTax;

    /**
     * 四月
     */
    @TableField("apr")
    private BigDecimal apr;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal aprTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal apr1AndTax;

    /**
     * 五月
     */
    @TableField("may")
    private BigDecimal may;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal mayTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal may1AndTax;

    /**
     * 六月
     */
    @TableField("jun")
    private BigDecimal jun;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal junTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal jun1AndTax;

    /**
     * 七月
     */
    @TableField("jul")
    private BigDecimal jul;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal julTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal jul1AndTax;

    /**
     * 八月
     */
    @TableField("aug")
    private BigDecimal aug;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal augTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal aug1AndTax;

    /**
     * 九月
     */
    @TableField("sept")
    private BigDecimal sept;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal septTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal sept1AndTax;

    /**
     * 十月
     */
    @TableField("oct")
    private BigDecimal oct;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal octTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal oct1AndTax;

    /**
     * 十一月
     */
    @TableField("nov")
    private BigDecimal nov;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal novTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal nov1AndTax;

    /**
     * 十二月
     */
    @TableField("dece")
    private BigDecimal dece;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal deceTax;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal dece1AndTax;

    /**
     * 小计
     */
    @TableField(exist = false)
    private BigDecimal sum = BigDecimal.ZERO;

    /**
     * 税额
     */
    @TableField(exist = false)
    private BigDecimal taxSum = BigDecimal.ZERO;

    /**
     * 营业收入金额
     */
    @TableField(exist = false)
    private BigDecimal all1AndTaxSum = BigDecimal.ZERO;

    public BigDecimal getSum() {
        return SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.
                add(SafeCompute.add(SafeCompute.add(jan, feb), mar), apr), may), jun), jul), aug), sept), oct), nov), dece);
    }

    public BigDecimal getTaxSum() {
        return SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.
                add(SafeCompute.add(SafeCompute.add(janTax, febTax), marTax), aprTax), mayTax), junTax), julTax), augTax), septTax), octTax), novTax), deceTax);
    }

    public BigDecimal getAll1AndTaxSum() {
        return SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.add(SafeCompute.
                add(SafeCompute.add(SafeCompute.add(jan1AndTax, feb1AndTax), mar1AndTax), apr1AndTax), may1AndTax), jun1AndTax), jul1AndTax), aug1AndTax), sept1AndTax), oct1AndTax), nov1AndTax), dece1AndTax);
    }


    @Override
    public Serializable pkVal() {
        return id;
    }

}
