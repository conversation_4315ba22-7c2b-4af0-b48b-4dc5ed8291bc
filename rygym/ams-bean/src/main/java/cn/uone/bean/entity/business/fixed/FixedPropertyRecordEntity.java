package cn.uone.bean.entity.business.fixed;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDate;
import cn.uone.bean.entity.base.BaseModel;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资产修改记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_fixed_property_record")
public class FixedPropertyRecordEntity extends BaseModel<FixedPropertyRecordEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 资产id
     */
    @TableField("property_id")
    private String propertyId;

    /**
     * 审核表id
     */
    @TableField("audit_id")
    private String auditId;

    /**
     * 资产编号
     */
    @TableField("property_code")
    private String propertyCode;

    /**
     * 资产状态
     */
    @TableField("property_sta")
    private String propertySta;

    /**
     * 资产名称
     */
    @TableField("property_name")
    private String propertyName;

    /**
     * 资产分类id
     */
    @TableField("property_type_id")
    private String propertyTypeId;

    /**
     * 资产分类
     */
    @TableField("property_type")
    private String propertyType;

    /**
     * 资产规格
     */
    @TableField("property_specifications")
    private String propertySpecifications;

    /**
     * 负责人
     */
    @TableField("property_head")
    private String propertyHead;

    /**
     * 标签id
     */
    @TableField("rfid_id")
    private String rfidId;

    /**
     * 标签编码
     */
    @TableField("rfid_code")
    private String rfidCode;

    /**
     * 标签类型
     */
    @TableField("rfid_type")
    private String rfidType;

    /**
     * 使用人名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 使用人id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 存放地址id
     */
    @TableField("address_id")
    private String addressId;

    /**
     * 存放地址
     */
    @TableField("address")
    private String address;

    /**
     * 购入日期
     */
    @TableField("purchase_time")
    private LocalDateTime purchaseTime;

    /**
     * 资产来源id
     */
    @TableField("source_type_id")
    private String sourceTypeId;

    /**
     * 资产来源
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 现值
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 部门id
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 使用部门
     */
    @TableField("dept_name")
    private String deptName;

    /**
     * 记录类型(1新增，2修改，3删除)
     */
    @TableField("type")
    private String type;

    /**
     * 审核结果（1.通过，2未通过）
     */
    @TableField("audit_result")
    private String auditResult;

    /**
     * 备注id
     */
    @TableField("remark_id")
    private String remarkId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 资产图片
     */
    @TableField("img")
    private String img;

    /**
     * 二维码地址
     */
    @TableField("qr_code")
    private String qrCode;

    /**
     * 公司id
     */
    @TableField("company_id")
    private String companyId;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 入库时间
     */
    @TableField("put_date")
    private LocalDate putDate;

    /**
     * 数量
     */
    @TableField("quantity")
    private Integer quantity;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
