package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_account_source_rel")
public class ResAccountSourceRelEntity extends BaseModel<ResAccountSourceRelEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("source_id")
    private String sourceId;

    @TableField("account_id")
    private String accountId;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
