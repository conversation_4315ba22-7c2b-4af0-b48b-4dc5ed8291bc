package cn.uone.bean.entity.business.report.vo;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 押金统计报表
 */
@Data
public class DepositStatisticsVo {

    String contractId;

    String sourceId;

    String isOrganize;

    String contractType;

    /**
     * 合同号
     */
    String contractCode;

    /**
     * 签约方
     */
    String name;

    /**
     * 房源坐落
     */
    String sourceName;

    /**
     * 合同状态
     */
    String contractState;

    /**
     * 租赁起日
     */
    Date startDate;

    /**
     * 租赁止日
     */
    Date endDate;

    /**
     * 缴费方式
     */
    String payType;

    /**
     * 应收押金
     */
    BigDecimal cashPledge =BigDecimal.ZERO;

    /**
     * 实收押金
     */
    BigDecimal deposit =BigDecimal.ZERO;

    /**
     * 抵扣金额
     */
    BigDecimal deduct =BigDecimal.ZERO;

    /**
     * 已退押金
     */
    BigDecimal outDeposit =BigDecimal.ZERO;

    /**
     * 罚没押金
     */
    BigDecimal fines =BigDecimal.ZERO;

    /**
     * 押金支付时间
     */
    Date depositPayTime;

    /**
     * 押金清算时间
     */
    Date outPayTime;

    /***
     * 清算状态
     */
    String auditState;

    /**
     * 是否换房
     */
    String changeState;


    public BigDecimal getFines(){
        if(StrUtil.isBlank(auditState)||"1".equals(changeState)||"0".equals(auditState)){
            return this.fines;
        }
        // 罚没押金 = 实收押金 - 抵扣金额 - 已退押金
        if(deposit.compareTo(BigDecimal.ZERO)>0){
            this.fines = this.fines.add(this.deposit);
        }

        this.fines = this.fines.subtract(deduct);
        this.fines = this.fines.subtract(outDeposit);
        if(this.fines.compareTo(BigDecimal.ZERO)<0){
            this.fines = BigDecimal.ZERO;
        }

        return this.fines;
    }

    public String getAuditStateStr(){
        if(StrUtil.isBlank(auditState)){
            return "";
        }else if("1".equals(auditState)){
            return "已清算";
        }else{
            return "未清算";
        }
    }

}
