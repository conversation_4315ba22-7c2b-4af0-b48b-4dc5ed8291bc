package cn.uone.bean.entity.tpi.cosmic;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>金蝶保存错误通用结果</p>
 *
 * {
 * 				"entityKey": "ar_finarbill",
 * 				"entryRowIndex": null,
 * 				"keys": {
 * 					"xmgd_sourcetype": "财务应收单",
 * 					"xmgd_sourcenumber": "J1HHUDUBJE_DJUE3232",
 * 					"xmgd_sourcesystem": "TEST001"
 *                                },
 * 				"rowMsg": ["“来源方式”下“编码=04003”引入不成功。可能的原因是：1、编码不正确；2、不符合基础资料字段查询条件；3、辅助属性类型=1894909217127541760不存在或没有使用权限；"],
 * 				"subEntryRowIndex": null            * 			}
 */
@Data
public class BaseSaveErrorVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单据key
     */
    private String entityKey;

    /**
     * xmgd_sourcetype 来源单据类型
     * xmgd_sourcenumber 来源基础资料编码
     * xmgd_sourcesystem 来源系统
     */
    private Map<String, Object> keys = new HashMap<>();

    /**
     * 错误信息(详细) 所有的错误信息
     */
    private List<String> rowMsg = new ArrayList<>();


    /**
     * 反序列化来源基础信息
     */
    @JSONField(serialize = false, deserialize = false)
    private BasePojo sourceInfo;

    public BasePojo getSourceInfo() {
        if (sourceInfo == null) {
            this.sourceInfo = BasePojo.mapToBean(keys, BasePojo.class);
        }
        return sourceInfo;
    }
}
