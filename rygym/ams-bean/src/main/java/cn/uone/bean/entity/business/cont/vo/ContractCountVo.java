package cn.uone.bean.entity.business.cont.vo;

import cn.uone.bean.entity.business.cont.ContContractEntity;
import lombok.Data;

import java.util.List;

/***
 * 合同统计vo
 */
@Data
public class ContractCountVo {

    /**
     * 市场化签约
     */
    private long shSignNum;
    /**
     * 员工签约
     */
    private long ygSignNum;
    /**
     * 生效合同
     */
    private long totalEffectedNum;
    /**
     * 市场化生效
     */
    private long shEffectedNum;
    /**
     * 员工生效
     */
    private long ygEffectedNum;
    /**
     * 待生效合同
     */
    private long totalToEffectNum;
    /**
     * 市场化待生效
     */
    private long shToEffectNum;
    /**
     * 员工待生效
     */
    private long ygToEffectNum;
    /**
     * 今日市场化签约
     */
    private long todayShSignNum;
    /**
     * 今日员工签约
     */
    private long todayYgSignNum;
    /**
     * 本月市场化签约
     */
    private long monShSignNum;
    /**
     * 本月员工签约
     */
    private long monYgSignNum;
    /**
     * 今年签约
     */
    private long thisYearNum;
    /**
     * 今年市场化签约
     */
    private long yearShSignNum;
    /**
     * 今年员工签约
     */
    private long yearYgSignNum;
    /**
     * 本月生效
     */
    private long monEffectedNum;
    /**
     * 本月市场化生效
     */
    private long monShEffectedNum;
    /**
     * 本月员工生效
     */
    private long monYgEffectedSignNum;
    /**
     * 今年生效
     */
    private long yearEffectedNum;
    /**
     * 今年市场化生效
     */
    private long yearShEffectedNum;
    /**
     * 今年员工生效
     */
    private long yearYgEffectedNum;
    /***
     * 累计签约数
     */
    private long totalTimeNum;
    /***
     * 今日签约数
     */
    private long todayNum;
    /***
     * 本月签约数
     */
    private long thisMonNum;
    /***
     * 待审核数
     */
    private long tobeNum;
    /***
     * 待签约数
     */
    private long waitNum;

    /***
     * 即将到期数（7天）
     */
    private int tobeExpired;

    /**
     * 平均租金
     */
    private double avgRent ;

    /**
     * 今日到期数量
     */
    private int expiredNow;

    /**
     * 今日续租
     */
    private int todayRenewal ;

    /**
     * 合同备案数
     */
    private int recordNum ;


    /**
     * 退租在途套数
     */
    private long totalOuttingNum;

    /**
     * 市场化退租在途套数
     */
    private int shOuttingNum ;

    /**
     * 员工退租在途套数
     */
    private int ygOuttingNum ;

}
