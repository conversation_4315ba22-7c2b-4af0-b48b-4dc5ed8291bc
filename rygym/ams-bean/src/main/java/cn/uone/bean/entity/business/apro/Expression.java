package cn.uone.bean.entity.business.apro;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class Expression {
    private String codeId;
    private String type;
    private String contractId;
    //支付时间
    private Date applyTime;
    private BigDecimal price;
    private String memo;
    private String payWay;
    private String tradeCode;
    private String projectId;
    private String sourceId;

    private BigDecimal deposit;
    private String signer;

    public Expression(){}
    public Expression(String codeId,String type){
        this.codeId=codeId;
        this.type=type;
    }

    public Expression(String codeId,String type,Date applyTime){
        this.codeId=codeId;
        this.type=type;
        this.applyTime=applyTime;
    }

    public Expression(String codeId,String type,Date applyTime,String contractId,String memo,BigDecimal price,String payWay,String tradeCode){
        this.codeId=codeId;
        this.type=type;
        this.applyTime=applyTime;
        this.contractId=contractId;
        this.memo=memo;
        this.price=price;
        this.payWay=payWay;
        this.tradeCode=tradeCode;
    }

    public Expression(String codeId,String type,BigDecimal price,Date applyTime){
        this.codeId=codeId;
        this.type=type;
        this.price=price;
        this.applyTime=applyTime;
    }
}
