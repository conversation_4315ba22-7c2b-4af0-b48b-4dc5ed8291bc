package cn.uone.bean.entity.business.cooperation;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_project_cooperation")
public class ProjectCooperationEntity extends BaseModel<ProjectCooperationEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 内容
     */
    @TableField("content")
    private String content;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 发布时间
     */
    @TableField("publisher_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publisherTime;

    /**
     * 发布人
     */
    @TableField("publisher")
    private String publisher;

    /**
     * 发布人ID
     */
    @TableField("publisher_id")
    private String publisherId;

    /**
     * 发布人ID
     */
    @TableField("project_id")
    private String projectId;



    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
