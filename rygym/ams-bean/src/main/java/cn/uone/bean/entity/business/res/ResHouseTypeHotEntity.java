package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_house_type_hot")
public class ResHouseTypeHotEntity extends BaseModel<ResHouseTypeHotEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 户型id
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 排序
     */
    @TableField("sort")
    private String sort;

    /**
     * 展示状态
     */
    @TableField("state")
    private String state;


    @Override
    public Serializable pkVal() {
        return id;
    }
}
