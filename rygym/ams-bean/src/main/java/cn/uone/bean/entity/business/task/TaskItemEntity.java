package cn.uone.bean.entity.business.task;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 任务项管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_task_item")
public class TaskItemEntity extends BaseModel<TaskItemEntity> {

    private static final long serialVersionUID = 1L;

    /** 任务名称 */
    @TableField("task_name")
    private String taskName;

    /** 任务描述 */
    @TableField("task_description")
    private String taskDescription;

    /** 任务类型 */
    @TableField("task_type")
    private String taskType;


    /** 任务结果选项ID */
    @TableField("option_id")
    private String optionId;

    /** 任务结果类型1 单选 2文本 3 数字  4范围 */
    @TableField("result_type")
    private String resultType;

    /** 任务结果标题 */
    @TableField("result_title")
    private String resultTitle;

    /** 备注 */
    @TableField("remark")
    private String remark;

    @TableField(exist = false)
    private String sublistId;



    @Override
    public Serializable pkVal() {
        return id;
    }

}
