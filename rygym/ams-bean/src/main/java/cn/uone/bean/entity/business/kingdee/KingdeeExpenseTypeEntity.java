package cn.uone.bean.entity.business.kingdee;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_kingdee_expense_type")
public class KingdeeExpenseTypeEntity extends BaseModel<KingdeeExpenseTypeEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 费用类型编码
     */
    @TableField("code")
    private String code;

    /**
     * 费用类型名称
     */
    @TableField("name")
    private String name;

    /**
     * 收付类型
     */
    @TableField("type")
    private String type;

    /**
     * 账单明细类型
     */
    @TableField("order_item_type")
    private String orderItemType;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
