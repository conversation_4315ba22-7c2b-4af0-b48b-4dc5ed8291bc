package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import cn.uone.web.base.annotation.CacheParam;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

/**
 * <p>
 * <p>
 * </p>
 * 价格配置审批记录表实体类
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_source_configure_audit")
public class ResSourceConfigureAuditEntity extends BaseModel<ResSourceConfigureAuditEntity> {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String applicat;
    @TableField(exist = false)
    private Set<String> ids;
    @TableField(exist = false)
    private Boolean isSelf;
    @TableField(exist = false)
    private String taskId;
    @TableField(exist = false)
    private String procInsId;
    @TableField(exist = false)
    private String deployId;
    @TableField("source_configure_id")
    @CacheParam(name = "sourceConfigureId")
    private String sourceConfigureId;

    //房源地址
    @TableField("source_name")
    private String sourceName;

    @TableField("source_id")
    @CacheParam(name = "sourceId")
    private String sourceId;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 底价
     */
    @TableField(value="low_price", strategy = FieldStrategy.IGNORED)
    private BigDecimal lowPrice;

    /**
     * 年增幅
     */
    @TableField(value="year_increase",strategy = FieldStrategy.IGNORED)
    private BigDecimal yearIncrease;

    /**
     * 对外表价
     */
    @TableField(value="price",strategy = FieldStrategy.IGNORED)
    private BigDecimal price;

    //审批状态
    @TableField("approval_state")
    private String approvalState;

    /**
     * 定金
     */
    @TableField(value="deposit",strategy = FieldStrategy.IGNORED)
    private BigDecimal deposit;

    /**
     * 描述
     */
    @TableField("summary")
    private String summary;

    /**
     * 增幅间隔年数
     */
    @TableField(value="year_num",strategy = FieldStrategy.IGNORED)
    private BigDecimal yearNum;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark ;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
