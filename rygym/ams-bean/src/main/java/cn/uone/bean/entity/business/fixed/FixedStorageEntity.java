package cn.uone.bean.entity.business.fixed;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 固定资产入库表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_fixed_storage")
public class FixedStorageEntity extends BaseModel<FixedStorageEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 入库单号
     */
    @TableField("code")
    private String code;

    /**
     * 入库单名称
     */
    @TableField("name")
    private String name;

    /**
     * 入库状态（1待入库，2部分入库，3已入库）
     */
    @TableField("status")
    private String status;

    /**
     * 入库人id
     */
    @TableField("storage_by")
    private String storageBy;

    /**
     * 批次id
     */
    @TableField("batch_id")
    private String batchId;

    /**
     * 总数量
     */
    @TableField("total")
    private Integer total;

    /**
     * 已入库数量
     */
    @TableField("done_quantity")
    private Integer doneQuantity;

    /**
     * 待入库数量
     */
    @TableField("undone_quantity")
    private Integer undoneQuantity;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("finish_time")
    private Date finishTime;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 公司id
     */
    @TableField("company_id")
    private String companyId;


    @TableField(exist = false)
    private String batchCode;


    @TableField(exist = false)
    private String creator;

    @TableField(exist = false)
    private String depositor;

    @TableField(exist = false)
    private List<FixedPropertyEntity> propertyList;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
