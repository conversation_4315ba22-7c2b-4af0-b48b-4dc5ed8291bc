package cn.uone.bean.entity.business.bil.vo;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.BilOrderItemEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by xmlin on 2018-12-15.
 */
@Data
public class BilOrderVo extends BilOrderEntity implements Serializable {



    /**
     * 公司名称（所属公司）
     */
    private String taxOrgName;
    /**
     * 合同性质
     */
    private String platform;
    /**
     * 纸质合同编号
     */
    private String paperCode;

    /**
     * 房源地址
     */
    private String address;
    /**
     * 付款方
     */
    private String payer;
    /**
     * 签约方
     */
    private String singer;
    /**
     * 员工姓名
     */
    private String empName;
    /**
     * 员工电话
     */
    private String empTel;
    /**
     * 账单支付方
     */
    private String payerType;
    /**
     * 合同编号
     */
    private String contractCode;
    /**
     * 合同类型
     */
    private String contractType;
    /**
     * 是否机构
     */
    private String isOrganize;
    /**
     * 账单日期
     */
    private String orderTime;
    /**
     * 申请时间
     */
    private String applyTime;

    /***
     *  账单最小日期
     */
    private Date minDate;
    /***
     *  账单最大日期
     */
    private Date maxDate;

    /**
     * 到账时间
     */
    private Date arriveTime;
    /**
     * 可催付
     */
    private String isPayable;
    /**
     * 催款次数
     */
    private Integer time;

    /**
     * 优惠编码
     */
    private String discountCode;
    /**
     * 优惠id
     */
    private String discountId;

    private Date checkoutDate;

    private String name;
    private String taxpayerCode;
    private String enterpriseAddress;
    private String enterpriseTel;
    private String enterpriseAccount;
    private String enterpriseBank;
    private String projectName;
    private String partitionName;
    private BigDecimal cashPledge;
    private BigDecimal  price;
    private BigDecimal   receivable;
    private Date transferTime;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
    private String signer;
    private String tel;

    private List<BilOrderItemEntity> detail;
    private String singerId;
    private BigDecimal fixAfter;
    /**
     * 固耗 服务费子账单金额
     */
    private BigDecimal itemPayment;

    private String projectId;
    private BigDecimal thisPayment;

    private String auditResult;

    private BigDecimal noTaxPrice;
    private BigDecimal qichuyue;
    /**
     * 权责期初余额负数
     */
    private BigDecimal qichuyuef;
    private BigDecimal qimoyue;
    private BigDecimal qimoyuef;
    /**
     * 权责期末余额不含税
     */
    private BigDecimal nqimoyue;
    /**
     * 权责期末余额不含税负数
     */
    private BigDecimal nqimoyuef;
    /**
     * 累计应确认租金（不含税）
     */
    private BigDecimal allPrice ;
    private String payType;
    private BigDecimal area ;
    private String cStartTime ;
    private String cEndTime ;
    private String startTimeStr ;
    private String endTimeStr ;
    private String payTimeStr ;
    /**
     * 创建人
     */
    private String realName ;

    /**
     * 票据名称
     */
    private String billName ;

    /**
     * 票据类型 意向金 押金
     */
    private String billType ;

    /**
     * 推送状态
     */
    private String pushType ;

    private BigDecimal feeCharge;

    /**
     * 意向金是否转化为押金
     */
    private String changes;

    private BigDecimal tax;

    /**
     * 应收往期租金
     */
    private BigDecimal yswq ;

    /**
     * 本期应收
     */
    private BigDecimal curPayment ;

    /**
     * 实收往期租金
     */
    private BigDecimal sswq ;

    /**
     * 实收本期租金
     */
    private BigDecimal curPay ;

    /**
     * 本月应确认租金
     */
    private BigDecimal mtPayment;

    /**
     * 账单所属周期
     */
    private String curMonth ;

    /**
     * 合同状态
     */
    private String cState ;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 不含税金额
     */
    private BigDecimal amountNoTax;

    /**
     * 税额
     */
    private BigDecimal taxAmount ;

    /***
     *  账单结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date eDate;
    /***
     *  账单开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sDate;

    /**
     * 税点
     */
    private BigDecimal taxPoint ;

    /**
     * 房源id(主要在多房源合同对应的房源id串时使用)
     */
    private String sourceIds;

    /**
     * 项目编码
     */
    private String projectCode;

}
