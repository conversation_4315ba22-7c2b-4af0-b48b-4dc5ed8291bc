package cn.uone.bean.entity.business.supplies;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_supplies_check")
public class SuppliesCheckEntity extends BaseModel<SuppliesCheckEntity> {

    private static final long serialVersionUID = 1L;


    /** 公司id */
    @TableField("company_id")
    private String companyId;

    /**
     * 盘点编号
     */
    @TableField("check_number")
    private String checkNumber;

    /**
     * 盘点批次
     */
    @TableField("check_batch")
    private String checkBatch;

    /**
     * 盘点状态
     */
    @TableField("check_state")
    private String checkState;

    /**
     * 盘点时间
     */
    @TableField("check_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;

    /**
     * 盘点人
     */
    @TableField("checker")
    private String checker;

    /**
     * 盘点人id
     */
    @TableField("checker_id")
    private String checkerId;

    /**
     * 异常情况
     */
    @TableField("abnormalities")
    private String abnormalities;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 物料IDS
     */
    @TableField(exist = false)
    private List<String> propertyIds;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
