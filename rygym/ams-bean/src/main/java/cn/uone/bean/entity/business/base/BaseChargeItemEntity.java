package cn.uone.bean.entity.business.base;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_base_charge_item")
public class BaseChargeItemEntity extends BaseModel<BaseChargeItemEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("name")
    private String name;

    @TableField("charge_id")
    private String chargeId;

    @TableField("is_organize")
    private Boolean organize;

    @TableField("is_enable")
    private Boolean enable;

    @TableField("contract_type")
    private String contractType;

    @TableField("remark")
    private String remark;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
