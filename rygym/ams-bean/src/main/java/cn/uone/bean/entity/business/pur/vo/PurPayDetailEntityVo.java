package cn.uone.bean.entity.business.pur.vo;

import cn.uone.bean.entity.business.pur.PurPayDetailEntity;

import java.math.BigDecimal;
import java.util.List;

public class PurPayDetailEntityVo extends PurPayDetailEntity {

    private BigDecimal totalPrice;//总金额
    private BigDecimal payPrice;//已支付金额
    private BigDecimal auditPrice;//审核中金额
    private BigDecimal maxPrice;//本次最多可申请
    private String realName;//申请人名字
    private List<PurPayDetailEntity> purPayDetailEntityList;

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getPayPrice() {
        return payPrice;
    }

    public void setPayPrice(BigDecimal payPrice) {
        this.payPrice = payPrice;
    }

    public BigDecimal getAuditPrice() {
        return auditPrice;
    }

    public void setAuditPrice(BigDecimal auditPrice) {
        this.auditPrice = auditPrice;
    }

    public List<PurPayDetailEntity> getPurPayDetailEntityList() {
        return purPayDetailEntityList;
    }

    public void setPurPayDetailEntityList(List<PurPayDetailEntity> purPayDetailEntityList) {
        this.purPayDetailEntityList = purPayDetailEntityList;
    }

    public BigDecimal getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }
}
