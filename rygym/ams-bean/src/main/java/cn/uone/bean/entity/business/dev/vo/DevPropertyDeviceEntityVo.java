package cn.uone.bean.entity.business.dev.vo;

import cn.uone.bean.entity.business.dev.DevPropertyDeviceEntity;

public class DevPropertyDeviceEntityVo extends DevPropertyDeviceEntity {

    private String deviceName;//设备名称
    private String supplierName;//供应商名称
    private String maintainName;//维护商名称
    private String projectName;//项目名称
    private String parentId;//查询改分类下的所有物业设备子类
    private String changestate;//是否可改：0不可，1可以

    private String partitionName;//区域名称

    public String getPartitionName() {
        return partitionName;
    }

    public void setPartitionName(String partitionName) {
        this.partitionName = partitionName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getMaintainName() {
        return maintainName;
    }

    public void setMaintainName(String maintainName) {
        this.maintainName = maintainName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getChangestate() {
        return changestate;
    }

    public void setChangestate(String changestate) {
        this.changestate = changestate;
    }
}
