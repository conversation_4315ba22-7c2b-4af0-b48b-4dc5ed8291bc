package cn.uone.bean.entity.business.kingdee;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_kingdee_amortize_rel")
public class KingdeeAmortizeRelEntity extends BaseModel<KingdeeAmortizeRelEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 摊收明细id
     */
    @TableField("amortize_item_id")
    private String amortizeItemId;

    /**
     * 摊销id
     */
    @TableField("revenue_id")
    private String revenueId;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
