package cn.uone.bean.entity.tpi.baiwang;

import lombok.Data;

/**
 * 开票明细
 */
@Data
public class BaiwangCorpTerminalVo {
    /**
     * 开票终端号
     * 长度：30
     * 必填：否
     */
    private String invoiceTerminalCode;
    /**
     * 税控设备类型:0-核心版,1-税控盘,5-UKEY,7-金税盘
     * 必填：否
     */
    private String deviceType;
    /**
     * 开票模式:100-平台托管,101-企业自持
     * 必填：否
     */
    private String invoiceMode;
    /**
     * 发票种类编码列表(支持多选，以英文“,”隔开):
     * 004-增值税专用发票,007-增值税普通发票,025-增值税普通发票(卷式),
     * 026-增值税电子普通发票(默认),028-增值税电子专用发票
     * 长度 30
     * 必填：否
     */
    private String invoiceTypeCodeList;

}
