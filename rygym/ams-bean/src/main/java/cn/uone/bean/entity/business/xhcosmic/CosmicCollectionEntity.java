package cn.uone.bean.entity.business.xhcosmic;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import cn.uone.web.base.BaseModel;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.List;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金蝶(星瀚)系统 财务收款处理单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cosmic_collection")
public class CosmicCollectionEntity extends BaseModel<CosmicCollectionEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 应收来源单号
     */
    @TableField("receivable_number")
    private String receivableNumber;

    /**
     * 是否推送
     */
    @TableField("is_push")
    private Integer isPush;

    /**
     * 是否开发费用
     */
    @TableField("is_dev_free")
    private String isDevFree;

    /**
     * 特殊场景类型
     */
    @TableField("special_type")
    private String specialType;

    /**
     * 特殊场景关联编号
     */
    @TableField("special_bill_number")
    private String specialBillNumber;

    /**
     * 业务日期
     */
    @TableField("biz_date")
    private LocalDateTime bizDate;

    /**
     * 收款类型编码
     */
    @TableField("receiving_type_number")
    private String receivingTypeNumber;

    /**
     * 付款人类型
     */
    @TableField("payer_type")
    private String payerType;

    /**
     * 摘要
     */
    @TableField("txt_description")
    private String txtDescription;

    /**
     * 收款人编码
     */
    @TableField("org_number")
    private String orgNumber;

    /**
     * 核算组织编码
     */
    @TableField("open_org_number")
    private String openOrgNumber;

    /**
     * 收款账户.编码 结算方式非现金时，该字段必填，填银行收款账户
     */
    @TableField("account_bank_number")
    private String accountBankNumber;

    /**
     * 现金账户.编码 结算方式为现金时，该字段必填，填现金收款账户
     */
    @TableField("account_cash_number")
    private String accountCashNumber;

    /**
     * 付款人（编码）客户传统一社会信用代码
     */
    @TableField("payer_name")
    private String payerName;

    /**
     * 付款账号 当结算方式为现金时，非必填
     */
    @TableField("payer_acct_bank_num")
    private String payerAcctBankNum;

    /**
     * 付款银行 当结算方式为现金时，非必填
     */
    @TableField("payer_bank_name")
    private String payerBankName;

    /**
     * 结算方式.编码
     * 使用金蝶“结算方式”基础资料编码
     * 当选择结算方式为数币钱包的业务时，收款方账户和付款方账户需要均为数币钱包账户才可正常确认收款。
     */
    @TableField("settle_type_number")
    private String settleTypeNumber;

    /**
     * URL
     */
    @TableField("url")
    private String url;

    /**
     * 收款金额
     */
    @TableField("act_rec_amt")
    private BigDecimal actRecAmt;

    /**
     * 凭证类型 编码
     */
    @TableField("voucher_number")
    private String voucherNumber;

    /**
     * 账单开始时间
     */
    @TableField("start_date")
    private LocalDateTime startDate;

    /**
     * 账单结束时间
     */
    @TableField("finish_date")
    private LocalDateTime finishDate;

    /**
     * 单据编号(金蝶)
     */
    @TableField("bill_no")
    private String billNo;

    /**
     * 来源系统
     */
    @TableField("source_system")
    private String sourceSystem;

    /**
     * 来源单据类型
     */
    @TableField("source_bill_type")
    private String sourceBillType;

    /**
     * 来源单据编码
     */
    @TableField("source_bill_number")
    private String sourceBillNumber;

    /**
     * 子表 对应金蝶参数类为:CollectionEntryPojo 字段为:entry
     */
    @TableField(exist = false)
    private List<CosmicCollectionItemEntity> items = Lists.newArrayList();


    @Override
    protected Serializable pkVal() {
        return id;
    }

}
