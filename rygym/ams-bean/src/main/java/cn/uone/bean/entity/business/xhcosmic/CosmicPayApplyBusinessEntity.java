package cn.uone.bean.entity.business.xhcosmic;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import cn.uone.web.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金蝶(星瀚)系统 付款申请单业务明细分录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cosmic_pay_apply_business")
public class CosmicPayApplyBusinessEntity extends BaseModel<CosmicPayApplyBusinessEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 付款申请id
     */
    @TableField("pay_apply_id")
    private String payApplyId;

    /**
     * 收款金额（业务明细分收款金额合计与申请明细的合计金额一致）
     */
    @TableField("bus_payee_amount")
    private BigDecimal busPayeeAmount;

    /**
     * 收支项目.编码
     */
    @TableField("expense_item_number")
    private String expenseItemNumber;

    /**
     * 项目编码
     */
    @TableField("project_number")
    private String projectNumber;

    /**
     * 合同号.编码
     */
    @TableField("contract_number")
    private String contractNumber;

    /**
     * 长期待摊项目.编码
     */
    @TableField("long_waiting_number")
    private String longWaitingNumber;

    /**
     * 车牌号 编码
     */
    @TableField("assistant_field_number")
    private String assistantFieldNumber;

    /**
     * 来源方式.编码
     */
    @TableField("source_method_number")
    private String sourceMethodNumber;

    /**
     * 楼栋房号.编码
     */
    @TableField("building_house_number")
    private String buildingHouseNumber;

    /**
     * 业态类型 编码
     */
    @TableField("biz_type_number")
    private String bizTypeNumber;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    @Override
    protected Serializable pkVal() {
        return id;
    }

}
