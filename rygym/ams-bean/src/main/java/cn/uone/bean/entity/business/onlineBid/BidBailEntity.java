package cn.uone.bean.entity.business.onlineBid;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_bid_bail")
public class BidBailEntity extends BaseModel<BidBailEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 招标ID
     */
    @TableField("bid_id")
    private String bidId;


    /**
     * 报名表Id
     */
    @TableField("apply_id")
    private String applyId;


    /**
     * 招标标题
     */
    @TableField("title")
    private String title;

    /**
     * 招标编号
     */
    @TableField("code")
    private String code;

    /**
     * 资产信息
     */
    @TableField("property_info")
    private String propertyInfo;

    /**
     * 资产ID
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 申请人
     */
    @TableField("applicant")
    private String applicant;

    /**
     * 申请id
     */
    @TableField("applicant_id")
    private String applicantId;

    /**
     * 身份证
     */
    @TableField("identity_card")
    private String identityCard;

    /**
     * 联系电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 性质
     */
    @TableField("nature")
    private String nature;

    /**
     * 公司名称
     */
    @TableField("company")
    private String company;

    /**
     * 报名日期
     */
    @TableField("apply_time")
    private Date applyTime;

    /**
     * 保证金金额
     */
    @TableField("earnest_money")
    private BigDecimal earnestMoney;

    /**
     * 缴交情况
     */
    @TableField("pay_status")
    private String payStatus;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    /**
     * 退款状态
     */
    @TableField("refund_status")
    private String refundStatus;

    /**
     * 退款时间
     */
    @TableField("refund_time")
    private String refundTime;

    /**
     *  备注信息
     */
    @TableField("remark")
    private String remark;



    @TableField(exist = false)
    private String playImgUrl;
    @TableField(exist = false)
    private String refundImgUrl;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
