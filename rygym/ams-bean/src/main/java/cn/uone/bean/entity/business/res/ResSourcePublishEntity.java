package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import cn.uone.web.base.annotation.CacheParam;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_source_publish")
public class ResSourcePublishEntity extends BaseModel<ResSourcePublishEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("source_id")
    private String sourceId;

    @TableField(exist = false)
    private String time;
    /**
     * 发布状态
     */
    @TableField("publish_target")
    private String publishTarget;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 出租类型
     */
    @TableField("rent_type")
    private String rentType;

    /**
     * 出租方式
     */
    @TableField("rent_mode")
    private String rentMode;

    /**
     * 是否短租
     */
    @TableField("is_short_rent")
    private Boolean isShortRent;

    /**
     * 房管姓名
     */
    @TableField("manage_name")
    private String manageName;

    /**
     * 房管电话
     */
    @TableField("manage_tel")
    private String manageTel;

    /**
     * 租凭起日
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 房管淘宝会员号
     */
    @TableField("taobao")
    private String taobao;

    /**
     * 支付宝租房/闲鱼租房付款方式
     */
    @TableField("pay_method_one")
    private String payMethodOne;

    /**
     * 春眠/房产之窗付款方式
     */
    @TableField("pay_method_two")
    private String payMethodTwo;

    /**
     * 厦门市住房租赁交易服务系统付款方式
     */
    @TableField("pay_method_xm")
    private String payMethodXm;
    /**
     * 楼房性质
     */
    @TableField("building_nature")
    private String buildingNature;

    /**
     * 第三方平台（1支付宝2闲鱼3蘑菇）
     */
    @TableField("type")
    @CacheParam
    private String type;

    @TableField("third_party_community")
    private String thirdPartyCommunity;

    @TableField("third_party_room")
    private String thirdPartyRoom;

    @TableField("third_party_layout")
    private String thirdPartyLayout;

    @TableField("total_floor")
    private Integer totalFloor;

    @TableField("third_party_images")
    private String thirdPartyImages;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
