package cn.uone.bean.entity.business.bil;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_bil_discount_log")
public class BilDiscountLogEntity extends BaseModel<BilDiscountLogEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 券码
     */
    @TableField("code")
    private String code;

    @TableField("order_id")
    private String orderId;

    @TableField("user_id")
    private String userId;

    @TableField("discount_id")
    private String discountId;

    /**
     * 状态
     */
    @TableField("state")
    private String state;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
