package cn.uone.bean.entity.business.investment;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 品牌库资源
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_investment_resource")
public class InvestmentResourceEntity extends BaseModel<InvestmentResourceEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 品牌名称
     */
    @TableField("name")
    private String name;

    /**
     * 业态
     */
    @TableField("business_name")
    private String businessName;

    /**
     * 业态id
     */
    @TableField("business_id")
    private String businessId;

    /**
     * 品类
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 品类id
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 合作关系
     */
    @TableField("symbiosis")
    private String symbiosis;

    /**
     * 意向等级
     */
    @TableField("intention_level")
    private String intentionLevel;

    /**
     * 客单价起
     */
    @TableField("price_start")
    private String priceStart;

    /**
     * 客单价止
     */
    @TableField("price_end")
    private String priceEnd;

    /**
     * 需求面积
     */
    @TableField("area")
    private String area;

    /**
     * 年开店数量
     */
    @TableField("open_shop_year")
    private Integer openShopYear;

    /**
     * 合作前开店数量
     */
    @TableField("before_cooperation")
    private Integer beforeCooperation;

    /**
     * 经营模式
     */
    @TableField("manage_model")
    private String manageModel;

    /**
     * 属地
     */
    @TableField("dependency")
    private String dependency;

    /**
     * 简介
     */
    @TableField("synopsis")
    private String synopsis;

    /**
     * 状态（1待审核，2已通过3未通过）
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;




    @Override
    public Serializable pkVal() {
        return id;
    }

}
