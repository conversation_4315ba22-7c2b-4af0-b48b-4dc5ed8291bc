package cn.uone.bean.entity.business.fixed;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 租借明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_fixed_lease_details")
public class FixedLeaseDetailsEntity extends BaseModel<FixedLeaseDetailsEntity> {

    private static final long serialVersionUID = 1L;

    /** 公司id */
    @TableField("company_id")
    private String companyId;

    /**
     * 租借表ID
     */
    @TableField("lease_id")
    private String leaseId;

    /**
     * 资产表ID
     */
    @TableField("property_id")
    private String propertyId;

    /**
     * 归还状态
     */
    @TableField("return_status")
    private String returnStatus;

    /**
     * 未归还原因
     */
    @TableField("no_return_cause")
    private String noReturnCause;

    /**
     * 处理备注
     */
    @TableField("dispose_remark")
    private String disposeRemark;

    /**
     * 处理状态
     */
    @TableField("dispose_state")
    private String disposeState;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;



    @Override
    public Serializable pkVal() {
        return id;
    }

}
