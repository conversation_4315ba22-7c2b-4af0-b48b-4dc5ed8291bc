package cn.uone.bean.entity.business.flow;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("act_re_deployment")
public class ActReDeploymentEntity extends BaseModel<ActReDeploymentEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID_", type = IdType.UUID)
    private String id;

    @TableField("NAME_")
    private String name;

    @TableField("CATEGORY_")
    private String category;

    @TableField("KEY_")
    private String key;

    @TableField("TENANT_ID_")
    private String tenantId;

    @TableField("DEPLOY_TIME_")
    private Date deployTime;

    @TableField("DERIVED_FROM_")
    private String derivedFrom;

    @TableField("DERIVED_FROM_ROOT_")
    private String derivedFromRoot;

    @TableField("PARENT_DEPLOYMENT_ID_")
    private String parentDeploymentId;

    @TableField("ENGINE_VERSION_")
    private String engineVersion;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
