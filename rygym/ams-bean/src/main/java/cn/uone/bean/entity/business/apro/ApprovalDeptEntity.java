package cn.uone.bean.entity.business.apro;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_approval_dept")
public class ApprovalDeptEntity extends BaseModel<ApprovalDeptEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("project_id")
    private String projectId;

    /**
     * 业务类型
     */
    @TableField("type")
    private String type;

    /**
     * 派单部门
     */
    @TableField("name")
    private String name;

    /**
     * 部门code
     */
    @TableField("dept_code")
    private String deptCode;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
