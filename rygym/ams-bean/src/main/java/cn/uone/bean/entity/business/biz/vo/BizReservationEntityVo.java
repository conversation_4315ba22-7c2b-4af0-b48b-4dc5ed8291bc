package cn.uone.bean.entity.business.biz.vo;

import cn.uone.bean.entity.business.biz.BizReservationEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-01-02 15:45
 * @Param:
 * @return
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizReservationEntityVo extends BizReservationEntity {
    // 房源名称
    private String sourcename;
    // 租客用户名称
    private String pname;
    //手机号码
    private String tel;
    //是否人才,0否,1是
    private String ispersonal;
    //机构名
    private String ename;
    //预约时间
    private String reservationDate;

    public String getReservationDate() {
        return reservationDate;
    }

    public void setReservationDate(String reservationDate) {
        this.reservationDate = reservationDate;
    }

    public String getSourcename() {
        return sourcename;
    }

    public void setSourcename(String sourcename) {
        this.sourcename = sourcename;
    }

    public String getPname() {
        return pname;
    }

    public void setPname(String pname) {
        this.pname = pname;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getIspersonal() {
        return ispersonal;
    }

    public void setIspersonal(String ispersonal) {
        this.ispersonal = ispersonal;
    }

    public String getEname() {
        return ename;
    }

    public void setEname(String ename) {
        this.ename = ename;
    }
}
