package cn.uone.bean.entity.business.biz;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_biz_reservation")
public class BizReservationEntity extends BaseModel<BizReservationEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("contract_source_id")
    private String contractSourceId;

    /**
     * 租客用户id
     */
    @TableField("renter_id")
    private String renterId;

    /**
     * 预约时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("reservation_time")
    private Date reservationTime;

    /**
     * 预约状态
     */
    @TableField("state")
    private String state;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
