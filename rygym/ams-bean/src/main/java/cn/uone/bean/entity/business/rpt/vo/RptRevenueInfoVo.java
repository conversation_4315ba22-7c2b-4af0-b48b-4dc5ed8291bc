package cn.uone.bean.entity.business.rpt.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class RptRevenueInfoVo {

    /**
     * 年月
     */
    private String yearMonth;

    /**
     * (含税金额)
     */
    private BigDecimal payment;

    /**
     * 税额
     */
    private BigDecimal tax;

    /**
     * 营业收入金额
     */
    private BigDecimal payment1AndTax;

    /**
     * 冲减(含税金额)
     */
    private BigDecimal deductPayment;

    /**
     * 冲减税额
     */
    private BigDecimal deductTax;

    /**
     * 冲减营业收入金额
     */
    private BigDecimal deduct1AndTax;
    /**
     * 冲减(含税金额)
     */
    private BigDecimal paymentTotal;

    /**
     * 冲减税额
     */
    private BigDecimal taxTotal;

    /**
     * 冲减营业收入金额
     */
    private BigDecimal total;

}
