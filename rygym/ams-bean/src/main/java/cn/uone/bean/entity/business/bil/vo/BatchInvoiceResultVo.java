package cn.uone.bean.entity.business.bil.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量开票结果返回对象
 *
 * <AUTHOR>
 * @date 2025/07/01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "批量开票结果返回对象", description = "包含批量开票成功和失败的订单信息")
public class BatchInvoiceResultVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总处理订单数量")
    private int totalCount;

    @ApiModelProperty(value = "成功开票数量")
    private int successCount;

    @ApiModelProperty(value = "失败开票数量")
    private int failureCount;

    @ApiModelProperty(value = "成功开票的订单ID列表")
    private List<String> successList;

    @ApiModelProperty(value = "失败开票的订单详情")
    private List<FailedInvoiceDetail> failureList;

    /**
     * 初始化批量开票结果对象
     * @return 初始化后的结果对象
     */
    public static BatchInvoiceResultVo initialize() {
        BatchInvoiceResultVo result = new BatchInvoiceResultVo();
        result.setTotalCount(0);
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setSuccessList(new ArrayList<>());
        result.setFailureList(new ArrayList<>());
        return result;
    }

    /**
     * 添加成功的订单ID
     * @param orderId 订单ID
     */
    public void addSuccess(String orderId) {
        this.successList.add(orderId);
        this.successCount++;
        this.totalCount++;
    }

    /**
     * 添加失败的订单详情
     * @param orderId 订单ID
     * @param reason 失败原因
     */
    public void addFailure(String orderId, String reason) {
        this.failureList.add(new FailedInvoiceDetail(orderId, reason));
        this.failureCount++;
        this.totalCount++;
    }

    /**
     * 失败开票详情内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FailedInvoiceDetail implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "订单ID")
        private String orderId;

        @ApiModelProperty(value = "失败原因")
        private String reason;
    }
} 