package cn.uone.bean.entity.business.qys;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_qys_contract")
public class QysContractEntity extends BaseModel<QysContractEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 合同ID
     */
    @TableField("contract_id")
    private String contractId;

    /**
     * 签署类型 1合同
     */
    @TableField("sign_type")
    private String signType;

    /**
     * 契约锁合同ID
     */
    @TableField("qys_contract_id")
    private Long qysContractId;

    /**
     * 契约锁合同文档ID
     */
    @TableField("qys_document_id")
    private Long qysDocumentId;

    @TableField("qys_signatory_id")
    private Long qysSignatoryId;

    @TableField("qys_action_id")
    private Long qysActionId;

    /**
     * 合同状态 0待创建 1创建草稿 2上传文档 3发起合同
     */
    @TableField("state")
    private String state;

    /**
     * 是否机构
     */
    @TableField("is_organize")
    private String isOrganize;

    @TableField("remark")
    private String remark;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
