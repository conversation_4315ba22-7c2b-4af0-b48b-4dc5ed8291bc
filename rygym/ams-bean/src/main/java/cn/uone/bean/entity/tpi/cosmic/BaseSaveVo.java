package cn.uone.bean.entity.tpi.cosmic;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *     保存结果响应类 共同字段
 * </p>
 *
 * {
 *  "data":{
 *   "failCount":"0",
 *   "result":[
 *    {
 *     "billIndex":0,
 *     "billStatus":true,
 *     "errors":[],
 *     "id":"1955213429241313280",
 *     "keys":{
 *      "xmgd_sourcetype":"MYERP_Skd",
 *      "xmgd_sourcenumber":"97ef6aaa-f310-48b9-46e2-08db836c4b7e",
 *      "xmgd_sourcesystem":"MYERP"
 *     },
 *     "number":"cs000001",
 *     "type":"Add"
 *    }
 *   ],
 *   "successCount":"1"
 *  }
 * }
 */
@Data
@Accessors(chain = true)
public class BaseSaveVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 失败数量
     */
    private String failCount;

    /**
     * 成功数量
     */
    private String successCount;

    /**
     * 请求结果
     */
    private List<BaseSaveResultVo> result = new ArrayList<>();
}
