package cn.uone.bean.entity.business.sys;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_sys_banner")
public class SysBannerEntity extends BaseModel<SysBannerEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 城市id
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 状态
     */
    @TableField("state")
    private String state;


    /**
     * 链接地址
     */
    @TableField("address")
    private String address;

    /**
     * 排序
     */
    @TableField("sort")
    private String sort;


    @TableField(exist = false)
    private String oldImageId;


    @TableField(exist = false)
    private String oldVideoId;



    @Override
    public Serializable pkVal() {
        return id;
    }

}
