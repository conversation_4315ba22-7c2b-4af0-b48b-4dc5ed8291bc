package cn.uone.bean.entity.business.res.vo;


import cn.uone.bean.entity.business.res.ResProjectHotEntity;
import cn.uone.util.FileUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ResProjectHotVo extends ResProjectHotEntity implements Serializable {
    private String name;
    private String address;
    private String projectName;
    private String pic;
    private String flagConf;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private String priceRange;

    public void setPic(String pic){
        this.pic = FileUtil.getPath(pic);
    }
}
