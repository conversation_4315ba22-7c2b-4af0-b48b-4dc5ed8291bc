package cn.uone.bean.entity.business.act;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_act_enroll")
public class ActEnrollEntity extends BaseModel<ActEnrollEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 活动id
     */
    @TableField("activity_id")
    private String activityId;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;

    /**
     * 手机
     */
    @TableField("tel")
    private String tel;

    /**
     * 额外信息 json格式
     */
    @TableField("extra_info")
    private String extraInfo;

    /**
     * 图形验证码
     */
    @TableField(exist = false)
    private String imageCode;

    @TableField(exist = false)
    private String codeKey;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enrollDate;


    @Override
    public Serializable pkVal() {
        return id;
    }

    public Date getEnrollDate() {
        return getCreateDate();
    }
}
