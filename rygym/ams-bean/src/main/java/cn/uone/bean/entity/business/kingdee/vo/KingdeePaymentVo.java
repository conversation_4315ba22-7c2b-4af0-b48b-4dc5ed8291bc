package cn.uone.bean.entity.business.kingdee.vo;

import cn.uone.application.enumerate.kingdee.PaymentTypeEnum;
import cn.uone.bean.entity.business.kingdee.KingdeePaymentEntity;
import cn.uone.bean.entity.business.kingdee.KingdeePaymentItemEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName KingdeeReceiptVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/2 11:14
 * @Version 1.0
 */
@Data
public class KingdeePaymentVo extends KingdeePaymentEntity implements Serializable {
   private List<KingdeePaymentItemEntity> entrys;

   private String companyName;
   private String paymentBankName;
   private String typeName;
   private String receivingBankName;
   private String customerName;
   private String productName;
   private String projectName;

   public String getTypeName() {
      return PaymentTypeEnum.getNameByValue(this.getType());
   }
}
