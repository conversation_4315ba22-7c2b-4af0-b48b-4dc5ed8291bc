package cn.uone.bean.entity.business.urge;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_urge_pay_record")
public class UrgePayRecordEntity extends BaseModel<UrgePayRecordEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 催缴时间
     */
    @TableField("urge_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date urgeDate;

    /**
     * 账单编号
     */
    @TableField("order_code")
    private String orderCode;
    /**
    /**
     * 催款人
     */
    @TableField("operator")
    private String operator;
    /**
     * 催款人ID
     */
    @TableField("operator_id")
    private String operatorId;
    /**
     * 账单金额
     */
    @TableField("payment")
    private BigDecimal payment;

    /**
     * 账单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 签约人
     */
    @TableField("renter_name")
    private String renterName;

    /**
     * 签约人ID
     */
    @TableField("renter_id")
    private String renterId;

    /**
     * 合同编号
     */
    @TableField("contract_code")
    private String contractCode;

    /**
     * 合同ID
     */
    @TableField("contract_id")
    private String contractId;

    /**
     * 房源名称
     */
    @TableField("source_name")
    private String sourceName;

    /**
     * 房源id
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 催缴详情
     */
    @TableField("info")
    private String info;

    /**
     * 推送日期
     */
    @TableField("push_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pushDate;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
