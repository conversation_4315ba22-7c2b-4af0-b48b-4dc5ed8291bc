package cn.uone.bean.entity.business.kingdee.vo;

import cn.uone.bean.entity.business.kingdee.KingdeeReceiptEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName KingdeeReceiptVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/2 11:14
 * @Version 1.0
 */
@Data
public class KingdeeReceiptSearchVo extends KingdeeReceiptEntity implements Serializable {
    private Date startEntryDate;
    private Date endEntryDate;
}
