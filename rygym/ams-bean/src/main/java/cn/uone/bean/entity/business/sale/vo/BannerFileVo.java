package cn.uone.bean.entity.business.sale.vo;

import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.Date;

/**
 * 推广管理附带图片
 */
@Data
public class BannerFileVo implements Serializable {
    private String id;

    private String cityCode;

    private String name;

    private String state;

    private String sort;

    private String address;

    private String url;

    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    private String path;

    private String projectId;

    private String type;

    public String getPath() {
        return FileUtil.getPath(url);
    }

    private String path2;

    public String getPath2() {
        return "https://demo.domeke.com/saas/"+url;
    }

}
