package cn.uone.bean.entity.tpi.cosmic.collection;

import cn.uone.bean.entity.tpi.cosmic.BasePojo;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 收款处理查询结果类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CollectionQueryVo extends BasePojo implements Serializable {

    /**
     * 业务日期 "yyyy-MM-dd"
     */
    @JSONField(name = "bizdate", format = "yyyy-MM-dd")
    private Date bizDate;

    /**
     * 收款类型.编码
     */
    @JSONField(name = "receivingtype_number")
    private String receivingTypeNumber;

    /**
     * 收款类型.名称
     */
    @JSONField(name = "receivingtype_name")
    private String receivingTypeName;

    /**
     * 付款人类型 CollectionPayerTypeEnum
     */
    @JSONField(name = "payertype")
    private String payerType;

    /**
     * 摘要
     */
    @JSONField(name = "txt_description")
    private String txtDescription;

    /**
     * 收款人编码
     */
    @JSONField(name = "org_number")
    private String orgNumber;

    /**
     * 收款人编码
     */
    @JSONField(name = "org_name")
    private String orgName;

    /**
     * 核算组织编码
     */
    @JSONField(name = "openorg_number")
    private String openOrgNumber;

    /**
     * 收款账号.编码
     */
    @JSONField(name = "accountcash_number")
    private String accountCashNumber;

    /**
     * 付款人名称
     */
    @JSONField(name = "itempayer_name")
    private String itemPayerName;

    /**
     * 付款人编码
     */
    @JSONField(name = "itempayer_number")
    private String itemPayerNumber;

    /**
     * 付款账号 编码
     */
    @JSONField(name = "f7_payeracctbank_number")
    private String payerAcctBankNumber;

    /**
     * 付款账号 账户简称
     */
    @JSONField(name = "f7_payeracctbank_name")
    private String payerAcctBankName;

    /**
     * 付款账号 银行卡号
     */
    @JSONField(name = "f7_payeracctbank_bankaccountnumber")
    private String payerAcctBankBankAccountNumber;

    /**
     * 付款银行 编码
     */
    @JSONField(name = "f7_payerbank_number")
    private String payerBankNumber;

    /**
     * 付款银行 名称
     */
    @JSONField(name = "f7_payerbank_name")
    private String payerBankName;

    /**
     * 收款金额
     */
    @JSONField(name = "actrecamt")
    private BigDecimal actRecAmt;

    /**
     * 特殊场景类型
     */
    @JSONField(name = "xmgd_tscj")
    private String specialType;

    /**
     * 特殊场景关联编号
     */
    @JSONField(name = "xmgd_tscjglbh")
    private String specialBillNumber;

    /**
     * 收款明细
     */
    @JSONField
    private List<CollectionEntryPojo> entry = new ArrayList<>();

}
