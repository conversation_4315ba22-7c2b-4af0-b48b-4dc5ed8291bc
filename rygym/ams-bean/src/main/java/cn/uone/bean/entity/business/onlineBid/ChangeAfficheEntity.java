package cn.uone.bean.entity.business.onlineBid;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 公告管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_bid_change_affiche")
public class ChangeAfficheEntity extends BaseModel<ChangeAfficheEntity> {


    private static final long serialVersionUID = 1L;

    /**
     * 招标ID
     */
    @TableField("bid_id")
    private String bidId;

    /**
     * 公告标题
     */
    @TableField("title")
    private String title;

    /**
     * 公告内容
     */
    @TableField("content")
    private String content;

    /**
     * 公告类型(1招标，2中标，3流标，4变更)
     */
    @TableField("type")
    private String type;

    /**
     * 状态(1未审核2已审核,3已发布，4已撤回)
     */
    @TableField("status")
    private String status;

    /**
     * 发布人
     */
    @TableField("publisher")
    private String publisher;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 楼栋id
     */
    @TableField("partition_id")
    private String partitionId;

    /**
     * 房源id
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 所属项目
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 所属楼栋
     */
    @TableField("partition_name")
    private String partitionName;

    /**
     * 房源
     */
    @TableField("source_name")
    private String sourceName;

    /**
     * 编号
     */
    @TableField("code")
    private String code;

    /**
     * 资产信息
     */
    @TableField("property_info")
    private String propertyInfo;


    /**
     * 报名开始时间
     */
    @TableField("apply_start")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String applyStart;

    /**
     * 报名结束时间
     */
    @TableField("apply_end")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String applyEnd;

    /**
     * 竞价开始时间
     */
    @TableField("bidding_start")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String biddingStart;

    /**
     * 竞价结束时间
     */
    @TableField("bidding_end")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String biddingEnd;

    /**
     * 底价
     */
    @TableField("floor_price")
    private String floorPrice;

    /**
     * 保证金
     */
    @TableField("earnest_money")
    private String earnestMoney;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 单位（1元/平方米/月，2元/月）
     */
    @TableField("units")
    private String units;

    /**
     * 关键字
     */
    @TableField(exist = false)
    private String keyword;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
