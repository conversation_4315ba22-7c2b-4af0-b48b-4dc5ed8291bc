package cn.uone.bean.entity.business.fixed;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 固定资产来源表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_fixed_asset_from")
public class AssetFromEntity extends BaseModel<AssetFromEntity> {

    private static final long serialVersionUID = 1L;

    /** 公司id */
    @TableField("company_id")
    private String companyId;

    /** 来源名称 */
    @TableField("from_name")
    private String fromName;

    /** 项目id */
    @TableField("project_id")
    private String projectId;

    /** 备注 */
    @TableField("remark")
    private String remark;

    @TableField(exist = false)
    private String value;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
