package cn.uone.bean.entity.business.bil.vo;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by cyl on 2024-2-29
 */
@Data
public class ParkOrderVo extends BilOrderEntity implements Serializable {

    /**
     * 账单id集合
     */
    private List<String> ids;

    /**
     *  车牌号
     */
    private String license;

    /**
     * 签约方
     */
    private String singer;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 起租时间
     */

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 止租时间
     */

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;


}
