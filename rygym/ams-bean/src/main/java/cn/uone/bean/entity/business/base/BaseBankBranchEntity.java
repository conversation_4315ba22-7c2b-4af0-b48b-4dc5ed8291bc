package cn.uone.bean.entity.business.base;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_base_bank_branch")
public class BaseBankBranchEntity extends BaseModel<BaseBankBranchEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("bank_id")
    private String bankId;

    /**
     * 编号
     */
    @TableField("code")
    private String code;

    @TableField("name")
    private String name;

    @TableField(exist = false)
    private String value;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
