Subject: [PATCH] 本地适配
---
Index: rygym/ams.ipr
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams.ipr b/rygym/ams.ipr
--- a/rygym/ams.ipr	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams.ipr	(date 1750159307687)
@@ -1,113 +1,4963 @@
 <?xml version="1.0" encoding="UTF-8"?>
-
-<!--
-Licensed to the Apache Software Foundation (ASF) under one
-or more contributor license agreements.  See the NOTICE file
-distributed with this work for additional information
-regarding copyright ownership.  The ASF licenses this file
-to you under the Apache License, Version 2.0 (the
-"License"); you may not use this file except in compliance
-with the License.  You may obtain a copy of the License at
-
-  http://www.apache.org/licenses/LICENSE-2.0
-
-Unless required by applicable law or agreed to in writing,
-software distributed under the License is distributed on an
-"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
-KIND, either express or implied.  See the License for the
-specific language governing permissions and limitations
-under the License.
--->
-<project version="4" relativePaths="false"> 
-  <component name="ProjectRootManager" version="2" assert-keyword="true" project-jdk-name="1.8" jdk-15="true" languageLevel="JDK_1_8"/>  
-  <component name="CodeStyleManager"> 
-    <option name="USE_DEFAULT_CODE_STYLE_SCHEME" value="true"/>  
-    <option name="CODE_STYLE_SCHEME" value=""/> 
-  </component>  
-  <component name="libraryTable"/>  
-  <component name="CompilerConfiguration"> 
-    <option name="DEFAULT_COMPILER" value="Javac"/>  
-    <option name="CLEAR_OUTPUT_DIRECTORY" value="false"/>  
-    <!--
-    <wildcardResourcePatterns>
-      <entry name="${wildcardResourcePattern}"/>
-    </wildcardResourcePatterns>
-    -->  
+<project version="4">
+  <component name="AntConfiguration">
+    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false" />
+    <option name="FILTER_TARGETS" value="false" />
+  </component>
+  <component name="CodeStyleManager">
+    <option name="USE_DEFAULT_CODE_STYLE_SCHEME" value="true" />
+    <option name="CODE_STYLE_SCHEME" value="" />
+  </component>
+  <component name="CompilerConfiguration">
     <wildcardResourcePatterns>
-      <entry name="!?*.java"/>
+      <entry name="!?*.java" />
     </wildcardResourcePatterns>
-  </component>  
-  <component name="JavacSettings"> 
-    <option name="DEBUGGING_INFO" value="true"/>  
-    <option name="GENERATE_NO_WARNINGS" value="false"/>  
-    <option name="DEPRECATION" value="true"/>  
-    <option name="ADDITIONAL_OPTIONS_STRING" value=""/>  
-    <option name="MAXIMUM_HEAP_SIZE" value="128"/>  
-    <option name="USE_GENERICS_COMPILER" value="false"/> 
-  </component>  
-  <component name="JikesSettings"> 
-    <option name="DEBUGGING_INFO" value="true"/>  
-    <option name="DEPRECATION" value="true"/>  
-    <option name="GENERATE_NO_WARNINGS" value="false"/>  
-    <option name="GENERATE_MAKE_FILE_DEPENDENCIES" value="false"/>  
-    <option name="DO_FULL_DEPENDENCE_CHECK" value="false"/>  
-    <option name="IS_INCREMENTAL_MODE" value="false"/>  
-    <option name="IS_EMACS_ERRORS_MODE" value="true"/>  
-    <option name="ADDITIONAL_OPTIONS_STRING" value=""/>  
-    <option name="MAXIMUM_HEAP_SIZE" value="128"/> 
-  </component>  
-  <component name="AntConfiguration"> 
-    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false"/>  
-    <option name="FILTER_TARGETS" value="false"/> 
-  </component>  
-  <component name="JavadocGenerationManager"> 
-    <option name="OUTPUT_DIRECTORY"/>  
-    <option name="OPTION_SCOPE" value="protected"/>  
-    <option name="OPTION_HIERARCHY" value="false"/>  
-    <option name="OPTION_NAVIGATOR" value="false"/>  
-    <option name="OPTION_INDEX" value="false"/>  
-    <option name="OPTION_SEPARATE_INDEX" value="false"/>  
-    <option name="OPTION_USE_1_1" value="false"/>  
-    <option name="OPTION_DOCUMENT_TAG_USE" value="false"/>  
-    <option name="OPTION_DOCUMENT_TAG_AUTHOR" value="false"/>  
-    <option name="OPTION_DOCUMENT_TAG_VERSION" value="false"/>  
-    <option name="OPTION_DOCUMENT_TAG_DEPRECATED" value="false"/>  
-    <option name="OPTION_DEPRECATED_LIST" value="false"/>  
-    <option name="OTHER_OPTIONS"/>  
-    <option name="HEAP_SIZE"/>  
-    <option name="OPEN_IN_BROWSER" value="false"/> 
-  </component>  
-  <component name="JUnitProjectSettings"> 
-    <option name="TEST_RUNNER" value="UI"/> 
-  </component>  
-  <component name="EntryPointsManager"> 
-    <entry_points/> 
-  </component>  
-  <component name="DataSourceManager"/>  
-  <component name="ExportToHTMLSettings"> 
-    <option name="PRINT_LINE_NUMBERS" value="false"/>  
-    <option name="OPEN_IN_BROWSER" value="false"/>  
-    <option name="OUTPUT_DIRECTORY"/> 
-  </component>  
-  <component name="ImportConfiguration"> 
-    <option name="VENDOR"/>  
-    <option name="RELEASE_TAG"/>  
-    <option name="LOG_MESSAGE"/>  
-    <option name="CHECKOUT_AFTER_IMPORT" value="true"/> 
-  </component>  
-  <component name="ProjectModuleManager"> 
-    <modules> 
-      <!-- module filepath="$$PROJECT_DIR$$/${pom.artifactId}.iml"/ -->  
-      <module filepath="$PROJECT_DIR$/ams.iml"/>
-      <module filepath="$PROJECT_DIR$/ams-bean/ams-bean.iml"/>
-      <module filepath="$PROJECT_DIR$/ams-central/ams-business/ams-business.iml"/>
-      <module filepath="$PROJECT_DIR$/ams-central/ams-crm/ams-crm.iml"/>
-      <module filepath="$PROJECT_DIR$/ams-central/ams-tpi/ams-tpi.iml"/>
-      <module filepath="$PROJECT_DIR$/ams-central/ams-job/ams-job.iml"/>
-      <module filepath="$PROJECT_DIR$/ams-central/ams-central.iml"/>
-    </modules> 
-  </component>  
-  <UsedPathMacros> 
-    <!--<macro name="cargo"></macro>--> 
-  </UsedPathMacros> 
-</project>
+    <annotationProcessing>
+      <profile default="true" name="Default" enabled="true" />
+      <profile name="Maven default annotation processors profile" enabled="true">
+        <sourceOutputDir name="target/generated-sources/annotations" />
+        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
+        <outputRelativeToContentRoot value="true" />
+        <module name="ams-bean" />
+        <module name="ams-job" />
+        <module name="ams-business" />
+        <module name="ams-crm" />
+      </profile>
+    </annotationProcessing>
+    <bytecodeTargetLevel>
+      <module name="ams" target="1.8" />
+      <module name="ams-bean" target="1.8" />
+      <module name="ams-business" target="1.8" />
+      <module name="ams-central" target="1.8" />
+      <module name="ams-crm" target="1.8" />
+      <module name="ams-job" target="1.8" />
+      <module name="ams-tpi" target="1.8" />
+    </bytecodeTargetLevel>
+  </component>
+  <component name="Encoding">
+    <file url="file://$PROJECT_DIR$/ams-bean/src/main/java" charset="UTF-8" />
+    <file url="file://$PROJECT_DIR$/ams-central/ams-business/src/main/java" charset="UTF-8" />
+    <file url="file://$PROJECT_DIR$/ams-central/ams-crm/src/main/java" charset="UTF-8" />
+    <file url="file://$PROJECT_DIR$/ams-central/ams-job/src/main/java" charset="UTF-8" />
+    <file url="file://$PROJECT_DIR$/ams-central/src/main/java" charset="UTF-8" />
+    <file url="file://$PROJECT_DIR$/ams-central/src/main/resources" charset="UTF-8" />
+    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
+    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
+  </component>
+  <component name="ExportToHTMLSettings">
+    <option name="PRINT_LINE_NUMBERS" value="false" />
+    <option name="OPEN_IN_BROWSER" value="false" />
+    <option name="OUTPUT_DIRECTORY" />
+  </component>
+  <component name="ExternalStorageConfigurationManager" enabled="true" />
+  <component name="ImportConfiguration">
+    <option name="VENDOR" />
+    <option name="RELEASE_TAG" />
+    <option name="LOG_MESSAGE" />
+    <option name="CHECKOUT_AFTER_IMPORT" value="true" />
+  </component>
+  <component name="InspectionProjectProfileManager">
+    <profile version="1.0">
+      <option name="myName" value="Project Default" />
+    </profile>
+    <version value="1.0" />
+  </component>
+  <component name="JUnitProjectSettings">
+    <option name="TEST_RUNNER" value="UI" />
+  </component>
+  <component name="JavacSettings">
+    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
+      <module name="ams-bean" options="-parameters" />
+      <module name="ams-business" options="-parameters" />
+      <module name="ams-crm" options="-parameters" />
+      <module name="ams-job" options="-parameters" />
+      <module name="ams-tpi" options="-parameters" />
+    </option>
+  </component>
+  <component name="JavadocGenerationManager">
+    <option name="OUTPUT_DIRECTORY" />
+    <option name="OPTION_SCOPE" value="protected" />
+    <option name="OPTION_HIERARCHY" value="false" />
+    <option name="OPTION_NAVIGATOR" value="false" />
+    <option name="OPTION_INDEX" value="false" />
+    <option name="OPTION_SEPARATE_INDEX" value="false" />
+    <option name="OPTION_USE_1_1" value="false" />
+    <option name="OPTION_DOCUMENT_TAG_USE" value="false" />
+    <option name="OPTION_DOCUMENT_TAG_AUTHOR" value="false" />
+    <option name="OPTION_DOCUMENT_TAG_VERSION" value="false" />
+    <option name="OPTION_DOCUMENT_TAG_DEPRECATED" value="false" />
+    <option name="OPTION_DEPRECATED_LIST" value="false" />
+    <option name="OTHER_OPTIONS" />
+    <option name="HEAP_SIZE" />
+    <option name="OPEN_IN_BROWSER" value="false" />
+  </component>
+  <component name="JikesSettings">
+    <option name="DEBUGGING_INFO" value="true" />
+    <option name="DEPRECATION" value="true" />
+    <option name="GENERATE_NO_WARNINGS" value="false" />
+    <option name="GENERATE_MAKE_FILE_DEPENDENCIES" value="false" />
+    <option name="DO_FULL_DEPENDENCE_CHECK" value="false" />
+    <option name="IS_INCREMENTAL_MODE" value="false" />
+    <option name="IS_EMACS_ERRORS_MODE" value="true" />
+    <option name="ADDITIONAL_OPTIONS_STRING" value="" />
+    <option name="MAXIMUM_HEAP_SIZE" value="128" />
+  </component>
+  <component name="MavenProjectsManager">
+    <option name="originalFiles">
+      <list>
+        <option value="$PROJECT_DIR$/pom.xml" />
+      </list>
+    </option>
+  </component>
+  <component name="ProjectModuleManager">
+    <modules>
+      <module fileurl="file://$PROJECT_DIR$/ams.iml" filepath="$PROJECT_DIR$/ams.iml" />
+      <module fileurl="file://$PROJECT_DIR$/ams-bean/ams-bean.iml" filepath="$PROJECT_DIR$/ams-bean/ams-bean.iml" />
+      <module fileurl="file://$PROJECT_DIR$/ams-central/ams-business/ams-business.iml" filepath="$PROJECT_DIR$/ams-central/ams-business/ams-business.iml" />
+      <module fileurl="file://$PROJECT_DIR$/ams-central/ams-central.iml" filepath="$PROJECT_DIR$/ams-central/ams-central.iml" />
+      <module fileurl="file://$PROJECT_DIR$/ams-central/ams-crm/ams-crm.iml" filepath="$PROJECT_DIR$/ams-central/ams-crm/ams-crm.iml" />
+      <module fileurl="file://$PROJECT_DIR$/ams-central/ams-job/ams-job.iml" filepath="$PROJECT_DIR$/ams-central/ams-job/ams-job.iml" />
+      <module fileurl="file://$PROJECT_DIR$/ams-central/ams-tpi/ams-tpi.iml" filepath="$PROJECT_DIR$/ams-central/ams-tpi/ams-tpi.iml" />
+    </modules>
+  </component>
+  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" />
+  <component name="RemoteRepositoriesConfiguration">
+    <remote-repository>
+      <option name="id" value="central" />
+      <option name="name" value="Central Repository" />
+      <option name="url" value="http://maven.uone.cn/repository/maven-public/" />
+    </remote-repository>
+    <remote-repository>
+      <option name="id" value="central" />
+      <option name="name" value="Central Repository" />
+      <option name="url" value="https://repo.maven.apache.org/maven2" />
+    </remote-repository>
+    <remote-repository>
+      <option name="id" value="central" />
+      <option name="name" value="Maven Central repository" />
+      <option name="url" value="https://repo1.maven.org/maven2" />
+    </remote-repository>
+    <remote-repository>
+      <option name="id" value="jboss.community" />
+      <option name="name" value="JBoss Community repository" />
+      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
+    </remote-repository>
+  </component>
+  <component name="VcsDirectoryMappings">
+    <mapping directory="$PROJECT_DIR$/.." vcs="Git" />
+  </component>
+  <component name="libraryTable">
+    <library name="Maven: cn.hutool:hutool-all:5.2.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="cn.hutool" artifactId="hutool-all" version="5.2.5" baseVersion="5.2.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/hutool/hutool-all/5.2.5/hutool-all-5.2.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/hutool/hutool-all/5.2.5/hutool-all-5.2.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/hutool/hutool-all/5.2.5/hutool-all-5.2.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: cn.uone.captcha:EasyCaptcha:1.5.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="cn.uone.captcha" artifactId="EasyCaptcha" version="1.5.0" baseVersion="1.5.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/captcha/EasyCaptcha/1.5.0/EasyCaptcha-1.5.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/captcha/EasyCaptcha/1.5.0/EasyCaptcha-1.5.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/captcha/EasyCaptcha/1.5.0/EasyCaptcha-1.5.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: cn.uone.fadada:fadada_api_sdk:2.0.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="cn.uone.fadada" artifactId="fadada_api_sdk" version="2.0.2" baseVersion="2.0.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/fadada/fadada_api_sdk/2.0.2/fadada_api_sdk-2.0.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/fadada/fadada_api_sdk/2.0.2/fadada_api_sdk-2.0.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/fadada/fadada_api_sdk/2.0.2/fadada_api_sdk-2.0.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: cn.uone:uone-cache-starter:1.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="cn.uone" artifactId="uone-cache-starter" version="1.0.1" baseVersion="1.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-cache-starter/1.0.1/uone-cache-starter-1.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-cache-starter/1.0.1/uone-cache-starter-1.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-cache-starter/1.0.1/uone-cache-starter-1.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: cn.uone:uone-mybatis-starter:1.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="cn.uone" artifactId="uone-mybatis-starter" version="1.0.1" baseVersion="1.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-mybatis-starter/1.0.1/uone-mybatis-starter-1.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-mybatis-starter/1.0.1/uone-mybatis-starter-1.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-mybatis-starter/1.0.1/uone-mybatis-starter-1.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: cn.uone:uone-shiro-starter:1.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="cn.uone" artifactId="uone-shiro-starter" version="1.0.1" baseVersion="1.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-shiro-starter/1.0.1/uone-shiro-starter-1.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-shiro-starter/1.0.1/uone-shiro-starter-1.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-shiro-starter/1.0.1/uone-shiro-starter-1.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: cn.uone:uone-web-starter:1.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="cn.uone" artifactId="uone-web-starter" version="1.0.1" baseVersion="1.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-web-starter/1.0.1/uone-web-starter-1.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-web-starter/1.0.1/uone-web-starter-1.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/cn/uone/uone-web-starter/1.0.1/uone-web-starter-1.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba.cloud:spring-cloud-alibaba-commons:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba.cloud" artifactId="spring-cloud-alibaba-commons" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/cloud/spring-cloud-alibaba-commons/2.2.5.RELEASE/spring-cloud-alibaba-commons-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/cloud/spring-cloud-alibaba-commons/2.2.5.RELEASE/spring-cloud-alibaba-commons-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/cloud/spring-cloud-alibaba-commons/2.2.5.RELEASE/spring-cloud-alibaba-commons-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba.cloud" artifactId="spring-cloud-starter-alibaba-nacos-config" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2.2.5.RELEASE/spring-cloud-starter-alibaba-nacos-config-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2.2.5.RELEASE/spring-cloud-starter-alibaba-nacos-config-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2.2.5.RELEASE/spring-cloud-starter-alibaba-nacos-config-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba.cloud" artifactId="spring-cloud-starter-alibaba-nacos-discovery" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2.2.5.RELEASE/spring-cloud-starter-alibaba-nacos-discovery-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2.2.5.RELEASE/spring-cloud-starter-alibaba-nacos-discovery-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2.2.5.RELEASE/spring-cloud-starter-alibaba-nacos-discovery-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba.fastjson2:fastjson2-extension:2.0.25" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba.fastjson2" artifactId="fastjson2-extension" version="2.0.25" baseVersion="2.0.25" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson2/fastjson2-extension/2.0.25/fastjson2-extension-2.0.25.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson2/fastjson2-extension/2.0.25/fastjson2-extension-2.0.25-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson2/fastjson2-extension/2.0.25/fastjson2-extension-2.0.25-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba.fastjson2:fastjson2:2.0.25" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba.fastjson2" artifactId="fastjson2" version="2.0.25" baseVersion="2.0.25" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson2/fastjson2/2.0.25/fastjson2-2.0.25.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson2/fastjson2/2.0.25/fastjson2-2.0.25-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson2/fastjson2/2.0.25/fastjson2-2.0.25-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba.nacos:nacos-api:1.4.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba.nacos" artifactId="nacos-api" version="1.4.1" baseVersion="1.4.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/nacos/nacos-api/1.4.1/nacos-api-1.4.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/nacos/nacos-api/1.4.1/nacos-api-1.4.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/nacos/nacos-api/1.4.1/nacos-api-1.4.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba.nacos:nacos-client:1.4.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba.nacos" artifactId="nacos-client" version="1.4.1" baseVersion="1.4.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/nacos/nacos-client/1.4.1/nacos-client-1.4.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/nacos/nacos-client/1.4.1/nacos-client-1.4.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/nacos/nacos-client/1.4.1/nacos-client-1.4.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba.nacos:nacos-common:1.4.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba.nacos" artifactId="nacos-common" version="1.4.1" baseVersion="1.4.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/nacos/nacos-common/1.4.1/nacos-common-1.4.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/nacos/nacos-common/1.4.1/nacos-common-1.4.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/nacos/nacos-common/1.4.1/nacos-common-1.4.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba.spring:spring-context-support:1.0.10" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba.spring" artifactId="spring-context-support" version="1.0.10" baseVersion="1.0.10" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/spring/spring-context-support/1.0.10/spring-context-support-1.0.10.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/spring/spring-context-support/1.0.10/spring-context-support-1.0.10-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/spring/spring-context-support/1.0.10/spring-context-support-1.0.10-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba:fastjson:1.2.47" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba" artifactId="fastjson" version="1.2.47" baseVersion="1.2.47" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson/1.2.47/fastjson-1.2.47.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson/1.2.47/fastjson-1.2.47-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson/1.2.47/fastjson-1.2.47-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba:fastjson:1.2.75" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba" artifactId="fastjson" version="1.2.75" baseVersion="1.2.75" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson/1.2.75/fastjson-1.2.75.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson/1.2.75/fastjson-1.2.75-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson/1.2.75/fastjson-1.2.75-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.alibaba:fastjson:2.0.25" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.alibaba" artifactId="fastjson" version="2.0.25" baseVersion="2.0.25" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson/2.0.25/fastjson-2.0.25.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson/2.0.25/fastjson-2.0.25-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/alibaba/fastjson/2.0.25/fastjson-2.0.25-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun.oss:aliyun-sdk-oss:3.10.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun.oss" artifactId="aliyun-sdk-oss" version="3.10.2" baseVersion="3.10.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/oss/aliyun-sdk-oss/3.10.2/aliyun-sdk-oss-3.10.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/oss/aliyun-sdk-oss/3.10.2/aliyun-sdk-oss-3.10.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/oss/aliyun-sdk-oss/3.10.2/aliyun-sdk-oss-3.10.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:alibabacloud-gateway-spi:0.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="alibabacloud-gateway-spi" version="0.0.1" baseVersion="0.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/alibabacloud-gateway-spi/0.0.1/alibabacloud-gateway-spi-0.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/alibabacloud-gateway-spi/0.0.1/alibabacloud-gateway-spi-0.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/alibabacloud-gateway-spi/0.0.1/alibabacloud-gateway-spi-0.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:aliyun-java-sdk-core:2.4.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="aliyun-java-sdk-core" version="2.4.2" baseVersion="2.4.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-core/2.4.2/aliyun-java-sdk-core-2.4.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-core/2.4.2/aliyun-java-sdk-core-2.4.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-core/2.4.2/aliyun-java-sdk-core-2.4.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:aliyun-java-sdk-core:3.4.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="aliyun-java-sdk-core" version="3.4.0" baseVersion="3.4.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:aliyun-java-sdk-dysmsapi:1.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="aliyun-java-sdk-dysmsapi" version="1.0.0" baseVersion="1.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-dysmsapi/1.0.0/aliyun-java-sdk-dysmsapi-1.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-dysmsapi/1.0.0/aliyun-java-sdk-dysmsapi-1.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-dysmsapi/1.0.0/aliyun-java-sdk-dysmsapi-1.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:aliyun-java-sdk-ecs:4.2.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="aliyun-java-sdk-ecs" version="4.2.0" baseVersion="4.2.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:aliyun-java-sdk-kms:2.7.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="aliyun-java-sdk-kms" version="2.7.0" baseVersion="2.7.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-kms/2.7.0/aliyun-java-sdk-kms-2.7.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:aliyun-java-sdk-ram:3.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="aliyun-java-sdk-ram" version="3.0.0" baseVersion="3.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:aliyun-java-sdk-sts:3.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="aliyun-java-sdk-sts" version="3.0.0" baseVersion="3.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:credentials-java:0.3.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="credentials-java" version="0.3.3" baseVersion="0.3.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/credentials-java/0.3.3/credentials-java-0.3.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/credentials-java/0.3.3/credentials-java-0.3.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/credentials-java/0.3.3/credentials-java-0.3.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:dysmsapi20170525:3.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="dysmsapi20170525" version="3.0.0" baseVersion="3.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/dysmsapi20170525/3.0.0/dysmsapi20170525-3.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/dysmsapi20170525/3.0.0/dysmsapi20170525-3.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/dysmsapi20170525/3.0.0/dysmsapi20170525-3.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:endpoint-util:0.0.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="endpoint-util" version="0.0.7" baseVersion="0.0.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/endpoint-util/0.0.7/endpoint-util-0.0.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/endpoint-util/0.0.7/endpoint-util-0.0.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/endpoint-util/0.0.7/endpoint-util-0.0.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:openapiutil:0.2.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="openapiutil" version="0.2.1" baseVersion="0.2.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/openapiutil/0.2.1/openapiutil-0.2.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/openapiutil/0.2.1/openapiutil-0.2.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/openapiutil/0.2.1/openapiutil-0.2.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:tea-openapi:0.3.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="tea-openapi" version="0.3.4" baseVersion="0.3.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea-openapi/0.3.4/tea-openapi-0.3.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea-openapi/0.3.4/tea-openapi-0.3.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea-openapi/0.3.4/tea-openapi-0.3.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:tea-util:0.2.22" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="tea-util" version="0.2.22" baseVersion="0.2.22" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea-util/0.2.22/tea-util-0.2.22.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea-util/0.2.22/tea-util-0.2.22-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea-util/0.2.22/tea-util-0.2.22-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:tea-xml:0.1.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="tea-xml" version="0.1.6" baseVersion="0.1.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea-xml/0.1.6/tea-xml-0.1.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea-xml/0.1.6/tea-xml-0.1.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea-xml/0.1.6/tea-xml-0.1.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.aliyun:tea:1.2.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.aliyun" artifactId="tea" version="1.2.7" baseVersion="1.2.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea/1.2.7/tea-1.2.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea/1.2.7/tea-1.2.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/aliyun/tea/1.2.7/tea-1.2.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.auth0:java-jwt:3.4.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.auth0" artifactId="java-jwt" version="3.4.0" baseVersion="3.4.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/auth0/java-jwt/3.4.0/java-jwt-3.4.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/auth0/java-jwt/3.4.0/java-jwt-3.4.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/auth0/java-jwt/3.4.0/java-jwt-3.4.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.baomidou:mybatis-plus-annotation:3.0.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.baomidou" artifactId="mybatis-plus-annotation" version="3.0.6" baseVersion="3.0.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-annotation/3.0.6/mybatis-plus-annotation-3.0.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-annotation/3.0.6/mybatis-plus-annotation-3.0.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-annotation/3.0.6/mybatis-plus-annotation-3.0.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.baomidou:mybatis-plus-boot-starter:3.0.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.baomidou" artifactId="mybatis-plus-boot-starter" version="3.0.6" baseVersion="3.0.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-boot-starter/3.0.6/mybatis-plus-boot-starter-3.0.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-boot-starter/3.0.6/mybatis-plus-boot-starter-3.0.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-boot-starter/3.0.6/mybatis-plus-boot-starter-3.0.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.baomidou:mybatis-plus-core:3.0.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.baomidou" artifactId="mybatis-plus-core" version="3.0.6" baseVersion="3.0.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-core/3.0.6/mybatis-plus-core-3.0.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-core/3.0.6/mybatis-plus-core-3.0.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-core/3.0.6/mybatis-plus-core-3.0.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.baomidou:mybatis-plus-extension:3.0.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.baomidou" artifactId="mybatis-plus-extension" version="3.0.6" baseVersion="3.0.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-extension/3.0.6/mybatis-plus-extension-3.0.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-extension/3.0.6/mybatis-plus-extension-3.0.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-extension/3.0.6/mybatis-plus-extension-3.0.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.baomidou:mybatis-plus-generator:3.0.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.baomidou" artifactId="mybatis-plus-generator" version="3.0.6" baseVersion="3.0.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-generator/3.0.6/mybatis-plus-generator-3.0.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-generator/3.0.6/mybatis-plus-generator-3.0.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus-generator/3.0.6/mybatis-plus-generator-3.0.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.baomidou:mybatis-plus:3.0.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.baomidou" artifactId="mybatis-plus" version="3.0.6" baseVersion="3.0.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus/3.0.6/mybatis-plus-3.0.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus/3.0.6/mybatis-plus-3.0.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/baomidou/mybatis-plus/3.0.6/mybatis-plus-3.0.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.belerweb:pinyin4j:2.5.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.belerweb" artifactId="pinyin4j" version="2.5.0" baseVersion="2.5.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.beust:jcommander:1.48" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.beust" artifactId="jcommander" version="1.48" baseVersion="1.48" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/beust/jcommander/1.48/jcommander-1.48.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/beust/jcommander/1.48/jcommander-1.48-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/beust/jcommander/1.48/jcommander-1.48-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.carrotsearch.thirdparty:simple-xml-safe:2.7.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.carrotsearch.thirdparty" artifactId="simple-xml-safe" version="2.7.1" baseVersion="2.7.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/carrotsearch/thirdparty/simple-xml-safe/2.7.1/simple-xml-safe-2.7.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/carrotsearch/thirdparty/simple-xml-safe/2.7.1/simple-xml-safe-2.7.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/carrotsearch/thirdparty/simple-xml-safe/2.7.1/simple-xml-safe-2.7.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.domeke:alihouse:1.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.domeke" artifactId="alihouse" version="1.0.0" baseVersion="1.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/domeke/alihouse/1.0.0/alihouse-1.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/domeke/alihouse/1.0.0/alihouse-1.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/domeke/alihouse/1.0.0/alihouse-1.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.domeke:qiyuesuo-private:4.3.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.domeke" artifactId="qiyuesuo-private" version="4.3.7" baseVersion="4.3.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/domeke/qiyuesuo-private/4.3.7/qiyuesuo-private-4.3.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/domeke/qiyuesuo-private/4.3.7/qiyuesuo-private-4.3.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/domeke/qiyuesuo-private/4.3.7/qiyuesuo-private-4.3.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.domeke:qiyuesuo:3.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.domeke" artifactId="qiyuesuo" version="3.7.2" baseVersion="3.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/domeke/qiyuesuo/3.7.2/qiyuesuo-3.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/domeke/qiyuesuo/3.7.2/qiyuesuo-3.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/domeke/qiyuesuo/3.7.2/qiyuesuo-3.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.10.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.fasterxml.jackson.core" artifactId="jackson-annotations" version="2.10.2" baseVersion="2.10.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/core/jackson-annotations/2.10.2/jackson-annotations-2.10.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/core/jackson-annotations/2.10.2/jackson-annotations-2.10.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/core/jackson-annotations/2.10.2/jackson-annotations-2.10.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.fasterxml.jackson.core:jackson-core:2.10.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.fasterxml.jackson.core" artifactId="jackson-core" version="2.10.2" baseVersion="2.10.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/core/jackson-core/2.10.2/jackson-core-2.10.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/core/jackson-core/2.10.2/jackson-core-2.10.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/core/jackson-core/2.10.2/jackson-core-2.10.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.fasterxml.jackson.core:jackson-databind:2.10.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.fasterxml.jackson.core" artifactId="jackson-databind" version="2.10.2" baseVersion="2.10.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/core/jackson-databind/2.10.2/jackson-databind-2.10.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/core/jackson-databind/2.10.2/jackson-databind-2.10.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/core/jackson-databind/2.10.2/jackson-databind-2.10.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.10.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.fasterxml.jackson.datatype" artifactId="jackson-datatype-jdk8" version="2.10.2" baseVersion="2.10.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.2/jackson-datatype-jdk8-2.10.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.2/jackson-datatype-jdk8-2.10.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.2/jackson-datatype-jdk8-2.10.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.fasterxml.jackson.datatype" artifactId="jackson-datatype-jsr310" version="2.10.2" baseVersion="2.10.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.2/jackson-datatype-jsr310-2.10.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.2/jackson-datatype-jsr310-2.10.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.2/jackson-datatype-jsr310-2.10.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.10.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.fasterxml.jackson.module" artifactId="jackson-module-parameter-names" version="2.10.2" baseVersion="2.10.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.2/jackson-module-parameter-names-2.10.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.2/jackson-module-parameter-names-2.10.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.2/jackson-module-parameter-names-2.10.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.fasterxml.uuid:java-uuid-generator:3.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.fasterxml.uuid" artifactId="java-uuid-generator" version="3.3.0" baseVersion="3.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/uuid/java-uuid-generator/3.3.0/java-uuid-generator-3.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/uuid/java-uuid-generator/3.3.0/java-uuid-generator-3.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/uuid/java-uuid-generator/3.3.0/java-uuid-generator-3.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.fasterxml:classmate:1.5.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.fasterxml" artifactId="classmate" version="1.5.1" baseVersion="1.5.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/classmate/1.5.1/classmate-1.5.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/fasterxml/classmate/1.5.1/classmate-1.5.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.github.jai-imageio:jai-imageio-core:1.3.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.github.jai-imageio" artifactId="jai-imageio-core" version="1.3.1" baseVersion="1.3.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/jai-imageio/jai-imageio-core/1.3.1/jai-imageio-core-1.3.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/jai-imageio/jai-imageio-core/1.3.1/jai-imageio-core-1.3.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/jai-imageio/jai-imageio-core/1.3.1/jai-imageio-core-1.3.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.github.jsqlparser:jsqlparser:1.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.github.jsqlparser" artifactId="jsqlparser" version="1.2" baseVersion="1.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/jsqlparser/jsqlparser/1.2/jsqlparser-1.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/jsqlparser/jsqlparser/1.2/jsqlparser-1.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/jsqlparser/jsqlparser/1.2/jsqlparser-1.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.github.jsqlparser:jsqlparser:4.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.github.jsqlparser" artifactId="jsqlparser" version="4.5" baseVersion="4.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/jsqlparser/jsqlparser/4.5/jsqlparser-4.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/jsqlparser/jsqlparser/4.5/jsqlparser-4.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/jsqlparser/jsqlparser/4.5/jsqlparser-4.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.github.pagehelper:pagehelper:5.3.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.github.pagehelper" artifactId="pagehelper" version="5.3.2" baseVersion="5.3.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/pagehelper/pagehelper/5.3.2/pagehelper-5.3.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/pagehelper/pagehelper/5.3.2/pagehelper-5.3.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/pagehelper/pagehelper/5.3.2/pagehelper-5.3.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.github.spotbugs:spotbugs-annotations:4.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.github.spotbugs" artifactId="spotbugs-annotations" version="4.0.0" baseVersion="4.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/spotbugs/spotbugs-annotations/4.0.0/spotbugs-annotations-4.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/spotbugs/spotbugs-annotations/4.0.0/spotbugs-annotations-4.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/spotbugs/spotbugs-annotations/4.0.0/spotbugs-annotations-4.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.github.virtuald:curvesapi:1.06" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.github.virtuald" artifactId="curvesapi" version="1.06" baseVersion="1.06" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/virtuald/curvesapi/1.06/curvesapi-1.06-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/virtuald/curvesapi/1.06/curvesapi-1.06-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.github.xiaoymin:swagger-bootstrap-ui:1.8.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.github.xiaoymin" artifactId="swagger-bootstrap-ui" version="1.8.7" baseVersion="1.8.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/xiaoymin/swagger-bootstrap-ui/1.8.7/swagger-bootstrap-ui-1.8.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/xiaoymin/swagger-bootstrap-ui/1.8.7/swagger-bootstrap-ui-1.8.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/github/xiaoymin/swagger-bootstrap-ui/1.8.7/swagger-bootstrap-ui-1.8.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.google.code.findbugs:jsr305:1.3.9" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.google.code.findbugs" artifactId="jsr305" version="1.3.9" baseVersion="1.3.9" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.google.code.findbugs:jsr305:3.0.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.google.code.findbugs" artifactId="jsr305" version="3.0.2" baseVersion="3.0.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.google.code.gson:gson:2.8.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.google.code.gson" artifactId="gson" version="2.8.6" baseVersion="2.8.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/code/gson/gson/2.8.6/gson-2.8.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/code/gson/gson/2.8.6/gson-2.8.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.google.errorprone:error_prone_annotations:2.0.18" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.google.errorprone" artifactId="error_prone_annotations" version="2.0.18" baseVersion="2.0.18" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/errorprone/error_prone_annotations/2.0.18/error_prone_annotations-2.0.18.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/errorprone/error_prone_annotations/2.0.18/error_prone_annotations-2.0.18-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/errorprone/error_prone_annotations/2.0.18/error_prone_annotations-2.0.18-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.google.guava:guava:23.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.google.guava" artifactId="guava" version="23.0" baseVersion="23.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/guava/guava/23.0/guava-23.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/guava/guava/23.0/guava-23.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/guava/guava/23.0/guava-23.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.google.j2objc:j2objc-annotations:1.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.google.j2objc" artifactId="j2objc-annotations" version="1.1" baseVersion="1.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.google.zxing:core:3.3.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.google.zxing" artifactId="core" version="3.3.3" baseVersion="3.3.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/zxing/core/3.3.3/core-3.3.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/zxing/core/3.3.3/core-3.3.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/zxing/core/3.3.3/core-3.3.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.google.zxing:javase:3.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.google.zxing" artifactId="javase" version="3.3.0" baseVersion="3.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/zxing/javase/3.3.0/javase-3.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/zxing/javase/3.3.0/javase-3.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/google/zxing/javase/3.3.0/javase-3.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.googlecode.aviator:aviator:5.3.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.googlecode.aviator" artifactId="aviator" version="5.3.3" baseVersion="5.3.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/googlecode/aviator/aviator/5.3.3/aviator-5.3.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/googlecode/aviator/aviator/5.3.3/aviator-5.3.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/googlecode/aviator/aviator/5.3.3/aviator-5.3.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.huaweicloud:esdk-obs-java:3.25.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.huaweicloud" artifactId="esdk-obs-java" version="3.25.5" baseVersion="3.25.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/huaweicloud/esdk-obs-java/3.25.5/esdk-obs-java-3.25.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/huaweicloud/esdk-obs-java/3.25.5/esdk-obs-java-3.25.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/huaweicloud/esdk-obs-java/3.25.5/esdk-obs-java-3.25.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.ibeetl:beetl:2.9.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.ibeetl" artifactId="beetl" version="2.9.3" baseVersion="2.9.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/ibeetl/beetl/2.9.3/beetl-2.9.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/ibeetl/beetl/2.9.3/beetl-2.9.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/ibeetl/beetl/2.9.3/beetl-2.9.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.icbc:InfosecCrypto:1.2.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.icbc" artifactId="InfosecCrypto" version="1.2.0" baseVersion="1.2.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/InfosecCrypto/1.2.0/InfosecCrypto-1.2.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/InfosecCrypto/1.2.0/InfosecCrypto-1.2.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/InfosecCrypto/1.2.0/InfosecCrypto-1.2.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.icbc:hsm-software-share:1.0.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.icbc" artifactId="hsm-software-share" version="1.0.5" baseVersion="1.0.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/hsm-software-share/1.0.5/hsm-software-share-1.0.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/hsm-software-share/1.0.5/hsm-software-share-1.0.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/hsm-software-share/1.0.5/hsm-software-share-1.0.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.icbc:icbc-api-sdk-cop-io:1.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.icbc" artifactId="icbc-api-sdk-cop-io" version="1.0.0" baseVersion="1.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/icbc-api-sdk-cop-io/1.0.0/icbc-api-sdk-cop-io-1.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/icbc-api-sdk-cop-io/1.0.0/icbc-api-sdk-cop-io-1.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/icbc-api-sdk-cop-io/1.0.0/icbc-api-sdk-cop-io-1.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.icbc:icbc-api-sdk-cop:1.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.icbc" artifactId="icbc-api-sdk-cop" version="1.0.0" baseVersion="1.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/icbc-api-sdk-cop/1.0.0/icbc-api-sdk-cop-1.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/icbc-api-sdk-cop/1.0.0/icbc-api-sdk-cop-1.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/icbc-api-sdk-cop/1.0.0/icbc-api-sdk-cop-1.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.icbc:icbc-ca:1.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.icbc" artifactId="icbc-ca" version="1.0.0" baseVersion="1.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/icbc-ca/1.0.0/icbc-ca-1.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/icbc-ca/1.0.0/icbc-ca-1.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/icbc/icbc-ca/1.0.0/icbc-ca-1.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.itextpdf.tool:xmlworker:5.5.13" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.itextpdf.tool" artifactId="xmlworker" version="5.5.13" baseVersion="5.5.13" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/itextpdf/tool/xmlworker/5.5.13/xmlworker-5.5.13.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/itextpdf/tool/xmlworker/5.5.13/xmlworker-5.5.13-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/itextpdf/tool/xmlworker/5.5.13/xmlworker-5.5.13-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.itextpdf:itext-asian:5.2.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.itextpdf" artifactId="itext-asian" version="5.2.0" baseVersion="5.2.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/itextpdf/itext-asian/5.2.0/itext-asian-5.2.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/itextpdf/itext-asian/5.2.0/itext-asian-5.2.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/itextpdf/itext-asian/5.2.0/itext-asian-5.2.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.itextpdf:itextpdf:5.5.13" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.itextpdf" artifactId="itextpdf" version="5.5.13" baseVersion="5.5.13" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/itextpdf/itextpdf/5.5.13/itextpdf-5.5.13.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/itextpdf/itextpdf/5.5.13/itextpdf-5.5.13-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/itextpdf/itextpdf/5.5.13/itextpdf-5.5.13-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.jcraft.jzlib:com.springsource.com.jcraft.jzlib:1.0.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.jcraft.jzlib" artifactId="com.springsource.com.jcraft.jzlib" version="1.0.7" baseVersion="1.0.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/jcraft/jzlib/com.springsource.com.jcraft.jzlib/1.0.7/com.springsource.com.jcraft.jzlib-1.0.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/jcraft/jzlib/com.springsource.com.jcraft.jzlib/1.0.7/com.springsource.com.jcraft.jzlib-1.0.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/jcraft/jzlib/com.springsource.com.jcraft.jzlib/1.0.7/com.springsource.com.jcraft.jzlib-1.0.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.jcraft:jsch:0.1.54" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.jcraft" artifactId="jsch" version="0.1.54" baseVersion="0.1.54" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/jcraft/jsch/0.1.54/jsch-0.1.54.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/jcraft/jsch/0.1.54/jsch-0.1.54-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/jcraft/jsch/0.1.54/jsch-0.1.54-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.jcraft:jsch:0.1.55" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.jcraft" artifactId="jsch" version="0.1.55" baseVersion="0.1.55" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/jcraft/jsch/0.1.55/jsch-0.1.55.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/jcraft/jsch/0.1.55/jsch-0.1.55-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/jcraft/jsch/0.1.55/jsch-0.1.55-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.mchange:mchange-commons-java:0.2.15" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.mchange" artifactId="mchange-commons-java" version="0.2.15" baseVersion="0.2.15" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/mchange/mchange-commons-java/0.2.15/mchange-commons-java-0.2.15.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/mchange/mchange-commons-java/0.2.15/mchange-commons-java-0.2.15-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/mchange/mchange-commons-java/0.2.15/mchange-commons-java-0.2.15-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.archaius:archaius-core:0.7.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.archaius" artifactId="archaius-core" version="0.7.6" baseVersion="0.7.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/archaius/archaius-core/0.7.6/archaius-core-0.7.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/archaius/archaius-core/0.7.6/archaius-core-0.7.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/archaius/archaius-core/0.7.6/archaius-core-0.7.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.hystrix:hystrix-core:1.5.18" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.hystrix" artifactId="hystrix-core" version="1.5.18" baseVersion="1.5.18" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/hystrix/hystrix-core/1.5.18/hystrix-core-1.5.18.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/hystrix/hystrix-core/1.5.18/hystrix-core-1.5.18-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/hystrix/hystrix-core/1.5.18/hystrix-core-1.5.18-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.netflix-commons:netflix-commons-util:0.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.netflix-commons" artifactId="netflix-commons-util" version="0.3.0" baseVersion="0.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/netflix-commons/netflix-commons-util/0.3.0/netflix-commons-util-0.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/netflix-commons/netflix-commons-util/0.3.0/netflix-commons-util-0.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/netflix-commons/netflix-commons-util/0.3.0/netflix-commons-util-0.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.netflix-commons:netflix-statistics:0.1.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.netflix-commons" artifactId="netflix-statistics" version="0.1.1" baseVersion="0.1.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/netflix-commons/netflix-statistics/0.1.1/netflix-statistics-0.1.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/netflix-commons/netflix-statistics/0.1.1/netflix-statistics-0.1.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/netflix-commons/netflix-statistics/0.1.1/netflix-statistics-0.1.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.ribbon:ribbon-core:2.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.ribbon" artifactId="ribbon-core" version="2.3.0" baseVersion="2.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-core/2.3.0/ribbon-core-2.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-core/2.3.0/ribbon-core-2.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-core/2.3.0/ribbon-core-2.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.ribbon:ribbon-httpclient:2.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.ribbon" artifactId="ribbon-httpclient" version="2.3.0" baseVersion="2.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-httpclient/2.3.0/ribbon-httpclient-2.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-httpclient/2.3.0/ribbon-httpclient-2.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-httpclient/2.3.0/ribbon-httpclient-2.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.ribbon:ribbon-loadbalancer:2.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.ribbon" artifactId="ribbon-loadbalancer" version="2.3.0" baseVersion="2.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-loadbalancer/2.3.0/ribbon-loadbalancer-2.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-loadbalancer/2.3.0/ribbon-loadbalancer-2.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-loadbalancer/2.3.0/ribbon-loadbalancer-2.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.ribbon:ribbon-transport:2.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.ribbon" artifactId="ribbon-transport" version="2.3.0" baseVersion="2.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-transport/2.3.0/ribbon-transport-2.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-transport/2.3.0/ribbon-transport-2.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon-transport/2.3.0/ribbon-transport-2.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.ribbon:ribbon:2.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.ribbon" artifactId="ribbon" version="2.3.0" baseVersion="2.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon/2.3.0/ribbon-2.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon/2.3.0/ribbon-2.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/ribbon/ribbon/2.3.0/ribbon-2.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.netflix.servo:servo-core:0.12.21" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.netflix.servo" artifactId="servo-core" version="0.12.21" baseVersion="0.12.21" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/servo/servo-core/0.12.21/servo-core-0.12.21.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/servo/servo-core/0.12.21/servo-core-0.12.21-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/netflix/servo/servo-core/0.12.21/servo-core-0.12.21-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.nuonuo:open-sdk:1.0.5.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.nuonuo" artifactId="open-sdk" version="1.0.5.2" baseVersion="1.0.5.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/nuonuo/open-sdk/1.0.5.2/open-sdk-1.0.5.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/nuonuo/open-sdk/1.0.5.2/open-sdk-1.0.5.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/nuonuo/open-sdk/1.0.5.2/open-sdk-1.0.5.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.squareup.okhttp3:okhttp:3.14.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.squareup.okhttp3" artifactId="okhttp" version="3.14.7" baseVersion="3.14.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okhttp3/okhttp/3.14.7/okhttp-3.14.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okhttp3/okhttp/3.14.7/okhttp-3.14.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okhttp3/okhttp/3.14.7/okhttp-3.14.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.squareup.okhttp3:okhttp:4.3.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.squareup.okhttp3" artifactId="okhttp" version="4.3.1" baseVersion="4.3.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okhttp3/okhttp/4.3.1/okhttp-4.3.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okhttp3/okhttp/4.3.1/okhttp-4.3.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okhttp3/okhttp/4.3.1/okhttp-4.3.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.squareup.okio:okio-jvm:3.8.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.squareup.okio" artifactId="okio-jvm" version="3.8.0" baseVersion="3.8.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okio/okio-jvm/3.8.0/okio-jvm-3.8.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okio/okio-jvm/3.8.0/okio-jvm-3.8.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okio/okio-jvm/3.8.0/okio-jvm-3.8.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.squareup.okio:okio:2.4.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.squareup.okio" artifactId="okio" version="2.4.1" baseVersion="2.4.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okio/okio/2.4.1/okio-2.4.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okio/okio/2.4.1/okio-2.4.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okio/okio/2.4.1/okio-2.4.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.squareup.okio:okio:3.8.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.squareup.okio" artifactId="okio" version="3.8.0" baseVersion="3.8.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okio/okio/3.8.0/okio-3.8.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okio/okio/3.8.0/okio-3.8.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/squareup/okio/okio/3.8.0/okio-3.8.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.sun.activation:jakarta.activation:1.2.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.sun.activation" artifactId="jakarta.activation" version="1.2.2" baseVersion="1.2.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/activation/jakarta.activation/1.2.2/jakarta.activation-1.2.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/activation/jakarta.activation/1.2.2/jakarta.activation-1.2.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/activation/jakarta.activation/1.2.2/jakarta.activation-1.2.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.sun.istack:istack-commons-runtime:4.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.sun.istack" artifactId="istack-commons-runtime" version="4.0.1" baseVersion="4.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/istack/istack-commons-runtime/4.0.1/istack-commons-runtime-4.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/istack/istack-commons-runtime/4.0.1/istack-commons-runtime-4.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/istack/istack-commons-runtime/4.0.1/istack-commons-runtime-4.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.sun.jersey.contribs:jersey-apache-client4:1.19.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.sun.jersey.contribs" artifactId="jersey-apache-client4" version="1.19.1" baseVersion="1.19.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/jersey/contribs/jersey-apache-client4/1.19.1/jersey-apache-client4-1.19.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/jersey/contribs/jersey-apache-client4/1.19.1/jersey-apache-client4-1.19.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/jersey/contribs/jersey-apache-client4/1.19.1/jersey-apache-client4-1.19.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.sun.jersey:jersey-client:1.19.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.sun.jersey" artifactId="jersey-client" version="1.19.1" baseVersion="1.19.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/jersey/jersey-client/1.19.1/jersey-client-1.19.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/jersey/jersey-client/1.19.1/jersey-client-1.19.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/jersey/jersey-client/1.19.1/jersey-client-1.19.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.sun.jersey:jersey-core:1.19.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.sun.jersey" artifactId="jersey-core" version="1.19.1" baseVersion="1.19.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/jersey/jersey-core/1.19.1/jersey-core-1.19.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/jersey/jersey-core/1.19.1/jersey-core-1.19.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/jersey/jersey-core/1.19.1/jersey-core-1.19.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.sun.mail:javax.mail:1.5.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.sun.mail" artifactId="javax.mail" version="1.5.6" baseVersion="1.5.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/mail/javax.mail/1.5.6/javax.mail-1.5.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/mail/javax.mail/1.5.6/javax.mail-1.5.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/mail/javax.mail/1.5.6/javax.mail-1.5.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.sun.xml.bind:jaxb-core:2.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.sun.xml.bind" artifactId="jaxb-core" version="2.3.0" baseVersion="2.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/xml/bind/jaxb-core/2.3.0/jaxb-core-2.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/xml/bind/jaxb-core/2.3.0/jaxb-core-2.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/xml/bind/jaxb-core/2.3.0/jaxb-core-2.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.sun.xml.bind:jaxb-impl:2.3.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.sun.xml.bind" artifactId="jaxb-impl" version="2.3.0" baseVersion="2.3.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/xml/bind/jaxb-impl/2.3.0/jaxb-impl-2.3.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/xml/bind/jaxb-impl/2.3.0/jaxb-impl-2.3.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/sun/xml/bind/jaxb-impl/2.3.0/jaxb-impl-2.3.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.thoughtworks.xstream:xstream:1.4.20" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.thoughtworks.xstream" artifactId="xstream" version="1.4.20" baseVersion="1.4.20" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/thoughtworks/xstream/xstream/1.4.20/xstream-1.4.20.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/thoughtworks/xstream/xstream/1.4.20/xstream-1.4.20-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/thoughtworks/xstream/xstream/1.4.20/xstream-1.4.20-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.uone:mogoroom:1.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.uone" artifactId="mogoroom" version="1.0.0" baseVersion="1.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/uone/mogoroom/1.0.0/mogoroom-1.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/uone/mogoroom/1.0.0/mogoroom-1.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/uone/mogoroom/1.0.0/mogoroom-1.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.uone:taobao:1.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.uone" artifactId="taobao" version="1.0.0" baseVersion="1.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/uone/taobao/1.0.0/taobao-1.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/uone/taobao/1.0.0/taobao-1.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/uone/taobao/1.0.0/taobao-1.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.zaxxer:HikariCP:3.4.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.zaxxer" artifactId="HikariCP" version="3.4.2" baseVersion="3.4.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: com.zaxxer:SparseBitSet:1.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="com.zaxxer" artifactId="SparseBitSet" version="1.2" baseVersion="1.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-beanutils:commons-beanutils:1.8.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-beanutils" artifactId="commons-beanutils" version="1.8.0" baseVersion="1.8.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-beanutils/commons-beanutils/1.8.0/commons-beanutils-1.8.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-beanutils/commons-beanutils/1.8.0/commons-beanutils-1.8.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-beanutils/commons-beanutils/1.8.0/commons-beanutils-1.8.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-beanutils:commons-beanutils:1.8.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-beanutils" artifactId="commons-beanutils" version="1.8.3" baseVersion="1.8.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-beanutils/commons-beanutils/1.8.3/commons-beanutils-1.8.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-beanutils/commons-beanutils/1.8.3/commons-beanutils-1.8.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-beanutils/commons-beanutils/1.8.3/commons-beanutils-1.8.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-beanutils:commons-beanutils:1.9.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-beanutils" artifactId="commons-beanutils" version="1.9.4" baseVersion="1.9.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-codec:commons-codec:1.10" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-codec" artifactId="commons-codec" version="1.10" baseVersion="1.10" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-codec/commons-codec/1.10/commons-codec-1.10.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-codec/commons-codec/1.10/commons-codec-1.10-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-codec/commons-codec/1.10/commons-codec-1.10-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-collections:commons-collections:3.2.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-collections" artifactId="commons-collections" version="3.2.1" baseVersion="3.2.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-configuration:commons-configuration:1.8" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-configuration" artifactId="commons-configuration" version="1.8" baseVersion="1.8" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-configuration/commons-configuration/1.8/commons-configuration-1.8.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-configuration/commons-configuration/1.8/commons-configuration-1.8-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-configuration/commons-configuration/1.8/commons-configuration-1.8-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-fileupload:commons-fileupload:1.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-fileupload" artifactId="commons-fileupload" version="1.5" baseVersion="1.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-httpclient:commons-httpclient:3.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-httpclient" artifactId="commons-httpclient" version="3.1" baseVersion="3.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-httpclient/commons-httpclient/3.1/commons-httpclient-3.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-httpclient/commons-httpclient/3.1/commons-httpclient-3.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-httpclient/commons-httpclient/3.1/commons-httpclient-3.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-io:commons-io:2.11.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-io" artifactId="commons-io" version="2.11.0" baseVersion="2.11.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-io/commons-io/2.11.0/commons-io-2.11.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-io/commons-io/2.11.0/commons-io-2.11.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-lang:commons-lang:2.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-lang" artifactId="commons-lang" version="2.6" baseVersion="2.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-lang/commons-lang/2.6/commons-lang-2.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-lang/commons-lang/2.6/commons-lang-2.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-lang/commons-lang/2.6/commons-lang-2.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-logging:commons-logging:1.0.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-logging" artifactId="commons-logging" version="1.0.4" baseVersion="1.0.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-logging/commons-logging/1.0.4/commons-logging-1.0.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-logging/commons-logging/1.0.4/commons-logging-1.0.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-logging/commons-logging/1.0.4/commons-logging-1.0.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: commons-logging:commons-logging:1.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="commons-logging" artifactId="commons-logging" version="1.2" baseVersion="1.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-logging/commons-logging/1.2/commons-logging-1.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-logging/commons-logging/1.2/commons-logging-1.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/commons-logging/commons-logging/1.2/commons-logging-1.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: de.codecentric:spring-boot-admin-client:2.2.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="de.codecentric" artifactId="spring-boot-admin-client" version="2.2.3" baseVersion="2.2.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/de/codecentric/spring-boot-admin-client/2.2.3/spring-boot-admin-client-2.2.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/de/codecentric/spring-boot-admin-client/2.2.3/spring-boot-admin-client-2.2.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/de/codecentric/spring-boot-admin-client/2.2.3/spring-boot-admin-client-2.2.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: de.codecentric:spring-boot-admin-starter-client:2.2.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="de.codecentric" artifactId="spring-boot-admin-starter-client" version="2.2.3" baseVersion="2.2.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/de/codecentric/spring-boot-admin-starter-client/2.2.3/spring-boot-admin-starter-client-2.2.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/de/codecentric/spring-boot-admin-starter-client/2.2.3/spring-boot-admin-starter-client-2.2.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/de/codecentric/spring-boot-admin-starter-client/2.2.3/spring-boot-admin-starter-client-2.2.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: dom4j:dom4j:1.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="dom4j" artifactId="dom4j" version="1.3" baseVersion="1.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/dom4j/dom4j/1.3/dom4j-1.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/dom4j/dom4j/1.3/dom4j-1.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/dom4j/dom4j/1.3/dom4j-1.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.github.openfeign.form:feign-form-spring:3.8.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.github.openfeign.form" artifactId="feign-form-spring" version="3.8.0" baseVersion="3.8.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/form/feign-form-spring/3.8.0/feign-form-spring-3.8.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/form/feign-form-spring/3.8.0/feign-form-spring-3.8.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/form/feign-form-spring/3.8.0/feign-form-spring-3.8.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.github.openfeign.form:feign-form:3.8.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.github.openfeign.form" artifactId="feign-form" version="3.8.0" baseVersion="3.8.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/form/feign-form/3.8.0/feign-form-3.8.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/form/feign-form/3.8.0/feign-form-3.8.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/form/feign-form/3.8.0/feign-form-3.8.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.github.openfeign:feign-core:10.7.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.github.openfeign" artifactId="feign-core" version="10.7.4" baseVersion="10.7.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-core/10.7.4/feign-core-10.7.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-core/10.7.4/feign-core-10.7.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-core/10.7.4/feign-core-10.7.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.github.openfeign:feign-httpclient:10.7.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.github.openfeign" artifactId="feign-httpclient" version="10.7.4" baseVersion="10.7.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-httpclient/10.7.4/feign-httpclient-10.7.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-httpclient/10.7.4/feign-httpclient-10.7.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-httpclient/10.7.4/feign-httpclient-10.7.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.github.openfeign:feign-hystrix:10.7.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.github.openfeign" artifactId="feign-hystrix" version="10.7.4" baseVersion="10.7.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-hystrix/10.7.4/feign-hystrix-10.7.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-hystrix/10.7.4/feign-hystrix-10.7.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-hystrix/10.7.4/feign-hystrix-10.7.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.github.openfeign:feign-slf4j:10.7.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.github.openfeign" artifactId="feign-slf4j" version="10.7.4" baseVersion="10.7.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-slf4j/10.7.4/feign-slf4j-10.7.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-slf4j/10.7.4/feign-slf4j-10.7.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/openfeign/feign-slf4j/10.7.4/feign-slf4j-10.7.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.github.x-stream:mxparser:1.2.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.github.x-stream" artifactId="mxparser" version="1.2.2" baseVersion="1.2.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.lettuce:lettuce-core:5.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.lettuce" artifactId="lettuce-core" version="5.2.2.RELEASE" baseVersion="5.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.micrometer:micrometer-core:1.3.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.micrometer" artifactId="micrometer-core" version="1.3.5" baseVersion="1.3.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/micrometer/micrometer-core/1.3.5/micrometer-core-1.3.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/micrometer/micrometer-core/1.3.5/micrometer-core-1.3.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/micrometer/micrometer-core/1.3.5/micrometer-core-1.3.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.minio:minio:7.1.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.minio" artifactId="minio" version="7.1.0" baseVersion="7.1.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/minio/minio/7.1.0/minio-7.1.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/minio/minio/7.1.0/minio-7.1.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/minio/minio/7.1.0/minio-7.1.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-all:4.1.100.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-all" version="4.1.100.Final" baseVersion="4.1.100.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-all/4.1.100.Final/netty-all-4.1.100.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-all/4.1.100.Final/netty-all-4.1.100.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-all/4.1.100.Final/netty-all-4.1.100.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-all:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-all" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-all/4.1.45.Final/netty-all-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-all/4.1.45.Final/netty-all-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-all/4.1.45.Final/netty-all-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-buffer:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-buffer" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-buffer/4.1.45.Final/netty-buffer-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-buffer/4.1.45.Final/netty-buffer-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-buffer/4.1.45.Final/netty-buffer-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-dns:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-dns" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-dns/4.1.45.Final/netty-codec-dns-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-dns/4.1.45.Final/netty-codec-dns-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-dns/4.1.45.Final/netty-codec-dns-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-haproxy:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-haproxy" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-haproxy/4.1.45.Final/netty-codec-haproxy-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-haproxy/4.1.45.Final/netty-codec-haproxy-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-haproxy/4.1.45.Final/netty-codec-haproxy-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-http2:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-http2" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-http2/4.1.45.Final/netty-codec-http2-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-http2/4.1.45.Final/netty-codec-http2-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-http2/4.1.45.Final/netty-codec-http2-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-http:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-http" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-http/4.1.45.Final/netty-codec-http-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-http/4.1.45.Final/netty-codec-http-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-http/4.1.45.Final/netty-codec-http-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-memcache:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-memcache" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-memcache/4.1.45.Final/netty-codec-memcache-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-memcache/4.1.45.Final/netty-codec-memcache-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-memcache/4.1.45.Final/netty-codec-memcache-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-mqtt:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-mqtt" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-mqtt/4.1.45.Final/netty-codec-mqtt-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-mqtt/4.1.45.Final/netty-codec-mqtt-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-mqtt/4.1.45.Final/netty-codec-mqtt-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-redis:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-redis" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-redis/4.1.45.Final/netty-codec-redis-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-redis/4.1.45.Final/netty-codec-redis-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-redis/4.1.45.Final/netty-codec-redis-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-smtp:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-smtp" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-smtp/4.1.45.Final/netty-codec-smtp-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-smtp/4.1.45.Final/netty-codec-smtp-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-smtp/4.1.45.Final/netty-codec-smtp-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-socks:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-socks" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-socks/4.1.45.Final/netty-codec-socks-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-socks/4.1.45.Final/netty-codec-socks-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-socks/4.1.45.Final/netty-codec-socks-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-stomp:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-stomp" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-stomp/4.1.45.Final/netty-codec-stomp-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-stomp/4.1.45.Final/netty-codec-stomp-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-stomp/4.1.45.Final/netty-codec-stomp-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec-xml:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec-xml" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-xml/4.1.45.Final/netty-codec-xml-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-xml/4.1.45.Final/netty-codec-xml-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec-xml/4.1.45.Final/netty-codec-xml-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-codec:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-codec" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec/4.1.45.Final/netty-codec-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec/4.1.45.Final/netty-codec-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-codec/4.1.45.Final/netty-codec-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-common:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-common" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-common/4.1.45.Final/netty-common-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-common/4.1.45.Final/netty-common-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-common/4.1.45.Final/netty-common-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-handler-proxy:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-handler-proxy" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-handler-proxy/4.1.45.Final/netty-handler-proxy-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-handler-proxy/4.1.45.Final/netty-handler-proxy-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-handler-proxy/4.1.45.Final/netty-handler-proxy-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-handler-ssl-ocsp:4.1.100.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-handler-ssl-ocsp" version="4.1.100.Final" baseVersion="4.1.100.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-handler-ssl-ocsp/4.1.100.Final/netty-handler-ssl-ocsp-4.1.100.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-handler-ssl-ocsp/4.1.100.Final/netty-handler-ssl-ocsp-4.1.100.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-handler-ssl-ocsp/4.1.100.Final/netty-handler-ssl-ocsp-4.1.100.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-handler:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-handler" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-handler/4.1.45.Final/netty-handler-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-handler/4.1.45.Final/netty-handler-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-handler/4.1.45.Final/netty-handler-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-resolver-dns-classes-macos:4.1.100.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-resolver-dns-classes-macos" version="4.1.100.Final" baseVersion="4.1.100.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns-classes-macos/4.1.100.Final/netty-resolver-dns-classes-macos-4.1.100.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns-classes-macos/4.1.100.Final/netty-resolver-dns-classes-macos-4.1.100.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns-classes-macos/4.1.100.Final/netty-resolver-dns-classes-macos-4.1.100.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-resolver-dns-native-macos:osx-aarch_64:4.1.100.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-resolver-dns-native-macos" version="4.1.100.Final" baseVersion="4.1.100.Final" classifier="osx-aarch_64" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns-native-macos/4.1.100.Final/netty-resolver-dns-native-macos-4.1.100.Final-osx-aarch_64.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns-native-macos/4.1.100.Final/netty-resolver-dns-native-macos-4.1.100.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns-native-macos/4.1.100.Final/netty-resolver-dns-native-macos-4.1.100.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-resolver-dns-native-macos:osx-x86_64:4.1.100.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-resolver-dns-native-macos" version="4.1.100.Final" baseVersion="4.1.100.Final" classifier="osx-x86_64" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns-native-macos/4.1.100.Final/netty-resolver-dns-native-macos-4.1.100.Final-osx-x86_64.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns-native-macos/4.1.100.Final/netty-resolver-dns-native-macos-4.1.100.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns-native-macos/4.1.100.Final/netty-resolver-dns-native-macos-4.1.100.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-resolver-dns:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-resolver-dns" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns/4.1.45.Final/netty-resolver-dns-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns/4.1.45.Final/netty-resolver-dns-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver-dns/4.1.45.Final/netty-resolver-dns-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-resolver:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-resolver" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver/4.1.45.Final/netty-resolver-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver/4.1.45.Final/netty-resolver-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-resolver/4.1.45.Final/netty-resolver-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-classes-epoll:4.1.100.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-classes-epoll" version="4.1.100.Final" baseVersion="4.1.100.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-classes-epoll/4.1.100.Final/netty-transport-classes-epoll-4.1.100.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-classes-epoll/4.1.100.Final/netty-transport-classes-epoll-4.1.100.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-classes-epoll/4.1.100.Final/netty-transport-classes-epoll-4.1.100.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-classes-kqueue:4.1.100.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-classes-kqueue" version="4.1.100.Final" baseVersion="4.1.100.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-classes-kqueue/4.1.100.Final/netty-transport-classes-kqueue-4.1.100.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-classes-kqueue/4.1.100.Final/netty-transport-classes-kqueue-4.1.100.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-classes-kqueue/4.1.100.Final/netty-transport-classes-kqueue-4.1.100.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-native-epoll:linux-aarch_64:4.1.100.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-native-epoll" version="4.1.100.Final" baseVersion="4.1.100.Final" classifier="linux-aarch_64" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-epoll/4.1.100.Final/netty-transport-native-epoll-4.1.100.Final-linux-aarch_64.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-epoll/4.1.100.Final/netty-transport-native-epoll-4.1.100.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-epoll/4.1.100.Final/netty-transport-native-epoll-4.1.100.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-native-epoll:linux-x86_64:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-native-epoll" version="4.1.45.Final" baseVersion="4.1.45.Final" classifier="linux-x86_64" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-epoll/4.1.45.Final/netty-transport-native-epoll-4.1.45.Final-linux-x86_64.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-epoll/4.1.45.Final/netty-transport-native-epoll-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-epoll/4.1.45.Final/netty-transport-native-epoll-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-native-kqueue:osx-aarch_64:4.1.100.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-native-kqueue" version="4.1.100.Final" baseVersion="4.1.100.Final" classifier="osx-aarch_64" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-kqueue/4.1.100.Final/netty-transport-native-kqueue-4.1.100.Final-osx-aarch_64.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-kqueue/4.1.100.Final/netty-transport-native-kqueue-4.1.100.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-kqueue/4.1.100.Final/netty-transport-native-kqueue-4.1.100.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-native-kqueue:osx-x86_64:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-native-kqueue" version="4.1.45.Final" baseVersion="4.1.45.Final" classifier="osx-x86_64" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-kqueue/4.1.45.Final/netty-transport-native-kqueue-4.1.45.Final-osx-x86_64.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-kqueue/4.1.45.Final/netty-transport-native-kqueue-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-kqueue/4.1.45.Final/netty-transport-native-kqueue-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-native-unix-common:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-native-unix-common" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-unix-common/4.1.45.Final/netty-transport-native-unix-common-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-unix-common/4.1.45.Final/netty-transport-native-unix-common-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-native-unix-common/4.1.45.Final/netty-transport-native-unix-common-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-rxtx:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-rxtx" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-rxtx/4.1.45.Final/netty-transport-rxtx-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-rxtx/4.1.45.Final/netty-transport-rxtx-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-rxtx/4.1.45.Final/netty-transport-rxtx-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-sctp:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-sctp" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-sctp/4.1.45.Final/netty-transport-sctp-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-sctp/4.1.45.Final/netty-transport-sctp-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-sctp/4.1.45.Final/netty-transport-sctp-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport-udt:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport-udt" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-udt/4.1.45.Final/netty-transport-udt-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-udt/4.1.45.Final/netty-transport-udt-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport-udt/4.1.45.Final/netty-transport-udt-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.netty:netty-transport:4.1.45.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.netty" artifactId="netty-transport" version="4.1.45.Final" baseVersion="4.1.45.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport/4.1.45.Final/netty-transport-4.1.45.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport/4.1.45.Final/netty-transport-4.1.45.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/netty/netty-transport/4.1.45.Final/netty-transport-4.1.45.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.projectreactor:reactor-core:3.3.3.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.projectreactor" artifactId="reactor-core" version="3.3.3.RELEASE" baseVersion="3.3.3.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/projectreactor/reactor-core/3.3.3.RELEASE/reactor-core-3.3.3.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/projectreactor/reactor-core/3.3.3.RELEASE/reactor-core-3.3.3.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/projectreactor/reactor-core/3.3.3.RELEASE/reactor-core-3.3.3.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.prometheus:simpleclient:0.5.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.prometheus" artifactId="simpleclient" version="0.5.0" baseVersion="0.5.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/prometheus/simpleclient/0.5.0/simpleclient-0.5.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/prometheus/simpleclient/0.5.0/simpleclient-0.5.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/prometheus/simpleclient/0.5.0/simpleclient-0.5.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.reactivex:rxjava:1.3.8" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.reactivex" artifactId="rxjava" version="1.3.8" baseVersion="1.3.8" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxjava/1.3.8/rxjava-1.3.8.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxjava/1.3.8/rxjava-1.3.8-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxjava/1.3.8/rxjava-1.3.8-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.reactivex:rxnetty-contexts:0.4.9" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.reactivex" artifactId="rxnetty-contexts" version="0.4.9" baseVersion="0.4.9" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxnetty-contexts/0.4.9/rxnetty-contexts-0.4.9.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxnetty-contexts/0.4.9/rxnetty-contexts-0.4.9-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxnetty-contexts/0.4.9/rxnetty-contexts-0.4.9-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.reactivex:rxnetty-servo:0.4.9" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.reactivex" artifactId="rxnetty-servo" version="0.4.9" baseVersion="0.4.9" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxnetty-servo/0.4.9/rxnetty-servo-0.4.9.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxnetty-servo/0.4.9/rxnetty-servo-0.4.9-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxnetty-servo/0.4.9/rxnetty-servo-0.4.9-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.reactivex:rxnetty:0.4.9" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.reactivex" artifactId="rxnetty" version="0.4.9" baseVersion="0.4.9" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxnetty/0.4.9/rxnetty-0.4.9.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxnetty/0.4.9/rxnetty-0.4.9-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/reactivex/rxnetty/0.4.9/rxnetty-0.4.9-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.springfox:springfox-core:2.9.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.springfox" artifactId="springfox-core" version="2.9.2" baseVersion="2.9.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.springfox:springfox-schema:2.9.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.springfox" artifactId="springfox-schema" version="2.9.2" baseVersion="2.9.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.springfox:springfox-spi:2.9.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.springfox" artifactId="springfox-spi" version="2.9.2" baseVersion="2.9.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.springfox:springfox-spring-web:2.9.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.springfox" artifactId="springfox-spring-web" version="2.9.2" baseVersion="2.9.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.springfox:springfox-swagger-common:2.9.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.springfox" artifactId="springfox-swagger-common" version="2.9.2" baseVersion="2.9.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.springfox:springfox-swagger2:2.9.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.springfox" artifactId="springfox-swagger2" version="2.9.2" baseVersion="2.9.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.swagger:swagger-annotations:1.5.20" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.swagger" artifactId="swagger-annotations" version="1.5.20" baseVersion="1.5.20" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: io.swagger:swagger-models:1.5.20" type="java-imported" external-system-id="Maven">
+      <properties groupId="io.swagger" artifactId="swagger-models" version="1.5.20" baseVersion="1.5.20" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: jakarta.annotation:jakarta.annotation-api:1.3.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="jakarta.annotation" artifactId="jakarta.annotation-api" version="1.3.5" baseVersion="1.3.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: jakarta.validation:jakarta.validation-api:2.0.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="jakarta.validation" artifactId="jakarta.validation-api" version="2.0.2" baseVersion="2.0.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: jakarta.xml.bind:jakarta.xml.bind-api:3.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="jakarta.xml.bind" artifactId="jakarta.xml.bind-api" version="3.0.1" baseVersion="3.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jakarta/xml/bind/jakarta.xml.bind-api/3.0.1/jakarta.xml.bind-api-3.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/jakarta/xml/bind/jakarta.xml.bind-api/3.0.1/jakarta.xml.bind-api-3.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jakarta/xml/bind/jakarta.xml.bind-api/3.0.1/jakarta.xml.bind-api-3.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: javax.activation:activation:1.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="javax.activation" artifactId="activation" version="1.1" baseVersion="1.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/activation/activation/1.1/activation-1.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/activation/activation/1.1/activation-1.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/activation/activation/1.1/activation-1.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: javax.activation:javax.activation-api:1.2.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="javax.activation" artifactId="javax.activation-api" version="1.2.0" baseVersion="1.2.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/activation/javax.activation-api/1.2.0/javax.activation-api-1.2.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/activation/javax.activation-api/1.2.0/javax.activation-api-1.2.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/activation/javax.activation-api/1.2.0/javax.activation-api-1.2.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: javax.inject:javax.inject:1" type="java-imported" external-system-id="Maven">
+      <properties groupId="javax.inject" artifactId="javax.inject" version="1" baseVersion="1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/inject/javax.inject/1/javax.inject-1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/inject/javax.inject/1/javax.inject-1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/inject/javax.inject/1/javax.inject-1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: javax.mail:mail:1.4.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="javax.mail" artifactId="mail" version="1.4.7" baseVersion="1.4.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/mail/mail/1.4.7/mail-1.4.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/mail/mail/1.4.7/mail-1.4.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/mail/mail/1.4.7/mail-1.4.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: javax.ws.rs:jsr311-api:1.1.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="javax.ws.rs" artifactId="jsr311-api" version="1.1.1" baseVersion="1.1.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: javax.xml.bind:jaxb-api:2.3.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="javax.xml.bind" artifactId="jaxb-api" version="2.3.1" baseVersion="2.3.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: jaxun:mas:1.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="jaxun" artifactId="mas" version="1.0.0" baseVersion="1.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jaxun/mas/1.0.0/mas-1.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/jaxun/mas/1.0.0/mas-1.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jaxun/mas/1.0.0/mas-1.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: jaxun:mysql.driver:3.1.14" type="java-imported" external-system-id="Maven">
+      <properties groupId="jaxun" artifactId="mysql.driver" version="3.1.14" baseVersion="3.1.14" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jaxun/mysql.driver/3.1.14/mysql.driver-3.1.14.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/jaxun/mysql.driver/3.1.14/mysql.driver-3.1.14-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/jaxun/mysql.driver/3.1.14/mysql.driver-3.1.14-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: joda-time:joda-time:2.10.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="joda-time" artifactId="joda-time" version="2.10.5" baseVersion="2.10.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/joda-time/joda-time/2.10.5/joda-time-2.10.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/joda-time/joda-time/2.10.5/joda-time-2.10.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: log4j:log4j:1.2.14" type="java-imported" external-system-id="Maven">
+      <properties groupId="log4j" artifactId="log4j" version="1.2.14" baseVersion="1.2.14" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/log4j/log4j/1.2.14/log4j-1.2.14.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/log4j/log4j/1.2.14/log4j-1.2.14-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/log4j/log4j/1.2.14/log4j-1.2.14-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: mysql:mysql-connector-java:8.0.19" type="java-imported" external-system-id="Maven">
+      <properties groupId="mysql" artifactId="mysql-connector-java" version="8.0.19" baseVersion="8.0.19" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/mysql/mysql-connector-java/8.0.19/mysql-connector-java-8.0.19.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/mysql/mysql-connector-java/8.0.19/mysql-connector-java-8.0.19-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/mysql/mysql-connector-java/8.0.19/mysql-connector-java-8.0.19-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: mysql:mysql-connector-java:8.0.27" type="java-imported" external-system-id="Maven">
+      <properties groupId="mysql" artifactId="mysql-connector-java" version="8.0.27" baseVersion="8.0.27" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/mysql/mysql-connector-java/8.0.27/mysql-connector-java-8.0.27.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/mysql/mysql-connector-java/8.0.27/mysql-connector-java-8.0.27-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/mysql/mysql-connector-java/8.0.27/mysql-connector-java-8.0.27-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: net.bytebuddy:byte-buddy:1.10.8" type="java-imported" external-system-id="Maven">
+      <properties groupId="net.bytebuddy" artifactId="byte-buddy" version="1.10.8" baseVersion="1.10.8" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: net.coobird:thumbnailator:0.4.8" type="java-imported" external-system-id="Maven">
+      <properties groupId="net.coobird" artifactId="thumbnailator" version="0.4.8" baseVersion="0.4.8" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: net.jcip:jcip-annotations:1.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="net.jcip" artifactId="jcip-annotations" version="1.0" baseVersion="1.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/jcip/jcip-annotations/1.0/jcip-annotations-1.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/jcip/jcip-annotations/1.0/jcip-annotations-1.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/jcip/jcip-annotations/1.0/jcip-annotations-1.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: net.sf.ezmorph:ezmorph:1.0.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="net.sf.ezmorph" artifactId="ezmorph" version="1.0.6" baseVersion="1.0.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/sf/ezmorph/ezmorph/1.0.6/ezmorph-1.0.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/sf/ezmorph/ezmorph/1.0.6/ezmorph-1.0.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/sf/ezmorph/ezmorph/1.0.6/ezmorph-1.0.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: net.sf.json-lib:json-lib:jdk15:2.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="net.sf.json-lib" artifactId="json-lib" version="2.4" baseVersion="2.4" classifier="jdk15" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/sf/json-lib/json-lib/2.4/json-lib-2.4-jdk15.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/sf/json-lib/json-lib/2.4/json-lib-2.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/sf/json-lib/json-lib/2.4/json-lib-2.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: net.sourceforge.jexcelapi:jxl:2.6.10" type="java-imported" external-system-id="Maven">
+      <properties groupId="net.sourceforge.jexcelapi" artifactId="jxl" version="2.6.10" baseVersion="2.6.10" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/sourceforge/jexcelapi/jxl/2.6.10/jxl-2.6.10.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/sourceforge/jexcelapi/jxl/2.6.10/jxl-2.6.10-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/net/sourceforge/jexcelapi/jxl/2.6.10/jxl-2.6.10-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.abego.treelayout:org.abego.treelayout.core:1.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.abego.treelayout" artifactId="org.abego.treelayout.core" version="1.0.1" baseVersion="1.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/abego/treelayout/org.abego.treelayout.core/1.0.1/org.abego.treelayout.core-1.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/abego/treelayout/org.abego.treelayout.core/1.0.1/org.abego.treelayout.core-1.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/abego/treelayout/org.abego.treelayout.core/1.0.1/org.abego.treelayout.core-1.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.antlr:antlr4-annotations:4.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.antlr" artifactId="antlr4-annotations" version="4.2" baseVersion="4.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/antlr/antlr4-annotations/4.2/antlr4-annotations-4.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/antlr/antlr4-annotations/4.2/antlr4-annotations-4.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/antlr/antlr4-annotations/4.2/antlr4-annotations-4.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.antlr:antlr4-runtime:4.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.antlr" artifactId="antlr4-runtime" version="4.2" baseVersion="4.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/antlr/antlr4-runtime/4.2/antlr4-runtime-4.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/antlr/antlr4-runtime/4.2/antlr4-runtime-4.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/antlr/antlr4-runtime/4.2/antlr4-runtime-4.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.commons:commons-collections4:4.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.commons" artifactId="commons-collections4" version="4.4" baseVersion="4.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.commons:commons-compress:1.19" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.commons" artifactId="commons-compress" version="1.19" baseVersion="1.19" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-compress/1.19/commons-compress-1.19.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-compress/1.19/commons-compress-1.19-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-compress/1.19/commons-compress-1.19-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.commons:commons-email:1.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.commons" artifactId="commons-email" version="1.5" baseVersion="1.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-email/1.5/commons-email-1.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-email/1.5/commons-email-1.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-email/1.5/commons-email-1.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.commons:commons-jexl3:3.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.commons" artifactId="commons-jexl3" version="3.1" baseVersion="3.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-jexl3/3.1/commons-jexl3-3.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-jexl3/3.1/commons-jexl3-3.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-jexl3/3.1/commons-jexl3-3.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.commons:commons-lang3:3.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.commons" artifactId="commons-lang3" version="3.1" baseVersion="3.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-lang3/3.1/commons-lang3-3.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-lang3/3.1/commons-lang3-3.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-lang3/3.1/commons-lang3-3.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.commons:commons-lang3:3.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.commons" artifactId="commons-lang3" version="3.7" baseVersion="3.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-lang3/3.7/commons-lang3-3.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-lang3/3.7/commons-lang3-3.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-lang3/3.7/commons-lang3-3.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.commons:commons-math3:3.6.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.commons" artifactId="commons-math3" version="3.6.1" baseVersion="3.6.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.commons:commons-pool2:2.4.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.commons" artifactId="commons-pool2" version="2.4.2" baseVersion="2.4.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-pool2/2.4.2/commons-pool2-2.4.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-pool2/2.4.2/commons-pool2-2.4.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/commons/commons-pool2/2.4.2/commons-pool2-2.4.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.httpcomponents:httpasyncclient:4.1.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.httpcomponents" artifactId="httpasyncclient" version="4.1.4" baseVersion="4.1.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpasyncclient/4.1.4/httpasyncclient-4.1.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpasyncclient/4.1.4/httpasyncclient-4.1.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpasyncclient/4.1.4/httpasyncclient-4.1.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.httpcomponents:httpclient:4.5.11" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.httpcomponents" artifactId="httpclient" version="4.5.11" baseVersion="4.5.11" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpclient/4.5.11/httpclient-4.5.11.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpclient/4.5.11/httpclient-4.5.11-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpclient/4.5.11/httpclient-4.5.11-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.httpcomponents:httpcore-nio:4.4.13" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.httpcomponents" artifactId="httpcore-nio" version="4.4.13" baseVersion="4.4.13" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpcore-nio/4.4.13/httpcore-nio-4.4.13.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpcore-nio/4.4.13/httpcore-nio-4.4.13-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpcore-nio/4.4.13/httpcore-nio-4.4.13-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.httpcomponents:httpcore:4.4.13" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.httpcomponents" artifactId="httpcore" version="4.4.13" baseVersion="4.4.13" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.httpcomponents:httpmime:4.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.httpcomponents" artifactId="httpmime" version="4.0" baseVersion="4.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpmime/4.0/httpmime-4.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpmime/4.0/httpmime-4.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/httpcomponents/httpmime/4.0/httpmime-4.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.james:apache-mime4j:0.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.james" artifactId="apache-mime4j" version="0.6" baseVersion="0.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/james/apache-mime4j/0.6/apache-mime4j-0.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/james/apache-mime4j/0.6/apache-mime4j-0.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/james/apache-mime4j/0.6/apache-mime4j-0.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.logging.log4j:log4j-api:2.12.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.logging.log4j" artifactId="log4j-api" version="2.12.1" baseVersion="2.12.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.logging.log4j:log4j-api:2.19.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.logging.log4j" artifactId="log4j-api" version="2.19.0" baseVersion="2.19.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-api/2.19.0/log4j-api-2.19.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-api/2.19.0/log4j-api-2.19.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-api/2.19.0/log4j-api-2.19.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.logging.log4j:log4j-core:2.12.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.logging.log4j" artifactId="log4j-core" version="2.12.1" baseVersion="2.12.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-core/2.12.1/log4j-core-2.12.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-core/2.12.1/log4j-core-2.12.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-core/2.12.1/log4j-core-2.12.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.logging.log4j:log4j-core:2.19.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.logging.log4j" artifactId="log4j-core" version="2.19.0" baseVersion="2.19.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-core/2.19.0/log4j-core-2.19.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-core/2.19.0/log4j-core-2.19.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-core/2.19.0/log4j-core-2.19.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.logging.log4j:log4j-jul:2.12.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.logging.log4j" artifactId="log4j-jul" version="2.12.1" baseVersion="2.12.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-jul/2.12.1/log4j-jul-2.12.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-jul/2.12.1/log4j-jul-2.12.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-jul/2.12.1/log4j-jul-2.12.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.logging.log4j:log4j-slf4j-impl:2.12.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.logging.log4j" artifactId="log4j-slf4j-impl" version="2.12.1" baseVersion="2.12.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-slf4j-impl/2.12.1/log4j-slf4j-impl-2.12.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-slf4j-impl/2.12.1/log4j-slf4j-impl-2.12.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/logging/log4j/log4j-slf4j-impl/2.12.1/log4j-slf4j-impl-2.12.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.poi:poi-ooxml-schemas:4.1.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.poi" artifactId="poi-ooxml-schemas" version="4.1.2" baseVersion="4.1.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.poi:poi-ooxml:4.1.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.poi" artifactId="poi-ooxml" version="4.1.2" baseVersion="4.1.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.poi:poi:4.1.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.poi" artifactId="poi" version="4.1.2" baseVersion="4.1.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/poi/poi/4.1.2/poi-4.1.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/poi/poi/4.1.2/poi-4.1.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/poi/poi/4.1.2/poi-4.1.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-cache:1.13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-cache" version="1.13.0" baseVersion="1.13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-cache/1.13.0/shiro-cache-1.13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-cache/1.13.0/shiro-cache-1.13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-cache/1.13.0/shiro-cache-1.13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-config-core:1.13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-config-core" version="1.13.0" baseVersion="1.13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-config-core/1.13.0/shiro-config-core-1.13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-config-core/1.13.0/shiro-config-core-1.13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-config-core/1.13.0/shiro-config-core-1.13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-config-ogdl:1.13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-config-ogdl" version="1.13.0" baseVersion="1.13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-config-ogdl/1.13.0/shiro-config-ogdl-1.13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-config-ogdl/1.13.0/shiro-config-ogdl-1.13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-config-ogdl/1.13.0/shiro-config-ogdl-1.13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-core:1.13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-core" version="1.13.0" baseVersion="1.13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-core/1.13.0/shiro-core-1.13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-core/1.13.0/shiro-core-1.13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-core/1.13.0/shiro-core-1.13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-core:1.3.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-core" version="1.3.2" baseVersion="1.3.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-core/1.3.2/shiro-core-1.3.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-core/1.3.2/shiro-core-1.3.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-core/1.3.2/shiro-core-1.3.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-crypto-cipher:1.13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-crypto-cipher" version="1.13.0" baseVersion="1.13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-crypto-cipher/1.13.0/shiro-crypto-cipher-1.13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-crypto-cipher/1.13.0/shiro-crypto-cipher-1.13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-crypto-cipher/1.13.0/shiro-crypto-cipher-1.13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-crypto-core:1.13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-crypto-core" version="1.13.0" baseVersion="1.13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-crypto-core/1.13.0/shiro-crypto-core-1.13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-crypto-core/1.13.0/shiro-crypto-core-1.13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-crypto-core/1.13.0/shiro-crypto-core-1.13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-crypto-hash:1.13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-crypto-hash" version="1.13.0" baseVersion="1.13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-crypto-hash/1.13.0/shiro-crypto-hash-1.13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-crypto-hash/1.13.0/shiro-crypto-hash-1.13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-crypto-hash/1.13.0/shiro-crypto-hash-1.13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-event:1.13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-event" version="1.13.0" baseVersion="1.13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-event/1.13.0/shiro-event-1.13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-event/1.13.0/shiro-event-1.13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-event/1.13.0/shiro-event-1.13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-lang:1.13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-lang" version="1.13.0" baseVersion="1.13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-lang/1.13.0/shiro-lang-1.13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-lang/1.13.0/shiro-lang-1.13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-lang/1.13.0/shiro-lang-1.13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-spring:1.4.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-spring" version="1.4.0" baseVersion="1.4.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-spring/1.4.0/shiro-spring-1.4.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-spring/1.4.0/shiro-spring-1.4.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-spring/1.4.0/shiro-spring-1.4.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.shiro:shiro-web:1.4.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.shiro" artifactId="shiro-web" version="1.4.0" baseVersion="1.4.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-web/1.4.0/shiro-web-1.4.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-web/1.4.0/shiro-web-1.4.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/shiro/shiro-web/1.4.0/shiro-web-1.4.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.tomcat.embed:tomcat-embed-core:9.0.31" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.tomcat.embed" artifactId="tomcat-embed-core" version="9.0.31" baseVersion="9.0.31" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/tomcat/embed/tomcat-embed-core/9.0.31/tomcat-embed-core-9.0.31.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/tomcat/embed/tomcat-embed-core/9.0.31/tomcat-embed-core-9.0.31-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/tomcat/embed/tomcat-embed-core/9.0.31/tomcat-embed-core-9.0.31-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.31" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.tomcat.embed" artifactId="tomcat-embed-el" version="9.0.31" baseVersion="9.0.31" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/tomcat/embed/tomcat-embed-el/9.0.31/tomcat-embed-el-9.0.31.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/tomcat/embed/tomcat-embed-el/9.0.31/tomcat-embed-el-9.0.31-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/tomcat/embed/tomcat-embed-el/9.0.31/tomcat-embed-el-9.0.31-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.31" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.tomcat.embed" artifactId="tomcat-embed-websocket" version="9.0.31" baseVersion="9.0.31" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.31/tomcat-embed-websocket-9.0.31.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.31/tomcat-embed-websocket-9.0.31-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.31/tomcat-embed-websocket-9.0.31-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.velocity:velocity-engine-core:2.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.velocity" artifactId="velocity-engine-core" version="2.3" baseVersion="2.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.apache.xmlbeans:xmlbeans:3.1.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.apache.xmlbeans" artifactId="xmlbeans" version="3.1.0" baseVersion="3.1.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.aspectj:aspectjweaver:1.9.5" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.aspectj" artifactId="aspectjweaver" version="1.9.5" baseVersion="1.9.5" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.bouncycastle:bcpkix-jdk15on:1.64" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.bouncycastle" artifactId="bcpkix-jdk15on" version="1.64" baseVersion="1.64" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcpkix-jdk15on/1.64/bcpkix-jdk15on-1.64.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcpkix-jdk15on/1.64/bcpkix-jdk15on-1.64-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcpkix-jdk15on/1.64/bcpkix-jdk15on-1.64-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.bouncycastle:bcpkix-jdk15on:1.70" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.bouncycastle" artifactId="bcpkix-jdk15on" version="1.70" baseVersion="1.70" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcpkix-jdk15on/1.70/bcpkix-jdk15on-1.70.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcpkix-jdk15on/1.70/bcpkix-jdk15on-1.70-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcpkix-jdk15on/1.70/bcpkix-jdk15on-1.70-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.bouncycastle:bcprov-jdk15on:1.64" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.bouncycastle" artifactId="bcprov-jdk15on" version="1.64" baseVersion="1.64" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.bouncycastle:bcprov-jdk15on:1.70" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.bouncycastle" artifactId="bcprov-jdk15on" version="1.70" baseVersion="1.70" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.bouncycastle:bcprov-jdk18on:1.76" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.bouncycastle" artifactId="bcprov-jdk18on" version="1.76" baseVersion="1.76" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.bouncycastle:bcutil-jdk15on:1.70" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.bouncycastle" artifactId="bcutil-jdk15on" version="1.70" baseVersion="1.70" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcutil-jdk15on/1.70/bcutil-jdk15on-1.70.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcutil-jdk15on/1.70/bcutil-jdk15on-1.70-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/bouncycastle/bcutil-jdk15on/1.70/bcutil-jdk15on-1.70-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.codehaus.jettison:jettison:1.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.codehaus.jettison" artifactId="jettison" version="1.1" baseVersion="1.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/codehaus/jettison/jettison/1.1/jettison-1.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/codehaus/jettison/jettison/1.1/jettison-1.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.codehaus.mojo:animal-sniffer-annotations:1.14" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.codehaus.mojo" artifactId="animal-sniffer-annotations" version="1.14" baseVersion="1.14" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/codehaus/mojo/animal-sniffer-annotations/1.14/animal-sniffer-annotations-1.14.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/codehaus/mojo/animal-sniffer-annotations/1.14/animal-sniffer-annotations-1.14-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/codehaus/mojo/animal-sniffer-annotations/1.14/animal-sniffer-annotations-1.14-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.csource:fastdfs-client-java:1.27" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.csource" artifactId="fastdfs-client-java" version="1.27" baseVersion="1.27" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/csource/fastdfs-client-java/1.27/fastdfs-client-java-1.27.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/csource/fastdfs-client-java/1.27/fastdfs-client-java-1.27-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/csource/fastdfs-client-java/1.27/fastdfs-client-java-1.27-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.dom4j:dom4j:2.0.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.dom4j" artifactId="dom4j" version="2.0.3" baseVersion="2.0.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/dom4j/dom4j/2.0.3/dom4j-2.0.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/dom4j/dom4j/2.0.3/dom4j-2.0.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/dom4j/dom4j/2.0.3/dom4j-2.0.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.eclipse.paho" artifactId="org.eclipse.paho.client.mqttv3" version="1.2.0" baseVersion="1.2.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/eclipse/paho/org.eclipse.paho.client.mqttv3/1.2.0/org.eclipse.paho.client.mqttv3-1.2.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/eclipse/paho/org.eclipse.paho.client.mqttv3/1.2.0/org.eclipse.paho.client.mqttv3-1.2.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/eclipse/paho/org.eclipse.paho.client.mqttv3/1.2.0/org.eclipse.paho.client.mqttv3-1.2.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-batch-service-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-batch-service-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-batch-service-api/6.7.2/flowable-batch-service-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-batch-service-api/6.7.2/flowable-batch-service-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-batch-service-api/6.7.2/flowable-batch-service-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-batch-service:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-batch-service" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-batch-service/6.7.2/flowable-batch-service-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-batch-service/6.7.2/flowable-batch-service-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-batch-service/6.7.2/flowable-batch-service-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-bpmn-converter:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-bpmn-converter" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-bpmn-converter/6.7.2/flowable-bpmn-converter-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-bpmn-converter/6.7.2/flowable-bpmn-converter-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-bpmn-converter/6.7.2/flowable-bpmn-converter-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-bpmn-model:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-bpmn-model" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-bpmn-model/6.7.2/flowable-bpmn-model-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-bpmn-model/6.7.2/flowable-bpmn-model-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-bpmn-model/6.7.2/flowable-bpmn-model-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-cmmn-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-cmmn-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-cmmn-api/6.7.2/flowable-cmmn-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-cmmn-api/6.7.2/flowable-cmmn-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-cmmn-api/6.7.2/flowable-cmmn-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-cmmn-model:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-cmmn-model" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-cmmn-model/6.7.2/flowable-cmmn-model-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-cmmn-model/6.7.2/flowable-cmmn-model-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-cmmn-model/6.7.2/flowable-cmmn-model-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-content-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-content-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-content-api/6.7.2/flowable-content-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-content-api/6.7.2/flowable-content-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-content-api/6.7.2/flowable-content-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-dmn-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-dmn-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-dmn-api/6.7.2/flowable-dmn-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-dmn-api/6.7.2/flowable-dmn-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-dmn-api/6.7.2/flowable-dmn-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-dmn-model:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-dmn-model" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-dmn-model/6.7.2/flowable-dmn-model-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-dmn-model/6.7.2/flowable-dmn-model-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-dmn-model/6.7.2/flowable-dmn-model-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-engine-common-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-engine-common-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-engine-common-api/6.7.2/flowable-engine-common-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-engine-common-api/6.7.2/flowable-engine-common-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-engine-common-api/6.7.2/flowable-engine-common-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-engine-common:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-engine-common" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-engine-common/6.7.2/flowable-engine-common-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-engine-common/6.7.2/flowable-engine-common-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-engine-common/6.7.2/flowable-engine-common-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-engine:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-engine" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-engine/6.7.2/flowable-engine-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-engine/6.7.2/flowable-engine-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-engine/6.7.2/flowable-engine-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-entitylink-service-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-entitylink-service-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-entitylink-service-api/6.7.2/flowable-entitylink-service-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-entitylink-service-api/6.7.2/flowable-entitylink-service-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-entitylink-service-api/6.7.2/flowable-entitylink-service-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-entitylink-service:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-entitylink-service" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-entitylink-service/6.7.2/flowable-entitylink-service-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-entitylink-service/6.7.2/flowable-entitylink-service-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-entitylink-service/6.7.2/flowable-entitylink-service-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-event-registry-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-event-registry-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-api/6.7.2/flowable-event-registry-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-api/6.7.2/flowable-event-registry-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-api/6.7.2/flowable-event-registry-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-event-registry-configurator:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-event-registry-configurator" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-configurator/6.7.2/flowable-event-registry-configurator-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-configurator/6.7.2/flowable-event-registry-configurator-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-configurator/6.7.2/flowable-event-registry-configurator-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-event-registry-json-converter:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-event-registry-json-converter" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-json-converter/6.7.2/flowable-event-registry-json-converter-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-json-converter/6.7.2/flowable-event-registry-json-converter-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-json-converter/6.7.2/flowable-event-registry-json-converter-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-event-registry-model:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-event-registry-model" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-model/6.7.2/flowable-event-registry-model-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-model/6.7.2/flowable-event-registry-model-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-model/6.7.2/flowable-event-registry-model-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-event-registry-spring-configurator:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-event-registry-spring-configurator" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-spring-configurator/6.7.2/flowable-event-registry-spring-configurator-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-spring-configurator/6.7.2/flowable-event-registry-spring-configurator-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-spring-configurator/6.7.2/flowable-event-registry-spring-configurator-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-event-registry-spring:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-event-registry-spring" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-spring/6.7.2/flowable-event-registry-spring-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-spring/6.7.2/flowable-event-registry-spring-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry-spring/6.7.2/flowable-event-registry-spring-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-event-registry:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-event-registry" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry/6.7.2/flowable-event-registry-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry/6.7.2/flowable-event-registry-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-event-registry/6.7.2/flowable-event-registry-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-eventsubscription-service-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-eventsubscription-service-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-eventsubscription-service-api/6.7.2/flowable-eventsubscription-service-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-eventsubscription-service-api/6.7.2/flowable-eventsubscription-service-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-eventsubscription-service-api/6.7.2/flowable-eventsubscription-service-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-eventsubscription-service:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-eventsubscription-service" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-eventsubscription-service/6.7.2/flowable-eventsubscription-service-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-eventsubscription-service/6.7.2/flowable-eventsubscription-service-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-eventsubscription-service/6.7.2/flowable-eventsubscription-service-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-form-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-form-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-form-api/6.7.2/flowable-form-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-form-api/6.7.2/flowable-form-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-form-api/6.7.2/flowable-form-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-form-model:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-form-model" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-form-model/6.7.2/flowable-form-model-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-form-model/6.7.2/flowable-form-model-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-form-model/6.7.2/flowable-form-model-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-http-common:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-http-common" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-http-common/6.7.2/flowable-http-common-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-http-common/6.7.2/flowable-http-common-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-http-common/6.7.2/flowable-http-common-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-identitylink-service-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-identitylink-service-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-identitylink-service-api/6.7.2/flowable-identitylink-service-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-identitylink-service-api/6.7.2/flowable-identitylink-service-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-identitylink-service-api/6.7.2/flowable-identitylink-service-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-identitylink-service:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-identitylink-service" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-identitylink-service/6.7.2/flowable-identitylink-service-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-identitylink-service/6.7.2/flowable-identitylink-service-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-identitylink-service/6.7.2/flowable-identitylink-service-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-idm-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-idm-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-idm-api/6.7.2/flowable-idm-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-idm-api/6.7.2/flowable-idm-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-idm-api/6.7.2/flowable-idm-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-idm-engine-configurator:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-idm-engine-configurator" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-idm-engine-configurator/6.7.2/flowable-idm-engine-configurator-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-idm-engine-configurator/6.7.2/flowable-idm-engine-configurator-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-idm-engine-configurator/6.7.2/flowable-idm-engine-configurator-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-idm-engine:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-idm-engine" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-idm-engine/6.7.2/flowable-idm-engine-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-idm-engine/6.7.2/flowable-idm-engine-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-idm-engine/6.7.2/flowable-idm-engine-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-image-generator:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-image-generator" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-image-generator/6.7.2/flowable-image-generator-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-image-generator/6.7.2/flowable-image-generator-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-image-generator/6.7.2/flowable-image-generator-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-job-service-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-job-service-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-job-service-api/6.7.2/flowable-job-service-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-job-service-api/6.7.2/flowable-job-service-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-job-service-api/6.7.2/flowable-job-service-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-job-service:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-job-service" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-job-service/6.7.2/flowable-job-service-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-job-service/6.7.2/flowable-job-service-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-job-service/6.7.2/flowable-job-service-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-job-spring-service:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-job-spring-service" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-job-spring-service/6.7.2/flowable-job-spring-service-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-job-spring-service/6.7.2/flowable-job-spring-service-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-job-spring-service/6.7.2/flowable-job-spring-service-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-process-validation:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-process-validation" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-process-validation/6.7.2/flowable-process-validation-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-process-validation/6.7.2/flowable-process-validation-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-process-validation/6.7.2/flowable-process-validation-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-spring-boot-autoconfigure:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-spring-boot-autoconfigure" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring-boot-autoconfigure/6.7.2/flowable-spring-boot-autoconfigure-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring-boot-autoconfigure/6.7.2/flowable-spring-boot-autoconfigure-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring-boot-autoconfigure/6.7.2/flowable-spring-boot-autoconfigure-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-spring-common:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-spring-common" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring-common/6.7.2/flowable-spring-common-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring-common/6.7.2/flowable-spring-common-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring-common/6.7.2/flowable-spring-common-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-spring-security:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-spring-security" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring-security/6.7.2/flowable-spring-security-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring-security/6.7.2/flowable-spring-security-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring-security/6.7.2/flowable-spring-security-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-spring:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-spring" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring/6.7.2/flowable-spring-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring/6.7.2/flowable-spring-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-spring/6.7.2/flowable-spring-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-task-service-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-task-service-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-task-service-api/6.7.2/flowable-task-service-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-task-service-api/6.7.2/flowable-task-service-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-task-service-api/6.7.2/flowable-task-service-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-task-service:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-task-service" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-task-service/6.7.2/flowable-task-service-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-task-service/6.7.2/flowable-task-service-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-task-service/6.7.2/flowable-task-service-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-variable-service-api:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-variable-service-api" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-variable-service-api/6.7.2/flowable-variable-service-api-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-variable-service-api/6.7.2/flowable-variable-service-api-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-variable-service-api/6.7.2/flowable-variable-service-api-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.flowable:flowable-variable-service:6.7.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.flowable" artifactId="flowable-variable-service" version="6.7.2" baseVersion="6.7.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-variable-service/6.7.2/flowable-variable-service-6.7.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-variable-service/6.7.2/flowable-variable-service-6.7.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/flowable/flowable-variable-service/6.7.2/flowable-variable-service-6.7.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.freemarker:freemarker:2.3.29" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.freemarker" artifactId="freemarker" version="2.3.29" baseVersion="2.3.29" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/freemarker/freemarker/2.3.29/freemarker-2.3.29.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/freemarker/freemarker/2.3.29/freemarker-2.3.29-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/freemarker/freemarker/2.3.29/freemarker-2.3.29-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.glassfish.jaxb:jaxb-core:3.0.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.glassfish.jaxb" artifactId="jaxb-core" version="3.0.2" baseVersion="3.0.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/glassfish/jaxb/jaxb-core/3.0.2/jaxb-core-3.0.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/glassfish/jaxb/jaxb-core/3.0.2/jaxb-core-3.0.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/glassfish/jaxb/jaxb-core/3.0.2/jaxb-core-3.0.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.glassfish.jaxb:jaxb-runtime:3.0.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.glassfish.jaxb" artifactId="jaxb-runtime" version="3.0.2" baseVersion="3.0.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/glassfish/jaxb/jaxb-runtime/3.0.2/jaxb-runtime-3.0.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/glassfish/jaxb/jaxb-runtime/3.0.2/jaxb-runtime-3.0.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/glassfish/jaxb/jaxb-runtime/3.0.2/jaxb-runtime-3.0.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.glassfish.jaxb:txw2:2.3.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.glassfish.jaxb" artifactId="txw2" version="2.3.2" baseVersion="2.3.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/glassfish/jaxb/txw2/2.3.2/txw2-2.3.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/glassfish/jaxb/txw2/2.3.2/txw2-2.3.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/glassfish/jaxb/txw2/2.3.2/txw2-2.3.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.hamcrest:hamcrest-core:1.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.hamcrest" artifactId="hamcrest-core" version="1.3" baseVersion="1.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.hdrhistogram:HdrHistogram:2.1.11" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.hdrhistogram" artifactId="HdrHistogram" version="2.1.11" baseVersion="2.1.11" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.hdrhistogram:HdrHistogram:2.1.9" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.hdrhistogram" artifactId="HdrHistogram" version="2.1.9" baseVersion="2.1.9" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hdrhistogram/HdrHistogram/2.1.9/HdrHistogram-2.1.9.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hdrhistogram/HdrHistogram/2.1.9/HdrHistogram-2.1.9-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hdrhistogram/HdrHistogram/2.1.9/HdrHistogram-2.1.9-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.hibernate.validator:hibernate-validator:6.0.18.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.hibernate.validator" artifactId="hibernate-validator" version="6.0.18.Final" baseVersion="6.0.18.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.ini4j:ini4j:0.5.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.ini4j" artifactId="ini4j" version="0.5.4" baseVersion="0.5.4" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/ini4j/ini4j/0.5.4/ini4j-0.5.4.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/ini4j/ini4j/0.5.4/ini4j-0.5.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/ini4j/ini4j/0.5.4/ini4j-0.5.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jacoco:org.jacoco.agent:runtime:0.8.4" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jacoco" artifactId="org.jacoco.agent" version="0.8.4" baseVersion="0.8.4" classifier="runtime" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jacoco/org.jacoco.agent/0.8.4/org.jacoco.agent-0.8.4-runtime.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jacoco/org.jacoco.agent/0.8.4/org.jacoco.agent-0.8.4-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jacoco/org.jacoco.agent/0.8.4/org.jacoco.agent-0.8.4-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jboss.logging:jboss-logging:3.4.1.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jboss.logging" artifactId="jboss-logging" version="3.4.1.Final" baseVersion="3.4.1.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jdom:jdom2:2.0.6" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jdom" artifactId="jdom2" version="2.0.6" baseVersion="2.0.6" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jdom/jdom2/2.0.6/jdom2-2.0.6.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jdom/jdom2/2.0.6/jdom2-2.0.6-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jdom/jdom2/2.0.6/jdom2-2.0.6-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jetbrains.kotlin:kotlin-stdlib-common:1.3.61" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jetbrains.kotlin" artifactId="kotlin-stdlib-common" version="1.3.61" baseVersion="1.3.61" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.61/kotlin-stdlib-common-1.3.61.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.61/kotlin-stdlib-common-1.3.61-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.61/kotlin-stdlib-common-1.3.61-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jetbrains.kotlin:kotlin-stdlib:1.3.61" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jetbrains.kotlin" artifactId="kotlin-stdlib" version="1.3.61" baseVersion="1.3.61" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/kotlin/kotlin-stdlib/1.3.61/kotlin-stdlib-1.3.61.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/kotlin/kotlin-stdlib/1.3.61/kotlin-stdlib-1.3.61-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/kotlin/kotlin-stdlib/1.3.61/kotlin-stdlib-1.3.61-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jetbrains:annotations:13.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jetbrains" artifactId="annotations" version="13.0" baseVersion="13.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/annotations/13.0/annotations-13.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/annotations/13.0/annotations-13.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/annotations/13.0/annotations-13.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jetbrains:annotations:26.0.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jetbrains" artifactId="annotations" version="26.0.2" baseVersion="26.0.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/annotations/26.0.2/annotations-26.0.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/annotations/26.0.2/annotations-26.0.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jetbrains/annotations/26.0.2/annotations-26.0.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jsoup:jsoup:1.8.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jsoup" artifactId="jsoup" version="1.8.1" baseVersion="1.8.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jsoup/jsoup/1.8.1/jsoup-1.8.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jsoup/jsoup/1.8.1/jsoup-1.8.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jsoup/jsoup/1.8.1/jsoup-1.8.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jxls:jxls-jexcel:1.0.9" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jxls" artifactId="jxls-jexcel" version="1.0.9" baseVersion="1.0.9" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jxls/jxls-jexcel/1.0.9/jxls-jexcel-1.0.9.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jxls/jxls-jexcel/1.0.9/jxls-jexcel-1.0.9-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jxls/jxls-jexcel/1.0.9/jxls-jexcel-1.0.9-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jxls:jxls-poi:2.10.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jxls" artifactId="jxls-poi" version="2.10.0" baseVersion="2.10.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jxls/jxls-poi/2.10.0/jxls-poi-2.10.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jxls/jxls-poi/2.10.0/jxls-poi-2.10.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jxls/jxls-poi/2.10.0/jxls-poi-2.10.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.jxls:jxls:2.10.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.jxls" artifactId="jxls" version="2.10.0" baseVersion="2.10.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jxls/jxls/2.10.0/jxls-2.10.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jxls/jxls/2.10.0/jxls-2.10.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/jxls/jxls/2.10.0/jxls-2.10.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.latencyutils:LatencyUtils:2.0.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.latencyutils" artifactId="LatencyUtils" version="2.0.3" baseVersion="2.0.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.liquibase:liquibase-core:3.8.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.liquibase" artifactId="liquibase-core" version="3.8.7" baseVersion="3.8.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/liquibase/liquibase-core/3.8.7/liquibase-core-3.8.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/liquibase/liquibase-core/3.8.7/liquibase-core-3.8.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/liquibase/liquibase-core/3.8.7/liquibase-core-3.8.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.mapstruct:mapstruct:1.2.0.Final" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.mapstruct" artifactId="mapstruct" version="1.2.0.Final" baseVersion="1.2.0.Final" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.mybatis:mybatis-spring:1.3.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.mybatis" artifactId="mybatis-spring" version="1.3.2" baseVersion="1.3.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/mybatis/mybatis-spring/1.3.2/mybatis-spring-1.3.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/mybatis/mybatis-spring/1.3.2/mybatis-spring-1.3.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/mybatis/mybatis-spring/1.3.2/mybatis-spring-1.3.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.mybatis:mybatis:3.5.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.mybatis" artifactId="mybatis" version="3.5.7" baseVersion="3.5.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/mybatis/mybatis/3.5.7/mybatis-3.5.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/mybatis/mybatis/3.5.7/mybatis-3.5.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.projectlombok:lombok:1.18.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.projectlombok" artifactId="lombok" version="1.18.2" baseVersion="1.18.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/projectlombok/lombok/1.18.2/lombok-1.18.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/projectlombok/lombok/1.18.2/lombok-1.18.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/projectlombok/lombok/1.18.2/lombok-1.18.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.quartz-scheduler:quartz:2.3.2" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.quartz-scheduler" artifactId="quartz" version="2.3.2" baseVersion="2.3.2" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/quartz-scheduler/quartz/2.3.2/quartz-2.3.2.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/quartz-scheduler/quartz/2.3.2/quartz-2.3.2-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/quartz-scheduler/quartz/2.3.2/quartz-2.3.2-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.reactivestreams:reactive-streams:1.0.3" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.reactivestreams" artifactId="reactive-streams" version="1.0.3" baseVersion="1.0.3" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.slf4j:jcl-over-slf4j:1.7.30" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.slf4j" artifactId="jcl-over-slf4j" version="1.7.30" baseVersion="1.7.30" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/slf4j/jcl-over-slf4j/1.7.30/jcl-over-slf4j-1.7.30.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/slf4j/jcl-over-slf4j/1.7.30/jcl-over-slf4j-1.7.30-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/slf4j/jcl-over-slf4j/1.7.30/jcl-over-slf4j-1.7.30-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.slf4j:jul-to-slf4j:1.7.30" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.slf4j" artifactId="jul-to-slf4j" version="1.7.30" baseVersion="1.7.30" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.slf4j:slf4j-api:1.7.25" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.slf4j" artifactId="slf4j-api" version="1.7.25" baseVersion="1.7.25" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-actuator-autoconfigure:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-actuator-autoconfigure" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.5.RELEASE/spring-boot-actuator-autoconfigure-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.5.RELEASE/spring-boot-actuator-autoconfigure-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.5.RELEASE/spring-boot-actuator-autoconfigure-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-actuator:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-actuator" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-actuator/2.2.5.RELEASE/spring-boot-actuator-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-actuator/2.2.5.RELEASE/spring-boot-actuator-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-actuator/2.2.5.RELEASE/spring-boot-actuator-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-autoconfigure" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-autoconfigure/2.2.5.RELEASE/spring-boot-autoconfigure-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-autoconfigure/2.2.5.RELEASE/spring-boot-autoconfigure-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-autoconfigure/2.2.5.RELEASE/spring-boot-autoconfigure-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-configuration-processor:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-configuration-processor" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-configuration-processor/2.2.5.RELEASE/spring-boot-configuration-processor-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-configuration-processor/2.2.5.RELEASE/spring-boot-configuration-processor-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-configuration-processor/2.2.5.RELEASE/spring-boot-configuration-processor-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-actuator:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-actuator" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-actuator/2.2.5.RELEASE/spring-boot-starter-actuator-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-actuator/2.2.5.RELEASE/spring-boot-starter-actuator-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-actuator/2.2.5.RELEASE/spring-boot-starter-actuator-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-aop:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-aop" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-aop/2.2.5.RELEASE/spring-boot-starter-aop-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-aop/2.2.5.RELEASE/spring-boot-starter-aop-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-aop/2.2.5.RELEASE/spring-boot-starter-aop-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-data-redis:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-data-redis" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-data-redis/2.2.5.RELEASE/spring-boot-starter-data-redis-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-data-redis/2.2.5.RELEASE/spring-boot-starter-data-redis-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-data-redis/2.2.5.RELEASE/spring-boot-starter-data-redis-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-integration:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-integration" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-integration/2.2.5.RELEASE/spring-boot-starter-integration-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-integration/2.2.5.RELEASE/spring-boot-starter-integration-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-integration/2.2.5.RELEASE/spring-boot-starter-integration-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-jdbc" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-jdbc/2.2.5.RELEASE/spring-boot-starter-jdbc-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-jdbc/2.2.5.RELEASE/spring-boot-starter-jdbc-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-jdbc/2.2.5.RELEASE/spring-boot-starter-jdbc-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-json:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-json" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-json/2.2.5.RELEASE/spring-boot-starter-json-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-json/2.2.5.RELEASE/spring-boot-starter-json-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-json/2.2.5.RELEASE/spring-boot-starter-json-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-log4j2:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-log4j2" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-log4j2/2.2.5.RELEASE/spring-boot-starter-log4j2-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-log4j2/2.2.5.RELEASE/spring-boot-starter-log4j2-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-log4j2/2.2.5.RELEASE/spring-boot-starter-log4j2-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-quartz:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-quartz" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-quartz/2.2.5.RELEASE/spring-boot-starter-quartz-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-quartz/2.2.5.RELEASE/spring-boot-starter-quartz-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-quartz/2.2.5.RELEASE/spring-boot-starter-quartz-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-tomcat" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-tomcat/2.2.5.RELEASE/spring-boot-starter-tomcat-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-tomcat/2.2.5.RELEASE/spring-boot-starter-tomcat-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-tomcat/2.2.5.RELEASE/spring-boot-starter-tomcat-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-validation:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-validation" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-validation/2.2.5.RELEASE/spring-boot-starter-validation-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-validation/2.2.5.RELEASE/spring-boot-starter-validation-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-validation/2.2.5.RELEASE/spring-boot-starter-validation-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter-web:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-web" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-web/2.2.5.RELEASE/spring-boot-starter-web-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-web/2.2.5.RELEASE/spring-boot-starter-web-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter-web/2.2.5.RELEASE/spring-boot-starter-web-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot-starter:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot-starter" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter/2.2.5.RELEASE/spring-boot-starter-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter/2.2.5.RELEASE/spring-boot-starter-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot-starter/2.2.5.RELEASE/spring-boot-starter-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.boot:spring-boot:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.boot" artifactId="spring-boot" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot/2.2.5.RELEASE/spring-boot-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot/2.2.5.RELEASE/spring-boot-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/boot/spring-boot/2.2.5.RELEASE/spring-boot-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.cloud:spring-cloud-commons:2.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.cloud" artifactId="spring-cloud-commons" version="2.2.2.RELEASE" baseVersion="2.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-commons/2.2.2.RELEASE/spring-cloud-commons-2.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-commons/2.2.2.RELEASE/spring-cloud-commons-2.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-commons/2.2.2.RELEASE/spring-cloud-commons-2.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.cloud:spring-cloud-context:2.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.cloud" artifactId="spring-cloud-context" version="2.2.2.RELEASE" baseVersion="2.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-context/2.2.2.RELEASE/spring-cloud-context-2.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-context/2.2.2.RELEASE/spring-cloud-context-2.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-context/2.2.2.RELEASE/spring-cloud-context-2.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.cloud:spring-cloud-netflix-archaius:2.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.cloud" artifactId="spring-cloud-netflix-archaius" version="2.2.2.RELEASE" baseVersion="2.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-netflix-archaius/2.2.2.RELEASE/spring-cloud-netflix-archaius-2.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-netflix-archaius/2.2.2.RELEASE/spring-cloud-netflix-archaius-2.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-netflix-archaius/2.2.2.RELEASE/spring-cloud-netflix-archaius-2.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.cloud" artifactId="spring-cloud-netflix-ribbon" version="2.2.2.RELEASE" baseVersion="2.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-netflix-ribbon/2.2.2.RELEASE/spring-cloud-netflix-ribbon-2.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-netflix-ribbon/2.2.2.RELEASE/spring-cloud-netflix-ribbon-2.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-netflix-ribbon/2.2.2.RELEASE/spring-cloud-netflix-ribbon-2.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.cloud:spring-cloud-openfeign-core:2.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.cloud" artifactId="spring-cloud-openfeign-core" version="2.2.2.RELEASE" baseVersion="2.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-openfeign-core/2.2.2.RELEASE/spring-cloud-openfeign-core-2.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-openfeign-core/2.2.2.RELEASE/spring-cloud-openfeign-core-2.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-openfeign-core/2.2.2.RELEASE/spring-cloud-openfeign-core-2.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter-netflix-archaius" version="2.2.2.RELEASE" baseVersion="2.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter-netflix-archaius/2.2.2.RELEASE/spring-cloud-starter-netflix-archaius-2.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter-netflix-archaius/2.2.2.RELEASE/spring-cloud-starter-netflix-archaius-2.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter-netflix-archaius/2.2.2.RELEASE/spring-cloud-starter-netflix-archaius-2.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter-netflix-ribbon" version="2.2.2.RELEASE" baseVersion="2.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter-netflix-ribbon/2.2.2.RELEASE/spring-cloud-starter-netflix-ribbon-2.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter-netflix-ribbon/2.2.2.RELEASE/spring-cloud-starter-netflix-ribbon-2.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter-netflix-ribbon/2.2.2.RELEASE/spring-cloud-starter-netflix-ribbon-2.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.cloud:spring-cloud-starter-openfeign:2.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter-openfeign" version="2.2.2.RELEASE" baseVersion="2.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter-openfeign/2.2.2.RELEASE/spring-cloud-starter-openfeign-2.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter-openfeign/2.2.2.RELEASE/spring-cloud-starter-openfeign-2.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter-openfeign/2.2.2.RELEASE/spring-cloud-starter-openfeign-2.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.cloud:spring-cloud-starter:2.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter" version="2.2.2.RELEASE" baseVersion="2.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter/2.2.2.RELEASE/spring-cloud-starter-2.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter/2.2.2.RELEASE/spring-cloud-starter-2.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/cloud/spring-cloud-starter/2.2.2.RELEASE/spring-cloud-starter-2.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.data:spring-data-commons:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.data" artifactId="spring-data-commons" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/data/spring-data-commons/2.2.5.RELEASE/spring-data-commons-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/data/spring-data-commons/2.2.5.RELEASE/spring-data-commons-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/data/spring-data-commons/2.2.5.RELEASE/spring-data-commons-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.data:spring-data-keyvalue:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.data" artifactId="spring-data-keyvalue" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/data/spring-data-keyvalue/2.2.5.RELEASE/spring-data-keyvalue-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/data/spring-data-keyvalue/2.2.5.RELEASE/spring-data-keyvalue-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/data/spring-data-keyvalue/2.2.5.RELEASE/spring-data-keyvalue-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.data:spring-data-redis:2.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.data" artifactId="spring-data-redis" version="2.2.5.RELEASE" baseVersion="2.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/data/spring-data-redis/2.2.5.RELEASE/spring-data-redis-2.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/data/spring-data-redis/2.2.5.RELEASE/spring-data-redis-2.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/data/spring-data-redis/2.2.5.RELEASE/spring-data-redis-2.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.integration:spring-integration-core:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.integration" artifactId="spring-integration-core" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/integration/spring-integration-core/5.2.4.RELEASE/spring-integration-core-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/integration/spring-integration-core/5.2.4.RELEASE/spring-integration-core-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/integration/spring-integration-core/5.2.4.RELEASE/spring-integration-core-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.integration:spring-integration-mqtt:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.integration" artifactId="spring-integration-mqtt" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/integration/spring-integration-mqtt/5.2.4.RELEASE/spring-integration-mqtt-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/integration/spring-integration-mqtt/5.2.4.RELEASE/spring-integration-mqtt-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/integration/spring-integration-mqtt/5.2.4.RELEASE/spring-integration-mqtt-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.integration:spring-integration-stream:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.integration" artifactId="spring-integration-stream" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/integration/spring-integration-stream/5.2.4.RELEASE/spring-integration-stream-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/integration/spring-integration-stream/5.2.4.RELEASE/spring-integration-stream-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/integration/spring-integration-stream/5.2.4.RELEASE/spring-integration-stream-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.plugin" artifactId="spring-plugin-core" version="1.2.0.RELEASE" baseVersion="1.2.0.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.plugin" artifactId="spring-plugin-metadata" version="1.2.0.RELEASE" baseVersion="1.2.0.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.retry:spring-retry:1.2.5.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.retry" artifactId="spring-retry" version="1.2.5.RELEASE" baseVersion="1.2.5.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/retry/spring-retry/1.2.5.RELEASE/spring-retry-1.2.5.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/retry/spring-retry/1.2.5.RELEASE/spring-retry-1.2.5.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/retry/spring-retry/1.2.5.RELEASE/spring-retry-1.2.5.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.security:spring-security-crypto:5.2.2.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.security" artifactId="spring-security-crypto" version="5.2.2.RELEASE" baseVersion="5.2.2.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/security/spring-security-crypto/5.2.2.RELEASE/spring-security-crypto-5.2.2.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/security/spring-security-crypto/5.2.2.RELEASE/spring-security-crypto-5.2.2.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/security/spring-security-crypto/5.2.2.RELEASE/spring-security-crypto-5.2.2.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework.security:spring-security-rsa:1.0.9.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework.security" artifactId="spring-security-rsa" version="1.0.9.RELEASE" baseVersion="1.0.9.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/security/spring-security-rsa/1.0.9.RELEASE/spring-security-rsa-1.0.9.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/security/spring-security-rsa/1.0.9.RELEASE/spring-security-rsa-1.0.9.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/security/spring-security-rsa/1.0.9.RELEASE/spring-security-rsa-1.0.9.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-aop:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-aop" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-aop/5.2.4.RELEASE/spring-aop-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-aop/5.2.4.RELEASE/spring-aop-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-aop/5.2.4.RELEASE/spring-aop-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-beans:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-beans" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-beans/5.2.4.RELEASE/spring-beans-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-beans/5.2.4.RELEASE/spring-beans-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-beans/5.2.4.RELEASE/spring-beans-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-context-support:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-context-support" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-context-support/5.2.4.RELEASE/spring-context-support-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-context-support/5.2.4.RELEASE/spring-context-support-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-context-support/5.2.4.RELEASE/spring-context-support-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-context:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-context" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-context/5.2.4.RELEASE/spring-context-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-context/5.2.4.RELEASE/spring-context-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-context/5.2.4.RELEASE/spring-context-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-core:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-core" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-core/5.2.4.RELEASE/spring-core-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-core/5.2.4.RELEASE/spring-core-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-core/5.2.4.RELEASE/spring-core-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-expression:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-expression" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-expression/5.2.4.RELEASE/spring-expression-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-expression/5.2.4.RELEASE/spring-expression-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-expression/5.2.4.RELEASE/spring-expression-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-jcl:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-jcl" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-jcl/5.2.4.RELEASE/spring-jcl-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-jcl/5.2.4.RELEASE/spring-jcl-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-jcl/5.2.4.RELEASE/spring-jcl-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-jdbc:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-jdbc" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-jdbc/5.2.4.RELEASE/spring-jdbc-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-jdbc/5.2.4.RELEASE/spring-jdbc-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-jdbc/5.2.4.RELEASE/spring-jdbc-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-messaging:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-messaging" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-messaging/5.2.4.RELEASE/spring-messaging-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-messaging/5.2.4.RELEASE/spring-messaging-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-messaging/5.2.4.RELEASE/spring-messaging-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-orm:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-orm" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-orm/5.2.4.RELEASE/spring-orm-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-orm/5.2.4.RELEASE/spring-orm-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-orm/5.2.4.RELEASE/spring-orm-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-oxm:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-oxm" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-oxm/5.2.4.RELEASE/spring-oxm-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-oxm/5.2.4.RELEASE/spring-oxm-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-oxm/5.2.4.RELEASE/spring-oxm-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-test:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-test" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-test/5.2.4.RELEASE/spring-test-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-test/5.2.4.RELEASE/spring-test-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-test/5.2.4.RELEASE/spring-test-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-tx:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-tx" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-tx/5.2.4.RELEASE/spring-tx-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-tx/5.2.4.RELEASE/spring-tx-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-tx/5.2.4.RELEASE/spring-tx-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-web:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-web" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-web/5.2.4.RELEASE/spring-web-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-web/5.2.4.RELEASE/spring-web-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-web/5.2.4.RELEASE/spring-web-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.springframework:spring-webmvc:5.2.4.RELEASE" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.springframework" artifactId="spring-webmvc" version="5.2.4.RELEASE" baseVersion="5.2.4.RELEASE" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-webmvc/5.2.4.RELEASE/spring-webmvc-5.2.4.RELEASE.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-webmvc/5.2.4.RELEASE/spring-webmvc-5.2.4.RELEASE-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/springframework/spring-webmvc/5.2.4.RELEASE/spring-webmvc-5.2.4.RELEASE-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: org.yaml:snakeyaml:1.33" type="java-imported" external-system-id="Maven">
+      <properties groupId="org.yaml" artifactId="snakeyaml" version="1.33" baseVersion="1.33" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/yaml/snakeyaml/1.33/snakeyaml-1.33.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/yaml/snakeyaml/1.33/snakeyaml-1.33-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/org/yaml/snakeyaml/1.33/snakeyaml-1.33-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: redis.clients:jedis:2.9.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="redis.clients" artifactId="jedis" version="2.9.0" baseVersion="2.9.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/redis/clients/jedis/2.9.0/jedis-2.9.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/redis/clients/jedis/2.9.0/jedis-2.9.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/redis/clients/jedis/2.9.0/jedis-2.9.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: redis.clients:jedis:3.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="redis.clients" artifactId="jedis" version="3.0.0" baseVersion="3.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/redis/clients/jedis/3.0.0/jedis-3.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/redis/clients/jedis/3.0.0/jedis-3.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/redis/clients/jedis/3.0.0/jedis-3.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: sdk.dcfirm:dcfirmsdk-all:1.7" type="java-imported" external-system-id="Maven">
+      <properties groupId="sdk.dcfirm" artifactId="dcfirmsdk-all" version="1.7" baseVersion="1.7" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/sdk/dcfirm/dcfirmsdk-all/1.7/dcfirmsdk-all-1.7.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/sdk/dcfirm/dcfirmsdk-all/1.7/dcfirmsdk-all-1.7-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/sdk/dcfirm/dcfirmsdk-all/1.7/dcfirmsdk-all-1.7-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: sdk.trudian:td-direct:1.0.0" type="java-imported" external-system-id="Maven">
+      <properties groupId="sdk.trudian" artifactId="td-direct" version="1.0.0" baseVersion="1.0.0" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/sdk/trudian/td-direct/1.0.0/td-direct-1.0.0.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/sdk/trudian/td-direct/1.0.0/td-direct-1.0.0-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/sdk/trudian/td-direct/1.0.0/td-direct-1.0.0-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: sdk.trudian:tdcloud:v1.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="sdk.trudian" artifactId="tdcloud" version="v1.0.1" baseVersion="v1.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/sdk/trudian/tdcloud/v1.0.1/tdcloud-v1.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/sdk/trudian/tdcloud/v1.0.1/tdcloud-v1.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/sdk/trudian/tdcloud/v1.0.1/tdcloud-v1.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: stax:stax-api:1.0.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="stax" artifactId="stax-api" version="1.0.1" baseVersion="1.0.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/stax/stax-api/1.0.1/stax-api-1.0.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/stax/stax-api/1.0.1/stax-api-1.0.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/stax/stax-api/1.0.1/stax-api-1.0.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+    <library name="Maven: xmlpull:xmlpull:1.1.3.1" type="java-imported" external-system-id="Maven">
+      <properties groupId="xmlpull" artifactId="xmlpull" version="1.1.3.1" baseVersion="1.1.3.1" />
+      <CLASSES>
+        <root url="jar://$USER_HOME$/.m2/repository1/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar!/" />
+      </CLASSES>
+      <JAVADOC>
+        <root url="jar://$USER_HOME$/.m2/repository1/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1-javadoc.jar!/" />
+      </JAVADOC>
+      <SOURCES>
+        <root url="jar://$USER_HOME$/.m2/repository1/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1-sources.jar!/" />
+      </SOURCES>
+    </library>
+  </component>
+</project>
\ No newline at end of file
Index: rygym/ams.iws
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams.iws b/rygym/ams.iws
--- a/rygym/ams.iws	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams.iws	(date 1750159316015)
@@ -1,418 +1,581 @@
 <?xml version="1.0" encoding="UTF-8"?>
-
-<!--
-Licensed to the Apache Software Foundation (ASF) under one
-or more contributor license agreements.  See the NOTICE file
-distributed with this work for additional information
-regarding copyright ownership.  The ASF licenses this file
-to you under the Apache License, Version 2.0 (the
-"License"); you may not use this file except in compliance
-with the License.  You may obtain a copy of the License at
-
-  http://www.apache.org/licenses/LICENSE-2.0
-
-Unless required by applicable law or agreed to in writing,
-software distributed under the License is distributed on an
-"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
-KIND, either express or implied.  See the License for the
-specific language governing permissions and limitations
-under the License.
--->
-<project version="4" relativePaths="false"> 
-  <component name="LvcsProjectConfiguration"> 
-    <option name="ADD_LABEL_ON_PROJECT_OPEN" value="true"/>  
-    <option name="ADD_LABEL_ON_PROJECT_COMPILATION" value="true"/>  
-    <option name="ADD_LABEL_ON_FILE_PACKAGE_COMPILATION" value="true"/>  
-    <option name="ADD_LABEL_ON_PROJECT_MAKE" value="true"/>  
-    <option name="ADD_LABEL_ON_RUNNING" value="true"/>  
-    <option name="ADD_LABEL_ON_DEBUGGING" value="true"/>  
-    <option name="ADD_LABEL_ON_UNIT_TEST_PASSED" value="true"/>  
-    <option name="ADD_LABEL_ON_UNIT_TEST_FAILED" value="true"/> 
-  </component>  
-  <component name="PropertiesComponent"> 
-    <property name="MemberChooser.copyJavadoc" value="false"/>  
-    <property name="GoToClass.includeLibraries" value="false"/>  
-    <property name="MemberChooser.showClasses" value="true"/>  
-    <property name="MemberChooser.sorted" value="false"/>  
-    <property name="GoToFile.includeJavaFiles" value="false"/>  
-    <property name="GoToClass.toSaveIncludeLibraries" value="false"/> 
-  </component>  
-  <component name="ToolWindowManager"> 
-    <frame x="-4" y="-4" width="1032" height="746" extended-state="6"/>  
-    <editor active="false"/>  
-    <layout> 
-      <window_info id="CVS" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="-1"/>  
-      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="7"/>  
-      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="0"/>  
-      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="1"/>  
-      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="1"/>  
-      <window_info id="Messages" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="-1"/>  
-      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.4" order="6"/>  
-      <window_info id="Aspects" active="false" anchor="right" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="-1"/>  
-      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="1"/>  
-      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="2"/>  
-      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="2"/>  
-      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.4" order="4"/>  
-      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="sliding" type="sliding" visible="false" weight="0.4" order="0"/>  
-      <window_info id="Web" active="false" anchor="left" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="2"/>  
-      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="0"/>  
-      <window_info id="EJB" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="3"/>  
-      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="5"/> 
-    </layout> 
-  </component>  
-  <component name="ErrorTreeViewConfiguration"> 
-    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false"/>  
-    <option name="HIDE_WARNINGS" value="false"/> 
-  </component>  
-  <component name="StructureViewFactory"> 
-    <option name="SORT_MODE" value="0"/>  
-    <option name="GROUP_INHERITED" value="true"/>  
-    <option name="AUTOSCROLL_MODE" value="true"/>  
-    <option name="SHOW_FIELDS" value="true"/>  
-    <option name="AUTOSCROLL_FROM_SOURCE" value="false"/>  
-    <option name="GROUP_GETTERS_AND_SETTERS" value="true"/>  
-    <option name="SHOW_INHERITED" value="false"/>  
-    <option name="HIDE_NOT_PUBLIC" value="false"/> 
-  </component>  
-  <component name="ProjectViewSettings"> 
-    <navigator currentView="ProjectPane" flattenPackages="false" showMembers="false" showStructure="false" autoscrollToSource="false" splitterProportion="0.5"/>  
-    <view id="ProjectPane"> 
-      <expanded_node type="directory" url="file://$PROJECT_DIR$"/> 
-    </view>  
-    <view id="SourcepathPane"/>  
-    <view id="ClasspathPane"/> 
-  </component>  
-  <component name="Commander"> 
-    <leftPanel view="Project"/>  
-    <rightPanel view="Project"/>  
-    <splitter proportion="0.5"/> 
-  </component>  
-  <component name="AspectsView"/>  
-  <component name="SelectInManager"/>  
-  <component name="HierarchyBrowserManager"> 
-    <option name="SHOW_PACKAGES" value="false"/>  
-    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false"/>  
-    <option name="SORT_ALPHABETICALLY" value="false"/> 
-  </component>  
-  <component name="TodoView" selected-index="0"> 
-    <todo-panel id="selected-file"> 
-      <are-packages-shown value="false"/>  
-      <flatten-packages value="false"/>  
-      <is-autoscroll-to-source value="true"/> 
-    </todo-panel>  
-    <todo-panel id="all"> 
-      <are-packages-shown value="true"/>  
-      <flatten-packages value="false"/>  
-      <is-autoscroll-to-source value="true"/> 
-    </todo-panel> 
-  </component>  
-  <component name="editorManager"/>  
-  <component name="editorHistoryManager"/>  
-  <component name="DaemonCodeAnalyzer"> 
-    <disable_hints/> 
-  </component>  
-  <component name="InspectionManager"> 
-    <option name="AUTOSCROLL_TO_SOURCE" value="false"/>  
-    <option name="SPLITTER_PROPORTION" value="0.5"/>  
-    <profile name="Default"/> 
-  </component>  
-  <component name="BookmarkManager"/>  
-  <component name="DebuggerManager"> 
-    <line_breakpoints/>  
-    <exception_breakpoints> 
-      <breakpoint_any> 
-        <option name="NOTIFY_CAUGHT" value="true"/>  
-        <option name="NOTIFY_UNCAUGHT" value="true"/>  
-        <option name="ENABLED" value="false"/>  
-        <option name="SUSPEND_VM" value="true"/>  
-        <option name="COUNT_FILTER_ENABLED" value="false"/>  
-        <option name="COUNT_FILTER" value="0"/>  
-        <option name="CONDITION_ENABLED" value="false"/>  
-        <option name="CONDITION"/>  
-        <option name="LOG_ENABLED" value="false"/>  
-        <option name="LOG_EXPRESSION_ENABLED" value="false"/>  
-        <option name="LOG_MESSAGE"/>  
-        <option name="CLASS_FILTERS_ENABLED" value="false"/>  
-        <option name="INVERSE_CLASS_FILLTERS" value="false"/>  
-        <option name="SUSPEND_POLICY" value="SuspendAll"/> 
-      </breakpoint_any> 
-    </exception_breakpoints>  
-    <field_breakpoints/>  
-    <method_breakpoints/> 
-  </component>  
-  <component name="DebuggerSettings"> 
-    <option name="TRACING_FILTERS_ENABLED" value="true"/>  
-    <option name="TOSTRING_CLASSES_ENABLED" value="false"/>  
-    <option name="VALUE_LOOKUP_DELAY" value="700"/>  
-    <option name="DEBUGGER_TRANSPORT" value="0"/>  
-    <option name="FORCE_CLASSIC_VM" value="true"/>  
-    <option name="HIDE_DEBUGGER_ON_PROCESS_TERMINATION" value="false"/>  
-    <option name="SKIP_SYNTHETIC_METHODS" value="true"/>  
-    <option name="SKIP_CONSTRUCTORS" value="false"/>  
-    <option name="STEP_THREAD_SUSPEND_POLICY" value="SuspendThread"/>  
-    <default_breakpoint_settings> 
-      <option name="NOTIFY_CAUGHT" value="true"/>  
-      <option name="NOTIFY_UNCAUGHT" value="true"/>  
-      <option name="WATCH_MODIFICATION" value="true"/>  
-      <option name="WATCH_ACCESS" value="true"/>  
-      <option name="WATCH_ENTRY" value="true"/>  
-      <option name="WATCH_EXIT" value="true"/>  
-      <option name="ENABLED" value="true"/>  
-      <option name="SUSPEND_VM" value="true"/>  
-      <option name="COUNT_FILTER_ENABLED" value="false"/>  
-      <option name="COUNT_FILTER" value="0"/>  
-      <option name="CONDITION_ENABLED" value="false"/>  
-      <option name="CONDITION"/>  
-      <option name="LOG_ENABLED" value="false"/>  
-      <option name="LOG_EXPRESSION_ENABLED" value="false"/>  
-      <option name="LOG_MESSAGE"/>  
-      <option name="CLASS_FILTERS_ENABLED" value="false"/>  
-      <option name="INVERSE_CLASS_FILLTERS" value="false"/>  
-      <option name="SUSPEND_POLICY" value="SuspendAll"/> 
-    </default_breakpoint_settings>  
-    <filter> 
-      <option name="PATTERN" value="com.sun.*"/>  
-      <option name="ENABLED" value="true"/> 
-    </filter>  
-    <filter> 
-      <option name="PATTERN" value="java.*"/>  
-      <option name="ENABLED" value="true"/> 
-    </filter>  
-    <filter> 
-      <option name="PATTERN" value="javax.*"/>  
-      <option name="ENABLED" value="true"/> 
-    </filter>  
-    <filter> 
-      <option name="PATTERN" value="org.omg.*"/>  
-      <option name="ENABLED" value="true"/> 
-    </filter>  
-    <filter> 
-      <option name="PATTERN" value="sun.*"/>  
-      <option name="ENABLED" value="true"/> 
-    </filter>  
-    <filter> 
-      <option name="PATTERN" value="junit.*"/>  
-      <option name="ENABLED" value="true"/> 
-    </filter> 
-  </component>  
-  <component name="CompilerWorkspaceConfiguration"> 
-    <option name="COMPILE_IN_BACKGROUND" value="false"/>  
-    <option name="AUTO_SHOW_ERRORS_IN_EDITOR" value="true"/> 
-  </component>  
-  <component name="RunManager"> 
-    <activeType name="Application"/>  
-    <configuration selected="false" default="true" type="Applet" factoryName="Applet"> 
-      <module name=""/>  
-      <option name="MAIN_CLASS_NAME"/>  
-      <option name="HTML_FILE_NAME"/>  
-      <option name="HTML_USED" value="false"/>  
-      <option name="WIDTH" value="400"/>  
-      <option name="HEIGHT" value="300"/>  
-      <option name="POLICY_FILE" value="$APPLICATION_HOME_DIR$/bin/appletviewer.policy"/>  
-      <option name="VM_PARAMETERS"/> 
-    </configuration>  
-    <configuration selected="false" default="true" type="Remote" factoryName="Remote"> 
-      <option name="USE_SOCKET_TRANSPORT" value="true"/>  
-      <option name="SERVER_MODE" value="false"/>  
-      <option name="SHMEM_ADDRESS" value="javadebug"/>  
-      <option name="HOST" value="localhost"/>  
-      <option name="PORT" value="5005"/> 
-    </configuration>  
-    <configuration selected="false" default="true" type="Application" factoryName="Application"> 
-      <option name="MAIN_CLASS_NAME"/>  
-      <option name="VM_PARAMETERS"/>  
-      <option name="PROGRAM_PARAMETERS"/>  
-      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$"/>  
-      <module name=""/> 
-    </configuration>  
-    <configuration selected="false" default="true" type="JUnit" factoryName="JUnit"> 
-      <module name=""/>  
-      <option name="PACKAGE_NAME"/>  
-      <option name="MAIN_CLASS_NAME"/>  
-      <option name="METHOD_NAME"/>  
-      <option name="TEST_OBJECT" value="class"/>  
-      <option name="VM_PARAMETERS"/>  
-      <option name="PARAMETERS"/>  
-      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$"/>  
-      <option name="ADDITIONAL_CLASS_PATH"/>  
-      <option name="TEST_SEARCH_SCOPE"> 
-        <value defaultName="wholeProject"/> 
-      </option> 
-    </configuration> 
-  </component>  
-  <component name="VcsManagerConfiguration"> 
-    <option name="ACTIVE_VCS_NAME" value=""/>  
-    <option name="STATE" value="0"/> 
-  </component>  
-  <component name="VssConfiguration"> 
-    <CheckoutOptions> 
-      <option name="COMMENT" value=""/>  
-      <option name="DO_NOT_GET_LATEST_VERSION" value="false"/>  
-      <option name="REPLACE_WRITABLE" value="false"/>  
-      <option name="RECURSIVE" value="false"/> 
-    </CheckoutOptions>  
-    <CheckinOptions> 
-      <option name="COMMENT" value=""/>  
-      <option name="KEEP_CHECKED_OUT" value="false"/>  
-      <option name="RECURSIVE" value="false"/> 
-    </CheckinOptions>  
-    <AddOptions> 
-      <option name="COMMENT" value=""/>  
-      <option name="STORE_ONLY_LATEST_VERSION" value="false"/>  
-      <option name="CHECK_OUT_IMMEDIATELY" value="false"/>  
-      <option name="FILE_TYPE" value="0"/> 
-    </AddOptions>  
-    <UndocheckoutOptions> 
-      <option name="MAKE_WRITABLE" value="false"/>  
-      <option name="REPLACE_LOCAL_COPY" value="0"/>  
-      <option name="RECURSIVE" value="false"/> 
-    </UndocheckoutOptions>  
-    <DiffOptions> 
-      <option name="IGNORE_WHITE_SPACE" value="false"/>  
-      <option name="IGNORE_CASE" value="false"/> 
-    </DiffOptions>  
-    <GetOptions> 
-      <option name="REPLACE_WRITABLE" value="0"/>  
-      <option name="MAKE_WRITABLE" value="false"/>  
-      <option name="RECURSIVE" value="false"/> 
-    </GetOptions>  
-    <option name="CLIENT_PATH" value=""/>  
-    <option name="SRCSAFEINI_PATH" value=""/>  
-    <option name="USER_NAME" value=""/>  
-    <option name="PWD" value=""/>  
-    <option name="SHOW_CHECKOUT_OPTIONS" value="true"/>  
-    <option name="SHOW_ADD_OPTIONS" value="true"/>  
-    <option name="SHOW_UNDOCHECKOUT_OPTIONS" value="true"/>  
-    <option name="SHOW_DIFF_OPTIONS" value="true"/>  
-    <option name="SHOW_GET_OPTIONS" value="true"/>  
-    <option name="USE_EXTERNAL_DIFF" value="false"/>  
-    <option name="EXTERNAL_DIFF_PATH" value=""/>  
-    <option name="REUSE_LAST_COMMENT" value="false"/>  
-    <option name="PUT_FOCUS_INTO_COMMENT" value="false"/>  
-    <option name="SHOW_CHECKIN_OPTIONS" value="true"/>  
-    <option name="LAST_COMMIT_MESSAGE" value=""/>  
-    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8"/> 
-  </component>  
-  <component name="CheckinPanelState"/>  
-  <component name="WebViewSettings"> 
-    <webview flattenPackages="false" showMembers="false" autoscrollToSource="false"/> 
-  </component>  
-  <component name="EjbViewSettings"> 
-    <EjbView showMembers="false" autoscrollToSource="false"/> 
-  </component>  
-  <component name="AppServerRunManager"/>  
-  <component name="StarteamConfiguration"> 
-    <option name="SERVER" value=""/>  
-    <option name="PORT" value="49201"/>  
-    <option name="USER" value=""/>  
-    <option name="PASSWORD" value=""/>  
-    <option name="PROJECT" value=""/>  
-    <option name="VIEW" value=""/>  
-    <option name="ALTERNATIVE_WORKING_PATH" value=""/>  
-    <option name="PUT_FOCUS_INTO_COMMENT" value="false"/>  
-    <option name="SHOW_CHECKIN_OPTIONS" value="true"/>  
-    <option name="LAST_COMMIT_MESSAGE" value=""/>  
-    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8"/> 
-  </component>  
-  <component name="Cvs2Configuration"> 
-    <option name="ON_FILE_ADDING" value="0"/>  
-    <option name="ON_FILE_REMOVING" value="0"/>  
-    <option name="PRUNE_EMPTY_DIRECTORIES" value="true"/>  
-    <option name="SHOW_UPDATE_OPTIONS" value="true"/>  
-    <option name="SHOW_ADD_OPTIONS" value="true"/>  
-    <option name="SHOW_REMOVE_OPTIONS" value="true"/>  
-    <option name="MERGING_MODE" value="0"/>  
-    <option name="MERGE_WITH_BRANCH1_NAME" value="HEAD"/>  
-    <option name="MERGE_WITH_BRANCH2_NAME" value="HEAD"/>  
-    <option name="RESET_STICKY" value="false"/>  
-    <option name="CREATE_NEW_DIRECTORIES" value="true"/>  
-    <option name="DEFAULT_TEXT_FILE_SUBSTITUTION" value="kv"/>  
-    <option name="PROCESS_UNKNOWN_FILES" value="false"/>  
-    <option name="PROCESS_DELETED_FILES" value="false"/>  
-    <option name="SHOW_EDIT_DIALOG" value="true"/>  
-    <option name="RESERVED_EDIT" value="false"/>  
-    <option name="FILE_HISTORY_SPLITTER_PROPORTION" value="0.6"/>  
-    <option name="SHOW_CHECKOUT_OPTIONS" value="true"/>  
-    <option name="CHECKOUT_DATE_OR_REVISION_SETTINGS"> 
-      <value> 
-        <option name="BRANCH" value=""/>  
-        <option name="DATE" value=""/>  
-        <option name="USE_BRANCH" value="false"/>  
-        <option name="USE_DATE" value="false"/> 
-      </value> 
-    </option>  
-    <option name="UPDATE_DATE_OR_REVISION_SETTINGS"> 
-      <value> 
-        <option name="BRANCH" value=""/>  
-        <option name="DATE" value=""/>  
-        <option name="USE_BRANCH" value="false"/>  
-        <option name="USE_DATE" value="false"/> 
-      </value> 
-    </option>  
-    <option name="SHOW_CHANGES_REVISION_SETTINGS"> 
-      <value> 
-        <option name="BRANCH" value=""/>  
-        <option name="DATE" value=""/>  
-        <option name="USE_BRANCH" value="false"/>  
-        <option name="USE_DATE" value="false"/> 
-      </value> 
-    </option>  
-    <option name="SHOW_OUTPUT" value="false"/>  
-    <option name="SHOW_FILE_HISTORY_AS_TREE" value="false"/>  
-    <option name="UPDATE_GROUP_BY_PACKAGES" value="false"/>  
-    <option name="ADD_WATCH_INDEX" value="0"/>  
-    <option name="REMOVE_WATCH_INDEX" value="0"/>  
-    <option name="UPDATE_KEYWORD_SUBSTITUTION"/>  
-    <option name="MAKE_NEW_FILES_READONLY" value="false"/>  
-    <option name="SHOW_CORRUPTED_PROJECT_FILES" value="0"/>  
-    <option name="TAG_AFTER_FILE_COMMIT" value="false"/>  
-    <option name="TAG_AFTER_FILE_COMMIT_NAME" value=""/>  
-    <option name="TAG_AFTER_PROJECT_COMMIT" value="false"/>  
-    <option name="TAG_AFTER_PROJECT_COMMIT_NAME" value=""/>  
-    <option name="PUT_FOCUS_INTO_COMMENT" value="false"/>  
-    <option name="SHOW_CHECKIN_OPTIONS" value="true"/>  
-    <option name="FORCE_NON_EMPTY_COMMENT" value="false"/>  
-    <option name="LAST_COMMIT_MESSAGE" value=""/>  
-    <option name="SAVE_LAST_COMMIT_MESSAGE" value="true"/>  
-    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8"/>  
-    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="false"/>  
-    <option name="OPTIMIZE_IMPORTS_BEFORE_FILE_COMMIT" value="false"/>  
-    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="false"/>  
-    <option name="REFORMAT_BEFORE_FILE_COMMIT" value="false"/>  
-    <option name="FILE_HISTORY_DIALOG_COMMENTS_SPLITTER_PROPORTION" value="0.8"/>  
-    <option name="FILE_HISTORY_DIALOG_SPLITTER_PROPORTION" value="0.5"/> 
-  </component>  
-  <component name="CvsTabbedWindow"/>  
-  <component name="SvnConfiguration"> 
-    <option name="USER" value=""/>  
-    <option name="PASSWORD" value=""/>  
-    <option name="AUTO_ADD_FILES" value="0"/>  
-    <option name="AUTO_DEL_FILES" value="0"/> 
-  </component>  
-  <component name="PerforceConfiguration"> 
-    <option name="PORT" value="magic:1666"/>  
-    <option name="USER" value=""/>  
-    <option name="PASSWORD" value=""/>  
-    <option name="CLIENT" value=""/>  
-    <option name="TRACE" value="false"/>  
-    <option name="PERFORCE_STATUS" value="true"/>  
-    <option name="CHANGELIST_OPTION" value="false"/>  
-    <option name="SYSTEMROOT" value=""/>  
-    <option name="P4_EXECUTABLE" value="p4"/>  
-    <option name="SHOW_BRANCH_HISTORY" value="false"/>  
-    <option name="GENERATE_COMMENT" value="false"/>  
-    <option name="SYNC_OPTION" value="Sync"/>  
-    <option name="PUT_FOCUS_INTO_COMMENT" value="false"/>  
-    <option name="SHOW_CHECKIN_OPTIONS" value="true"/>  
-    <option name="FORCE_NON_EMPTY_COMMENT" value="true"/>  
-    <option name="LAST_COMMIT_MESSAGE" value=""/>  
-    <option name="SAVE_LAST_COMMIT_MESSAGE" value="true"/>  
-    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8"/>  
-    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="false"/>  
-    <option name="OPTIMIZE_IMPORTS_BEFORE_FILE_COMMIT" value="false"/>  
-    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="false"/>  
-    <option name="REFORMAT_BEFORE_FILE_COMMIT" value="false"/>  
-    <option name="FILE_HISTORY_DIALOG_COMMENTS_SPLITTER_PROPORTION" value="0.8"/>  
-    <option name="FILE_HISTORY_DIALOG_SPLITTER_PROPORTION" value="0.5"/> 
-  </component> 
-</project>
+<project version="4">
+  <component name="AutoImportSettings">
+    <option name="autoReloadType" value="SELECTIVE" />
+  </component>
+  <component name="ChangeListManager">
+    <list default="true" id="b2f4104d-3e59-4c9e-82e2-c463c712c36b" name="更改" comment="">
+      <change afterPath="$PROJECT_DIR$/ams-bean/ams-bean.iml" afterDir="false" />
+      <change afterPath="$PROJECT_DIR$/ams-central/ams-business/ams-business.iml" afterDir="false" />
+      <change afterPath="$PROJECT_DIR$/ams-central/ams-central.iml" afterDir="false" />
+      <change afterPath="$PROJECT_DIR$/ams-central/ams-crm/ams-crm.iml" afterDir="false" />
+      <change afterPath="$PROJECT_DIR$/ams-central/ams-tpi/ams-tpi.iml" afterDir="false" />
+      <change afterPath="$PROJECT_DIR$/ams.iml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams-central/ams-business/src/main/resources/bootstrap-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ams-central/ams-business/src/main/resources/bootstrap-prod.yml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams-central/ams-business/src/main/resources/log4j2.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ams-central/ams-business/src/main/resources/log4j2.xml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams-central/ams-crm/src/main/resources/bootstrap-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ams-central/ams-crm/src/main/resources/bootstrap-prod.yml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams-central/ams-crm/src/main/resources/log4j2.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ams-central/ams-crm/src/main/resources/log4j2.xml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams-central/ams-job/ams-job.iml" beforeDir="false" afterPath="$PROJECT_DIR$/ams-central/ams-job/ams-job.iml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams-central/ams-job/src/main/resources/log4j2.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ams-central/ams-job/src/main/resources/log4j2.xml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams-central/ams-tpi/src/main/resources/bootstrap-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ams-central/ams-tpi/src/main/resources/bootstrap-prod.yml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams-central/ams-tpi/src/main/resources/log4j2.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ams-central/ams-tpi/src/main/resources/log4j2.xml" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams.ipr" beforeDir="false" afterPath="$PROJECT_DIR$/ams.ipr" afterDir="false" />
+      <change beforePath="$PROJECT_DIR$/ams.iws" beforeDir="false" afterPath="$PROJECT_DIR$/ams.iws" afterDir="false" />
+    </list>
+    <option name="SHOW_DIALOG" value="false" />
+    <option name="HIGHLIGHT_CONFLICTS" value="true" />
+    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
+    <option name="LAST_RESOLUTION" value="IGNORE" />
+  </component>
+  <component name="Commander">
+    <leftPanel view="Project" />
+    <rightPanel view="Project" />
+    <splitter proportion="0.5" />
+  </component>
+  <component name="CompilerWorkspaceConfiguration">
+    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
+  </component>
+  <component name="Cvs2Configuration">
+    <option name="ON_FILE_ADDING" value="0" />
+    <option name="ON_FILE_REMOVING" value="0" />
+    <option name="PRUNE_EMPTY_DIRECTORIES" value="true" />
+    <option name="SHOW_UPDATE_OPTIONS" value="true" />
+    <option name="SHOW_ADD_OPTIONS" value="true" />
+    <option name="SHOW_REMOVE_OPTIONS" value="true" />
+    <option name="MERGING_MODE" value="0" />
+    <option name="MERGE_WITH_BRANCH1_NAME" value="HEAD" />
+    <option name="MERGE_WITH_BRANCH2_NAME" value="HEAD" />
+    <option name="RESET_STICKY" value="false" />
+    <option name="CREATE_NEW_DIRECTORIES" value="true" />
+    <option name="DEFAULT_TEXT_FILE_SUBSTITUTION" value="kv" />
+    <option name="PROCESS_UNKNOWN_FILES" value="false" />
+    <option name="PROCESS_DELETED_FILES" value="false" />
+    <option name="SHOW_EDIT_DIALOG" value="true" />
+    <option name="RESERVED_EDIT" value="false" />
+    <option name="FILE_HISTORY_SPLITTER_PROPORTION" value="0.6" />
+    <option name="SHOW_CHECKOUT_OPTIONS" value="true" />
+    <option name="CHECKOUT_DATE_OR_REVISION_SETTINGS">
+      <value>
+        <option name="BRANCH" value="" />
+        <option name="DATE" value="" />
+        <option name="USE_BRANCH" value="false" />
+        <option name="USE_DATE" value="false" />
+      </value>
+    </option>
+    <option name="UPDATE_DATE_OR_REVISION_SETTINGS">
+      <value>
+        <option name="BRANCH" value="" />
+        <option name="DATE" value="" />
+        <option name="USE_BRANCH" value="false" />
+        <option name="USE_DATE" value="false" />
+      </value>
+    </option>
+    <option name="SHOW_CHANGES_REVISION_SETTINGS">
+      <value>
+        <option name="BRANCH" value="" />
+        <option name="DATE" value="" />
+        <option name="USE_BRANCH" value="false" />
+        <option name="USE_DATE" value="false" />
+      </value>
+    </option>
+    <option name="SHOW_OUTPUT" value="false" />
+    <option name="SHOW_FILE_HISTORY_AS_TREE" value="false" />
+    <option name="UPDATE_GROUP_BY_PACKAGES" value="false" />
+    <option name="ADD_WATCH_INDEX" value="0" />
+    <option name="REMOVE_WATCH_INDEX" value="0" />
+    <option name="UPDATE_KEYWORD_SUBSTITUTION" />
+    <option name="MAKE_NEW_FILES_READONLY" value="false" />
+    <option name="SHOW_CORRUPTED_PROJECT_FILES" value="0" />
+    <option name="TAG_AFTER_FILE_COMMIT" value="false" />
+    <option name="TAG_AFTER_FILE_COMMIT_NAME" value="" />
+    <option name="TAG_AFTER_PROJECT_COMMIT" value="false" />
+    <option name="TAG_AFTER_PROJECT_COMMIT_NAME" value="" />
+    <option name="PUT_FOCUS_INTO_COMMENT" value="false" />
+    <option name="SHOW_CHECKIN_OPTIONS" value="true" />
+    <option name="FORCE_NON_EMPTY_COMMENT" value="false" />
+    <option name="LAST_COMMIT_MESSAGE" value="" />
+    <option name="SAVE_LAST_COMMIT_MESSAGE" value="true" />
+    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8" />
+    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="false" />
+    <option name="OPTIMIZE_IMPORTS_BEFORE_FILE_COMMIT" value="false" />
+    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="false" />
+    <option name="REFORMAT_BEFORE_FILE_COMMIT" value="false" />
+    <option name="FILE_HISTORY_DIALOG_COMMENTS_SPLITTER_PROPORTION" value="0.8" />
+    <option name="FILE_HISTORY_DIALOG_SPLITTER_PROPORTION" value="0.5" />
+  </component>
+  <component name="DaemonCodeAnalyzer">
+    <disable_hints />
+  </component>
+  <component name="DebuggerManager">
+    <line_breakpoints converted="true" />
+    <exception_breakpoints converted="true">
+      <breakpoint_any>
+        <option name="NOTIFY_CAUGHT" value="true" />
+        <option name="NOTIFY_UNCAUGHT" value="true" />
+        <option name="ENABLED" value="false" />
+        <option name="SUSPEND_VM" value="true" />
+        <option name="COUNT_FILTER_ENABLED" value="false" />
+        <option name="COUNT_FILTER" value="0" />
+        <option name="CONDITION_ENABLED" value="false" />
+        <option name="CONDITION" />
+        <option name="LOG_ENABLED" value="false" />
+        <option name="LOG_EXPRESSION_ENABLED" value="false" />
+        <option name="LOG_MESSAGE" />
+        <option name="CLASS_FILTERS_ENABLED" value="false" />
+        <option name="INVERSE_CLASS_FILLTERS" value="false" />
+        <option name="SUSPEND_POLICY" value="SuspendAll" />
+      </breakpoint_any>
+    </exception_breakpoints>
+    <field_breakpoints converted="true" />
+    <method_breakpoints converted="true" />
+  </component>
+  <component name="DebuggerSettings">
+    <option name="TRACING_FILTERS_ENABLED" value="true" />
+    <option name="TOSTRING_CLASSES_ENABLED" value="false" />
+    <option name="VALUE_LOOKUP_DELAY" value="700" />
+    <option name="DEBUGGER_TRANSPORT" value="0" />
+    <option name="FORCE_CLASSIC_VM" value="true" />
+    <option name="HIDE_DEBUGGER_ON_PROCESS_TERMINATION" value="false" />
+    <option name="SKIP_SYNTHETIC_METHODS" value="true" />
+    <option name="SKIP_CONSTRUCTORS" value="false" />
+    <option name="STEP_THREAD_SUSPEND_POLICY" value="SuspendThread" />
+    <default_breakpoint_settings>
+      <option name="NOTIFY_CAUGHT" value="true" />
+      <option name="NOTIFY_UNCAUGHT" value="true" />
+      <option name="WATCH_MODIFICATION" value="true" />
+      <option name="WATCH_ACCESS" value="true" />
+      <option name="WATCH_ENTRY" value="true" />
+      <option name="WATCH_EXIT" value="true" />
+      <option name="ENABLED" value="true" />
+      <option name="SUSPEND_VM" value="true" />
+      <option name="COUNT_FILTER_ENABLED" value="false" />
+      <option name="COUNT_FILTER" value="0" />
+      <option name="CONDITION_ENABLED" value="false" />
+      <option name="CONDITION" />
+      <option name="LOG_ENABLED" value="false" />
+      <option name="LOG_EXPRESSION_ENABLED" value="false" />
+      <option name="LOG_MESSAGE" />
+      <option name="CLASS_FILTERS_ENABLED" value="false" />
+      <option name="INVERSE_CLASS_FILLTERS" value="false" />
+      <option name="SUSPEND_POLICY" value="SuspendAll" />
+    </default_breakpoint_settings>
+    <filter>
+      <option name="PATTERN" value="com.sun.*" />
+      <option name="ENABLED" value="true" />
+    </filter>
+    <filter>
+      <option name="PATTERN" value="java.*" />
+      <option name="ENABLED" value="true" />
+    </filter>
+    <filter>
+      <option name="PATTERN" value="javax.*" />
+      <option name="ENABLED" value="true" />
+    </filter>
+    <filter>
+      <option name="PATTERN" value="org.omg.*" />
+      <option name="ENABLED" value="true" />
+    </filter>
+    <filter>
+      <option name="PATTERN" value="sun.*" />
+      <option name="ENABLED" value="true" />
+    </filter>
+    <filter>
+      <option name="PATTERN" value="junit.*" />
+      <option name="ENABLED" value="true" />
+    </filter>
+  </component>
+  <component name="EjbViewSettings">
+    <EjbView showMembers="false" autoscrollToSource="false" />
+  </component>
+  <component name="ErrorTreeViewConfiguration">
+    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false" />
+    <option name="HIDE_WARNINGS" value="false" />
+  </component>
+  <component name="Git.Pull.Settings">
+    <option name="OPTIONS">
+      <set>
+        <option value="REBASE" />
+      </set>
+    </option>
+  </component>
+  <component name="Git.Settings">
+    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
+  </component>
+  <component name="HierarchyBrowserManager">
+    <option name="SHOW_PACKAGES" value="false" />
+    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false" />
+    <option name="SORT_ALPHABETICALLY" value="false" />
+  </component>
+  <component name="InspectionManager">
+    <option name="AUTOSCROLL_TO_SOURCE" value="false" />
+    <option name="SPLITTER_PROPORTION" value="0.5" />
+    <profile name="Default" />
+  </component>
+  <component name="JRebelWorkspace">
+    <option name="jrebelEnabledAutocompile" value="true" />
+    <option name="hasSeenReactiveStreamsDisablingDialog" value="true" />
+  </component>
+  <component name="KubernetesApiPersistence">{}</component>
+  <component name="KubernetesApiProvider">{
+  &quot;isMigrated&quot;: true
+}</component>
+  <component name="LvcsProjectConfiguration">
+    <option name="ADD_LABEL_ON_PROJECT_OPEN" value="true" />
+    <option name="ADD_LABEL_ON_PROJECT_COMPILATION" value="true" />
+    <option name="ADD_LABEL_ON_FILE_PACKAGE_COMPILATION" value="true" />
+    <option name="ADD_LABEL_ON_PROJECT_MAKE" value="true" />
+    <option name="ADD_LABEL_ON_RUNNING" value="true" />
+    <option name="ADD_LABEL_ON_DEBUGGING" value="true" />
+    <option name="ADD_LABEL_ON_UNIT_TEST_PASSED" value="true" />
+    <option name="ADD_LABEL_ON_UNIT_TEST_FAILED" value="true" />
+  </component>
+  <component name="MavenImportPreferences">
+    <option name="generalSettings">
+      <MavenGeneralSettings>
+        <option name="localRepository" value="$USER_HOME$/.m2/repository1" />
+        <option name="userSettingsFile" value="$USER_HOME$/.m2/uonexy-settings_db.xml" />
+      </MavenGeneralSettings>
+    </option>
+  </component>
+  <component name="PerforceConfiguration">
+    <option name="PORT" value="magic:1666" />
+    <option name="USER" value="" />
+    <option name="PASSWORD" value="" />
+    <option name="CLIENT" value="" />
+    <option name="TRACE" value="false" />
+    <option name="PERFORCE_STATUS" value="true" />
+    <option name="CHANGELIST_OPTION" value="false" />
+    <option name="SYSTEMROOT" value="" />
+    <option name="P4_EXECUTABLE" value="p4" />
+    <option name="SHOW_BRANCH_HISTORY" value="false" />
+    <option name="GENERATE_COMMENT" value="false" />
+    <option name="SYNC_OPTION" value="Sync" />
+    <option name="PUT_FOCUS_INTO_COMMENT" value="false" />
+    <option name="SHOW_CHECKIN_OPTIONS" value="true" />
+    <option name="FORCE_NON_EMPTY_COMMENT" value="true" />
+    <option name="LAST_COMMIT_MESSAGE" value="" />
+    <option name="SAVE_LAST_COMMIT_MESSAGE" value="true" />
+    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8" />
+    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="false" />
+    <option name="OPTIMIZE_IMPORTS_BEFORE_FILE_COMMIT" value="false" />
+    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="false" />
+    <option name="REFORMAT_BEFORE_FILE_COMMIT" value="false" />
+    <option name="FILE_HISTORY_DIALOG_COMMENTS_SPLITTER_PROPORTION" value="0.8" />
+    <option name="FILE_HISTORY_DIALOG_SPLITTER_PROPORTION" value="0.5" />
+  </component>
+  <component name="ProjectColorInfo">{
+  &quot;associatedIndex&quot;: 3
+}</component>
+  <component name="ProjectId" id="2ydHb55bj8n8Gnh0DOPasqkWiBa" />
+  <component name="ProjectViewSettings">
+    <navigator currentView="ProjectPane" flattenPackages="false" showMembers="false" showStructure="false" autoscrollToSource="false" splitterProportion="0.5" />
+    <view id="ProjectPane">
+      <expanded_node type="directory" url="file://$PROJECT_DIR$" />
+    </view>
+    <view id="SourcepathPane" />
+    <view id="ClasspathPane" />
+  </component>
+  <component name="ProjectViewState">
+    <option name="autoscrollToSource" value="true" />
+    <option name="showLibraryContents" value="true" />
+  </component>
+  <component name="PropertiesComponent">{
+  &quot;keyToString&quot;: {
+    &quot;Maven.ams-tpi [clean].executor&quot;: &quot;Run&quot;,
+    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
+    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
+    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
+    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
+    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
+    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
+    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
+    &quot;Spring Boot.BusApplication.executor&quot;: &quot;Run&quot;,
+    &quot;Spring Boot.CrmApplication.executor&quot;: &quot;Debug&quot;,
+    &quot;Spring Boot.TpiApplication.executor&quot;: &quot;Debug&quot;,
+    &quot;git-widget-placeholder&quot;: &quot;ljz__dev&quot;,
+    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
+    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/IdeaProjects/rygy-dev/rygym&quot;,
+    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
+    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
+    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
+    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
+    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
+    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
+    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
+    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
+    &quot;应用程序.CrmApplication.executor&quot;: &quot;Debug&quot;,
+    &quot;应用程序.TpiApplication.executor&quot;: &quot;Debug&quot;
+  }
+}</component>
+  <component name="ReactorSettings">
+    <option name="stackFrameCustomizationEnabled" value="false" />
+  </component>
+  <component name="RebelAgentSelection">
+    <selection>jr</selection>
+  </component>
+  <component name="RunDashboard">
+    <option name="configurationTypes">
+      <set>
+        <option value="KtorApplicationConfigurationType" />
+        <option value="MicronautRunConfigurationType" />
+        <option value="QuarkusRunConfigurationType" />
+        <option value="SpringBootApplicationConfigurationType" />
+      </set>
+    </option>
+  </component>
+  <component name="RunManager" selected="Spring Boot.BusApplication">
+    <configuration selected="false" default="true" type="Applet" factoryName="Applet">
+      <module name="" />
+      <option name="MAIN_CLASS_NAME" />
+      <option name="HTML_FILE_NAME" />
+      <option name="HTML_USED" value="false" />
+      <option name="WIDTH" value="400" />
+      <option name="HEIGHT" value="300" />
+      <option name="POLICY_FILE" value="$APPLICATION_HOME_DIR$/bin/appletviewer.policy" />
+      <option name="VM_PARAMETERS" />
+    </configuration>
+    <configuration name="CrmApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
+      <option name="MAIN_CLASS_NAME" value="cn.uone.crm.CrmApplication" />
+      <module name="ams-crm" />
+      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
+      <extension name="coverage">
+        <pattern>
+          <option name="PATTERN" value="cn.uone.crm.*" />
+          <option name="ENABLED" value="true" />
+        </pattern>
+      </extension>
+      <method v="2">
+        <option name="Make" enabled="true" />
+      </method>
+    </configuration>
+    <configuration name="TpiApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
+      <option name="MAIN_CLASS_NAME" value="cn.uone.ams.tpi.TpiApplication" />
+      <module name="ams-tpi" />
+      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
+      <extension name="coverage">
+        <pattern>
+          <option name="PATTERN" value="cn.uone.ams.tpi.*" />
+          <option name="ENABLED" value="true" />
+        </pattern>
+      </extension>
+      <method v="2">
+        <option name="Make" enabled="true" />
+      </method>
+    </configuration>
+    <configuration default="true" type="Application" factoryName="Application">
+      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
+      <method v="2">
+        <option name="Make" enabled="true" />
+      </method>
+    </configuration>
+    <configuration default="true" type="JUnit" factoryName="JUnit">
+      <option name="TEST_OBJECT" value="class" />
+      <option name="VM_PARAMETERS" />
+      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
+      <option name="TEST_SEARCH_SCOPE">
+        <value defaultName="wholeProject" />
+      </option>
+      <method v="2">
+        <option name="Make" enabled="true" />
+      </method>
+    </configuration>
+    <configuration name="BusApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
+      <module name="ams-business" />
+      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.uone.business.BusApplication" />
+      <extension name="coverage">
+        <pattern>
+          <option name="PATTERN" value="cn.uone.business.*" />
+          <option name="ENABLED" value="true" />
+        </pattern>
+      </extension>
+      <method v="2">
+        <option name="Make" enabled="true" />
+      </method>
+    </configuration>
+    <configuration name="CrmApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
+      <module name="ams-crm" />
+      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.uone.crm.CrmApplication" />
+      <extension name="coverage">
+        <pattern>
+          <option name="PATTERN" value="cn.uone.crm.*" />
+          <option name="ENABLED" value="true" />
+        </pattern>
+      </extension>
+      <method v="2">
+        <option name="Make" enabled="true" />
+      </method>
+    </configuration>
+    <configuration name="TpiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
+      <module name="ams-tpi" />
+      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.uone.ams.tpi.TpiApplication" />
+      <extension name="coverage">
+        <pattern>
+          <option name="PATTERN" value="cn.uone.ams.tpi.*" />
+          <option name="ENABLED" value="true" />
+        </pattern>
+      </extension>
+      <method v="2">
+        <option name="Make" enabled="true" />
+      </method>
+    </configuration>
+    <recent_temporary>
+      <list>
+        <item itemvalue="Spring Boot.TpiApplication" />
+        <item itemvalue="Spring Boot.CrmApplication" />
+        <item itemvalue="Spring Boot.BusApplication" />
+        <item itemvalue="应用程序.CrmApplication" />
+        <item itemvalue="应用程序.TpiApplication" />
+      </list>
+    </recent_temporary>
+  </component>
+  <component name="SharedIndexes">
+    <attachedChunks>
+      <set>
+        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
+        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
+      </set>
+    </attachedChunks>
+  </component>
+  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
+  <component name="StarteamConfiguration">
+    <option name="SERVER" value="" />
+    <option name="PORT" value="49201" />
+    <option name="USER" value="" />
+    <option name="PASSWORD" value="" />
+    <option name="PROJECT" value="" />
+    <option name="VIEW" value="" />
+    <option name="ALTERNATIVE_WORKING_PATH" value="" />
+    <option name="PUT_FOCUS_INTO_COMMENT" value="false" />
+    <option name="SHOW_CHECKIN_OPTIONS" value="true" />
+    <option name="LAST_COMMIT_MESSAGE" value="" />
+    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8" />
+  </component>
+  <component name="StructureViewFactory">
+    <option name="SORT_MODE" value="0" />
+    <option name="GROUP_INHERITED" value="true" />
+    <option name="AUTOSCROLL_MODE" value="true" />
+    <option name="SHOW_FIELDS" value="true" />
+    <option name="AUTOSCROLL_FROM_SOURCE" value="false" />
+    <option name="GROUP_GETTERS_AND_SETTERS" value="true" />
+    <option name="SHOW_INHERITED" value="false" />
+    <option name="HIDE_NOT_PUBLIC" value="false" />
+  </component>
+  <component name="SvnConfiguration">
+    <option name="USER" value="" />
+    <option name="PASSWORD" value="" />
+    <option name="AUTO_ADD_FILES" value="0" />
+    <option name="AUTO_DEL_FILES" value="0" />
+  </component>
+  <component name="TaskManager">
+    <task active="true" id="Default" summary="默认任务">
+      <changelist id="b2f4104d-3e59-4c9e-82e2-c463c712c36b" name="更改" comment="" />
+      <created>1750157597562</created>
+      <option name="number" value="Default" />
+      <option name="presentableId" value="Default" />
+      <updated>1750157597562</updated>
+      <workItem from="1750157599227" duration="1409000" />
+    </task>
+    <servers />
+  </component>
+  <component name="TodoView" selected-index="0">
+    <todo-panel id="selected-file">
+      <are-packages-shown value="false" />
+      <flatten-packages value="false" />
+      <is-autoscroll-to-source value="true" />
+    </todo-panel>
+    <todo-panel id="all">
+      <are-packages-shown value="true" />
+      <flatten-packages value="false" />
+      <is-autoscroll-to-source value="true" />
+    </todo-panel>
+  </component>
+  <component name="ToolWindowManager">
+    <frame x="-4" y="-4" width="1032" height="746" extended-state="6" />
+    <editor active="false" />
+    <layout>
+      <window_info id="CVS" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="-1" />
+      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="7" />
+      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="0" />
+      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="1" />
+      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="1" />
+      <window_info id="Messages" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="-1" />
+      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.4" order="6" />
+      <window_info id="Aspects" active="false" anchor="right" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="-1" />
+      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="1" />
+      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="2" />
+      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="2" />
+      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.4" order="4" />
+      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="sliding" type="sliding" visible="false" weight="0.4" order="0" />
+      <window_info id="Web" active="false" anchor="left" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="2" />
+      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="0" />
+      <window_info id="EJB" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="3" />
+      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="5" />
+    </layout>
+  </component>
+  <component name="TypeScriptGeneratedFilesManager">
+    <option name="version" value="3" />
+  </component>
+  <component name="Vcs.Log.Tabs.Properties">
+    <option name="TAB_STATES">
+      <map>
+        <entry key="MAIN">
+          <value>
+            <State />
+          </value>
+        </entry>
+      </map>
+    </option>
+  </component>
+  <component name="VssConfiguration">
+    <CheckoutOptions>
+      <option name="COMMENT" value="" />
+      <option name="DO_NOT_GET_LATEST_VERSION" value="false" />
+      <option name="REPLACE_WRITABLE" value="false" />
+      <option name="RECURSIVE" value="false" />
+    </CheckoutOptions>
+    <CheckinOptions>
+      <option name="COMMENT" value="" />
+      <option name="KEEP_CHECKED_OUT" value="false" />
+      <option name="RECURSIVE" value="false" />
+    </CheckinOptions>
+    <AddOptions>
+      <option name="COMMENT" value="" />
+      <option name="STORE_ONLY_LATEST_VERSION" value="false" />
+      <option name="CHECK_OUT_IMMEDIATELY" value="false" />
+      <option name="FILE_TYPE" value="0" />
+    </AddOptions>
+    <UndocheckoutOptions>
+      <option name="MAKE_WRITABLE" value="false" />
+      <option name="REPLACE_LOCAL_COPY" value="0" />
+      <option name="RECURSIVE" value="false" />
+    </UndocheckoutOptions>
+    <DiffOptions>
+      <option name="IGNORE_WHITE_SPACE" value="false" />
+      <option name="IGNORE_CASE" value="false" />
+    </DiffOptions>
+    <GetOptions>
+      <option name="REPLACE_WRITABLE" value="0" />
+      <option name="MAKE_WRITABLE" value="false" />
+      <option name="RECURSIVE" value="false" />
+    </GetOptions>
+    <option name="CLIENT_PATH" value="" />
+    <option name="SRCSAFEINI_PATH" value="" />
+    <option name="USER_NAME" value="" />
+    <option name="PWD" value="" />
+    <option name="SHOW_CHECKOUT_OPTIONS" value="true" />
+    <option name="SHOW_ADD_OPTIONS" value="true" />
+    <option name="SHOW_UNDOCHECKOUT_OPTIONS" value="true" />
+    <option name="SHOW_DIFF_OPTIONS" value="true" />
+    <option name="SHOW_GET_OPTIONS" value="true" />
+    <option name="USE_EXTERNAL_DIFF" value="false" />
+    <option name="EXTERNAL_DIFF_PATH" value="" />
+    <option name="REUSE_LAST_COMMENT" value="false" />
+    <option name="PUT_FOCUS_INTO_COMMENT" value="false" />
+    <option name="SHOW_CHECKIN_OPTIONS" value="true" />
+    <option name="LAST_COMMIT_MESSAGE" value="" />
+    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8" />
+  </component>
+  <component name="WebViewSettings">
+    <webview flattenPackages="false" showMembers="false" autoscrollToSource="false" />
+  </component>
+</project>
\ No newline at end of file
Index: rygym/ams-central/ams-crm/src/main/resources/bootstrap-prod.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-crm/src/main/resources/bootstrap-prod.yml b/rygym/ams-central/ams-crm/src/main/resources/bootstrap-prod.yml
--- a/rygym/ams-central/ams-crm/src/main/resources/bootstrap-prod.yml	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams-central/ams-crm/src/main/resources/bootstrap-prod.yml	(date 1750158424912)
@@ -2,7 +2,7 @@
   cloud:
     nacos:
       discovery:
-        server-addr: http://127.0.0.1:8852
+        server-addr: http://127.0.0.1:8848
         namespace: f9cabb73-130b-4a70-b059-34c263ceb01e
       config:
         file-extension: yml
Index: rygym/ams-central/ams-tpi/src/main/resources/log4j2.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-tpi/src/main/resources/log4j2.xml b/rygym/ams-central/ams-tpi/src/main/resources/log4j2.xml
--- a/rygym/ams-central/ams-tpi/src/main/resources/log4j2.xml	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams-central/ams-tpi/src/main/resources/log4j2.xml	(date 1750159008915)
@@ -4,7 +4,7 @@
         <!-- 文件输出格式 -->
         <property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} |-%-5level [%thread] %c [%L] -| %msg%n</property>
 
-        <Property name="LOG_FILE_PATH">/logs</Property>
+        <Property name="LOG_FILE_PATH">/Users/<USER>/IdeaProjects/rygy-dev/rygym/logs</Property>
     </properties>
 
     <appenders>
Index: rygym/ams-central/ams-crm/src/main/resources/log4j2.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-crm/src/main/resources/log4j2.xml b/rygym/ams-central/ams-crm/src/main/resources/log4j2.xml
--- a/rygym/ams-central/ams-crm/src/main/resources/log4j2.xml	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams-central/ams-crm/src/main/resources/log4j2.xml	(date 1750159008922)
@@ -4,7 +4,7 @@
         <!-- 文件输出格式 -->
         <property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} |-%-5level [%thread] %c [%L] -| %msg%n</property>
 
-        <Property name="LOG_FILE_PATH">/logs</Property>
+        <Property name="LOG_FILE_PATH">/Users/<USER>/IdeaProjects/rygy-dev/rygym/logs</Property>
     </properties>
 
     <appenders>
Index: rygym/ams-central/ams-tpi/src/main/resources/bootstrap-prod.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-tpi/src/main/resources/bootstrap-prod.yml b/rygym/ams-central/ams-tpi/src/main/resources/bootstrap-prod.yml
--- a/rygym/ams-central/ams-tpi/src/main/resources/bootstrap-prod.yml	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams-central/ams-tpi/src/main/resources/bootstrap-prod.yml	(date 1750158539796)
@@ -2,7 +2,7 @@
   cloud:
     nacos:
       discovery:
-        server-addr: http://127.0.0.1:8852
+        server-addr: http://127.0.0.1:8848
         namespace: f9cabb73-130b-4a70-b059-34c263ceb01e
       config:
         file-extension: yml
Index: rygym/ams-central/ams-business/src/main/resources/bootstrap-prod.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-business/src/main/resources/bootstrap-prod.yml b/rygym/ams-central/ams-business/src/main/resources/bootstrap-prod.yml
--- a/rygym/ams-central/ams-business/src/main/resources/bootstrap-prod.yml	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams-central/ams-business/src/main/resources/bootstrap-prod.yml	(date 1750158552751)
@@ -2,7 +2,7 @@
   cloud:
     nacos:
       discovery:
-        server-addr: http://127.0.0.1:8852
+        server-addr: http://127.0.0.1:8848
         namespace: f9cabb73-130b-4a70-b059-34c263ceb01e
       config:
         file-extension: yml
Index: rygym/ams-central/ams-job/ams-job.iml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-job/ams-job.iml b/rygym/ams-central/ams-job/ams-job.iml
--- a/rygym/ams-central/ams-job/ams-job.iml	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams-central/ams-job/ams-job.iml	(date 1750159385617)
@@ -1,6 +1,28 @@
 <?xml version="1.0" encoding="UTF-8"?>
-<module version="4">
+<module external.linked.project.id="$MODULE_DIR$/pom.xml" external.system.module.type="SINGLE_MODULE" external.system.module.version="223-2" org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
+  <component name="AdditionalModuleElements">
+    <content url="file://$MODULE_DIR$" dumb="true">
+      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
+      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" type="java-test-resource" />
+      <sourceFolder url="file://$MODULE_DIR$/target/generated-sources/annotations" isTestSource="false" generated="true" />
+      <excludeFolder url="file://$MODULE_DIR$/target" />
+    </content>
+  </component>
   <component name="FacetManager">
+    <facet type="JRebel" name="JRebel">
+      <configuration>
+        <option name="ideModuleStorage">
+          <map>
+            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
+            <entry key="jrebelEnabled" value="true" />
+            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5hbXMtam9iPC9pZD48Y2xhc3NwYXRoPjxkaXIgbmFtZT0iL1VzZXJzL3hpYW9sdS9JZGVhUHJvamVjdHMvcnlneS1kZXYvcnlneW0vYW1zLWNlbnRyYWwvYW1zLWpvYi90YXJnZXQvY2xhc3NlcyI+PC9kaXI+PC9jbGFzc3BhdGg+PC9hcHBsaWNhdGlvbj4=" />
+          </map>
+        </option>
+        <option name="version" value="6" />
+      </configuration>
+    </facet>
     <facet type="Spring" name="Spring">
       <configuration />
     </facet>
Index: rygym/ams-central/ams-business/src/main/resources/log4j2.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-business/src/main/resources/log4j2.xml b/rygym/ams-central/ams-business/src/main/resources/log4j2.xml
--- a/rygym/ams-central/ams-business/src/main/resources/log4j2.xml	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams-central/ams-business/src/main/resources/log4j2.xml	(date 1750159008920)
@@ -4,7 +4,7 @@
         <!-- 文件输出格式 -->
         <property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} |-%-5level [%thread] %c [%L] -| %msg%n</property>
 
-        <Property name="LOG_FILE_PATH">/logs</Property>
+        <Property name="LOG_FILE_PATH">/Users/<USER>/IdeaProjects/rygy-dev/rygym/logs</Property>
     </properties>
 
     <appenders>
Index: rygym/ams-central/ams-crm/ams-crm.iml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-crm/ams-crm.iml b/rygym/ams-central/ams-crm/ams-crm.iml
new file mode 100644
--- /dev/null	(date 1750159385621)
+++ b/rygym/ams-central/ams-crm/ams-crm.iml	(date 1750159385621)
@@ -0,0 +1,27 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<module external.linked.project.id="$MODULE_DIR$/pom.xml" external.system.module.type="SINGLE_MODULE" external.system.module.version="223-2" org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
+  <component name="AdditionalModuleElements">
+    <content url="file://$MODULE_DIR$" dumb="true">
+      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
+      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" type="java-test-resource" />
+      <sourceFolder url="file://$MODULE_DIR$/target/generated-sources/annotations" isTestSource="false" generated="true" />
+      <excludeFolder url="file://$MODULE_DIR$/target" />
+    </content>
+  </component>
+  <component name="FacetManager">
+    <facet type="JRebel" name="JRebel">
+      <configuration>
+        <option name="ideModuleStorage">
+          <map>
+            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
+            <entry key="jrebelEnabled" value="true" />
+            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5hbXMtY3JtPC9pZD48Y2xhc3NwYXRoPjxkaXIgbmFtZT0iL1VzZXJzL3hpYW9sdS9JZGVhUHJvamVjdHMvcnlneS1kZXYvcnlneW0vYW1zLWNlbnRyYWwvYW1zLWNybS90YXJnZXQvY2xhc3NlcyI+PC9kaXI+PC9jbGFzc3BhdGg+PC9hcHBsaWNhdGlvbj4=" />
+          </map>
+        </option>
+        <option name="version" value="6" />
+      </configuration>
+    </facet>
+  </component>
+</module>
\ No newline at end of file
Index: rygym/ams.iml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams.iml b/rygym/ams.iml
new file mode 100644
--- /dev/null	(date 1750159385624)
+++ b/rygym/ams.iml	(date 1750159385624)
@@ -0,0 +1,22 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<module external.linked.project.id="$MODULE_DIR$/pom.xml" external.system.module.type="AGGREGATOR" external.system.module.version="223-2" org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
+  <component name="AdditionalModuleElements">
+    <content url="file://$MODULE_DIR$" dumb="true">
+      <excludeFolder url="file://$MODULE_DIR$/target" />
+    </content>
+  </component>
+  <component name="FacetManager">
+    <facet type="JRebel" name="JRebel">
+      <configuration>
+        <option name="ideModuleStorage">
+          <map>
+            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
+            <entry key="jrebelEnabled" value="true" />
+            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5hbXM8L2lkPjwvYXBwbGljYXRpb24+" />
+          </map>
+        </option>
+        <option name="version" value="6" />
+      </configuration>
+    </facet>
+  </component>
+</module>
\ No newline at end of file
Index: rygym/ams-central/ams-tpi/ams-tpi.iml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-tpi/ams-tpi.iml b/rygym/ams-central/ams-tpi/ams-tpi.iml
new file mode 100644
--- /dev/null	(date 1750159385614)
+++ b/rygym/ams-central/ams-tpi/ams-tpi.iml	(date 1750159385614)
@@ -0,0 +1,26 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<module external.linked.project.id="$MODULE_DIR$/pom.xml" external.system.module.type="SINGLE_MODULE" external.system.module.version="223-2" org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
+  <component name="AdditionalModuleElements">
+    <content url="file://$MODULE_DIR$" dumb="true">
+      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
+      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" type="java-test-resource" />
+      <excludeFolder url="file://$MODULE_DIR$/target" />
+    </content>
+  </component>
+  <component name="FacetManager">
+    <facet type="JRebel" name="JRebel">
+      <configuration>
+        <option name="ideModuleStorage">
+          <map>
+            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
+            <entry key="jrebelEnabled" value="true" />
+            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5hbXMtdHBpPC9pZD48Y2xhc3NwYXRoPjxkaXIgbmFtZT0iL1VzZXJzL3hpYW9sdS9JZGVhUHJvamVjdHMvcnlneS1kZXYvcnlneW0vYW1zLWNlbnRyYWwvYW1zLXRwaS90YXJnZXQvY2xhc3NlcyI+PC9kaXI+PC9jbGFzc3BhdGg+PC9hcHBsaWNhdGlvbj4=" />
+          </map>
+        </option>
+        <option name="version" value="6" />
+      </configuration>
+    </facet>
+  </component>
+</module>
\ No newline at end of file
Index: rygym/ams-central/ams-central.iml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-central.iml b/rygym/ams-central/ams-central.iml
new file mode 100644
--- /dev/null	(date 1750159385623)
+++ b/rygym/ams-central/ams-central.iml	(date 1750159385623)
@@ -0,0 +1,22 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<module external.linked.project.id="$MODULE_DIR$/pom.xml" external.system.module.type="AGGREGATOR" external.system.module.version="223-2" org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
+  <component name="AdditionalModuleElements">
+    <content url="file://$MODULE_DIR$" dumb="true">
+      <excludeFolder url="file://$MODULE_DIR$/target" />
+    </content>
+  </component>
+  <component name="FacetManager">
+    <facet type="JRebel" name="JRebel">
+      <configuration>
+        <option name="ideModuleStorage">
+          <map>
+            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
+            <entry key="jrebelEnabled" value="true" />
+            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5hbXMtY2VudHJhbDwvaWQ+PC9hcHBsaWNhdGlvbj4=" />
+          </map>
+        </option>
+        <option name="version" value="6" />
+      </configuration>
+    </facet>
+  </component>
+</module>
\ No newline at end of file
Index: rygym/ams-central/ams-business/ams-business.iml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-business/ams-business.iml b/rygym/ams-central/ams-business/ams-business.iml
new file mode 100644
--- /dev/null	(date 1750159385619)
+++ b/rygym/ams-central/ams-business/ams-business.iml	(date 1750159385619)
@@ -0,0 +1,28 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<module external.linked.project.id="$MODULE_DIR$/pom.xml" external.system.module.type="SINGLE_MODULE" external.system.module.version="223-2" org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
+  <component name="AdditionalModuleElements">
+    <content url="file://$MODULE_DIR$" dumb="true">
+      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
+      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" type="java-test-resource" />
+      <sourceFolder url="file://$MODULE_DIR$/target/generated-sources/annotations" isTestSource="false" generated="true" />
+      <sourceFolder url="file://$MODULE_DIR$/target/generated-test-sources/test-annotations" isTestSource="true" generated="true" />
+      <excludeFolder url="file://$MODULE_DIR$/target" />
+    </content>
+  </component>
+  <component name="FacetManager">
+    <facet type="JRebel" name="JRebel">
+      <configuration>
+        <option name="ideModuleStorage">
+          <map>
+            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
+            <entry key="jrebelEnabled" value="true" />
+            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5hbXMtYnVzaW5lc3M8L2lkPjxjbGFzc3BhdGg+PGRpciBuYW1lPSIvVXNlcnMveGlhb2x1L0lkZWFQcm9qZWN0cy9yeWd5LWRldi9yeWd5bS9hbXMtY2VudHJhbC9hbXMtYnVzaW5lc3MvdGFyZ2V0L2NsYXNzZXMiPjwvZGlyPjwvY2xhc3NwYXRoPjwvYXBwbGljYXRpb24+" />
+          </map>
+        </option>
+        <option name="version" value="6" />
+      </configuration>
+    </facet>
+  </component>
+</module>
\ No newline at end of file
Index: rygym/ams-central/ams-job/src/main/resources/log4j2.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-central/ams-job/src/main/resources/log4j2.xml b/rygym/ams-central/ams-job/src/main/resources/log4j2.xml
--- a/rygym/ams-central/ams-job/src/main/resources/log4j2.xml	(revision 82b2d660ec1decbdbfd81fcf07e391593325ca03)
+++ b/rygym/ams-central/ams-job/src/main/resources/log4j2.xml	(date 1750159008924)
@@ -4,7 +4,7 @@
         <!-- 文件输出格式 -->
         <property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} |-%-5level [%thread] %c [%L] -| %msg%n</property>
 
-        <Property name="LOG_FILE_PATH">/logs</Property>
+        <Property name="LOG_FILE_PATH">/Users/<USER>/IdeaProjects/rygy-dev/rygym/logs</Property>
     </properties>
 
     <appenders>
Index: rygym/ams-bean/ams-bean.iml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/rygym/ams-bean/ams-bean.iml b/rygym/ams-bean/ams-bean.iml
new file mode 100644
--- /dev/null	(date 1750159385618)
+++ b/rygym/ams-bean/ams-bean.iml	(date 1750159385618)
@@ -0,0 +1,27 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<module external.linked.project.id="$MODULE_DIR$/pom.xml" external.system.module.type="SINGLE_MODULE" external.system.module.version="223-2" org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
+  <component name="AdditionalModuleElements">
+    <content url="file://$MODULE_DIR$" dumb="true">
+      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
+      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
+      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" type="java-test-resource" />
+      <sourceFolder url="file://$MODULE_DIR$/target/generated-sources/annotations" isTestSource="false" generated="true" />
+      <excludeFolder url="file://$MODULE_DIR$/target" />
+    </content>
+  </component>
+  <component name="FacetManager">
+    <facet type="JRebel" name="JRebel">
+      <configuration>
+        <option name="ideModuleStorage">
+          <map>
+            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
+            <entry key="jrebelEnabled" value="true" />
+            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5hbXMtYmVhbjwvaWQ+PGNsYXNzcGF0aD48ZGlyIG5hbWU9Ii9Vc2Vycy94aWFvbHUvSWRlYVByb2plY3RzL3J5Z3ktZGV2L3J5Z3ltL2Ftcy1iZWFuL3RhcmdldC9jbGFzc2VzIj48L2Rpcj48L2NsYXNzcGF0aD48L2FwcGxpY2F0aW9uPg==" />
+          </map>
+        </option>
+        <option name="version" value="6" />
+      </configuration>
+    </facet>
+  </component>
+</module>
\ No newline at end of file
