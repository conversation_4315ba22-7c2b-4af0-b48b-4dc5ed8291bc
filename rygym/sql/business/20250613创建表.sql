--20240604添加活动报名表
CREATE TABLE `t_act_enroll` (
    `id` varchar(32) NOT NULL,
    `activity_id` varchar(32) DEFAULT NULL COMMENT '活动id',
    `name` varchar(50) DEFAULT NULL COMMENT '姓名',
    `tel` varchar(11) DEFAULT NULL COMMENT '手机',
    `extra_info` text COMMENT '额外信息 json格式',
    `create_by` varchar(32) DEFAULT NULL,
    `create_date` datetime DEFAULT NULL,
    `update_by` varchar(32) DEFAULT NULL,
    `update_date` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4

--20240606
--创建合同价格策略关联表
CREATE TABLE `t_cont_contract_price_rel`  (
  `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键id',
  `contract_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '合同id',
  `price_strategy_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '价格策略id',
  `project_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT ' 项目 门店id',
  `status` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '1 生效 0 失效',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_date` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC COMMENT='合同价格策略关联表';

CREATE TABLE `t_price_month_detail`  (
     `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键id',
     `price_strategy_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '价格策略id',
     `project_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT ' 项目 门店id',
     `month_sequ` int(8)  NULL DEFAULT NULL COMMENT '月份顺序号',
     `fixed_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '优惠金额',
     `status` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '1 生效 0 失效',
     `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
     `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
     `update_date` datetime NULL DEFAULT NULL COMMENT '更新时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC COMMENT='价格策略指定月份明细表';

--创建租金主账单预览表
CREATE TABLE `t_bil_order_preview` (
   `id` varchar(32) NOT NULL,
   `is_sync` tinyint(1) DEFAULT '0',
   `ccb_bill_id` varchar(50) DEFAULT NULL,
   `code` varchar(32) DEFAULT NULL COMMENT '编号',
   `merge_code` varchar(1000) DEFAULT NULL COMMENT '合单编号',
   `source_id` varchar(32) DEFAULT NULL,
   `contract_id` varchar(32) DEFAULT NULL,
   `discount_log_id` varchar(32) DEFAULT NULL COMMENT '优惠卷id',
   `payable_payment` decimal(10,2) DEFAULT NULL,
   `actual_payment` decimal(10,2) DEFAULT NULL COMMENT '支付金额',
   `payment` decimal(10,2) DEFAULT NULL COMMENT '账单金额',
   `trade_code` varchar(255) DEFAULT NULL COMMENT '交易回调码',
   `pay_way` varchar(10) DEFAULT NULL COMMENT '支付方式',
   `account_id` varchar(32) DEFAULT NULL,
   `pay_unit` varchar(100) DEFAULT NULL,
   `pay_state` char(10) DEFAULT NULL,
   `pay_time` date DEFAULT NULL COMMENT '支付时间',
   `order_type` varchar(10) DEFAULT NULL COMMENT '账单类型',
   `payer_id` varchar(32) DEFAULT NULL,
   `data_from` varchar(32) DEFAULT '2' COMMENT '数据来源',
   `invoice_type` char(1) DEFAULT '0' COMMENT '开票类型',
   `invoice_state` char(1) DEFAULT '0' COMMENT '开票状态',
   `invoice_payment` decimal(10,2) DEFAULT NULL COMMENT '已开票金额',
   `approval_state` char(1) DEFAULT NULL COMMENT '审批状态',
   `is_push` tinyint(1) DEFAULT '0' COMMENT '是否推送',
   `push_time` date DEFAULT NULL COMMENT '推送时间',
   `payable_time` date DEFAULT NULL COMMENT '应付时间',
   `clean_time` datetime DEFAULT NULL COMMENT '清算时间',
   `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
   `is_discard` tinyint(1) DEFAULT '0' COMMENT '是否移入回收站',
   `is_first` tinyint(1) DEFAULT '0' COMMENT '是否首期账单',
   `is_initial` tinyint(1) DEFAULT '0' COMMENT '是否是最后一期账单 0不是  1是',
   `remark` text COMMENT '备注',
   `transfer_state` char(1) DEFAULT '0' COMMENT '转账状态',
   `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
   `create_date` datetime DEFAULT NULL COMMENT '创建时间',
   `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
   `update_date` datetime DEFAULT NULL COMMENT '更新时间',
   `apply_inv_time` datetime DEFAULT NULL COMMENT '申请开票时间',
   `transfer_mode` char(10) DEFAULT '0' COMMENT '转账模式',
   `report_date` datetime DEFAULT NULL COMMENT '退租报告生成时间',
   `intention` varchar(8) DEFAULT NULL,
   `bills_code` varchar(40) DEFAULT NULL COMMENT ' 票据号码',
   `bills_state` varchar(2) DEFAULT '0' COMMENT '票据状态 0 有效  1 无效',
   `state_owned_assets_push` varchar(1) DEFAULT '0' COMMENT '国资是否推送 1已推送 0未推送',
   `demand_payment_count` int(11) DEFAULT NULL COMMENT '催款次数',
   `old_renter_id` varchar(32) DEFAULT NULL COMMENT '原租客id',
   `start_date` date DEFAULT NULL COMMENT '开始日期',
   `end_date` date DEFAULT NULL COMMENT '结束日期',
   PRIMARY KEY (`id`) USING BTREE,
   KEY `FK_Reference_56` (`discount_log_id`) USING BTREE,
   KEY `FK_Reference_57` (`payer_id`) USING BTREE,
   KEY `FK_Reference_58` (`contract_id`) USING BTREE,
   KEY `FK_Reference_59` (`source_id`) USING BTREE,
   KEY `FK_Reference_510` (`order_type`) USING BTREE,
   KEY `FK_Reference_511` (`pay_state`) USING BTREE,
   KEY `FK_Reference_512` (`payable_time`) USING BTREE,
   KEY `FK_Reference_513` (`create_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='租金主账单预览表'


--创建租金子账单预览表
CREATE TABLE `t_bil_order_item_preview` (
    `id` VARCHAR(32) NOT NULL,
    `order_id` VARCHAR(32) DEFAULT NULL COMMENT '主账单预览表id',
    `code` VARCHAR(20) DEFAULT NULL COMMENT '编号',
    `payment` DECIMAL(10,2) DEFAULT NULL COMMENT '账单金额',
    `fix_before` DECIMAL(10,2) DEFAULT NULL,
    `fix_after` DECIMAL(10,2) DEFAULT NULL,
    `order_item_type` VARCHAR(10) DEFAULT NULL COMMENT '账单类型',
    `start_time` DATETIME(3) DEFAULT NULL COMMENT '开始时间',
    `end_time` DATETIME(3) DEFAULT NULL COMMENT '结束时间',
    `price` DECIMAL(10,2) DEFAULT NULL COMMENT '单价',
    `num` DECIMAL(10,2) DEFAULT NULL COMMENT '数量',
    `start_read` DECIMAL(10,2) DEFAULT NULL COMMENT '起始读数',
    `end_read` DECIMAL(10,2) DEFAULT NULL COMMENT '截止读数',
    `device_id` VARCHAR(32) DEFAULT NULL COMMENT '设备ID',
    `transfer_state` CHAR(1) DEFAULT '0' COMMENT '转账状态',
    `arrive_code` VARCHAR(32) DEFAULT NULL COMMENT '对账编码',
    `merchant_id` VARCHAR(32) DEFAULT NULL COMMENT '子商户号',
    `arrive_time` DATETIME DEFAULT NULL COMMENT '到账日期',
    `bank_serial_code` VARCHAR(255) DEFAULT NULL COMMENT '银行流水号',
    `create_by` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
    `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
    `update_by` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
    `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
    `invoice_id` VARCHAR(32) DEFAULT '' COMMENT '开票id',
    `state_owned_assets_push` VARCHAR(1) DEFAULT '0' COMMENT '国资是否推送 1已推送 0未推送',
    `price_strategy_id` VARCHAR(32) DEFAULT NULL COMMENT '价格策略id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `FK_Reference_55` (`order_id`) USING BTREE,
    CONSTRAINT `t_bil_order_item_pre_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `t_bil_order_preview` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='租金子账单预览表'

--********
CREATE TABLE `sys_dict_data` (
     `id` varchar(32) NOT NULL COMMENT '字典主键',
     `dict_sort` int(4) DEFAULT '0' COMMENT '字典排序',
     `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
     `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
     `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
     `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
     `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
     `update_date` datetime DEFAULT NULL COMMENT '更新时间',
     `remark` varchar(500) DEFAULT NULL COMMENT '备注',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典数据表';

CREATE TABLE `sys_dict_type` (
     `id` varchar(32) NOT NULL COMMENT '字典类型主键',
     `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
     `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
     `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
     `update_date` datetime DEFAULT NULL COMMENT '更新时间',
     `remark` varchar(500) DEFAULT NULL COMMENT '备注',
     PRIMARY KEY (`id`),
     UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典类型表';

--20240729
CREATE TABLE `t_bil_carry_over` (
    `id` varchar(32) NOT NULL COMMENT 'ID',
    `order_item_id` varchar(32) DEFAULT NULL COMMENT '账单明细id',
    `order_id` varchar(32) DEFAULT NULL COMMENT '账单id',
    `from_order_id` varchar(32) DEFAULT NULL COMMENT '结转账单id',
    `payment` decimal(10,2) DEFAULT NULL COMMENT '结转金额',
    `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(32) DEFAULT NULL,
    `create_date` datetime DEFAULT NULL,
    `update_by` varchar(32) DEFAULT NULL,
    `update_date` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账单结转表';

--20240726
CREATE TABLE `t_urge_pay_record`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'id',
  `order_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单ID',
  `order_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单编号',
  `renter_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签约人',
  `renter_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签约人ID',
  `contract_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
  `contract_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同ID',
  `operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '催缴人',
  `operator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '催缴人id',
  `payment` decimal(10, 2) NULL DEFAULT NULL COMMENT '账单金额',
  `source_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房源名称',
  `source_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房源id',
  `info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '催缴详情',
  `push_date` date NULL DEFAULT NULL COMMENT '推送日期',
  `urge_date` datetime(0) NULL DEFAULT NULL COMMENT '催缴时间',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_date` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

--20240802
DROP TABLE IF EXISTS `t_project_cooperation`;
CREATE TABLE `t_project_cooperation`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'id',
  `title` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布状态（1未发布，2已发布）',
  `publisher_time` datetime(0) NULL DEFAULT NULL COMMENT '发布时间',
  `publisher` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布人',
  `publisher_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布人ID',
  `project_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目id',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_date` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

--base库
CREATE TABLE `t_expense_config` (
    `id` varchar(32) NOT NULL COMMENT 'ID',
    `expense_project_id` varchar(32) DEFAULT NULL COMMENT '开户项目id',
    `expense_project_code` varchar(32) DEFAULT NULL COMMENT '开户项目编码',
    `sys_company_id` varchar(32) DEFAULT NULL COMMENT '开户公司id',
    `config_type` varchar(32) DEFAULT NULL COMMENT '配置类型',
    `config_name` varchar(100) DEFAULT NULL COMMENT '配置名称',
    `config_content` text COMMENT '配置内容 json',
    `remark` varchar(500) DEFAULT NULL,
    `create_by` varchar(32) DEFAULT NULL,
    `create_date` datetime DEFAULT NULL,
    `update_by` varchar(32) DEFAULT NULL,
    `update_date` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开户项目接口配置表'

--20240814 business
CREATE TABLE `t_temporary_reside` (
  `id` varchar(32) NOT NULL COMMENT 'id',
  `project_id` varchar(32) DEFAULT NULL COMMENT '项目id',
  `source_id` varchar(32) DEFAULT NULL COMMENT '房源id',
  `state` varchar(10) DEFAULT NULL COMMENT '审核状态（1待审核 2已通过 3未通过)',
  `start_time` date DEFAULT NULL COMMENT '开始时间',
  `end_time` date DEFAULT NULL COMMENT '结束时间',
  `renter_id` varchar(32) DEFAULT NULL COMMENT '租客id',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='临时用房表';

--20240817
--创建表t_res_source_configure_audit
--价格配置审批记录表
CREATE TABLE `t_res_source_configure_audit`  (
     `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
     `source_configure_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房源价格配置id',
     `source_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房源地址',
     `source_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房源id',
     `project_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目id',
     `low_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '底价',
     `year_increase` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '年增幅',
     `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '对外表价',
     `approval_state` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '审核状态(0待审核，1已通过，2未通过)',
     `deposit` decimal(10, 2) NULL DEFAULT NULL COMMENT '定金',
     `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
     `year_num` decimal(10, 0) NULL DEFAULT 0 COMMENT '增幅间隔年数',
     `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
     `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
     `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
     `update_date` datetime NULL DEFAULT NULL COMMENT '更新时间',
     PRIMARY KEY (`id`) USING BTREE,
     INDEX `FK_source_configure_id`(`source_configure_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '价格配置审批记录表' ROW_FORMAT = DYNAMIC;

--20240923
CREATE TABLE `t_cont_contract_file_rel` (
        `id` VARCHAR(32) NOT NULL COMMENT '主键id',
        `name` VARCHAR(200) DEFAULT NULL COMMENT '文件名称',
        `contract_id` VARCHAR(32) DEFAULT NULL COMMENT '合同id',
        `file_type` VARCHAR(2) DEFAULT NULL COMMENT '文件类型',
        `project_id` VARCHAR(32) DEFAULT NULL COMMENT '项目 门店id',
        `state` VARCHAR(2) DEFAULT NULL COMMENT '1待审核 2已生效',
        `create_by` VARCHAR(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人',
        `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
        `update_by` VARCHAR(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新人',
        `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
        PRIMARY KEY (`id`) USING BTREE
) ENGINE=INNODB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='合同审批文件关联表'

--20240925
CREATE TABLE `t_fixed_rfid_batch` (
  `id` varchar(32) NOT NULL COMMENT '主键id',
  `batch` varchar(100) DEFAULT NULL COMMENT '批次',
  `quantity` int(100) DEFAULT NULL COMMENT '数量',
  `use_info` varchar(100) DEFAULT NULL COMMENT '使用情况',
  `label_norms` varchar(100) DEFAULT NULL COMMENT '标签规格',
  `label_antenna` varchar(100) DEFAULT NULL COMMENT '天线规格',
  `label_quality` varchar(100) DEFAULT NULL COMMENT '材质',
  `type` varchar(10) DEFAULT NULL COMMENT '类型',
  `remark` text CHARACTER SET utf8 COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `company_id` varchar(32) DEFAULT NULL COMMENT '公司id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='RFID批次'

--20241010
CREATE TABLE `t_cont_contract_gjjtq` (
 `id` varchar(32) NOT NULL COMMENT '主键id',
 `contract_id` varchar(32) DEFAULT NULL COMMENT '合同id',
 `paper_code` varchar(50) DEFAULT NULL COMMENT '纸质合同编号',
 `pay_type` char(2) DEFAULT NULL COMMENT '缴费方式',
 `order_id` varchar(32) DEFAULT NULL COMMENT '租金账单id',
 `order_code` varchar(32) DEFAULT NULL COMMENT '账单编号',
 `order_payable_payment` decimal(10,2) DEFAULT '0.00' COMMENT '账单应付金额',
 `start_date` date DEFAULT NULL COMMENT '开始时间',
 `end_date` date DEFAULT NULL COMMENT '结束时间',
 `state` varchar(2) DEFAULT '0' COMMENT '0待抵扣 1已抵扣',
 `zjhm` varchar(32) DEFAULT NULL COMMENT '证件号码',
 `xingming` varchar(32) DEFAULT NULL COMMENT '姓名',
 `payment` decimal(10,2) DEFAULT '0.00' COMMENT '抵扣金额',
 `trs_date` varchar(8) DEFAULT NULL COMMENT '交易日期',
 `trs_time` varchar(6) DEFAULT NULL COMMENT '交易时间',
 `ywlsh` varchar(20) DEFAULT NULL COMMENT '业务流水号',
 `yhzjlsh` varchar(100) DEFAULT NULL COMMENT '银行主机流水号',
 `create_by` varchar(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人',
 `create_date` datetime DEFAULT NULL COMMENT '创建时间',
 `update_by` varchar(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新人',
 `update_date` datetime DEFAULT NULL COMMENT '更新时间',
 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公积金授权抵扣账单关联表';

--20241010
CREATE TABLE `t_cont_contract_gjjcx` (
 `id` VARCHAR(32) NOT NULL COMMENT '主键id',
 `contract_id` VARCHAR(32) DEFAULT NULL COMMENT '合同id',
 `zjhm` VARCHAR(32) DEFAULT NULL COMMENT '证件号码',
 `xingming` VARCHAR(32) DEFAULT NULL COMMENT '姓名',
 `trs_date` VARCHAR(8) DEFAULT NULL COMMENT '交易日期',
 `trs_time` VARCHAR(6) DEFAULT NULL COMMENT '交易时间',
 `zhkhyhmc` VARCHAR(255) DEFAULT NULL COMMENT '账户开户银行名称',
 `dwmc` VARCHAR(255) DEFAULT NULL COMMENT '单位名称',
 `grzh` VARCHAR(20) DEFAULT NULL COMMENT '个人账号',
 `grzhye` VARCHAR(32) DEFAULT NULL COMMENT '个人账户余额',
 `grzhzt` VARCHAR(32) DEFAULT NULL COMMENT '个人账户状态',
 `byzfkzqje` DECIMAL(10,2) DEFAULT NULL COMMENT '本月租房可支取金额',
 `byzfyzfje` DECIMAL(10,2) DEFAULT NULL COMMENT '本月租房已支取金额',
 `tqny` VARCHAR(6) DEFAULT NULL COMMENT '提取年月',
 `tqlx` VARCHAR(2) DEFAULT NULL COMMENT '提取类型',
 `create_by` VARCHAR(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人',
 `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
 `update_by` VARCHAR(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新人',
 `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
 PRIMARY KEY (`id`) USING BTREE
) ENGINE=INNODB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公积金查询表';

--20241105
CREATE TABLE `t_cosmic_receivable` (
   `id` varchar(32) NOT NULL COMMENT '主键id',
   `xmgd_sourcesystem` varchar(32) DEFAULT NULL COMMENT '来源系统（业务系统标识）',
   `xmgd_sourcetype` varchar(10) DEFAULT NULL COMMENT '来源单据类型（业务系统单据类型）',
   `xmgd_sourcenumber` varchar(100) DEFAULT NULL COMMENT '来源单据编码（业务系统单据id）',
   `org_number` varchar(32) DEFAULT NULL COMMENT '结算组织（业务发生的组织，传组织guid或组织id）',
   `billtype_number` varchar(10) DEFAULT NULL COMMENT '单据类型.编码（默认：arfin_other_BT_S（财务应收））',
   `bizdate` date DEFAULT NULL COMMENT '单据日期（确认应收款项的日期）',
   `invoicedate` date DEFAULT NULL COMMENT '发票日期（实际开票日期）',
   `duedate` date DEFAULT NULL COMMENT '到期日（往来双方约定的应收款项最晚收款日期）',
   `asstacttype` varchar(32) DEFAULT NULL COMMENT '往来类型 bd_customer:客户, bd_supplier:供应商, bos_user:人员（往来户的归类。分为供应商、客户、人员）',
   `asstact_number` varchar(255) DEFAULT NULL COMMENT '往来户.编码（与公司发生往来业务的单位或个人）',
   `paymode` varchar(32) DEFAULT NULL COMMENT '付款方式 CASH:现销, CREDIT:赊销',
   `department_number` varchar(32) DEFAULT NULL COMMENT '部门.编码（发生应收业务的部门，传guid或组织id）',
   `createtime` datetime DEFAULT NULL COMMENT '创建时间',
   `modifytime` datetime DEFAULT NULL COMMENT '修改时间',
   `creator_number` varchar(32) DEFAULT NULL COMMENT '创建人.工号',
   `modifier_number` varchar(10) DEFAULT NULL COMMENT '修改人.工号',
   `settlementtype_number` varchar(32) DEFAULT NULL COMMENT '结算方式',
   `xmgd_vouchertype_number` varchar(0) DEFAULT NULL COMMENT '凭证类型',
   `xmgd_startdate` date DEFAULT NULL COMMENT '账单开始日期',
   `xmgd_finishdate` date DEFAULT NULL COMMENT '账单结束日期',
   `remark` text CHARACTER SET utf8 COMMENT '备注',
   `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
   `create_date` datetime DEFAULT NULL COMMENT '创建时间',
   `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
   `update_date` datetime DEFAULT NULL COMMENT '更新时间',
   `return_result` longtext COMMENT '返回结果',
   `org_name` varchar(255) DEFAULT NULL COMMENT '结算组织名称',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='应收单';

CREATE TABLE `t_cosmic_receivable_detail` (
  `id` varchar(32) NOT NULL COMMENT '主键id',
  `receivable_id` varchar(100) DEFAULT NULL COMMENT '应收表Id',
  `e_taxunitprice` decimal(12,0) DEFAULT NULL COMMENT '含税金额',
  `taxrateid_number` varchar(10) DEFAULT NULL COMMENT '税率.编码（增值税税率）',
  `e_tax` varchar(100) DEFAULT NULL COMMENT '税额（校验含税金额 = 不含税金额 + 税额）',
  `e_unitprice` decimal(12,0) DEFAULT NULL COMMENT '不含税单价',
  `xmgd_ht_number` varchar(10) DEFAULT NULL COMMENT '合同号.编码（辅助资料（合同））',
  `xmgd_xm_number` varchar(255) DEFAULT NULL COMMENT '项目.项目编码（基础资料（项目））',
  `xmgd_ldfh_number` varchar(255) DEFAULT NULL COMMENT '楼栋房号.编码（辅助资料（楼栋房号））',
  `xmgd_ytlx_number` varchar(32) DEFAULT NULL COMMENT '业态类型.编码（辅助资料（业态类型））',
  `xmgd_lyfs_number` varchar(100) DEFAULT NULL COMMENT '来源方式.编码（辅助资料（来源方式））',
  `xmgd_skqd_number` varchar(10) DEFAULT NULL COMMENT '收款渠道.编码（辅助资料（收款渠道））',
  `e_expenseitem_number` varchar(100) DEFAULT NULL COMMENT '收支项目.编码（基础资料（收支项目））(以上辅助资料根据收支项目所对应的核算维度进行动态必填校验，收支项目所对应的核算维度与核算组织有关，不同核算组织 所对应的核算维度是不同的)',
  `e_remark` varchar(32) DEFAULT NULL COMMENT '明细.摘要',
  `e_quantity` decimal(32,0) DEFAULT NULL COMMENT '明细.数量',
  `xmgd_incometype_number` varchar(255) DEFAULT NULL COMMENT '收入类型（辅助资料（收入类型））',
  `remark` text CHARACTER SET utf8 COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='财务应收子表';

--20241121
CREATE TABLE `t_apply_card`  (
 `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
 `student_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学（工）号',
 `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '片区',
 `partition_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼栋',
 `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房间号',
 `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房源名称',
 `source_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房源Id',
 `property_owner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产权人',
 `property_owner_tel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产权人电话',
 `property_owner_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产权人身份证',
 `declare_time` datetime NULL DEFAULT NULL COMMENT '申报时间',
 `leaser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租人',
 `renter` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '承租人',
 `applicant` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '办卡人',
 `gender` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '办卡人性别',
 `applicant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '办卡人Id',
 `applicant_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '办卡人身份证',
 `cause` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '办卡原因（1新办、2补卡、3注销、4延期）',
 `link_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联Id',
 `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡类别',
 `main_or_assistant` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主副卡',
 `expiry_date` date NULL DEFAULT NULL COMMENT '失效期',
 `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核状态(1待审核，2已通过，3未通过)',
 `use_state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用状态',
 `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
 `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
 `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
 `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
 `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

--********
CREATE TABLE `t_invoice_account` (
 `id` VARCHAR(32) NOT NULL,
 `project_id` VARCHAR(32) DEFAULT NULL,
 `invoice_terminal_code` VARCHAR(32) DEFAULT NULL COMMENT '开票终端/数电账号',
 `invoice_tax_no` VARCHAR(20) DEFAULT NULL COMMENT '销方税号',
 `invoice_seller_name` VARCHAR(100) DEFAULT NULL COMMENT '销方名称',
 `invoice_seller_address` VARCHAR(200) DEFAULT NULL COMMENT '销方地址',
 `invoice_seller_phone` VARCHAR(11) DEFAULT NULL COMMENT '销方电话',
 `invoice_seller_bank_name` VARCHAR(20) DEFAULT NULL COMMENT '销方开户行',
 `invoice_seller_bank_number` VARCHAR(50) DEFAULT NULL COMMENT '销方银行账户',
 `invoice_drawer` VARCHAR(20) DEFAULT NULL COMMENT '开票员',
 `invoice_payee` VARCHAR(20) DEFAULT NULL COMMENT '收款人',
 `invoice_checker` VARCHAR(20) DEFAULT NULL COMMENT '收款人',
 `state` CHAR(1) DEFAULT NULL COMMENT '状态',
 `order_types` VARCHAR(200) DEFAULT NULL COMMENT '账单类型',
 `create_by` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
 `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
 `update_by` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
 `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
 PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `t_invoice_account_ordertype_rel` (
`id` VARCHAR(32) NOT NULL,
`project_id` VARCHAR(32) DEFAULT NULL,
`account_id` VARCHAR(32) DEFAULT NULL,
`order_type` VARCHAR(32) DEFAULT NULL COMMENT '账单类型',
`invoice_tax_rate` VARCHAR(20) DEFAULT NULL COMMENT '税率',
`create_by` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
`create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
`update_by` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
`update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

--********
CREATE TABLE `t_bil_refund` (
`id` VARCHAR(32) NOT NULL COMMENT 'ID',
`tk_order_id` VARCHAR(32) DEFAULT NULL COMMENT '退款账单id',
`sk_order_id` VARCHAR(32) DEFAULT NULL COMMENT '收款账单id（实际退款的账单）',
`payment` DECIMAL(10,2) DEFAULT NULL COMMENT '退款金额',
`mer_order_id` VARCHAR(32) DEFAULT NULL COMMENT '商户订单号',
`seq_id` VARCHAR(32) DEFAULT NULL COMMENT '平台流水号',
`total_amount` VARCHAR(32) DEFAULT NULL COMMENT '支付总金额',
`refund_amount` VARCHAR(32) DEFAULT NULL COMMENT '总退款金额',
`refund_order_id` VARCHAR(32) DEFAULT NULL COMMENT '退货订单号',
`refund_target_order_id` VARCHAR(32) DEFAULT NULL COMMENT '目标系统退货订单号',
`refund_way` VARCHAR(32) DEFAULT NULL COMMENT '退款方式 1在线退款 2线下退款',
`refund_status` VARCHAR(32) DEFAULT NULL COMMENT '退款状态',
`remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
`create_by` VARCHAR(32) DEFAULT NULL,
`create_date` DATETIME DEFAULT NULL,
`update_by` VARCHAR(32) DEFAULT NULL,
`update_date` DATETIME DEFAULT NULL,
PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表'

--20241217
DROP TABLE IF EXISTS `t_parking_apply`;
CREATE TABLE `t_parking_apply`  (
`id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT ' 主键ID',
`apply_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 申请人',
`telphone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 联系电话',
`belong_enterprise` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 所属企业',
`type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 类型  个人 1、企业 2',
`license` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 车牌号',
`brand` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 车辆品牌',
`vehicle_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 车辆类型 1：5座  2：7座 3：大车',
`vehicle_color` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 车身颜色',
`status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 数据状态 1 待审  2 通过 3 失败 4 失效',
`aduit_time` datetime NULL DEFAULT NULL COMMENT ' 审核时间',
`remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 备注信息',
`create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 创建人',
`create_date` datetime NULL DEFAULT NULL COMMENT ' 创建时间',
`update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 修改人',
`update_date` datetime NULL DEFAULT NULL COMMENT ' 修改时间',
`parking_space_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车位ID',
`user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 用户ID',
`car_owner_tel` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车主电话',
`start_date` date NULL DEFAULT NULL COMMENT '开始时间',
`end_date` date NULL DEFAULT NULL COMMENT '结束时间',
`pay_way` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式(1线上2线下)',
`month_quantity` int(11) NULL DEFAULT 0 COMMENT '预缴月数',
`payment` decimal(11, 0) NULL DEFAULT NULL COMMENT '预缴金额',
`auxiliary_have` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '有无附属车辆(1无2有)',
`auxiliary_license` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属车牌号',
`auxiliary_car_tel` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属车主电话',
`auxiliary_brand` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属车辆品牌',
`auxiliary_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属车辆类型',
`auxiliary_color` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属车颜色',
`presented` int(11) NULL DEFAULT 0 COMMENT '附属车颜色',
`apply_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请备注',
`car_owner` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车主姓名',
`operator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人员ID',
`building` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼栋',
`project_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目Id',
PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

DROP TABLE IF EXISTS `t_parking_record`;
CREATE TABLE `t_parking_record`  (
                 `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                 `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                 `numberplate` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                 `telphone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                 `start_time` date NULL DEFAULT NULL,
                 `end_time` date NULL DEFAULT NULL,
                 `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '1正常2暂停3注销',
                 `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                 `create_date` datetime NULL DEFAULT NULL,
                 `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                 `update_date` datetime NULL DEFAULT NULL,
                 `park_space_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                 `apply_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 停车位申请 ID',
                 `card_id` int(11) NULL DEFAULT NULL COMMENT '科拓接口返回值',
                 `cost_date` date NULL DEFAULT NULL COMMENT '费用到期时间',
                 `auxiliary_license` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附属车牌号',
                 `car_owner_tel` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车主电话',
                 `car_owner` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车主姓名',
                 `operator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人员ID',
                 `project_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目Id',
                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;


DROP TABLE IF EXISTS `t_parking_space`;
CREATE TABLE `t_parking_space`  (
                `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
                `type` char(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '停车位类别（TCL、DXCK、DMCW）',
                `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '停车位名称 正常情况（停车楼、地面、地下车库）',
                `number` int(10) NULL DEFAULT NULL COMMENT '停车位数量',
                `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '租金价格',
                `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
                `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
                `area_id` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域编号',
                `project_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目id',
                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;


DROP TABLE IF EXISTS `t_contract_filing_parameters`;
CREATE TABLE `t_contract_filing_parameters`  (
                 `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
                 `project_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目Id',
                 `ssxmmc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属项目名称',
                 `tyshxydm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目所属单位统一社会信用代码',
                 `sqrxm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人姓名',
                 `sqrsjh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人手机号',
                 `sqrzjlx` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人证件类型',
                 `sqrzjh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人证件号',
                 `qszjlx` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权属证件类型（详见字典3.3权属证件类型）',
                 `qszjbh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权属证件号（详见字典4.1权属证件编号规则）',
                 `baBzdz` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准地址（格式：福建省厦门市XX区XX路XXX号XXX室）',
                 `baXzqh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行政区划编码（详见字典3.2附件）',
                 `baZj` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '镇街编码（详见字典3.2附件）',
                 `baCj` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '村居编码（详见字典3.2附件）',
                 `fwxz` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房屋性质（详见字典3.11房屋性质）',
                 `fwyt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房屋用途（详见字典3.12房屋用途）',
                 `wqS` int(11) NULL DEFAULT NULL COMMENT '室（房屋用途或房屋性质为10时，必填）',
                 `wqTing` int(11) NULL DEFAULT NULL COMMENT '厅（房屋用途或房屋性质为10时，必填）',
                 `wqW` int(11) NULL DEFAULT NULL COMMENT '卫（房屋用途或房屋性质为10时，必填）',
                 `wqC` int(11) NULL DEFAULT NULL COMMENT '厨（房屋用途或房屋性质为10时，必填）',
                 `zlfs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租赁方式（详见字典3.5租赁方式）',
                 `bw` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租部位（租赁方式为零租时必填）',
                 `zxcd` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '装修程度（详见字典3.6装修程度，房屋用途或房屋性质为10时，必填）',
                 `jzmj` double NULL DEFAULT NULL COMMENT '建筑面积（使用、居住、建筑面积三选一必填）',
                 `cqrxm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产权人姓名',
                 `cqrzjlx` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产权人证件类型（详见字典3.4人员证件类型）',
                 `cqrzjhm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产权人证件号码',
                 `cqzsmj` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传产权证扫描件，文件上传返回的对象数组转字符串',
                 `hirerType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方类型（详见字典3.7备案类型）',
                 `hirerName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方姓名',
                 `hirerPhone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方手机号',
                 `hirerCardType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方证件类型（详见字典3.4人员证件类型）',
                 `hirerCardNum` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方证件号码',
                 `hirerCardFile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方附件，文件上传返回的对象数组转字符串',
                 `hirerAgentName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方代理人姓名（出租方类型为企业备案时必填）',
                 `hirerAgentPhone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方代理人手机号（出租方类型为企业备案时必填）',
                 `hirerAgentCardType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方代理人证件类型（详见字典3.4人员证件类型，出租方类型为企业备案时必填）',
                 `hirerAgentCardNum` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出租方代理人证件号码（出租方类型为企业备案时必填）',
                 `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ' 备注信息',
                 `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                 `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
                 `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                 `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;


--20241224
CREATE TABLE `t_face_audit` (
    `id` varchar(32) NOT NULL COMMENT '主键id',
    `contract_id` varchar(32) DEFAULT NULL COMMENT '合同id',
    `paper_code` varchar(50) DEFAULT NULL COMMENT '纸质合同编号',
    `project_id` varchar(32) DEFAULT NULL COMMENT '项目id',
    `source_id` varchar(32) DEFAULT NULL COMMENT '房源id',
    `source_name` varchar(32) DEFAULT NULL COMMENT '房源名称',
    `renter_id` varchar(32) DEFAULT NULL COMMENT '租客id',
    `renter_name` varchar(32) DEFAULT NULL COMMENT '租客姓名',
    `renter_tel` varchar(32) DEFAULT NULL COMMENT '租客手机号',
    `start_date` varchar(32) DEFAULT NULL COMMENT '开始时间',
    `end_date` varchar(32) DEFAULT NULL COMMENT '结束时间',
    `state` varchar(2) DEFAULT '0' COMMENT '0未注册 1待审核 2已审核 3审核不通过',
    `card_sn` varchar(32) DEFAULT NULL COMMENT '门锁卡号',
    `face_url` varchar(255) DEFAULT NULL COMMENT '人脸图片公网链接（jpg）',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新人',
    `update_date` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='人脸注册表';

CREATE TABLE `t_face_lock` (
   `id` VARCHAR(32) NOT NULL COMMENT '主键id',
   `local_id` VARCHAR(32) DEFAULT NULL COMMENT '门禁机序列号',
   `project_id` VARCHAR(32) DEFAULT NULL COMMENT '项目id',
   `address` VARCHAR(200) DEFAULT NULL COMMENT '地址',
   `state` VARCHAR(2) DEFAULT '0' COMMENT '0禁用 1启用',
   `remark` VARCHAR(255) DEFAULT NULL COMMENT '备注',
   `create_by` VARCHAR(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人',
   `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
   `update_by` VARCHAR(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新人',
   `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE=INNODB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='人脸门禁表';

CREATE TABLE `t_face_rel` (
  `id` varchar(32) NOT NULL COMMENT 'id',
  `audit_id` varchar(32) NOT NULL COMMENT '审核id',
  `lock_id` varchar(32) NOT NULL COMMENT '门禁id',
  `remark` VARCHAR(255) DEFAULT NULL COMMENT '备注',
  `create_by` VARCHAR(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人',
  `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
  `update_by` VARCHAR(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新人',
  `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='人脸审核关联表';

--20250118
CREATE TABLE `t_contract_filing_parameters` (
    `id` varchar(32) NOT NULL COMMENT '主键ID',
    `project_id` varchar(255) DEFAULT NULL COMMENT '项目Id',
    `partition_id` varchar(32) DEFAULT NULL COMMENT '楼栋id',
    `ssxmmc` varchar(255) DEFAULT NULL COMMENT '所属项目名称',
    `tyshxydm` varchar(255) DEFAULT NULL COMMENT '项目所属单位统一社会信用代码',
    `sqrxm` varchar(255) DEFAULT NULL COMMENT '申请人姓名',
    `sqrsjh` varchar(255) DEFAULT NULL COMMENT '申请人手机号',
    `sqrzjlx` varchar(255) DEFAULT NULL COMMENT '申请人证件类型',
    `sqrzjh` varchar(255) DEFAULT NULL COMMENT '申请人证件号',
    `qszjlx` varchar(255) DEFAULT NULL COMMENT '权属证件类型（详见字典3.3权属证件类型）',
    `qszjbh` varchar(255) DEFAULT NULL COMMENT '权属证件号（详见字典4.1权属证件编号规则）',
    `baBzdz` varchar(255) DEFAULT NULL COMMENT '标准地址（格式：福建省厦门市XX区XX路XXX号XXX室）',
    `baXzqh` varchar(255) DEFAULT NULL COMMENT '行政区划编码（详见字典3.2附件）',
    `baZj` varchar(255) DEFAULT NULL COMMENT '镇街编码（详见字典3.2附件）',
    `baCj` varchar(255) DEFAULT NULL COMMENT '村居编码（详见字典3.2附件）',
    `fwxz` varchar(255) DEFAULT NULL COMMENT '房屋性质（详见字典3.11房屋性质）',
    `fwyt` varchar(255) DEFAULT NULL COMMENT '房屋用途（详见字典3.12房屋用途）',
    `wqS` int(11) DEFAULT NULL COMMENT '室（房屋用途或房屋性质为10时，必填）',
    `wqTing` int(11) DEFAULT NULL COMMENT '厅（房屋用途或房屋性质为10时，必填）',
    `wqW` int(11) DEFAULT NULL COMMENT '卫（房屋用途或房屋性质为10时，必填）',
    `wqC` int(11) DEFAULT NULL COMMENT '厨（房屋用途或房屋性质为10时，必填）',
    `zlfs` varchar(255) DEFAULT NULL COMMENT '租赁方式（详见字典3.5租赁方式）',
    `bw` varchar(255) DEFAULT NULL COMMENT '出租部位（租赁方式为零租时必填）',
    `zxcd` varchar(255) DEFAULT NULL COMMENT '装修程度（详见字典3.6装修程度，房屋用途或房屋性质为10时，必填）',
    `jzmj` double DEFAULT NULL COMMENT '建筑面积（使用、居住、建筑面积三选一必填）',
    `cqrxm` varchar(255) DEFAULT NULL COMMENT '产权人姓名',
    `cqrzjlx` varchar(255) DEFAULT NULL COMMENT '产权人证件类型（详见字典3.4人员证件类型）',
    `cqrzjhm` varchar(255) DEFAULT NULL COMMENT '产权人证件号码',
    `cqzsmj` varchar(255) DEFAULT NULL COMMENT '上传产权证扫描件，文件上传返回的对象数组转字符串',
    `hirerType` varchar(255) DEFAULT NULL COMMENT '出租方类型（详见字典3.7备案类型）',
    `hirerName` varchar(255) DEFAULT NULL COMMENT '出租方姓名',
    `hirerPhone` varchar(255) DEFAULT NULL COMMENT '出租方手机号',
    `hirerCardType` varchar(255) DEFAULT NULL COMMENT '出租方证件类型（详见字典3.4人员证件类型）',
    `hirerCardNum` varchar(255) DEFAULT NULL COMMENT '出租方证件号码',
    `hirerCardFile` varchar(255) DEFAULT NULL COMMENT '出租方附件，文件上传返回的对象数组转字符串',
    `hirerAgentName` varchar(255) DEFAULT NULL COMMENT '出租方代理人姓名（出租方类型为企业备案时必填）',
    `hirerAgentPhone` varchar(255) DEFAULT NULL COMMENT '出租方代理人手机号（出租方类型为企业备案时必填）',
    `hirerAgentCardType` varchar(255) DEFAULT NULL COMMENT '出租方代理人证件类型（详见字典3.4人员证件类型，出租方类型为企业备案时必填）',
    `hirerAgentCardNum` varchar(255) DEFAULT NULL COMMENT '出租方代理人证件号码（出租方类型为企业备案时必填）',
    `hirerAgentCardFile` varchar(255) DEFAULT NULL COMMENT '出租方代理人身份证附件',
    `hirerAgentQtFile` varchar(255) DEFAULT NULL COMMENT '代理人授权书附件',
    `remark` varchar(500) DEFAULT NULL COMMENT ' 备注信息',
    `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(32) DEFAULT NULL COMMENT '修改人',
    `update_date` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

--20250120
CREATE TABLE `t_caller_record` (
   `id` varchar(32) NOT NULL COMMENT 'ID',
   `name` varchar(32) DEFAULT NULL COMMENT '姓名',
   `gender` varchar(32) DEFAULT NULL COMMENT '性别(1男2女)',
   `tel` varchar(32) DEFAULT NULL COMMENT '联系方式',
   `access_time` datetime DEFAULT NULL COMMENT '访问时间',
   `remark` text COMMENT '备注',
   `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
   `create_date` datetime DEFAULT NULL COMMENT '创建时间',
   `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
   `update_date` datetime DEFAULT NULL COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='访客记录表';

--20250324
CREATE TABLE `t_apply_card_efficient`  (
   `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
   `student_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学（工）号',
   `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
   `applicant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '办卡人Id',
   `gender` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
   `tel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
   `identity_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件号',
   `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡类别',
   `use_state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用状态',
   `expiry_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失效期',
   `main_or_assistant` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主副卡标志',
   `dept` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门全称',
   `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '片区',
   `project_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '片区id',
   `partition_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼栋',
   `partition_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼栋id',
   `source_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房间号',
   `source_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房源Id',
   `pay_state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费状态',
   `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
   `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
   `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
   `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
   `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '住宅卡生效表' ROW_FORMAT = DYNAMIC;

CREATE TABLE `t_cosmic_base_data` (
  `id` varchar(255) NOT NULL,
  `type` varchar(255) DEFAULT NULL COMMENT '本系统指定的金蝶系统基础资料类型',
  `base_id` bigint(20) DEFAULT NULL COMMENT '金蝶系统中的id',
  `name` varchar(255) DEFAULT NULL COMMENT '金蝶系统中的名称',
  `full_name` varchar(255) DEFAULT NULL COMMENT '长名称',
  `number` varchar(255) DEFAULT NULL COMMENT '金蝶系统中的编码',
  `group_number` varchar(255) DEFAULT NULL COMMENT '对应type在金蝶系统中对应的分类编码',
  `parent_number` varchar(255) DEFAULT NULL COMMENT '对应type在金蝶里的对应上级编码',
  `parent_name` varchar(255) DEFAULT NULL COMMENT '对应type在金蝶里的对应上级名称',
  `enable` varchar(2) DEFAULT NULL COMMENT '使用状态 0:禁用, 1:可用',
  `status` varchar(2) DEFAULT NULL COMMENT '数据状态 A:暂存, B:已提交, C:已审核',
  `level` int(11) DEFAULT NULL COMMENT '级次',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `org_id` bigint(20) DEFAULT NULL COMMENT '组织id',
  `org_number` varchar(255) DEFAULT '' COMMENT '组织编码',
  `create_org_id` bigint(20) DEFAULT NULL COMMENT '创建组织id',
  `create_org_number` varchar(255) DEFAULT NULL COMMENT '创建组织 编码',
  `create_org_name` varchar(255) DEFAULT NULL COMMENT '创建组织名称',
  `use_org_id` bigint(20) DEFAULT NULL COMMENT '资产组织id',
  `use_org_number` varchar(255) DEFAULT NULL COMMENT '资产组织编码',
  `use_org_name` varchar(255) DEFAULT NULL COMMENT '资产组织名称',
  `description` varchar(2000) DEFAULT NULL COMMENT '描述',
  `expand_data` text COMMENT '拓展数据 基础资料很多 把上面的比较通用字段去掉后的其他字段用拓展信息存',
  `create_date` datetime DEFAULT NULL,
  `create_by` varchar(255) DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `update_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_number` (`number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='金蝶财务系统基础资料表';

CREATE TABLE `t_cosmic_income_expenditure` (
   `id` varchar(32) NOT NULL COMMENT '主键ID',
   `order_type` varchar(32) DEFAULT NULL COMMENT '\r\n账单类型',
   `type_name` varchar(50) DEFAULT NULL COMMENT '账单类型名称',
   `number` varchar(50) DEFAULT NULL COMMENT '收支项目编码',
   `xmgd_combofile_id` varchar(50) DEFAULT NULL COMMENT '收支项目属性值',
   `name` varchar(50) DEFAULT NULL COMMENT '收支项目名称',
   `attribute` varchar(50) DEFAULT NULL COMMENT '收支项目属性[1:费用，2:成本，3:收入，4:薪酬]',
   `type` varchar(255) DEFAULT NULL COMMENT '类别（1收入2支出）',
   `payment_number` varchar(500) DEFAULT NULL COMMENT '付款类型编码 ',
   `tax_rate` decimal(11,2) DEFAULT NULL COMMENT '税率',
   `remark` varchar(500) DEFAULT NULL COMMENT '备注',
   `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
   `create_date` datetime DEFAULT NULL COMMENT '创建时间',
   `update_by` varchar(64) DEFAULT NULL COMMENT '修改人',
   `update_date` datetime DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC --20250325

--20250325
CREATE TABLE `t_cosmic_assistant_data`
(
    `id`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `type_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类别编码',
    `type_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类别名称',
    `number`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
    `name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
    `create_date` datetime                                                      NULL DEFAULT NULL,
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `update_date` datetime                                                      NULL DEFAULT NULL,
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_number_union` (`type_number`, `number`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '金蝶财务(星瀚)辅助资料表'
  ROW_FORMAT = Dynamic;

CREATE TABLE `t_cosmic_income`
(
    `id`                         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `bill_no`                    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '金蝶单据编号',
    `source_system`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源系统编码(本系统)',
    `source_bill_type`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据来源类型',
    `source_bill_number`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源单据编码',
    `is_dev_free`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否开发费用',
    `org_number`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结算组织的guid或者id',
    `bill_type_number`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据类型',
    `biz_date`                   datetime                                                      NULL DEFAULT NULL COMMENT '单据日期',
    `invoice_date`               datetime                                                      NULL DEFAULT NULL COMMENT '发票日期(实际开票日期)',
    `due_date`                   datetime                                                      NULL DEFAULT NULL COMMENT '到期日',
    `asst_act_type`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '往来类型 bd_customer:客户, bd_supplier:供应商, bos_user:人员（往来户的归类。分为供应商、客户、人员）',
    `asst_act_number`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '往来户编码',
    `payment_customer_id_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款客户 统一社会信用代码',
    `pay_mode`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'CASH:现销, CREDIT:赊销',
    `department_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门 编码 应收业务的部门 guid或id',
    `create_time`                datetime                                                      NULL DEFAULT NULL COMMENT '创建时间',
    `modify_time`                datetime                                                      NULL DEFAULT NULL COMMENT '修改时间',
    `creator_number`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人编号',
    `modifier_number`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人编号',
    `url`                        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用于存储业务系统原单地址',
    `voucher_type_number`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '凭证类型 编码',
    `voucher_number`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '凭证号',
    `start_date`                 datetime                                                      NULL DEFAULT NULL COMMENT '账单开始日期',
    `finish_date`                datetime                                                      NULL DEFAULT NULL COMMENT '账单结束日期',
    `settle_type_number`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结算方式编码',
    `book_date`                  datetime                                                      NULL DEFAULT NULL COMMENT '报账日期',
    `create_date`                datetime                                                      NULL DEFAULT NULL,
    `create_by`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `update_date`                datetime                                                      NULL DEFAULT NULL,
    `update_by`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '金蝶(星瀚)财务应收单主表'
  ROW_FORMAT = Dynamic;

CREATE TABLE `t_cosmic_income_item`
(
    `id`                       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `income_id`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表id',
    `tax_unit_price`           decimal(10, 2)                                                NULL DEFAULT NULL COMMENT '含税金额',
    `tax_rate_id_number`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税率.编码',
    `tax`                      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税额（校验含税金额 = 不含税金额 + 税额）',
    `unit_price`               decimal(10, 2)                                                NULL DEFAULT NULL COMMENT '不含税单价',
    `contract_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同号.编码（辅助资料（合同））',
    `project_number`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目.项目编码（基础资料（项目））',
    `building_and_unit_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼栋房号.编码（辅助资料（楼栋房号））',
    `biz_type_number`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业态类型.编码（辅助资料（业态类型））',
    `source_method_number`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源方式.编码（辅助资料（来源方式））',
    `channel_number`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收款渠道.编码（辅助资料（收款渠道））',
    `expense_item_number`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收支项目.编码（基础资料（收支项目））\r\n(以上辅助资料根据收支项目所对应的核算维度进行动态必填校验，\r\n收支项目所对应的核算维度与核算组织有关，\r\n不同核算组织 所对应的核算维度是不同的)',
    `remark`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `quantity`                 decimal(10, 2)                                                NULL DEFAULT NULL COMMENT '数量',
    `income_type_number`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收入类型（辅助资料（收入类型））',
    `create_date`              datetime                                                      NULL DEFAULT NULL,
    `create_by`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `update_time`              datetime                                                      NULL DEFAULT NULL,
    `update_by`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_incomeId` (`income_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '金蝶(星瀚)财务应收单子表'
  ROW_FORMAT = Dynamic;

--********
CREATE TABLE `t_cosmic_transaction`
(
    `id`                                varchar(255) NOT NULL,
    `bank_id`                           varchar(255)   DEFAULT NULL COMMENT '交易明细唯一表示',
    `bill_no`                           varchar(255)   DEFAULT NULL COMMENT '交易明细编码',
    `claim`                             tinyint(4)     DEFAULT NULL COMMENT '认领状态',
    `company_number`                    varchar(255)   DEFAULT NULL COMMENT '资金组织 编码',
    `company_name`                      varchar(255)   DEFAULT NULL COMMENT '资金组织 名称',
    `company_id`                        varchar(255)   DEFAULT NULL COMMENT '资金组织 id',
    `company_guid`                      varchar(255)   DEFAULT NULL COMMENT '资金组织 guid',
    `currency_number`                   varchar(255)   DEFAULT NULL COMMENT '币别 货币代码',
    `account_bank_bank_account_number`  varchar(255)   DEFAULT NULL COMMENT '银行账号 银行账号',
    `account_bank_comment`              varchar(255)   DEFAULT NULL COMMENT '银行账号 备注',
    `account_bank_acct_property_number` varchar(255)   DEFAULT NULL COMMENT '账户用途编码',
    `account_bank_acct_property_name`   varchar(255)   DEFAULT NULL COMMENT '账户用途',
    `bank_name`                         varchar(255)   DEFAULT NULL COMMENT '开户银行',
    `biz_date`                          date           DEFAULT NULL COMMENT '交易日期',
    `biz_time`                          datetime       DEFAULT NULL COMMENT '交易时间',
    `description`                       text COMMENT '摘要',
    `credit_amount`                     decimal(10, 2) DEFAULT NULL COMMENT '收款金额',
    `opp_bank_number`                   varchar(255)   DEFAULT NULL COMMENT '对方账号',
    `opp_unit`                          varchar(255)   DEFAULT NULL COMMENT '对方户名',
    `opp_bank`                          varchar(255)   DEFAULT NULL COMMENT '对方开户行',
    `is_matcherceipt`                   tinyint(4)     DEFAULT NULL COMMENT '跟电子回单匹配',
    `recered_type`                      varchar(255)   DEFAULT NULL COMMENT '入账状态 [3:已入账, 0:待入账]',
    `modifier_name`                     varchar(255)   DEFAULT NULL COMMENT '修改人姓名',
    `modify_time`                       datetime       DEFAULT NULL COMMENT '最后更新时间(金蝶)',
    `create_date`                       datetime       DEFAULT NULL COMMENT '创建时间',
    `create_by`                         varchar(255)   DEFAULT NULL COMMENT '创建人',
    `update_date`                       datetime       DEFAULT NULL COMMENT '修改时间',
    `update_by`                         varchar(255)   DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)交易明细表';

--********
CREATE TABLE `t_cosmic_expense_item`
(
    `id`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `bill_type`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '金蝶账单类型',
    `order_item_type`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单明细类型',
    `expense_item_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收支明细编码',
    `expense_item_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收支明细名称',
    `create_date`         datetime                                                      NULL DEFAULT NULL,
    `create_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `update_date`         datetime                                                      NULL DEFAULT NULL,
    `update_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_type_number_union` (`bill_type`, `expense_item_number`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '订单明细对应收支项目表'
  ROW_FORMAT = Dynamic;

--20250411
CREATE TABLE `t_contract_expiration_msg`
(
    `id`          varchar(255) NOT NULL,
    `days`        int(11)      DEFAULT '30' COMMENT '到期前通知的天数 30天/15天',
    `contract_id` varchar(255) DEFAULT NULL COMMENT '通知的合同id',
    `is_send`     tinyint(4)   DEFAULT '1' COMMENT '是否发送 0否1是',
    `create_date` datetime     DEFAULT NULL,
    `create_by`   varchar(255) DEFAULT NULL,
    `update_date` datetime     DEFAULT NULL,
    `update_by`   varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_contract` (`contract_id`),
    KEY `idx_days` (`days`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='合同到期通知表';

--20250418
CREATE TABLE `t_cosmic_collection`
(
    `id`                    varchar(255) NOT NULL,
    `receivable_number`     varchar(255)   DEFAULT NULL COMMENT '应收来源单号',
    `is_push`               tinyint(4)     DEFAULT '0' COMMENT '是否推送',
    `is_dev_free`           char(2)        DEFAULT NULL COMMENT '是否开发费用',
    `special_type`          char(2)        DEFAULT NULL COMMENT '特殊场景类型',
    `special_bill_number`   varchar(255)   DEFAULT NULL COMMENT '特殊场景关联编号',
    `biz_date`              datetime       DEFAULT NULL COMMENT '业务日期',
    `receiving_type_number` varchar(255)   DEFAULT NULL COMMENT '收款类型编码',
    `payer_type`            varchar(255)   DEFAULT NULL COMMENT '付款人类型',
    `txt_description`       text COMMENT '摘要',
    `org_number`            varchar(255)   DEFAULT NULL COMMENT '收款人编码',
    `open_org_number`       varchar(255)   DEFAULT NULL COMMENT '核算组织编码',
    `account_bank_number`   varchar(255)   DEFAULT NULL COMMENT '收款账户.编码 结算方式非现金时，该字段必填，填银行收款账户',
    `account_cash_number`   varchar(255)   DEFAULT NULL COMMENT '现金账户.编码 结算方式为现金时，该字段必填，填现金收款账户',
    `payer_name`            varchar(255)   DEFAULT NULL COMMENT '付款人（编码）客户传统一社会信用代码',
    `payer_acct_bank_num`   varchar(255)   DEFAULT NULL COMMENT '付款账号 当结算方式为现金时，非必填',
    `payer_bank_name`       varchar(255)   DEFAULT NULL COMMENT '付款银行 当结算方式为现金时，非必填',
    `settle_type_number`    varchar(255)   DEFAULT NULL COMMENT ' 结算方式.编码\r\n 使用金蝶“结算方式”基础资料编码\r\n当选择结算方式为数币钱包的业务时，收款方账户和付款方账户需要均为数币钱包账户才可正常确认收款。',
    `url`                   varchar(255)   DEFAULT NULL COMMENT 'URL',
    `act_rec_amt`           decimal(10, 2) DEFAULT NULL COMMENT '收款金额',
    `voucher_number`        varchar(255)   DEFAULT NULL COMMENT '凭证类型 编码',
    `start_date`            datetime       DEFAULT NULL COMMENT '账单开始时间',
    `finish_date`           datetime       DEFAULT NULL COMMENT '账单结束时间',
    `bill_no`               varchar(255)   DEFAULT NULL COMMENT '单据编号(金蝶)',
    `source_system`         varchar(255)   DEFAULT NULL COMMENT '来源系统',
    `source_bill_type`      varchar(255)   DEFAULT NULL COMMENT '来源单据类型',
    `source_bill_number`    varchar(255)   DEFAULT NULL COMMENT '来源单据编码',
    `create_date`           datetime       DEFAULT NULL,
    `create_by`             varchar(255)   DEFAULT NULL,
    `update_date`           datetime       DEFAULT NULL,
    `update_by`             varchar(255)   DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_receivable_number` (`receivable_number`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)系统 财务收款处理单';

CREATE TABLE `t_cosmic_collection_item`
(
    `id`                       varchar(255) NOT NULL,
    `collection_id`            varchar(255)   DEFAULT NULL COMMENT '收款处理单id',
    `contract_number`          varchar(255)   DEFAULT NULL COMMENT '合同号编码',
    `contract_name`            varchar(255)   DEFAULT NULL COMMENT '合同号名称',
    `project_number`           varchar(255)   DEFAULT NULL COMMENT '项目 编码',
    `project_name`             varchar(255)   DEFAULT NULL COMMENT '项目 名称',
    `channel_number`           varchar(255)   DEFAULT NULL COMMENT '收款渠道编码',
    `channel_name`             varchar(255)   DEFAULT NULL COMMENT '收款渠道名称',
    `biz_type_number`          varchar(255)   DEFAULT NULL COMMENT '业态类型编码',
    `biz_type_name`            varchar(255)   DEFAULT NULL COMMENT '业态类型名称',
    `source_method_number`     varchar(255)   DEFAULT NULL COMMENT '来源方式编码',
    `source_method_name`       varchar(255)   DEFAULT NULL COMMENT '来源方式名称',
    `building_and_unit_number` varchar(255)   DEFAULT NULL COMMENT '楼栋房号编码',
    `building_and_unit_name`   varchar(255)   DEFAULT NULL COMMENT '楼栋房号名称',
    `financial_org_number`     varchar(255)   DEFAULT NULL COMMENT '金融机构编码',
    `financial_org_name`       varchar(255)   DEFAULT NULL COMMENT '金融机构名称',
    `settle_org_number`        varchar(255)   DEFAULT NULL COMMENT '结算组织编码',
    `settle_org_name`          varchar(255)   DEFAULT NULL COMMENT '结算组织名称',
    `expense_item_number`      varchar(255)   DEFAULT NULL COMMENT '收支项目 编码',
    `expense_item_name`        varchar(255)   DEFAULT NULL COMMENT '收支项目 名称',
    `receivable_amt`           decimal(10, 2) DEFAULT NULL COMMENT '收款明细 应收金额',
    `fee`                      DECIMAL(13, 2) DEFAULT NULL COMMENT '手续费',
    `cost_center_number`       varchar(255)   DEFAULT NULL COMMENT '成本中心 编码',
    `cost_center_name`         varchar(255)   DEFAULT NULL COMMENT '成本中心 名称',
    `remark`                   varchar(255)   DEFAULT NULL COMMENT '收款明细 备注',
    `create_date`              datetime       DEFAULT NULL,
    `create_by`                varchar(255)   DEFAULT NULL,
    `update_date`              datetime       DEFAULT NULL,
    `update_by`                varchar(255)   DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_collection_id` (`collection_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)系统 财务收款处理单明细';

--20250421
CREATE TABLE `t_cosmic_reimbursement`
(
    `id`                 varchar(255) NOT NULL,
    `bill_no`            varchar(255) DEFAULT NULL COMMENT '单据编号(金蝶)',
    `is_push`            tinyint(4)   DEFAULT '0' COMMENT '是否推送金蝶系统',
    `source_system`      varchar(255) DEFAULT NULL COMMENT '来源系统',
    `source_bill_type`   varchar(255) DEFAULT NULL COMMENT '来源单据类型',
    `source_bill_number` varchar(255) DEFAULT NULL COMMENT '单据来源编号',
    `creator_number`     varchar(255) DEFAULT NULL COMMENT '创建人 工号',
    `modifier_number`    varchar(255) DEFAULT NULL COMMENT '修改人 编号',
    `dept_number`        varchar(255) DEFAULT NULL COMMENT '部门编码',
    `date_field`         date         DEFAULT NULL COMMENT '业务日期',
    `audit_date`         datetime     DEFAULT NULL COMMENT '审核日期',
    `account_org_number` varchar(255) DEFAULT NULL COMMENT '核算组织.编码（支持guid、id、编码）',
    `biz_type_number`    varchar(255) DEFAULT NULL COMMENT '报账业务类型必填.编码（备注：不同业务类型对应不同的报账业务类型，联系金蝶人员根据具体业务需要创建相应的业务类型）',
    `attachment_num`     int(255)     DEFAULT NULL COMMENT '附件数',
    `apply_date`         date         DEFAULT NULL COMMENT '申请日期',
    `modify_time`        datetime     DEFAULT NULL COMMENT '修改时间',
    `auditor_number`     varchar(255) DEFAULT NULL COMMENT '审核人 工号',
    `description`        varchar(255) DEFAULT NULL COMMENT '事由',
    `book_date`          date         DEFAULT NULL COMMENT '报账日期',
    `voucher_no`         varchar(255) DEFAULT NULL COMMENT '凭证号',
    `create_time`        datetime     DEFAULT NULL COMMENT '创建时间',
    `create_date`        datetime     DEFAULT NULL,
    `create_by`          varchar(255) DEFAULT NULL,
    `update_date`        datetime     DEFAULT NULL,
    `update_by`          varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)报账工单';

CREATE TABLE `t_cosmic_reimbursement_item`
(
    `id`                    varchar(255) NOT NULL,
    `reimbursement_id`      varchar(255)   DEFAULT NULL COMMENT '报账工单id',
    `debit_amount`          decimal(10, 2) DEFAULT NULL COMMENT '借方金额',
    `crebit_amount`         decimal(10, 2) DEFAULT NULL COMMENT '贷方金额',
    `c_accounting_dim`      varchar(500)   DEFAULT NULL COMMENT '借方核算维度json',
    `d_accounting_dim`      varchar(500)   DEFAULT NULL COMMENT '贷方核算维度 json',
    `debit_account_number`  varchar(255)   DEFAULT NULL COMMENT '借方科目 编码',
    `crebit_account_number` varchar(255)   DEFAULT NULL COMMENT '贷方科目 编码',
    `base_project_number`   varchar(255)   DEFAULT NULL COMMENT '主表项目 编码',
    `add_data_number`       varchar(255)   DEFAULT NULL COMMENT '补充资料编码',
    `description`           varchar(500)   DEFAULT NULL COMMENT '摘要',
    `expense_item_number`   varchar(255)   DEFAULT NULL COMMENT '收支项目.编码（基础资料（收支项目））\r\n (以上辅助资料根据收支项目所对应的核算维度进行动态必填校验，\r\n 收支项目所对应的核算维度与核算组织有关，\r\n不同核算组织 所对应的核算维度是不同的)',
    `create_date`           datetime       DEFAULT NULL,
    `create_by`             varchar(255)   DEFAULT NULL,
    `update_date`           datetime       DEFAULT NULL,
    `update_by`             varchar(255)   DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_reimbursement_id` (`reimbursement_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

--********
CREATE TABLE `t_cosmic_accounts_payable`
(
    `id`                           varchar(255) NOT NULL,
    `source_bill_type`             varchar(255)   DEFAULT NULL COMMENT '来源单据类型',
    `source_bill_number`           varchar(255)   DEFAULT NULL COMMENT '来源单据编号',
    `bill_no`                      varchar(255)   DEFAULT NULL COMMENT '单据号',
    `voucher_num`                  varchar(255)   DEFAULT NULL COMMENT '凭证号',
    `biz_date`                     date           DEFAULT NULL COMMENT '单据日期',
    `org_id`                       varchar(255)   DEFAULT NULL COMMENT '结算组织.id（传入组织id或者guid）',
    `pay_org_id`                   varchar(255)   DEFAULT NULL COMMENT '付款组织.id（传入组织id或者guid）',
    `pur_mode`                     varchar(10)    DEFAULT NULL COMMENT '付款方式 CASH:现购, CREDIT:赊购',
    `currency_number`              varchar(255)   DEFAULT NULL COMMENT '结算币别.货币代码',
    `pay_type_number`              varchar(10)    DEFAULT NULL COMMENT '付款类型.编码',
    `sum_invoice_amount`           decimal(10, 2) DEFAULT NULL COMMENT '含本期累计开票金额',
    `sum_pay_amount`               decimal(10, 2) DEFAULT NULL COMMENT '含本期累计实付金额',
    `if_additional`                tinyint(4)     DEFAULT NULL COMMENT '是否冲期初预付或代付 0否1是',
    `invoice_code`                 varchar(255)   DEFAULT NULL COMMENT '发票代码',
    `audit_date`                   datetime       DEFAULT NULL COMMENT '审核时间',
    `modify_time`                  datetime       DEFAULT NULL COMMENT '修改时间',
    `create_time`                  datetime       DEFAULT NULL COMMENT '创建时间',
    `due_date`                     date           DEFAULT NULL COMMENT '到期日',
    `is_dev_fee`                   tinyint(4)     DEFAULT NULL COMMENT '是否开发费用 0否1是',
    `receiving_supplier_id_number` varchar(255)   DEFAULT NULL COMMENT '收款供应商.编码(统一社会信用代码)',
    `payee_bank_num`               varchar(255)   DEFAULT NULL COMMENT '收款账号',
    `be_bank_number`               varchar(255)   DEFAULT NULL COMMENT '收款银行.编码',
    `invoice_biz_type_number`      varchar(255)   DEFAULT NULL COMMENT '发票类别.编码',
    `department_id`                varchar(255)   DEFAULT NULL COMMENT '部门.id（传入组织id或者guid）',
    `apply_bill_number`            varchar(255)   DEFAULT NULL COMMENT '付款申请单号',
    `tax`                          decimal(10, 2) DEFAULT NULL COMMENT '税额',
    `un_invoiced_amt`              decimal(10, 2) DEFAULT NULL COMMENT '未开票应付金额',
    `creator_id`                   varchar(255)   DEFAULT NULL COMMENT '创建人id',
    `is_fx`                        tinyint(4)     DEFAULT NULL COMMENT '微调金额 0否1是',
    `is_fx_price_tax_total`        tinyint(4)     DEFAULT NULL COMMENT '微调应付金额 0否1是',
    `auditor_id`                   varchar(255)   DEFAULT NULL COMMENT '审核人id',
    `modifier_id`                  varchar(255)   DEFAULT NULL COMMENT '修改人id',
    `book_date`                    date           DEFAULT NULL COMMENT '记账日期',
    `price_tax_total`              decimal(10, 2) DEFAULT NULL COMMENT '应付金额',
    `is_include_tax`               tinyint(4)     DEFAULT NULL COMMENT '录入含税价 0否1是',
    `remark`                       varchar(2000)  DEFAULT NULL COMMENT '备注',
    `is_price_total`               tinyint(4)     DEFAULT NULL COMMENT '录入总价 0否1是',
    `check_box_field`              tinyint(4)     DEFAULT NULL COMMENT '发票后补 0否1是',
    `url`                          varchar(255)   DEFAULT NULL COMMENT 'url',
    `create_date`                  datetime       DEFAULT NULL COMMENT '创建日期',
    `create_by`                    varchar(255)   DEFAULT NULL COMMENT '创建人',
    `update_date`                  datetime       DEFAULT NULL COMMENT '修改日期',
    `update_by`                    varchar(255)   DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)系统财务应付单';

CREATE TABLE `t_cosmic_accounts_payable_engine`
(
    `id`                  varchar(255) NOT NULL,
    `accounts_payable_id` varchar(255)   DEFAULT NULL,
    `engineering_number`  varchar(255)   DEFAULT NULL COMMENT '工程名称',
    `month_amount`        decimal(10, 2) DEFAULT NULL COMMENT '计量工程单据体.本月完成金额',
    `create_date`         datetime       DEFAULT NULL,
    `create_by`           varchar(255)   DEFAULT NULL,
    `update_date`         datetime       DEFAULT NULL,
    `update_by`           varchar(255)   DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)系统财务应付单 计量工程单据体';

CREATE TABLE `t_cosmic_accounts_payable_collection`
(
    `id`                  varchar(255) NOT NULL,
    `unique`              varchar(255)   DEFAULT NULL COMMENT '收款信息单据体.唯一标识',
    `collectioner`        varchar(255)   DEFAULT NULL COMMENT '收款信息单据体.收款人（收款供应商统一社会代码）',
    `accounts_payable_id` varchar(255)   DEFAULT NULL,
    `col_amount`          decimal(10, 2) DEFAULT NULL COMMENT '收款信息单据体.收款金额',
    `col_account`         varchar(255)   DEFAULT NULL COMMENT '收款账号',
    `col_account_name`    varchar(255)   DEFAULT NULL COMMENT '收款名称',
    `col_bank_number`     varchar(255)   DEFAULT NULL COMMENT '收款银行.编码   备注：应对收款人类型为其他和个人，或者供应商/客户的银行账户信息不全的情况下，需要填写对应的银行账户的收款银行的行名行号',
    `col_remark`          varchar(255)   DEFAULT NULL COMMENT '收款信息单据体.备注',
    `create_date`         datetime       DEFAULT NULL,
    `create_by`           varchar(255)   DEFAULT NULL,
    `update_date`         datetime       DEFAULT NULL,
    `update_by`           varchar(255)   DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_accounts_payable_id` (`accounts_payable_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)系统财务应付单收款信息单据体';

CREATE TABLE `t_cosmic_accounts_payable_detail`
(
    `id`                     varchar(255) NOT NULL,
    `accounts_payable_id`    varchar(255)   DEFAULT NULL,
    `tax`                    decimal(10, 2) DEFAULT NULL COMMENT '明细.税额（负数场景该金额传负数）',
    `tax_rate`               varchar(10)    DEFAULT NULL COMMENT '明细.税率(%)（税率基础资料编码）',
    `expense_item_number`    varchar(10)    DEFAULT NULL COMMENT '收支项目.编码',
    `price`                  decimal(10, 2) DEFAULT NULL COMMENT '明细.不含税金额（负数场景该金额传正数）',
    `price_tax`              decimal(10, 2) DEFAULT NULL COMMENT '明细.含税单价（负数场景该金额传负数）',
    `price_tax_total`        decimal(10, 2) DEFAULT NULL COMMENT '明细.应付金额',
    `remark`                 varchar(2000)  DEFAULT NULL COMMENT '明细.备注',
    `contract_number`        varchar(255)   DEFAULT NULL COMMENT '合同号.编码',
    `project_number`         varchar(255)   DEFAULT NULL COMMENT '项目 编码',
    `building_number`        varchar(255)   DEFAULT NULL COMMENT '楼栋房号 编码',
    `source_method_number`   varchar(255)   DEFAULT NULL COMMENT '来源方式',
    `long_project_number`    varchar(255)   DEFAULT NULL COMMENT '长期待摊项目.编码',
    `assistant_field_number` varchar(255)   DEFAULT NULL COMMENT '车牌号.编码',
    `cost_center_number`     varchar(255)   DEFAULT NULL COMMENT '成本中心.编码（传入组织id或者guid）',
    `unique`                 varchar(255)   DEFAULT NULL COMMENT '明细 唯一标识',
    `amount`                 decimal(10, 2) DEFAULT NULL COMMENT '明细 金额',
    `quantity`               decimal(10, 2) DEFAULT NULL COMMENT '明细.数量（负数场景传-1）',
    `create_date`            datetime       DEFAULT NULL,
    `create_by`              varchar(255)   DEFAULT NULL,
    `update_date`            datetime       DEFAULT NULL,
    `update_by`              varchar(255)   DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_accounts_payable_id` (`accounts_payable_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)系统财务应付单明细';

CREATE TABLE `t_cosmic_pay_apply`
(
    `id`                      varchar(255) NOT NULL,
    `bill_no`                 varchar(255)   DEFAULT NULL COMMENT '付款申请单编码',
    `deal_bill_no`            varchar(255)   DEFAULT NULL COMMENT '付款处理单编码',
    `voucher_num`             varchar(255)   DEFAULT NULL COMMENT '凭证号',
    `source_system`           varchar(255)   DEFAULT NULL COMMENT '来源系统',
    `source_bill_type`        varchar(255)   DEFAULT NULL COMMENT '来源单据类型',
    `source_bill_number`      varchar(255)   DEFAULT NULL COMMENT '来源单据编号',
    `apply_date`              date           DEFAULT NULL COMMENT '申请日期（付款申请发起日期）',
    `pay_org_number`          varchar(255)   DEFAULT NULL COMMENT '付款组织.编码（本笔付款申请的实际付款组织的guid。）',
    `apply_org_number`        varchar(255)   DEFAULT NULL COMMENT '申请组织.编码（本笔付款的申请公司的guid，可以不是资金组织。）',
    `creator_number`          varchar(255)   DEFAULT NULL COMMENT '申请人.工号（本笔付款的申请人的guid，默认自动填充制单人。',
    `payment_type_number`     varchar(255)   DEFAULT NULL COMMENT '付款类型.编码（本笔付款申请的付款类型。）',
    `payment_identify_number` varchar(255)   DEFAULT NULL COMMENT '付款标识.编码（异构系统：主动付款）',
    `paid_status`             char(1)        DEFAULT NULL COMMENT ' 付款状态 A:未付款, B:付款中, C:已付款, D:已排款（接口默认：未付款）',
    `payee_amount`            decimal(10, 2) DEFAULT NULL COMMENT '收付款总额（金额与分录明细的合计总额一致。）',
    `is_dev_fee`              tinyint(4)     DEFAULT NULL COMMENT '是否开发费用',
    `new_apply_cause`         varchar(255)   DEFAULT NULL COMMENT '申请事由',
    `url`                     varchar(255)   DEFAULT NULL COMMENT 'url',
    `text_field`              varchar(255)   DEFAULT NULL COMMENT '收款单据编号',
    `combo_field`             char(1)        DEFAULT NULL COMMENT '特殊场景 1:退款, 2:结转',
    `is_strike_balance`       tinyint(4)     DEFAULT NULL COMMENT '是否已挂账为冲 0否1是',
    `payee_bank_num`          varchar(255)   DEFAULT NULL COMMENT '收款账号',
    `payee_name`              varchar(255)   DEFAULT NULL COMMENT '收款人姓名',
    `payee_bank_name`         varchar(255)   DEFAULT NULL COMMENT '收款银行名称',
    `act_pay_amt`             decimal(10, 2) DEFAULT NULL COMMENT '付款金额',
    `local_amt`               decimal(10, 2) DEFAULT NULL COMMENT '付款金额折本位币',
    `exchange`                varchar(255)   DEFAULT NULL COMMENT '汇率',
    `currency_number`         varchar(255)   DEFAULT NULL COMMENT '货币类别 货币代码',
    `currency_name`           varchar(255)   DEFAULT NULL COMMENT '货币类别 货币名称',
    `settle_type_number`      varchar(255)   DEFAULT NULL COMMENT '结算方式 编码',
    `settle_type_name`        varchar(255)   DEFAULT NULL COMMENT '结算方式 名称',
    `description`             varchar(2000)  DEFAULT NULL COMMENT '摘要',
    `create_date`             datetime       DEFAULT NULL,
    `create_by`               varchar(255)   DEFAULT NULL,
    `update_date`             datetime       DEFAULT NULL,
    `update_by`               varchar(255)   DEFAULT NULL,
    `auditor_number`          varchar(255)   DEFAULT NULL COMMENT '审核人 工号',
    `auditor_name`            varchar(255)   DEFAULT NULL COMMENT '审核人 姓名',
    `creator_name`            varchar(255)   DEFAULT NULL COMMENT '创建人 姓名',
    `modifier_number`         varchar(255)   DEFAULT NULL COMMENT '修改人 工号',
    `modifier_name`           varchar(255)   DEFAULT NULL COMMENT '修改人 姓名',
    `payer_acct_bank_number`  varchar(255)   DEFAULT NULL COMMENT '付款账号.账户编码',
    `payer_acct_bank_name`    varchar(255)   DEFAULT NULL COMMENT '付款账号 账户名称',
    `payer_bank_number`       varchar(255)   DEFAULT NULL COMMENT '付款银行.编码',
    `payer_bank_name`         varchar(255)   DEFAULT NULL COMMENT '付款银行 名称',
    `complete_del`            tinyint(4)     DEFAULT NULL COMMENT '是否整单删除',
    `back_type`               varchar(255)   DEFAULT NULL COMMENT '存入类型 [fail:付款失败, back:退款]',
    `fail_reason`             varchar(255)   DEFAULT NULL COMMENT '失败原因',
    `back_col_no`             varchar(255)   DEFAULT NULL COMMENT '收款单号',
    `modifity_time`           datetime       DEFAULT NULL COMMENT '修改时间',
    `if_update`               tinyint(4)     DEFAULT NULL COMMENT '是否已更新 [1:是, 0:否]',
    `is_push`                 tinyint(4)     DEFAULT '0' COMMENT '是否已经推送至金蝶系统',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)系统 付款申请单';

CREATE TABLE `t_cosmic_pay_apply_info`
(
    `id`                     varchar(255) NOT NULL,
    `pay_apply_id`           varchar(255)   DEFAULT NULL COMMENT '付款申请id',
    `settlement_type_number` varchar(255)   DEFAULT NULL COMMENT '申请明细.申请结算方式(本笔申请期望结算方式，可以是现汇、支票、票据、现金等等。)',
    `payee_amount`           decimal(10, 2) DEFAULT NULL COMMENT '申请明细 收款金额',
    `priority`               varchar(255)   DEFAULT NULL COMMENT '紧急程度',
    `payee_type`             varchar(255)   DEFAULT NULL COMMENT '收款人类型 bd_customer:客户, bd_supplier:供应商, bos_user:职员, bos_org:公司, other:其他',
    `payee_name`             varchar(255)   DEFAULT NULL COMMENT '申请明细.收款人\r\n备注：根据收款人类型传不同的数据\r\n        客户、供应商——>社会信用代码\r\n        公司——>guid/id   \r\n        职员——>guid\r\n     ',
    `payee_acc_bank_num`     varchar(255)   DEFAULT NULL COMMENT '收款账号',
    `account_name`           varchar(255)   DEFAULT NULL COMMENT '账户名称',
    `remark`                 varchar(255)   DEFAULT NULL COMMENT '备注',
    `payee_bank_number`      varchar(255)   DEFAULT NULL COMMENT '收款银行.编码（基础资料：行名行号）\r\n备注：应对收款人类型为其他和职员，或者供应商/客户的银行账户信息不全的情况下，需要填写对应的银行账户的收款银行的行名行号',
    `special_scene`          varchar(255)   DEFAULT NULL COMMENT '特殊场景关联编号',
    `create_date`            datetime       DEFAULT NULL,
    `update_date`            datetime       DEFAULT NULL,
    `create_by`              varchar(255)   DEFAULT NULL,
    `update_by`              datetime       DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_pay_apply_id` (`pay_apply_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)系统 付款申请单申请明细分录';

CREATE TABLE `t_cosmic_pay_apply_business`
(
    `id`                     varchar(255) NOT NULL,
    `pay_apply_id`           varchar(255)   DEFAULT NULL COMMENT '付款申请id',
    `create_date`            datetime       DEFAULT NULL,
    `update_date`            datetime       DEFAULT NULL,
    `create_by`              varchar(255)   DEFAULT NULL,
    `update_by`              datetime       DEFAULT NULL,
    `bus_payee_amount`       decimal(10, 2) DEFAULT NULL COMMENT '收款金额（业务明细分收款金额合计与申请明细的合计金额一致）',
    `expense_item_number`    varchar(255)   DEFAULT NULL COMMENT '收支项目.编码',
    `project_number`         varchar(255)   DEFAULT NULL COMMENT '项目编码',
    `contract_number`        varchar(255)   DEFAULT NULL COMMENT '合同号.编码',
    `long_waiting_number`    varchar(255)   DEFAULT NULL COMMENT '长期待摊项目.编码',
    `assistant_field_number` varchar(255)   DEFAULT NULL COMMENT '车牌号 编码',
    `source_method_number`   varchar(255)   DEFAULT NULL COMMENT '来源方式.编码',
    `building_house_number`  varchar(255)   DEFAULT NULL COMMENT '楼栋房号.编码',
    `biz_type_number`        varchar(255)   DEFAULT NULL COMMENT '业态类型 编码',
    `remark`                 varchar(255)   DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_pay_apply_id` (`pay_apply_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='金蝶(星瀚)系统 付款申请单业务明细分录';

/*20250513 */
CREATE TABLE `t_cosmic_log`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调用路径',
  `push_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送状态（1成功2失败）',
  `input_parameters` varchar(10000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入参',
  `output_parameters` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出参',
  `retry_frequency` int(11) NULL DEFAULT 0 COMMENT '重发次数',
  `call_time` datetime NULL DEFAULT NULL COMMENT '调用时间',
  `retry_time` datetime NULL DEFAULT NULL COMMENT '重发时间',
  `http_status_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'HTTP码状态',
  `business_status_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务状态码',
  `data_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据id',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型（1财务应付单推送，2财务收款处理单 ，3财务应收单，4付款申请单，5报账工单）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '金蝶接口日志' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

/*20250520 */
CREATE TABLE `t_investment_release`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `policy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '招商政策',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态（1待发布，2已发布）',
  `release_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '招商发布' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;


/*20250521 */
CREATE TABLE `t_bil_order_collection`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `order_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单id',
  `responsible_person` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '催收责任人（经办及部门负责人）',
  `tel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话（部门负责人）',
  `unreceived_cause` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '未收回原因',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '催收数据表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

/*20250523 */
CREATE TABLE `t_investment_merchant`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `merchant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户名称',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `tel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `business_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业态',
  `brand_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌名称',
  `brand_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌级别',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户管理' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

/*20250526 */
CREATE TABLE `t_investment_register`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户类型',
  `id_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件类型',
  `id_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件号码',
  `tel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `policy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '招商政策',
  `register_time` datetime NULL DEFAULT NULL COMMENT '登记日期',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `tail_after` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跟踪状态',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '招商登记' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

/*20250527 */
CREATE TABLE `t_investment_archives`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `year` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年度',
  `quarter` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '季度',
  `quarterly_target` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '季度目标',
  `quarterly_revenue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '季度收益',
  `quarterly_investment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '季度投入',
  `advertising_investment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告投入',
  `other_investment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他费用投入',
  `progress` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '进度',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '招商档案' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;


/*20250528 */
CREATE TABLE `t_investment_news`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态（1待发布，2已发布）',
  `release_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '新闻资讯' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

/*20250528 */
CREATE TABLE `t_investment_information`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态（1待发布，2已发布）',
  `release_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '招商资讯' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

-- 20250613
CREATE TABLE `t_cosmic_income_transaction_rel` (
`id` VARCHAR(36) NOT NULL COMMENT '主键',
`income_id` VARCHAR(36) NOT NULL COMMENT '应收单ID',
`transaction_id` VARCHAR(36) NOT NULL COMMENT '交易明细ID',
`create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
`update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
PRIMARY KEY (`id`),
KEY `idx_income_id` (`income_id`),
KEY `idx_transaction_id` (`transaction_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='应收单与交易明细关联表'